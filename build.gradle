// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    alias(libs.plugins.android.application) apply false
    alias(libs.plugins.android.library) apply false
    alias(libs.plugins.kotlin.android) apply false
    alias(libs.plugins.hilt.android) apply false
    alias(libs.plugins.compose.compiler) apply false
    id("com.google.gms.google-services") version "4.4.2" apply false
    id("com.google.firebase.crashlytics") version "3.0.2" apply false
    alias(libs.plugins.ktlint)
}

// Tắt hoàn toàn Configuration Cache cho task này
// Precompute values at configuration time to be config-cache friendly
def rootDirForHooks = rootProject.layout.projectDirectory.asFile

tasks.register('installGitHooks') {
    // Vô hiệu hóa Configuration Cache cho task này
    notCompatibleWithConfigurationCache("T<PERSON>y cập động tới file system")

    description = 'Cài đặt Git hooks để kiểm tra code'
    group = 'Setup'

    doLast {
        def gitDir = new File(rootDirForHooks, ".git")
        if (!gitDir.exists()) {
            println "❌ Thư mục .git không tồn tại. Đảm bảo đây là một Git repository."
            return
        }

        def gitHooksDir = new File(gitDir, "hooks")
        gitHooksDir.mkdirs()

        // Kiểm tra hệ điều hành - cách tiêu chuẩn để kiểm tra trong Java/Groovy
        def isWindows = System.getProperty('os.name').toLowerCase().contains('windows')

        if (isWindows) {
            // ===== BEGIN: THAY ĐỔI CHO WINDOWS =====
            // Tạo script đơn giản hơn cho Windows sử dụng Git Bash (đi kèm với Git for Windows)
            def prePushScript = """#!/bin/sh

# Hook chạy trước khi push code
echo "→ Đang kiểm tra code với ktlint trước khi push..."

# Kiểm tra và set JAVA_HOME nếu cần (cho Windows)
if [ -z "\$JAVA_HOME" ] && [ -d "/c/Program Files/Java" ]; then
    # Tìm Java 64-bit trong Program Files
    for dir in "/c/Program Files/Java/"*; do
        if [ -d "\$dir" ] && [ -f "\$dir/bin/java.exe" ]; then
            export JAVA_HOME="\$dir"
            export PATH="\$JAVA_HOME/bin:\$PATH"
            echo "  → Set JAVA_HOME to: \$JAVA_HOME"
            break
        fi
    done
fi

# Chuyển đến thư mục gốc của dự án (sử dụng git để xác định)
PROJECT_ROOT="\$(git rev-parse --show-toplevel)"
cd "\$PROJECT_ROOT" || exit 1

# Detect Java version và architecture để set memory phù hợp
echo "→ Đang kiểm tra Java environment..."
echo "  → which java: \$(which java)"
echo "  → JAVA_HOME: \$JAVA_HOME"
JAVA_VERSION=\$(java -version 2>&1)
echo "  → Java version output:"
echo "\$JAVA_VERSION" | head -3

# Kiểm tra xem có override từ environment không
if [ -n "\$KTLINT_GRADLE_OPTS" ]; then
    echo "  → Sử dụng KTLINT_GRADLE_OPTS từ environment: \$KTLINT_GRADLE_OPTS"
    export GRADLE_OPTS="\$KTLINT_GRADLE_OPTS"
else
    # Auto-detect và set memory
    if echo "\$JAVA_VERSION" | grep -q "64-Bit"; then
        echo "  ✓ Phát hiện Java 64-bit"
        # Thử với các mức memory khác nhau cho Windows
        export GRADLE_OPTS="-Xmx1536m -Dfile.encoding=UTF-8 -XX:+UseParallelGC"
    else
        echo "  ⚠ Phát hiện Java 32-bit hoặc không xác định được architecture"
        echo "  → Sử dụng cấu hình memory thấp hơn để tương thích"
        export GRADLE_OPTS="-Xmx1024m -Dfile.encoding=UTF-8 -XX:+UseParallelGC"
    fi
fi

# Hiển thị GRADLE_OPTS đang sử dụng
echo "  → GRADLE_OPTS: \$GRADLE_OPTS"

# Chạy ktlint với gradlew hoặc gradlew.bat tùy môi trường
echo "→ Đang chạy ktlint check..."


# Thử chạy ktlint với error handling
echo "  → Chạy ktlintCheck..."

# Đối với Windows, cần xử lý đặc biệt với memory configuration
if [ -f "./gradlew.bat" ]; then
    echo "  → Sử dụng gradlew.bat cho Windows..."
    echo "  → Override JVM memory settings để tránh lỗi..."
    
    # Phương pháp: Dùng JAVA_TOOL_OPTIONS - biến này được JVM tự động nhận
    # và override mọi settings khác
    export JAVA_TOOL_OPTIONS="-Xmx1536m -XX:MaxMetaspaceSize=512m -Dfile.encoding=UTF-8"
    
    # Chạy ktlintCheck
    ./gradlew.bat ktlintCheck 2>&1 | tee ktlint_output.log
    KTLINT_EXIT_CODE=\${PIPESTATUS[0]}
    
    # Clear JAVA_TOOL_OPTIONS sau khi dùng
    unset JAVA_TOOL_OPTIONS
else
    ./gradlew ktlintCheck 2>&1 | tee ktlint_output.log
    KTLINT_EXIT_CODE=\${PIPESTATUS[0]}
fi

# Kiểm tra nếu lỗi là do memory
if grep -q "heap size\\|OutOfMemoryError\\|Could not reserve enough space" ktlint_output.log; then
    echo ""
    echo "❌ Lỗi memory khi chạy ktlint!"
    echo ""
    echo "GIẢI PHÁP CHO WINDOWS:"
    echo "1. Kiểm tra Java version: java -version"
    echo "2. Nếu dùng Java 32-bit → Nâng cấp lên Java 64-bit"
    echo "3. Set JAVA_HOME đúng trong System Environment Variables"
    echo "4. Restart Git Bash/terminal sau khi thay đổi"
    echo ""
    echo "Nếu vẫn lỗi, vui lòng liên hệ team để được hỗ trợ."
    rm -f ktlint_output.log
    exit 1
fi

rm -f ktlint_output.log

# Kiểm tra kết quả ktlint
if [ \$KTLINT_EXIT_CODE -ne 0 ]; then
    echo "✗ Kiểm tra ktlint thất bại. Vui lòng sửa lỗi trước khi push."
    if [ -f "./gradlew.bat" ]; then
      echo "  Bạn có thể chạy './gradlew.bat ktlintFormat' để tự động sửa một số lỗi."
    else
      echo "  Bạn có thể chạy './gradlew ktlintFormat' để tự động sửa một số lỗi."
    fi
    echo ""
    echo "  Nếu gặp lỗi về memory, vui lòng thử:"
    echo "  1. Set biến môi trường KTLINT_GRADLE_OPTS với memory phù hợp:"
    echo "     - Windows CMD: set KTLINT_GRADLE_OPTS=-Xmx512m -Dfile.encoding=UTF-8"
    echo "     - Git Bash: export KTLINT_GRADLE_OPTS=\"-Xmx512m -Dfile.encoding=UTF-8\""
    echo "  2. Sau đó thử push lại"
    echo "  3. Hoặc tạm thời bypass: git push --no-verify (không khuyến khích)"
    exit 1
fi

echo "✓ Kiểm tra ktlint thành công! Code đã sẵn sàng để push."
exit 0
"""
            // Tạo hook trong .git/hooks (cùng cách cho cả Windows)
            def hookFile = new File(gitHooksDir, "pre-push")
            hookFile.text = prePushScript
            hookFile.setExecutable(true)

            println "✅ Git hooks đã được cài đặt cho Windows tại: ${hookFile.absolutePath}"
            // ===== END: THAY ĐỔI CHO WINDOWS =====
        } else {
            // Giữ nguyên script gốc cho macOS/Linux
            def prePushScript = """#!/bin/sh

# Hook chạy trước khi push code
echo "→ Đang kiểm tra code với ktlint trước khi push..."

# Chuyển đến thư mục gốc của dự án
cd "${rootDir.absolutePath}" || exit 1

# Chạy ktlint
echo "→ Đang chạy ktlint check..."
./gradlew ktlintCheck

if [ \$? -ne 0 ]; then
    echo "✗ Kiểm tra ktlint thất bại. Vui lòng sửa lỗi trước khi push."
    echo "  Bạn có thể chạy './gradlew ktlintFormat' để tự động sửa một số lỗi."
    exit 1
fi

echo "✓ Kiểm tra ktlint thành công! Code đã sẵn sàng để push."
exit 0
"""
            // Tạo hook trong .git/hooks
            def hookFile = new File(gitHooksDir, "pre-push")
            hookFile.text = prePushScript
            hookFile.setExecutable(true)

            println "✅ Git hooks đã được cài đặt tại: ${hookFile.absolutePath}"
        }
    }
}

// Chạy installGitHooks mỗi khi các task quan trọng được chạy trong subprojects
subprojects {
    afterEvaluate { subproject ->
        // Chỉ áp dụng cho các module Android (có plugin com.android.application hoặc com.android.library)
        if (subproject.plugins.hasPlugin('com.android.application') || subproject.plugins.hasPlugin('com.android.library')) {
            subproject.tasks.matching { task ->
                task.name == 'preBuild' || task.name == 'clean' || task.name == 'build'
            }.configureEach { task ->
                task.dependsOn(rootProject.tasks.named('installGitHooks'))
            }
        }
    }
}

// Đảm bảo installGitHooks chạy khi ktlintCheck được chạy
tasks.named('ktlintCheck').configure {
    dependsOn('installGitHooks')
}


subprojects {
    apply plugin: "org.jlleitschuh.gradle.ktlint" // Version should be inherited from parent

    // Optionally configure plugin
    ktlint {
        version = "0.50.0"
        debug = true
        verbose = true
        android = true
        outputToConsole = true
        outputColorName = "RED"
        ignoreFailures = false

        filter {
            exclude("**/test/**")
            exclude("**/androidTest/**")
            exclude("**/generated/**")
            exclude("**/build/**")
            include("**/kotlin/**")
            include("**/*.kt")
        }

        kotlinScriptAdditionalPaths {
            include fileTree("scripts/")
        }

        reporters {
            reporter "plain"
            reporter "checkstyle"
            reporter "html" // Thêm báo cáo HTML để dễ đọc
            reporter "json" // Thêm JSON để tích hợp với các công cụ khác
        }

        // Nếu muốn sử dụng baseline
        baseline = file("${project.projectDir}/ktlint-baseline.xml")

        tasks.withType(org.jlleitschuh.gradle.ktlint.tasks.GenerateReportsTask).configureEach {
            reportsOutputDirectory.set(
                    project.layout.buildDirectory.dir("reports/ktlint/${project.name}")
            )
        }

        tasks.withType(org.jlleitschuh.gradle.ktlint.tasks.BaseKtLintCheckTask).configureEach {
            workerMaxHeapSize.set("512m")
        }
    }
}
