<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.vietinbank.feature_soft.common.view.VSoftHeaderView
        android:id="@+id/headerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:v_soft_title="Mã Soft OTP" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:paddingHorizontal="@dimen/dp15"
        android:paddingTop="@dimen/dp20">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Thông tin giao dịch"
                android:textColor="@color/text_blue_09"
                android:textSize="@dimen/sp16"
                app:fontCus="regular_bold" />

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:background="@drawable/bg_yellow_radius_4dp"
                android:backgroundTint="@color/white"
                android:orientation="vertical"
                android:padding="@dimen/dp16">

                <com.vietinbank.feature_soft.common.view.VSoftHorizontalInfoView
                    android:id="@+id/infoRef"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:v_soft_title="Mã tham chiếu"
                    tools:vtb_value="********" />

                <com.vietinbank.feature_soft.common.view.VSoftHorizontalInfoView
                    android:id="@+id/infoSum"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp16"
                    app:v_soft_title="Tổng số giao dịch"
                    tools:vtb_value="8" />

            </androidx.appcompat.widget.LinearLayoutCompat>

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp20"
                android:text="Mã xác thực OTP bằng phương thức xác thực Soft OTP của Quý khách đang được hiển thị dưới đây. Quý khách vui lòng nhấn Xác nhận để tiếp tục giao dịch"
                android:textColor="@color/text_blue_09"
                android:textSize="@dimen/sp14"
                app:fontCus="semi_bold" />

            <com.vietinbank.feature_soft.common.view.VSoftInputOTPView
                android:id="@+id/inputOTP"
                android:fontFamily="@font/bold"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp53"
                android:layout_marginTop="@dimen/dp10"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:inputType="numberDecimal"
                android:maxLength="6"
                android:textSize="@dimen/sp20"
                app:v_soft_otp_margin="@dimen/dp8"
                app:v_soft_otp_width="@dimen/dp57" />

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <com.vietinbank.core_ui.base.views.BaseTextView
                    android:id="@+id/tvTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp16"
                    android:layout_weight="1"
                    android:textColor="@color/text_blue_09"
                    android:textSize="@dimen/sp14"
                    app:fontCus="medium"
                    tools:text="Mã OTP sẽ cập nhật sau 60s" />

                <com.vietinbank.core_ui.base.views.BaseTextView
                    android:id="@+id/tvCopyOTP"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:background="@drawable/v_soft_bg_radius_52_boder"
                    android:paddingHorizontal="@dimen/dp25"
                    android:paddingVertical="@dimen/dp6"
                    android:text="Copy mã OTP"
                    android:textColor="@color/text_blue_09"
                    app:fontCus="semi_bold" />

            </androidx.appcompat.widget.LinearLayoutCompat>

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:text="Để đảm bảo an toàn, Quý khách tuyệt đối không cung cấp OTP cho bất kỳ ai"
                android:textColor="@color/text_blue_09"
                android:textSize="@dimen/sp12"
                app:fontCus="medium" />

        </androidx.appcompat.widget.LinearLayoutCompat>

    </androidx.core.widget.NestedScrollView>


    <com.vietinbank.core_ui.base.views.BaseButton
        android:id="@+id/btnNext"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp48"
        android:layout_marginHorizontal="@dimen/dp15"
        android:layout_marginTop="@dimen/dp10"
        android:layout_marginBottom="@dimen/dp20"
        android:text="Đóng" />

</androidx.appcompat.widget.LinearLayoutCompat>
