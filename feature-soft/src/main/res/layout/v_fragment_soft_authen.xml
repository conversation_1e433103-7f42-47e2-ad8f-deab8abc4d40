<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.vietinbank.feature_soft.common.view.VSoftHeaderView
        android:id="@+id/headerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:v_soft_title="Mã Soft OTP" />

    <com.vietinbank.core_ui.base.views.BaseTextView

        android:id="@+id/tvNote"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp15"
        android:layout_marginTop="@dimen/dp8"
        android:text="Mã xác thực OTP bằng phương thức xác thực Soft OTP của Quý khách đang được hiển thị dưới đây. Quý khách vui lòng nhấn “Xác nhận” để tiếp tục giao dịch"
        android:textColor="@color/text_blue_09"
        android:textSize="@dimen/sp16"
        app:fontCus="semi_bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/headerView" />

    <com.vietinbank.feature_soft.common.view.VSoftInputOTPView
        android:id="@+id/edtSoft"
        android:fontFamily="@font/bold"
        android:layout_width="@dimen/dp0"
        android:layout_height="@dimen/dp53"
        android:layout_marginTop="@dimen/dp10"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:inputType="numberDecimal"
        android:maxLength="6"
        android:textSize="@dimen/sp20"
        app:layout_constraintEnd_toEndOf="@id/tvNote"
        app:layout_constraintStart_toStartOf="@id/tvNote"
        app:layout_constraintTop_toBottomOf="@id/tvNote"
        app:v_soft_otp_margin="@dimen/dp8"
        app:v_soft_otp_width="@dimen/dp57" />

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/tvCountTime"
        android:layout_width="@dimen/dp0"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        android:textColor="@color/text_blue_09"
        android:textSize="@dimen/sp14"
        app:fontCus="medium"
        app:layout_constraintEnd_toEndOf="@id/tvNote"
        app:layout_constraintStart_toStartOf="@id/tvNote"
        app:layout_constraintTop_toBottomOf="@id/edtSoft"
        tools:text="@string/vtb_ra_authen_soft_time" />

    <LinearLayout
        android:id="@+id/vtb_ll_action"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:paddingHorizontal="@dimen/dp16"
        android:paddingTop="@dimen/dp8"
        android:paddingBottom="@dimen/dp20"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!-- Button Xóa -->
        <com.vietinbank.core_ui.base.views.BaseTextView
            android:id="@+id/btnCancel"
            android:layout_width="@dimen/dp0"
            android:layout_height="@dimen/dp48"
            android:layout_marginEnd="@dimen/dp16"
            android:layout_weight="1"
            android:background="@drawable/bg_button_disable"
            android:gravity="center"
            android:text="Hủy"
            android:textColor="@color/text_blue_02"
            app:fontCus="semi_bold" />

        <!-- Button Sao chép -->
        <com.vietinbank.core_ui.base.views.BaseButton
            android:id="@+id/btnNext"
            android:layout_width="@dimen/dp0"
            android:layout_height="@dimen/dp48"
            android:layout_weight="1"
            android:enabled="true"
            android:text="Xác nhận" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
