<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.vietinbank.feature_soft.common.view.VSoftHeaderView
        android:id="@+id/headerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:v_soft_title="Phương thức nhận mã" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp0"
        android:layout_weight="1"
        android:paddingHorizontal="@dimen/dp15"
        android:paddingTop="@dimen/dp20">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tvInfo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Thông tin người dùng"
                android:textColor="@color/text_blue_09"
                android:textSize="@dimen/sp16"
                app:fontCus="regular_bold" />

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/llInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:background="@drawable/bg_yellow_radius_4dp"
                android:backgroundTint="@color/white"
                android:orientation="vertical"
                android:padding="@dimen/dp16">

                <com.vietinbank.feature_soft.common.view.VSoftHorizontalInfoView
                    android:id="@+id/infoName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:v_soft_title="Họ và tên"
                    tools:vtb_value="Lê Văn A" />

                <com.vietinbank.feature_soft.common.view.VSoftHorizontalInfoView
                    android:id="@+id/infoUser"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp16"
                    app:v_soft_title="Tên đăng nhập"
                    tools:vtb_value="tuna" />

                <com.vietinbank.feature_soft.common.view.VSoftHorizontalInfoView
                    android:id="@+id/infoPhone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp24"
                    app:v_soft_title="Số điện thoại"
                    tools:vtb_value="*********" />

                <com.vietinbank.feature_soft.common.view.VSoftHorizontalInfoView
                    android:id="@+id/infoEmail"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp24"
                    app:v_soft_title="Email"
                    tools:vtb_value="<EMAIL>" />

            </androidx.appcompat.widget.LinearLayoutCompat>

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tvResendDes"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/sp16"
                android:text="Quý khách vui lòng nhập mã kích hoạt đã được gửi đến Số điện thoại/Email của Quý khách"
                android:textColor="@color/text_blue_09"
                android:visibility="gone"
                app:fontCus="semi_bold" />

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp15"
                android:text="Phương thức nhận mã kích hoạt Soft OTP"
                android:textColor="@color/text_blue_09"
                android:textSize="@dimen/sp16"
                app:fontCus="regular_bold" />

            <RadioGroup
                android:id="@+id/rgMethod"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:background="@drawable/bg_yellow_radius_4dp"
                android:backgroundTint="@color/white"
                android:orientation="horizontal"
                android:padding="@dimen/dp16">

                <androidx.appcompat.widget.AppCompatRadioButton
                    android:id="@+id/rbPhone"
                    android:layout_width="@dimen/dp0"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@null"
                    android:button="@null"
                    android:drawableStart="@drawable/selector_radio"
                    android:drawablePadding="@dimen/dp8"
                    android:gravity="center_vertical"
                    android:text="Số điện thoại"
                    android:textColor="@color/radio_text_color"
                    android:textSize="@dimen/sp16" />

                <androidx.appcompat.widget.AppCompatRadioButton
                    android:id="@+id/rbEmail"
                    android:layout_width="@dimen/dp0"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp8"
                    android:layout_weight="1"
                    android:background="@null"
                    android:button="@null"
                    android:drawableStart="@drawable/selector_radio"
                    android:drawablePadding="@dimen/dp8"
                    android:gravity="center_vertical"
                    android:text="Email"
                    android:textColor="@color/radio_text_color"
                    android:textSize="@dimen/sp16" />

            </RadioGroup>

        </androidx.appcompat.widget.LinearLayoutCompat>

    </androidx.core.widget.NestedScrollView>

    <com.vietinbank.core_ui.base.views.BaseButton
        android:id="@+id/btnNext"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp48"
        android:layout_marginHorizontal="@dimen/dp15"
        android:layout_marginTop="@dimen/dp10"
        android:layout_marginBottom="@dimen/dp20"
        android:text="@string/vtb_ra_continue" />

</androidx.appcompat.widget.LinearLayoutCompat>
