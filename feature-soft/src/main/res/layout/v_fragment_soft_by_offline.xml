<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.vietinbank.feature_soft.common.view.VSoftHeaderView
        android:id="@+id/headerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:v_soft_title="Thông tin giao dịch" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp0"
        android:layout_weight="1"
        android:paddingHorizontal="@dimen/dp15"
        android:paddingTop="@dimen/dp20">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tvResendDes"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Quý khách vui lòng nhập thông tin giao dịch hoặc quét mã QR Code đang hiển thị trên kênh Internet Banking để lấy mã OTP"
                android:textColor="@color/text_blue_09"
                android:textSize="@dimen/sp16"
                app:fontCus="semi_bold" />

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp24"
                android:text="Phương thức giao dịch"
                android:textColor="@color/text_blue_09"
                android:textSize="@dimen/sp16"
                app:fontCus="regular_bold" />

            <RadioGroup
                android:id="@+id/rgMethod"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:background="@drawable/bg_yellow_radius_4dp"
                android:backgroundTint="@color/white"
                android:orientation="horizontal"
                android:padding="@dimen/dp16">

                <androidx.appcompat.widget.AppCompatRadioButton
                    android:id="@+id/rbScan"
                    android:layout_width="@dimen/dp0"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@null"
                    android:button="@null"
                    android:checked="true"
                    android:drawableStart="@drawable/selector_radio"
                    android:drawablePadding="@dimen/dp8"
                    android:gravity="center_vertical"
                    android:text="Quét QR"
                    android:textColor="@color/radio_text_color"
                    android:textSize="@dimen/sp16" />

                <androidx.appcompat.widget.AppCompatRadioButton
                    android:id="@+id/rbInput"
                    android:layout_width="@dimen/dp0"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp8"
                    android:layout_weight="1"
                    android:background="@null"
                    android:button="@null"
                    android:drawableStart="@drawable/selector_radio"
                    android:drawablePadding="@dimen/dp8"
                    android:gravity="center_vertical"
                    android:text="Nhập thông tin"
                    android:textColor="@color/radio_text_color"
                    android:textSize="@dimen/sp16" />

            </RadioGroup>

        </androidx.appcompat.widget.LinearLayoutCompat>

    </androidx.core.widget.NestedScrollView>

    <com.vietinbank.core_ui.base.views.BaseButton
        android:id="@+id/btnNext"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp48"
        android:layout_marginHorizontal="@dimen/dp15"
        android:layout_marginTop="@dimen/dp10"
        android:layout_marginBottom="@dimen/dp20"
        android:text="@string/vtb_ra_continue" />

</androidx.appcompat.widget.LinearLayoutCompat>
