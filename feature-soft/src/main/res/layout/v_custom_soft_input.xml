<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/vtb_layout_input"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColorHint="@color/text_blue_07"
        app:boxBackgroundMode="filled"
        app:boxStrokeWidth="@dimen/dp0"
        app:boxStrokeWidthFocused="@dimen/dp0">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/vtb_edt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/transparent"
            android:fontFamily="@font/medium"
            android:inputType="text"
            android:paddingStart="@dimen/dp0"
            android:paddingEnd="@dimen/dp40"
            android:paddingBottom="@dimen/dp0"
            android:saveEnabled="false"
            android:textCursorDrawable="@drawable/v_soft_cursor"
            android:textSize="@dimen/sp16"
            tools:text="1234567" />

    </com.google.android.material.textfield.TextInputLayout>


    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/vtb_iv_clear"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:src="@drawable/v_soft_ic_clear"
        android:visibility="gone"
        tools:visibility="visible" />

    <View
        android:id="@+id/vtb_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp1"
        android:layout_below="@id/vtb_layout_input"
        android:layout_marginTop="@dimen/dp5"
        android:background="@color/text_blue_09" />

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/vtb_tv_more"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/vtb_line"
        android:layout_marginTop="@dimen/dp4"
        android:textSize="@dimen/sp12"
        android:visibility="gone"
        app:fontCus="medium"
        tools:text="mo ta"
        tools:visibility="gone" />

</RelativeLayout>