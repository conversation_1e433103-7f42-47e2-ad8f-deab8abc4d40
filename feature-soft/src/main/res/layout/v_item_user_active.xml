<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/tvValue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_gray_08"
        android:textSize="@dimen/sp14"
        app:fontCus="medium"
        app:layout_constraintBottom_toBottomOf="@id/ivCheck"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/ivCheck"
        tools:text="tuna5" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivCheck"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_radio_uncheck" />

    <View
        android:id="@+id/vLine"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp1"
        android:layout_marginTop="@dimen/dp12"
        android:background="@color/text_gray_200"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivCheck" />

</androidx.constraintlayout.widget.ConstraintLayout>