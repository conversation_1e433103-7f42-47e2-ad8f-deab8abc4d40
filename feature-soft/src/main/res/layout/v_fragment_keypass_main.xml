<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.vietinbank.feature_soft.common.view.VSoftHeaderView
        android:id="@+id/headerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:v_soft_title="Cài đặt Keypass" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp0"
        android:layout_weight="1">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp15"
            android:layout_marginTop="@dimen/dp20"
            android:background="@drawable/bg_yellow_radius_4dp"
            android:backgroundTint="@color/white"
            android:orientation="vertical"
            android:padding="@dimen/dp16">

            <RelativeLayout
                android:id="@+id/rlSync"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.vietinbank.core_ui.base.views.BaseTextView
                    android:id="@+id/tvSync"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_marginEnd="@dimen/dp8"
                    android:layout_toStartOf="@id/ivSync"
                    android:drawablePadding="@dimen/dp5"
                    android:text="Đồng bộ Keypass"
                    android:textColor="@color/text_blue_01"
                    android:textSize="@dimen/sp14"
                    app:drawableStartCompat="@drawable/v_keypass_ic_sync"
                    app:fontCus="semi_bold" />

                <com.vietinbank.core_ui.base.views.BaseTextView
                    android:id="@+id/tvSyncDescription"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/tvSync"
                    android:layout_alignStart="@id/tvSync"
                    android:layout_alignEnd="@id/tvSync"
                    android:layout_marginTop="@dimen/dp12"
                    android:text="Đồng bộ Soft OTP trong trường hợp Xác thực OTP sai quá số lần hoặc đồng bộ thời gian Soft OTP"
                    android:textColor="@color/text_gray_08"
                    android:textSize="@dimen/sp14"
                    app:fontCus="medium" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivSync"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    app:srcCompat="@drawable/ic_drop_down" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp1"
                    android:layout_below="@id/tvSyncDescription"
                    android:layout_marginTop="@dimen/dp10"
                    android:background="@color/text_blue_09" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rlForgot"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.vietinbank.core_ui.base.views.BaseTextView
                    android:id="@+id/tvForgot"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_marginTop="@dimen/dp10"
                    android:layout_marginEnd="@dimen/dp8"
                    android:layout_toStartOf="@id/ivForgot"
                    android:drawablePadding="@dimen/dp5"
                    android:text="Quên pin Keypass"
                    android:textColor="@color/text_blue_01"
                    android:textSize="@dimen/sp14"
                    app:drawableStartCompat="@drawable/v_keypass_ic_sync"
                    app:fontCus="semi_bold" />

                <com.vietinbank.core_ui.base.views.BaseTextView
                    android:id="@+id/tvForgotDescription"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/tvForgot"
                    android:layout_alignStart="@id/tvForgot"
                    android:layout_alignEnd="@id/tvForgot"
                    android:layout_marginTop="@dimen/dp12"
                    android:text="Đồng bộ Soft OTP trong trường hợp Xác thực OTP sai quá số lần hoặc đồng bộ thời gian Soft OTP"
                    android:textColor="@color/text_gray_08"
                    android:textSize="@dimen/sp14"
                    app:fontCus="medium" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivForgot"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    app:srcCompat="@drawable/ic_drop_down" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp1"
                    android:layout_below="@id/tvForgotDescription"
                    android:layout_marginTop="@dimen/dp10"
                    android:background="@color/text_blue_09" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rlInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.vietinbank.core_ui.base.views.BaseTextView
                    android:id="@+id/tvCode"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_marginTop="@dimen/dp10"
                    android:layout_marginEnd="@dimen/dp8"
                    android:layout_toStartOf="@id/ivCode"
                    android:drawablePadding="@dimen/dp5"
                    android:text="Mã kích hoạt Keypass"
                    android:textColor="@color/text_blue_01"
                    android:textSize="@dimen/sp14"
                    app:drawableStartCompat="@drawable/v_keypass_ic_sync"
                    app:fontCus="semi_bold" />

                <com.vietinbank.core_ui.base.views.BaseTextView
                    android:id="@+id/tvCodeDescription"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/tvCode"
                    android:layout_alignStart="@id/tvCode"
                    android:layout_alignEnd="@id/tvCode"
                    android:layout_marginTop="@dimen/dp12"
                    android:text="Đồng bộ Soft OTP trong trường hợp Xác thực OTP sai quá số lần hoặc đồng bộ thời gian Soft OTP"
                    android:textColor="@color/text_gray_08"
                    android:textSize="@dimen/sp14"
                    app:fontCus="medium" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivCode"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    app:srcCompat="@drawable/ic_drop_down" />


            </RelativeLayout>

        </androidx.appcompat.widget.LinearLayoutCompat>

    </androidx.core.widget.NestedScrollView>

</androidx.appcompat.widget.LinearLayoutCompat>
