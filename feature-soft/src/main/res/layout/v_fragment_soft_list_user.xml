<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.vietinbank.feature_soft.common.view.VSoftHeaderView
        android:id="@+id/headerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:v_soft_title="Danh sách người dùng" />

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp15"
        android:layout_marginTop="@dimen/dp20"
        android:text="Danh sách người dùng"
        android:textColor="@color/text_blue_09"
        android:textSize="@dimen/sp16"
        app:fontCus="semi_bold" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:paddingHorizontal="@dimen/dp15"
        android:paddingTop="@dimen/dp10">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_yellow_radius_4dp"
            android:backgroundTint="@color/white"
            android:orientation="vertical"
            android:padding="@dimen/dp16">

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Danh sách người dùng"
                android:textColor="@color/text_gray_08"
                android:textSize="@dimen/sp14"
                app:fontCus="semi_bold" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/lstUser"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:nestedScrollingEnabled="false"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="2"
                tools:listitem="@layout/v_item_user_active" />

        </androidx.appcompat.widget.LinearLayoutCompat>

    </androidx.core.widget.NestedScrollView>

    <com.vietinbank.core_ui.base.views.BaseButton
        android:id="@+id/btnNext"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp48"
        android:layout_marginHorizontal="@dimen/dp15"
        android:layout_marginTop="@dimen/dp10"
        android:layout_marginBottom="@dimen/dp20"
        android:enabled="true"
        android:text="@string/vtb_ra_continue" />

</androidx.appcompat.widget.LinearLayoutCompat>
