<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.vietinbank.feature_soft.common.view.VSoftHeaderView
        android:id="@+id/headerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:v_soft_title="Nhập mã PIN Soft OTP" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:paddingHorizontal="@dimen/dp15"
        android:paddingTop="@dimen/dp20">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tvSoft"
                android:layout_width="@dimen/dp0"
                android:layout_height="wrap_content"
                android:text="Quý khách vui lòng nhập mã PIN Soft OTP để tiếp tục giao dịch"
                android:textColor="@color/text_blue_09"
                android:textSize="@dimen/sp16"
                app:fontCus="semi_bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivEyeInput"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp8"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvSoft"
                app:srcCompat="@drawable/v_soft_ic_show_eye" />

            <com.vietinbank.feature_soft.common.view.VSoftInputOTPView
                android:id="@+id/inputPin"
                android:fontFamily="@font/bold"
                android:layout_width="@dimen/dp0"
                android:layout_height="@dimen/dp53"
                android:layout_marginTop="@dimen/dp8"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:inputType="numberDecimal"
                android:maxLength="6"
                android:textSize="@dimen/sp20"
                app:layout_constraintEnd_toEndOf="@id/tvSoft"
                app:layout_constraintStart_toStartOf="@id/tvSoft"
                app:layout_constraintTop_toBottomOf="@id/ivEyeInput"
                app:v_soft_otp_margin="@dimen/dp8"
                app:v_soft_otp_width="@dimen/dp53" />

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tvNote"
                android:layout_width="@dimen/dp0"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:text="@string/vtb_ra_authen_soft_note"
                android:textColor="@color/text_blue_09"
                android:textSize="@dimen/sp14"
                app:fontCus="medium"
                app:layout_constraintEnd_toEndOf="@id/tvSoft"
                app:layout_constraintStart_toStartOf="@id/tvSoft"
                app:layout_constraintTop_toBottomOf="@id/inputPin" />

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tvForgot"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/dp50"
                android:textColor="@color/text_blue_09"
                android:textSize="@dimen/sp16"
                app:fontCus="semi_bold"
                app:layout_constraintEnd_toEndOf="@id/tvSoft"
                app:layout_constraintStart_toStartOf="@id/tvSoft"
                app:layout_constraintTop_toBottomOf="@id/tvNote"
                tools:text="Quên mã PIN" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <com.vietinbank.core_ui.base.views.BaseButton
        android:id="@+id/btnNext"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp48"
        android:layout_marginHorizontal="@dimen/dp15"
        android:layout_marginTop="@dimen/dp10"
        android:layout_marginBottom="@dimen/dp20"
        android:text="@string/vtb_ra_continue" />

</androidx.appcompat.widget.LinearLayoutCompat>
