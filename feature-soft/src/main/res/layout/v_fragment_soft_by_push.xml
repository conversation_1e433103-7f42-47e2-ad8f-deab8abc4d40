<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.vietinbank.feature_soft.common.view.VSoftHeaderView
        android:id="@+id/headerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:v_soft_title="Vietinbank OTP" />


    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp0"
        android:layout_weight="1"
        android:paddingHorizontal="@dimen/dp15"
        android:paddingTop="@dimen/dp20">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

        </androidx.appcompat.widget.LinearLayoutCompat>

    </androidx.core.widget.NestedScrollView>


    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:paddingHorizontal="@dimen/dp16"
        android:paddingTop="@dimen/dp8"
        android:paddingBottom="@dimen/dp20">

        <!-- Button Xóa -->
        <com.vietinbank.core_ui.base.views.BaseTextView
            android:id="@+id/vtb_btn_cancel"
            android:layout_width="@dimen/dp0"
            android:layout_height="@dimen/dp48"
            android:layout_marginEnd="@dimen/dp16"
            android:layout_weight="1"
            android:background="@drawable/bg_button_disable"
            android:gravity="center"
            android:text="Hủy"
            android:textColor="@color/text_blue_02"
            android:textSize="@dimen/sp14"
            app:fontCus="semi_bold" />

        <!-- Button Sao chép -->
        <com.vietinbank.core_ui.base.views.BaseButton
            android:id="@+id/vtb_btn_approve"
            android:layout_width="@dimen/dp0"
            android:layout_height="@dimen/dp48"
            android:layout_weight="1"
            android:text="Tiếp tục" />

    </androidx.appcompat.widget.LinearLayoutCompat>

</androidx.appcompat.widget.LinearLayoutCompat>
