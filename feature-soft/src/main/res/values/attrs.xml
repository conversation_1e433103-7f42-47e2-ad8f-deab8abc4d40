<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!--    7420 - Module registes account -->
    <attr name="v_soft_title" format="string" />
    <attr name="v_soft_title_color" format="color" />
    <attr name="v_soft_title_size" format="dimension" />
    <attr name="v_soft_title_font" format="enum">
        <enum name="Regular" value="0" />
        <enum name="Medium" value="1" />
        <enum name="SemiBold" value="2" />
        <enum name="Bold" value="3" />
    </attr>

    <attr name="v_soft_value" format="string" />
    <attr name="v_soft_value_color" format="color" />
    <attr name="v_soft_value_size" format="dimension" />
    <attr name="v_soft_src_right" format="reference" />

    <attr name="v_soft_value_font" format="enum">
        <enum name="Regular" value="0" />
        <enum name="Medium" value="1" />
        <enum name="SemiBold" value="2" />
        <enum name="Bold" value="3" />
    </attr>

    <declare-styleable name="VSoftHeaderView">
        <attr name="v_soft_title" />
        <attr name="v_soft_title_color" />
        <attr name="v_soft_is_show_home" format="boolean" />

    </declare-styleable>

    <declare-styleable name="VSoftInfoView">
        <attr name="v_soft_title" />
        <attr name="v_soft_title_color" />
        <attr name="v_soft_value" />
        <attr name="v_soft_value_color" />
        <attr name="v_soft_value_font" />
        <attr name="v_soft_title_font" />
        <attr name="v_soft_title_size" />
        <attr name="v_soft_value_size" />
        <attr name="v_soft_src_right" />

    </declare-styleable>

    <declare-styleable name="VSoftInputView">
        <attr name="v_soft_title" />
        <attr name="v_soft_title_color" />
        <attr name="v_soft_title_color_focus" format="color" />
        <attr name="v_soft_clear_text" format="boolean" />
        <attr name="v_soft_value" />
        <attr name="v_soft_value_color" />
        <attr name="v_soft_value_font" />
        <attr name="v_soft_title_font" />
        <attr name="v_soft_src_right" />
        <attr name="android:inputType" />
        <attr name="android:imeOptions" />
        <attr name="android:maxLength" />
        <attr name="v_soft_is_amount" format="boolean" />

    </declare-styleable>

    <declare-styleable name="VSoftInputOTPView">
        <attr name="android:height" />
        <attr name="android:width" />
        <attr name="android:maxLength" />
        <attr name="android:textSize" />
        <attr name="v_soft_otp_margin" format="dimension" />
        <attr name="v_soft_otp_width" format="dimension" />
        <attr name="v_soft_otp_stroke_width" format="dimension" />
        <attr name="v_soft_otp_radius" format="dimension" />
        <attr name="v_soft_otp_text_show" format="boolean" />
        <attr name="v_soft_otp_background_type" format="enum">
            <enum name="line" value="0" />
            <enum name="rect" value="1" />
            <enum name="round" value="2" />
        </attr>

    </declare-styleable>

</resources>