<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/soft_otp_nav_graph"
    app:startDestination="@id/termSoftOTPFragment">

    <fragment
        android:id="@+id/activeSoftOTPFragment"
        android:name="com.vietinbank.feature_soft.views.newui.fragment.active.ActiveSoftOTPFragment"
        android:label="ActiveSoftOTPFragment">
        <deepLink app:uri="soft://active_soft/{KEY_FLOW_OTP}" />
    </fragment>

    <fragment
        android:id="@+id/termSoftOTPFragment"
        android:name="com.vietinbank.feature_soft.views.newui.fragment.term.TermSoftOTPFragment"
        android:label="TermSoftOTPFragment" >
        <deepLink app:uri="soft://term_soft/{KEY_FLOW_OTP}" />
    </fragment>

    <fragment
        android:id="@+id/confirmOTPFragment"
        android:name="com.vietinbank.feature_soft.views.newui.fragment.confirm_otp.ConfirmOTPFragment"
        android:label="ConfirmOTPFragment">
        <deepLink app:uri="soft://confirm_otp/{KEY_FLOW_OTP}" />
    </fragment>

    <fragment
        android:id="@+id/installOTPFragment"
        android:name="com.vietinbank.feature_soft.views.newui.fragment.install_otp.InstallOTPFragment" />

    <dialog
        android:id="@+id/softOTPNoticeDialog"
        android:name="com.vietinbank.feature_soft.views.newui.dialog.SoftOTPNoticeDialog"
        android:label="SoftOTPNoticeDialog">
        <deepLink app:uri="soft://notice_dialog" />
    </dialog>

    <fragment
        android:id="@+id/testSoftOTPFragment"
        android:name="com.vietinbank.feature_soft.views.newui.TestSoftOTPFragment"
        android:label="TestSoftOTPFragment">
        <deepLink app:uri="soft://test_otp" />
    </fragment>


</navigation>