<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/v_soft_otp_nav"
    app:startDestination="@id/vHome">

    <!--    Soft module-->
    <fragment
        android:id="@+id/vHome"
        android:name="com.vietinbank.feature_soft.views.fragment.VSoftHomeFragment"
        android:label="VSoftOTPHomeFragment"
        tools:layout="@layout/v_fragment_soft_home" >
        <deepLink app:uri="soft://home" />
    </fragment>

    <fragment
        android:id="@+id/vSoftActiveFragment"
        android:name="com.vietinbank.feature_soft.views.fragment.active.VSoftActiveFragment"
        android:label="VSoftActiveFragment"
        tools:layout="@layout/v_fragment_soft_active">
        <deepLink app:uri="soft://active" />
    </fragment>

    <fragment
        android:id="@+id/vSoftOTPSetPinFragment"
        android:name="com.vietinbank.feature_soft.views.fragment.pin.VSoftSetPinFragment"
        android:label="VSoftSetPinFragment"
        tools:layout="@layout/v_fragment_soft_set_pin" />

    <fragment
        android:id="@+id/vSoftPinFragment"
        android:name="com.vietinbank.feature_soft.views.fragment.pin.VSoftPinFragment"
        android:label="VSoftPinFragment"
        tools:layout="@layout/v_fragment_soft_pin">
        <deepLink app:uri="soft://enter_pin/{KEY_FLOW_OTP}" />
    </fragment>

    <fragment
        android:id="@+id/vSoftMethodFragment"
        android:name="com.vietinbank.feature_soft.views.fragment.active.VSoftMethodFragment"
        android:label="VSoftMethodFragment"
        tools:layout="@layout/v_fragment_soft_method" />

    <fragment
        android:id="@+id/vSoftTranEfastFragment"
        android:name="com.vietinbank.feature_soft.views.fragment.gensoft.VSoftTranEFastFragment"
        android:label="VSoftTranEFastFragment"
        tools:layout="@layout/v_fragment_soft_authen" />

    <fragment
        android:id="@+id/vSoftTranPushFragment"
        android:name="com.vietinbank.feature_soft.views.fragment.gensoft.VSoftTranByPushFragment"
        android:label="VSoftTranOfflineFragment"
        tools:layout="@layout/v_fragment_soft_authen" />

    <fragment
        android:id="@+id/vSoftTranByOfflineFragment"
        android:name="com.vietinbank.feature_soft.views.fragment.offline.VSoftTranByOfflineFragment"
        android:label="VSoftTranResultFragment"
        tools:layout="@layout/v_fragment_soft_by_offline" />

    <fragment
        android:id="@+id/vSoftTranByScanQRFragment"
        android:name="com.vietinbank.feature_soft.views.fragment.offline.VSoftTranByScanQRFragment"
        android:label="VSoftTranByScanQRFragment"
        tools:layout="@layout/v_fragment_soft_off_scan_qr" />

    <fragment
        android:id="@+id/vSoftTranByInputFragment"
        android:name="com.vietinbank.feature_soft.views.fragment.offline.VSoftTranByInputFragment"
        android:label="VSoftTranByInputFragment"
        tools:layout="@layout/v_fragment_soft_off_input" />

    <fragment
        android:id="@+id/vSoftListUserFragment"
        android:name="com.vietinbank.feature_soft.views.fragment.gensoft.VSoftListUserFragment"
        android:label="VSoftListUserFragment"
        tools:layout="@layout/v_fragment_soft_list_user" />

    <fragment
        android:id="@+id/vSoftTranResultFragment"
        android:name="com.vietinbank.feature_soft.views.fragment.result.VSoftTranResultFragment"
        android:label="VSoftTranResultFragment"
        tools:layout="@layout/v_fragment_soft_result" />

    <fragment
        android:id="@+id/vSoftTranVerifyFragment"
        android:name="com.vietinbank.feature_soft.views.fragment.online.VSoftTranVerifyFragment"
        android:label="VSoftTranVerifyFragment"
        tools:layout="@layout/v_fragment_soft_tran_verify" />

    <fragment
        android:id="@+id/vSoftTranConfirmFragment"
        android:name="com.vietinbank.feature_soft.views.fragment.online.VSoftTranConfirmFragment"
        android:label="VSoftTranConfirmFragment"
        tools:layout="@layout/v_fragment_soft_tran_confirm" />

    <!-- Keypass    -->
    <fragment
        android:id="@+id/vKeypassMainFragment"
        android:name="com.vietinbank.feature_soft.views.fragment.keypass.VKeypassMainFragment"
        android:label="VKeypassMainFragment"
        tools:layout="@layout/v_fragment_keypass_main">
        <deepLink app:uri="keypass://main" />

    </fragment>

    <fragment
        android:id="@+id/vKeypassForgotFragment"
        android:name="com.vietinbank.feature_soft.views.fragment.keypass.VKeypassForgotFragment"
        android:label="VKeypassForgotFragment"
        tools:layout="@layout/v_fragment_keypass_forgot_pin" />

    <fragment
        android:id="@+id/vKeypassInfoFragment"
        android:name="com.vietinbank.feature_soft.views.fragment.keypass.VKeypassInfoFragment"
        android:label="VKeypassInfoFragment"
        tools:layout="@layout/v_fragment_keypass_info" />

</navigation>