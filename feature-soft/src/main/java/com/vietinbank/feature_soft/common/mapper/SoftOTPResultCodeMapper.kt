package com.vietinbank.feature_soft.common.mapper

import android.content.Context
import com.vietinbank.core_domain.models.soft.SoftSDKErrorCode
import com.vietinbank.feature_soft.R
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SoftOTPResultCodeMapper @Inject constructor(
    @ApplicationContext private val context: Context,
) {

    fun toMessage(
        errorCode: SoftSDKErrorCode,
    ): String {
        return when (errorCode) {
            SoftSDKErrorCode.FAIL -> context.getString(R.string.sdk_fail)
            SoftSDKErrorCode.WRONG_ACTIVATION_5_TIMES -> context.getString(R.string.sdk_wrong_activation_5_times)
            SoftSDKErrorCode.WRONG_ACTIVATION_CODE -> context.getString(R.string.sdk_wrong_activation_code)
            SoftSDKErrorCode.ACTIVATION_CODE_EXPIRED -> context.getString(R.string.sdk_activation_code_expired)
            SoftSDKErrorCode.WRONG_PIN -> context.getString(R.string.sdk_wrong_pin)
            SoftSDKErrorCode.CONNECTION_TIMEOUT -> context.getString(R.string.sdk_connection_timeout)
            SoftSDKErrorCode.CONNECTION_FAILED_1 -> context.getString(R.string.sdk_connection_failed_1)
            SoftSDKErrorCode.CONNECTION_FAILED_2 -> context.getString(R.string.sdk_connection_failed_2)
            SoftSDKErrorCode.SYNC_LIMIT_EXCEEDED -> context.getString(R.string.sdk_sync_limit_exceeded)
            SoftSDKErrorCode.ROOTED_DEVICE -> context.getString(R.string.sdk_rooted_device)
            SoftSDKErrorCode.INVALID_URL -> context.getString(R.string.sdk_invalid_url)
            SoftSDKErrorCode.NOT_ACTIVATED -> context.getString(R.string.sdk_not_activated)
            SoftSDKErrorCode.INVALID_DATA -> context.getString(R.string.sdk_invalid_data)
            SoftSDKErrorCode.DB_ERROR -> context.getString(R.string.sdk_db_error)
            SoftSDKErrorCode.TRANSACTION_TIMEOUT -> context.getString(R.string.sdk_transaction_timeout)
            SoftSDKErrorCode.TRANSACTION_UPDATE_FAIL -> context.getString(R.string.sdk_transaction_update_fail)
            SoftSDKErrorCode.CHALLENGE_CODE_USED -> context.getString(R.string.sdk_challenge_code_used)
            SoftSDKErrorCode.TRANSACTION_NOT_FOUND -> context.getString(R.string.sdk_transaction_not_found)
            SoftSDKErrorCode.SYSTEM_ERROR -> context.getString(R.string.sdk_system_error)
            SoftSDKErrorCode.CALLBACK_FAILED -> context.getString(R.string.sdk_callback_failed)
            else -> context.getString(R.string.sdk_fail)
        }
    }
}