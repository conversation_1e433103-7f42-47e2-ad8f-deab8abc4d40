package com.vietinbank.feature_soft.common.view

import android.content.Context
import android.content.res.ColorStateList
import android.text.Editable
import android.text.InputFilter
import android.text.SpannableStringBuilder
import android.text.TextWatcher
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.ColorInt
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.extensions.applySpan
import com.vietinbank.core_common.extensions.showKeyboardExt
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.feature_soft.R
import com.vietinbank.feature_soft.common.interfaces.VSoftInfoViewListener
import com.vietinbank.feature_soft.databinding.VCustomSoftInputBinding

class VSoftInputView : LinearLayout, VSoftInfoViewListener {
    companion object {
        const val MAX_LENGTH = 160
    }

    var maxLength: Int = MAX_LENGTH
    var isShowClearText = false
    var onTextChange: ((String) -> Unit)? = null
    var onFocusListener: ((Boolean) -> Unit)? = null
    lateinit var binding: VCustomSoftInputBinding

    constructor(context: Context) : super(context) {
        init(context, null)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context, attrs)
    }

    fun init(context: Context, attrs: AttributeSet?) {
        binding = VCustomSoftInputBinding.inflate(LayoutInflater.from(context), this, true)
        val attributes =
            context.obtainStyledAttributes(attrs, R.styleable.VSoftInputView, 0, 0)

        try {
            binding.vtbLayoutInput.typeface = Utils.g().getFont(
                attributes.getInt(
                    R.styleable.VSoftInputView_v_soft_title_font,
                    2,
                ),
                context,
            )
            binding.vtbLayoutInput.isSaveEnabled = false
            isShowClearText =
                attributes.getBoolean(R.styleable.VSoftInputView_v_soft_clear_text, true)

            setVtbTitle(
                attributes.getString(R.styleable.VSoftInputView_v_soft_title),
                attributes.getColor(
                    R.styleable.VSoftInputView_v_soft_title_color,
                    ContextCompat.getColor(context, com.vietinbank.core_ui.R.color.text_blue_07),
                ),
            )

            setVtbValue(
                attributes.getString(R.styleable.VSoftInputView_v_soft_value),
                attributes.getColor(
                    R.styleable.VSoftInputView_v_soft_value_color,
                    ContextCompat.getColor(context, com.vietinbank.core_ui.R.color.text_blue_02),
                ),
            )

            setVtbMaxLength(
                attributes.getInt(
                    R.styleable.VSoftInputView_android_maxLength,
                    MAX_LENGTH,
                ),
            )
            setVtbInputType(attributes.getInt(R.styleable.VSoftInputView_android_inputType, -1))
            setImeOptions(attributes.getInt(R.styleable.VSoftInputView_android_imeOptions, -1))

            binding.apply {
                vtbIvClear.setThrottleClickListener {
                    vtbEdt.setText("")
                }

                vtbEdt.addTextChangedListener(object : TextWatcher {
                    override fun beforeTextChanged(
                        s: CharSequence?,
                        start: Int,
                        count: Int,
                        after: Int,
                    ) {
                    }

                    override fun onTextChanged(
                        s: CharSequence?,
                        start: Int,
                        before: Int,
                        count: Int,
                    ) {
                        vtbIvClear.isVisible = isShowClearText && !vtbEdt.text.isNullOrEmpty()
                        onTextChange?.invoke(vtbEdt.text.toString())
                    }

                    override fun afterTextChanged(s: Editable?) {
                    }
                })

                vtbEdt.setOnFocusChangeListener { _, hasFocus ->
                    if (hasFocus) {
                        binding.vtbLine.setBackgroundColor(
                            ContextCompat.getColor(
                                context,
                                com.vietinbank.core_ui.R.color.text_gray_200,
                            ),
                        )
                        vtbIvClear.isVisible = isShowClearText && !vtbEdt.text.isNullOrEmpty()
                        vtbTvMore.isVisible = false
                    } else {
                        vtbIvClear.isVisible = false
                    }
                    onFocusListener?.invoke(hasFocus)
                }
            }
        } finally {
            attributes.recycle()
        }
    }

    private fun setVtbTitle(title: String?, @ColorInt color: Int) {
        binding.vtbLayoutInput.hint = title ?: ""
        binding.vtbLayoutInput.hintTextColor = ColorStateList.valueOf(color)
    }

    private fun setVtbInputType(inputType: Int) {
        if (inputType != -1) binding.vtbEdt.inputType = inputType
    }

    private fun setVtbMaxLength(_maxLength: Int) {
        maxLength = _maxLength
        binding.vtbEdt.filters = arrayOf(InputFilter.LengthFilter(maxLength))
    }

    fun setVtbMoreInfo(content: String?, color: Int = 0) {
        binding.vtbTvMore.isVisible = !content.isNullOrEmpty()
        binding.vtbTvMore.text = content
        if (color != 0) {
            binding.vtbTvMore.setTextColor(color)
        }
    }

    override fun setVtbError(content: String?) {
        content?.let {
            binding.vtbTvMore.isVisible = true
            binding.vtbTvMore.text = it
        }
        binding.vtbLine.setBackgroundColor(
            ContextCompat.getColor(
                context,
                com.vietinbank.core_ui.R.color.text_primary_red,
            ),
        )
    }

    override fun setVtbFieldRequire(highlight: String?, color: Int) {
        try {
            highlight?.let {
                val span = SpannableStringBuilder(binding.vtbLayoutInput.hint.toString()).applySpan(
                    text = it,
                    colorInt = color,
                )
                binding.vtbLayoutInput.hint = span
            }
        } catch (e: Exception) {
        }
    }

    override fun getVtbValue(): String {
        return binding.vtbEdt.text.toString()
    }

    override fun setVtbValue(content: String?, color: Int?, font: String?) {
        binding.vtbEdt.apply {
            setText(content ?: "")
            color?.let { setTextColor(it) }
            font?.let { typeface = Utils.g().getFont(it.toIntOrNull(), context) }
        }
    }

    fun getVtbMoreInfo(): String {
        return binding.vtbTvMore.text.toString()
    }

    fun setOnEditorActionListener(onEditorActionListener: TextView.OnEditorActionListener?) {
        binding.vtbEdt.setOnEditorActionListener(onEditorActionListener)
    }

    private fun setImeOptions(imeOption: Int) {
        if (imeOption != -1) binding.vtbEdt.imeOptions = imeOption
    }

    fun setVtbEdtFocus() {
        try {
            binding.vtbEdt.requestFocus()
            binding.vtbEdt.showKeyboardExt()
        } catch (_: Exception) {
        }
    }
}
