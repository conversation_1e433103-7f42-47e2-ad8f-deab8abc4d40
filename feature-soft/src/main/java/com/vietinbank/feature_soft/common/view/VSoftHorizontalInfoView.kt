package com.vietinbank.feature_soft.common.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import androidx.annotation.ColorInt
import androidx.core.content.ContextCompat
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.feature_soft.R
import com.vietinbank.feature_soft.common.interfaces.VSoftInfoViewListener
import com.vietinbank.feature_soft.databinding.VCustomSoftHorizonalInfoBinding

class VSoftHorizontalInfoView : LinearLayout, VSoftInfoViewListener {

    lateinit var binding: VCustomSoftHorizonalInfoBinding

    constructor(context: Context) : super(context) {
        init(context, null)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context, attrs)
    }

    fun init(context: Context, attrs: AttributeSet?) {
        binding = VCustomSoftHorizonalInfoBinding.inflate(LayoutInflater.from(context), this, true)
        val attributes =
            context.obtainStyledAttributes(attrs, R.styleable.VSoftInfoView, 0, 0)

        try {
            setVtbInfo(
                binding.vtbTvTitle,
                attributes.getString(R.styleable.VSoftInfoView_v_soft_title),
                attributes.getColor(
                    R.styleable.VSoftInfoView_v_soft_title_color,
                    ContextCompat.getColor(context, com.vietinbank.core_ui.R.color.text_blue_07),
                ),
                attributes.getString(
                    R.styleable.VSoftInfoView_v_soft_title_font,
                ) ?: "1", // default font medium
                attributes.getDimension(
                    R.styleable.VSoftInfoView_v_soft_title_size,
                    resources.getDimension(com.vietinbank.core_ui.R.dimen.sp14),
                ),
            )

            setVtbInfo(
                binding.vtbTvValue,
                attributes.getString(R.styleable.VSoftInfoView_v_soft_value),
                attributes.getColor(
                    R.styleable.VSoftInfoView_v_soft_value_color,
                    ContextCompat.getColor(context, com.vietinbank.core_ui.R.color.text_gray_08),
                ),
                attributes.getString(
                    R.styleable.VSoftInfoView_v_soft_value_font,
                ) ?: "1", // default font medium
                attributes.getDimension(
                    R.styleable.VSoftInfoView_v_soft_value_size,
                    resources.getDimension(com.vietinbank.core_ui.R.dimen.sp14),
                ),
            )
        } finally {
            attributes.recycle()
        }
    }

    override fun setVtbFieldRequire(highlight: String?, color: Int) {
    }

    override fun getVtbValue(): String {
        return binding.vtbTvValue.text.toString()
    }

    override fun setVtbValue(content: String?, @ColorInt color: Int?, font: String?) {
        binding.vtbTvValue.apply {
            text = content ?: ""
            color?.let { setTextColor(it) }
            font?.let { typeface = Utils.g().getFont(it.toIntOrNull(), context) }
        }
    }

    override fun setVtbError(content: String?) {
    }
}