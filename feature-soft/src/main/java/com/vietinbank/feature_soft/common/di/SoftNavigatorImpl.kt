package com.vietinbank.feature_soft.common.di

import android.os.Bundle
import android.os.Parcelable
import androidx.fragment.app.FragmentManager
import androidx.navigation.NavController
import androidx.navigation.NavOptions
import com.vietinbank.core_common.nav.ISoftNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.feature_soft.R
import java.io.Serializable

class SoftNavigatorImpl(
    private val navController: NavController,
    private val fragmentManager: FragmentManager,
) : ISoftNavigator {

    override fun popBackStack() {
        navController.popBackStack()
    }

    override fun setFragmentResult(requestKey: String, result: Bundle) {
        // Lấy NavHostFragment
        fragmentManager.setFragmentResult(requestKey, result)
    }

    /**
     * Tạo NavOptions cho màn forward:
     * - enterAnim, exitAnim, popEnterAnim, popExitAnim
     */
    private fun createSlideNavOptions(): NavOptions {
        val builder = NavOptions.Builder()
            .setEnterAnim(com.vietinbank.core_common.R.anim.core_slide_in_right)
            .setExitAnim(com.vietinbank.core_common.R.anim.core_slide_out_left)
            .setPopEnterAnim(com.vietinbank.core_common.R.anim.core_slide_in_left)
            .setPopExitAnim(com.vietinbank.core_common.R.anim.core_slide_out_right)
        return builder.build()
    }

    private fun createBundle(vararg pair: Pair<String, Any?>): Bundle {
        val bundle = Bundle()
        pair.forEach {
            val first = it.first
            val second = it.second
            second?.let {
                when (second) {
                    is Boolean -> bundle.putBoolean(first, second)
                    is Byte -> bundle.putByte(first, second)
                    is Short -> bundle.putShort(first, second)
                    is Int -> bundle.putInt(first, second)
                    is Long -> bundle.putLong(first, second)
                    is Float -> bundle.putFloat(first, second)
                    is Double -> bundle.putDouble(first, second)
                    is Char -> bundle.putChar(first, second)
                    is String -> bundle.putString(first, second)
                    is CharSequence -> bundle.putCharSequence(first, second)
                    is Parcelable -> bundle.putParcelable(first, second)
                    is Serializable -> bundle.putSerializable(first, second)
                    else -> bundle.putString(first, Utils.g().provideGson().toJson(second))
                }
            }
        }
        return bundle
    }

    override fun goToVerifyPIN(vararg pair: Pair<String, Any?>) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.vSoftMethodFragment, createBundle(*pair), navOptions)
    }

    override fun goToEnterPIN(vararg pair: Pair<String, Any?>) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.vSoftPinFragment, createBundle(*pair), navOptions)
    }

    override fun goToSetPIN(vararg pair: Pair<String, Any?>) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.vSoftOTPSetPinFragment, createBundle(*pair), navOptions)
    }

    override fun popToActiveSoft() {
        navController.popBackStack(R.id.vSoftActiveFragment, false)
    }

    override fun goToActiveSoft(vararg pair: Pair<String, Any?>) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.vSoftActiveFragment, createBundle(*pair), navOptions)
    }

    override fun goToListUser(vararg pair: Pair<String, Any?>) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.vSoftListUserFragment, createBundle(*pair), navOptions)
    }

    override fun goToGenOTP(vararg pair: Pair<String, Any?>) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.vSoftTranResultFragment, createBundle(*pair), navOptions)
    }

    override fun goToVerifyOffline(vararg pair: Pair<String, Any?>) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.vSoftTranByOfflineFragment, createBundle(*pair), navOptions)
    }

    override fun goToScanInfo(vararg pair: Pair<String, Any?>) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.vSoftTranByScanQRFragment, createBundle(*pair), navOptions)
    }

    override fun goToInputInfo(vararg pair: Pair<String, Any?>) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.vSoftTranByInputFragment, createBundle(*pair), navOptions)
    }

    override fun goToVerifyOnline(vararg pair: Pair<String, Any?>) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.vSoftTranVerifyFragment, createBundle(*pair), navOptions)
    }

    override fun goToConfirmOnline(vararg pair: Pair<String, Any?>) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.vSoftTranConfirmFragment, createBundle(*pair), navOptions)
    }

    override fun goToHomeSoft(vararg pair: Pair<String, Any?>) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.vHome, createBundle(*pair), navOptions)
    }

    override fun goToSoftInEfast(vararg pair: Pair<String, Any?>) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.vSoftTranEfastFragment, createBundle(*pair), navOptions)
    }

    override fun popBackGraph() {
        navController.popBackStack(R.id.v_soft_otp_nav, false)
    }

    override fun goToKeypassMain() {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.vKeypassMainFragment, null, navOptions)
    }

    override fun goToKeypassForgotPin() {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.vKeypassForgotFragment, null, navOptions)
    }

    override fun goToKeypassInfo() {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.vKeypassInfoFragment, null, navOptions)
    }

    override fun popToKeypassMain() {
        navController.popBackStack(R.id.vKeypassMainFragment, false)
    }
}