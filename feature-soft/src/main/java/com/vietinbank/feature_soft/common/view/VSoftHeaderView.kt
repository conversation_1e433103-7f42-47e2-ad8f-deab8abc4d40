package com.vietinbank.feature_soft.common.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.ColorInt
import androidx.core.view.isVisible
import androidx.navigation.findNavController
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.extensions.hideKeyboard
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.feature_soft.R
import com.vietinbank.feature_soft.databinding.VCustomSoftHeaderViewBinding

class VSoftHeaderView : LinearLayout {
    lateinit var binding: VCustomSoftHeaderViewBinding
    private var mHomePressedListener: (() -> Unit)? = null
    private var mBackPressedListener: (() -> Unit)? = null

    constructor(context: Context) : super(context) {
        init(context, null)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context, attrs)
    }

    @SuppressLint("ClickableViewAccessibility")
    fun init(context: Context, attrs: AttributeSet?) {
        val attributes = context.obtainStyledAttributes(attrs, R.styleable.VSoftHeaderView, 0, 0)
        binding = VCustomSoftHeaderViewBinding.inflate(LayoutInflater.from(context), this, true)

        try {
            binding.apply {
                vtbBtnBack.setThrottleClickListener {
                    mBackPressedListener?.invoke() ?: kotlin.runCatching {
                        hideKeyboard()
                        findNavController().popBackStack()
                    }
                }

                setVtbIconHome(
                    attributes.getBoolean(
                        R.styleable.VSoftHeaderView_v_soft_is_show_home,
                        false,
                    ),
                )

                setVtbInfo(
                    vtbTvHeaderTitle,
                    attributes.getString(R.styleable.VSoftHeaderView_v_soft_title),
                )

                vtbBtnHome.setThrottleClickListener {
                    mHomePressedListener?.invoke()
                }
            }
        } finally {
            attributes.recycle()
        }
    }

    fun setVtbOnBackPressed(listener: (() -> Unit)?) {
        mBackPressedListener = listener
    }

    fun setVtbOnBackPressed(it: Drawable?, listener: (() -> Unit)?) {
        mBackPressedListener = listener
        binding.vtbBtnBack.setImageDrawable(it)
    }

    fun setVtbOnHomePressed(listener: (() -> Unit)?) {
        mHomePressedListener = listener
    }

    private fun setVtbInfo(
        view: TextView?,
        title: String?,
        @ColorInt color: Int? = null,
        font: Int? = null,
    ) {
        view?.apply {
            text = title ?: ""
            color?.let { setTextColor(it) }
            font?.let { typeface = Utils.g().getFont(it, context) }
        }
    }

    fun setVtbTitleHeader(
        title: String?,
    ): VSoftHeaderView {
        binding.vtbTvHeaderTitle.text = title ?: ""
        return this
    }

    fun setVtbIconHome(isShow: Boolean = false) {
        binding.vtbBtnHome.isVisible = isShow
    }
}