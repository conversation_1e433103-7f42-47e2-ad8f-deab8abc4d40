package com.vietinbank.feature_soft.common.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.RectF
import android.text.Editable
import android.text.InputType
import android.text.TextPaint
import android.text.TextUtils
import android.text.TextWatcher
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatEditText
import androidx.core.content.ContextCompat
import com.vietinbank.feature_soft.R

class VSoftInputOTPView : AppCompatEditText {
    private lateinit var rectText: Rect
    private lateinit var rectDrawble: RectF
    private lateinit var paintDrawable: Paint
    private lateinit var paintBackground: Paint
    private lateinit var paintDrawableError: Paint
    private lateinit var paintDrawableFocus: Paint
    private lateinit var paintText: TextPaint
    private lateinit var paintDot: Paint
    private var otpWidth = 0f
    private var otpHeigth = 0f
    private var otpLength = MAX_LENGTH
    private var otpType = TYPE_LINE
    private var otpTextShow = true
    private var otpMargin = 10f
    private var otpStrokeWidth = 1f
    private var otpRadius = 0f
    private var isOTPError = false
    private var otpStart = 0f

    var onTextChange: ((String) -> Unit)? = null

    constructor(context: Context) : super(context) {
        initView(null)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        initView(attrs)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr,
    ) {
        initView(attrs)
    }

    companion object {
        const val TYPE_RECT = 0
        const val TYPE_LINE = 1
        const val TYPE_ROUND = 2

        const val MAX_LENGTH = 8
    }

    fun initView(attrs: AttributeSet?) {
        background = null
        isCursorVisible = false
        inputType = InputType.TYPE_CLASS_NUMBER
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.VSoftInputOTPView, 0, 0)
        // type
        otpType =
            typedArray.getInt(R.styleable.VSoftInputOTPView_v_soft_otp_background_type, TYPE_RECT)
        otpTextShow =
            typedArray.getBoolean(R.styleable.VSoftInputOTPView_v_soft_otp_text_show, true)

        otpWidth = typedArray.getDimension(
            R.styleable.VSoftInputOTPView_v_soft_otp_width,
            resources.getDimensionPixelSize(com.vietinbank.core_ui.R.dimen.dp4).toFloat(),
        )

        otpLength = typedArray.getInt(
            R.styleable.VSoftInputOTPView_android_maxLength,
            MAX_LENGTH,
        )

        otpRadius = typedArray.getDimension(
            R.styleable.VSoftInputOTPView_v_soft_otp_radius,
            resources.getDimensionPixelSize(com.vietinbank.core_ui.R.dimen.dp4).toFloat(),
        )

        otpMargin = typedArray.getDimension(
            R.styleable.VSoftInputOTPView_v_soft_otp_margin,
            resources.getDimensionPixelSize(com.vietinbank.core_ui.R.dimen.dp8).toFloat(),
        )

        otpStrokeWidth = typedArray.getDimension(
            R.styleable.VSoftInputOTPView_v_soft_otp_stroke_width,
            resources.getDimensionPixelSize(com.vietinbank.core_ui.R.dimen.dp0).toFloat(),
        )

        // ve drawble
        paintBackground = Paint().apply {
            color = ContextCompat.getColor(context, com.vietinbank.core_ui.R.color.white)
            style = Paint.Style.FILL
        }

        paintDrawable = Paint().apply {
            color = ContextCompat.getColor(context, com.vietinbank.core_data.R.color.white)
            style = Paint.Style.STROKE
            strokeWidth = otpStrokeWidth
        }

        // ve drawble focus
        paintDrawableFocus = Paint().apply {
            color = ContextCompat.getColor(context, com.vietinbank.core_ui.R.color.white) // vtb_efast_red
            style = Paint.Style.STROKE
            strokeWidth = otpStrokeWidth
        }

        // ve text
        paintText = TextPaint().apply {
            color = ContextCompat.getColor(context, com.vietinbank.core_ui.R.color.text_blue_01)
            textSize = <EMAIL>
            typeface = <EMAIL>
        }
        // pin show
        paintDot = Paint().apply {
            color = ContextCompat.getColor(context, com.vietinbank.core_ui.R.color.text_blue_01)
            style = Paint.Style.FILL
            strokeWidth = resources.getDimensionPixelSize(com.vietinbank.core_ui.R.dimen.dp1).toFloat()
        }

        // drawble
        rectDrawble = RectF()
        rectText = Rect()

        addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                if (p0.toString().length < otpLength) {
                    setDrawError(false)
                }
                onTextChange?.invoke(p0.toString())
            }

            override fun afterTextChanged(p0: Editable?) {
            }
        })
    }

    override fun onDraw(canvas: Canvas) {
        canvas.let {
            when (otpType) {
                TYPE_ROUND -> onDrawRound(it)
                TYPE_LINE -> onDrawLine(it)
                else -> onDrawRect(it)
            }
            // ve text
            if (otpTextShow) {
                onDrawText(it)
            } else {
                onDrawDot(it)
            }
        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        val otpWidthTmp = otpMargin * (otpLength - 1) + otpWidth * otpLength
        if (measuredWidth < otpWidthTmp) {
            otpWidth = (measuredWidth - otpMargin * (otpLength - 1)) / otpLength
        }
        otpStart = (measuredWidth - otpWidth * otpLength - otpMargin * (otpLength - 1)) / 2f
        otpHeigth = measuredHeight * 1f
    }

    fun onDrawRect(canvas: Canvas?) {
//        var startX = 0f
        for (i in 0..(otpLength - 1)) {
            rectDrawble = RectF(
                otpStart + i * (otpWidth + otpMargin),
                0f,
                otpStart + i * (otpWidth + otpMargin) + otpWidth,
                otpHeigth,
            )
            canvas?.drawRoundRect(
                rectDrawble,
                otpRadius,
                otpRadius,
                paintBackground,
            )
            canvas?.drawRoundRect(
                rectDrawble,
                otpRadius,
                otpRadius,
                if (isOTPError) paintDrawableError else if (i == (text.toString().length - 1)) paintDrawableFocus else paintDrawable,
            )
        }
    }

    fun onDrawLine(canvas: Canvas?) {
        for (i in 0..(otpLength - 1)) {
            rectDrawble = RectF(
                otpStart + i * (otpWidth + otpMargin),
                0f,
                otpStart + i * (otpWidth + otpMargin) + otpWidth,
                otpHeigth,
            )
            canvas?.drawRect(
                rectDrawble,
                paintBackground,
            )
            canvas?.drawLine(
                otpStart + i * (otpWidth + otpMargin),
                otpStart + otpHeigth - otpStrokeWidth,
                i * (otpWidth + otpMargin) + otpWidth,
                otpHeigth - otpStrokeWidth,
                if (isOTPError) paintDrawableError else if (i == (text.toString().length - 1)) paintDrawableFocus else paintDrawable,
            )
        }
    }

    fun onDrawRound(canvas: Canvas?) {
        for (i in 0 until otpLength) {
            canvas?.drawCircle(
                otpStart + (otpWidth + otpMargin) * i + otpWidth / 2f,
                otpHeigth / 2f,
                otpWidth / 2f,
                paintBackground,
            )

            canvas?.drawCircle(
                otpStart + (otpWidth + otpMargin) * i + otpWidth / 2f,
                otpHeigth / 2f,
                otpWidth / 2f,
                if (isOTPError) paintDrawableError else if (i == (text.toString().length - 1)) paintDrawableFocus else paintDrawable,
            )
        }
    }

    fun onDrawDot(canvas: Canvas?) {
        // ve text
        val otpInput = text.toString()
        if (!TextUtils.isEmpty(otpInput)) {
            for (i in otpInput.indices) {
                canvas?.drawCircle(
                    otpStart + (otpWidth + otpMargin) * i + otpWidth / 2f,
                    otpHeigth / 2f,
                    resources.getDimension(com.vietinbank.core_ui.R.dimen.dp4),
                    paintDot,
                )
            }
        }
    }

    fun onDrawText(canvas: Canvas?) {
        // ve text
        val otpInput = text.toString()
        if (!TextUtils.isEmpty(otpInput)) {
            for (i in otpInput.indices) {
                paintText.getTextBounds(otpInput.get(i).toString(), 0, 1, rectText)
                val startY = otpHeigth / 2f + rectText.height().toFloat() / 2f
                val startX =
                    otpStart + otpWidth / 2f - rectText.width()
                        .toFloat() / 2f + (otpWidth + otpMargin) * i
                canvas?.drawText(otpInput.get(i).toString(), 0, 1, startX, startY, paintText)
            }
        }
    }

    fun setDrawError(isError: Boolean) {
        this.isOTPError = isError
        requestLayout()
        invalidate()
    }

    fun setVtbShowOTP(isShow: Boolean) {
        otpTextShow = isShow
        requestLayout()
        invalidate()
    }

    fun clearText() {
        this.setText("")
    }
}