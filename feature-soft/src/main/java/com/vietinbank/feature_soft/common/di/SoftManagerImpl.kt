package com.vietinbank.feature_soft.common.di

import android.content.Context
import android.text.TextUtils
import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.lockSoftToTime
import com.vietinbank.core_common.extensions.toTimeInMillis
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.soft.SoftEntity
import com.vietinbank.core_domain.models.soft.SoftUserEntity
import com.vietinbank.core_domain.softotp.IPinResult
import com.vietinbank.core_domain.softotp.ISoftManager
import com.vietinbank.core_domain.softotp.ISoftResult
import com.vietinbank.core_ui.R
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import vn.mk.otp.OtpSdk
import vn.mk.token.sdk.SdkNative
import java.util.Calendar
import javax.inject.Inject

class SoftManagerImpl @Inject constructor(
    private val userProf: IUserProf,
    private val appConfig: IAppConfigManager,
    private val resourceProvider: IResourceProvider,

) : ISoftManager {
    private var transactionData: String? = null
    private var challenge: String? = null
    private var softResultListener: ISoftResult? = null
    private var pinListener: IPinResult? = null
    override var isAppActive: Boolean = false
    override var isUserActive: Boolean = false
    override var isSkipVerify: Boolean = false
    override var wrongPinCounter: Int = 0
    override fun initSoft(context: Context) {
        OtpSdk.init(context)
    }

    override fun clearSoftCache() {
        transactionData = null
        challenge = null
        isAppActive = false
        isSkipVerify = false
        isUserActive = false
        wrongPinCounter = 0
    }

    override fun loadSoftStatus() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                clearSoftCache()
                if (appConfig.getStatusSoft() && !TextUtils.isEmpty(
                        userProf.getKeypassProfile() ?: "",
                    )
                ) {
                    val result = SdkNative.getInstance().userListInJson
                    val type = object : TypeToken<List<SoftUserEntity>>() {}.type
                    val transactions: List<SoftUserEntity> =
                        Utils.g().provideGson().fromJson(String(result), type)
                    if (transactions.isNotEmpty()) {
                        isAppActive = true
                        val userCheck =
                            transactions.firstOrNull { item -> item.userID == userProf.getKeypassProfile() }
                        if (null != userCheck) {
                            // da kich hoat
                            isUserActive = true
                        }
                    } else if (SdkNative.getInstance().deleteAllExistingTokens()) {
                        isAppActive = false
                        appConfig.updateStatusSoft(false)
                    }
                }
            } catch (e: Exception) {
                println("OTP fun loadSoftStatus: ${e.message}")
            }
        }
    }

    override fun genSoftOtp(userId: String?, transactionId: String?, messageId: String?) {
        if (null == userId || null == transactionId || null == messageId) {
            println("OTP genOTP: userId, transactionId, messageId is null")
        } else {
            CoroutineScope(Dispatchers.Default).launch {
                try {
                    println("OTP gen check active: userId = $userId, transactionId = $transactionId, messageId=$messageId")
                    if (!isAppActive) {
                        // chua kich hoat soft
                        softResultListener?.isActive(false)
                        return@launch
                    } else if (!isSkipVerify) {
                        // gen soft lần đầu
                        val syncTimeCode = String(SdkNative.getInstance().doSyncTime()).toInt()
                        if (syncTimeCode != VSoftConstants.OTPResultCode.SUCCESS.code) {
                            softResultListener?.onError(
                                VSoftConstants.getErrorMessage(syncTimeCode),
                                syncTimeCode,
                            )
                            return@launch
                        } else if (SdkNative.getInstance().checkUserIdExistence(userId)) {
                            isSkipVerify = true
                        }
                    }
                    // da kich hoat soft otp
                    SdkNative.getInstance().setSelectedUserId(userId)
                    val transactionInfo = String(
                        SdkNative.getInstance().getTransactionInfo(transactionId, messageId),
                    )
                    Utils.g().provideGson().fromJson(transactionInfo, SoftEntity::class.java)?.let {
                        if (VSoftConstants.OTPResultCode.SUCCESS.code == it.responseCode) {
                            transactionData = it.transactionData
                            challenge = it.challenge
                            println("OTP gen: ${it.userID} - ${it.transactionData} - ${it.challenge}")
                            val genOTPWithTransactionInfo =
                                SdkNative.getInstance().getCRotpWithTransactionInfo(
                                    it.transactionData,
                                    it.challenge,
                                )
                            val otpCode = String(genOTPWithTransactionInfo)
                            val timeStep = SdkNative.getInstance().timeStep
                            softResultListener?.onSuccess(otpCode, timeStep)
                        } else {
                            softResultListener?.onError(
                                VSoftConstants.getErrorMessage(it.responseCode),
                                it.responseCode,
                            )
                        }
                    }
                } catch (e: Exception) {
                    println("OTP Gen Exception: ${e.message}")
                }
            }
        }
    }

    override fun genSoftOtpV2() {
        CoroutineScope(Dispatchers.Default).launch {
            try {
                if (transactionData.isNullOrEmpty() && challenge.isNullOrEmpty()) {
                    println("OTP - fun genOTPV2: TransactionData && challenge is null or empty")
                    softResultListener?.onError("TransactionData && challenge is null or empty", -1)
                } else {
                    println("OTP: $transactionData - $challenge")
                    val genOTPWithTransactionInfo = SdkNative.getInstance()
                        .getCRotpWithTransactionInfo(transactionData, challenge)
                    val otpCode = String(genOTPWithTransactionInfo)
                    val timeStep = SdkNative.getInstance().timeStep
                    softResultListener?.onSuccess(otpCode, timeStep)
                }
            } catch (e: Exception) {
                println("OTP - fun genOTPV2: $e")
            }
        }
    }

    override fun setSoftResultListener(callback: ISoftResult) {
        this.softResultListener = callback
    }

    override fun setPinResultListener(callback: IPinResult) {
        this.pinListener = callback
    }

    /**
     * At PIN Screen
     * Input Pin => call verifyPin(pin)
     * setPinResultListener listener handle verify pin
     * Success => navigate to OTP Screen call genSoftOtp
     * Error => show message or ...
     * */
    override fun verifyPin(pin: String?) {
        // kiem tra pin
        CoroutineScope(Dispatchers.Default).launch {
            try {
                val result = SdkNative.getInstance().loginPin(pin)
                val resultCode = String(result).toInt()
                if (VSoftConstants.OTPResultCode.SUCCESS.code == resultCode) {
                    wrongPinCounter = 0
                    pinListener?.onSuccess()
                } else if (VSoftConstants.OTPResultCode.WRONG_PIN_CODE.code == resultCode) {
                    // Sai Pin
                    wrongPinCounter++
                    val messageError = when (wrongPinCounter) {
                        VSoftConstants.WRONG_PIN_CODE_MAX -> {
                            appConfig.updateLockSoft(lockSoftToTime())
                            resourceProvider.getString(
                                R.string.soft_pin_wrong_5_times,
                                lockSoftToTime(),
                            )
                        }

                        else -> {
                            resourceProvider.getString(
                                R.string.soft_pin_wrong_times,
                                VSoftConstants.WRONG_PIN_CODE_MAX - wrongPinCounter,
                            )
                        }
                    }
                    pinListener?.onError(
                        messageError,
                        resultCode,
                    )
                } else {
                    pinListener?.onError(
                        VSoftConstants.getErrorMessage(resultCode),
                        resultCode,
                    )
                }
            } catch (e: Exception) {
                println("PIN - fun verifyPin: $e")
            }
        }
    }

    override fun getStatusLockSoft(): String? {
        if (appConfig.getLockSoftTime().isNotEmpty() && appConfig.getLockSoftTime()
                .toTimeInMillis() > Calendar.getInstance().timeInMillis
        ) {
            return resourceProvider.getString(R.string.soft_pin_wrong_5_times, lockSoftToTime())
        }
        appConfig.updateLockSoft("")
        return null
    }

    override fun isAllowSoft(): Boolean =
        Tags.ACCEPTABLE_REGISTER_SOTP == userProf.getKeypassSoftotp()
}