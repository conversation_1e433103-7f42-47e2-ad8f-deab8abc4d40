package com.vietinbank.feature_soft.common

import android.os.Build
import android.os.Bundle
import android.os.Parcelable
import androidx.fragment.app.Fragment
import androidx.lifecycle.SavedStateHandle
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.components.foundation.pin.PinAuthConstants
import com.vietinbank.core_ui.components.foundation.pin.PinAuthDialog
import com.vietinbank.core_ui.components.foundation.pin.PinAuthResult
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import com.vietinbank.feature_soft.common.constant.VSoftConstants.WRONG_PIN_CODE_MAX
import com.vietinbank.feature_soft.views.newui.dialog.OnSoftOTPAction
import com.vietinbank.feature_soft.views.newui.dialog.SoftOTPAuthEvent
import com.vietinbank.feature_soft.views.newui.dialog.SoftOTPNoticeDialog
import com.vietinbank.feature_soft.views.newui.fragment.active.ActiveSoftOTPFlow
import com.vietinbank.feature_soft.views.newui.fragment.confirm_otp.ConfirmOTPFlow

fun Fragment.listenSoftOtpDialogResult(
    onPositiveAction: () -> Unit,
    onNegativeAction: (() -> Unit)? = null,
) {
    parentFragmentManager.setFragmentResultListener(
        SoftOTPNoticeDialog.OPT_DIALOG_RESULT_KEY,
        viewLifecycleOwner,
    ) { _, bundle ->
        val dialogAction = bundle.safeParcelable("key_result") as? OnSoftOTPAction
        if (dialogAction?.isPositiveClick == true) {
            onPositiveAction.invoke()
        } else {
            onNegativeAction?.invoke()
        }
    }
}

inline fun <reified T : Parcelable> Bundle.safeParcelable(key: String): T? {
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        getParcelable(key, T::class.java)
    } else {
        @Suppress("DEPRECATION")
        getParcelable(key) as? T
    }
}

fun SavedStateHandle.getOTPActiveFlow(
    key: String = VSoftConstants.Bundle.KEY_FLOW_OTP,
): ActiveSoftOTPFlow {
    val flowName: String? = this[key]
    return flowName?.let { ActiveSoftOTPFlow.valueOf(it) }
        ?: ActiveSoftOTPFlow.NEW
}

fun SavedStateHandle.getOTPConfirmFlow(
    key: String = VSoftConstants.Bundle.KEY_FLOW_OTP,
): ConfirmOTPFlow {
    val flowName: String? = this[key]
    return flowName?.let { ConfirmOTPFlow.valueOf(it) }
        ?: ConfirmOTPFlow.VERIFY_ACTIVE_FIRST
}

fun <VM : BaseViewModel> BaseFragment<VM>.simpleHandleSoftOTPEvent(
    event: SoftOTPAuthEvent,
    onGetTransactionIdSuccess: ((String) -> Unit)? = null,
) {
    when (event) {
        is SoftOTPAuthEvent.OnCancelFlow -> {
            dismissAllDialogs()
        }

        is SoftOTPAuthEvent.OnGenOTPTransCodeSuccess -> {
            onGetTransactionIdSuccess?.invoke(event.transactionId)
            PinAuthDialog.show(
                fragmentManager = parentFragmentManager,
                transactionId = event.transactionId,
            )
        }

        is SoftOTPAuthEvent.OnGetChallengeCodeSuccess -> {
            PinAuthDialog.updateWithOtp(
                event.challengeCode,
                event.expiredTime,
            )
        }

        is SoftOTPAuthEvent.OnVerifyPinFail -> {
            PinAuthDialog.updateWithPinError(
                getString(
                    R.string.soft_pin_wrong_times,
                    WRONG_PIN_CODE_MAX - event.failCount,
                ),
            )
        }

        SoftOTPAuthEvent.OnVerifyPinLocked -> {
            dismissAllDialogs()
            appNavigator.goToLockSoftOTPDialog()
        }
    }
}

fun <VM : BaseViewModel> BaseFragment<VM>.setupSoftOTPDialogListener(
    onVerifyPin: (String) -> Unit,
    onApprove: (String) -> Unit,
) {
    parentFragmentManager.setFragmentResultListener(
        PinAuthConstants.RESULT_KEY,
        viewLifecycleOwner,
    ) { _, bundle ->
        val result = bundle.safeParcelable<PinAuthResult>("key_result")
        result?.let {
            val pinCode = it.pinCode
            val otpCode = it.otpCode
            val isForgot = it.isForgot
            when {
                isForgot -> {
                    appNavigator.gotToForgotSoftOTP()
                }

                !pinCode.isNullOrEmpty() -> {
                    onVerifyPin(pinCode)
                }

                !otpCode.isNullOrEmpty() -> {
                    dismissAllDialogs()
                    onApprove(otpCode)
                }

                else -> {
                    // suppress
                }
            }
        }
    }
}
