package com.vietinbank.core_common.nav

import android.os.Bundle

/**
 * Created by vand<PERSON> on 27/2/25.
 */
interface ISoftNavigator {
    fun popBackStack() // optional
    fun setFragmentResult(requestKey: String, result: Bundle)

    // soft otp
    fun goToVerifyPIN(vararg pair: Pair<String, Any?>) // nhap ma xac thuc gui ve email/phone
    fun goToEnterPIN(vararg pair: Pair<String, Any?>) // nhap pin
    fun goToSetPIN(vararg pair: Pair<String, Any?>) // thiet lap pin
    fun goToActiveSoft(vararg pair: Pair<String, Any?>) // active soft
    fun goToListUser(vararg pair: Pair<String, Any?>) // danh sach user da kich hoat
    fun goToGenOTP(vararg pair: Pair<String, Any?>) // gen otp
    fun popToActiveSoft() // quay ve man active soft
    fun goToVerifyOffline(vararg pair: Pair<String, Any?>) // xac thuc giao dich offline
    fun goToScanInfo(vararg pair: Pair<String, Any?>) // xac thuc giao dich offline scan qr
    fun goToInputInfo(vararg pair: Pair<String, Any?>) // xac thuc giao dich offline input
    fun goToVerifyOnline(vararg pair: Pair<String, Any?>) // xac thuc giao dich online
    fun goToConfirmOnline(vararg pair: Pair<String, Any?>) // xac thuc giao dich online
    fun goToHomeSoft(vararg pair: Pair<String, Any?>) // danh sach user da kich hoat
    fun goToSoftInEfast(vararg pair: Pair<String, Any?>) // xac thuc giao dich trong efast
    fun popBackGraph()

    // keypass
    fun goToKeypassMain() // cai dat keypass
    fun goToKeypassForgotPin() // quen pin keypass
    fun goToKeypassInfo() // lay thong tin keypass
    fun popToKeypassMain() // quay ve man cai dat keypass
}