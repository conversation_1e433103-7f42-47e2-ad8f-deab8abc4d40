package com.vietinbank.feature_soft.common.interfaces

import android.util.TypedValue
import android.widget.TextView
import androidx.annotation.ColorInt
import com.vietinbank.core_common.utils.Utils

interface VSoftInfoViewListener {
    fun setVtbInfo(
        view: TextView?,
        title: String?,
        @ColorInt color: Int? = null,
        font: String? = null,
        textSize: Float? = null,
    ) {
        view?.apply {
            text = title ?: ""
            color?.let { setTextColor(it) }
            font?.let { typeface = Utils.g().getFont(it.toInt(), context) }
            textSize?.let { setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize) }
        }
    }
    fun setVtbFieldRequire(highlight: String?, @ColorInt color: Int = 0)
    fun getVtbValue(): String
    fun setVtbValue(content: String?, @ColorInt color: Int? = null, font: String? = null)
    fun setVtbError(content: String? = null)
}