package com.vietinbank.feature_soft.views.newui.fragment.install_otp

import android.os.Bundle
import android.view.View
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.fragment.app.viewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.core_ui.utils.eFastBackgroundLevel2
import com.vietinbank.feature_soft.R
import com.vietinbank.feature_soft.common.listenSoftOtpDialogResult
import com.vietinbank.feature_soft.navigation.INewSoftNavigator
import com.vietinbank.feature_soft.views.newui.dialog.SoftOTPDialogType
import com.vietinbank.feature_soft.views.newui.dialog.SoftOTPNoticeDialog
import com.vietinbank.feature_soft.views.newui.screen.InstallOTPScreen
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class InstallOTPFragment : BaseFragment<InstallOTPViewModel>() {
    override val viewModel: InstallOTPViewModel by viewModels()

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softNavigator: INewSoftNavigator

    override val useCompose: Boolean = true

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)
        listenSoftOtpDialogResult(
            onPositiveAction = {
                softNavigator.popOutOfFlow()
            },
        )
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.uiState.collectAsStateWithLifecycle()
        handleSingleEvent {
            when (it as InstallOTPEvent) {
                InstallOTPEvent.OnInstallOTPSuccess -> {
                    softNavigator.goToOTPNoticeDialog(
                        SoftOTPNoticeDialog.buildBundle(
                            message = resources.getString(R.string.install_otp_dialog_success),
                            type = SoftOTPDialogType.SUCCESS,
                        ),
                    )
                }
            }
        }
        AppTheme {
            InstallOTPScreen(
                modifier = Modifier
                    .fillMaxSize()
                    .eFastBackgroundLevel2()
                    .systemBarsPadding()
                    .imePadding(),
                uiState = uiState,
                onPinValueChange = viewModel::onInputPinChange,
                onRePinValueChange = viewModel::onReInputPinChange,
                onBack = {
                    softNavigator.popToNewActiveSoft()
                },
                onNext = viewModel::onInstallPin,
            )
        }
    }
}