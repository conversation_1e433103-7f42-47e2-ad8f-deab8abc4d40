package com.vietinbank.feature_soft.views.newui.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CheckboxDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.github.barteksc.pdfviewer.PDFView
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.core_ui.utils.commonRoundedCornerCard
import com.vietinbank.core_ui.utils.eFastBackgroundLevel2
import com.vietinbank.feature_soft.R
import com.vietinbank.feature_soft.views.newui.fragment.term.TermSoftOTPUiState
import java.io.File
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun TermSoftOTPScreen(
    modifier: Modifier = Modifier,
    uiState: TermSoftOTPUiState,
    onConfirmCheckBoxChange: (Boolean) -> Unit,
    onNext: () -> Unit,
    onBack: () -> Unit,
) {
    Box(
        modifier = modifier,
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = FDS.Sizer.Padding.padding8),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            FoundationAppBar(
                isLightIcon = false,
                title = stringResource(R.string.soft_opt_term_title),
                onNavigationClick = onBack,
                modifier = Modifier.padding(top = FDS.Sizer.Padding.padding8),
            )

            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .commonRoundedCornerCard(),
            ) {
                PDFViewWithScrollbar(
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth(),
                    file = uiState.termOfUseFile,
                )

                HorizontalDivider(
                    modifier = Modifier
                        .fillMaxWidth(),
                    thickness = FDS.Sizer.Stroke.stroke05,
                    color = FDS.Colors.strokeDivider,
                )

                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

                Row(
                    modifier = Modifier
                        .clickable(
                            interactionSource = null,
                            indication = null,
                            onClick = {
                                onConfirmCheckBoxChange.invoke(!uiState.confirmChecked)
                            },
                        )
                        .padding(horizontal = FDS.Sizer.Padding.padding12),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Checkbox(
                        checked = uiState.confirmChecked,
                        onCheckedChange = onConfirmCheckBoxChange,
                        colors = CheckboxDefaults.colors(
                            checkedColor = FDS.Colors.characterHighlightedLighter,
                        ),
                    )
                    FoundationText(
                        text = stringResource(R.string.soft_opt_term_confirm),
                        style = FDS.Typography.bodyB2,
                    )
                }
            }

            FoundationButton(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        vertical = FDS.Sizer.Gap.gap16,
                        horizontal = FDS.Sizer.Gap.gap24,
                    ),
                text = stringResource(com.vietinbank.core_ui.R.string.btn_continue),
                enabled = uiState.buttonEnabled,
                onClick = onNext,
            )
        }
    }
}

@Composable
private fun PDFViewWithScrollbar(
    modifier: Modifier = Modifier,
    file: File?,
    scrollbarHeight: Dp = 124.dp,
) {
    var layoutHeight by remember {
        mutableIntStateOf(0)
    }

    var proportion by remember {
        mutableFloatStateOf(0f)
    }

    Box(
        modifier = modifier
            .padding(
                end = FDS.Sizer.Padding.padding16,
            )
            .onGloballyPositioned { layoutCoordinates ->
                layoutHeight = layoutCoordinates.size.height
            },
    ) {
        AndroidView(
            factory = { ctx ->
                PDFView(
                    ctx,
                    null,
                )
            },
            update = { pdfView ->
                pdfView.fromFile(file)
                    .enableSwipe(true)
                    .onPageScroll { _, offset ->
                        proportion = offset
                    }
                    .swipeHorizontal(false)
                    .load()
            },
            onReset = { pdfView ->
                // Clean up resources
                pdfView.recycle()
                pdfView.stopFling()
            },
            modifier = Modifier
                .fillMaxSize()
                .clip(RectangleShape),
        )

        Box(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .width(4.dp)
                .height(scrollbarHeight)
                .offset(
                    y = with(LocalDensity.current) {
                        (proportion * (layoutHeight - scrollbarHeight.toPx())).toDp()
                    },
                )
                .background(FDS.Colors.strokeDivider, shape = RoundedCornerShape(2.dp)),
        )
    }
}

@Preview
@Composable
private fun PreviewTermSoftOTPScreen() {
    var isChecked by remember {
        mutableStateOf(false)
    }
    val uiState by remember {
        derivedStateOf {
            TermSoftOTPUiState(confirmChecked = isChecked)
        }
    }
    AppTheme {
        TermSoftOTPScreen(
            modifier = Modifier
                .fillMaxSize()
                .eFastBackgroundLevel2()
                .systemBarsPadding()
                .imePadding(),
            uiState = uiState,
            onConfirmCheckBoxChange = {
                isChecked = !isChecked
            },
            onNext = {},
            onBack = {},
        )
    }
}
