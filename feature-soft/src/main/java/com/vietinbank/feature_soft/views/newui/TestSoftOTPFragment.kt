package com.vietinbank.feature_soft.views.newui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_domain.softotp.ISoftManager
import com.vietinbank.feature_soft.R
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import com.vietinbank.feature_soft.common.listenSoftOtpDialogResult
import com.vietinbank.feature_soft.databinding.FragmentTestSoftOtpBinding
import com.vietinbank.feature_soft.navigation.INewSoftNavigator
import com.vietinbank.feature_soft.views.newui.dialog.SoftOTPDialogType
import com.vietinbank.feature_soft.views.newui.dialog.SoftOTPNoticeDialog
import com.vietinbank.feature_soft.views.newui.fragment.active.ActiveSoftOTPFlow
import com.vietinbank.feature_soft.views.newui.fragment.confirm_otp.ConfirmOTPFlow
import dagger.hilt.android.AndroidEntryPoint
import jakarta.inject.Inject

@AndroidEntryPoint
class TestSoftOTPFragment : Fragment() {
    private var _binding: FragmentTestSoftOtpBinding? = null
    private val binding
        get() = _binding!!

    @Inject
    lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softNavigator: INewSoftNavigator

    @Inject
    lateinit var softManager: ISoftManager

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        _binding = FragmentTestSoftOtpBinding.inflate(inflater, container, false)
        return binding.root
    }

    private var testSoftOTPFlow: TestSoftOTPFlow = TestSoftOTPFlow.SYNC

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)

        listenSoftOtpDialogResult(
            onPositiveAction = {
                when (testSoftOTPFlow) {
                    TestSoftOTPFlow.FORGOT ->
                        softNavigator.goToTermOfUse(
                            VSoftConstants.Bundle.KEY_FLOW_OTP
                                to ActiveSoftOTPFlow.FORGOT.name,
                        )
                    TestSoftOTPFlow.TOGGLE ->
                        softNavigator.goToConfirmOTP(
                            VSoftConstants.Bundle.KEY_FLOW_OTP
                                to ConfirmOTPFlow.TOGGLE.name,
                        )

                    else -> {
                        softNavigator.popOutOfFlow()
                    }
                }
            },
        )

        with(binding) {
            btnActiveSoftOTP.setOnClickListener {
                appNavigator.gotoActivSoft()
            }
            btnActiveSoftOTPSecond.setOnClickListener {
                checkSoftActive {
                    appNavigator.gotoActivSoft()
                }
            }
            btnSyncSoftOTP.setOnClickListener {
                testSoftOTPFlow = TestSoftOTPFlow.SYNC
                checkSoftActive {
                    softNavigator.goToConfirmOTP(
                        VSoftConstants.Bundle.KEY_FLOW_OTP
                            to ConfirmOTPFlow.SYNC.name,
                    )
                }
            }
            btnForgotSoftOTP.setOnClickListener {
                testSoftOTPFlow = TestSoftOTPFlow.FORGOT
                checkSoftActive {
                    softNavigator.goToOTPNoticeDialog(
                        SoftOTPNoticeDialog.buildBundle(
                            message = resources.getString(R.string.forgot_otp_dialog_message),
                            type = SoftOTPDialogType.WARNING,
                            isShowNegativeButton = true,
                        ),
                    )
                }
            }
            btnTurnOffSoftOTP.setOnClickListener {
                testSoftOTPFlow = TestSoftOTPFlow.TOGGLE
                checkSoftActive {
                    softNavigator.goToOTPNoticeDialog(
                        SoftOTPNoticeDialog.buildBundle(
                            message = resources.getString(R.string.toggle_otp_dialog_alert_message),
                            type = SoftOTPDialogType.WARNING,
                            isShowNegativeButton = true,
                        ),
                    )
                }
            }
        }
    }

    private fun checkSoftActive(action: () -> Unit) {
        if (!softManager.isAppActive) {
            MaterialAlertDialogBuilder(requireContext())
                .setMessage("Chưa kích hoạt Soft OTP")
                .setPositiveButton("OK") { dialog, _ ->
                    dialog.dismiss()
                }
                .show()
        } else {
            action.invoke()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}

enum class TestSoftOTPFlow {
    SYNC, FORGOT, TOGGLE
}