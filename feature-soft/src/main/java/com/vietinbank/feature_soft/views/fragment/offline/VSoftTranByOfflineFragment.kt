package com.vietinbank.feature_soft.views.fragment.offline

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.nav.ISoftNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_soft.R
import com.vietinbank.feature_soft.databinding.VFragmentSoftByOfflineBinding
import com.vietinbank.feature_soft.views.fragment.VSoftViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class VSoftTranByOfflineFragment : BaseFragment<VSoftViewModel>() {
    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softNavigator: ISoftNavigator
    override val viewModel: VSoftViewModel by viewModels()

    override fun inflateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
    ): VFragmentSoftByOfflineBinding {
        return VFragmentSoftByOfflineBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        (binding as VFragmentSoftByOfflineBinding).apply {
            headerView.setVtbOnBackPressed {
                onBackPressed()
            }
            rgMethod.setOnCheckedChangeListener { group, checkedId ->
                context?.let {
                    when (checkedId) {
                        R.id.rbScan -> {
                            rbScan.typeface = Utils.g().getFont(5, it)
                            rbInput.typeface = Utils.g().getFont(4, it)
                        }

                        else -> {
                            rbInput.typeface = Utils.g().getFont(5, it)
                            rbScan.typeface = Utils.g().getFont(4, it)
                        }
                    }
                }
            }

            btnNext.setThrottleClickListener {
                if (rbScan.isChecked) {
                    // scan qr
                    softNavigator.goToScanInfo()
                } else {
                    // input transfer
                    softNavigator.goToInputInfo()
                }
            }
        }
    }

    override fun onBackPressed(): Boolean {
        softNavigator.popBackStack()
        return true
    }
}