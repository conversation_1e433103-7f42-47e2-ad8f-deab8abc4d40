package com.vietinbank.feature_soft.views.fragment.online

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.nav.ISoftNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.soft.SoftEntity
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import com.vietinbank.feature_soft.databinding.VFragmentSoftTranVerifyBinding
import com.vietinbank.feature_soft.views.fragment.VSoftViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class VSoftTranVerifyFragment : BaseFragment<VSoftViewModel>() {
    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softNavigator: ISoftNavigator
    override val viewModel: VSoftViewModel by viewModels()

    private var transactionEntity: SoftEntity? = null

    override fun inflateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
    ): VFragmentSoftTranVerifyBinding {
        return VFragmentSoftTranVerifyBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        try {
            transactionEntity =
                Utils.g().provideGson().fromJson(
                    arguments?.getString(VSoftConstants.Bundle.KEY_DATA_1),
                    SoftEntity::class.java,
                )
        } catch (_: Exception) {
        }

        (binding as VFragmentSoftTranVerifyBinding).apply {
            headerView.setVtbOnBackPressed { onBackPressed() }
            infoRef.setVtbValue(transactionEntity?.challenge ?: "")
            infoSum.setVtbValue(transactionEntity?.transactionData ?: "")

            btnApprove.setThrottleClickListener {
                softNavigator.goToConfirmOnline(
                    VSoftConstants.Bundle.KEY_DATA_1 to Utils.g().provideGson()
                        .toJson(transactionEntity),
                )
            }

            btnCancel.setThrottleClickListener {
                onBackPressed()
            }
        }
    }

    override fun onBackPressed(): Boolean {
        softNavigator.popBackStack()
        return true
    }
}