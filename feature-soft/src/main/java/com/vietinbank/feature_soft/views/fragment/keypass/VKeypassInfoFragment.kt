package com.vietinbank.feature_soft.views.fragment.keypass

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.viewbinding.ViewBinding
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.nav.ISoftNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_soft.databinding.VFragmentKeypassInfoBinding
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class VKeypassInfoFragment : BaseFragment<VKeypassViewModel>() {
    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softNavigator: ISoftNavigator
    override val viewModel: VKeypassViewModel by viewModels()
    override fun inflateViewBinding(inflater: LayoutInflater, container: ViewGroup?): ViewBinding {
        return VFragmentKeypassInfoBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
    }

    private fun initView() {
        (binding as VFragmentKeypassInfoBinding).apply {
            headerView.setVtbOnBackPressed { onBackPressed() }
            infoToken.setVtbValue(viewModel.getKeypassToken())
            infoCode.setVtbValue(viewModel.getKeypassCode())
        }
    }

    override fun onBackPressed(): Boolean {
        softNavigator.popBackStack()
        return true
    }
}