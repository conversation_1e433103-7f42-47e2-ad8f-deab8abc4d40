package com.vietinbank.feature_soft.views.newui.fragment.confirm_otp

import androidx.compose.runtime.Stable
import androidx.lifecycle.SavedStateHandle
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.extensions.lockSoftToTime
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.CustomEncryptedPrefs
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.soft.SoftSDKErrorCode
import com.vietinbank.core_domain.softotp.ISoftManager
import com.vietinbank.core_domain.usecase.soft.SyncOTPUseCase
import com.vietinbank.core_domain.usecase.soft.ToggleSoftUseCase
import com.vietinbank.core_domain.usecase.soft.VerifyActivationCodeUseCase
import com.vietinbank.core_domain.usecase.soft.VerifyPinUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.OneTimeEvent
import com.vietinbank.core_ui.components.foundation.pin.PinState
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import com.vietinbank.feature_soft.common.constant.VSoftConstants.WRONG_PIN_CODE_MAX
import com.vietinbank.feature_soft.common.getOTPConfirmFlow
import com.vietinbank.feature_soft.common.mapper.SoftOTPResultCodeMapper
import com.vietinbank.feature_soft.views.newui.fragment.confirm_otp.ConfirmInstallOTPUIState.Companion.OTP_LENGTH
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.withContext
import javax.inject.Inject

@HiltViewModel
class ConfirmOTPViewModel @Inject constructor(
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val sessionManager: ISessionManager,
    override val ottSetupService: IOttSetupService,
    private val verifyActivationCodeUseCase: VerifyActivationCodeUseCase,
    private val softManager: ISoftManager,
    private val syncOTPUseCase: SyncOTPUseCase,
    private val toggleSoftUseCase: ToggleSoftUseCase,
    private val verifyPinUseCase: VerifyPinUseCase,
    private val sharePref: CustomEncryptedPrefs,
    private val otpResultCodeMapper: SoftOTPResultCodeMapper,
    savedStateHandle: SavedStateHandle,
) : BaseViewModel() {
    val confirmOTPFlow: ConfirmOTPFlow = savedStateHandle.getOTPConfirmFlow()
    private val _uiState = MutableStateFlow(ConfirmInstallOTPUIState())
    val uiState = _uiState.asStateFlow()

    init {
        observerLockStatus()
        _uiState.update {
            it.copy(
                errorCount = sharePref.getInt(VSoftConstants.KEY_OTP_FAIL_COUNT),
            )
        }
    }

    fun onInputChange(value: String) = launchJobSilent {
        _uiState.update {
            it.copy(
                pinValue = value,
                pinState = PinState.INPUT,
            )
        }
        if (_uiState.value.pinValue.length == OTP_LENGTH) {
            _uiState.update {
                it.copy(enablePinInput = false)
            }
            // Đợi keyboard animate
            delay(50)
            when (confirmOTPFlow) {
                ConfirmOTPFlow.VERIFY_ACTIVE_FIRST,
                ConfirmOTPFlow.VERIFY_ACTIVE_SECOND,
                -> verifyActivationCode()

                ConfirmOTPFlow.TOGGLE -> toggleOTP()
                ConfirmOTPFlow.SYNC -> syncOTP()
                ConfirmOTPFlow.VERIFY_OTP_SECOND_USER -> confirmActiveSecondUser()
            }
        }
    }

    // Validate response từ SDK khi nhập OTP
    private suspend fun <T> validatePinResponse(
        response: Resource<T>,
        onSuccess: suspend () -> Unit,
    ) = withContext(Dispatchers.Main) {
        when (response) {
            is Resource.Error -> {
                when (response.code) {
                    SoftSDKErrorCode.WRONG_ACTIVATION_5_TIMES.code.toString() -> {
                        _uiState.update {
                            it.copy(
                                pinState = PinState.ERROR,
                                errorCount = WRONG_PIN_CODE_MAX,
                            )
                        }
                        sharePref.setInt(
                            VSoftConstants.KEY_OTP_FAIL_COUNT,
                            WRONG_PIN_CODE_MAX,
                        )
                    }

                    SoftSDKErrorCode.WRONG_ACTIVATION_CODE.code.toString() -> {
                        val countingErrorTimes = _uiState.value.errorCount + 1
                        _uiState.update {
                            it.copy(
                                pinState = PinState.ERROR,
                                errorCount = countingErrorTimes,
                            )
                        }
                        sharePref.setInt(
                            VSoftConstants.KEY_OTP_FAIL_COUNT,
                            countingErrorTimes,
                        )
                    }

                    else -> {
                        sendEvent(
                            ConfirmInstallOTPEvent.OnUnKnownSDKError(
                                otpResultCodeMapper.toMessage(
                                    SoftSDKErrorCode.Companion.fromCode(
                                        response.code.toIntOrNull()
                                            ?: SoftSDKErrorCode.FAIL.code,
                                    ),
                                ),
                            ),
                        )
                    }
                }

                // Tránh spam keyboard khi chưa hết animation PinState.ERROR
                delay(100)
                _uiState.update {
                    it.copy(enablePinInput = true)
                }
            }

            is Resource.Success -> {
                sharePref.setInt(
                    VSoftConstants.KEY_OTP_FAIL_COUNT,
                    0,
                )
                onSuccess.invoke()
            }
        }
    }

    private fun verifyActivationCode() {
        // Luồng 2.1.1 Kích hoạt Vietinbank OTP của MK doc
        // Áp dụng cho cả user thứ nhất và thứ hai (Active User A và Active User B)
        launchJob {
            validatePinResponse(
                // Đã nhận OTP từ Email/SMS -> call đến doActive (step 8.1 doActive)
                verifyActivationCodeUseCase(_uiState.value.pinValue),
                onSuccess = {
                    /*
                    Active lại user khi verify success cho user thứ 2
                    (user thứ nhất chỉ active khi done luồng Install OTP)
                     */
                    if (confirmOTPFlow == ConfirmOTPFlow.VERIFY_ACTIVE_SECOND) {
                        appConfig.updateStatusSoft(true)
                        softManager.isAppActive = true
                        softManager.isUserActive = true
                    }
                    setPinInputSuccess()
                },
            )
        }
    }

    private fun syncOTP() {
        launchJob {
            validatePinResponse(
                verifyPinUseCase(_uiState.value.pinValue),
                onSuccess = {
                    handleResource(syncOTPUseCase()) {
                        setPinInputSuccess()
                    }
                },
            )
        }
    }

    private fun toggleOTP() {
        launchJob {
            validatePinResponse(
                verifyPinUseCase(_uiState.value.pinValue),
                onSuccess = {
                    handleResource(
                        toggleSoftUseCase(
                            userId = userProf.getKeypassProfile() ?: "",
                            pushToken = "",
                        ),
                    ) {
                        setPinInputSuccess()
                    }
                },
            )
        }
    }

    private fun confirmActiveSecondUser() {
        launchJob {
            validatePinResponse(
                verifyPinUseCase(_uiState.value.pinValue),
                onSuccess = {
                    setPinInputSuccess()
                },
            )
        }
    }

    // Cho phép enale nút tiếp tục đến step tiếp. Check step ở ConfirmOTPFragment -> onNext
    private fun setPinInputSuccess() {
        _uiState.update {
            it.copy(
                pinState = PinState.SUCCESS,
            )
        }
    }

    private fun observerLockStatus() = launchJobSilent {
        _uiState.collect { state ->
            if (state.errorCount >= WRONG_PIN_CODE_MAX) {
                _uiState.update {
                    it.copy(
                        pinState = PinState.ERROR,
                        enablePinInput = false,
                    )
                }
                sendEvent(ConfirmInstallOTPEvent.OnVerifyPinBlocked)
                appConfig.updateLockSoft(lockSoftToTime())
            }
        }
    }
}

@Stable
data class ConfirmInstallOTPUIState(
    val pinValue: String = "",
    val pinState: PinState = PinState.INPUT,
    val errorCount: Int = 0,
    val enablePinInput: Boolean = true,
) {
    companion object {
        const val OTP_LENGTH = 6
    }

    val nextButtonEnabled: Boolean
        get() = pinState == PinState.SUCCESS
}

internal sealed interface ConfirmInstallOTPEvent : OneTimeEvent {
    object OnVerifyPinBlocked : ConfirmInstallOTPEvent
    data class OnUnKnownSDKError(val message: String?) : ConfirmInstallOTPEvent
}
