package com.vietinbank.feature_soft.views.newui.fragment.active

import android.os.Bundle
import android.view.View
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.fragment.app.viewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.core_ui.utils.eFastBackgroundLevel2
import com.vietinbank.feature_soft.R
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import com.vietinbank.feature_soft.common.listenSoftOtpDialogResult
import com.vietinbank.feature_soft.navigation.INewSoftNavigator
import com.vietinbank.feature_soft.views.newui.dialog.SoftOTPDialogType
import com.vietinbank.feature_soft.views.newui.dialog.SoftOTPNoticeDialog
import com.vietinbank.feature_soft.views.newui.fragment.confirm_otp.ConfirmOTPFlow
import com.vietinbank.feature_soft.views.newui.screen.ActiveSoftOTPInfoScreen
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class ActiveSoftOTPFragment : BaseFragment<ActiveSoftOTPViewModel>() {
    override val viewModel: ActiveSoftOTPViewModel by viewModels()

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softNavigator: INewSoftNavigator

    override val useCompose: Boolean = true

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)
        listenSoftOtpDialogResult(
            onPositiveAction = {
                when (viewModel.activeFlow) {
                    ActiveSoftOTPFlow.FORGOT_KEYPASS -> {
                        softNavigator.popOutOfFlow()
                    }
                    else -> {
                        // Just dismiss
                    }
                }
            },
        )
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.uiState.collectAsStateWithLifecycle()
        handleSingleEvent {
            when (it as ActiveSoftOTPEvent) {
                is ActiveSoftOTPEvent.OnGetActivationCodeDone -> {
                    when (viewModel.activeFlow) {
                        ActiveSoftOTPFlow.NEW,
                        ActiveSoftOTPFlow.FORGOT,
                        -> {
                            softNavigator.goToConfirmOTP(
                                VSoftConstants.Bundle.KEY_FLOW_OTP
                                    to ConfirmOTPFlow.VERIFY_ACTIVE_FIRST.name,
                                VSoftConstants.Bundle.KEY_DATA_1 to it,
                            )
                        }

                        ActiveSoftOTPFlow.SECOND -> {
                            softNavigator.goToConfirmOTP(
                                VSoftConstants.Bundle.KEY_FLOW_OTP
                                    to ConfirmOTPFlow.VERIFY_ACTIVE_SECOND.name,
                                VSoftConstants.Bundle.KEY_DATA_1 to it,
                            )
                        }

                        ActiveSoftOTPFlow.FORGOT_KEYPASS -> {
                            // Xử lý ở OnRequestKeypassPinDone
                        }
                    }
                }

                is ActiveSoftOTPEvent.OnRequestKeypassPinDone -> {
                    softNavigator.goToOTPNoticeDialog(
                        SoftOTPNoticeDialog.buildBundle(
                            message = resources.getString(R.string.keypass_forgot_request_pin_success),
                            type = SoftOTPDialogType.SUCCESS,
                        ),
                    )
                }

                ActiveSoftOTPEvent.OnGetActivationCodeFail -> {
                    softNavigator.goToOTPNoticeDialog(
                        SoftOTPNoticeDialog.buildBundle(
                            message = resources.getString(R.string.active_opt_fail_message),
                            type = SoftOTPDialogType.ERROR,
                        ),
                    )
                }
            }
        }

        AppTheme {
            ActiveSoftOTPInfoScreen(
                modifier = Modifier
                    .fillMaxSize()
                    .eFastBackgroundLevel2()
                    .systemBarsPadding()
                    .imePadding(),
                uiState = uiState,
                isKeypassActiveForgot = viewModel.activeFlow == ActiveSoftOTPFlow.FORGOT_KEYPASS,
                onMethodPicked = viewModel::onMethodPicked,
                onNext = {
                    when (viewModel.activeFlow) {
                        ActiveSoftOTPFlow.NEW, ActiveSoftOTPFlow.FORGOT -> {
                            viewModel.getActivationCode()
                        }

                        ActiveSoftOTPFlow.SECOND -> {
                            viewModel.getActivationCodeForSecondInstall()
                        }

                        ActiveSoftOTPFlow.FORGOT_KEYPASS -> {
                            viewModel.forgotKeypass()
                        }
                    }
                },
                onKeypassValueChange = viewModel::onKeypassValueChange,
                onBack = {
                    softNavigator.popBackStack()
                },
            )
        }
    }
}

enum class ActiveSoftOTPFlow {
    NEW, // Actice cho user thứ nhất
    SECOND, // Actice cho user thứ hai
    FORGOT, // Quên Soft OTP
    FORGOT_KEYPASS, // Quên keypass
}