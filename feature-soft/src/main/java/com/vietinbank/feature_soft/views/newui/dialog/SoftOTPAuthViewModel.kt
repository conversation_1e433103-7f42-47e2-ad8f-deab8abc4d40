package com.vietinbank.feature_soft.views.newui.dialog

import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.extensions.lockSoftToTime
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.CustomEncryptedPrefs
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.checker.GenSOTPTransCodeParams
import com.vietinbank.core_domain.models.soft.SoftSDKErrorCode
import com.vietinbank.core_domain.softotp.ISoftManager
import com.vietinbank.core_domain.usecase.checker.CheckerUserCase
import com.vietinbank.core_domain.usecase.soft.CheckOTPActiveSessionUseCase
import com.vietinbank.core_domain.usecase.soft.CheckUserExistsUseCase
import com.vietinbank.core_domain.usecase.soft.GetChallengeOTPExpiredTime
import com.vietinbank.core_domain.usecase.soft.GetChallengeOTPUseCase
import com.vietinbank.core_domain.usecase.soft.SetCurrentUserUseCase
import com.vietinbank.core_domain.usecase.soft.SyncOTPUseCase
import com.vietinbank.core_domain.usecase.soft.VerifyPinUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.OneTimeEvent
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import com.vietinbank.feature_soft.common.constant.VSoftConstants.WRONG_PIN_CODE_MAX
import com.vietinbank.feature_soft.common.mapper.SoftOTPResultCodeMapper
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class SoftOTPAuthViewModel @Inject constructor(
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val sessionManager: ISessionManager,
    override val ottSetupService: IOttSetupService,
    private val softManager: ISoftManager,
    private val checkerUserCase: CheckerUserCase,
    private val sharePref: CustomEncryptedPrefs,
    private val otpResultCodeMapper: SoftOTPResultCodeMapper,
    private val verifyPinUseCase: VerifyPinUseCase,
    private val checkOTPActiveSessionUseCase: CheckOTPActiveSessionUseCase,
    private val syncOTPUseCase: SyncOTPUseCase,
    private val checkUserExistsUseCase: CheckUserExistsUseCase,
    private val setCurrentUserUseCase: SetCurrentUserUseCase,
    private val getChallengeOTPUseCase: GetChallengeOTPUseCase,
    private val getChallengeOTPExpiredTime: GetChallengeOTPExpiredTime,
) : BaseViewModel() {

    companion object {
        private const val GEN_OPT_ACTION_ID = "approve"
        private const val GEN_OPT_TRANS_DATA = "1"
        private const val GEN_OPT_TRANS_REASON = ""
    }

    private val userId
        get() = userProf.getKeypassProfile() ?: run {
            handleDisplayMessage("User id is empty")
            ""
        }

    private var authenticatingTransactionId = ""

    private var failCount = sharePref.getInt(VSoftConstants.KEY_OTP_FAIL_COUNT)

    fun gentOTPTransCode(
        groupType: String?,
        mtIds: String,
        tranType: String,
    ) = launchJob {
        // Lấy transactionId trước khi mở dialog nhập pin
        val response = checkerUserCase.genSOTPTransCode(
            GenSOTPTransCodeParams(
                cifno = userProf.getCifNo(),
                userName = userProf.getUserName().toString(),
                actionId = GEN_OPT_ACTION_ID,
                groupType = groupType,
                mtIds = mtIds,
                reason = GEN_OPT_TRANS_REASON,
                transactionData = GEN_OPT_TRANS_DATA,
                trxType = tranType,
            ),
        )
        handleResource(response) { data ->
            data.transactionId?.let {
                sendEvent(SoftOTPAuthEvent.OnGenOTPTransCodeSuccess(it))
                authenticatingTransactionId = it
            } ?: run {
                // TODO: Refactor this message
                handleDisplayMessage("Transaction Id is empty")
            }
        }
    }

    /**
     * 4.1+4.2+4.3:
     * Xác thực bằng PIN, gọi API loginPIN trong SDK để Keypass SDK xác thực
     * người dùng.
     */
    fun verifyPin(pin: String) = launchJob {
        validatePinResponse(
            verifyPinUseCase(pin),
        ) {
            if (!softManager.isSkipVerify) {
                checkActiveToken()
            }
            setCurrentUserId()
            getChallengeCode()
        }
    }

    /**
     * 5.1 -> 5.5: Từ SDK gọi API checkActiveToken trong SDK để Keypass server kiểm tra hiệu lực
     * của token
     */
    private suspend fun checkActiveToken() {
        handleResource(
            resource = checkOTPActiveSessionUseCase(),
            handleError = {
                return@handleResource
            },
            onSuccess = {
                syncSoftOTP()
                checkIfUserExists()
            },
        )
    }

    /**
     * 6.1 -> 6.5: Từ SDK gọi API doSyncTime trong SDK để đồng bộ thời gian với Keypass server
     */
    private suspend fun syncSoftOTP() {
        handleResource(syncOTPUseCase()) {}
    }

    /**
     * 7.1: Mobile App gọi API checkUserIdExistence
     * Kiểm tra user đã kích hoạt soft otp trong app chưa
     */
    private suspend fun checkIfUserExists() {
        handleResource(checkUserExistsUseCase(userId)) {
            if (it) {
                // Skip bước syncSoftOTP và checkActiveToken trong session từ lần sau
                softManager.isSkipVerify = true
            }
        }
    }

    /**
     * Set User default hiện tại vào SDK
     */
    private suspend fun setCurrentUserId() {
        setCurrentUserUseCase(userId)
    }

    /**
     * 13.1: Sau khi xác thực người dùng thành công, Mobile App gọi API getTransactionInfo trong
     * SDK.
     * Sau đó lấy data nhận được call đến getCRotpWithTransactionInfo để lấy challenge code
     */
    private suspend fun getChallengeCode() {
        handleResource(
            getChallengeOTPUseCase(authenticatingTransactionId),
        ) { challengeCode ->
            sendEvent(
                SoftOTPAuthEvent.OnGetChallengeCodeSuccess(
                    challengeCode,
                    getChallengeCodeExpiredTime(),
                ),
            )
        }
    }

    private suspend fun getChallengeCodeExpiredTime(): Int {
        var expiredTime = 0
        handleResource(getChallengeOTPExpiredTime()) {
            expiredTime = it
        }
        return expiredTime
    }

    private fun handleDisplayMessage(message: String) {
        apiErrorMessage.postValue(message)
        sendEvent(SoftOTPAuthEvent.OnCancelFlow)
    }

    private suspend fun <T> validatePinResponse(
        response: Resource<T>,
        onSuccess: suspend () -> Unit,
    ) {
        when (response) {
            is Resource.Error -> {
                when (response.code) {
                    SoftSDKErrorCode.WRONG_PIN.code.toString() -> {
                        failCount++
                        sharePref.setInt(
                            VSoftConstants.KEY_OTP_FAIL_COUNT,
                            failCount,
                        )
                        if (failCount >= WRONG_PIN_CODE_MAX) {
                            appConfig.updateLockSoft(lockSoftToTime())
                            sendEvent(SoftOTPAuthEvent.OnVerifyPinLocked)
                            return
                        }
                        sendEvent(SoftOTPAuthEvent.OnVerifyPinFail(failCount))
                    }

                    else -> {
                        handleDisplayMessage(
                            otpResultCodeMapper.toMessage(
                                SoftSDKErrorCode.Companion.fromCode(
                                    response.code.toIntOrNull()
                                        ?: SoftSDKErrorCode.FAIL.code,
                                ),
                            ),
                        )
                    }
                }
            }

            is Resource.Success -> {
                sharePref.setInt(VSoftConstants.KEY_OTP_FAIL_COUNT, 0)
                onSuccess.invoke()
            }
        }
    }
}

sealed interface SoftOTPAuthEvent : OneTimeEvent {
    data class OnVerifyPinFail(val failCount: Int) : SoftOTPAuthEvent
    object OnVerifyPinLocked : SoftOTPAuthEvent
    data class OnGetChallengeCodeSuccess(
        val challengeCode: String,
        val expiredTime: Int,
    ) : SoftOTPAuthEvent

    object OnCancelFlow : SoftOTPAuthEvent

    data class OnGenOTPTransCodeSuccess(val transactionId: String) : SoftOTPAuthEvent
}