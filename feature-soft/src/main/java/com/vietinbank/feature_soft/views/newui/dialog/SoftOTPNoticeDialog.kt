package com.vietinbank.feature_soft.views.newui.dialog

import android.os.Bundle
import android.os.Parcelable
import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.viewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.vietinbank.core_ui.base.dialog.BaseDialog
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.dialog.DialogLayout
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.core_ui.utils.commonRoundedCornerCard
import com.vietinbank.feature_soft.R
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.parcelize.Parcelize
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Parcelize
data class OnSoftOTPAction(
    val isPositiveClick: Boolean,
) : Parcelable

@AndroidEntryPoint
class SoftOTPNoticeDialog : BaseDialog<OnSoftOTPAction>() {

    private val viewModel: SoftNoticeDialogViewModel by viewModels()

    companion object {
        const val OPT_DIALOG_RESULT_KEY = "OPT_DIALOG_RESULT_KEY"
        const val ARG_TITLE = "arg_title"
        const val ARG_MESSAGE = "arg_message"
        const val ARG_ICON = "arg_icon"
        const val ARG_TYPE = "arg_type"
        const val ARG_SHOW_NEGATIVE_BUTTON = "arg_show_button"
        const val ARG_POSITIVE_BUTTON_TEXT = "arg_positive_button_text"
        fun buildBundle(
            title: String? = null,
            message: String? = null,
            @DrawableRes icon: Int? = null,
            type: SoftOTPDialogType = SoftOTPDialogType.LOCK,
            isShowNegativeButton: Boolean = false,
            positiveButtonText: String = "",
        ): Bundle {
            val dialogIcon = icon ?: getIconByDialogType(type)

            return Bundle().apply {
                putString(ARG_TITLE, title)
                putString(ARG_MESSAGE, message)
                putInt(ARG_ICON, dialogIcon)
                putString(ARG_TYPE, type.name)
                putBoolean(ARG_SHOW_NEGATIVE_BUTTON, isShowNegativeButton)
                putString(ARG_POSITIVE_BUTTON_TEXT, positiveButtonText)
            }
        }

        private fun getIconByDialogType(type: SoftOTPDialogType): Int {
            return when (type) {
                SoftOTPDialogType.ERROR, SoftOTPDialogType.LOCK -> R.drawable.ic_dialog_otp_error
                SoftOTPDialogType.SUCCESS -> R.drawable.ic_dialog_otp_success
                SoftOTPDialogType.WARNING -> R.drawable.ic_dialog_otp_warning
            }
        }
    }

    override val resultKey: String = OPT_DIALOG_RESULT_KEY

    override val layout: DialogLayout = DialogLayout.BottomSheet

    @Composable
    override fun DialogContent(
        visible: Boolean,
        onDismissRequest: () -> Unit,
        onResult: (OnSoftOTPAction) -> Unit,
    ) {
        val title = arguments?.getString(ARG_TITLE)
            ?: stringResource(com.vietinbank.core_ui.R.string.dialog_title_default)

        val dialogType = arguments?.getString(ARG_TYPE)
            ?.let { value -> enumValues<SoftOTPDialogType>().find { it.name == value } }
            ?: SoftOTPDialogType.LOCK

        val icon = arguments?.getInt(ARG_ICON)
            ?.takeIf { it != 0 }
            ?: getIconByDialogType(dialogType)

        val message = if (dialogType != SoftOTPDialogType.LOCK) {
            arguments?.getString(ARG_MESSAGE) ?: ""
        } else {
            val timeLeft by viewModel.timeLeft.collectAsStateWithLifecycle()
            viewModel.startCountdown()
            remember(timeLeft) {
                mutableStateOf(
                    resources.getString(
                        R.string.lock_soft_otp_dialog_message,
                        timeLeft.first,
                        timeLeft.second,
                    ),
                )
            }.value
        }

        val isShowPositiveButton = arguments?.getBoolean(ARG_SHOW_NEGATIVE_BUTTON)
            ?: false

        val positiveButtonText = arguments?.getString(ARG_POSITIVE_BUTTON_TEXT)
            ?: ""

        OTPDialogContent(
            iconRes = icon,
            title = title,
            message = message,
            isShowNegativeButton = isShowPositiveButton,
            positiveButtonText = positiveButtonText,
            onPositive = {
                onResult.invoke(
                    OnSoftOTPAction(
                        isPositiveClick = true,
                    ),
                )
            },
            onNegative = {
                onResult.invoke(
                    OnSoftOTPAction(
                        isPositiveClick = false,
                    ),
                )
            },
        )
    }
}

@Composable
private fun OTPDialogContent(
    modifier: Modifier = Modifier,
    iconRes: Int,
    title: String,
    message: String,
    isShowNegativeButton: Boolean,
    positiveButtonText: String,
    onPositive: () -> Unit,
    onNegative: () -> Unit,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = FDS.Sizer.Padding.padding8),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Column(
            modifier = Modifier
                .commonRoundedCornerCard()
                .fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            FoundationText(
                text = title,
                style = FDS.Typography.headingH3,
                color = FDS.Colors.characterHighlighted,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
            )

            Spacer(
                modifier = Modifier.height(
                    FDS.Sizer.Gap.gap16,
                ),
            )

            HorizontalDivider(
                color = FDS.Colors.strokeDivider,
                thickness = FDS.Sizer.Stroke.stroke05,
            )

            Spacer(
                modifier = Modifier.height(
                    FDS.Sizer.Gap.gap24,
                ),
            )

            Image(
                painter = painterResource(id = iconRes),
                contentDescription = null,
                modifier = Modifier.size(FDS.Sizer.Icon.icon96),
            )

            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

            FoundationText(
                text = message,
                style = FDS.Typography.bodyB2,
                color = FDS.Colors.characterPrimary,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
            )
        }

        Row(
            modifier = Modifier.padding(
                top = FDS.Sizer.Padding.padding16,
                start = FDS.Sizer.Padding.padding24,
                end = FDS.Sizer.Padding.padding24,
                bottom = FDS.Sizer.Gap.gap36,
            ),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center,
        ) {
            if (isShowNegativeButton) {
                FoundationButton(
                    modifier = Modifier.weight(1f),
                    text = stringResource(com.vietinbank.core_ui.R.string.common_back),
                    onClick = onNegative,
                    isLightButton = false,
                )
                Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap8))
            }

            FoundationButton(
                modifier = Modifier.weight(1f),
                text = positiveButtonText.ifEmpty {
                    stringResource(
                        if (isShowNegativeButton) {
                            com.vietinbank.core_ui.R.string.btn_continue
                        } else {
                            com.vietinbank.core_ui.R.string.common_close
                        },
                    )
                },
                onClick = onPositive,
            )
        }
    }
}

@Preview
@Composable
private fun PreviewDialogContent() {
    AppTheme {
        OTPDialogContent(
            modifier = Modifier.background(FDS.Colors.backgroundBgScreen),
            iconRes = R.drawable.ic_dialog_otp_success,
            title = "Title",
            message = "Message",
            isShowNegativeButton = false,
            positiveButtonText = "Button",
            onPositive = {},
            onNegative = {},
        )
    }
}

enum class SoftOTPDialogType {
    SUCCESS, ERROR, WARNING, LOCK
}