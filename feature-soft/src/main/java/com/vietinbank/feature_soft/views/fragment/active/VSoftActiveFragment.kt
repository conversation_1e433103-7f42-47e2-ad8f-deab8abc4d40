package com.vietinbank.feature_soft.views.fragment.active

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.setFragmentResultListener
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.extensions.maskEmail
import com.vietinbank.core_common.extensions.maskPhoneNumber
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.nav.ISoftNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_soft.R
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import com.vietinbank.feature_soft.databinding.VFragmentSoftActiveBinding
import com.vietinbank.feature_soft.views.fragment.VSoftViewModel
import dagger.hilt.android.AndroidEntryPoint
import vn.mk.token.sdk.SdkNative
import javax.inject.Inject

@AndroidEntryPoint
class VSoftActiveFragment : BaseFragment<VSoftViewModel>() {
    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softNavigator: ISoftNavigator
    override val viewModel: VSoftViewModel by viewModels()
    private var otpFlow = VSoftConstants.VtpOTPFlowType.DEFAULT

    override fun inflateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
    ): VFragmentSoftActiveBinding {
        return VFragmentSoftActiveBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initObserve()
    }

    fun initObserve() {
        viewModel.apply {
            activeCodeLiveData.observe(viewLifecycleOwner) {
                softNavigator.goToVerifyPIN(
                    VSoftConstants.Bundle.KEY_FLOW_OTP to otpFlow.name,
                    VSoftConstants.Bundle.KEY_DATA_1 to it.message,
                )
            }

            deleteAllTokensLD.observe(viewLifecycleOwner) {
                if (it) {
                    SdkNative.getInstance().setSelectedUserId("")
                    viewModel.updateStatusSoft(false)
                    softManager.isAppActive = false
                    softManager.isUserActive = false
                }
            }
        }
    }

    private fun initView() {
        try {
            otpFlow = arguments?.getString(VSoftConstants.Bundle.KEY_FLOW_OTP, "")?.let {
                VSoftConstants.VtpOTPFlowType.valueOf(it)
            } ?: VSoftConstants.VtpOTPFlowType.DEFAULT
            if (otpFlow == VSoftConstants.VtpOTPFlowType.FORGOT_PIN) {
                viewModel.deleteAllTokensSmartOTP()
            }
        } catch (e: Exception) {
        }

        (binding as VFragmentSoftActiveBinding).apply {
            infoName.setVtbValue(viewModel.getFullName())
            infoUser.setVtbValue(viewModel.getUserName())
            infoEmail.setVtbValue(viewModel.getEmail().maskEmail())
            infoPhone.setVtbValue(viewModel.getPhone().maskPhoneNumber())
            headerView.setVtbOnBackPressed { onBackPressed() }

            rgMethod.setOnCheckedChangeListener { _, checkedId ->
                context?.let { ct ->
                    when (checkedId) {
                        R.id.rbEmail -> {
                            rbEmail.typeface = Utils.g().getFont(5, ct)
                            rbPhone.typeface = Utils.g().getFont(4, ct)
                        }

                        else -> {
                            rbPhone.typeface = Utils.g().getFont(5, ct)
                            rbEmail.typeface = Utils.g().getFont(4, ct)
                        }
                    }
                }
            }

            // mac dinh chon email
            rbPhone.isChecked = true

            btnNext.setThrottleClickListener {
                viewModel.getActivationCode(if (rbEmail.isChecked) "2" else "2")
            }

            setFragmentResultListener(VSoftConstants.RESULT_REND_OTP) { _, _ ->
                try {
                    headerView.setVtbTitleHeader("Xác thực cài đặt")
                    tvResendDes.isVisible = true
                    tvInfo.isVisible = false
                    llInfo.isVisible = false
                } catch (_: Exception) {
                }
            }
        }
    }

    override fun onBackPressed(): Boolean {
        softNavigator.popBackGraph()
        return true
    }
}