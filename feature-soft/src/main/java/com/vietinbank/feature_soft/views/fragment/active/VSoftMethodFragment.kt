package com.vietinbank.feature_soft.views.fragment.active

import android.os.Bundle
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.extensions.appendText
import com.vietinbank.core_common.extensions.focusAndShowKeyboard
import com.vietinbank.core_common.extensions.hideKeyboard
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.nav.ISoftNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_soft.R
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import com.vietinbank.feature_soft.databinding.VFragmentSoftMethodBinding
import com.vietinbank.feature_soft.views.fragment.VSoftViewModel
import dagger.hilt.android.AndroidEntryPoint
import java.util.Locale
import javax.inject.Inject

@AndroidEntryPoint
class VSoftMethodFragment : BaseFragment<VSoftViewModel>() {
    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softNavigator: ISoftNavigator
    override val viewModel: VSoftViewModel by viewModels()
    private var otpFlow = VSoftConstants.VtpOTPFlowType.DEFAULT
    private var wrongSMSCounter = 0

    override fun inflateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
    ): VFragmentSoftMethodBinding {
        return VFragmentSoftMethodBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val otp = arguments?.getString(VSoftConstants.Bundle.KEY_DATA_1)
        showNoticeDialog("otp: $otp")
        initView()
        initObserve()
    }

    fun initView() {
        try {
            otpFlow = arguments?.getString(VSoftConstants.Bundle.KEY_FLOW_OTP, "")?.let {
                VSoftConstants.VtpOTPFlowType.valueOf(it)
            } ?: VSoftConstants.VtpOTPFlowType.DEFAULT
        } catch (e: Exception) {
        }

        startCoolDown()
        (binding as VFragmentSoftMethodBinding).apply {
            headerView.setVtbOnBackPressed {
                onBackPressed()
            }

            tvResendOTP.appendText(
                text = "Gửi lại mã kích hoạt",
                underline = true,
                color = context?.let { ContextCompat.getColor(it, com.vietinbank.core_ui.R.color.text_blue_09) },
            )

            inputOTP.focusAndShowKeyboard()

            inputOTP.onTextChange = {
                if (it.length == 6) {
                    inputOTP.hideKeyboard()
                }
            }

            onTimerTick = {
                tvTime.text = String.format(
                    Locale.getDefault(),
                    "Thời gian hiệu lực OTP còn %02d:%02d",
                    (it / 60000),
                    (it % 60000) / 1000,
                )
            }

            onTimerFinished = {
            }

            tvResendOTP.setThrottleClickListener {
                // quay lai man hinh xac thuc
                setFragmentResult(VSoftConstants.RESULT_REND_OTP, bundleOf())
                softNavigator.popBackStack()
            }

            btnNext.setThrottleClickListener {
                if (inputOTP.text.toString().isEmpty()) {
                    showNoticeDialog("Mã kích hoạt không được bỏ trống. Quý khách vui lòng kiểm tra lại")
                    return@setThrottleClickListener
                } else if (inputOTP.text.toString().length < 6) {
                    showNoticeDialog("Mã kích hoạt phải bao gồm 6 ký tự số. Quý khách vui lòng kiểm tra lại")
                    return@setThrottleClickListener
                }
                viewModel.sdkDoActive(inputOTP.text.toString())
            }
        }
    }

    fun initObserve() {
        viewModel.apply {
            sdkCheckStatusCodeLD.observe(viewLifecycleOwner) {
                when (it) {
                    VSoftConstants.OTPResultCode.SUCCESS.code -> {
                        countDownTimer?.cancel()
                        countDownTimer = null
                        // thành công -> cai dat pin
                        if (otpFlow == VSoftConstants.VtpOTPFlowType.ACTIVE) {
                            // kich hoat user t2
                            viewModel.updateStatusSoft(true)
                            viewModel.softManager.isAppActive = true
                            viewModel.softManager.isUserActive = true
                            showNoticeDialog("Quý khách đã kích hoạt xác thực giao dịch bằng Soft OTP thành công") {
                                softNavigator.popBackGraph()
                            }
                        } else {
                            softNavigator.goToSetPIN()
                        }
                    }

                    VSoftConstants.OTPResultCode.WRONG_5TIMES_ACTIVE_CODE.code -> {
                        // nhap sai qua 5 lan
                        showNoticeDialog("Ứng dụng đã bị khóa kích hoạt Soft OTP và sẽ tự động mở khóa sau 30 phút. Quý khách vui long thử lại sau.") {
                            countDownTimer?.cancel()
                            countDownTimer = null
                            softNavigator.popBackGraph()
                        }
                    }

                    else -> {
                        //  sai otp
                        wrongSMSCounter++
                        showNoticeDialog("Mã kích hoạt không đúng. Lưu ý: ứng dụng sẽ bị khóa nếu nhập mã sai 5 lần liên tiếp. Quý khách đã nhập sai $wrongSMSCounter lần.") {
                            (binding as VFragmentSoftMethodBinding).apply {
                                inputOTP.setText("")
                                inputOTP.focusAndShowKeyboard()
                            }
                        }
                    }
                }
            }
        }
    }

    // count time otp
    private var countDownTimer: CountDownTimer? = null
    var onTimerFinished: (() -> Unit)? = null
    var onTimerTick: ((Long) -> Unit)? = null

    private fun startCoolDown() {
        try {
            countDownTimer?.cancel()
            countDownTimer = null
            countDownTimer =
                object : CountDownTimer((VSoftConstants.COUNT_SMS_TIME * 1000).toLong(), 10) {
                    override fun onTick(timeLeft: Long) {
                        if (isAdded && isVisible) {
                            onTimerTick?.invoke(timeLeft)
                        }
                    }

                    override fun onFinish() {
                        onTimerFinished?.invoke()
                    }
                }.start()
        } catch (_: Exception) {
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        countDownTimer?.cancel()
        countDownTimer = null
    }

    override fun onBackPressed(): Boolean {
        softNavigator.popBackStack()
        return true
    }
}