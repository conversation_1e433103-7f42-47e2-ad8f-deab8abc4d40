package com.vietinbank.feature_soft.views.fragment.gensoft

import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.nav.ISoftNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_soft.R
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import com.vietinbank.feature_soft.databinding.VFragmentSoftAuthenBinding
import com.vietinbank.feature_soft.views.fragment.VSoftViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class VSoftTranByPushFragment : BaseFragment<VSoftViewModel>() {
    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softNavigator: ISoftNavigator
    override val viewModel: VSoftViewModel by viewModels()

    override fun inflateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
    ): VFragmentSoftAuthenBinding {
        return VFragmentSoftAuthenBinding.inflate(inflater, container, false)
    }

    fun initView() {
        (binding as VFragmentSoftAuthenBinding).apply {
            onTimerTick = {
                tvCountTime.text =
                    getString(
                        R.string.vtb_ra_authen_soft_time,
                        (it / 1000).toString(),
                    )
            }

            onTimerFinished = {
                viewModel.sdkGetCRotpWithTransactionInfo(
                    viewModel.sdkTransInfoModel?.transactionData,
                    viewModel.sdkTransInfoModel?.challenge,
                )
            }

            btnNext.setThrottleClickListener {
                // step 23 -> 32 xac nhan
                viewModel.sdkConfirmTransaction(
                    viewModel.sdkTransInfoModel?.userID,
                    viewModel.sdkTransInfoModel?.transactionID,
                    "",
                    true,
                )
            }

            btnCancel.setThrottleClickListener {
                // step 23 -> 32 tu choi
                viewModel.sdkConfirmTransaction(
                    viewModel.sdkTransInfoModel?.userID,
                    viewModel.sdkTransInfoModel?.transactionID,
                    "",
                    false,
                )
            }
        }

        // STEP 13.1-13.5
        viewModel.sdkDoSyncTime()
    }

    fun initObserve() {
        viewModel.apply {
//            1032124K24110216
            sdkCheckStatusCodeLD.observe(this@VSoftTranByPushFragment) {
                if (it == VSoftConstants.OTPResultCode.SUCCESS.code) {
                    // step 14 -> 17
                    // - Nếu click Notification, truyền TransId và MessId lấy từ response của Notification
                    // - Trong TH không nhận được Notification, click “Lấy mã OTP” thì truyền TransId = " và MessId = “”
                    sdkGetTransactionInfo("", "")
                } else {
                    // hien thị loi
                    showNoticeDialog(VSoftConstants.getErrorMessage(it)) {
                        backFragment()
                    }
                }
            }

            sdkTransactionInfoLD.observe(this@VSoftTranByPushFragment) {
                // step 20
//                binding.vtbInfo.setVtbValue("SDK Info: ${it.toJson()}")
                if (VSoftConstants.OTPResultCode.SUCCESS.code == it.responseCode) {
                    sdkCheckUserIdExistence(it.userID ?: "")
                } else {
                    showNoticeDialog(VSoftConstants.getErrorMessage(it.responseCode)) {
                        backFragment()
                    }
                }
            }

            sdkCheckStatusLD.observe(this@VSoftTranByPushFragment) {
                // step 20.1 -> 20.3
                if (it) {
                    // Trường hợp KH đã kích hoạt Soft OTP, chuyển tiếp đến bước 21
                    // hiển thị thông tin giao dich
//                    binding.vtbInfo.setVtbValue(sdkTransInfoModel.toJson())
                } else {
                    // Trường hợp KH chưa kích hoạt Soft OTP
                    // hiển thị thông báo lỗi và cho phép khách hàng kích hoạt Soft OTP và yêu cầu thực hiện lại giao dịch từ đầu.
//                    showNoticeDialogActiveSmartOTP()
                }
            }

            sdkConfirmTransactionLD.observe(this@VSoftTranByPushFragment) {
                if (it == VSoftConstants.OTPResultCode.SUCCESS.code) {
                    // thanh cong
                    // hien thi ket qua
                    showNoticeDialog("Thành công") {
                        backFragment()
                    }
                } else {
                    showNoticeDialog(VSoftConstants.getErrorMessage(it)) {
                        backFragment()
                    }
                }
            }
        }
    }

    fun backFragment() {
        activity?.finish()
    }

    // count time otp
    private var countDownTimer: CountDownTimer? = null
    var onTimerFinished: (() -> Unit)? = null
    var onTimerTick: ((Long) -> Unit)? = null

    private fun startCoolDown() {
        try {
            countDownTimer?.cancel()
            countDownTimer = null
            countDownTimer =
                object : CountDownTimer((VSoftConstants.COUNT_OTP_TIME * 1000).toLong(), 10) {
                    override fun onTick(timeLeft: Long) {
                        if (isAdded && isVisible) {
                            onTimerTick?.invoke(timeLeft)
                        }
                    }

                    override fun onFinish() {
                        onTimerFinished?.invoke()
                    }
                }.start()
        } catch (_: Exception) {
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        countDownTimer?.cancel()
        countDownTimer = null
    }
}