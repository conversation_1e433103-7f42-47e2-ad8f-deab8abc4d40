package com.vietinbank.feature_soft.views.newui.component

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.foundation.pin.OTPPinInput
import com.vietinbank.core_ui.components.foundation.pin.PinState
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.core_ui.utils.commonRoundedCornerCard
import com.vietinbank.feature_soft.R
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun OTPInputDialogComponent(
    modifier: Modifier = Modifier,
    title: String,
    pinState: PinState,
    pinValue: String,
    failCount: Int,
    description: String? = null,
    enabled: Boolean = true,
    nextButtonEnabled: Boolean = false,
    showNumber: Boolean = true,
    focusRequester: FocusRequester? = null,
    onPinChange: (String) -> Unit,
    onNext: () -> Unit,
    onBack: () -> Unit,
    ctaDescription: @Composable () -> Unit,
) {
    Column(modifier = modifier.padding(FDS.Sizer.Padding.padding8)) {
        Column(
            modifier = Modifier.commonRoundedCornerCard(),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            FoundationText(
                text = title,
                style = FDS.Typography.headingH3,
                color = FDS.Colors.characterHighlighted,
            )
            HorizontalDivider(
                modifier = Modifier.padding(vertical = FDS.Sizer.Padding.padding16),
                color = FDS.Colors.strokeDivider,
                thickness = FDS.Sizer.Stroke.stroke05,
            )

            description?.let {
                FoundationText(
                    modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
                    text = it,
                    style = FDS.Typography.bodyB1,
                    textAlign = TextAlign.Center,
                    color = FDS.Colors.characterPrimary,
                )
                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
            }

            PinInputForm(
                pinState = pinState,
                pinValue = pinValue,
                failCount = failCount,
                enabled = enabled,
                showNumber = showNumber,
                onPinChange = onPinChange,
                focusRequester = focusRequester,
                ctaDescription = ctaDescription,
            )
        }

        Row(
            modifier = Modifier.padding(
                top = FDS.Sizer.Padding.padding16,
                start = FDS.Sizer.Padding.padding24,
                end = FDS.Sizer.Padding.padding24,
                bottom = FDS.Sizer.Padding.padding16,
            ),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center,
        ) {
            FoundationButton(
                modifier = Modifier.weight(1f),
                text = stringResource(com.vietinbank.core_ui.R.string.common_back),
                onClick = onBack,
                isLightButton = false,
            )

            Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap8))

            FoundationButton(
                modifier = Modifier.weight(1f),
                text = stringResource(com.vietinbank.core_ui.R.string.btn_continue),
                enabled = nextButtonEnabled,
                onClick = onNext,
            )
        }
    }
}

@Composable
private fun PinInputForm(
    modifier: Modifier = Modifier,
    pinState: PinState,
    pinValue: String,
    failCount: Int,
    enabled: Boolean = true,
    otpLength: Int = 6,
    showNumber: Boolean = true,
    focusRequester: FocusRequester? = null,
    onPinChange: (String) -> Unit,
    ctaDescription: @Composable () -> Unit,
) {
    Column(
        modifier = modifier.padding(
            vertical = FDS.Sizer.Padding.padding8,
            horizontal = FDS.Sizer.Padding.padding24,
        ),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        OTPPinInput(
            modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
            pinState = pinState,
            pinValue = pinValue,
            onPinValueChange = onPinChange,
            otpLength = otpLength,
            enabled = enabled,
            showNumber = showNumber,
            focusRequester = focusRequester,
        )
        if (pinState == PinState.ERROR) {
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
            FoundationText(
                text = stringResource(
                    R.string.confirm_otp_screen_otp_fail_alert,
                    failCount.toString(),
                ),
                style = FDS.Typography.captionCaptionL,
                color = FDS.Colors.stateError,
                textAlign = TextAlign.Center,
            )
        }
        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
        ctaDescription()
    }
}

@Preview
@Composable
private fun PreviewOTPInputDialog() {
    AppTheme {
        OTPInputDialogComponent(
            title = "PreviewOTPInputDialog",
            description = "PreviewOTPInputDialog Des",
            pinState = PinState.INPUT,
            pinValue = "123456",
            failCount = 5,
            enabled = true,
            onPinChange = {},
            nextButtonEnabled = true,
            onNext = {},
            onBack = {},
            ctaDescription = {
                FoundationText("This is CTA")
            },
        )
    }
}