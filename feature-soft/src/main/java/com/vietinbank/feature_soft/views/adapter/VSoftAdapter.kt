package com.vietinbank.feature_soft.views.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.soft.SoftUserEntity
import com.vietinbank.core_ui.R
import com.vietinbank.feature_soft.databinding.VItemUserActiveBinding

class VSoftAdapter : RecyclerView.Adapter<VSoftAdapter.UserActiveVH>() {
    private var lstOrigin: MutableList<SoftUserEntity> = mutableListOf()
    private var userSelected: SoftUserEntity? = null

    fun getUserSelected(): SoftUserEntity? = userSelected
    fun setUserSelected(user: SoftUserEntity?) {
        userSelected = user
    }

    fun updateList(lst: List<SoftUserEntity>) {
        lstOrigin.clear()
        lstOrigin.addAll(lst)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): UserActiveVH {
        return UserActiveVH(
            VItemUserActiveBinding.inflate(LayoutInflater.from(parent.context), parent, false),
        )
    }

    override fun getItemCount(): Int {
        return lstOrigin.size
    }

    override fun onBindViewHolder(holder: UserActiveVH, position: Int) {
        holder.bindData(lstOrigin[position])
    }

    inner class UserActiveVH(val binding: VItemUserActiveBinding) :
        RecyclerView.ViewHolder(binding.root) {
        init {
            binding.root.setThrottleClickListener {
                userSelected = lstOrigin[adapterPosition]
                notifyDataSetChanged()
            }
        }

        fun bindData(item: SoftUserEntity) {
            with(binding) {
                tvValue.text = item.getDisplayName()
                if (userSelected != null && userSelected?.getDisplayName() == item.getDisplayName()) {
                    tvValue.typeface = Utils.g().getFont(5, binding.root.context)
                    tvValue.setTextColor(
                        ContextCompat.getColor(
                            binding.root.context,
                            R.color.text_blue_02,
                        ),
                    )
                    ivCheck.setImageResource(R.drawable.ic_radio_check)
                } else {
                    tvValue.typeface = Utils.g().getFont(4, binding.root.context)
                    tvValue.setTextColor(
                        ContextCompat.getColor(
                            binding.root.context,
                            R.color.text_gray_08,
                        ),
                    )
                    ivCheck.setImageResource(R.drawable.ic_radio_uncheck)
                }
                vLine.isVisible = (adapterPosition != lstOrigin.size - 1)
            }
        }
    }
}