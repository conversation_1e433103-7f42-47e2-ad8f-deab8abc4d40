package com.vietinbank.feature_soft.views.fragment.pin

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.extensions.appendText
import com.vietinbank.core_common.extensions.focusAndShowKeyboard
import com.vietinbank.core_common.extensions.hideKeyboard
import com.vietinbank.core_common.extensions.lockSoftToTime
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.nav.ISoftNavigator
import com.vietinbank.core_domain.softotp.ISoftManager
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_soft.R
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import com.vietinbank.feature_soft.databinding.VFragmentSoftPinBinding
import com.vietinbank.feature_soft.views.fragment.VSoftViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class VSoftPinFragment : BaseFragment<VSoftViewModel>() {
    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softNavigator: ISoftNavigator

    @Inject
    lateinit var softManager: ISoftManager

    override val viewModel: VSoftViewModel by viewModels()

    private var otpFlow = VSoftConstants.VtpOTPFlowType.DEFAULT

    private var isShowPIN = false

    override fun inflateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
    ): VFragmentSoftPinBinding {
        return VFragmentSoftPinBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initObserve()
    }

    fun initView() {
        try {
            // kiem tra xem soft co bi lock hay khong
            softManager.getStatusLockSoft()?.let { message ->
                showNoticeDialog(message) {
                    onBackPressed()
                }
                return
            }

            otpFlow = arguments?.getString(VSoftConstants.Bundle.KEY_FLOW_OTP, "")?.let {
                VSoftConstants.VtpOTPFlowType.valueOf(it)
            } ?: VSoftConstants.VtpOTPFlowType.DEFAULT

            (binding as VFragmentSoftPinBinding).apply {
                if (viewModel.softManager.isAppActive) {
                    (binding as VFragmentSoftPinBinding).apply {
                        tvForgot.appendText("Quên mã PIN", underline = true)
                        inputPin.focusAndShowKeyboard()
                    }
                } else {
                    showNoticeDialog("Quý khách chưa kích hoạt Soft OTP") {
                        onBackPressed()
                    }
                }
                if (otpFlow == VSoftConstants.VtpOTPFlowType.ACTIVE) {
                    tvSoft.text =
                        "Quý khách vui lòng nhập mã PIN Soft OTP đã thiết lập từ lần kích hoạt đầu tiên cho người dùng phê duyệt của quý khách"
                }

                // pin
                handleHidePin(isShowPIN)
                ivEyeInput.setThrottleClickListener {
                    handleHidePin(!isShowPIN)
                }

                inputPin.onTextChange = {
                    if (inputPin.text.toString().length == 6) {
                        inputPin.hideKeyboard()
                    }
                }

                tvForgot.setThrottleClickListener {
                    showConfirmDialog(
                        message = "Quý khách sẽ yêu cầu kích hoạt lại tất cả các user phê duyệt. Quý khách có muốn tiếp tục?",
                        positiveButtonText = "Tiếp tục",
                        negativeButtonText = "Hủy",
                        positiveAction = {
                            softNavigator.goToActiveSoft(
                                VSoftConstants.Bundle.KEY_FLOW_OTP
                                    to VSoftConstants.VtpOTPFlowType.FORGOT_PIN.name,
                            )
                        },
                    )
                }

                btnNext.setThrottleClickListener {
                    if (inputPin.text.toString().isEmpty()) {
                        showNoticeDialog("Mã PIN không được bỏ trống. Quý khách vui lòng kiểm tra lại")
                        return@setThrottleClickListener
                    } else if (inputPin.text.toString().length < 6) {
                        showNoticeDialog("Mã PIN phải bao gồm 6 ký tự số. Quý khách vui lòng kiểm tra lại")
                        return@setThrottleClickListener
                    }
                    viewModel.sdkLoginPin(inputPin.text.toString())
                }
            }
        } catch (e: Exception) {
            onBackPressed()
        }
    }

    fun initObserve() {
        viewModel.apply {
            sdkAuthenLD.observe(viewLifecycleOwner) {
                if (VSoftConstants.OTPResultCode.SUCCESS.code == it) {
                    softManager.wrongPinCounter = 0
                    directFlow(otpFlow)
                } else if (VSoftConstants.OTPResultCode.WRONG_PIN_CODE.code == it) {
                    // Sai Pin
                    softManager.wrongPinCounter++
                    if (softManager.wrongPinCounter == VSoftConstants.WRONG_PIN_CODE_MAX) {
                        updateLockSoft(lockSoftToTime())
                        showNoticeDialog(
                            getString(
                                com.vietinbank.core_ui.R.string.soft_pin_wrong_5_times,
                                lockSoftToTime(),
                            ),
                        ) {
                            onBackPressed()
                        }
                    } else {
                        showNoticeDialog(
                            getString(
                                com.vietinbank.core_ui.R.string.soft_pin_wrong_times,
                                VSoftConstants.WRONG_PIN_CODE_MAX - softManager.wrongPinCounter,
                            ),
                        ) {
                            (binding as VFragmentSoftPinBinding).apply {
                                inputPin.setText("")
                                inputPin.focusAndShowKeyboard()
                            }
                        }
                    }
                }
            }

//            // danh sach user kich hoạt trên app
            sdkUserListInJson.observe(viewLifecycleOwner) {
                if (it.isEmpty()) {
                    // cập nhât trạng thái active soft cho app + user
                    viewModel.updateStatusSoft(false)
                    softManager.isAppActive = false
                    softManager.isUserActive = false
                }
            }

            // call api un active
//            blockTokenLiveData.observe(viewLifecycleOwner) {
//                if (it.code == "0" || it.code == "14") {
//                    // xóa thành công
//                    viewModel.deleteAllTokensSmartOTP()
//                } else {
//                    showNoticeDialog(it.message ?: "")
//                }
//            }

            // xoa token in sdk
//            deleteAllTokensLD.observe(viewLifecycleOwner) {
//                if (it) {
//                    appConfig.updateStatusSoft(false)
//                    SdkNative.getInstance().setSelectedUserId("")
//                    showNoticeDialog("Quý khách đã hủy xác thực giao dịch bằng Soft OTP thành công") {
//                        onBackPressed()
//                    }
//
//                }
//            }

            // đồng bộ soft otp
            sdkCheckStatusCodeLD.observe(viewLifecycleOwner) {
                if (it == VSoftConstants.OTPResultCode.SUCCESS.code) {
                    showNoticeDialog("Quý khách đã đồng bộ xác thực giao dịch Soft OTP thành công") {
                        onBackPressed()
                    }
                } else {
                    // loi
                    showNoticeDialog(VSoftConstants.getErrorMessage(it)) {
                        onBackPressed()
                    }
                }
            }

            sdkCheckStatusLD.observe(viewLifecycleOwner) {
                if (it) {
                    // tat thanh cong
                    softManager.isUserActive = false
                    sdkUserListInJson(false)
                    showNoticeDialog("Quý khách đã hủy xác thực giao dịch bằng Soft OTP thành công") {
                        onBackPressed()
                    }
                }
            }
        }
    }

    private fun directFlow(flow: VSoftConstants.VtpOTPFlowType) {
        activity?.runOnUiThread {
            when (flow) {
                VSoftConstants.VtpOTPFlowType.ACTIVE -> {
                    softNavigator.goToActiveSoft(VSoftConstants.Bundle.KEY_FLOW_OTP to flow.name)
                }
                // LUONG TAT SMART OTP
                VSoftConstants.VtpOTPFlowType.BLOCK -> {
                    viewModel.turnOffInApp()
                }

                // LUONG DONG BO
                VSoftConstants.VtpOTPFlowType.SYNC -> {
                    viewModel.sdkDoSyncTime(false)
                }

                VSoftConstants.VtpOTPFlowType.CONFIRM_IN_EFAST -> {
                    val bundle = Bundle()
                    bundle.putBoolean(VSoftConstants.Bundle.KEY_DATA_1, true)
                    setFragmentResult(VSoftConstants.RESULT_VERIFY_PIN, bundle)
                    softNavigator.popBackStack()
                }

                // LUONG test XAC NHAN GIAO DICH
                VSoftConstants.VtpOTPFlowType.CONFIRM_IN_APP -> {
                    softNavigator.goToSoftInEfast(
                        VSoftConstants.Bundle.KEY_DATA_1
                            to arguments?.getString(VSoftConstants.Bundle.KEY_DATA_1, ""),
                        VSoftConstants.Bundle.KEY_DATA_2
                            to arguments?.getString(VSoftConstants.Bundle.KEY_DATA_2, ""),
                    )
                }

                // LUONG LAY OTP QUA PUSH NOTIFICATION
                VSoftConstants.VtpOTPFlowType.CONFIRM_NOTIFY -> {
                    softNavigator.goToVerifyOnline()
                }

                // LUONG LAY OTP chủ động
                VSoftConstants.VtpOTPFlowType.GET_OTP -> {
                    // online - true
                    // offline - false
                    softNavigator.goToListUser(VSoftConstants.Bundle.KEY_DATA_1 to isNetworkAvailable())
                }

                else -> {
                    // default
                }
            }
        }
    }

    override fun onBackPressed(): Boolean {
        softNavigator.popBackStack()
        return true
    }

    private fun handleHidePin(isShow: Boolean = false) {
        (binding as VFragmentSoftPinBinding).apply {
            isShowPIN = isShow
            inputPin.setVtbShowOTP(isShowPIN)
            ivEyeInput.setImageResource(
                if (isShowPIN) {
                    R.drawable.v_soft_ic_hide_eye
                } else {
                    R.drawable.v_soft_ic_show_eye
                },
            )
        }
    }
}