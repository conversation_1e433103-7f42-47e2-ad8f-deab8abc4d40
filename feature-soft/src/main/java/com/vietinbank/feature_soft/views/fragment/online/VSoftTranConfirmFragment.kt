package com.vietinbank.feature_soft.views.fragment.online

import android.os.Bundle
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.nav.ISoftNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.soft.SoftEntity
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import com.vietinbank.feature_soft.databinding.VFragmentSoftTranConfirmBinding
import com.vietinbank.feature_soft.views.fragment.VSoftViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class VSoftTranConfirmFragment : BaseFragment<VSoftViewModel>() {
    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softNavigator: ISoftNavigator
    override val viewModel: VSoftViewModel by viewModels()

    private var transactionEntity: SoftEntity? = null

    override fun inflateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
    ): VFragmentSoftTranConfirmBinding {
        return VFragmentSoftTranConfirmBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initObserve()
    }

    fun initObserve() {
        viewModel.apply {
            sdkCrotpWithTransactionInfoLD.observe(viewLifecycleOwner) {
                // step 18+19+20
                (binding as VFragmentSoftTranConfirmBinding).inputOTP.setText(it)
                startCoolDown()
            }

            sdkConfirmTransactionLD.observe(viewLifecycleOwner) {
                if (it == VSoftConstants.OTPResultCode.SUCCESS.code) {
                    // thanh cong
                    // hien thi ket qua
                    showNoticeDialog("Đã gửi yêu cầu phê duyệt thành công!") {
                        softNavigator.popBackGraph()
                    }
                } else {
                    showNoticeDialog(VSoftConstants.getErrorMessage(it)) {
                        softNavigator.popBackStack()
                    }
                }
            }
        }
    }

    fun initView() {
        try {
            transactionEntity = Utils.g().provideGson().fromJson(
                arguments?.getString(VSoftConstants.Bundle.KEY_DATA_1),
                SoftEntity::class.java,
            )

            viewModel.sdkGetCRotpWithTransactionInfo(
                transactionEntity?.transactionData,
                transactionEntity?.challenge,
            )
        } catch (_: Exception) {
        }

        (binding as VFragmentSoftTranConfirmBinding).apply {
            headerView.setVtbOnBackPressed {
                softNavigator.popBackStack()
            }
            onTimerTick = {
                tvTime.text = "Mã OTP sẽ cập nhật sau ${(it / 1000)}"
            }

            onTimerFinished = {
                viewModel.sdkGetCRotpWithTransactionInfo(
                    transactionEntity?.transactionData,
                    transactionEntity?.challenge,
                )
            }

            btnApprove.setThrottleClickListener {
                // step 23 -> 32 xac nhan
                viewModel.sdkConfirmTransaction(
                    transactionEntity?.userID,
                    transactionEntity?.transactionID,
                    inputOTP.text.toString(),
                    true,
                )
            }

            btnCancel.setThrottleClickListener {
                // step 23 -> 32 xac nhan
                viewModel.sdkConfirmTransaction(
                    viewModel.sdkTransInfoModel?.userID,
                    viewModel.sdkTransInfoModel?.transactionID,
                    inputOTP.text.toString(),
                    false,
                )
            }
        }
    }

    // count time otp
    private var countDownTimer: CountDownTimer? = null
    var onTimerFinished: (() -> Unit)? = null
    var onTimerTick: ((Long) -> Unit)? = null

    private fun startCoolDown() {
        try {
            countDownTimer?.cancel()
            countDownTimer = null
            countDownTimer =
                object : CountDownTimer((VSoftConstants.COUNT_OTP_TIME * 1000).toLong(), 10) {
                    override fun onTick(timeLeft: Long) {
                        if (isAdded && isVisible) {
                            onTimerTick?.invoke(timeLeft)
                        }
                    }

                    override fun onFinish() {
                        onTimerFinished?.invoke()
                    }
                }.start()
        } catch (_: Exception) {
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        countDownTimer?.cancel()
        countDownTimer = null
    }
}