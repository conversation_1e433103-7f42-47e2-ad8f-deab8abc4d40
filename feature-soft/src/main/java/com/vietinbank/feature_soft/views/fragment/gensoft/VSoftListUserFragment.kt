package com.vietinbank.feature_soft.views.fragment.gensoft

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.nav.ISoftNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import com.vietinbank.feature_soft.databinding.VFragmentSoftListUserBinding
import com.vietinbank.feature_soft.views.adapter.VSoftAdapter
import com.vietinbank.feature_soft.views.fragment.VSoftViewModel
import dagger.hilt.android.AndroidEntryPoint
import vn.mk.token.sdk.SdkNative
import javax.inject.Inject

@AndroidEntryPoint
class VSoftListUserFragment : BaseFragment<VSoftViewModel>() {
    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softNavigator: ISoftNavigator
    override val viewModel: VSoftViewModel by viewModels()

    private var adapter: VSoftAdapter? = null
    private var isOnline: Boolean = false
    override fun inflateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
    ): VFragmentSoftListUserBinding {
        return VFragmentSoftListUserBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initObserve()
    }

    fun initView() {
        isOnline = arguments?.getBoolean(VSoftConstants.Bundle.KEY_DATA_1, false) == true
        // danh sách người dùng
        viewModel.sdkUserListInJson()
        adapter = VSoftAdapter()

        (binding as VFragmentSoftListUserBinding).apply {
            headerView.setVtbOnBackPressed {
                onBackPressed()
            }
            lstUser.adapter = adapter

            btnNext.setThrottleClickListener {
                adapter?.getUserSelected()?.userID?.let { id ->
                    SdkNative.getInstance().setSelectedUserId(id)
                    // kiem tra co mang hay khong
                    if (!isNetworkAvailable()) {
                        // luồng offline
                        // scan + input
                        softNavigator.goToVerifyOffline()
                    } else {
//                        if (isOnline) {
                        // điều hướng sang màn thông tin giao dịch -> confirm
                        viewModel.sdkDoSyncTime()
//                        } else if (isNetworkAvailable()) {
                        // luồng offline
//                            viewModel.sdkDoSyncTime()
//                        }
                    }
                }
            }
        }
    }

    fun initObserve() {
        viewModel.apply {
            sdkUserListInJson.observe(viewLifecycleOwner) {
                if (it.isNotEmpty()) {
                    adapter?.setUserSelected(it[0])
                    adapter?.updateList(it)
                }
            }

            sdkCheckStatusCodeLD.observe(viewLifecycleOwner) {
                if (it == VSoftConstants.OTPResultCode.SUCCESS.code) {
                    // khi tiếp tục thì kiểm tra user đã kích hoạt soft trên app hay chua
//                    viewModel.sdkCheckUserIdExistence(userId)
                    sdkGetTransactionInfo("", "")
                } else {
                    // hien thị loi
                    showNoticeDialog(VSoftConstants.getErrorMessage(it))
                }
            }

            sdkTransactionInfoLD.observe(viewLifecycleOwner) {
                // step 20
                if (VSoftConstants.OTPResultCode.SUCCESS.code == it.responseCode) {
                    if (isOnline) {
                        // chuyen sang man hinh co the confirm giao dich
                        softNavigator.goToVerifyOnline(
                            VSoftConstants.Bundle.KEY_DATA_1 to Utils.g().provideGson().toJson(it),
                        )
                    } else {
                        softNavigator.goToGenOTP(
                            VSoftConstants.Bundle.KEY_DATA_1 to it.transactionData,
                            VSoftConstants.Bundle.KEY_DATA_2 to it.challenge,
                        )
                    }
                } else {
                    showNoticeDialog(VSoftConstants.getErrorMessage(it.responseCode))
                }
            }
        }
    }

    override fun onBackPressed(): Boolean {
        softNavigator.popBackGraph()
        return true
    }
}