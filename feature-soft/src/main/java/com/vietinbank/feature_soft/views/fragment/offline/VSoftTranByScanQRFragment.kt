package com.vietinbank.feature_soft.views.fragment.offline

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.os.Bundle
import android.provider.MediaStore
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.fragment.app.viewModels
import com.google.zxing.BarcodeFormat
import com.google.zxing.BinaryBitmap
import com.google.zxing.DecodeHintType
import com.google.zxing.LuminanceSource
import com.google.zxing.MultiFormatReader
import com.google.zxing.RGBLuminanceSource
import com.google.zxing.Reader
import com.google.zxing.Result
import com.google.zxing.common.HybridBinarizer
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.nav.ISoftNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import com.vietinbank.feature_soft.databinding.VFragmentSoftOffScanQrBinding
import com.vietinbank.feature_soft.views.fragment.VSoftViewModel
import dagger.hilt.android.AndroidEntryPoint
import me.dm7.barcodescanner.zxing.ZXingScannerView
import vn.mk.token.sdk.SdkNative
import java.io.FileNotFoundException
import java.io.InputStream
import java.util.EnumSet
import java.util.Hashtable
import javax.inject.Inject

@AndroidEntryPoint
class VSoftTranByScanQRFragment :
    BaseFragment<VSoftViewModel>(),
    ZXingScannerView.ResultHandler {
    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softNavigator: ISoftNavigator
    override val viewModel: VSoftViewModel by viewModels()
    override fun inflateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
    ): VFragmentSoftOffScanQrBinding {
        return VFragmentSoftOffScanQrBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initListener()
        initObserve()
    }

    private fun initView() {
        activity?.let {
            if (ContextCompat.checkSelfPermission(
                    it,
                    Manifest.permission.CAMERA,
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                // Permission not granted
                if (!ActivityCompat.shouldShowRequestPermissionRationale(
                        it,
                        Manifest.permission.CAMERA,
                    )
                ) {
                    // Can ask user for permission
                    requestPermissionCameraLauncher.launch(Manifest.permission.CAMERA)
                }
            }
        }

        initScanner()
    }

    private fun initObserve() {
        viewModel.apply {
            sdkDecryptQRCodeDataLD.observe(viewLifecycleOwner) {
                // step 17
                // từ string -> convert VTBTransactionInfoEntity
                // 0|7|d7179c9c-9d61-44c5-892e-88b8|1|790444#1
                println(it)
                if (TextUtils.isEmpty(it) || !it.contains("#")) {
                    showNoticeDialog("Thông tin QR không hợp lệ") {
                        onBackPressed()
                    }
                } else {
                    val decryptQRLst: List<String> = it.split("#")
                    if (decryptQRLst.size == 2) {
                        val dataItems: List<String> = decryptQRLst[0].split("\\|".toRegex())
                        if (dataItems.size >= 5) {
                            softNavigator.goToGenOTP(
                                VSoftConstants.Bundle.KEY_DATA_1 to decryptQRLst[1],
                                VSoftConstants.Bundle.KEY_DATA_2 to dataItems[4],
                            )
                        } else {
                            showNoticeDialog("Thông tin QR không hợp lệ") {
                                onBackPressed()
                            }
                        }
                    } else {
                        showNoticeDialog("Thông tin QR không hợp lệ") {
                            onBackPressed()
                        }
                    }
                }
            }
        }
    }

    private fun initListener() {
        (binding as VFragmentSoftOffScanQrBinding).apply {
            headerView.setVtbOnBackPressed { onBackPressed() }
            vtbPicture.setOnClickListener {
                Intent(
                    Intent.ACTION_PICK,
                    MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                ).also { pikerIntent ->
                    pikerIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    openGalleryLauncher.launch(pikerIntent)
                }
            }
        }
    }

    private val requestPermissionCameraLauncher =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted: Boolean ->
            if (isGranted) {
                initScanner()
            } else {
                onBackPressed()
            }
        }

    private val openGalleryLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                result.data?.data?.let { uri ->
                    var imageStream: InputStream? = null
                    try {
                        imageStream = activity?.contentResolver?.openInputStream(uri)
                        BitmapFactory.decodeStream(imageStream)
                        scanBitmap(BitmapFactory.decodeStream(imageStream))
                    } catch (e: FileNotFoundException) {
                        println("FileNotFoundException")
                    } finally {
                        imageStream?.close()
                    }
                }
            }
        }

    override fun onDestroyView() {
        (binding as VFragmentSoftOffScanQrBinding).vtbScanView.stopCamera()
        super.onDestroyView()
    }

    private fun initScanner() {
//        // this paramter will make your HUAWEI phone works great!
        (binding as VFragmentSoftOffScanQrBinding).apply {
            vtbScanView.setAutoFocus(true)
            vtbScanView.setFormats(listOf<BarcodeFormat>(BarcodeFormat.QR_CODE))
            vtbScanView.setResultHandler(this@VSoftTranByScanQRFragment) // Register ourselves as a handler for scan results.
            vtbScanView.startCamera() // Start camera on resume
        }
    }

    private fun scanBitmap(bMap: Bitmap) {
        val intArray = IntArray(bMap.width * bMap.height)
        // copy pixel data from the Bitmap into the 'intArray' array
        bMap.getPixels(intArray, 0, bMap.width, 0, 0, bMap.width, bMap.height)
        val source: LuminanceSource = RGBLuminanceSource(bMap.width, bMap.height, intArray)
        val bitmap = BinaryBitmap(HybridBinarizer(source))
        val reader: Reader = MultiFormatReader() // use this otherwise
        // ChecksumException
        try {
            val decodeHints = Hashtable<DecodeHintType, Any?>()
            decodeHints[DecodeHintType.TRY_HARDER] = java.lang.Boolean.TRUE
            decodeHints[DecodeHintType.POSSIBLE_FORMATS] = EnumSet.allOf(
                BarcodeFormat::class.java,
            )
            val result = reader.decode(bitmap, decodeHints)
            val barcode = result.text
            println("VTBSCANQR ============= $barcode")
        } catch (e: Exception) {
            println("VTBSCANQR ============= ${e.message}")
        }
    }

    override fun handleResult(p: Result?) {
        // Do something with the result here
        println("VTBQRRESULT: ======= ${p?.text ?: ""}")
        val handleQR = p?.text?.split("|") ?: listOf()
        if (handleQR.size > 1) {
            if (handleQR[0] == String(SdkNative.getInstance().selectedUserId)) {
                viewModel.sdkDecryptQRCodeDataWithUserId(handleQR[0], handleQR[1])
            } else {
                showNoticeDialog("Thông tin QR không hợp lệ")
            }
        } else {
            showNoticeDialog("Thông tin QR không hợp lệ") {
                onBackPressed()
            }
        }
    }

    override fun onBackPressed(): Boolean {
        softNavigator.popBackStack()
        return true
    }
}