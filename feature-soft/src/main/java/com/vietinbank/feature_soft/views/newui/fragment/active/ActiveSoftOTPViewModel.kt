package com.vietinbank.feature_soft.views.newui.fragment.active

import androidx.compose.runtime.Stable
import androidx.lifecycle.SavedStateHandle
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.soft.KeypassParams
import com.vietinbank.core_domain.models.soft.SoftOtpActivationType
import com.vietinbank.core_domain.models.soft.VGetSoftOtpActivationCodeParams
import com.vietinbank.core_domain.softotp.ISoftManager
import com.vietinbank.core_domain.usecase.soft.DeleteSoftOTPTokenUseCase
import com.vietinbank.core_domain.usecase.soft.ForgotPinUseCase
import com.vietinbank.core_domain.usecase.soft.SoftUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.OneTimeEvent
import com.vietinbank.feature_soft.common.getOTPActiveFlow
import com.vietinbank.feature_soft.views.newui.screen.ReceiveOTPMethod
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class ActiveSoftOTPViewModel @Inject constructor(
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val sessionManager: ISessionManager,
    override val ottSetupService: IOttSetupService,
    private val softUseCase: SoftUseCase,
    private val softManager: ISoftManager,
    private val forgotPinUseCase: ForgotPinUseCase,
    private val deleteSoftOTPTokenUseCase: DeleteSoftOTPTokenUseCase,
    savedStateHandle: SavedStateHandle,
) : BaseViewModel() {

    val activeFlow: ActiveSoftOTPFlow = savedStateHandle.getOTPActiveFlow()

    private val _uiState = MutableStateFlow(UserActiveInfoUiState())
    val uiState = _uiState.asStateFlow()

    init {
        _uiState.value = UserActiveInfoUiState(
            userProf.getFullName() ?: "",
            userProf.getUserName() ?: "",
            userProf.getEmail() ?: "",
            userProf.getPhoneNo() ?: "",
        )
        if (activeFlow == ActiveSoftOTPFlow.FORGOT) {
            deleteAllTokensSmartOTP()
        }
    }

    override fun onDisplayErrorMessage(exception: AppException) {
        sendEvent(ActiveSoftOTPEvent.OnGetActivationCodeFail)
    }

    private fun deleteAllTokensSmartOTP() {
        launchJob {
            forgotPinUseCase.invoke()
        }
    }

    fun onMethodPicked(method: ReceiveOTPMethod) {
        _uiState.update {
            it.copy(pickedOTPMethod = method)
        }
    }

    fun onKeypassValueChange(value: String) {
        _uiState.update {
            it.copy(
                keypassPUKValue = value,
                keypassError = false,
            )
        }
    }

    fun getActivationCodeForSecondInstall() {
        /* Luồng 2.1.1 Kích hoạt Vietinbank OTP của MK doc
        Áp dụng cho user thứ 2 (Active User B)
        Trước khi call getActivationCode cần call API blockToken với tokenSn lưu từ lần 1
        Response code = 0 hoặc 14 thì xóa thông tin SoftTokenSN ở DB của MB app. (MK Doc)
        Sau đó call đến deleteAllExistToken và setSelectedUserId("")
         */
        launchJob {
            handleResource(
                deleteSoftOTPTokenUseCase(appConfig.getSoftOTPToken() ?: ""),
            ) {
                getActivationCode()
            }
        }
    }

    fun getActivationCode() = launchJob {
        val response = softUseCase.getActivationCode(
            VGetSoftOtpActivationCodeParams(
                when (_uiState.value.pickedOTPMethod) {
                    ReceiveOTPMethod.SMS -> SoftOtpActivationType.SMS
                    ReceiveOTPMethod.EMAIL -> SoftOtpActivationType.EMAIL
                }, // Temp
                userProf.getCifNo() ?: "",
                userProf.getUserName() ?: "",
            ),
        )
        handleResource(response) { result ->
            sendEvent(
                ActiveSoftOTPEvent.OnGetActivationCodeDone(result.message ?: ""),
            )
        }
    }

    fun forgotKeypass() = launchJob {
        val response = softUseCase.forgotKeypassPin(
            KeypassParams(
                sendType = when (_uiState.value.pickedOTPMethod) {
                    ReceiveOTPMethod.SMS -> SoftOtpActivationType.SMS.type
                    ReceiveOTPMethod.EMAIL -> SoftOtpActivationType.EMAIL.type
                },
                puk = _uiState.value.keypassPUKValue ?: "",
                userProf.getUserName() ?: "",
            ),
        )
        when (response) {
            is Resource.Error -> {
                _uiState.update {
                    it.copy(keypassError = true)
                }
            }

            is Resource.Success -> {
                sendEvent(ActiveSoftOTPEvent.OnRequestKeypassPinDone)
            }
        }
    }
}

@Stable
data class UserActiveInfoUiState(
    val fullName: String = "",
    val username: String = "",
    val email: String = "",
    val phoneNumber: String = "",
    val pickedOTPMethod: ReceiveOTPMethod = ReceiveOTPMethod.SMS,
    val keypassPUKValue: String? = null,
    val keypassError: Boolean = false,
)

internal sealed interface ActiveSoftOTPEvent : OneTimeEvent {
    data class OnGetActivationCodeDone(val otp: String) : ActiveSoftOTPEvent
    object OnGetActivationCodeFail : ActiveSoftOTPEvent
    object OnRequestKeypassPinDone : ActiveSoftOTPEvent
}