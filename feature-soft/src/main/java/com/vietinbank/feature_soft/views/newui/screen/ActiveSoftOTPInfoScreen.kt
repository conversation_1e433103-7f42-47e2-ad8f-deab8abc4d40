package com.vietinbank.feature_soft.views.newui.screen

import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.animateIntAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.layout.layout
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.TextUnitType
import com.vietinbank.core_ui.base.dialog.GetKeypassInstruction
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.core_ui.utils.commonRoundedCornerCard
import com.vietinbank.core_ui.utils.eFastBackgroundLevel2
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_soft.R
import com.vietinbank.feature_soft.views.newui.fragment.active.UserActiveInfoUiState
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun ActiveSoftOTPInfoScreen(
    modifier: Modifier = Modifier,
    uiState: UserActiveInfoUiState,
    isKeypassActiveForgot: Boolean = false,
    onMethodPicked: (ReceiveOTPMethod) -> Unit,
    onKeypassValueChange: (String) -> Unit,
    onNext: () -> Unit,
    onBack: () -> Unit,
) {
    var methodPickerVisible by remember {
        mutableStateOf(false)
    }

    var keypassInstructionVisible by remember {
        mutableStateOf(false)
    }
    val focusManager = LocalFocusManager.current
    val keyboardController = LocalSoftwareKeyboardController.current

    Box(
        modifier = modifier.safeClickable {
            focusManager.clearFocus()
            keyboardController?.hide()
        },
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = FDS.Sizer.Padding.padding8),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            FoundationAppBar(
                isLightIcon = false,
                title = if (isKeypassActiveForgot) {
                    stringResource(R.string.active_forgot_keypass_header)
                } else {
                    stringResource(R.string.active_opt_info_header)
                },
                onNavigationClick = onBack,
                modifier = Modifier.padding(top = FDS.Sizer.Padding.padding8),
            )
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
            Column(
                modifier = Modifier.commonRoundedCornerCard(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                FoundationText(
                    stringResource(R.string.active_opt_info_title),
                    style = FDS.Typography.headingH4,
                    color = FDS.Colors.characterPrimary,
                )
                HorizontalDivider(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = FDS.Sizer.Padding.padding24),
                    thickness = FDS.Sizer.Stroke.stroke05,
                    color = FDS.Colors.strokeDivider,
                )
                Column(
                    modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
                    verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding16),
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween,
                    ) {
                        FoundationText(
                            stringResource(R.string.active_opt_info_full_name),
                            style = FDS.Typography.bodyB2,
                            color = FDS.Colors.characterSecondary,
                        )
                        FoundationText(
                            uiState.fullName,
                            style = FDS.Typography.bodyB2,
                            color = FDS.Colors.characterPrimary,
                        )
                    }
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween,
                    ) {
                        FoundationText(
                            stringResource(R.string.active_opt_info_user_name),
                            style = FDS.Typography.bodyB2,
                            color = FDS.Colors.characterSecondary,
                        )
                        FoundationText(
                            uiState.username,
                            style = FDS.Typography.bodyB2,
                            color = FDS.Colors.characterPrimary,
                        )
                    }
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween,
                    ) {
                        FoundationText(
                            stringResource(R.string.active_opt_info_email),
                            style = FDS.Typography.bodyB2,
                            color = FDS.Colors.characterSecondary,
                        )
                        FoundationText(
                            uiState.email,
                            style = FDS.Typography.bodyB2,
                            color = FDS.Colors.characterPrimary,
                        )
                    }
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween,
                    ) {
                        FoundationText(
                            stringResource(R.string.active_opt_info_phone_number),
                            style = FDS.Typography.bodyB2,
                            color = FDS.Colors.characterSecondary,
                        )
                        FoundationText(
                            uiState.phoneNumber,
                            style = FDS.Typography.bodyB2,
                            color = FDS.Colors.characterPrimary,
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
            OTPSpinner(
                modifier = Modifier
                    .commonRoundedCornerCard()
                    .padding(
                        horizontal = FDS.Sizer.Padding.padding24,
                    ),
                selectedItemName = uiState.pickedOTPMethod.title,
                onMethodPickedClick = {
                    methodPickerVisible = !methodPickerVisible
                },
                isNeedKeypassInput = isKeypassActiveForgot,
                keypassPUKValue = uiState.keypassPUKValue,
                isKeypassInputError = uiState.keypassError,
                onKeypassPUKChange = onKeypassValueChange,
                onKeypassInstructionClick = {
                    keypassInstructionVisible = true
                },
            )

            Spacer(modifier = Modifier.weight(1f))
            FoundationButton(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        top = FDS.Sizer.Padding.padding16,
                        start = FDS.Sizer.Padding.padding24,
                        end = FDS.Sizer.Padding.padding24,
                        bottom = FDS.Sizer.Gap.gap36,
                    ),
                text = stringResource(com.vietinbank.core_ui.R.string.btn_continue),
                onClick = onNext,
            )
        }

        ReceiveOTPPicker(
            visible = methodPickerVisible,
            receiveMethods = ReceiveOTPMethod.entries,
            onItemPicked = onMethodPicked,
            onDismiss = { methodPickerVisible = !methodPickerVisible },
        )

        GetKeypassInstruction(
            visible = keypassInstructionVisible,
            onBack = {
                keypassInstructionVisible = !keypassInstructionVisible
            },
        )
    }
}

@Composable
fun OTPSpinner(
    modifier: Modifier = Modifier,
    selectedItemName: String? = null,
    keypassPUKValue: String? = null,
    isNeedKeypassInput: Boolean = false,
    onMethodPickedClick: () -> Unit,
    isKeypassInputError: Boolean = false,
    onKeypassPUKChange: ((String) -> Unit)? = null,
    onKeypassInstructionClick: (() -> Unit)? = null,
) {
    val hasSelection = selectedItemName != null
    Column(modifier = modifier) {
        if (isNeedKeypassInput) {
            AnimatedLabelTextField(
                label = stringResource(R.string.keypass_input_field_label),
                value = keypassPUKValue ?: "",
                onValueChange = onKeypassPUKChange ?: {},
                onKeypassInstructionClick = onKeypassInstructionClick ?: {},
            )

            if (isKeypassInputError) {
                Spacer(modifier = Modifier.height(FDS.Sizer.Padding.padding10))

                FoundationText(
                    text = stringResource(R.string.keypass_input_error_message),
                    style = FDS.Typography.captionCaptionL,
                    color = FDS.Colors.stateError,
                )
            }

            val parentHorizontalPadding = FDS.Sizer.Padding.padding24

            HorizontalDivider(
                modifier = Modifier
                    .padding(vertical = FDS.Sizer.Padding.padding8)
                    .layout { measurable, constrain ->
                        val placeable = measurable.measure(
                            constrain.copy(
                                maxWidth = constrain.maxWidth +
                                    (parentHorizontalPadding * 2).roundToPx(),
                            ),
                        )
                        layout(placeable.width, placeable.height) {
                            placeable.place(0, 0)
                        }
                    },
                color = FDS.Colors.strokeDivider,
                thickness = FDS.Sizer.Stroke.stroke05,
            )
        }

        Row(
            modifier = Modifier
                .safeClickable {
                    onMethodPickedClick.invoke()
                }
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Column(
                modifier = Modifier.weight(1f),
            ) {
                if (hasSelection) {
                    Text(
                        text = stringResource(R.string.receive_otp_picker_title),
                        style = FDS.Typography.captionCaptionL,
                        color = FDS.Colors.characterSecondary,
                        modifier = Modifier.padding(bottom = FDS.Sizer.Gap.gap2),
                    )
                    Text(
                        text = selectedItemName,
                        style = FDS.Typography.bodyB1,
                        color = FDS.Colors.characterHighlighted,
                    )
                } else {
                    Text(
                        text = stringResource(R.string.receive_otp_picker_title),
                        style = FDS.Typography.bodyB2Emphasized,
                        color = FDS.Colors.characterSecondary,
                    )
                }
            }

            Icon(
                painter = painterResource(
                    id = com.vietinbank.core_ui.R.drawable.ic_drop_down,
                ),
                contentDescription = null,
                tint = FDS.Colors.characterSecondary,
                modifier = Modifier
                    .size(FDS.Sizer.Icon.icon24),
            )
        }
    }
}

@Composable
private fun AnimatedLabelTextField(
    modifier: Modifier = Modifier,
    label: String,
    value: String,
    onValueChange: (String) -> Unit,
    onKeypassInstructionClick: () -> Unit,
) {
    var isFocused by remember { mutableStateOf(false) }

    // Transition control
    val offsetY by animateDpAsState(
        targetValue = if (isFocused) {
            -FDS.Sizer.Gap.gap8
        } else {
            -FDS.Sizer.Gap.gap0
        },
    )
    val fontSize by animateFloatAsState(
        targetValue = if (isFocused) {
            FDS.Typography.captionCaptionL.fontSize.value
        } else {
            FDS.Typography.bodyB2Emphasized.fontSize.value
        },
    )

    val fontWeight by animateIntAsState(
        targetValue = if (isFocused) {
            FDS.Typography.captionCaptionL.fontWeight?.weight ?: FontWeight.SemiBold.weight
        } else {
            FDS.Typography.bodyB2Emphasized.fontWeight?.weight ?: FontWeight.Bold.weight
        },
    )

    val labelPadding by animateDpAsState(
        targetValue = if (isFocused) {
            FDS.Sizer.Padding.padding10
        } else {
            FDS.Sizer.Padding.padding0
        },
    )

    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Box(modifier = Modifier.weight(1f)) {
            FoundationText(
                text = label,
                style = FDS.Typography.bodyB2Emphasized.copy(
                    fontSize = TextUnit(fontSize, TextUnitType.Sp),
                    fontWeight = FontWeight(fontWeight),
                ),
                color = FDS.Colors.characterSecondary,
                modifier = Modifier.offset(y = offsetY),
            )

            Column {
                Spacer(modifier = Modifier.height(labelPadding))
                BasicTextField(
                    value = value,
                    onValueChange = onValueChange,
                    textStyle = FDS.Typography.bodyB1.copy(
                        color = FDS.Colors.characterHighlighted,
                    ),
                    modifier = Modifier
                        .fillMaxWidth()
                        .onFocusChanged { isFocused = it.isFocused },
                )
            }
        }

        Image(
            painter = painterResource(
                id = R.drawable.ic_trailing_input_keypass,
            ),
            contentDescription = null,
            modifier = Modifier
                .size(FDS.Sizer.Icon.icon24)
                .safeClickable {
                    onKeypassInstructionClick.invoke()
                },
        )
    }
}

@Composable
private fun ReceiveOTPPicker(
    modifier: Modifier = Modifier,
    visible: Boolean,
    receiveMethods: List<ReceiveOTPMethod>,
    onItemPicked: (ReceiveOTPMethod) -> Unit,
    onDismiss: () -> Unit,
) {
    BaseBottomSheet(
        visible = visible,
        onDismissRequest = onDismiss,
        shape = RoundedCornerShape(FDS.Sizer.Radius.radius0),
    ) {
        Column {
            Column(
                modifier = modifier.commonRoundedCornerCard(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                FoundationText(
                    text = stringResource(R.string.receive_otp_picker_title),
                    style = FDS.Typography.headingH3,
                    color = FDS.Colors.characterHighlighted,
                )
                HorizontalDivider(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = FDS.Sizer.Padding.padding16),
                    color = FDS.Colors.strokeDivider,
                    thickness = FDS.Sizer.Stroke.stroke1,
                )

                LazyColumn(
                    modifier = Modifier.padding(
                        vertical = FDS.Sizer.Padding.padding8,
                        horizontal = FDS.Sizer.Padding.padding24,
                    ),
                ) {
                    itemsIndexed(receiveMethods) { index, method ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .safeClickable {
                                    onItemPicked.invoke(method)
                                    onDismiss.invoke()
                                },
                            horizontalArrangement = Arrangement.Start,
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            Box(
                                modifier = Modifier
                                    .size(FDS.Sizer.Icon.icon40)
                                    .clip(CircleShape)
                                    .background(colorResource(R.color.soft_opt_background_icon)),
                                contentAlignment = Alignment.Center,
                            ) {
                                Icon(
                                    modifier = Modifier
                                        .size(FDS.Sizer.Icon.icon24),
                                    painter = painterResource(method.icon),
                                    tint = FDS.Colors.characterHighlighted,
                                    contentDescription = null,
                                )
                            }

                            Spacer(modifier = Modifier.width(FDS.Sizer.Padding.padding8))

                            FoundationText(
                                text = method.title,
                                style = FDS.Typography.bodyB2,
                                color = FDS.Colors.characterHighlighted,
                            )
                        }

                        if (index != receiveMethods.lastIndex) {
                            HorizontalDivider(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = FDS.Sizer.Padding.padding16), // No horizontal padding - full width
                                color = FDS.Colors.strokeDivider,
                                thickness = FDS.Sizer.Stroke.stroke1,
                            )
                        }
                    }
                }
            }
        }
    }
}

enum class ReceiveOTPMethod(
    val icon: Int,
    val title: String,
) {
    SMS(icon = R.drawable.device_mobile, title = "SMS"),
    EMAIL(icon = R.drawable.mail, title = "Email"),
}

@Preview
@Composable
private fun PreviewActivateSoftOTPInfoScreen() {
    AppTheme {
        ActiveSoftOTPInfoScreen(
            modifier = Modifier
                .fillMaxSize()
                .eFastBackgroundLevel2()
                .systemBarsPadding()
                .imePadding(),
            uiState = UserActiveInfoUiState(),
            onMethodPicked = {},
            onNext = {},
            onBack = {},
            isKeypassActiveForgot = true,
            onKeypassValueChange = {},
        )
    }
}