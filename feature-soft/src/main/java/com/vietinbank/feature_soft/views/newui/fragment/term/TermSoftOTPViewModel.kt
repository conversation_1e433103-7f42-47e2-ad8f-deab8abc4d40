package com.vietinbank.feature_soft.views.newui.fragment.term

import androidx.compose.runtime.Stable
import androidx.lifecycle.SavedStateHandle
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_domain.softotp.ISoftManager
import com.vietinbank.core_domain.usecase.soft.GetOTPTermOfUseFileUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import com.vietinbank.feature_soft.common.getOTPActiveFlow
import com.vietinbank.feature_soft.views.newui.fragment.active.ActiveSoftOTPFlow
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import java.io.File
import javax.inject.Inject

@HiltViewModel
class TermSoftOTPViewModel @Inject constructor(
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val sessionManager: ISessionManager,
    override val ottSetupService: IOttSetupService,
    private val softManager: ISoftManager,
    private val getOTPTermOfUseFileUseCase: GetOTPTermOfUseFileUseCase,
    savedStateHandle: SavedStateHandle,
) : BaseViewModel() {
    val activeFlow: ActiveSoftOTPFlow = savedStateHandle.getOTPActiveFlow(VSoftConstants.Bundle.KEY_FLOW_OTP)
    private val _uiState = MutableStateFlow(TermSoftOTPUiState())
    val uiState = _uiState.asStateFlow()

    init {
        launchJob {
            handleResource(getOTPTermOfUseFileUseCase()) { file ->
                _uiState.update {
                    it.copy(termOfUseFile = file)
                }
            }
        }
    }

    val isSoftOTPActivated: Boolean
        get() = softManager.isAppActive

    fun onChecked(isChecked: Boolean) {
        _uiState.update {
            it.copy(confirmChecked = isChecked)
        }
    }
}

@Stable
data class TermSoftOTPUiState(
    val confirmChecked: Boolean = false,
    val termOfUseFile: File? = null,
) {
    val buttonEnabled: Boolean
        get() = confirmChecked
}