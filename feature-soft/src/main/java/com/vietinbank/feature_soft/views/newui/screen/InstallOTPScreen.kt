package com.vietinbank.feature_soft.views.newui.screen

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.foundation.pin.OTPPinInput
import com.vietinbank.core_ui.components.foundation.pin.PinState
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.core_ui.utils.commonRoundedCornerCard
import com.vietinbank.core_ui.utils.dismissKeyboardOnClickOutside
import com.vietinbank.core_ui.utils.eFastBackgroundLevel2
import com.vietinbank.feature_soft.R
import com.vietinbank.feature_soft.views.newui.fragment.install_otp.InstallOTPUIState
import kotlinx.coroutines.android.awaitFrame
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun InstallOTPScreen(
    modifier: Modifier = Modifier,
    uiState: InstallOTPUIState,
    onPinValueChange: (String) -> Unit,
    onRePinValueChange: (String) -> Unit,
    onNext: () -> Unit,
    onBack: () -> Unit,
) {
    val inputFocusRequester = remember {
        FocusRequester()
    }

    val reinputFocusRequester = remember {
        FocusRequester()
    }

    val keyboardController = LocalSoftwareKeyboardController.current

    LaunchedEffect(Unit) {
        awaitFrame()
        inputFocusRequester.requestFocus()
        keyboardController?.show()
    }

    LaunchedEffect(uiState.inputPinValue.length) {
        if (uiState.inputPinValue.length >= InstallOTPUIState.OTP_LENGTH) {
            reinputFocusRequester.requestFocus()
        }
    }

    Box(modifier = modifier.dismissKeyboardOnClickOutside()) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = FDS.Sizer.Padding.padding8),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            FoundationAppBar(
                isLightIcon = false,
                title = stringResource(R.string.install_otp_screen_title),
                onNavigationClick = onBack,
                modifier = Modifier.padding(top = FDS.Sizer.Padding.padding8),
            )
            Column(
                modifier = Modifier
                    .padding(vertical = FDS.Sizer.Padding.padding16)
                    .commonRoundedCornerCard()
                    .padding(horizontal = FDS.Sizer.Padding.padding24),

            ) {
                PinInstallInput(
                    title = stringResource(R.string.install_otp_screen_input_pin_title),
                    pinState = uiState.inputPinState,
                    pinValue = uiState.inputPinValue,
                    enabled = uiState.enablePinInput,
                    focusRequester = inputFocusRequester,
                    onPinValueChange = onPinValueChange,
                    onDone = {
                        reinputFocusRequester.requestFocus()
                    },
                )
                HorizontalDivider(
                    modifier = Modifier.padding(vertical = FDS.Sizer.Padding.padding8),
                )
                PinInstallInput(
                    title = stringResource(R.string.install_otp_screen_reinput_pin_title),
                    pinState = uiState.reinputPinState,
                    pinValue = uiState.reinputPinValue,
                    focusRequester = reinputFocusRequester,
                    enabled = uiState.enablePinInput,
                    onPinValueChange = onRePinValueChange,
                )
            }

            Spacer(modifier = Modifier.weight(1f))
            FoundationButton(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        top = FDS.Sizer.Padding.padding16,
                        start = FDS.Sizer.Padding.padding24,
                        end = FDS.Sizer.Padding.padding24,
                        bottom = FDS.Sizer.Gap.gap36,
                    ),
                enabled = uiState.enableNextButton,
                text = stringResource(com.vietinbank.core_ui.R.string.btn_continue),
                onClick = onNext,
            )
        }
    }
}

@Composable
private fun PinInstallInput(
    modifier: Modifier = Modifier,
    title: String,
    pinState: PinState,
    pinValue: String,
    focusRequester: FocusRequester? = null,
    onPinValueChange: (String) -> Unit,
    enabled: Boolean = true,
    onDone: (() -> Unit)? = null,
) {
    Column(
        modifier = modifier.padding(vertical = FDS.Sizer.Padding.padding8),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        FoundationText(
            title,
            style = FDS.Typography.bodyB1,
            color = FDS.Colors.characterPrimary,
        )
        OTPPinInput(
            modifier = Modifier.padding(vertical = FDS.Sizer.Padding.padding16),
            pinState = pinState,
            pinValue = pinValue,
            onPinValueChange = onPinValueChange,
            enabled = enabled,
            focusRequester = focusRequester,
            onDone = onDone,
            showNumber = false,
        )

        if (pinState == PinState.ERROR) {
            Spacer(modifier = Modifier.padding(FDS.Sizer.Gap.gap16))
            FoundationText(
                stringResource(R.string.install_otp_screen_input_alert),
                style = FDS.Typography.captionCaptionL,
                color = FDS.Colors.stateError,
            )
        }
    }
}

@Preview
@Composable
private fun PreviewInstallOTpScreen() {
    AppTheme {
        InstallOTPScreen(
            modifier = Modifier
                .fillMaxSize()
                .eFastBackgroundLevel2()
                .systemBarsPadding()
                .imePadding(),
            uiState = InstallOTPUIState(),
            onPinValueChange = {},
            onRePinValueChange = {},
            onNext = {},
            onBack = {},
        )
    }
}