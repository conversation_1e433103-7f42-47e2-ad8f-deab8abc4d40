package com.vietinbank.feature_soft.views.newui.screen

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.core_ui.utils.dismissKeyboardOnClickOutside
import com.vietinbank.core_ui.utils.eFastBackgroundLevel2
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_soft.R
import com.vietinbank.feature_soft.views.newui.component.OTPInputDialogComponent
import com.vietinbank.feature_soft.views.newui.fragment.confirm_otp.ConfirmInstallOTPUIState
import com.vietinbank.feature_soft.views.newui.fragment.confirm_otp.ConfirmOTPFlow
import kotlinx.coroutines.android.awaitFrame
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun ConfirmOTPScreen(
    modifier: Modifier = Modifier,
    uiState: ConfirmInstallOTPUIState,
    confirmOTPFlow: ConfirmOTPFlow,
    onPinChange: (String) -> Unit,
    onNext: () -> Unit,
    onCTA: () -> Unit,
    onBack: () -> Unit,
) {
    val focusManager = LocalFocusManager.current
    val keyboardController = LocalSoftwareKeyboardController.current
    val optFocusRequester = remember {
        FocusRequester()
    }

    // Clear old focus and request new one
    LaunchedEffect(Unit) {
        focusManager.clearFocus()
        awaitFrame()
        optFocusRequester.requestFocus()
        keyboardController?.show()
    }

    LaunchedEffect(uiState.enablePinInput) {
        if (!uiState.enablePinInput) {
            focusManager.clearFocus()
            keyboardController?.hide()
        }
    }

    Box(
        modifier = modifier.dismissKeyboardOnClickOutside(),
        contentAlignment = Alignment.Center,
    ) {
        OTPInputDialogComponent(
            title = when (confirmOTPFlow) {
                ConfirmOTPFlow.VERIFY_ACTIVE_FIRST, ConfirmOTPFlow.VERIFY_ACTIVE_SECOND ->
                    stringResource(R.string.confirm_otp_screen_title)

                ConfirmOTPFlow.TOGGLE ->
                    stringResource(R.string.toggle_otp_screen_title)

                ConfirmOTPFlow.SYNC ->
                    stringResource(R.string.sync_otp_title)

                ConfirmOTPFlow.VERIFY_OTP_SECOND_USER ->
                    stringResource(R.string.install_otp_second_title)
            },
            description = when (confirmOTPFlow) {
                ConfirmOTPFlow.VERIFY_ACTIVE_FIRST, ConfirmOTPFlow.VERIFY_ACTIVE_SECOND ->
                    stringResource(R.string.confirm_otp_screen_des)

                ConfirmOTPFlow.VERIFY_OTP_SECOND_USER ->
                    stringResource(R.string.install_otp_second_des)

                else -> null
            },
            pinState = uiState.pinState,
            pinValue = uiState.pinValue,
            failCount = uiState.errorCount,
            enabled = uiState.enablePinInput,
            onPinChange = onPinChange,
            nextButtonEnabled = uiState.nextButtonEnabled,
            focusRequester = optFocusRequester,
            showNumber = confirmOTPFlow == ConfirmOTPFlow.VERIFY_ACTIVE_FIRST ||
                confirmOTPFlow == ConfirmOTPFlow.VERIFY_ACTIVE_SECOND,
            onNext = onNext,
            onBack = onBack,
            ctaDescription = {
                when (confirmOTPFlow) {
                    ConfirmOTPFlow.VERIFY_ACTIVE_FIRST, ConfirmOTPFlow.VERIFY_ACTIVE_SECOND -> {
                        ResendCodeText(modifier = Modifier.fillMaxWidth(), onResendClick = onCTA)
                    }

                    ConfirmOTPFlow.TOGGLE -> {
                        ToggleOTPForgotPinCTA(
                            modifier = Modifier.fillMaxWidth(),
                            onClick = onCTA,
                        )
                    }

                    ConfirmOTPFlow.SYNC -> {
                        SyncOTPForgotPinCTA(
                            modifier = Modifier.fillMaxWidth(),
                            onClick = onCTA,
                        )
                    }

                    else -> {
                        ToggleOTPForgotPinCTA(
                            modifier = Modifier.fillMaxWidth(),
                            onClick = onCTA,
                        )
                    }
                }
            },
        )
    }
}

@Composable
private fun ResendCodeText(
    modifier: Modifier = Modifier,
    onResendClick: () -> Unit,
) {
    val text = buildAnnotatedString {
        append(stringResource(R.string.confirm_otp_screen_resend_ask))

        pushLink(
            LinkAnnotation.Clickable(
                tag = "resend",
                linkInteractionListener = { onResendClick() },
            ),
        )
        withStyle(
            style = SpanStyle(
                color = FDS.Colors.characterHighlightedLighter,
                fontWeight = FDS.Typography.captionCaptionLBold.fontWeight,
            ),
        ) {
            append(stringResource(R.string.confirm_otp_screen_resend_cta))
        }
        pop()
    }

    FoundationText(
        modifier = modifier,
        text = text,
        style = FDS.Typography.captionCaptionL,
        color = FDS.Colors.characterSecondary,
        textAlign = TextAlign.Center,
    )
}

@Composable
private fun ToggleOTPForgotPinCTA(
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
) {
    FoundationText(
        modifier = modifier.safeClickable {
            onClick.invoke()
        },
        text = stringResource(R.string.toggle_otp_cta),
        style = FDS.Typography.captionCaptionL,
        color = FDS.Colors.characterHighlightedLighter,
        textAlign = TextAlign.Center,
    )
}

@Composable
private fun SyncOTPForgotPinCTA(
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
) {
    FoundationText(
        modifier = modifier.safeClickable {
            onClick.invoke()
        },
        text = stringResource(R.string.sync_otp_cta),
        style = FDS.Typography.captionCaptionL,
        color = FDS.Colors.characterHighlightedLighter,
        textAlign = TextAlign.Center,
    )
}

@Preview
@Composable
private fun PreviewConfirmInstallOTPScreen() {
    AppTheme {
        ConfirmOTPScreen(
            modifier = Modifier
                .fillMaxSize()
                .eFastBackgroundLevel2()
                .systemBarsPadding()
                .imePadding(),
            uiState = ConfirmInstallOTPUIState(),
            confirmOTPFlow = ConfirmOTPFlow.VERIFY_ACTIVE_FIRST,
            onPinChange = {},
            onNext = {},
            onCTA = {},
            onBack = {},
        )
    }
}
