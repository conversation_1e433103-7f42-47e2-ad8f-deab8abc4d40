package com.vietinbank.feature_soft.views.newui.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.core_ui.utils.commonRoundedCornerCard
import com.vietinbank.feature_soft.R
import com.vietinbank.feature_soft.views.newui.fragment.keypass.KeypassInfoUiState
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun KeypassInfoScreen(
    modifier: Modifier = Modifier,
    uiState: KeypassInfoUiState,
    onBack: () -> Unit,
) {
    Box(modifier = modifier) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = FDS.Sizer.Padding.padding8),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            FoundationAppBar(
                isLightIcon = false,
                title = stringResource(R.string.keypass_info_title),
                onNavigationClick = onBack,
                modifier = Modifier.padding(top = FDS.Sizer.Padding.padding8),
            )
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
            Column(
                modifier = Modifier.commonRoundedCornerCard(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                FoundationInfoHorizontal(
                    modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
                    title = stringResource(R.string.keypass_info_token_serial),
                    value = uiState.keypassToken,
                    titleColor = FDS.Colors.characterPrimary,
                    titleStyle = FDS.Typography.bodyB2,
                    valueColor = FDS.Colors.characterHighlighted,
                    valueStyle = FDS.Typography.bodyB2,
                )

                HorizontalDivider(
                    modifier = Modifier.padding(vertical = FDS.Sizer.Padding.padding16),
                    color = FDS.Colors.strokeDivider,
                    thickness = FDS.Sizer.Stroke.stroke05,
                )

                FoundationInfoHorizontal(
                    modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
                    title = stringResource(R.string.keypass_info_token_code),
                    value = uiState.keypassToken,
                    titleColor = FDS.Colors.characterPrimary,
                    titleStyle = FDS.Typography.bodyB2,
                    valueColor = FDS.Colors.characterHighlighted,
                    valueStyle = FDS.Typography.bodyB2,
                )
            }
        }
    }
}

@Preview
@Composable
private fun PreviewKeypassInfoScreen() {
    AppTheme {
        KeypassInfoScreen(
            modifier = Modifier
                .fillMaxSize()
                .background(FDS.Colors.backgroundBgScreen)
                .systemBarsPadding()
                .imePadding(),
            uiState = KeypassInfoUiState(),
            onBack = {},
        )
    }
}
