package com.vietinbank.feature_soft.views.newui.fragment.term

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.fragment.app.viewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.core_ui.utils.eFastBackgroundLevel2
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import com.vietinbank.feature_soft.navigation.INewSoftNavigator
import com.vietinbank.feature_soft.views.newui.fragment.active.ActiveSoftOTPFlow
import com.vietinbank.feature_soft.views.newui.fragment.confirm_otp.ConfirmOTPFlow
import com.vietinbank.feature_soft.views.newui.screen.TermSoftOTPScreen
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class TermSoftOTPFragment : BaseFragment<TermSoftOTPViewModel>() {
    override val viewModel: TermSoftOTPViewModel by viewModels()

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softNavigator: INewSoftNavigator

    override val useCompose: Boolean = true

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.uiState.collectAsStateWithLifecycle()
        AppTheme {
            TermSoftOTPScreen(
                modifier = Modifier
                    .fillMaxSize()
                    .eFastBackgroundLevel2()
                    .systemBarsPadding()
                    .imePadding(),
                uiState = uiState,
                onConfirmCheckBoxChange = viewModel::onChecked,
                onNext = {
                    if (viewModel.activeFlow == ActiveSoftOTPFlow.FORGOT) {
                        softNavigator.goToNewActiveSoft(
                            VSoftConstants.Bundle.KEY_FLOW_OTP
                                to ActiveSoftOTPFlow.FORGOT.name,
                        )
                    } else {
                        if (viewModel.isSoftOTPActivated) {
                            softNavigator.goToConfirmOTP(
                                VSoftConstants.Bundle.KEY_FLOW_OTP
                                    to ConfirmOTPFlow.VERIFY_OTP_SECOND_USER.name,
                            )
                        } else {
                            softNavigator.goToNewActiveSoft()
                        }
                    }
                },
                onBack = {
                    softNavigator.popBackStack()
                },
            )
        }
    }
}