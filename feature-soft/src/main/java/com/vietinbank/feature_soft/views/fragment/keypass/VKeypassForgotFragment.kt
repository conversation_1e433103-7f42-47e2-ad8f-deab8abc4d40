package com.vietinbank.feature_soft.views.fragment.keypass

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.viewModels
import androidx.viewbinding.ViewBinding
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.extensions.openUrl
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.nav.ISoftNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_soft.R
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import com.vietinbank.feature_soft.common.constant.VSoftConstants.URL_SYNC
import com.vietinbank.feature_soft.databinding.VFragmentKeypassForgotPinBinding
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class VKeypassForgotFragment : BaseFragment<VKeypassViewModel>() {
    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softNavigator: ISoftNavigator
    override val viewModel: VKeypassViewModel by viewModels()
    private var isShowPIN = false
    private var pukCode = ""

    companion object {
        const val BY_EMAIL = "1"
        const val BY_SMS = "2"
    }

    override fun inflateViewBinding(inflater: LayoutInflater, container: ViewGroup?): ViewBinding {
        return VFragmentKeypassForgotPinBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initObserve()
    }

    private fun initView() {
        (binding as VFragmentKeypassForgotPinBinding).apply {
            headerView.setVtbOnBackPressed { onBackPressed() }
            tvGetPUK.isVisible = viewModel.getKeypassToken().isNotEmpty()
            vLine.isVisible = viewModel.getKeypassToken().isNotEmpty()
            infoName.setVtbValue(viewModel.getFullName())
            infoUser.setVtbValue(viewModel.getUserName())
            infoEmail.setVtbValue(viewModel.getEmail())
            infoDevice.setVtbValue(viewModel.getKeypassCode())

            edtPuk.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int,
                ) {
                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                    pukCode = edtPuk.text.toString()
                }

                override fun afterTextChanged(s: Editable?) {
                }
            })

            ivEye.setThrottleClickListener {
                isShowPIN = isShowPIN.not()
                edtPuk.setText(
                    if (isShowPIN) {
                        pukCode
                    } else {
                        "********"
                    },
                )
                ivEye.setImageResource(
                    if (isShowPIN) {
                        com.vietinbank.core_ui.R.drawable.ic_dangnhap_eye_open
                    } else {
                        com.vietinbank.core_ui.R.drawable.ic_dangnhap_eye_close
                    },
                )
            }

            tvGetPUK.setThrottleClickListener {
                activity?.openUrl(URL_SYNC)
            }

//            if (viewModel.getEmail().isEmpty()) {
//                rbEmail.isChecked = false
//                rbEmail.isClickable = false
//            }
//
//            if (viewModel.getPhone().isEmpty()) {
//                rbPhone.isChecked = false
//                rbPhone.isClickable = false
//            }

            rgMethod.setOnCheckedChangeListener { _, checkedId ->
                context?.let { ct ->
                    when (checkedId) {
                        R.id.rbEmail -> {
                            rbEmail.typeface = Utils.g().getFont(5, ct)
                            rbPhone.typeface = Utils.g().getFont(4, ct)
                        }

                        else -> {
                            rbPhone.typeface = Utils.g().getFont(5, ct)
                            rbEmail.typeface = Utils.g().getFont(4, ct)
                        }
                    }
                }
            }

            btnNext.setThrottleClickListener {
                if (pukCode.isEmpty()) {
                    showNoticeDialog("Mã PUK không được bỏ trống. Quý khách vui lòng kiểm tra lại")
                    return@setThrottleClickListener
                }
                viewModel.setKeypassPin(
                    if (rbEmail.isChecked) BY_EMAIL else BY_SMS,
                    pukCode,
                )
            }
        }
    }

    private fun initObserve() {
        viewModel.apply {
            forgotPinLiveData.observe(viewLifecycleOwner) {
                // update
                showNoticeDialog("Quý khách đã gửi yêu cầu cấp lại mã PIN Keypass thành công") {
                    val bundle = Bundle()
                    bundle.putBoolean(VSoftConstants.Bundle.KEY_DATA_1, true)
                    setFragmentResult(VSoftConstants.RESULT_UPDATE_KEYPASS, bundle)
                    onBackPressed()
                }
            }
        }
    }

    override fun onBackPressed(): Boolean {
        softNavigator.popBackStack()
        return true
    }
}
