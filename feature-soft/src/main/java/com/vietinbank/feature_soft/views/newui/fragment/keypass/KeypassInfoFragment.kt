package com.vietinbank.feature_soft.views.newui.fragment.keypass

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.fragment.app.viewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_soft.navigation.INewSoftNavigator
import com.vietinbank.feature_soft.views.newui.screen.KeypassInfoScreen
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@AndroidEntryPoint
class KeypassInfoFragment : BaseFragment<KeypassInfoViewModel>() {
    override val viewModel: KeypassInfoViewModel by viewModels()

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softNavigator: INewSoftNavigator

    override val useCompose: Boolean = true

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.uiState.collectAsStateWithLifecycle()
        KeypassInfoScreen(
            modifier = Modifier
                .fillMaxSize()
                .background(FDS.Colors.backgroundBgScreen)
                .systemBarsPadding()
                .imePadding(),
            uiState = uiState,
            onBack = {
                softNavigator.popBackStack()
            },
        )
    }
}