package com.vietinbank.feature_soft.views.newui.fragment.keypass

import androidx.compose.runtime.Stable
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class KeypassInfoViewModel @Inject constructor(
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val sessionManager: ISessionManager,
    override val ottSetupService: IOttSetupService,
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(KeypassInfoUiState())
    val uiState = _uiState.asStateFlow()

    init {
        _uiState.update {
            it.copy(
                keypassToken = userProf.getKeypassToken() ?: "",
                keypassCode = userProf.getKeypassCode() ?: "",
            )
        }
    }
}

@Stable
data class KeypassInfoUiState(
    val keypassToken: String = "",
    val keypassCode: String = "",
)