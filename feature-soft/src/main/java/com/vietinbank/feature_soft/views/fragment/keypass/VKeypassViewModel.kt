package com.vietinbank.feature_soft.views.fragment.keypass

import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.livedata.SingleLiveEvent
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_domain.models.soft.KeypassParams
import com.vietinbank.core_domain.models.soft.VSoftResponseParams
import com.vietinbank.core_domain.usecase.soft.SoftUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class VKeypassViewModel @Inject constructor(
    val softUseCase: SoftUseCase,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
) : BaseViewModel() {

    val forgotPinLiveData = SingleLiveEvent<VSoftResponseParams>()
    fun setKeypassPin(
        sendType: String,
        puk: String,
    ) = launchJob {
        val result = softUseCase.forgotKeypassPin(
            KeypassParams(
                sendType,
                puk,
                userProf.getUserName() ?: "",
            ),
        )
        handleResource<VSoftResponseParams>(result) { data ->
            forgotPinLiveData.postValue(data)
        }
    }

    fun getFullName() = userProf.getFullName() ?: ""
    fun getUserName() = userProf.getUserName() ?: ""
    fun getPhone() = userProf.getPhoneNo() ?: ""
    fun getEmail() = userProf.getEmail() ?: ""
    fun getKeypassToken() = userProf.getKeypassToken() ?: ""
    fun getKeypassCode() = userProf.getKeypassCode() ?: ""
}