package com.vietinbank.feature_soft.views.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.nav.ISoftNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_soft.R
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import com.vietinbank.feature_soft.databinding.VFragmentSoftHomeBinding
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class VSoftHomeFragment : BaseFragment<VSoftViewModel>() {
    @Inject
    lateinit var softNavigator: ISoftNavigator
    override val viewModel: VSoftViewModel by viewModels()

    @Inject
    override lateinit var appNavigator: IAppNavigator
    private var isActive = false
    override fun inflateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
    ): VFragmentSoftHomeBinding {
        return VFragmentSoftHomeBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        (binding as VFragmentSoftHomeBinding).apply {
            isActive = viewModel.softManager.isAppActive
            activeStatus.setImageResource(
                if (viewModel.softManager.isAppActive) {
                    com.vietinbank.core_ui.R.drawable.ic_toggle_on
                } else {
                    com.vietinbank.core_ui.R.drawable.ic_toggle_off
                },
            )
            vtbBtnSync.isVisible = isActive
            vtbBtnForgotPin.isVisible = isActive
            vtbBtnOffline.isVisible = isActive
            vtbBtnOnline.isVisible = isActive

            activeStatus.setThrottleClickListener {
                if (isActive) {
                    // tat smart otp
                    showConfirmDialog(
                        message = "Quý khách có chắc chắn muốn hủy xác thực giao dịch bằng Soft OTP?",
                        positiveButtonText = "Có",
                        negativeButtonText = "Không",
                        positiveAction = {
                            softNavigator.goToEnterPIN(
                                VSoftConstants.Bundle.KEY_FLOW_OTP to VSoftConstants.VtpOTPFlowType.BLOCK.name,
                            )
                        },
                    )
                } else if (viewModel.softManager.isAppActive) {
                    // kich hoat user t2
                    softNavigator.goToEnterPIN(
                        VSoftConstants.Bundle.KEY_FLOW_OTP to VSoftConstants.VtpOTPFlowType.ACTIVE.name,
                    )
                } else {
                    // kich hoat lan dau
                    softNavigator.goToActiveSoft()
                }
            }

            // luong dong bo smart otp
            vtbBtnSync.setThrottleClickListener {
                softNavigator.goToEnterPIN(
                    VSoftConstants.Bundle.KEY_FLOW_OTP to VSoftConstants.VtpOTPFlowType.SYNC.name,
                )
            }

            // luong quen pin
            vtbBtnForgotPin.setThrottleClickListener {
                context?.let { it1 ->
                    showConfirmDialog(
                        message =
                        "Quý khách sẽ yêu cầu kích hoạt lại tất cả các user phê duyệt. Quý khách có muốn tiếp tục?",
                        positiveButtonText = "Tiếp tục",
                        negativeButtonText = "Hủy",
                        positiveAction = {
                            softNavigator.goToActiveSoft(
                                VSoftConstants.Bundle.KEY_FLOW_OTP to VSoftConstants.VtpOTPFlowType.FORGOT_PIN.name,
                            )
                        },
                        negativeAction = {},
                    )
                }
            }

            // luong offline qr + eSigner
            vtbBtnOffline.setThrottleClickListener {
                softNavigator.goToEnterPIN(
                    VSoftConstants.Bundle.KEY_FLOW_OTP to VSoftConstants.VtpOTPFlowType.GET_OTP.name,
                )
            }

            // luong online
            vtbBtnOnline.setThrottleClickListener {
                softNavigator.goToEnterPIN(
                    VSoftConstants.Bundle.KEY_FLOW_OTP to VSoftConstants.VtpOTPFlowType.CONFIRM_IN_EFAST.name,
                )
            }
        }
    }
}