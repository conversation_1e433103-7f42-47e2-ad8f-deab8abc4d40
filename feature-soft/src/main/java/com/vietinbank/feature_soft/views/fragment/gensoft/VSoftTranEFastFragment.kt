package com.vietinbank.feature_soft.views.fragment.gensoft

import android.os.Bundle
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.nav.ISoftNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_soft.R
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import com.vietinbank.feature_soft.databinding.VFragmentSoftAuthenBinding
import com.vietinbank.feature_soft.views.fragment.VSoftViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class VSoftTranEFastFragment : BaseFragment<VSoftViewModel>() {
    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softNavigator: ISoftNavigator
    override val viewModel: VSoftViewModel by viewModels()

    // count time otp
    private var countDownTimer: CountDownTimer? = null
    private var transactionId = ""
    private var messageId = ""
    override fun inflateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
    ): VFragmentSoftAuthenBinding {
        return VFragmentSoftAuthenBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initObserve()
    }

    fun initView() {
        transactionId = arguments?.getString(VSoftConstants.Bundle.KEY_DATA_1) ?: ""
        messageId = arguments?.getString(VSoftConstants.Bundle.KEY_DATA_2) ?: ""
        viewModel.checkTranEfast(transactionId, messageId)
        (binding as VFragmentSoftAuthenBinding).apply {
            headerView.setVtbOnBackPressed {
                onBackPressed()
            }

            btnNext.setThrottleClickListener {
                val bundle = Bundle()
                bundle.putString(VSoftConstants.Bundle.KEY_DATA_1, edtSoft.text.toString())
                setFragmentResult(VSoftConstants.RESULT_CONFIRM_OTP, bundle)
                onBackPressed()
            }

            btnCancel.setThrottleClickListener {
                onBackPressed()
            }
        }
    }

    fun initObserve() {
        viewModel.apply {
            sdkCheckStatusCodeLD.observe(viewLifecycleOwner) {
                // hien thị loi
                showNoticeDialog(VSoftConstants.getErrorMessage(it)) {
                    onBackPressed()
                }
            }

            sdkCheckStatusLD.observe(viewLifecycleOwner) {
                // thong bao loi: chua kich hoat smart otp
                // dieu huong di kich hoat smart otp
                showNoticeDialog("Chưa kích hoạt smart otp") {
                    softNavigator.goToActiveSoft()
                }
            }

            // ma otp
            sdkCrotpWithTransactionInfoLD.observe(viewLifecycleOwner) {
                // step 18 -> 19
                startCoolDown()
                (binding as VFragmentSoftAuthenBinding).edtSoft.setText(it)
            }
        }
    }

    override fun onBackPressed(): Boolean {
        countDownTimer?.cancel()
        countDownTimer = null
        softNavigator.popBackGraph()
        return true
    }

    private fun startCoolDown() {
        try {
            countDownTimer?.cancel()
            countDownTimer = null
            countDownTimer =
                object : CountDownTimer((VSoftConstants.COUNT_OTP_TIME * 1000).toLong(), 10) {
                    override fun onTick(timeLeft: Long) {
                        if (isAdded && isVisible) {
                            (binding as VFragmentSoftAuthenBinding).tvCountTime.text =
                                getString(
                                    R.string.vtb_ra_authen_soft_time,
                                    (timeLeft / 1000).toString(),
                                )
                        }
                    }

                    override fun onFinish() {
                        viewModel.sdkTransInfoModel?.let {
                            viewModel.sdkGetCRotpWithTransactionInfo(
                                it.transactionData,
                                it.challenge,
                            )
                        }
                    }
                }.start()
        } catch (_: Exception) {
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        countDownTimer?.cancel()
        countDownTimer = null
    }
}