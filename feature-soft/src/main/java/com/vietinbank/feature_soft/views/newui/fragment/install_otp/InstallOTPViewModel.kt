package com.vietinbank.feature_soft.views.newui.fragment.install_otp

import androidx.compose.runtime.Stable
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_domain.usecase.soft.InstallOTPUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.OneTimeEvent
import com.vietinbank.core_ui.components.foundation.pin.PinState
import com.vietinbank.feature_soft.views.newui.fragment.install_otp.InstallOTPUIState.Companion.OTP_LENGTH
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class InstallOTPViewModel @Inject constructor(
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val sessionManager: ISessionManager,
    override val ottSetupService: IOttSetupService,
    private val installOTPUseCase: InstallOTPUseCase,
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(InstallOTPUIState())
    val uiState = _uiState.asStateFlow()

    fun onInputPinChange(pinValue: String) {
        with(_uiState) {
            update {
                it.copy(
                    inputPinValue = pinValue,
                    enableNextButton = false,
                )
            }
            if (value.reinputPinValue.length == OTP_LENGTH &&
                value.inputPinValue.length == OTP_LENGTH
            ) {
                update {
                    it.copy(enablePinInput = false)
                }
                validateOTP()
            }
        }
    }

    fun onReInputPinChange(pinValue: String) {
        with(_uiState) {
            update {
                it.copy(
                    reinputPinValue = pinValue,
                    enableNextButton = false,
                )
            }
            if (value.reinputPinValue.length == OTP_LENGTH) {
                update {
                    it.copy(enablePinInput = false)
                }
                validateOTP()
            }
        }
    }

    private fun validateOTP() = launchJobSilent {
        with(_uiState) {
            if (value.inputPinValue == value.reinputPinValue) {
                update {
                    it.copy(
                        enableNextButton = true,
                        enablePinInput = true,
                    )
                }
            } else {
                update {
                    it.copy(
                        reinputPinState = PinState.ERROR,
                    )
                }

                // Delay 1 khoảng trước khi cho user nhập lại
                delay(1200)

                update {
                    it.copy(
                        reinputPinValue = "",
                        reinputPinState = PinState.INPUT,
                        enablePinInput = true,
                    )
                }
            }
        }
    }

    fun onInstallPin() {
        // Luồng 2.1.1 Kích hoạt Vietinbank OTP của MK doc
        launchJob {
            handleResource(
                // Thực hiện call đến setPin của SDK và lưu lại tokenSN vào local DB
                // step 12.1 setPin -> getTokenSn
                installOTPUseCase(
                    pin = _uiState.value.inputPinValue,
                    userId = userProf.getKeypassProfile(),
                ),
            ) {
                appConfig.setSoftOTPToken(it.token)
                sendEvent((InstallOTPEvent.OnInstallOTPSuccess))
            }
        }
    }
}

@Stable
data class InstallOTPUIState(
    val inputPinState: PinState = PinState.INPUT,
    val inputPinValue: String = "",
    val reinputPinState: PinState = PinState.INPUT,
    val reinputPinValue: String = "",
    val enablePinInput: Boolean = true,
    val enableNextButton: Boolean = false,
) {
    companion object {
        const val OTP_LENGTH = 6
    }
}

internal sealed interface InstallOTPEvent : OneTimeEvent {
    object OnInstallOTPSuccess : InstallOTPEvent
}