package com.vietinbank.feature_soft.views.fragment.keypass

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.setFragmentResultListener
import androidx.fragment.app.viewModels
import androidx.viewbinding.ViewBinding
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.extensions.openUrl
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.nav.ISoftNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import com.vietinbank.feature_soft.databinding.VFragmentKeypassMainBinding
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class VKeypassMainFragment : BaseFragment<VKeypassViewModel>() {
    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softNavigator: ISoftNavigator
    override val viewModel: VKeypassViewModel by viewModels()

    override fun inflateViewBinding(inflater: LayoutInflater, container: ViewGroup?): ViewBinding {
        return VFragmentKeypassMainBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
    }

    private fun initView() {
        (binding as VFragmentKeypassMainBinding).apply {
            rlSync.setThrottleClickListener {
                activity?.openUrl(VSoftConstants.URL_SYNC)
            }

            rlForgot.setThrottleClickListener { softNavigator.goToKeypassForgotPin() }

            rlInfo.setThrottleClickListener { softNavigator.goToKeypassInfo() }

            setFragmentResultListener(VSoftConstants.RESULT_UPDATE_KEYPASS) { _, bundle ->
                if (bundle.getBoolean(VSoftConstants.Bundle.KEY_DATA_1, false)) {
                    // update status
                }
            }
        }
    }
}