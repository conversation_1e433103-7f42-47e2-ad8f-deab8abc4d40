package com.vietinbank.feature_soft.views.fragment.pin

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.extensions.focusAndShowKeyboard
import com.vietinbank.core_common.extensions.hideKeyboard
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.nav.ISoftNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_soft.R
import com.vietinbank.feature_soft.databinding.VFragmentSoftSetPinBinding
import com.vietinbank.feature_soft.views.fragment.VSoftViewModel
import dagger.hilt.android.AndroidEntryPoint
import vn.mk.token.sdk.SdkNative
import javax.inject.Inject

@AndroidEntryPoint
class VSoftSetPinFragment : BaseFragment<VSoftViewModel>() {
    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softNavigator: ISoftNavigator
    override val viewModel: VSoftViewModel by viewModels()

    private var isShowPIN = false
    private var isShowPINRe = false
    override fun inflateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
    ): VFragmentSoftSetPinBinding {
        return VFragmentSoftSetPinBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initObserve()
    }

    fun initView() {
        (binding as VFragmentSoftSetPinBinding).apply {
            headerView.setVtbOnBackPressed {
                onBackPressed()
            }
            inputPin.focusAndShowKeyboard()

            // pin
            inputPin.setVtbShowOTP(isShowPIN)
            ivEyeInput.setThrottleClickListener {
                isShowPIN = !isShowPIN
                inputPin.setVtbShowOTP(isShowPIN)
                ivEyeInput.setImageResource(
                    if (isShowPIN) {
                        R.drawable.v_soft_ic_hide_eye
                    } else {
                        R.drawable.v_soft_ic_show_eye
                    },
                )
            }

            // re pin
            inputPinRe.setVtbShowOTP(isShowPINRe)
            ivEyeReInput.setThrottleClickListener {
                isShowPINRe = !isShowPINRe
                inputPinRe.setVtbShowOTP(isShowPINRe)
                ivEyeReInput.setImageResource(
                    if (isShowPINRe) {
                        R.drawable.v_soft_ic_hide_eye
                    } else {
                        R.drawable.v_soft_ic_show_eye
                    },
                )
            }

            inputPin.onTextChange = {
                if (inputPin.text.toString().length == 6 && inputPinRe.text.toString().length < 6) {
                    inputPinRe.focusAndShowKeyboard()
                }
            }

            inputPinRe.onTextChange = {
                if (inputPinRe.text.toString().length == 6) {
                    inputPinRe.hideKeyboard()
                }
            }

            btnNext.setThrottleClickListener {
                if (inputPin.text.toString().isEmpty() || inputPinRe.text.toString().isEmpty()) {
                    showNoticeDialog("Mã PIN không được bỏ trống. Quý khách vui lòng kiểm tra lại")
                } else if (inputPin.text.toString().length < 6 || inputPinRe.text.toString().length < 6) {
                    showNoticeDialog("Mã PIN phải bao gồm 6 ký tự số. Quý khách vui lòng kiểm tra lại")
                } else if (inputPin.text.toString() != inputPinRe.text.toString()) {
                    showNoticeDialog("Mã PIN không khớp. Quý khách vui lòng kiểm tra lại")
                } else {
                    SdkNative.getInstance().setPin(inputPin.text.toString())
                    SdkNative.getInstance().setSelectedUserId(viewModel.getKeypassProfile())
                    viewModel.sdkGetTokenSn()
                }
            }
        }
    }

    fun initObserve() {
        viewModel.apply {
            sdkGetTokenSnLD.observe(viewLifecycleOwner) {
                viewModel.updateStatusSoft(true)
                viewModel.softManager.isAppActive = true
                viewModel.softManager.isUserActive = true
                showNoticeDialog("Quý khách đã kích hoạt xác thực giao dịch bằng Soft OTP thành công") {
                    softNavigator.popBackGraph()
                }
            }
        }
    }

    override fun onBackPressed(): Boolean {
        softNavigator.popToActiveSoft()
        return true
    }
}