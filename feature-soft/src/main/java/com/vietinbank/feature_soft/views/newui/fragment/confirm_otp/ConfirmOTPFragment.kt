package com.vietinbank.feature_soft.views.newui.fragment.confirm_otp

import android.os.Bundle
import android.view.View
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.fragment.app.viewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.vietinbank.core_common.environment.IEnvironmentProvider
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.core_ui.utils.eFastBackgroundLevel2
import com.vietinbank.feature_soft.R
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import com.vietinbank.feature_soft.common.listenSoftOtpDialogResult
import com.vietinbank.feature_soft.navigation.INewSoftNavigator
import com.vietinbank.feature_soft.views.newui.dialog.SoftOTPDialogType
import com.vietinbank.feature_soft.views.newui.dialog.SoftOTPNoticeDialog
import com.vietinbank.feature_soft.views.newui.fragment.active.ActiveSoftOTPFlow
import com.vietinbank.feature_soft.views.newui.screen.ConfirmOTPScreen
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class ConfirmOTPFragment : BaseFragment<ConfirmOTPViewModel>() {
    override val viewModel: ConfirmOTPViewModel by viewModels()

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softNavigator: INewSoftNavigator

    @Inject
    lateinit var envProvider: IEnvironmentProvider

    override val useCompose: Boolean = true

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)
        if (viewModel.confirmOTPFlow == ConfirmOTPFlow.VERIFY_ACTIVE_FIRST ||
            viewModel.confirmOTPFlow == ConfirmOTPFlow.VERIFY_ACTIVE_SECOND &&
            envProvider.isBuildConfigDebug()
        ) {
            showNoticeDialog(
                "otp: ${arguments?.getString(VSoftConstants.Bundle.KEY_DATA_1)}",
            )
        }

        listenSoftOtpDialogResult(
            onPositiveAction = {
                when (viewModel.confirmOTPFlow) {
                    ConfirmOTPFlow.TOGGLE,
                    ConfirmOTPFlow.SYNC,
                    -> {
                        softNavigator.popBackStack()
                    }
                    ConfirmOTPFlow.VERIFY_OTP_SECOND_USER,
                    -> {
                        softNavigator.goToTermOfUse(
                            VSoftConstants.Bundle.KEY_FLOW_OTP
                                to ActiveSoftOTPFlow.FORGOT.name,
                        )
                    }

                    else -> {
                        softNavigator.popOutOfFlow()
                    }
                }
            },
        )
    }

    @Composable
    override fun ComposeScreen() {
        val confirmOTPFlow = viewModel.confirmOTPFlow
        val confirmDoneNoticeDialogParam = SoftOTPNoticeDialog.buildBundle(
            message = when (confirmOTPFlow) {
                ConfirmOTPFlow.VERIFY_ACTIVE_FIRST -> null // Go to set pin, doesn't show dialog
                ConfirmOTPFlow.VERIFY_ACTIVE_SECOND -> stringResource(R.string.install_otp_dialog_success)
                ConfirmOTPFlow.TOGGLE -> stringResource(R.string.toggle_otp_dialog_success_message)
                ConfirmOTPFlow.SYNC -> stringResource(R.string.sync_otp_dialog_success_message)
                ConfirmOTPFlow.VERIFY_OTP_SECOND_USER -> null // Go to active info, doesn't show dialog
            },
            type = SoftOTPDialogType.SUCCESS,
        )

        val uiState by viewModel.uiState.collectAsStateWithLifecycle()
        handleSingleEvent {
            when (val event = it as ConfirmInstallOTPEvent) {
                ConfirmInstallOTPEvent.OnVerifyPinBlocked -> {
                    softNavigator.goToOTPNoticeDialog(
                        SoftOTPNoticeDialog.buildBundle(
                            message = resources.getString(R.string.confirm_otp_dialog_locked_message),
                            type = SoftOTPDialogType.ERROR,
                            positiveButtonText = resources.getString(R.string.confirm_otp_dialog_locked_button),
                        ),
                    )
                }

                is ConfirmInstallOTPEvent.OnUnKnownSDKError -> {
                    showNoticeDialog(
                        message = event.message
                            ?: resources.getString(com.vietinbank.core_ui.R.string.error_at_server),
                    )
                }
            }
        }
        AppTheme {
            ConfirmOTPScreen(
                modifier = Modifier
                    .fillMaxSize()
                    .eFastBackgroundLevel2()
                    .systemBarsPadding()
                    .imePadding(),
                uiState = uiState,
                confirmOTPFlow = confirmOTPFlow,
                onPinChange = viewModel::onInputChange,
                onNext = {
                    when (confirmOTPFlow) {
                        ConfirmOTPFlow.VERIFY_ACTIVE_FIRST -> {
                            softNavigator.goToInstallOTP()
                        }

                        ConfirmOTPFlow.VERIFY_OTP_SECOND_USER -> {
                            softNavigator.goToNewActiveSoft(
                                VSoftConstants.Bundle.KEY_FLOW_OTP
                                    to ActiveSoftOTPFlow.SECOND.name,
                            )
                        }

                        else -> {
                            softNavigator.goToOTPNoticeDialog(
                                confirmDoneNoticeDialogParam,
                            )
                        }
                    }
                },
                onCTA = {
                    when (confirmOTPFlow) {
                        // Gửi lại mã kích hoạt
                        ConfirmOTPFlow.VERIFY_ACTIVE_FIRST,
                        ConfirmOTPFlow.VERIFY_ACTIVE_SECOND,
                        -> {
                            softNavigator.popBackStack()
                        }

                        // Quên pin
                        ConfirmOTPFlow.TOGGLE,
                        ConfirmOTPFlow.SYNC,
                        ConfirmOTPFlow.VERIFY_OTP_SECOND_USER,
                        -> {
                            softNavigator.goToOTPNoticeDialog(
                                SoftOTPNoticeDialog.buildBundle(
                                    message = resources.getString(R.string.forgot_otp_dialog_message),
                                    type = SoftOTPDialogType.WARNING,
                                    isShowNegativeButton = true,
                                ),
                            )
                        }
                    }
                },
                onBack = { softNavigator.popBackStack() },
            )
        }
    }
}

// Xác định luồng verify OTP. Truyền vào bundle khi navigate qua key "KEY_FLOW_OTP"
enum class ConfirmOTPFlow {
    VERIFY_ACTIVE_FIRST, // Verìy SMS Code user thứ nhất
    VERIFY_ACTIVE_SECOND, // Verìy SMS Code user thứ hai
    VERIFY_OTP_SECOND_USER, // Verìy Soft OTP user thứ hai trước khi request active
    TOGGLE, // Tắt OTP
    SYNC, // Đồng bộ
}