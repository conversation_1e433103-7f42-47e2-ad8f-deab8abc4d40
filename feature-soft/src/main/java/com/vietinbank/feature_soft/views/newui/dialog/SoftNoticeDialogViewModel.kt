package com.vietinbank.feature_soft.views.newui.dialog

import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

@HiltViewModel
class SoftNoticeDialogViewModel @Inject constructor(
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val sessionManager: ISessionManager,
    override val ottSetupService: IOttSetupService,
) : BaseViewModel() {
    private val _timeLeft = MutableStateFlow(Pair("0", "0"))
    val timeLeft: StateFlow<Pair<String, String>> = _timeLeft.asStateFlow()

    fun startCountdown() = launchJobSilent {
        val targetMillis = appConfig.getLockSoftEndTime()
        while (true) {
            val now = System.currentTimeMillis()
            val diff = (targetMillis - now).coerceAtLeast(0)

            val minutes = (diff / 1000) / 60
            val seconds = (diff / 1000) % 60
            _timeLeft.value = Pair(
                "%02d".format(minutes),
                "%02d".format(seconds),
            )
            if (diff == 0L) break
            delay(1000)
        }
    }
}