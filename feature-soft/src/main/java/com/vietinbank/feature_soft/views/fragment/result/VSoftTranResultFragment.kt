package com.vietinbank.feature_soft.views.fragment.result

import android.os.Bundle
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.extensions.copyText
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.nav.ISoftNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_soft.R
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import com.vietinbank.feature_soft.databinding.VFragmentSoftResultBinding
import com.vietinbank.feature_soft.views.fragment.VSoftViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class VSoftTranResultFragment : BaseFragment<VSoftViewModel>() {
    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softNavigator: ISoftNavigator
    override val viewModel: VSoftViewModel by viewModels()
    private var transactionData = ""
    private var challenge = ""
    override fun inflateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
    ): VFragmentSoftResultBinding {
        return VFragmentSoftResultBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initObserve()
    }

    fun initView() {
        transactionData = arguments?.getString(VSoftConstants.Bundle.KEY_DATA_1) ?: ""
        challenge = arguments?.getString(VSoftConstants.Bundle.KEY_DATA_2) ?: ""

        (binding as VFragmentSoftResultBinding).apply {
            infoRef.setVtbValue(challenge)
            infoSum.setVtbValue(transactionData)

            tvCopyOTP.copyText(inputOTP.text.toString(), getString(R.string.v_soft_copied))

            btnNext.setThrottleClickListener {
                softNavigator.popBackGraph()
            }

            onTimerTick = {
                tvTime.text =
                    "Mã OTP sẽ cập nhật sau ${(it / 1000)}"
            }

            onTimerFinished = {
                viewModel.sdkGetCRotpWithTransactionInfo(
                    transactionData,
                    challenge,
                )
            }
        }

        viewModel.sdkGetCRotpWithTransactionInfo(transactionData, challenge)
    }

    fun initObserve() {
        viewModel.sdkCrotpWithTransactionInfoLD.observe(viewLifecycleOwner) {
            // step 18+19+20
            (binding as VFragmentSoftResultBinding).inputOTP.setText(it)
            startCoolDown()
        }
    }

    // count time otp
    private var countDownTimer: CountDownTimer? = null
    var onTimerFinished: (() -> Unit)? = null
    var onTimerTick: ((Long) -> Unit)? = null

    private fun startCoolDown() {
        try {
            countDownTimer?.cancel()
            countDownTimer = null
            countDownTimer =
                object : CountDownTimer((VSoftConstants.COUNT_OTP_TIME * 1000).toLong(), 10) {
                    override fun onTick(timeLeft: Long) {
                        if (isAdded && isVisible) {
                            onTimerTick?.invoke(timeLeft)
                        }
                    }

                    override fun onFinish() {
                        onTimerFinished?.invoke()
                    }
                }
            countDownTimer?.start()
        } catch (_: Exception) {
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        countDownTimer?.cancel()
        countDownTimer = null
    }
}