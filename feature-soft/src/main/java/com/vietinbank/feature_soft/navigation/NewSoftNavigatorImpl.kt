package com.vietinbank.feature_soft.navigation

import android.os.Bundle
import android.os.Parcelable
import androidx.navigation.NavController
import androidx.navigation.NavOptions
import com.vietinbank.core_common.R
import com.vietinbank.core_common.utils.Utils
import java.io.Serializable
import javax.inject.Inject

class NewSoftNavigatorImpl @Inject constructor(
    private val navController: NavController,
) : INewSoftNavigator {
    private fun createSlideNavOptions(): NavOptions {
        val builder = NavOptions.Builder()
            .setEnterAnim(R.anim.core_slide_in_right)
            .setExitAnim(R.anim.core_slide_out_left)
            .setPopEnterAnim(R.anim.core_slide_in_left)
            .setPopExitAnim(R.anim.core_slide_out_right)
        return builder.build()
    }

    private fun createBundle(vararg pair: Pair<String, Any?>): Bundle {
        val bundle = Bundle()
        pair.forEach {
            val first = it.first
            val second = it.second
            second?.let {
                when (second) {
                    is Boolean -> bundle.putBoolean(first, second)
                    is Byte -> bundle.putByte(first, second)
                    is Short -> bundle.putShort(first, second)
                    is Int -> bundle.putInt(first, second)
                    is Long -> bundle.putLong(first, second)
                    is Float -> bundle.putFloat(first, second)
                    is Double -> bundle.putDouble(first, second)
                    is Char -> bundle.putChar(first, second)
                    is String -> bundle.putString(first, second)
                    is CharSequence -> bundle.putCharSequence(first, second)
                    is Parcelable -> bundle.putParcelable(first, second)
                    is Serializable -> bundle.putSerializable(first, second)
                    else -> bundle.putString(first, Utils.g().provideGson().toJson(second))
                }
            }
        }
        return bundle
    }

    override fun popBackStack() {
        navController.popBackStack()
    }

    override fun popToNewActiveSoft() {
        navController.popBackStack(
            com.vietinbank.feature_soft.R.id.activeSoftOTPFragment,
            false,
        )
    }

    override fun popOutOfFlow() {
        navController.popBackStack(com.vietinbank.feature_soft.R.id.soft_otp_nav_graph, false)
    }

    override fun goToTermOfUse(vararg pair: Pair<String, Any?>) {
        val navOptions = createSlideNavOptions()
        navController.navigate(
            com.vietinbank.feature_soft.R.id.termSoftOTPFragment,
            createBundle(*pair),
            navOptions,
        )
    }

    override fun goToNewActiveSoft(vararg pair: Pair<String, Any?>) {
        val navOptions = createSlideNavOptions()
        navController.navigate(
            com.vietinbank.feature_soft.R.id.activeSoftOTPFragment,
            createBundle(*pair),
            navOptions,
        )
    }

    override fun goToConfirmOTP(vararg pair: Pair<String, Any?>) {
        val navOptions = createSlideNavOptions()
        navController.navigate(
            com.vietinbank.feature_soft.R.id.confirmOTPFragment,
            createBundle(*pair),
            navOptions,
        )
    }

    override fun goToInstallOTP(vararg pair: Pair<String, Any?>) {
        val navOptions = createSlideNavOptions()
        navController.navigate(
            com.vietinbank.feature_soft.R.id.installOTPFragment,
            createBundle(*pair),
            navOptions,
        )
    }

    override fun goToOTPNoticeDialog(bundle: Bundle) {
        val navOptions = createSlideNavOptions()
        navController.navigate(
            com.vietinbank.feature_soft.R.id.softOTPNoticeDialog,
            bundle,
            navOptions,
        )
    }
}