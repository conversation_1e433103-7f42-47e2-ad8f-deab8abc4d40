package com.vietinbank.feature_soft.navigation

import android.os.Bundle

interface INewSoftNavigator {
    fun popBackStack()
    fun popToNewActiveSoft()
    fun popOutOfFlow()
    fun goToTermOfUse(vararg pair: Pair<String, Any?>)
    fun goToNewActiveSoft(vararg pair: Pair<String, Any?>)
    fun goToConfirmOTP(vararg pair: Pair<String, Any?>)
    fun goToInstallOTP(vararg pair: Pair<String, Any?>)
    fun goToOTPNoticeDialog(bundle: Bundle)
}