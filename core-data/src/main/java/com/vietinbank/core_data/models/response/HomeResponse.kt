package com.vietinbank.core_data.models.response

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_data.models.base.Status

/**
 * Created by vandz on 5/3/25.
 */

data class RegisterTouchIDResponseData(
    @SerializedName("status")
    val status: Status = Status(code = "", message = "", subCode = ""),
    @SerializedName("sessionId")
    val sessionId: String,
    @SerializedName("requestId")
    val requestId: String,
    @SerializedName("touchIDToken")
    val touchIDToken: String,
)
