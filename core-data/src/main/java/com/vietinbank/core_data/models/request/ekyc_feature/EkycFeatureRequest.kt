package com.vietinbank.core_data.models.request.ekyc_feature

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_data.models.request.CommonRequest

class EkycOcrRequest(
    @SerializedName("imageBack") val imageBack: String,
    @SerializedName("imageFront") val imageFront: String,
    @SerializedName("username") val username: String,
    @SerializedName("type") val type: String,
    @SerializedName("userType") val userType: String,
    @SerializedName("onboardingId") val onboardingId: String = "",
    @SerializedName("eKycId") val eKycId: String = "",
) : CommonRequest(Constants.MB_EKYC_OCR)

class EkycNFCRequest(
    @SerializedName("userType") val userType: String,
    @SerializedName("aAResult") val aAResult: String,
    @SerializedName("challenge") val challenge: String,
    @SerializedName("dg1") val dg1: String,
    @SerializedName("dg2") val dg2: String,
    @SerializedName("dg3") val dg3: String,
    @SerializedName("dg4") val dg4: String,
    @SerializedName("dg5") val dg5: String,
    @SerializedName("dg6") val dg6: String,
    @SerializedName("dg7") val dg7: String,
    @SerializedName("dg8") val dg8: String,
    @SerializedName("dg9") val dg9: String,
    @SerializedName("dg10") val dg10: String,
    @SerializedName("dg11") val dg11: String,
    @SerializedName("dg12") val dg12: String,
    @SerializedName("dg13") val dg13: String,
    @SerializedName("dg14") val dg14: String,
    @SerializedName("dg15") val dg15: String,
    @SerializedName("dg16") val dg16: String,
    @SerializedName("eACCAResult") val eACCAResult: String,
    @SerializedName("sod") val sod: String,
    @SerializedName("username") val username: String,
    @SerializedName("eKycId") val eKycId: String,
    @SerializedName("type") val type: String,
) : CommonRequest(Constants.MB_EKYC_NFC)

class EkycConfirmInputRequest(
    @SerializedName("eKycId") val eKycId: String,
    @SerializedName("username") val username: String,
    @SerializedName("images") val images: List<ImageLivenessItemRequest>,
) : CommonRequest(Constants.MB_EKYC_CONFIRM_INPUT)

class ImageLivenessItemRequest(
    @SerializedName("image") val image: String?,
    @SerializedName("time") val time: String? = "",
)
class EkycCompareImageRequest(
    @SerializedName("eKycId") val eKycId: String?,
    @SerializedName("username") val username: String? = "",
    @SerializedName("image") val image: String? = "",
) : CommonRequest(Constants.MB_EKYC_COMPARE_IMAGE)
