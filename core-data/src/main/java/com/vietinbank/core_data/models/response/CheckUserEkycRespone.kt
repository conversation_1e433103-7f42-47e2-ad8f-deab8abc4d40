package com.vietinbank.core_data.models.response

import com.google.gson.annotations.SerializedName

data class CheckUserEkycRespone(
    @SerializedName("bioStat")
    val bioStat: String?,
    @SerializedName("biometricFace")
    val biometricFace: String?,
    @SerializedName("ekycFlag")
    val ekycFlag: String?,
    @SerializedName("messagePopup")
    val messagePopup: String?,
    @SerializedName("retail")
    val retail: String?,
    @SerializedName("national")
    val national: String?,
    @SerializedName("statusAccountHolder")
    val statusAccountHolder: String?,
)

data class GetBiometricFaceResponse(
    @SerializedName("image")
    val image: String?,
    @SerializedName("requestId")
    val requestId: String?,
    @SerializedName("sessionId")
    val sessionId: String?,
    @SerializedName("status")
    val status: StatusUpdateEkyc?,
)