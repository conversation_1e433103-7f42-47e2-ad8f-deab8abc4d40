package com.vietinbank.core_data.models.ott

import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import java.lang.reflect.Type

/**
 * Created by vandz on 12/6/25.
 */
class StatusDeserializer : JsonDeserializer<Int> {
    override fun deserialize(
        json: JsonElement?,
        typeOfT: Type?,
        context: JsonDeserializationContext?,
    ): Int {
        return when (json?.asString?.uppercase()) {
            "SENT" -> 0 // Đ<PERSON> gửi
            "UNREAD" -> 1 // <PERSON><PERSON>a đọc
            "READ" -> 2 // <PERSON><PERSON> đọc
            "FAILED" -> 3 // G<PERSON>i lỗi
            else -> 0 // Default to SENT
        }
    }
}