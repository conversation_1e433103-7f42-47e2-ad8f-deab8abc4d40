package com.vietinbank.core_data.models.request.ott_feature

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_data.helper.RequestManager
import com.vietinbank.core_data.models.request.ott.OttCommonRequest

class OttStatusUpdateRequest(
    @SerializedName("userName")
    val userName: String? = "",
    @SerializedName("cifNo")
    val cifNo: String? = RequestManager.getUserProf().getCifNo() ?: "",
    @SerializedName("status")
    val status: List<String>? = listOf(),
    @SerializedName("channelType")
    val channelType: String?,
) : OttCommonRequest() {
    init {
        targetPath = Constants.MB_OTT_STATUS_UPDATE
    }
}