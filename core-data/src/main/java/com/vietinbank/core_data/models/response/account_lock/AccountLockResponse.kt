package com.vietinbank.core_data.models.response.account_lock

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_data.models.response.ApproverDomains

data class AccountLockResponse(
    @SerializedName("listChecker") val listChecker: List<ApproverDomains>?,
    @SerializedName("isPresent") val present: String?,
    @SerializedName("ekycFlag") val ekycFlag: String?,
    @SerializedName("phoneNumber") val phoneNumber: String?,
    @SerializedName("username") val username: String?,
    @SerializedName("cifNo") val cifNo: String?,
    @SerializedName("idCard") val idCard: String?,
    @SerializedName("accountPayment") val accountPayment: String?,
    @SerializedName("email") val email: String?,
)

data class AccountLockOTPResponse(
    @SerializedName("listChecker") val listChecker: List<ApproverDomains>?,
    @SerializedName("isPresent") val present: String?,
    @SerializedName("ekycFlag") val ekycFlag: String?,
    @SerializedName("phoneNumber") val phoneNumber: String?,
    @SerializedName("username") val username: String?,
    @SerializedName("cifNo") val cifNo: String?,
    @SerializedName("idCard") val idCard: String?,
    @SerializedName("accountPayment") val accountPayment: String?,
    @SerializedName("email") val email: String?,
    @SerializedName("isMatch") val match: String?,
    @SerializedName("exist") val exist: String?,
    @SerializedName("createdDate") val createdDate: String?,
)
