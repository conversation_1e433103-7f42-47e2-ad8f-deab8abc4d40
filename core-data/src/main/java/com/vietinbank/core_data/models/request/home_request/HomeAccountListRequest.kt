package com.vietinbank.core_data.models.request.home_request

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_data.models.request.CommonRequest

data class HomeAccountListRequest(
    @SerializedName("username")
    val username: String = "",
    @SerializedName("isBalance")
    val isBalance: String = "",
) : CommonRequest(Constants.MB_HOME_ACCOUNT_LIST)