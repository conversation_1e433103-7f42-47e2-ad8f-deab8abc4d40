package com.vietinbank.core_data.models.response

import com.google.gson.annotations.SerializedName

data class NextStatusTransactionByRuleResponse(
    @SerializedName("nextApprovers") val nextApprovers: MutableList<ApproverDomains>? = mutableListOf(),
    @SerializedName("statusTrans") val statusTrans: String?,
)

data class ApproverDomains(
    @SerializedName("approverlevel") val approverlevel: String?,
    @SerializedName("birthdate") val birthdate: String?,
    @SerializedName("email") val email: String?,
    @SerializedName("endate") val endate: String?,
    @SerializedName("enterpriseid") val enterpriseid: String?,
    @SerializedName("faillogon") val faillogon: String?,
    @SerializedName("fullname") val fullname: String?,
    @SerializedName("gender") val gender: String?,
    @SerializedName("grouptype") val grouptype: String?,
    @SerializedName("id") val id: String?,
    @SerializedName("idnumber") val idnumber: String?,
    @SerializedName("idtype") val idtype: String?,
    @SerializedName("keypassid") val keypassid: String?,
    @SerializedName("keypassprofile") val keypassprofile: String?,
    @SerializedName("keypasssoftotp") val keypasssoftotp: String?,
    @SerializedName("keypasswaitactive") val keypasswaitactive: String?,
    @SerializedName("lastlogon") val lastlogon: String?,
    @SerializedName("mailalert") val mailalert: String?,
    @SerializedName("mobile") val mobile: String?,
    @SerializedName("nationality") val nationality: String?,
    @SerializedName("pStatus") val pStatus: String?,
    @SerializedName("password") val password: String?,
    @SerializedName("rsaprofile") val rsaprofile: String?,
    @SerializedName("rsaserial") val rsaserial: String?,
    @SerializedName("startdate") val startdate: String?,
    @SerializedName("status") val status: String?,
    @SerializedName("title") val title: String?,
    @SerializedName("username") val username: String?,
    @SerializedName("version") val version: String?,
    @SerializedName("isSelected") val isSelected: String?,
    @SerializedName("enterpriseUserId") val enterpriseUserId: String?,
    @SerializedName("endDate") val endDate: String?,
)
