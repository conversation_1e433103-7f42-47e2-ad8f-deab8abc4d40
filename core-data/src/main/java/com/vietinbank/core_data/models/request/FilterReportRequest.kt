package com.vietinbank.core_data.models.request

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_common.constants.Constants

data class FilterReportRequest(
//    @SerializedName("statusGroups") val statusGroups: String = "",
//    @SerializedName("groupTypes") val groupTypes: String = "",
//    @SerializedName("fromAccounts") val fromAccounts: String = "",
    @SerializedName("username") val username: String = "",
    @SerializedName("distributorNo") val distributorNo: String = "",
    @SerializedName("type") val type: String = "",
    @SerializedName("searchAdvance") val searchAdvance: String = "",
) : CommonRequest(Constants.MB_REPORT_PRE_FILTER)

data class ListReportRequest(
    @SerializedName("creditacc") val creditacc: String = "", // tu tai khoan
    @SerializedName("creditname") val creditname: String = "", // ten thu huong
    @SerializedName("dateType") val dateType: String = "", // ngày tạo điện trường dateType = 0; ngày phê duyệt dateType = 1
    @SerializedName("fromdate") val fromdate: String = "", // tu ngay
    @SerializedName("maxAmt") val maxAmt: String = "", // den so tien
    @SerializedName("minAmt") val minAmt: String = "", // tu so tien
    @SerializedName("receiveBankName") val receiveBankName: String = "", // ngan hang huong
    @SerializedName("status") val status: String = "", // trang thai giao dich
    @SerializedName("toAccountNo") val toAccountNo: String = "", // toi tai khoan
    @SerializedName("todate") val todate: String = "", // den ngay
    @SerializedName("trantype") val trantype: String = "", // loai giao dich
    @SerializedName("username") val username: String = "",

    @SerializedName("accountCurr") val accountCurr: String = "",
    @SerializedName("content") val content: String = "",
    @SerializedName("distributorNo") val distributorNo: String = "",
    @SerializedName("filename") val filename: String = "",
    @SerializedName("fromPage") val fromPage: String = "",
    @SerializedName("refno") val refno: String = "",
    @SerializedName("saleOrderNo") val saleOrderNo: String = "",
    @SerializedName("toPage") val toPage: String = "",
    @SerializedName("type") val type: String = "",
    @SerializedName("viewType") val viewType: String = "",
    @SerializedName("orderBy") val orderBy: String = "", // sap xep theo tien (amount), ngày (createDate)
    @SerializedName("orderDirect") val orderDirect: String = "", // tang dan asc, giam dan  desc
) : CommonRequest(Constants.MB_REPORT_LIST_FILTER)

data class DetailReportRequest(
    @SerializedName("mtId") val mtId: String = "",
    @SerializedName("username") val username: String = "",
    @SerializedName("serviceType") val serviceType: String = "",
    @SerializedName("signType") val signType: String = "",
    @SerializedName("tranType") val tranType: String = "",
    @SerializedName("type") val type: String? = null,
    @SerializedName("mtIdList") val mtIdList: List<String>? = null,
) : CommonRequest(Constants.MB_REPORT_DETAIL_FILTER)

data class InquiryApproverRequest(
    @SerializedName("mtId") val mtId: String = "",
    @SerializedName("username") val username: String = "",
    @SerializedName("accountNumber") val accountNumber: String = "",
    @SerializedName("serviceId") val serviceId: String = "",
) : CommonRequest(Constants.MB_INQUIRY_APPROVER)

data class TraceInquiryRequest(
    @SerializedName("mtId") val mtId: String = "",
    @SerializedName("username") val username: String = "",
) : CommonRequest(Constants.MB_TRACE_INQUIRY)

data class ReportDetailRequest(
    @SerializedName("mtId") val mtId: String = "",
    @SerializedName("serviceType") val serviceType: String = "",
    @SerializedName("signType") val signType: String = "",
    @SerializedName("trantype") val trantype: String = "",
    @SerializedName("type") val type: String = "",
    @SerializedName("username") val username: String = "",
) : CommonRequest(Constants.MB_REPORT_DETAIL)

data class TraceCreateRequest(
    @SerializedName("feeAccountNo") val feeAccountNo: String = "",
    @SerializedName("mtId") val mtId: String = "",
    @SerializedName("traceMsg") val traceMsg: String = "",
    @SerializedName("username") val username: String = "",
) : CommonRequest(Constants.MB_TRACE_CREATE)
