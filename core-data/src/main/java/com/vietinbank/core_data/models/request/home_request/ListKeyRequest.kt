package com.vietinbank.core_data.models.request.home_request

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_data.models.request.CommonRequest

class ListKeyRequest(
    @SerializedName("username")
    val username: String = "",
    @SerializedName("type")
    val type: String = "",
    @SerializedName("role")
    val role: String = "",
) : CommonRequest(Constants.MB_LIST_KEY)