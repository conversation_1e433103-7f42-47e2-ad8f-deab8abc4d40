package com.vietinbank.core_data.models.request.home_request

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_data.models.request.CommonRequest
import com.vietinbank.core_data.models.response.home_response.FunctionData

class ListFunctionRequest(
    @SerializedName("username")
    val username: String = "",
    @SerializedName("roleId")
    val roleId: String = "",
    @SerializedName("role")
    val role: String = "",
) : CommonRequest(Constants.MB_LIST_FUNCTION)

class ListFuncFavouriteRequest(
    @SerializedName("username")
    val username: String = "",
    @SerializedName("roleId")
    val roleId: String = "",
    @SerializedName("role")
    val role: String = "",
) : CommonRequest(Constants.MB_LIST_FUNCTION_FAVOURITE)

class UpdateListFuncFavouriteRequest(
    @SerializedName("username")
    val username: String = "",
    @SerializedName("roleId")
    val roleId: String = "",
    @SerializedName("role")
    val role: String = "",
    @SerializedName("functions")
    val functions: List<FunctionData> = arrayListOf(),
) : CommonRequest(Constants.MB_LIST_UPDATE_FUNCTION_FAVOURITE)