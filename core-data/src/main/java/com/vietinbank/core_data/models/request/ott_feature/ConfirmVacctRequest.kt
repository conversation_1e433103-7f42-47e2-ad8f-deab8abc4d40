package com.vietinbank.core_data.models.request.ott_feature

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_data.models.request.CommonRequest

class ConfirmVacctRequest(
    @SerializedName("phoneNo")
    val phoneNo: String? = "",
    @SerializedName("records")
    val records: List<RecordRequest?>? = listOf(),
    @SerializedName("userAction")
    val userAction: String? = "",
    @SerializedName("userRole")
    val userRole: String? = "",
    @SerializedName("username")
    val username: String? = "",
) : CommonRequest(Constants.MB_OTT_CONFIRM_VACCT)

class CancelVacctRequest(
    @SerializedName("records")
    val records: List<RecordRequest?>? = listOf(),
    @SerializedName("phoneNo")
    val phoneNo: String? = "",
    @SerializedName("username")
    val username: String? = "",
) : CommonRequest(Constants.MB_OTT_CANCEL_VACCT)

class RecordRequest(
    @SerializedName("username")
    val username: String? = null,
    @SerializedName("tranId")
    val tranId: String? = null,
    @SerializedName("virAcctNo")
    val virAcctNo: String? = null,
    @SerializedName("virAcctName")
    val virAcctName: String? = null,
    @SerializedName("virPhoneNo")
    val virPhoneNo: String? = null,
)