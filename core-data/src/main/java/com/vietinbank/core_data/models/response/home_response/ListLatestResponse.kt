package com.vietinbank.core_data.models.response.home_response

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_data.models.base.Status

data class ListLatestResponse(
    @SerializedName("requestId")
    val requestId: String? = "",
    @SerializedName("sessionId")
    val sessionId: String? = "",
    @SerializedName("status")
    val status: Status = Status(
        code = "",
        message = "",
        subCode = "",
    ),
    @SerializedName("transactionInfoList")
    val transactionInfoList: ArrayList<TransactionInfo>?,
)

data class TransactionInfo(
    @SerializedName("iconUrl")
    val iconUrl: String? = "",
    @SerializedName("amount")
    val amount: String? = "",
    @SerializedName("amount2")
    val amount2: String? = "",
    @SerializedName("amount3")
    val amount3: String? = "",
    @SerializedName("approvalDate")
    val approvalDate: String? = "",
    @SerializedName("approver")
    val approver: String? = "",
    @SerializedName("billOwnerCode")
    val billOwnerCode: String? = "",
    @SerializedName("billOwnerName")
    val billOwnerName: String? = "",
    @SerializedName("branchName")
    val branchName: String? = "",
    @SerializedName("cifNo")
    val cifNo: String? = "",
    @SerializedName("content")
    val content: String? = "",
    @SerializedName("createDate")
    val createDate: String? = "",
    @SerializedName("creator")
    val creator: String? = "",
    @SerializedName("currency")
    val currency: String? = "",
    @SerializedName("currency2")
    val currency2: String? = "",
    @SerializedName("currency3")
    val currency3: String? = "",
    @SerializedName("declareCode")
    val declareCode: String? = "",
    @SerializedName("disbursementDate")
    val disbursementDate: String? = "",
    @SerializedName("documentType")
    val documentType: String? = "",
    @SerializedName("documentTypeName")
    val documentTypeName: String? = "",
    @SerializedName("ebankCode")
    val ebankCode: String? = "",
    @SerializedName("expireTime")
    val expireTime: String? = "",
    @SerializedName("expiredTime")
    val expiredTime: String? = "",
    @SerializedName("feeDecNum")
    val feeDecNum: String? = "",
    @SerializedName("feeDeclarationNumber")
    val feeDeclarationNumber: String? = "",
    @SerializedName("feeUnitName")
    val feeUnitName: String? = "",
    @SerializedName("fileID")
    val fileID: String? = "",
    @SerializedName("fileName")
    val fileName: String? = "",
    @SerializedName("fromAccount")
    val fromAccount: String? = "",
    @SerializedName("groupID")
    val groupID: String? = "",
    @SerializedName("groupType")
    val groupType: String? = "",
    @SerializedName("interest")
    val interest: String? = "",
    @SerializedName("loanRate")
    val loanRate: String? = "",
    @SerializedName("loanTerm")
    val loanTerm: String? = "",
    @SerializedName("mtId")
    val mtId: String? = "",
    @SerializedName("nextPaymentDay")
    val nextPaymentDay: String? = "",
    @SerializedName("originMtID")
    val originMtID: String? = "",
    @SerializedName("payCode")
    val payCode: String? = "",
    @SerializedName("processTime")
    val processTime: String? = "",
    @SerializedName("productId")
    val productId: String? = "",
    @SerializedName("productName")
    val productName: String? = "",
    @SerializedName("serviceId")
    val serviceId: String? = "",
    @SerializedName("serviceName")
    val serviceName: String? = "",
    @SerializedName("serviceType")
    val serviceType: String? = "",
    @SerializedName("siInsuranceID")
    val siInsuranceID: String? = "",
    @SerializedName("siInsuranceName")
    val siInsuranceName: String? = "",
    @SerializedName("status")
    val status: String? = "",
    @SerializedName("statusBill")
    val statusBill: String? = "",
    @SerializedName("statusName")
    val statusName: String? = "",
    @SerializedName("sub_trx_type")
    val subTrxType: String? = "",
    @SerializedName("term")
    val term: String? = "",
    @SerializedName("toAccount")
    val toAccount: String? = "",
    @SerializedName("toAccountName")
    val toAccountName: String? = "",
    @SerializedName("totalTrx")
    val totalTrx: String? = "",
    @SerializedName("tranTypeName")
    val tranTypeName: String? = "",
    @SerializedName("trxType")
    val trxType: String? = "",
    @SerializedName("bankName")
    val bankName: String? = "",
)
