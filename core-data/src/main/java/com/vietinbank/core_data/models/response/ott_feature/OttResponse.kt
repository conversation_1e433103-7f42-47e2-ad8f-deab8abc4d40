package com.vietinbank.core_data.models.response.ott_feature

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_data.models.response.Status

open class OttSecureResponse(
    @SerializedName("status") val status: Status,
)

class OttRegisterResponse(
    status: Status,
    @SerializedName("data") val data: RegisterData?,
) : OttSecureResponse(status)

data class RegisterData(
    @SerializedName("registered") val registered: <PERSON>olean,
    @SerializedName("deviceId") val deviceId: String?,
)

class OttUpdateStatusResponse(
    status: Status,
    @SerializedName("data") val data: UpdateStatusData?,
) : OttSecureResponse(status)

data class UpdateStatusData(
    @SerializedName("updated") val updated: Boolean,
    @SerializedName("messageId") val messageId: String,
)

class OttRegisterSocketResponse(
    status: Status,
    @SerializedName("data") val data: RegisterData?,
) : OttSecureResponse(status)

class OttGetAllMessageResponse(
    @SerializedName("requestId")
    val requestId: String,
    @SerializedName("sessionId")
    val sessionId: String,
    @SerializedName("status")
    val status: com.vietinbank.core_data.models.base.Status? = com.vietinbank.core_data.models.base.Status(
        code = "",
        message = "",
        subCode = "",
    ),
    @SerializedName("message")
    val message: String?,
    @SerializedName("data") val data: String?,
)

class CheckRegResponse()

class ListRegResponse(
    @SerializedName("encryptKey")
    val encryptKey: String,
    @SerializedName("encryptType")
    val encryptType: String,
    @SerializedName("fees")
    val fees: List<FeeItemResponse?>,
    @SerializedName("results")
    val results: List<ResultItemResponse?>,
)

data class FeeItemResponse(
    @SerializedName("alertType") val alertType: String,
    @SerializedName("alertMethod") val alertMethod: String,
    @SerializedName("feeType") val feeType: String,
    @SerializedName("custType") val custType: String,
    @SerializedName("feeAmount") val feeAmount: String,
    @SerializedName("currCode") val currCode: String,
    @SerializedName("feeName") val feeName: String,
)

data class ResultItemResponse(
    @SerializedName("customerNumber") val customerNumber: String?,
    @SerializedName("accountNumber") val accountNumber: String?,
    @SerializedName("alertAddr") val alertAddr: String?,
    @SerializedName("alertType") val alertType: String?,
    @SerializedName("alertMethod") val alertMethod: String?,
    @SerializedName("feeAccountNumber") val feeAccountNumber: String?,
    @SerializedName("feeAmount") val feeAmount: String?,
    @SerializedName("feeType") val feeType: String?,
    @SerializedName("startDate") val startDate: String?,
    @SerializedName("action") val action: String?,
    @SerializedName("accountName") val accountName: String?,
    @SerializedName("username") val username: String?,
    @SerializedName("isVacct") val isVacct: String?,
    @SerializedName("phonenumber") val phonenumber: String?,
    @SerializedName("status") val status: String?,
    @SerializedName("accountType") val accountType: String?,
    @SerializedName("branchId") val branchId: String?,
)

class SmsOtpCreateRequestResponse()
class SmsOtpVerifyRequestResponse()
data class OttRegResponse(
    @SerializedName("encryptType") val encryptType: String?,
    @SerializedName("encUserId") val encUserId: String?,
    @SerializedName("fees") val fees: String?,
    @SerializedName("encryptKey") val encryptKey: String?,
    @SerializedName("results") val results: List<ResultItemResponse>?,
    @SerializedName("status") val status: StatusResponse,
)
data class StatusResponse(
    @SerializedName("code") val code: String?,
    @SerializedName("message") val message: String?,
)