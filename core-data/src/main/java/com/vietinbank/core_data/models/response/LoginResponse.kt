package com.vietinbank.core_data.models.response

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_data.models.base.Status

/**
 * Created by vand<PERSON> on 18/12/24.
 */
data class LoginResponseData(
    @SerializedName("status")
    val status: Status = Status(code = "", message = "", subCode = ""),
    @SerializedName("sessionId")
    val sessionId: String?,
    @SerializedName("corpUser")
    val corpUser: CorpUser,
    @SerializedName("roleList")
    val roleList: List<Role>,
    @SerializedName("cifNo")
    val cifNo: String?,
    @SerializedName("userId")
    val userId: String?,
    @SerializedName("tokenId")
    val tokenId: String?,
    @SerializedName("loginFail")
    val loginFail: Int?,
    @SerializedName("timeout")
    val timeout: String?,
    @SerializedName("addField1")
    val addField1: String?,
    @SerializedName("addField2")
    val addField2: String?,
    @SerializedName("addField3")
    val addField3: String?,
    @SerializedName("addField4")
    val addField4: String?,
    @SerializedName("lsImportantMessage")
    val lsImportantMessage: List<String?>,
    @SerializedName("listCifShared")
    val listCifShared: ArrayList<CifSharedEntity>?,
)

data class CorpUser(
    @SerializedName("id")
    val id: String?,
    @SerializedName("username")
    val username: String?,
    @SerializedName("fullname")
    val fullname: String?,
    @SerializedName("idCard")
    val idCard: String?,
    @SerializedName("phoneNo")
    val phoneNo: String?,
    @SerializedName("email")
    val email: String?,
    @SerializedName("lastLogin")
    val lastLogin: String?,
    @SerializedName("corpName")
    val corpName: String?,
    @SerializedName("corpAddress")
    val corpAddress: String?,
    @SerializedName("cifno")
    val cifno: String?,
    @SerializedName("paycode")
    val paycode: String?,
    @SerializedName("website")
    val website: String?,
    @SerializedName("roleId")
    val roleId: String?,
    @SerializedName("roleLevel")
    val roleLevel: String?,
    @SerializedName("tokenNo")
    val tokenNo: String?,
    @SerializedName("rsaprofile")
    val rsaprofile: String?,
    @SerializedName("numOfLevel1")
    val numOfLevel1: String?,
    @SerializedName("numOfLevel2")
    val numOfLevel2: String?,
    @SerializedName("legacySystem")
    val legacySystem: String?,
    @SerializedName("birthday")
    val birthday: String?,
    @SerializedName("gender")
    val gender: String?,
    @SerializedName("nationality")
    val nationality: String?,
    @SerializedName("title")
    val title: String?,
    @SerializedName("status")
    val status: String?,
    @SerializedName("keypasswaitactive")
    val keypasswaitactive: String?,
    @SerializedName("keypassid")
    val keypassid: String?,
    @SerializedName("keypassprofile")
    val keypassprofile: String?,
    @SerializedName("keypasssoftotp")
    val keypasssoftotp: String?,
    @SerializedName("idNumber")
    val idNumber: String?,
    @SerializedName("expiredDate")
    val expiredDate: String?,
    @SerializedName("startDate")
    val startDate: String?,
    @SerializedName("enDate")
    val enDate: String?,
    @SerializedName("phoneOtt")
    val phoneOtt: String?,
)

data class Role(
    @SerializedName("role")
    val role: String?,
    @SerializedName("serviceCode")
    val serviceCode: String?,
)

data class CifSharedEntity(
    @SerializedName("enterpriseid") val enterpriseid: String?,
    @SerializedName("enterprisename") val enterprisename: String?,
)

// change password
data class ChangePasswordResponseData(
    @SerializedName("status")
    val status: Status = Status(code = "", message = "", subCode = ""),
    @SerializedName("sessionId")
    val sessionId: String,
    @SerializedName("requestId")
    val requestId: String,
)

data class ForceUpdateResponseData(
    @SerializedName("versionApp")
    val versionApp: VersionApp,
)

data class VersionApp(
    @SerializedName("deviceOs")
    val deviceOs: String?,
    @SerializedName("displayNum")
    val displayNum: String?,
    @SerializedName("functionId")
    val functionId: List<String?>,
    @SerializedName("id")
    val id: String?,
    @SerializedName("mess")
    val mess: String?,
    @SerializedName("messUpdated")
    val messUpdated: String?,
    @SerializedName("navigation")
    val navigation: String?,
    @SerializedName("roleId")
    val roleId: String?,
    @SerializedName("status")
    val status: String?,
    @SerializedName("update_displayNum")
    val update_displayNum: String?,
    @SerializedName("version")
    val version: String?,
)

data class GenOTPResponseData(
    @SerializedName("status")
    val status: Status,
    @SerializedName("efastId")
    val efastId: String,
    @SerializedName("phoneNumber")
    val phoneNumber: String,
    @SerializedName("requestId")
    val requestId: String,
)

data class VerifyOTPResponseData(
    @SerializedName("loginResponse")
    val loginResponse: LoginResponseData?,
    @SerializedName("sessionId")
    val sessionId: String,
    @SerializedName("requestId")
    val requestId: String?,
)
