package com.vietinbank.core_data.models.response

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_domain.models.checker.ItemSubTransactionDetailDomain

data class ReportDetailsResponse(
    @SerializedName("transaction") val transactionResponse: TransactionResponse?,
)

data class TransactionResponse(
    @SerializedName("amount") val amount: String?,
    @SerializedName("bulkID") val bulkId: String?,
    @SerializedName("createdDate") val createdDate: String?,
    @SerializedName("verifiedDate") val verifiedDate: String?,
    @SerializedName("currency") val currency: String?,
    @SerializedName("feeAmount") val feeAmount: String?,
    @SerializedName("fromAccountNo") val fromAccountNo: String?,
    @SerializedName("groupType") val groupType: String?,
    @SerializedName("mtId") val mtId: String?,
    @SerializedName("receiveBank") val receiveBank: String?,
    @SerializedName("receiveBankName") val receiveBankName: String?,
    @SerializedName("receiveName") val receiveName: String?,
    @SerializedName("remark") val remark: String?,
    @SerializedName("statusName") val statusName: String?,
    @SerializedName("toAccountNo") val toAccountNo: String?,
    @SerializedName("tranType") val tranType: String?,
    @SerializedName("tranTypeName") val tranTypeName: String?,
    @SerializedName("process_time") val processTime: String?,
    @SerializedName("processTimeDesc") val processTimeDesc: String?,
    @SerializedName("process_status") val processStatus: String?,
    @SerializedName("beneficiaryNo") val beneficiaryNo: String?,
    @SerializedName("refno") val refNo: String?,
    @SerializedName("executeBrn") val executeBrn: String?,
    @SerializedName("priority") val priority: String?,
    @SerializedName("feePayMethod") val feePayMethod: String?,
    @SerializedName("feePayMethodDesc") val feePayMethodDesc: String?,
    @SerializedName("feePayMethodDesc1") val feePayMethodDesc1: String?,
    @SerializedName("cifno") val cifNo: String?,
    @SerializedName("tranNumber") val tranNumber: String?,
    @SerializedName("cardNumber") val cardNumber: String?,
    @SerializedName("createTranDate") val createTranDate: String?,
    @SerializedName("statusCard") val statusCard: String?,
    @SerializedName("cardName") val cardName: String?,
    @SerializedName("cardType") val cardType: String?,
    @SerializedName("creatorCard") val creatorCard: String?,
    @SerializedName("approverCard") val approverCard: String?,
    @SerializedName("prisentStatus") val prisentStatus: String?,
    @SerializedName("addressOfPayer") val addressOfPayer: String?,
    @SerializedName("addressOfReceiver") val addressOfReceiver: String?,
    @SerializedName("issuedPlace") val issuedPlace: String?,
    @SerializedName("issuedDate") val issuedDate: String?,
    @SerializedName("repaymentType") val repaymentType: String?,
    @SerializedName("territoryIn") val territoryIn: String?,
    @SerializedName("territoryInLimit") val territoryInLimit: String?,
    @SerializedName("territoryOut") val territoryOut: String?,
    @SerializedName("territoryOutlimit") val territoryOutlimit: String?,
    @SerializedName("frequencyOnDay") val frequencyOnDay: String?,
    @SerializedName("frequencyOnFourDay") val frequencyOnFourDay: String?,
    @SerializedName("frequencyLimit") val frequencyLimit: String?,
    @SerializedName("totalLimitOnDay") val totalLimitOnDay: String?,
    @SerializedName("totalLimitOnFourDay") val totalLimitOnFourDay: String?,
    @SerializedName("totalLimit") val totalLimit: String?,
    @SerializedName("alertPhone1") val alertPhone1: String?,
    @SerializedName("alertPhone2") val alertPhone2: String?,
    @SerializedName("tuitionStudentDataId") val tuitionStudentDataId: String?,
    @SerializedName("tuitionStudentCode") val tuitionStudentCode: String?,
    @SerializedName("tuitionStudentName") val tuitionStudentName: String?,
    @SerializedName("tuitionStudentClass") val tuitionStudentClass: String?,
    @SerializedName("tuitionFileId") val tuitionFileId: String?,
    @SerializedName("tuitionAmount") val tuitionAmount: String?,
    @SerializedName("tuitionContent") val tuitionContent: String?,
    @SerializedName("tuitionPaymentStatusId") val tuitionPaymentStatusId: String?,
    @SerializedName("tuitionBillId") val tuitionBillId: String?,
    @SerializedName("tuitionFileName") val tuitionFileName: String?,
    @SerializedName("tuitionProviderName") val tuitionProviderName: String?,
    @SerializedName("feePrePaid") val feePrePaid: String?,
    @SerializedName("prePrincipal") val prePrincipal: String?,
    @SerializedName("totalAmount") val totalAmount: String?,
    @SerializedName("maturityDate") val maturityDate: String?,
    @SerializedName("current") val current: String?,
    @SerializedName("accruedInterest") val accruedInterest: String?,
    @SerializedName("loanType") val loanType: String?,
    @SerializedName("napasType") val napasType: String?,
    @SerializedName("settlementAmount") val settlementAmount: String?,
    @SerializedName("debitCurrency") val debitCurrency: String?,
    @SerializedName("creditCurrency") val creditCurrency: String?,
    @SerializedName("exchangeRate") val exchangeRate: String?,
    @SerializedName("currenciesPair") val currenciesPair: String?,
    @SerializedName("fromAccountName2") val fromAccountName2: String?,
    @SerializedName("alias") val alias: String?,
    @SerializedName("availableBalance") val availableBalance: String?,
    @SerializedName("sendedDate") val sendedDate: String?,
    @SerializedName("fromAccountName") val fromAccountName: String?,
    @SerializedName("branchId") val branchId: String?,
    @SerializedName("branchName") val branchName: String?,
    @SerializedName("corpName") val corpName: String?,
    @SerializedName("corpCif") val corpCif: String?,
    @SerializedName("tranDesc") val tranDesc: String?,
    @SerializedName("toAccountName") val toAccountName: String?,
    @SerializedName("allowcancel") val allowCancel: Boolean?,
    @SerializedName("rejectContent") val rejectContent: String?,
    @SerializedName("activityLogs") val activityLogs: ActivityLogEntity?,
    @SerializedName("payMethod") val payMethod: String?, // NSNN
    @SerializedName("payname") val payname: String?,
    @SerializedName("paycode") val paycode: String?,
    @SerializedName("payadd") val payadd: String?,
    @SerializedName("provinceCode") val provinceCode: String?,
    @SerializedName("provinceName") val provinceName: String?,
    @SerializedName("areaCode") val areaCode: String?,
    @SerializedName("areaName") val areaName: String?,
    @SerializedName("treasuryCode") val treasuryCode: String?,
    @SerializedName("treasuryName") val treasuryName: String?,
    @SerializedName("collectionAccountNo") val collectionAccountNo: String?,
    @SerializedName("collectionAccountName") val collectionAccountName: String?,
    @SerializedName("collectAgencyCode") val collectAgencyCode: String?,
    @SerializedName("collectAgencyName") val collectAgencyName: String?,
    @SerializedName("taxType") val taxType: String?,
    @SerializedName("bucode") val bucode: String?,
    @SerializedName("buName") val buName: String?,
    @SerializedName("oficode") val oficode: String?,
    @SerializedName("ofiName") val ofiName: String?,
    @SerializedName("chapCode") val chapCode: String?,
    @SerializedName("chapName") val chapName: String?,
    @SerializedName("declareNumber") val declareNumber: String?,
    @SerializedName("declareDate") val declareDate: String?,
    @SerializedName("ieType") val ieType: String?,
    @SerializedName("ieName") val ieName: String?,
    @SerializedName("uid") val uid: String?,
    @SerializedName("items") val items: List<ItemSubTransactionDetailDomain>? = null,
    @SerializedName("providerCode") val providerCode: String?, // Hạ tầng
    @SerializedName("invoiceId") val invoiceId: String?,
    @SerializedName("docId") val docId: String?,
    @SerializedName("voucherNumber") val voucherNumber: String?,
    @SerializedName("docNum") val docNum: String?,
    @SerializedName("voucherSymbol") val voucherSymbol: String?,
    @SerializedName("docSign") val docSign: String?,
    @SerializedName("voucherDate") val voucherDate: String?,
    @SerializedName("docDate") val docDate: String?,
    @SerializedName("subsect") val subsect: String?,
    @SerializedName("assetCode") val assetCode: String?,
    @SerializedName("machineNo") val machineNo: String?,
    @SerializedName("machineFeatures") val machineFeatures: String?,
    @SerializedName("propertyAdd") val propertyAdd: String?,
)
