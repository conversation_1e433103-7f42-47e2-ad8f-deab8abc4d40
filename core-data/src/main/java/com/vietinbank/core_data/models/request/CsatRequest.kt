package com.vietinbank.core_data.models.request

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_common.constants.Constants

data class CSatRateRequest(
    @SerializedName("comment") val comment: String?,
    @SerializedName("functionId") val functionId: String?,
    @SerializedName("ratePoint") val ratePoint: String?,
    @SerializedName("userName") val userName: String?,
) : CommonRequest(Constants.MB_CSAT_RATE)

data class CSatConfigRequest(
    @SerializedName("functionId") val functionId: String?,
    @SerializedName("userName") val userName: String?,
) : CommonRequest(Constants.MB_CSAT_CONFIG)
