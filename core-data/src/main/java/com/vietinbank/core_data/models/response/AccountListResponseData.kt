package com.vietinbank.core_data.models.response

import com.google.gson.annotations.SerializedName

data class AccountListResponseData(
    @SerializedName("accounts")
    val accountDefault: List<AccountDefault>,
)

data class AccountDefault(
    @SerializedName("accountName")
    val accountName: String,
    @SerializedName("accountNo")
    val accountNo: String,
    @SerializedName("accrueInterest")
    val accrueInterest: String,
    @SerializedName("accountType")
    val accountType: String,
    @SerializedName("accruedInterest")
    val accruedInterest: String,
    @SerializedName("acctNbr")
    val acctNbr: String,
    @SerializedName("acctType")
    val acctType: String,
    @SerializedName("aliasName")
    val aliasName: String,
    @SerializedName("availableBalance")
    val availableBalance: String,
    @SerializedName("benBankName")
    val benBankName: String,
    @SerializedName("beneficiaryName")
    val beneficiaryName: String,
    @SerializedName("branchId")
    val branchId: String,
    @SerializedName("branchName")
    val branchName: String,
    @SerializedName("cifNo")
    val cifNo: String,
    @SerializedName("closeDate")
    val closeDate: String,
    @SerializedName("closingOutstandingBalance")
    val closingOutstandingBalance: String,
    @SerializedName("contractNbr")
    val contractNbr: String,
    @SerializedName("corpName")
    val corpName: String,
    @SerializedName("creditCardLst")
    val creditCardLst: List<CreaditCardList>,
    @SerializedName("creditLimit")
    val creditLimit: String,
    @SerializedName("currency")
    val currency: String,
    @SerializedName("currentBalance")
    val currentBalance: String,
    @SerializedName("depositCardSerialNumber")
    val depositCardSerialNumber: String,
    @SerializedName("depositContractNumber")
    val depositContractNumber: String,
    @SerializedName("districtId")
    val districtId: String,
    @SerializedName("districtName")
    val districtName: String,
    @SerializedName("dueDate")
    val dueDate: String,
    @SerializedName("escrowAmt")
    val escrowAmt: String,
    @SerializedName("feeAcctNo")
    val feeAcctNo: String,
    @SerializedName("feePlans")
    val feePlans: List<FeePlans>,
    @SerializedName("fullPayLeft")
    val fullPayLeft: String,
    @SerializedName("holdBalance")
    val holdBalance: String,
    @SerializedName("interestAmount")
    val interestAmount: String,
    @SerializedName("interestNotDueBilled")
    val interestNotDueBilled: String,
    @SerializedName("interestPastDue")
    val interestPastDue: String,
    @SerializedName("interestRate")
    val interestRate: String,
    @SerializedName("interestTerm")
    val interestTerm: String,
    @SerializedName("issuedDate")
    val issuedDate: String,
    @SerializedName("lang")
    val lang: String,
    @SerializedName("lateCharge")
    val lateCharge: String,
    @SerializedName("mainAccountId")
    val mainAccountId: String,
    @SerializedName("maturityDate")
    val maturityDate: String,
    @SerializedName("minPayLeft")
    val minPayLeft: String,
    @SerializedName("minimumAmount")
    val minimumAmount: String,
    @SerializedName("nextPaymentDate")
    val nextPaymentDate: String,
    @SerializedName("openDate")
    val openDate: String,
    @SerializedName("origIssueDt")
    val origIssueDt: String,
    @SerializedName("outstandingBalance")
    val outstandingBalance: String,
    @SerializedName("payOffAmount")
    val payOffAmount: String,
    @SerializedName("penaltyAmount")
    val penaltyAmount: String,
    @SerializedName("principalAmount")
    val principalAmount: String,
    @SerializedName("principalNotDueBilled")
    val principalNotDueBilled: String,
    @SerializedName("principalPastDue")
    val principalPastDue: String,
    @SerializedName("productId")
    val productId: String,
    @SerializedName("productName")
    val productName: String,
    @SerializedName("provinceId")
    val provinceId: String,
    @SerializedName("provinceName")
    val provinceName: String,
    @SerializedName("remainingLoanTotal")
    val remainingLoanTotal: String,
    @SerializedName("settlementDate")
    val settlementDate: String,
    @SerializedName("statementDate")
    val statementDate: String,
    @SerializedName("status")
    val status: String,
    @SerializedName("statusName")
    val statusName: String,
    @SerializedName("term")
    val term: String,
    @SerializedName("totalCredit")
    val totalCredit: String,
    @SerializedName("totalPayment")
    val totalPayment: String,
)

data class CreaditCardList(
    @SerializedName("availLimit")
    val availLimit: String,
    @SerializedName("branchId")
    val branchId: String,
    @SerializedName("cardEmbossNum")
    val cardEmbossNum: String,
    @SerializedName("cardGrp")
    val cardGrp: String,
    @SerializedName("cardId")
    val cardId: String,
    @SerializedName("cardNumber")
    val cardNumber: String,
    @SerializedName("cardType")
    val cardType: String,
    @SerializedName("cardTypeName")
    val cardTypeName: String,
    @SerializedName("creditLimit")
    val creditLimit: String,
    @SerializedName("curCode")
    val curCode: String,
    @SerializedName("customerNumber")
    val customerNumber: String,
    @SerializedName("ecommerceInd")
    val ecommerceInd: String,
    @SerializedName("fullname")
    val fullname: String,
    @SerializedName("group")
    val group: String,
    @SerializedName("internetBankingInd")
    val internetBankingInd: String,
    @SerializedName("issuedDt")
    val issuedDt: String,
    @SerializedName("status")
    val status: String,
    @SerializedName("statusName")
    val statusName: String,
)

data class FeePlans(
    @SerializedName("feeCategory")
    val feeCategory: String,
    @SerializedName("feeType")
    val feeType: String,
    @SerializedName("name")
    val name: String,
)
