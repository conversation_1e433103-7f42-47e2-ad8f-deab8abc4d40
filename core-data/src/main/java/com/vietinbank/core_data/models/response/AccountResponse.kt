package com.vietinbank.core_data.models.response

import android.graphics.Bitmap
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.sp
import com.google.gson.annotations.SerializedName
import com.vietinbank.core_common.models.ItemResult
import com.vietinbank.core_common.models.TransferResult
import com.vietinbank.core_domain.models.account.AccountDetailDomain
import com.vietinbank.core_domain.models.account.AccountFilterHistory
import com.vietinbank.core_domain.models.account.AccountModelUI
import com.vietinbank.core_domain.models.account.HistoryDomain
import com.vietinbank.core_domain.models.account.StatusDescriptionUI
import com.vietinbank.core_domain.models.account.SubsidiaryInfoDomain
import com.vietinbank.core_domain.models.account.TransactionStatusDomain

data class AccountListEnterpriseResponse(
    @SerializedName("enterprises")
    val enterprises: List<ListEnterpriseResponse>?,
)

data class ListEnterpriseResponse(
    @SerializedName("enterpriseid")
    val enterpriseid: String?,
    @SerializedName("enterprisename")
    val enterprisename: String?,
)

data class AccountListNewResponse(
    @SerializedName("accounts")
    val accounts: List<AccountDefaultNew>?,
    @SerializedName("accountDefault")
    val accountDefault: AccountDefaultNew?,
    @SerializedName("parentSubsidiaryInfoList")
    val parentSubsidiaryInfoList: List<SubsidiaryInfoModel>?,
    @SerializedName("totalAmount")
    val totalAmount: String?,
)

data class AccountDefaultNew(
    @SerializedName("accountName")
    val accountName: String?,
    @SerializedName("accountNo")
    val accountNo: String?,
    @SerializedName("accrueInterest")
    val accrueInterest: String?,
    @SerializedName("accountType")
    val accountType: String?,
    @SerializedName("accruedInterest")
    val accruedInterest: String?,
    @SerializedName("acctNbr")
    val acctNbr: String?,
    @SerializedName("acctType")
    val acctType: String?,
    @SerializedName("aliasName")
    val aliasName: String?,
    @SerializedName("availableBalance")
    val availableBalance: String?,
    @SerializedName("benBankName")
    val benBankName: String?,
    @SerializedName("beneficiaryName")
    val beneficiaryName: String?,
    @SerializedName("branchId")
    val branchId: String?,
    @SerializedName("branchName")
    val branchName: String?,
    @SerializedName("cifNo")
    val cifNo: String?,
    @SerializedName("closeDate")
    val closeDate: String?,
    @SerializedName("closingOutstandingBalance")
    val closingOutstandingBalance: String?,
    @SerializedName("contractNbr")
    val contractNbr: String?,
    @SerializedName("corpName")
    val corpName: String?,
    @SerializedName("creditCardLst")
    val creditCardLst: List<CreaditCardList>,
    @SerializedName("creditLimit")
    val creditLimit: String?,
    @SerializedName("currency")
    val currency: String?,
    @SerializedName("currentBalance")
    val currentBalance: String?,
    @SerializedName("depositCardSerialNumber")
    val depositCardSerialNumber: String?,
    @SerializedName("depositContractNumber")
    val depositContractNumber: String?,
    @SerializedName("districtId")
    val districtId: String?,
    @SerializedName("districtName")
    val districtName: String?,
    @SerializedName("dueDate")
    val dueDate: String?,
    @SerializedName("escrowAmt")
    val escrowAmt: String?,
    @SerializedName("feeAcctNo")
    val feeAcctNo: String?,
    @SerializedName("feePlans")
    val feePlans: List<FeePlans>,
    @SerializedName("fullPayLeft")
    val fullPayLeft: String?,
    @SerializedName("holdBalance")
    val holdBalance: String?,
    @SerializedName("interestAmount")
    val interestAmount: String?,
    @SerializedName("interestNotDueBilled")
    val interestNotDueBilled: String?,
    @SerializedName("interestPastDue")
    val interestPastDue: String?,
    @SerializedName("interestRate")
    val interestRate: String?,
    @SerializedName("interestTerm")
    val interestTerm: String?,
    @SerializedName("issuedDate")
    val issuedDate: String?,
    @SerializedName("lang")
    val lang: String?,
    @SerializedName("lateCharge")
    val lateCharge: String?,
    @SerializedName("mainAccountId")
    val mainAccountId: String?,
    @SerializedName("maturityDate")
    val maturityDate: String?,
    @SerializedName("minPayLeft")
    val minPayLeft: String?,
    @SerializedName("minimumAmount")
    val minimumAmount: String?,
    @SerializedName("nextPaymentDate")
    val nextPaymentDate: String?,
    @SerializedName("openDate")
    val openDate: String?,
    @SerializedName("origIssueDt")
    val origIssueDt: String?,
    @SerializedName("outstandingBalance")
    val outstandingBalance: String?,
    @SerializedName("payOffAmount")
    val payOffAmount: String?,
    @SerializedName("penaltyAmount")
    val penaltyAmount: String?,
    @SerializedName("principalAmount")
    val principalAmount: String?,
    @SerializedName("principalNotDueBilled")
    val principalNotDueBilled: String?,
    @SerializedName("principalPastDue")
    val principalPastDue: String?,
    @SerializedName("productId")
    val productId: String?,
    @SerializedName("productName")
    val productName: String?,
    @SerializedName("provinceId")
    val provinceId: String?,
    @SerializedName("provinceName")
    val provinceName: String?,
    @SerializedName("remainingLoanTotal")
    val remainingLoanTotal: String?,
    @SerializedName("settlementDate")
    val settlementDate: String?,
    @SerializedName("statementDate")
    val statementDate: String?,
    @SerializedName("status")
    val status: String?,
    @SerializedName("statusName")
    val statusName: String?,
    @SerializedName("term")
    val term: String?,
    @SerializedName("totalCredit")
    val totalCredit: String?,
    @SerializedName("totalPayment")
    val totalPayment: String?,
    @SerializedName("cifName")
    val cifName: String,
)

data class SubsidiaryInfoModel(
    @SerializedName("cifNo") val cifNo: String?,
    @SerializedName("enterpriseName") val enterpriseName: String?,
    @SerializedName("parentSubsidiary") val parentSubsidiary: String?,
    @SerializedName("stateAddress") val stateAddress: String?,
)

data class AccountDetailResponse(
    @SerializedName("accountName") val accountName: String?,
    @SerializedName("accountNo") val accountNo: String?,
    @SerializedName("accountStatus") val accountStatus: String?,
    @SerializedName("accountStatusName") val accountStatusName: String?,
    @SerializedName("accountType") val accountType: String?,
    @SerializedName("accountTypeName") val accountTypeName: String?,
    @SerializedName("accrueInterest") val accrueInterest: String?,
    @SerializedName("accruedInterest") val accruedInterest: String?,
    @SerializedName("aliasAccount") val aliasAccount: AliasAccountModel?,
    @SerializedName("availableBalance") val availableBalance: String?,
    @SerializedName("branchId") val branchId: String?,
    @SerializedName("branchName") val branchName: String?,
    @SerializedName("commitFee") val commitFee: String?,
    @SerializedName("companyName") val companyName: String?,
    @SerializedName("currency") val currency: String?,
    @SerializedName("currentBalance") val currentBalance: String?,
    @SerializedName("debitAccount") val debitAccount: String?,
    @SerializedName("depositCardSerialNumber") val depositCardSerialNumber: String?,
    @SerializedName("depositContractNumber") val depositContractNumber: String?,
    @SerializedName("effDate") val effDate: String?,
    @SerializedName("feeAcctNo") val feeAcctNo: String?,
    @SerializedName("feeCollection") val feeCollection: String?,
    @SerializedName("fineAmount") val fineAmount: String?,
    @SerializedName("holdBalance") val holdBalance: String?,
    @SerializedName("interestAccountNo") val interestAccountNo: String?,
    @SerializedName("interestAmount") val interestAmount: String?,
    @SerializedName("interestMethod") val interestMethod: String?,
    @SerializedName("interestMethodName") val interestMethodName: String?,
    @SerializedName("interestNotDueBilled") val interestNotDueBilled: String?,
    @SerializedName("interestPastDue") val interestPastDue: String?,
    @SerializedName("interestRate") val interestRate: String?,
    @SerializedName("interestTerm") val interestTerm: String?,
    @SerializedName("interestTermCode") val interestTermCode: String?,
    @SerializedName("issuedDate") val issuedDate: String?,
    @SerializedName("lateCharge") val lateCharge: String?,
    @SerializedName("maturityDate") val maturityDate: String?,
    @SerializedName("nextPaymentDate") val nextPaymentDate: String?,
    @SerializedName("nextRepaymentDate") val nextRepaymentDate: String?,
    @SerializedName("openDate") val openDate: String?,
    @SerializedName("originalAmount") val originalAmount: String?,
    @SerializedName("otherCharge") val otherCharge: String?,
    @SerializedName("payOffAmount") val payOffAmount: String?,
    @SerializedName("penaltyAmount") val penaltyAmount: String?,
    @SerializedName("penantyInterest") val penantyInterest: String?,
    @SerializedName("prepaidInterest") val prepaidInterest: String?,
    @SerializedName("principalAmount") val principalAmount: String?,
    @SerializedName("principalBalance") val principalBalance: String?,
    @SerializedName("principalMethod") val principalMethod: String?,
    @SerializedName("principalMethodName") val principalMethodName: String?,
    @SerializedName("principalNotDueBilled") val principalNotDueBilled: String?,
    @SerializedName("principalPastDue") val principalPastDue: String?,
    @SerializedName("productId") val productId: String?,
    @SerializedName("productName") val productName: String?,
    @SerializedName("remainingLoanTotal") val remainingLoanTotal: String?,
    @SerializedName("renewalAcctNo") val renewalAcctNo: String?,
    @SerializedName("requestId") val requestId: String?,
    @SerializedName("serialNumber") val serialNumber: String?,
    @SerializedName("sessionId") val sessionId: String?,
    @SerializedName("settlementAmount") val settlementAmount: String?,
    @SerializedName("settlementDate") val settlementDate: String?,
    @SerializedName("spreadInterestRate") val spreadInterestRate: String?,
    @SerializedName("status") val status: Status?,
    @SerializedName("subAccType") val subAccType: String?,
    @SerializedName("term") val term: String?,
    @SerializedName("totalCurAmt") val totalCurAmt: String?,
    @SerializedName("totalPaymentAmount") val totalPaymentAmount: String?,
    @SerializedName("aliasName") val aliasName: String?,
    @SerializedName("data") val data: List<DataAccountModel>?,
)

data class DataAccountModel(
    @SerializedName("key") val key: String?,
    @SerializedName("value") val value: String?,
)

data class AliasAccountModel(
    @SerializedName("acctId") val acctId: String?,
    @SerializedName("aliasAcctId") val aliasAcctId: String?,
    @SerializedName("aliasFeeType") val aliasFeeType: String?,
    @SerializedName("aliasODT") val aliasODT: String?,
    @SerializedName("aliasPromoType") val aliasPromoType: String?,
    @SerializedName("aliasStatus") val aliasStatus: String?,
    @SerializedName("aliasType") val aliasType: String?,
    @SerializedName("convType") val convType: String?,
)

data class AccountHistoryDetailResponse(
    @SerializedName("transaction") val transaction: HistoryDetailModel?,
)

data class HistoryDetailModel(
    @SerializedName("accountNo") val accountNo: String?,
    @SerializedName("amount") val amount: String?,
    @SerializedName("balance") val balance: String?,
    @SerializedName("contraAccountName") val contraAccountName: String?,
    @SerializedName("contraAccountNo") val contraAccountNo: String?,
    @SerializedName("currency") val currency: String?,
    @SerializedName("dorc") val dorc: String?,
    @SerializedName("remark") val remark: String?,
    @SerializedName("tranDate") val tranDate: String?,
    @SerializedName("beneficiaryBankName") val beneficiaryBankName: String?,
    @SerializedName("trxId") val trxId: String?,
)

data class AccountHistoryListResponse(
    @SerializedName("accountNo") val accountNo: String?,
    @SerializedName("accountType") val accountType: String?,
    @SerializedName("aliasName") val aliasName: String?,
    @SerializedName("closingBalance") val closingBalance: String?,
    @SerializedName("currency") val currency: String?,
    @SerializedName("currentPage") val currentPage: Int?,
    @SerializedName("lastRecord") val lastRecord: String?,
    @SerializedName("more") val more: String?,
    @SerializedName("nextPage") val nextPage: String?,
    @SerializedName("openingBalance") val openingBalance: String?,
    @SerializedName("pageSize") val pageSize: Int?,
    @SerializedName("requestId") val requestId: String?,
    @SerializedName("sessionId") val sessionId: String?,
    @SerializedName("status") val status: Status?,
    @SerializedName("totalCredits") val totalCredits: String?,
    @SerializedName("totalDebits") val totalDebits: String?,
    @SerializedName("totalNumberOfCredits") val totalNumberOfCredits: String?,
    @SerializedName("totalNumberOfDebits") val totalNumberOfDebits: String?,
    @SerializedName("transactions") val transactions: List<HistoryModel>?,
    @SerializedName("sender") val sender: String?,
)

data class HistoryModel(
    @SerializedName("tranDate") val tranDate: String?,
    @SerializedName("remark") val remark: String?,
    @SerializedName("amount") val amount: String?,
    @SerializedName("balance") val balance: String?,
    @SerializedName("currency") val currency: String?,
    @SerializedName("trxId") val trxId: String?,
    @SerializedName("dorc") val dorc: String?,
    @SerializedName("branchId") val branchId: String?,
    @SerializedName("branchName") val branchName: String?,
    @SerializedName("channel") val channel: String?,
    @SerializedName("corresponsiveAccount") val corresponsiveAccount: String?,
    @SerializedName("corresponsiveName") val corresponsiveName: String?,
    @SerializedName("trxRefNo") val trxRefNo: String?,
    @SerializedName("pmtType") val pmtType: String?,
    @SerializedName("trnNum") val trnNum: String?,
    @SerializedName("endAmt") val endAmt: String?,
    @SerializedName("beginAmt") val beginAmt: String?,
    @SerializedName("numberOrder") val numberOrder: String?,
    @SerializedName("beneficiaryBankId") val beneficiaryBankId: String?,
    @SerializedName("beneficiaryBankName") val beneficiaryBankName: String?,
    @SerializedName("transferBankName") val transferBankName: String?,
    @SerializedName("bankType") val bankType: String?,
)

data class AliasAccountResponse(
    @SerializedName("aliasUpdate") val aliasUpdate: List<AliasUpdateModel>?,
)

data class AliasUpdateModel(
    @SerializedName("accountNo") val accountNo: String?,
    @SerializedName("aliasName") val aliasName: String?,
    @SerializedName("aliasNameGroups") val aliasNameGroups: String?,
    @SerializedName("cifNo") val cifNo: String?,
    @SerializedName("status") val status: Status?,
)

class AccountSaveQRResponse()

data class AccountImageQRResponse(
    @SerializedName("ibImageList") val ibImageList: List<ImageModel>?,
)

data class ImageModel(
    @SerializedName("content_type") val content_type: String?,
    @SerializedName("enable") val enable: String?,
    @SerializedName("id") val id: String?,
    @SerializedName("image") val image: String?,
    @SerializedName("title_en") val title_en: String?,
    @SerializedName("title_vi") val title_vi: String?,
    @SerializedName("type") val type: String?,
    @SerializedName("weight") val weight: String?,
    @SerializedName("zone") val zone: String?,
)

data class TransactionStatusResponse(
    @SerializedName("data") val data: TransactionStatusModel?,
)

data class TransactionStatusModel(
    @SerializedName("chan") val chan: String?,
    @SerializedName("chanrefno") val chanrefno: String?,
    @SerializedName("datecre") val datecre: String?,
    @SerializedName("efttype") val efttype: String?,
    @SerializedName("hostrefno") val hostrefno: String?,
    @SerializedName("timecre") val timecre: String?,
    @SerializedName("tranStatusDetailList") val tranStatusDetailList: List<StatusDescriptionModel>?,
    @SerializedName("type") val type: String?,
)

data class StatusDescriptionModel(
    @SerializedName("statusCode") val statusCode: String?,
    @SerializedName("statusDesc") val statusDesc: String?,
    @SerializedName("statusView") val statusView: String?,
)

data class AccountFileSavingResponse(
    @SerializedName("file") val file: String?,
    @SerializedName("fileName") val fileName: String?,
)

data class SortModelUI(
    var option: String? = null,
    var value: String? = null,
)

data class SortSelectedUI(
    // loai tai khoan
    var orderInquiry: String? = null,
    // loai sắp xếp
    var orderBy: String? = null,
    // chon ngày
    var orderType: String? = null,
)

data class QRModelUI(
    var accountName: String? = null,
    var accountNo: String? = null,
    var branch: String? = null,
    var qr: Bitmap? = null,
    var amount: String? = null,
    var note: String? = null,
)

data class BackgroundModelUI(
    val id: String?,
    val title: String?,
    val image: Bitmap?,
)

data class DisplayModelUI(
    // title custom
    var title: String? = null,
    var titleColor: Color? = null,
    var titleSize: TextUnit = 14.sp,
    var titleFont: Int = 4,
    // value custom
    var value: String? = null,
    var valueColor: Color? = null,
    var valueSize: TextUnit = 14.sp,
    var valueFont: Int = 4,
    // option
    var icon: Int? = null,
    var modifierValue: Modifier = Modifier,
    var clickDirect: String? = null,
    // description
    var valueDescription: String? = null,
    var isShowLine: Boolean = true,
)

data class AccountInquiryActions(
    var onSortClick: (() -> Unit)? = null,
    var onCompanyClick: (() -> Unit)? = null,
    var onDetailClick: ((AccountModelUI?) -> Unit)? = null,
    var onMoreAction: ((AccountModelUI?) -> Unit)? = null,
    var onCopyClick: ((AccountModelUI?) -> Unit)? = null,
    var onBackClick: (() -> Unit)? = null,
)

data class AccountDetailActions(
    var onQrClick: ((AccountDetailDomain?) -> Unit)? = null,
    var onDetailHistoryClick: ((HistoryDomain?, String?) -> Unit)? = null,
    var onDownloadClick: (() -> Unit)? = null,
    var onFilterClick: (() -> Unit)? = null,
    var onInfoClick: ((AccountDetailDomain?) -> Unit)? = null,
    var onAliasClick: ((String?) -> Unit)? = null,
    var onBackClick: (() -> Unit)? = null,
    var onLoadMore: (() -> Unit)? = null,
)

data class AccountQRActions(
    var onBackClick: (() -> Unit)? = null,
    var onDownloadClick: (() -> Unit)? = null,
    var onShareClick: (() -> Unit)? = null,
    var onCreateClick: ((String?, String?) -> Unit)? = null,
    var onLocalClick: (() -> Unit)? = null,
    var onAvatarClick: (() -> Unit)? = null,
)

data class InquiryUiState(
    var inquiryType: String? = null,
    var tabSelected: Int = 0,
    var isShowCompany: Boolean = false,
    var allCompany: List<SubsidiaryInfoDomain>? = null,
    var selectedCompany: SubsidiaryInfoDomain? = null,
    var hostCif: String? = null,
    var fastAccount: AccountModelUI? = null,
    var isChecker: Boolean = false,
    // sort
    var isShowSort: Boolean = false,
    var sortSelector: SortSelectedUI? = null,
    var sortInit: SortSelectedUI? = null,
    // tai khoan
    var accountLst: List<AccountModelUI>? = null, // tai khoan theo cif
    var accountSortLst: List<AccountModelUI>? = null, // tai khoan da sap xep
    var currencyWithCif: LinkedHashMap<String, List<AccountModelUI>>? = null, // tien te theo cif
    var currencyExpandLst: List<String>? = null, // expand account
    var totalAmount: String? = null, // tong so du theo cif
)

data class DetailUiState(
    var tabSelected: Int = 0,
    var accountDefault: AccountModelUI? = null,
    var account: AccountDetailDomain? = null,
    var alias: AliasUiState? = null,
    var infoLst: List<Any>? = null,
    var filterText: String? = null,
    var currentPage: String = "0",
    var canLoadMore: Boolean = false,
    var isLoadingMore: Boolean = false,
    var isShowFilter: Boolean = false,
    var filterHistory: AccountFilterHistory? = null,
    var isCallHistory: Boolean = false,
    var isCreateQR: Boolean = false,
    var isChecker: Boolean = false,
    var historyOriginLst: List<HistoryDomain>? = null,

)

data class AliasUiState(
    var aliasName: String? = null,
    var aliasNameChange: String? = null,
)

data class HistoryUiState(
    var tabSelected: Int = 0,
    var isAccounting: Boolean = false,
    var historyModel: HistoryDomain? = null,
    var displayLst: List<ItemResult>? = null,
    var accountFrom: TransferResult? = null,
    var accountTo: TransferResult? = null,
    var statusForTransaction: TransactionStatusDomain? = null,
    var statusAccounting: List<StatusDescriptionUI>? = null,

)

data class SearchUiState(
    var inquiryType: String? = null,
    var inquiryName: String? = null,
    var searchText: String? = null,
    var accountLst: List<AccountModelUI>? = null,
)