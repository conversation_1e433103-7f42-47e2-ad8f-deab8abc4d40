package com.vietinbank.core_data.models.request

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_common.constants.Constants

data class TransactionListRequest(
    @SerializedName("orderByAmount") val orderByAmount: String = "",
    @SerializedName("content") val content: String = "",
    @SerializedName("username") val username: String = "",
    @SerializedName("serviceType") val serviceType: String = "",
    @SerializedName("all") val all: String = "",
    @SerializedName("pageNum") val pageNum: String = "",
    @SerializedName("orderByApproveDate") val orderByApproveDate: String = "",
    @SerializedName("pageSize") val pageSize: String = "",
    @SerializedName("tranType") val tranType: String = "",
    @SerializedName("mtId") val mtId: String = "",
) : CommonRequest(Constants.MB_TRANSACTION_LIST)
