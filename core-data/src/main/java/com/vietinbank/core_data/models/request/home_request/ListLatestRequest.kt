package com.vietinbank.core_data.models.request.home_request

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_data.models.request.CommonRequest

data class ListLatestRequest(
    @SerializedName("accountNo")
    val accountNo: String = "",
    @SerializedName("pageSize")
    val pageSize: String = "",
    @SerializedName("pageNum")
    val pageNum: String = "",
    @SerializedName("status")
    val status: String = "",
    @SerializedName("role")
    val role: String = "",
    @SerializedName("roleId")
    val roleId: String = "",
    @SerializedName("username")
    val username: String = "",
) : CommonRequest(Constants.MB_LIST_LATEST)
