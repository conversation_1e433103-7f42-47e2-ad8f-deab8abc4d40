package com.vietinbank.core_data.models.response.ekyc_feature

import com.google.gson.annotations.SerializedName

data class EkycOcrResponse(
    @SerializedName("data") val data: EkycOcrItemResponse?,
)

data class EkycOcrItemResponse(
    @SerializedName("identityNumber") val identityNumber: String?,
    @SerializedName("birthDay") val birthDay: String?,
    @SerializedName("expireDate") val expireDate: String?,
    @SerializedName("provideDate") val provideDate: String?,
    @SerializedName("fullName") val fullName: String?,
    @SerializedName("onboardingUserId") val onboardingUserId: String?,
    @SerializedName("ekycId") val ekycId: String?,
)

data class EkycNFCResponse(
    @SerializedName("data") val data: EkycNFCItemResponse?,
)

data class EkycNFCItemResponse(
    @SerializedName("fullName") val fullName: String?,
    @SerializedName("citizenPid") val citizenPid: String?,
    @SerializedName("oldIdentify") val oldIdentify: String?,
    @SerializedName("birthDate") val birthDate: String?,
    @SerializedName("gender") val gender: String?,
    @SerializedName("nationality") val nationality: String?,
    @SerializedName("religion") val religion: String?,
    @SerializedName("dateProvide") val dateProvide: String?,
    @SerializedName("outOfDate") val outOfDate: String?,
    @SerializedName("ethnic") val ethnic: String?,
    @SerializedName("identifyCharacteristics") val identifyCharacteristics: String?,
    @SerializedName("regPlaceAddress") val regPlaceAddress: String?,
    @SerializedName("homeTown") val homeTown: String?,
)

class EkycConfirmInputResponse()

data class EkycCompareImageResponse(
    @SerializedName("differentDataEkycSTH") val differentDataEkycSTH: DifferentDataEkycSTHItemResponse?,
    @SerializedName("dob") val dob: String?,
    @SerializedName("ethnic") val ethnic: String?,
    @SerializedName("fatherName") val fatherName: String?,
    @SerializedName("fileGTDTT") val fileGTDTT: FileGTDTTItemResponse?,
    @SerializedName("fullName") val fullName: String?,
    @SerializedName("gender") val gender: String?,
    @SerializedName("homeTown") val homeTown: String?,
    @SerializedName("idExpiryDate") val idExpiryDate: String?,
    @SerializedName("idIssuedDate") val idIssuedDate: String?,
    @SerializedName("idIssuedLocation") val idIssuedLocation: String?,
    @SerializedName("idNumber") val idNumber: String?,
    @SerializedName("identityCharacteristics") val identityCharacteristics: String?,
    @SerializedName("motherName") val motherName: String?,
    @SerializedName("nationality") val nationality: String?,
    @SerializedName("oldIdNumber") val oldIdNumber: String?,
    @SerializedName("regPlaceAddress") val regPlaceAddress: String?,
    @SerializedName("religion") val religion: String?,
)

data class FileGTDTTItemResponse(
    @SerializedName("file") val file: String?,
    @SerializedName("fileName") val fileName: String?,
)

data class DifferentDataEkycSTHItemResponse(
    @SerializedName("dateOfBirth") val dateOfBirth: String?,
    @SerializedName("dateOfExpiry") val dateOfExpiry: String?,
    @SerializedName("dateOfIssuance") val dateOfIssuance: String?,
    @SerializedName("gender") val gender: String?,
    @SerializedName("idCard") val idCard: String?,
    @SerializedName("idType") val idType: String?,
    @SerializedName("issueLocation") val issueLocation: String?,
    @SerializedName("name") val name: String?,
    @SerializedName("nationality") val nationality: String?,
    @SerializedName("religion") val religion: String?,
)