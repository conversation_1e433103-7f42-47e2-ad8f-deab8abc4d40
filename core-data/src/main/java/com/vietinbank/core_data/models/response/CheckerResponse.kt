package com.vietinbank.core_data.models.response

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_data.models.base.Status
import com.vietinbank.core_domain.models.checker.ItemSubTransactionDetailDomain
import java.math.BigDecimal

/**
 * Created by vandz on 10/3/25.
 */

data class GetTransactionListResponseData(
    @SerializedName("status") val status: Status = Status(code = "", message = "", subCode = ""),
    @SerializedName("countTransactionApprove") val countTransactionApprove: String?,
    @SerializedName("countTransaction") val countTransaction: String?,
    @SerializedName("serviceType") val serviceType: String?,
    @SerializedName("mtIds") val mtIds: ArrayList<String>?,
    @SerializedName("subTranTypeList") val subTranTypeList: ArrayList<SubTranTypeListEntity>?,
    @SerializedName("transactions") val transactions: ArrayList<TransactionEntity>?,
    @SerializedName("subMtIds") val subMtIds: ArrayList<String>?,
)

data class SubTranTypeListEntity(
    @SerializedName("count_detail") val count_detail: String?,
    @SerializedName("count_transaction") val count_transaction: String?,
    @SerializedName("count_valid_transaction") val count_valid_transaction: String?,
    @SerializedName("groupType") val groupType: String?,
    @SerializedName("servicetype") val servicetype: String?,
    @SerializedName("servicetypename") val servicetypename: String?,
    @SerializedName("tranType") val tranType: String?,
    @SerializedName("functionId") val functionId: String?,
)

class TransactionEntity {
    @SerializedName("accruedInterest")
    val accruedInterest: String? = null

    @SerializedName("activityLogs")
    val activityLogs: ActivityLogEntity? = null

    @SerializedName("amount")
    val amount: String? = null

    @SerializedName(value = "bulkID", alternate = ["bulkId"])
    val bulkID: String? = null

    @SerializedName("cardNumber")
    val cardNumber: String? = null

    @SerializedName("cifno")
    val cifno: String? = null

    @SerializedName("createdDate")
    val createdDate: String? = null

    @SerializedName("currency")
    val currency: String? = null

    @SerializedName("current")
    val current: String? = null

    @SerializedName("debitCurrency")
    val debitCurrency: String? = null

    @SerializedName("exchangeRate")
    val exchangeRate: String? = null

    @SerializedName("feeAccountNo")
    val feeAccountNo: String? = null

    @SerializedName("feeAmount")
    val feeAmount: String? = null

    @SerializedName("feeVat")
    val feeVat: String? = null

    @SerializedName("feePayMethod")
    val feePayMethod: String? = null

    @SerializedName("feePayMethodDesc")
    val feePayMethodDesc: String? = null

    @SerializedName("feePayMethodDesc1")
    val feePayMethodDesc1: String? = null

    @SerializedName("feeType")
    val feeType: String? = null

    @SerializedName("fileName")
    val fileName: String? = null

    @SerializedName("fromAccountNo")
    val fromAccountNo: String? = null

    @SerializedName("groupType")
    val groupType: String? = null

    @SerializedName("invoiceId")
    val invoiceId: String? = null

    @SerializedName("maturityDate")
    val maturityDate: String? = null

    @SerializedName("mtId")
    val mtId: String? = null

    @SerializedName("process_status")
    val process_status: String? = null

    @SerializedName("processDate")
    val processDate: String? = null

    @SerializedName("process_time")
    val process_time: String? = null

    @SerializedName("providerCode")
    val providerCode: String? = null

    @SerializedName("providerName")
    val providerName: String? = null

    @SerializedName("provinceID")
    val provinceID: String? = null

    @SerializedName("provinceName")
    val provinceName: String? = null

    @SerializedName("receiveBank")
    val receiveBank: String? = null

    @SerializedName("receiveBankName")
    val receiveBankName: String? = null

    @SerializedName("receiveName")
    val receiveName: String? = null

    @SerializedName("refno")
    val refno: String? = null

    @SerializedName("remark")
    val remark: String? = null

    @SerializedName("serviceId")
    val serviceId: String? = null

    @SerializedName("serviceType")
    val serviceType: String? = null

    @SerializedName("status")
    val status: String? = null

    @SerializedName("statusCode")
    val statusCode: String? = null

    @SerializedName("statusName")
    val statusName: String? = null

    @SerializedName("toAccountNo")
    val toAccountNo: String? = null

    @SerializedName("totalAmount")
    val totalAmount: String? = null

    @SerializedName("totalTrxNo")
    val totalTrxNo: String? = null

    @SerializedName(value = "tranType", alternate = ["trxType"])
    var tranType: String? = null

    @SerializedName("tranTypeName")
    val tranTypeName: String? = null

    @SerializedName("valueDate")
    val valueDate: String? = null

    @SerializedName("verifiedDate")
    val verifiedDate: String? = null

    @SerializedName("taxType")
    val taxType: String? = null

    @SerializedName("interestRate")
    val interestRate: String? = null

    @SerializedName("payMethod")
    val payMethod: String? = null

    @SerializedName("payname")
    val payname: String? = null

    @SerializedName("payCode")
    val payCode: String? = null

    @SerializedName("payAddress")
    val payAddress: String? = null

    @SerializedName("fromAccountName")
    val fromAccountName: String? = null

    @SerializedName("branchName")
    val branchName: String? = null

    @SerializedName("areaCode")
    val areaCode: String? = null

    @SerializedName("areaName")
    val areaName: String? = null

    @SerializedName("treasuryCode")
    val treasuryCode: String? = null

    @SerializedName("treasuryName")
    val treasuryName: String? = null

    @SerializedName("collectingAccount")
    val collectingAccount: String? = null

    @SerializedName("bucode")
    val bucode: String? = null

    @SerializedName("buName")
    val buName: String? = null

    @SerializedName("oficode")
    val oficode: String? = null

    @SerializedName("ofiName")
    val ofiName: String? = null

    @SerializedName("uid")
    val uid: String? = null

    @SerializedName("items")
    val items: List<ItemSubTransactionDetailDomain>? = null

    @SerializedName("chapterCode")
    val chapterCode: String? = null

    @SerializedName("declareNumber")
    val declareNumber: String? = null

    @SerializedName("declareDate")
    val declareDate: String? = null

    @SerializedName("ieType")
    val ieType: String? = null

    @SerializedName("ieName")
    val ieName: String? = null

    @SerializedName("total")
    val total: String? = null

    @SerializedName("totalFee")
    val totalFee: String? = null

    @SerializedName("referenceNo")
    val referenceNo: String? = null

    @SerializedName("collectionAgencyCode")
    val collectionAgencyCode: String? = null

    @SerializedName("collectionAgencyName")
    val collectionAgencyName: String? = null

    @SerializedName("midBankCode")
    val midBankCode: String? = null

    @SerializedName("midBankName")
    val midBankName: String? = null

    @SerializedName("debitAmount")
    val debitAmount: String? = null

    @SerializedName("availableBalance")
    val availableBalance: String? = null

    @SerializedName("pref")
    val pref: TransactionEntity? = null

    @SerializedName("districtCode")
    val districtCode: String? = null

    @SerializedName("districtName")
    val districtName: String? = null

    @SerializedName("docId")
    val docId: String? = null

    @SerializedName("docNum")
    val docNum: String? = null

    @SerializedName("docSign")
    val docSign: String? = null

    @SerializedName("docDate")
    val docDate: String? = null

    @SerializedName("subsect")
    val subsect: String? = null

    @SerializedName("debitRate1")
    val debitRate1: String? = null

    @SerializedName("debitAmtByRate1")
    val debitAmtByRate1: String? = null

    @SerializedName("debitRate2")
    val debitRate2: String? = null

    @SerializedName("debitAmtByRate2")
    val debitAmtByRate2: String? = null

    @SerializedName("exchangeRateType")
    val exchangeRateType: String? = null

    @SerializedName("exchangeRateTypeDesc")
    val exchangeRateTypeDesc: String? = null

    @SerializedName("listFile")
    val listFile: List<FileTransaction>? = null

    @SerializedName("listFile2")
    val listFile2: List<FileTransaction>? = null

    @SerializedName("processTime")
    val processTime: String? = null

    @SerializedName("creator")
    val creator: String? = null

    @SerializedName("count")
    val count: String? = null

    @SerializedName("voucherDate")
    val voucherDate: String? = null

    @SerializedName("voucherNumber")
    val voucherNumber: String? = null

    @SerializedName("voucherSymbol")
    val voucherSymbol: String? = null

    @SerializedName("aliasName")
    val aliasName: String? = null

    @SerializedName("id")
    val id: String? = null

    // giai ngan online
    @SerializedName("disbursementDate")
    val disbursementDate: String? = null

    @SerializedName("amountProposed")
    val amountProposed: String? = null

    @SerializedName("currencyCode")
    val currencyCode: String? = null

    @SerializedName("manager")
    val manager: String? = null

    @SerializedName("companyName")
    val companyName: String? = null

    @SerializedName("representativeName")
    val representativeName: String? = null

    @SerializedName("authority")
    val authority: String? = null

    @SerializedName("authorityNumber")
    val authorityNumber: String? = null

    @SerializedName("contractNo")
    val contractNo: String? = null

    @SerializedName("contractDate")
    val contractDate: String? = null

    @SerializedName("loanLimit")
    val loanLimit: String? = null

    @SerializedName("disbursedAmount")
    val disbursedAmount: String? = null

    @SerializedName("disbursedBranchCode")
    val disbursedBranchCode: String? = null

    @SerializedName("disbursedBranchName")
    val disbursedBranchName: String? = null

    @SerializedName("transferAmount")
    val transferAmount: String? = null

    @SerializedName("transferLoanAmount")
    val transferLoanAmount: String? = null

    @SerializedName("transferLoanAmountCal")
    val transferLoanAmountCal: String? = null

    @SerializedName("transferCurType")
    val transferCurType: String? = null

    @SerializedName("transferLoanCurType")
    val transferLoanCurType: String? = null

    @SerializedName("purpose")
    val purpose: String? = null

    @SerializedName("loanRate")
    val loanRate: String? = null

    @SerializedName("loanTerm")
    val loanTerm: String? = null

    @SerializedName("autoRepayment")
    val autoRepayment: String? = null

    @SerializedName("accountId")
    val accountId: String? = null

    @SerializedName("firstPaymentDate")
    val firstPaymentDate: String? = null

    @SerializedName("dataFx")
    val dataFx: List<DataFXEntity>? = null

    @SerializedName("fileGNN")
    val fileGNN: List<FileTransaction>? = null

    @SerializedName("listDocumentFileVND")
    val listDocumentFileVND: List<FileTransaction>? = null

    @SerializedName("preferenceNo")
    val preferenceNo: String? = null

    @SerializedName("dueDate")
    val dueDate: String? = null

    // bao lanh
    @SerializedName("host_mtid")
    val host_mtid: String? = null

    @SerializedName("releaseDate")
    val releaseDate: String? = null

    @SerializedName("cifName")
    val cifName: String? = null

    @SerializedName("dateRange")
    val dateRange: String? = null

    @SerializedName("issuesBy")
    val issuesBy: String? = null

    @SerializedName("reasonChange")
    val reasonChange: String? = null

    @SerializedName("feeDesc")
    val feeDesc: String? = null

    @SerializedName("depositAccount")
    val depositAccount: String? = null

    @SerializedName("depositAmount")
    val depositAmount: String? = null

    @SerializedName("depositCurrency")
    val depositCurrency: String? = null

    @SerializedName("beneficiaryName")
    val beneficiaryName: String? = null

    @SerializedName("beneficiaryAddress")
    val beneficiaryAddress: String? = null

    @SerializedName("beneficiaryCode")
    val beneficiaryCode: String? = null

    @SerializedName("receiverIssuedBy")
    val receiverIssuedBy: String? = null

    @SerializedName("receiverDateRange")
    val receiverDateRange: String? = null

    @SerializedName("guaranteePurpose")
    val guaranteePurpose: String? = null

    @SerializedName("effectiveStartDate")
    val effectiveStartDate: String? = null

    @SerializedName("effectiveEndDate")
    val effectiveEndDate: String? = null

    @SerializedName("messageType")
    val messageType: String? = null

    @SerializedName("message")
    val message: String? = null

    @SerializedName("guaranteeCode")
    val guaranteeCode: String? = null

    @SerializedName("branch")
    val branch: String? = null

    @SerializedName("issueTypeDesc")
    val issueTypeDesc: String? = null

    @SerializedName("issueType")
    val issueType: String? = null

    @SerializedName("conditionPerform")
    val conditionPerform: String? = null

    @SerializedName("conditionReduction")
    val conditionReduction: String? = null

    @SerializedName("sendTypeDesc")
    val sendTypeDesc: String? = null

    @SerializedName("sendTypeCmnd")
    val sendTypeCmnd: String? = null

    @SerializedName("sendTypeNo")
    val sendTypeNo: String? = null

    @SerializedName("sendTypeDate")
    val sendTypeDate: String? = null

    @SerializedName("sendTypeBy")
    val sendTypeBy: String? = null

    @SerializedName("feeTimeDesc")
    val feeTimeDesc: String? = null

    @SerializedName("feeTime")
    val feeTime: String? = null

    @SerializedName("measure")
    val measure: String? = null

    @SerializedName("measureAcct")
    val measureAcct: String? = null

    @SerializedName("measureAmount")
    val measureAmount: String? = null

    @SerializedName("measureCurrency")
    val measureCurrency: String? = null

    @SerializedName("measureDesc")
    val measureDesc: String? = null

    @SerializedName("language")
    val language: String? = null

    @SerializedName("biCodeInfo")
    val biCodeInfo: BiCodeModel? = null

    @SerializedName("documentType")
    val documentType: String? = null

    @SerializedName("documentTypeName")
    val documentTypeName: String? = null

    @SerializedName("listFiles")
    val listFiles: List<FileTransaction>? = null

    @SerializedName("ibFile")
    val ibFile: List<FileTransaction>? = null

    @SerializedName("amount2")
    val amount2: String? = null

    @SerializedName("amount3")
    val amount3: String? = null

    @SerializedName("currency2")
    val currency2: String? = null

    @SerializedName("currency3")
    val currency3: String? = null

    @SerializedName("amountChange")
    val amountChange: String? = null

    @SerializedName("amountChange2")
    val amountChange2: String? = null

    @SerializedName("amountChange3")
    val amountChange3: String? = null

    @SerializedName("effectiveEndDateChange")
    val effectiveEndDateChange: String? = null

    @SerializedName("effectiveStartDateChange")
    val effectiveStartDateChange: String? = null

    @SerializedName("guaranteeCommitContentChange")
    val guaranteeCommitContentChange: String? = null

    @SerializedName("guaranteeCommitContent")
    val guaranteeCommitContent: String? = null

    @SerializedName("subType")
    val subType: String? = null

    @SerializedName("headQuarters")
    val headQuarters: String? = null

    @SerializedName("disbursementPurposeCode")
    val disbursementPurposeCode: String? = null

    @SerializedName("companyCode")
    val companyCode: String? = null

    @SerializedName("feeAccount")
    val feeAccount: String? = null

    @SerializedName("beneficiaryAccount")
    val beneficiaryAccount: String? = null

    @SerializedName("contentTransfer")
    val contentTransfer: String? = null

    @SerializedName("file")
    val file: FileTransaction? = null

    @SerializedName("transferListFile")
    val transferListFile: List<FileTransaction>? = null

    @SerializedName("doTransferVNDList")
    val doTransferVNDList: List<TransferVNDModel>? = null

    @SerializedName("transferListFileVND")
    val transferListFileVND: FileTransaction? = null

    @SerializedName("authorizationDate")
    val authorizationDate: String? = null

    @SerializedName("typeChange")
    val typeChange: String? = null

    @SerializedName("sendType")
    val sendType: String? = null

    @SerializedName("companyAddress")
    val companyAddress: String? = null

    @SerializedName("disbursementPurposeName")
    val disbursementPurposeName: String? = null

    @SerializedName("countTransaction")
    val countTransaction: String? = null

    @SerializedName("idNumber")
    val idNumber: String? = null

    @SerializedName("chapterName")
    val chapterName: String? = null

    @SerializedName("assetCode")
    val assetCode: String? = null

    @SerializedName("machineNo")
    val machineNo: String? = null

    @SerializedName("machineFeatures")
    val machineFeatures: String? = null

    @SerializedName("propertyAdd")
    val propertyAdd: String? = null

    @SerializedName("collectAcctName")
    val collectAcctName: String? = null

    @SerializedName("subTranItemList")
    val subTranItemList: List<SubTranItemListModel>? = null

    @SerializedName("createDateDesc")
    val createDateDesc: String? = null

    @SerializedName("verifiedDateDesc")
    val verifiedDateDesc: String? = null

    @SerializedName("messageTypeDes")
    val messageTypeDes: String? = null

    @SerializedName("introducer")
    val introducer: String? = null

    @SerializedName("note")
    val note: String? = null

    @SerializedName("purposeTransferName")
    val purposeTransferName: String? = null

    @SerializedName("feeTypeName")
    val feeTypeName: String? = null

    @SerializedName("fromAccount2")
    val fromAccount2: String? = null

    @SerializedName("fromAccountName2")
    val fromAccountName2: String? = null

    @SerializedName("benName")
    val benName: String? = null

    @SerializedName("benCountry")
    val benCountry: String? = null

    @SerializedName("benWard")
    val benWard: String? = null

    @SerializedName("benStreet")
    val benStreet: String? = null

    @SerializedName("benBankName")
    val benBankName: String? = null

    @SerializedName("benBankCountry")
    val benBankCountry: String? = null

    @SerializedName("benBankWard")
    val benBankWard: String? = null

    @SerializedName("benBankStreet")
    val benBankStreet: String? = null

    @SerializedName("remitterName")
    val remitterName: String? = null

    @SerializedName("remitterCountry")
    val remitterCountry: String? = null

    @SerializedName("remitterDistrict")
    val remitterDistrict: String? = null

    @SerializedName("remitterWard")
    val remitterWard: String? = null

    @SerializedName("remitterStreet")
    val remitterStreet: String? = null

    @SerializedName("benDistrict")
    val benDistrict: String? = null

    @SerializedName("finalBenName")
    val finalBenName: String? = null

    @SerializedName("finalBenCountry")
    val finalBenCountry: String? = null

    @SerializedName("finalBenDistrict")
    val finalBenDistrict: String? = null

    @SerializedName("finalBenWard")
    val finalBenWard: String? = null

    @SerializedName("finalBenStreet")
    val finalBenStreet: String? = null

    @SerializedName("lcReturnDate")
    val lcReturnDate: String? = null

    @SerializedName("benBankDistrict")
    val benBankDistrict: String? = null

    @SerializedName("midBankCountry")
    val midBankCountry: String? = null

    @SerializedName("midBankDistrict")
    val midBankDistrict: String? = null

    @SerializedName("midBankWard")
    val midBankWard: String? = null

    @SerializedName("midBankStreet")
    val midBankStreet: String? = null

    @SerializedName("rateMsgWarning")
    val rateMsgWarning: String? = null

    @SerializedName("conditionLink")
    val conditionLink: String? = null

    // cap lai mk
    @SerializedName("accountPayment")
    val accountPayment: String? = null

    @SerializedName("paymentAccount")
    val paymentAccount: String? = null

    @SerializedName("idCard")
    val idCard: String? = null

    @SerializedName("typeSend")
    val typeSend: String? = null

    @SerializedName("guaranteeBalances")
    val guaranteeBalances: String? = null

    @SerializedName("trxTypeName")
    val trxTypeName: String? = null

    @SerializedName("mtIdList")
    val mtIdList: List<String>? = null

    // Tra soát NSNN. ha tang
    @SerializedName("paycode")
    val paycode: String? = null

    @SerializedName("payadd")
    val payadd: String? = null

    @SerializedName("taxMethodName")
    val taxMethodName: String? = null

    @SerializedName("collectionAccountNo")
    val collectionAccountNo: String? = null

    @SerializedName("collectionAccountName")
    val collectionAccountName: String? = null

    @SerializedName("collectAgencyCode")
    val collectAgencyCode: String? = null

    @SerializedName("ofiname")
    val ofiname: String? = null

    @SerializedName("buname")
    val buname: String? = null

    @SerializedName("chapCode")
    val chapCode: String? = null

    @SerializedName("customTableNo")
    val customTableNo: String? = null

    @SerializedName("customDate")
    val customDate: String? = null

    @SerializedName("chapName")
    val chapName: String? = null

    @SerializedName("collectAgencyName")
    val collectAgencyName: String? = null
}

data class TransferVNDModel(
    @SerializedName("bankName") val bankName: String?,
    @SerializedName("account") val account: String?,
    @SerializedName("accountName") val accountName: String?,
    @SerializedName("amountPayment") val amountPayment: String?,
    @SerializedName("description") val description: String?,
    @SerializedName("citadName") val citadName: String?,
)

data class BiCodeModel(
    @SerializedName("bicCode") val bicCode: String?,
    @SerializedName("bankName") val bankName: String?,
    @SerializedName("bankAddr") val bankAddr: String?,
)

data class FileTransaction(
    @SerializedName("id") val id: String?,
    @SerializedName("mtId") val mtId: String?,
    @SerializedName("fileName") val fileName: String?,
    @SerializedName("fileAttach") val fileAttach: String?,
    @SerializedName("createDate") val createDate: String?,
    @SerializedName("creator") val creator: String?,
    @SerializedName("fileType") val fileType: String?,
    @SerializedName("fileData") val fileData: String?,
    @SerializedName("cifNo") val cifNo: String?,
    @SerializedName(value = "objectId", alternate = ["objectID"]) val objectId: String?,
    @SerializedName("objectType") val objectType: String?,
    @SerializedName("totalTrxNo") val totalTrxNo: String?,
    @SerializedName("totalAmount") val totalAmount: String?,
    @SerializedName("channel") val channel: String?,
    @SerializedName("appMess") val appMess: String?,
    @SerializedName("appStatus") val appStatus: String?,
    @SerializedName("typeDownload") val typeDownload: String?,
    @SerializedName("isViewSlFile") val isViewSlFile: String?,
    @SerializedName("typeName") val typeName: String?,
    @SerializedName("attachmentType") val attachmentType: String?,
    @SerializedName("file") val file: String?,
)

data class CountPendingResponseData(
    @SerializedName("status") val status: Status = Status(code = "", message = "", subCode = ""),
    @SerializedName("total_count") val total_count: String?,
    @SerializedName("countTransactionList") val countTransactionList: ArrayList<SubTranTypeListEntity>?,
)

data class MobileConfigLastResponseData(
    @SerializedName("configList") val configList: ArrayList<SubconfigListEntity>?,
    @SerializedName("totalLastModified") val totalLastModified: Long?,
)

data class SubconfigListEntity(
    @SerializedName("id") val id: String?,
    @SerializedName("code") val code: String?,
    @SerializedName("type") val type: String?,
    @SerializedName("value") val value: String?,
    @SerializedName("description") val description: String?,
    @SerializedName("status") val status: String?,
    @SerializedName("lastModified") val lastModified: String?,
)

data class PreApproveResponseData(
    @SerializedName("methodList") val methodList: ArrayList<MethodEntity>?,
    @SerializedName("tranSTHId") val tranSTHId: String?,
    @SerializedName("provisionalAccount") val provisionalAccount: ArrayList<ProvisionalAccountEntity>?,
    @SerializedName("requestId") val requestId: String?,
    @SerializedName("sessionId") val sessionId: String?,
    @SerializedName("totalFee") val totalFee: String?,
    @SerializedName("listGroupTran") val listGroupTran: ArrayList<String>?,
    @SerializedName("transactions") var transactionPres: ArrayList<PreTransactionEntity>?,
    @SerializedName("countTransactionFX") var countTransactionFX: String?,
    @SerializedName("countTransactionFXR") var countTransactionFXR: String?,
    @SerializedName("totalAmountFX") var totalAmountFX: Map<String, BigDecimal>?,
    @SerializedName("totalAmountFXR") var totalAmountFXR: Map<String, BigDecimal>?,
)

data class MethodEntity(
    @SerializedName("method") val method: String?,
    @SerializedName("isActivated") val isActivated: String?,
    @SerializedName("description") val description: String?,
)

data class ActivityLogEntity(
    @SerializedName("bnkProcess") var bnkProcess: MutableList<BnkProcessEntity>?,
    @SerializedName("createdBy") var createdBy: CreatedByEntity?,
    @SerializedName("verifiedBy") var verifiedBy: MutableList<VerifiedByEntity>?,
)

data class BnkProcessEntity(
    @SerializedName("processDate") var processDate: String?,
    @SerializedName("userfullname") var userfullname: String?,
    @SerializedName("username") var username: String?,
)

data class CreatedByEntity(
    @SerializedName("processDate") var processDate: String?,
    @SerializedName("userfullname") var userfullname: String?,
    @SerializedName("username") var username: String?,
)

data class VerifiedByEntity(
    @SerializedName("processDate") var processDate: String?,
    @SerializedName("userfullname") var userfullname: String?,
    @SerializedName("username") var username: String?,
)

data class ProvisionalAccountEntity(
    @SerializedName("accountNo") val accountNo: String?,
    @SerializedName("balance") val balance: String?,
)

data class PreTransactionEntity(
    @SerializedName("mtId") val mtId: String?,
    @SerializedName("fromAccountNo") val fromAccountNo: String?,
    @SerializedName("toAccountNo") val toAccountNo: String?,
    @SerializedName("receiveName") val receiveName: String?,
    @SerializedName("amount") val amount: String?,
    @SerializedName("currency") val currency: String?,
    @SerializedName("createdDate") val createdDate: String?,
    @SerializedName("status") val status: String?,
    @SerializedName("statusName") val statusName: String?,
    @SerializedName("remark") val remark: String?,
    @SerializedName("tranType") val tranType: String?,
    @SerializedName("tranTypeName") val tranTypeName: String?,
    @SerializedName("feeAmount") val feeAmount: String?,
    @SerializedName("activityLogs") val activityLogs: ActivityLogEntity?,
    @SerializedName("receiveBank") val receiveBank: String?,
    @SerializedName("receiveBankName") val receiveBankName: String?,
    @SerializedName("debitAmount2") val debitAmount2: String?,
    @SerializedName("groupName") val groupName: String?,
    @SerializedName("debitAmount1") val debitAmount1: String?,
    @SerializedName("idCard") val idCard: String?,
    @SerializedName("valueDate") val valueDate: String?,
    @SerializedName("debitFxAmount2") val debitFxAmount2: String?,
    @SerializedName("interestRate2") val interestRate2: String?,
    @SerializedName("debitFxAmount1") val debitFxAmount1: String?,
    @SerializedName("branchName") val branchName: String?,
    @SerializedName("interestRate1") val interestRate1: String?,
    @SerializedName("originalAmount") val originalAmount: String?,
    @SerializedName("settlementAmount") val settlementAmount: String?,
    @SerializedName("accruedInterest") val accruedInterest: String?,
    @SerializedName("penaltyInterest") val penaltyInterest: String?,
    @SerializedName("issuedDate") val issuedDate: String?,
    @SerializedName("maturityDate") val maturityDate: String?,
    @SerializedName("interestRate") val interestRate: String?,
    @SerializedName("rate") val rate: String?,
    @SerializedName("paidAmount") val paidAmount: String?,
    @SerializedName("principalAmount") val principalAmount: String?,
    @SerializedName("interestAmount") val interestAmount: String?,
    @SerializedName("penaltyAmount") val penaltyAmount: String?,
    @SerializedName("paycode") val paycode: String?,
    @SerializedName("payname") val payname: String?,
    @SerializedName("payadd") val payadd: String?,
    @SerializedName("taxTableNo") val taxTableNo: String?,
    @SerializedName("provinceName") val provinceName: String?,
    @SerializedName("bankCode") val bankCode: String?,
    @SerializedName("collectionAccountNo") val collectionAccountNo: String?,
    @SerializedName("collectionOrg") val collectionOrg: String?,
    @SerializedName("customTableNo") val customTableNo: String?,
    @SerializedName("customDate") val customDate: String?,
    @SerializedName("customType") val customType: String?,
    @SerializedName("taxType") val taxType: String?,
    @SerializedName("taxTypeName") val taxTypeName: String?,
    @SerializedName("balance") val balance: String?,
    @SerializedName("groupType") val groupType: String?,
    @SerializedName("serviceName") val serviceName: String?,
    @SerializedName("serviceId") val serviceId: String?,
    @SerializedName("renewal") val renewal: String?,
    @SerializedName("treasuryCode") val treasuryCode: String?,
    @SerializedName("treasuryName") val treasuryName: String?,
    @SerializedName("provinceCode") val provinceCode: String?,
    @SerializedName("areaCode") val areaCode: String?,
    @SerializedName("bucode") val bucode: String?,
    @SerializedName("oficode") val oficode: String?,
    @SerializedName("ieType") val ieType: String?,
    @SerializedName("exchangeRate") val exchangeRate: String?,
    @SerializedName("creditCurrency") val creditCurrency: String?,
    @SerializedName("creditAmount") val creditAmount: String?,
    @SerializedName("addressOfPayer") val addressOfPayer: String?,
    @SerializedName("addressOfReceiver") val addressOfReceiver: String?,
    @SerializedName("alertPhone1") val alertPhone1: String?,
    @SerializedName("alertPhone2") val alertPhone2: String?,
    @SerializedName("approverCard") val approverCard: String?,
    @SerializedName("beneficiaryNo") val beneficiaryNo: String?,
    @SerializedName("billOwnerAddress") val billOwnerAddress: String?,
    @SerializedName("billOwnerCode") val billOwnerCode: String?,
    @SerializedName("billOwnerName") val billOwnerName: String?,
    @SerializedName("billOwnerPhone") val billOwnerPhone: String?,
    @SerializedName("bulkID") val bulkID: String?,
    @SerializedName("cardName") val cardName: String?,
    @SerializedName("cardNumber") val cardNumber: String?,
    @SerializedName("cardType") val cardType: String?,
    @SerializedName("cifno") val cifno: String?,
    @SerializedName("createTranDate") val createTranDate: String?,
    @SerializedName("creatorCard") val creatorCard: String?,
    @SerializedName("currenciesPair") val currenciesPair: String?,
    @SerializedName("current") val current: String?,
    @SerializedName("customerAddress") val customerAddress: String?,
    @SerializedName("customerCode") val customerCode: String?,
    @SerializedName("customerName") val customerName: String?,
    @SerializedName("customerPhone") val customerPhone: String?,
    @SerializedName("debitCurrency") val debitCurrency: String?,
    @SerializedName("expiredDate") val expiredDate: String?,
    @SerializedName("feePayMethod") val feePayMethod: String?,
    @SerializedName("fileName") val fileName: String?,
    @SerializedName("frequencyLimit") val frequencyLimit: String?,
    @SerializedName("frequencyOnDay") val frequencyOnDay: String?,
    @SerializedName("frequencyOnFourDay") val frequencyOnFourDay: String?,
    @SerializedName("groupId") val groupId: String?,
    @SerializedName("identificationNo") val identificationNo: String?,
    @SerializedName("invoiceId") val invoiceId: String?,
    @SerializedName("issuedPlace") val issuedPlace: String?,
    @SerializedName("loanType") val loanType: String?,
    @SerializedName("nextPaymentDay") val nextPaymentDay: String?,
    @SerializedName("paymentAmount") val paymentAmount: String?,
    @SerializedName("paymentDate") val paymentDate: String?,
    @SerializedName("prePrincipal") val prePrincipal: String?,
    @SerializedName("prisentStatus") val prisentStatus: String?,
    @SerializedName("process_status") val process_status: String?,
    @SerializedName("process_time") val process_time: String?,
    @SerializedName("providerCode") val providerCode: String?,
    @SerializedName("providerName") val providerName: String?,
    @SerializedName("provinceID") val provinceID: String?,
    @SerializedName("receiveDept") val receiveDept: String?,
    @SerializedName("receiveDeptName") val receiveDeptName: String?,
    @SerializedName("repaymentType") val repaymentType: String?,
    @SerializedName("serviceType") val serviceType: String?,
    @SerializedName("siID") val siID: String?,
    @SerializedName("siName") val siName: String?,
    @SerializedName("siOrgName") val siOrgName: String?,
    @SerializedName("siOrgcode") val siOrgcode: String?,
    @SerializedName("statusCard") val statusCard: String?,
    @SerializedName("territoryIn") val territoryIn: String?,
    @SerializedName("territoryInLimit") val territoryInLimit: String?,
    @SerializedName("territoryOut") val territoryOut: String?,
    @SerializedName("territoryOutlimit") val territoryOutlimit: String?,
    @SerializedName("totalAmount") val totalAmount: String?,
    @SerializedName("totalLimit") val totalLimit: String?,
    @SerializedName("totalLimitOnDay") val totalLimitOnDay: String?,
    @SerializedName("totalLimitOnFourDay") val totalLimitOnFourDay: String?,
    @SerializedName("totalTrxNo") val totalTrxNo: String?,
    @SerializedName("tranNumber") val tranNumber: String?,
    @SerializedName("tranSubType") val tranSubType: String?,
    @SerializedName("verifiedDate") val verifiedDate: String?,
    @SerializedName("amount2") val amount2: String?,
    @SerializedName("amount3") val amount3: String?,
    @SerializedName("currency2") val currency2: String?,
    @SerializedName("currency3") val currency3: String?,
)

data class GenSOTPTransCodeResponseData(
    @SerializedName("requestId") val requestId: String?,
    @SerializedName("sessionId") val sessionId: String?,
    @SerializedName("userId") val userId: String?,
    @SerializedName("transactionId") val transactionId: String?,
    @SerializedName("transactionData") val transactionData: String?,
    @SerializedName("transactionStatusId") val transactionStatusId: String?,
    @SerializedName("isOnline") val isOnline: String?,
    @SerializedName("challenge") val challenge: String?,
    @SerializedName("callBackUrl") val callBackUrl: String?,
    @SerializedName("isPush") val isPush: String?,
)

data class ApproveResponseData(
    @SerializedName("status") val status: Status = Status(code = "", message = "", subCode = ""),
    @SerializedName("totalFee") val totalFee: String?,
    @SerializedName("tranDate") val tranDate: String?,
    @SerializedName("defineCreatedDate") val defineCreatedDate: String?,
    @SerializedName("username") val userName: String?,
    @SerializedName("transactions") val transactions: ArrayList<ApproveTransaction>?,
    @SerializedName("verifySignInfo") val verifySignInfo: SignInfoEntity?,
)

data class SignInfoEntity(
    @SerializedName("transactionId") val transactionId: String?,
    @SerializedName("androidKey") val androidKey: String?,
    @SerializedName("iosKey") val iosKey: String?,
    @SerializedName("tranCode") val tranCode: String?,
    @SerializedName("expiredIn") val expiredIn: String?,
    @SerializedName("custCode") val custCode: String?,
    @SerializedName("nextApprovers") val nextApprovers: ArrayList<NextApproversList>?,
)

data class NextApproversList(
    @SerializedName("status") var status: String?,
    @SerializedName("email") var email: String?,
    @SerializedName("mobile") var mobile: String?,

    @SerializedName("fullname") var fullname: String?,

    @SerializedName("title") var title: String?,

    @SerializedName("username") var username: String?,

    @SerializedName("idtype") var idtype: String?,

    @SerializedName("idnumber") var idnumber: String?,

    @SerializedName("approverlevel") var approverlevel: Int?,

    @SerializedName("nationality") var nationality: String?,

    @SerializedName("rsaserial") var rsaserial: String?,

    @SerializedName("birthdate") var birthdate: String?,

    @SerializedName("lastlogon") var lastlogon: String?,

    @SerializedName("startdate") var startdate: String?,

    @SerializedName("endate") var endate: String?,

    @SerializedName("gender") var gender: String?,

    @SerializedName("version") var version: Int?,

    @SerializedName("faillogon") var faillogon: Int?,

    @SerializedName("enterpriseid") var enterpriseid: String?,

    @SerializedName("mailalert") var mailalert: Int?,

    @SerializedName("grouptype") var grouptype: String?,

    @SerializedName("rsaprofile") var rsaprofile: String?,

    @SerializedName("keypassid") var keypassid: String?,

    @SerializedName("keypassprofile") var keypassprofile: String?,

    @SerializedName("keypasswaitactive") var keypasswaitactive: String?,

    @SerializedName("keypasssoftotp") var keypasssoftotp: String?,

    @SerializedName("password") var password: String?,

    @SerializedName("pStatus") var pStatus: String?,

    @SerializedName("id") var id: Long?,
)

data class ApproveTransaction(
    @SerializedName("mtId") val mtId: String?,
    @SerializedName("statusTrans") val statusTrans: String?,
    @SerializedName("status") val status: String?,
    @SerializedName("description") val description: String?,
    @SerializedName("trxtype") val trxtype: String?,
    @SerializedName("groupId") val groupId: String?,
    @SerializedName("depositAccountNo") val depositAccountNo: String?,
)

data class RejectResponseData(
    @SerializedName("status") val status: Status = Status(code = "", message = "", subCode = ""),
    @SerializedName("totalFee") val totalFee: String?,
    @SerializedName(value = "transDate", alternate = ["tranDate"]) val tranDate: String?,
    @SerializedName("defineCreatedDate") val defineCreatedDate: String?,
    @SerializedName("username") val userName: String?,
    @SerializedName("transactions") val transactions: ArrayList<ApproveTransaction>?,
)

data class GenKeyPassChallengeCodeResponseData(
    @SerializedName("requestId") val requestId: String?,
    @SerializedName("challengeCode") val challengeCode: String?,
)

data class GetBatchTransactionListResponseData(
    @SerializedName("requestId") val requestId: String?,
    @SerializedName("info") val info: BatchListEntity?,
    @SerializedName("mtIds") val mtIds: ArrayList<String>?,
    @SerializedName("transactions") val transactions: ArrayList<TransactionEntity>?,
)

data class BatchListEntity(
    @SerializedName("bulkId") val bulkId: String?,
    @SerializedName("creator") val creator: String?,
    @SerializedName("currency") val currency: String?,
    @SerializedName("fileName") val fileName: String?,
    @SerializedName("payFeeType") val payFeeType: String?,
    @SerializedName("processDate") val processDate: String?,
    @SerializedName("processTime") val processTime: String?,
    @SerializedName("processType") val processType: String?,
    @SerializedName("total") val total: String?,
    @SerializedName("totalAmount") val totalAmount: String?,
)

data class GetDownloadFileIDResponseData(
    @SerializedName("requestId") val requestId: String?,
    @SerializedName("downloadFileId") val downloadFileId: String?,
)

data class GetDownloadBase64FileResponseData(
    @SerializedName("file") val file: String?,
    @SerializedName("fileName") val fileName: String?,
)

data class SubTransactionResponseData(
    @SerializedName("requestId") val requestId: String?,
    @SerializedName("approver") val approver: String?,
    @SerializedName("data") val data: List<SubTransactionDataEntity>?,
)

data class SubTransactionDataEntity(
    @SerializedName("amount") val amount: String?,
    @SerializedName("approver") val approver: String?,
    @SerializedName("coreHostRefNum") val coreHostRefNum: String?,
    @SerializedName("coreHostRefNumFile") val coreHostRefNumFile: String?,
    @SerializedName("coreStatus") val coreStatus: String?,
    @SerializedName("errorDes") val errorDes: String?,
    @SerializedName("feeAmt") val feeAmt: String?,
    @SerializedName("feeCode") val feeCode: String?,
    @SerializedName("feeVat") val feeVat: String?,
    @SerializedName("fromAccount") val fromAccount: String?,
    @SerializedName("hostMtId") val hostMtId: String?,
    @SerializedName("mtId") val mtId: String?,
    @SerializedName("receiveName") val receiveName: String?,
    @SerializedName("sendName") val sendName: String?,
    @SerializedName("splitFee") val splitFee: String?,
    @SerializedName("splitFeeVat") val splitFeeVat: String?,
    @SerializedName("statusName") val statusName: String?,
    @SerializedName("toAccountNo") val toAccountNo: String?,
    @SerializedName("transactionStatus") val transactionStatus: String?,
)

data class GetTransactionDetailResponseData(
    @SerializedName("transaction") val transaction: TransactionEntity?,
    @SerializedName("status") val status: Status?,
)

data class DataFXEntity(
    @SerializedName("mtId") val mtId: String?,
    @SerializedName("receiveAccount") val receiveAccount: String?,
    @SerializedName("receiveInfo") val receiveInfo: String?,
    @SerializedName("postAmount") val postAmount: String?,
    @SerializedName("postCurrency") val postCurrency: String?,
    @SerializedName("hostMTID") val hostMTID: String?,
    @SerializedName("receiveBank") val receiveBank: String?,
    @SerializedName("receiveBankName") val receiveBankName: String?,
    @SerializedName("midBank") val midBank: String?,
    @SerializedName("midBankName") val midBankName: String?,
    @SerializedName("content") val content: String?,
    @SerializedName("prePaidDate") val prePaidDate: String?,
    @SerializedName("lcReturnDate") val lcReturnDate: String?,
    @SerializedName("chargeMethod") val chargeMethod: String?,
    @SerializedName("accountDebit") val accountDebit: String?,
    @SerializedName("fromAccount1") val fromAccount1: String?,
    @SerializedName("fromAccountCurrency1") val fromAccountCurrency1: String?,
    @SerializedName("purchaseAmount1") val purchaseAmount1: String?,
    @SerializedName("exchangeRate1") val exchangeRate1: String?,
    @SerializedName("paymentAmount1") val paymentAmount1: String?,
    @SerializedName("fromAccount2") val fromAccount2: String?,
    @SerializedName("fromAccountCurrency2") val fromAccountCurrency2: String?,
    @SerializedName("purchaseAmount2") val purchaseAmount2: String?,
    @SerializedName("exchangeRate2") val exchangeRate2: String?,
    @SerializedName("paymentAmount2") val paymentAmount2: String?,
    @SerializedName("citadName") val citadName: String?,
    @SerializedName("fileList") val fileList: List<FileTransaction>?,
    @SerializedName("fileMt103") val fileMt103: List<FileTransaction>?,
    @SerializedName("messError") val messError: String?,
    @SerializedName("index") val index: String?,
    @SerializedName("amountValue") val amountValue: String?,
    @SerializedName("amountFx") val amountFx: String?,
)
