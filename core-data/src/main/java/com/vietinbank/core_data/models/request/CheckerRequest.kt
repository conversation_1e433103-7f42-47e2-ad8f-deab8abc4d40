package com.vietinbank.core_data.models.request

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_domain.models.maker.ApproverDomains

/**
 * Created by vand<PERSON> on 10/3/25.
 */

data class GetTransactionListRequest(
    @SerializedName("all") val all: String?,
    @SerializedName("content") val content: String?,
    @SerializedName("fromAccountNo") val fromAccountNo: String?,
    @SerializedName("fromDate") val fromDate: String?,
    @SerializedName("groupType") val groupType: String?,
    @SerializedName("maxAmount") val maxAmount: String?,
    @SerializedName("minAmount") val minAmount: String?,
    @SerializedName("orderByAmount") val orderByAmount: String?,
    @SerializedName("orderByApproveDate") val orderByApproveDate: String?,
    @SerializedName("pageNum") val pageNum: String?,
    @SerializedName("pageSize") val pageSize: String?,
    @SerializedName("receiveName") val receiveName: String?,
    @SerializedName("serviceType") val serviceType: String?,
    @SerializedName("toDate") val toDate: String?,
    @SerializedName("tranType") val tranType: String?,
    @SerializedName("username") val username: String?,
    @SerializedName("mtId") val mtId: String? = "",
    @SerializedName("receiveBankName") val receiveBankName: String?,
    @SerializedName("maker") val maker: String?,
    @SerializedName("toAcctNo") val toAcctNo: String?,
    @SerializedName("isSchedule") val isSchedule: String? = "",

) : CommonRequest(Constants.MB_GET_TRANSACTION_LIST)

data class GetTranListRequest(
    @SerializedName("all") val all: String?,
    @SerializedName("content") val content: String?,
    @SerializedName("fromAccountNo") val fromAccountNo: String?,
    @SerializedName("fromDate") val fromDate: String?,
    @SerializedName("groupType") val groupType: String?,
    @SerializedName("maxAmount") val maxAmount: String?,
    @SerializedName("minAmount") val minAmount: String?,
    @SerializedName("orderByAmount") val orderByAmount: String?,
    @SerializedName("orderByApproveDate") val orderByApproveDate: String?,
    @SerializedName("pageNum") val pageNum: String?,
    @SerializedName("pageSize") val pageSize: String?,
    @SerializedName("receiveName") val receiveName: String?,
    @SerializedName("serviceType") val serviceType: String?,
    @SerializedName("toDate") val toDate: String?,
    @SerializedName("tranType") val tranType: String?,
    @SerializedName("username") val username: String?,
    @SerializedName("mtId") val mtId: String? = "",
    @SerializedName("receiveBankName") val receiveBankName: String?,
    @SerializedName("maker") val maker: String?,
    @SerializedName("toAcctNo") val toAcctNo: String?,
    @SerializedName("isSchedule") val isSchedule: String? = "",
    @SerializedName("tranSubType") val tranSubType: String? = null,
) : CommonRequest(Constants.MB_GET_TRAN_LIST)

data class CountPendingRequest(
    @SerializedName("username") val username: String?,
    @SerializedName("role") val role: String?,
) : CommonRequest(Constants.MB_COUNT_PENDING)

data class MobileConfigLastRequest(
    @SerializedName("username") val username: String?,
    @SerializedName("lastModified") val lastModified: String?,
) : CommonRequest(Constants.MB_MOBILE_CONFIG_LAST_PARAMS)

data class PreApproveRequest(
    @SerializedName("serviceType") val serviceType: String?,
    @SerializedName("transactions") val transactions: ArrayList<String>?,
    @SerializedName("username") val username: String?,
    @SerializedName("tranType") val tranType: String?,
    @SerializedName("serviceId") val serviceId: String?,
    @SerializedName("pereAppTp") val pereAppTp: ArrayList<SubTuitionTransaction>?,
) : CommonRequest(Constants.MB_PRE_APPROVE)

data class SubTuitionTransaction(
    @SerializedName("fileId") val fileId: String?,
    @SerializedName("studentCode") val studentCode: String?,
)

data class GenSOTPTransCodeRequest(
    @SerializedName("username") val username: String?,
    @SerializedName("groupType") val groupType: String?,
    @SerializedName("actionId") val actionId: String?,
    @SerializedName("mtIds") val mtIds: String?,
    @SerializedName("reason") val reason: String?,
    @SerializedName("transactionData") val transactionData: String?,
    @SerializedName("trxType") val trxType: String?,
) : CommonRequest(Constants.MB_GEN_SOTP_TRANS_CODE)

data class ApproveRequest(
    @SerializedName("username") val username: String?,
    @SerializedName("requestType") val requestType: String?,
    @SerializedName("authenType") var authenType: String?,
    @SerializedName("softOtpTransId") var softOtpTransId: String?,
    @SerializedName("token") var token: String?,
    @SerializedName("tranType") val tranType: String?,
    @SerializedName("transactions") val transactions: ArrayList<String>?,
    @SerializedName("serviceType") val serviceType: String?,
    @SerializedName("transactionsTp") val transactionsTp: ArrayList<SubTuitionTransaction>?,
    @SerializedName("nextApprovers") val nextApprovers: ArrayList<ApproverDomains>?,
) : CommonRequest(Constants.MB_APPROVE)

data class PreRejectRequest(
    @SerializedName("serviceType") val serviceType: String?,
    @SerializedName("transactions") val transactions: ArrayList<String>?,
    @SerializedName("username") val username: String?,
    @SerializedName("tranType") val tranType: String?,
    @SerializedName("serviceId") val serviceId: String?,
    @SerializedName("pereAppTp") val pereAppTp: ArrayList<SubTuitionTransaction>?,
) : CommonRequest(Constants.MB_PRE_REJECT)

data class RejectRequest(
    @SerializedName("requestType") val requestType: String?,
    @SerializedName("authenType") var authType: String?,
    @SerializedName("softOtpTransId") var softOtpTransId: String?,
    @SerializedName("token") var token: String?,
    @SerializedName("tranType") val tranType: String?,
    @SerializedName("serviceId") val serviceId: String?,
    @SerializedName("transactions") val transactions: ArrayList<String>?,
    @SerializedName("username") val username: String?,
    @SerializedName("reason") val reason: String?,
    @SerializedName("serviceType") val serviceType: String?,
    @SerializedName("transactionsTp") val transactionsTp: ArrayList<SubTuitionTransaction>?,

) : CommonRequest(Constants.MB_REJECT)

data class GenKeyPassChallengeCodeRequest(
    @SerializedName("username") val username: String?,
) : CommonRequest(Constants.MB_GEN_KEYPASS_CHALLENGE_CDE)

data class GetBatchFileListRequest(
    @SerializedName("username") val username: String?,
    @SerializedName("fromDate") val fromDate: String?,
    @SerializedName("toDate") val toDate: String?,
    @SerializedName("orderByAmount") val orderByAmount: String?,
    @SerializedName("orderByCreatedDate") val orderByCreatedDate: String?,
    @SerializedName("pageNum") val pageNum: String?,
    @SerializedName("pageSize") val pageSize: String?,
) : CommonRequest(Constants.MB_GET_BATCH_FILE_LIST)

data class GetBatchTransactionListRequest(
    @SerializedName("username") val username: String?,
    @SerializedName("all") val all: String? = "",
    @SerializedName("orderByAmount") val orderByAmount: String?,
    @SerializedName("orderByApproveDate") val orderByApproveDate: String?,
    @SerializedName("pageNum") val pageNum: String?,
    @SerializedName("pageSize") val pageSize: String?,
    @SerializedName("transactionId") val transactionId: String?,
) : CommonRequest(Constants.MB_GET_BATCH_TRANS_LIST)

data class GetDownloadFileIDRequest(
    @SerializedName("username") val username: String?,
    @SerializedName("fileId") val fileId: String?,
    @SerializedName("mtID") val mtID: String?,
    @SerializedName("tranType") val tranType: String?,
) : CommonRequest(Constants.MB_GET_DOWNLOAD_FILE_ID)

data class GetDownloadBase64FileRequest(
    @SerializedName("username") val username: String?,
    @SerializedName("type") val type: String?,
    @SerializedName("mtId") val mtId: String?,
    @SerializedName("signType") val signType: String?,
) : CommonRequest(Constants.MB_GET_DOWNLOAD_BASE_FILE)

data class GetExportNSNNFileTemplateRequest(
    @SerializedName("username") val username: String?,
    @SerializedName("type") val type: String?,
    @SerializedName("mtId") val mtId: String?,
) : CommonRequest(Constants.MB_GET_DOWNLOAD_NSNN_FILE)

data class GetTransactionDetailRequest(
    @SerializedName("mtId") val mtId: String?,
    @SerializedName("username") val username: String?,
    @SerializedName("serviceType") val serviceType: String?,
    @SerializedName("trantype") val trantype: String?,
    @SerializedName("signType") val signType: String?,
//    @SerializedName("type") val type: String?,
) : CommonRequest(Constants.MB_GET_TRANSACTION_DETAIL)

data class SubTransactionRequest(
    @SerializedName("hostMtId") val hostMtId: String?,
    @SerializedName("username") val username: String?,
) : CommonRequest(Constants.MB_SUB_TRANSACTION)
