package com.vietinbank.core_data.models.request

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_common.constants.Constants

data class CheckUserEkycRequest(
    @SerializedName("username") val username: String = "",
) : CommonRequest(Constants.MB_CHECK_USER_EKYC)

data class GenerateOtpRequest(
    @SerializedName("username") val username: String = "",
    @SerializedName("addition1") val addition1: String = "",
    @SerializedName("transType") val transType: String = "UTH",
    @SerializedName("mobilePhone") val mobilePhone: String = "",
) : CommonRequest(Constants.MB_GENERATE_OTP)

data class VerifyOtpEkycRequest(
    @SerializedName("username")
    val username: String? = "",
    @SerializedName("type")
    val type: String? = "",
    @SerializedName("addition1")
    val addition1: String? = "",
    @SerializedName("efastId")
    val efastId: String? = "",
    @SerializedName("otp")
    val otp: String? = "",
) : CommonRequest(Constants.MB_VERIFY_OTP_EKYC)

data class UpdateEkycRequest(
    @SerializedName("username")
    val username: String? = "",
    @SerializedName("authenType")
    val authenType: String? = "",
    @SerializedName("softOtpTransId")
    val softOtpTransId: String? = "",
    @SerializedName("token")
    val token: String? = "",
    @SerializedName("tranType")
    val tranType: String? = "",
    @SerializedName("serviceType")
    val serviceType: String? = "",
    @SerializedName("transactions")
    val transactions: List<String>? = listOf(),
    @SerializedName("roleId")
    val roleId: String? = "",
    @SerializedName("transactionsTp")
    val transactionsTp: List<String>? = listOf(),
) : CommonRequest(Constants.MB_UPDATE_EKYC)

data class GetBiometricFaceRequest(
    @SerializedName("username") val username: String? = "",
    @SerializedName("biometricId") val biometricId: String? = "",
    @SerializedName("roleId") val roleId: String? = "",
) : CommonRequest(Constants.MB_GET_BIOMETRIC_FACE)