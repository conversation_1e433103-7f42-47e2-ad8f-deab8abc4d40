package com.vietinbank.core_data.models.request

import android.os.Build
import android.provider.Settings
import com.google.gson.annotations.SerializedName
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_data.helper.RequestManager
import kotlin.random.Random

/**
 * Created by vand<PERSON> on 18/12/24.
 */
open class CommonRequest(
    @SerializedName("requestPath") val requestPath: String,
    @SerializedName("requestId") val requestId: String = generateRequestID(),
    @SerializedName("channel")
    var channel: String = "MobileNew",
) {
    @SerializedName("version")
    val version: String = RequestManager.getEnvironmentProvider().getBuildConfigVersionName()

    @SerializedName("newCore")
    val newCore: String = "Y"

    @SerializedName("getActive")
    val getActive: String = "123"

    @SerializedName("jCaptcha")
    val jCaptcha: String = "123"

    @SerializedName("deviceData")
    val deviceData: DeviceData = DeviceData()

    @SerializedName("sessionId")
    var sessionId: String = RequestManager.getSessionManager().getSessionId() ?: ""

    @SerializedName("cifno")
    var cifno: String = RequestManager.getUserProf().getCifNo() ?: ""

    @SerializedName("language")
    var language: String = RequestManager.getAppConfig().getLanguage()

    companion object {
        private val charPool: List<Char> = ('A'..'Z') + ('0'..'9')

        fun generateRequestID(): String = (1..8)
            .map { Random.nextInt(0, charPool.size) }
            .map(charPool::get)
            .joinToString("")
    }
}

data class DeviceData(
    @SerializedName("additionInfor")
    val additionInfor: String = "null",

    @SerializedName("applicationVersion")
    val applicationVersion: String = "1.0.0",

    @SerializedName("deviceIP")
    val deviceIP: String = "",

    @SerializedName("deviceName")
    val deviceName: String = getDeviceInfo(),

    @SerializedName("deviceOS")
    val deviceOS: String = getDeviceOs(),

    @SerializedName("imei")
    val imei: String = getAndroidID(),

    @SerializedName("screenResolution")
    val screenResolution: String = "null",

    @SerializedName("sessionAuthenType")
    val sessionAuthenType: String = "null",
) {
    companion object {
        fun getAndroidID(): String {
            return Settings.Secure.getString(
                RequestManager.getContext().contentResolver,
                Settings.Secure.ANDROID_ID,
            ) ?: ""
        }

        fun getDeviceOs(): String {
            return "android/" + Build.VERSION.RELEASE
        }

        fun getDeviceInfo(): String {
            val manufacturer = Build.MANUFACTURER?.trim() ?: ""
            val deviceModel = Build.MODEL?.trim() ?: ""
            val androidVersion = Build.VERSION.RELEASE?.trim() ?: ""
            return when {
                // in case manufacturer not contain value
                manufacturer.isEmpty() -> {
                    if (deviceModel.isEmpty()) {
                        (
                            Utils
                                .g()
                                .removeAccent("Android $androidVersion".trim())
                                .replace("-", " ")
                            ).replace("[^a-zA-Z0-9\\s]".toRegex(), "")
                    } else {
                        (
                            Utils
                                .g()
                                .removeAccent("$deviceModel Android $androidVersion".trim())
                                .replace("-", " ")
                            ).replace("[^a-zA-Z0-9\\s]".toRegex(), "")
                    }
                }
                // device model already contain manufacturer
                deviceModel.lowercase().startsWith(manufacturer.lowercase()) -> {
                    (
                        Utils
                            .g()
                            .removeAccent("$deviceModel Android $androidVersion".trim())
                            .replace("-", " ")
                        ).replace("[^a-zA-Z0-9\\s]".toRegex(), "")
                }
                // normal case
                else -> {
                    (
                        Utils
                            .g()
                            .removeAccent("$manufacturer $deviceModel Android $androidVersion".trim())
                            .replace("-", " ")
                        ).replace("[^a-zA-Z0-9\\s]".toRegex(), "")
                }
            }
        }
    }
}
