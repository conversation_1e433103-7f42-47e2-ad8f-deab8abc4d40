package com.vietinbank.core_data.models.request.ott

import com.google.gson.annotations.SerializedName

/**
 * Created by vand<PERSON> on 17/4/25.
 */
data class OttRegisterTokenRequest(
    @SerializedName("phoneNumber") val phoneNumber: String?,
    @SerializedName("registerToken") val registerToken: String?,
    @SerializedName("username") val username: String?,
    @SerializedName("userId") val userId: String?,
    @SerializedName("channelId") val channelId: String?,
    @SerializedName("actionId") val actionId: String?,
    @SerializedName("roleId") val roleId: String?,
) : OttCommonRequest() {
    init {
        targetPath = "/ott-info/register-token"
    }
}

data class OttRegisterDeviceRequest(
    @SerializedName("channelType") val channelType: String?, // default truyen: "default"
    @SerializedName("cif") val cif: String?,
    @SerializedName("phone") val phone: String?,
    @SerializedName("username") val username: String?,
    @SerializedName("tokenType") val tokenType: String?, // fcmToken hoặc socketId tương ứng với Loại token đăng ký
    @SerializedName("fcmToken") val fcmToken: String?,
    @SerializedName("socketId") val socketId: String?,
) : OttCommonRequest() {
    init {
        targetPath = "/ott-info/register-device"
    }
}

class OttRegisterSocketRequest(
    @SerializedName("username") val username: String,
    @SerializedName("actionId") val actionId: String,
    @SerializedName("roleId") val roleId: String,
    @SerializedName("phoneNumber") val phoneNumber: String,
    @SerializedName("socketId") val socketId: String,
) : OttCommonRequest() {
    init {
        targetPath = "/ott-info/register-socket-id"
    }
}

class OttUpdateStatusRequest(
    @SerializedName("messageId") val messageId: String,
    @SerializedName("status") val status: Int,
    @SerializedName("cif") val cif: String,
    @SerializedName("phone") val phone: String,
    @SerializedName("deviceId") val deviceId: String,
) : OttCommonRequest() {
    init {
        targetPath = "updateStatus"
    }
}

class OttGetAllMessageRequest(
    @SerializedName("channelType") val channelType: String?,
    @SerializedName("channelId") val channelId: String?,
    @SerializedName("cif") val cif: String,
    @SerializedName("phone") val phone: String,
    @SerializedName("username") val username: String,
    @SerializedName("page") val page: Int,
    @SerializedName("size") val size: String,
) : OttCommonRequest() {
    init {
        targetPath = "/ott-message/get-all-messages"
    }
}
