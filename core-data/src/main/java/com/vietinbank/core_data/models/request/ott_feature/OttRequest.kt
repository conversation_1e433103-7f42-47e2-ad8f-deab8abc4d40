package com.vietinbank.core_data.models.request.ott_feature

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_data.models.request.CommonRequest
import com.vietinbank.core_data.models.response.ott_feature.ResultItemResponse

class CheckRegisterRequest(
    @SerializedName("registedInfoList") val registedInfoList: List<ItemRegistedInfoRequest>,
    @SerializedName("tranId") var tranId: String,
    @SerializedName("username") val username: String,
    @SerializedName("isVacct") val isVacct: String,
) : CommonRequest(Constants.MB_OTT_CHECK_REG)

data class ItemRegistedInfoRequest(
    @SerializedName("cifno") val cifno: String,
    @SerializedName("phoneNumber") val phoneNumber: String,
    @SerializedName("username") val username: String,
    @SerializedName("accountName") val accountName: String,
)

class ListRegisterRequest(
    @SerializedName("alertMethod") val alertMethod: String,
    @SerializedName("isVacct") val isVacct: String,
    @SerializedName("mobileNumber") val mobileNumber: String,
    @SerializedName("roleId") val roleId: String,
    @SerializedName("tranId") var tranId: String,
    @SerializedName("typeCheck") val typeCheck: String,
    @SerializedName("username") val username: String,
) : CommonRequest(Constants.MB_OTT_LIST_REG)

class SmsOtpCreateRequest(
    @SerializedName("accountNumber") val accountNumber: String,
    @SerializedName("isVacct") val isVacct: String,
    @SerializedName("phoneNo") val phoneNo: String,
    @SerializedName("roleId") val roleId: String,
    @SerializedName("type") val type: String,
    @SerializedName("username") val username: String,
) : CommonRequest(Constants.MB_OTT_SMS_CREATE)

class SmsOtpVerifyRequest(
    @SerializedName("isVacct") val isVacct: String,
    @SerializedName("otpNumber") val otpNumber: String,
    @SerializedName("phoneNo") val phoneNo: String,
    @SerializedName("roleId") val roleId: String,
    @SerializedName("typeCheck") val typeCheck: String,
    @SerializedName("username") val username: String,
) : CommonRequest(Constants.MB_OTT_SMS_VERIFY)

class OttRegisterRequest(
    @SerializedName("isVacct") val isVacct: String,
    @SerializedName("mobileNumber") val mobileNumber: String,
    @SerializedName("roleId") val roleId: String,
    @SerializedName("tranId") var tranId: String,
    @SerializedName("username") val username: String,
    @SerializedName("records") val recordsParams: MutableList<ResultItemResponse>,
) : CommonRequest(Constants.MB_OTT_REGISTER)
