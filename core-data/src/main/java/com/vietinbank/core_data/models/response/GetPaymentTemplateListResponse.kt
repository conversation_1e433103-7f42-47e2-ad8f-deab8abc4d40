package com.vietinbank.core_data.models.response

import com.google.gson.annotations.SerializedName

data class GetPaymentTemplateListResponse(
    @SerializedName("tempTransactionList") val tempTransactionList: MutableList<TempTransactionResponse>,
)

data class TempTransactionResponse(
    @SerializedName("amount") val amount: String?,
    @SerializedName("iconUrl") val iconUrl: String?,
    @SerializedName("confirm") val confirm: String?,
    @SerializedName("content") val content: String?,
    @SerializedName("currency") val currency: String?,
    @SerializedName("fromAccountNo") val fromAccountNo: String?,
    @SerializedName("id") val id: String?,
    @SerializedName("numberTransacion") val numberTransacion: String?,
    @SerializedName("processDate") val processDate: String?,
    @SerializedName("toAccountName") val toAccountName: String?,
    @SerializedName("toAccountNo") val toAccountNo: String?,
    @SerializedName("toBankCode") val toBankCode: String?,
    @SerializedName("toBankName") val toBankName: String?,
    @SerializedName("tranType") val tranType: String?,
    @SerializedName("userId") val userId: String?,
    @SerializedName("icon") val icon: String?,
)
