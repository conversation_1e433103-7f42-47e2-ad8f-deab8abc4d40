package com.vietinbank.core_data.models.response

import com.google.gson.annotations.SerializedName

data class BranchListResponse(
    @SerializedName("data")
    val data: List<BranchResponse>?,
)

data class BranchResponse(
    @SerializedName("bankId")
    val bankId: String?,
    @SerializedName("bankName")
    val bankName: String?,
    @SerializedName("branchId")
    val branchId: String?,
    @SerializedName("branchName")
    val branchName: String?,
    @SerializedName("provinceId")
    val provinceId: String?,
    @SerializedName("provinceName")
    val provinceName: String?,
)
