package com.vietinbank.core_data.models.request

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_common.constants.Constants

/**
 * Created by vand<PERSON> on 18/12/24.
 */
data class LoginRequest(
    @SerializedName("accessCode")
    val accessCode: String? = "",

    @SerializedName("authenType")
    val authenType: String? = "",

    @SerializedName("browerInfo")
    val browerInfo: String? = "",

    @SerializedName("password")
    val password: String? = "",

    @SerializedName("screenResolution")
    val screenResolution: String? = "",

    @SerializedName("touchId")
    val touchId: String? = "",

    @SerializedName("regTouchId")
    val regTouchId: String? = "",

    @SerializedName("username")
    val username: String? = "",

) : CommonRequest(Constants.MB_LOGIN)

data class AccountListRequest(
    @SerializedName("accountType")
    val accountType: String,
    @SerializedName("currencySort")
    val currencySort: String,
    @SerializedName("username")
    val username: String,
    @SerializedName("serviceType")
    val serviceType: String,
) : CommonRequest(Constants.MB_ACCOUNT_LIST)

// Banner
data class BannerRequest(

    @SerializedName("username")
    val username: String = "",

    @SerializedName("zone")
    val zone: String? = null,

) : CommonRequest(Constants.MB_BANNER)

// Change password

data class ChangePasswordRequest(

    @SerializedName("status")
    val status: String? = "",

    @SerializedName("oldPass")
    val oldPass: String? = "",

    @SerializedName("newPass")
    val newPass: String? = "",

    @SerializedName("username")
    val username: String? = "",

    @SerializedName("zone")
    val zone: String? = null,

) : CommonRequest(Constants.MB_CHANGE_PWD)

data class ForceUpdateRequest(

    @SerializedName("roleId")
    val roleId: String? = "",

    @SerializedName("username")
    val username: String? = "",

) : CommonRequest(Constants.MB_CHECK_VERSION_APP)

data class GenOTPRequest(
    @SerializedName("username")
    val username: String? = "",
    @SerializedName("type")
    val type: String? = "",
    @SerializedName("addition1")
    val addition1: String? = "",
) : CommonRequest(Constants.MB_GEN_OTP)

data class VerifyOTPRequest(
    @SerializedName("username")
    val username: String? = "",
    @SerializedName("type")
    val type: String? = "",
    @SerializedName("addition1")
    val addition1: String? = "",
    @SerializedName("efastId")
    val efastId: String? = "",
    @SerializedName("otp")
    val otp: String? = "",
) : CommonRequest(Constants.MB_VERIFY_OTP)
