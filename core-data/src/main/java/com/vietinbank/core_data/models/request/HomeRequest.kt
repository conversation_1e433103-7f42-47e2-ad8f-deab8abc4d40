package com.vietinbank.core_data.models.request

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_common.constants.Constants

/**
 * Created by vandz on 5/3/25.
 */

data class RegisterTouchIDRequest(

    @SerializedName("password")
    var password: String = "",

    @SerializedName("username")
    var username: String = "",

) : CommonRequest(Constants.MB_REGISTER_TOUCH_ID)
