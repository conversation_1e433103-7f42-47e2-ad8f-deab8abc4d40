package com.vietinbank.core_data.models.response

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_data.models.base.Status

class CheckPasswordResponse(
    @SerializedName("requestId")
    val requestId: String,
    @SerializedName("sessionId")
    val sessionId: String,
    @SerializedName("status")
    val status: Status? = Status(code = "", message = "", subCode = ""),
)