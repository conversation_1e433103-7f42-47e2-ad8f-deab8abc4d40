package com.vietinbank.core_data.models.request.account_lock

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_data.models.request.CommonRequest

data class AccountLockRequest(
    @SerializedName("username") private val username: String?,
    @SerializedName("accountPayment") private val accountPayment: String?,
    @SerializedName("idCard") private val idCard: String?,
    @SerializedName("typeReset") private val typeReset: String?,
) : CommonRequest(Constants.MB_ACCOUNT_LOCK)

data class AccountLockOTPRequest(
    @SerializedName("confirm") private val confirm: String? = "N",
    @SerializedName("username") private val username: String?,
    @SerializedName("type") private val type: String?,
    @SerializedName("accountPayment") private val accountPayment: String?,
    @SerializedName("idCard") private val idCard: String?,
    @SerializedName("typeReset") private val typeReset: String?,
    @SerializedName("approver") private val approver: String?,
    @SerializedName("roleChecker") private val roleChecker: String?,
    @SerializedName("token") private val token: String?,
) : CommonRequest(Constants.MB_ACCOUNT_LOCK_OTP)

data class AccountLockFaceRequest(
    @SerializedName("username") private val username: String?,
    @SerializedName("image") private val image: String?,
) : CommonRequest(Constants.MB_ACCOUNT_LOCK_FACE)
