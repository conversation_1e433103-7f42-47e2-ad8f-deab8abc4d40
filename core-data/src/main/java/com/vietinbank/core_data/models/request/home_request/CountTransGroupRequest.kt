package com.vietinbank.core_data.models.request.home_request

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_data.models.request.CommonRequest

class CountTransGroupRequest(
    @SerializedName("accountNo")
    val accountNo: String = "",
    @SerializedName("type")
    val type: String = "",
    @SerializedName("role")
    val role: String = "",
    @SerializedName("roleId")
    val roleId: String = "",
    @SerializedName("username")
    val username: String = "",
) : CommonRequest(Constants.MB_COUNT_TRANS_GROUP)
