package com.vietinbank.core_data.models.request

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_domain.models.maker.ApproverDomains

data class NapasBankListRequest(
    @SerializedName("username") val username: String = "",
) : CommonRequest(Constants.MB_NAPAS_BANK_LIST)

data class ValidateNapasAccountRequest(
    @SerializedName("receiveAccount") val receiveAccount: String = "",
    @SerializedName("receiveBin") val receiveBin: String = "",
    @SerializedName("debitAccount") val debitAccount: String = "",
    @SerializedName("debitFullname") val debitFullname: String = "",
    @SerializedName("currency") val currency: String = "",
    @SerializedName("username") val username: String = "",
    @SerializedName("tranType") val tranType: String = "",
    @SerializedName("receiveBankName") val receiveBankName: String = "",
) : CommonRequest(Constants.MB_VALIDATE_ACCOUNT)

data class CreateNapasAccountTransferRequest(
    @SerializedName("username") val username: String = "",
    @SerializedName("tranType") val tranType: String = "",
    @SerializedName("confirm") val confirm: String = "",
) : CommonRequest(Constants.MB_CREATE_ACCOUNT_TRANSFER)

data class CreateNapasCardTransferRequest(
    @SerializedName("username") val username: String = "",
) : CommonRequest(Constants.MB_CREATE_CARD_TRANSFER)

data class ValidateNapasAccountTransferRequest(
    @SerializedName("amount") val amount: String = "",
    @SerializedName("currency") val currency: String = "",
    @SerializedName("feePayMethod") val feePayMethod: String = "",
    @SerializedName("fromAcctNo") val fromAcctNo: String = "",
    @SerializedName("isQRTransfer") val isQRTransfer: String = "",
    @SerializedName("processDate") val processDate: String = "",
    @SerializedName("receiveBin") val receiveBin: String = "",
    @SerializedName("remark") val remark: String = "",
    @SerializedName("sendBank") val sendBank: String = "",
    @SerializedName("toAcctName") val toAcctName: String = "",
    @SerializedName("toAcctNo") val toAcctNo: String = "",
    @SerializedName("toBankName") val toBankName: String = "",
    @SerializedName("username") val username: String = "",
    @SerializedName("tranType") val tranType: String = "",
    @SerializedName("confirm") val confirm: String = "",
    @SerializedName("nextApprovers") val nextApprovers: List<NextApproversRequest> = emptyList(),
) : CommonRequest(Constants.MB_VALIDATE_ACCOUNT_TRANSFER)

data class NextApproversRequest(
    @SerializedName("approverlevel") val approverlevel: String?,
    @SerializedName("birthdate") val birthdate: String?,
    @SerializedName("email") val email: String?,
    @SerializedName("endate") val endate: String?,
    @SerializedName("enterpriseid") val enterpriseid: String?,
    @SerializedName("faillogon") val faillogon: String?,
    @SerializedName("fullname") val fullname: String?,
    @SerializedName("gender") val gender: String?,
    @SerializedName("grouptype") val grouptype: String?,
    @SerializedName("id") val id: String?,
    @SerializedName("idnumber") val idnumber: String?,
    @SerializedName("idtype") val idtype: String?,
    @SerializedName("keypassid") val keypassid: String?,
    @SerializedName("keypassprofile") val keypassprofile: String?,
    @SerializedName("keypasssoftotp") val keypasssoftotp: String?,
    @SerializedName("keypasswaitactive") val keypasswaitactive: String?,
    @SerializedName("lastlogon") val lastlogon: String?,
    @SerializedName("mailalert") val mailalert: String?,
    @SerializedName("mobile") val mobile: String?,
    @SerializedName("nationality") val nationality: String?,
    @SerializedName("pStatus") val pStatus: String?,
    @SerializedName("password") val password: String?,
    @SerializedName("rsaprofile") val rsaprofile: String?,
    @SerializedName("rsaserial") val rsaserial: String?,
    @SerializedName("startdate") val startdate: String?,
    @SerializedName("status") val status: String?,
    @SerializedName("title") val title: String?,
    @SerializedName("username") val username: String?,
    @SerializedName("version") val version: String?,
    @SerializedName("isSelected") var isSelected: Boolean = false,
    @SerializedName("enterpriseUserId") var enterpriseUserId: String?,
    @SerializedName("endDate") var endDate: String?,
)

data class ValidateNapasCardRequest(
    @SerializedName("branch") val branch: String = "",
    @SerializedName("cardNumber") val cardNumber: String = "",
    @SerializedName("currency") val currency: String = "",
    @SerializedName("debitAccount") val debitAccount: String = "",
    @SerializedName("debitFullname") val debitFullname: String = "",
    @SerializedName("username") val username: String = "",
) : CommonRequest(Constants.MB_VALIDATE_NAPAS_CARD)

data class ValidateNapasCardTransferRequest(
    @SerializedName("amount") val amount: String = "",
    @SerializedName("currency") val currency: String = "",
    @SerializedName("feePayMethod") val feePayMethod: String = "",
    @SerializedName("fromAcctNo") val fromAcctNo: String = "",
    @SerializedName("isQRTransfer") val isQRTransfer: String = "",
    @SerializedName("processDate") val processDate: String = "",
    @SerializedName("remark") val remark: String = "",
    @SerializedName("sendBank") val sendBank: String = "",
    @SerializedName("toBankName") val toBankName: String = "",
    @SerializedName("toCardName") val toCardName: String = "",
    @SerializedName("toCardNo") val toCardNo: String = "",
    @SerializedName("username") val username: String = "",
    @SerializedName("nextApprovers") val nextApprovers: List<ApproverDomains>,
) : CommonRequest(Constants.MB_VALIDATE_NAPAS_CARD_TRANSFER)

data class ContactListRequest(
    @SerializedName("accountNo") val accountNo: String = "",
    @SerializedName("actionId") val actionId: String = "",
    @SerializedName("customercode") val customercode: String = "",
    @SerializedName("keySearch") val keySearch: String = "",
    @SerializedName("payeeName") val payeeName: String = "",
    @SerializedName("place") val place: String = "",
    @SerializedName("provinceId") val provinceId: String = "",
    @SerializedName("revBankCode") val revBankCode: String = "",
    @SerializedName("username") val username: String = "",
    @SerializedName("serviceId") val serviceId: String = "",
) : CommonRequest(Constants.MB_CONTACT_LIST)

data class GetPaymentTemplateListRequest(
    @SerializedName("tempTransaction") val tempTransaction: TempTransactionRequest? = null,
    @SerializedName("username") val username: String = "",
) : CommonRequest(Constants.MB_GET_PAYMENT_TEMPLATE_LIST)

data class TempTransactionRequest(
    @SerializedName("amount") val amount: String? = null,
    @SerializedName("confirm") val confirm: String? = null,
    @SerializedName("content") val content: String? = null,
    @SerializedName("currency") val currency: String? = null,
    @SerializedName("fromAccountNo") val fromAccountNo: String? = null,
    @SerializedName("id") val id: String? = null,
    @SerializedName("numberTransacion") val numberTransacion: String? = null,
    @SerializedName("processDate") val processDate: String? = null,
    @SerializedName("toAccountName") val toAccountName: String? = null,
    @SerializedName("toAccountNo") val toAccountNo: String? = null,
    @SerializedName("toBankCode") val toBankCode: String? = null,
    @SerializedName("toBankName") val toBankName: String? = null,
    @SerializedName("tranType") val tranType: String? = null,
    @SerializedName("userId") val userId: String? = null,
    @SerializedName("bankIcon") val bankIcon: String? = null,
    @SerializedName("toCardNo") val toCardNo: String? = null,
)

data class ContactCreateRequest(
    @SerializedName("accountNo") val accountNo: String = "",
    @SerializedName("actionId") val actionId: String = "",
    @SerializedName("cardNo") val cardNo: String = "",
    @SerializedName("confirm") val confirm: String = "",
    @SerializedName("contactId") val contactId: String = "",
    @SerializedName("currency") val currency: String = "",
    @SerializedName("customercode") val customercode: String = "",
    @SerializedName("idtype") val idtype: String = "",
    @SerializedName("isdisplayed") val isdisplayed: String = "",
    @SerializedName("issuePlace") val issuePlace: String = "",
    @SerializedName("issuedate") val issuedate: String = "",
    @SerializedName("istrusted") val istrusted: String = "",
    @SerializedName("midBank") val midBank: String = "",
    @SerializedName("midBankName") val midBankName: String = "",
    @SerializedName("orgAcctBranch") val orgAcctBranch: String = "",
    @SerializedName("orgAcctName") val orgAcctName: String = "",
    @SerializedName("orgId") val orgId: String = "",
    @SerializedName("orgName") val orgName: String = "",
    @SerializedName("payeeName") val payeeName: String = "",
    @SerializedName("place") val place: String = "",
    @SerializedName("provinceId") val provinceId: String = "",
    @SerializedName("provinceName") val provinceName: String = "",
    @SerializedName("receiveBank") val receiveBank: String = "",
    @SerializedName("receiveBankName") val receiveBankName: String = "",
    @SerializedName("revBankCode") val revBankCode: String = "",
    @SerializedName("serviceId") val serviceId: String = "",
    @SerializedName("tranType") val tranType: String = "",
    @SerializedName("username") val username: String = "",
) : CommonRequest(Constants.MB_CONTACT_CREATE)

data class CreateTemplateRequest(
    @SerializedName("username") val username: String = "",
    @SerializedName("tempTransaction") val tempTransaction: TempTransactionRequest?,
) : CommonRequest(Constants.MB_CREATE_TEMPLATE)

data class ValidatePaymentOrderTransferRequest(
    @SerializedName("amount") val amount: String = "",
    @SerializedName("file") val file: String = "",
    @SerializedName("content") val content: String = "",
    @SerializedName("feePayMethod") val feePayMethod: String = "",
    @SerializedName("fromAccountNo") val fromAccountNo: String = "",
    @SerializedName("toAccountName") val toAccountName: String = "",
    @SerializedName("toAccountNo") val toAccountNo: String = "",
    @SerializedName("toBankName") val toBankName: String = "",
    @SerializedName("username") val username: String = "",
    @SerializedName("processTime") val processTime: String = "",
    @SerializedName("fileName") val fileName: String = "",
    @SerializedName("branchId") val branchId: String = "",
    @SerializedName("nextApprovers") val nextApprovers: List<ApproverDomains>,

) : CommonRequest(Constants.MB_VALIDATE_PAYMENT_ORDER_TRANSFER)

data class CreatePaymentOrderTransfer(
    @SerializedName("username") val username: String = "",
    @SerializedName("tranType") val tranType: String = "",
) : CommonRequest(Constants.MB_CREATE_ORDER_PAYMENT_TRANSFER)

data class NextStatusTransactionByRuleRequest(
    @SerializedName("mtId") val mtId: String = "",
    @SerializedName("amount") val amount: String = "",
    @SerializedName("creator") val creator: String = "",
    @SerializedName("currentStatus") val currentStatus: String = "",
    @SerializedName("currentUserGroup") val currentUserGroup: String = "",
    @SerializedName("currentUserLevel") val currentUserLevel: String = "",
    @SerializedName("customerNumber") val customerNumber: String = "",
    @SerializedName("fromAccountNo") val fromAccountNo: String = "",
    @SerializedName("serviceCode") val serviceCode: String = "",
    @SerializedName("toAccountNo") val toAccountNo: String = "",
    @SerializedName("username") val username: String = "",
) : CommonRequest(Constants.MB_NEXT_STATUS_TRANSACTION_BY_RULE)

data class BranchRequest(
    @SerializedName("branchId") val branchId: String = "",
    @SerializedName("branchName") val branchName: String = "",
    @SerializedName("username") val username: String = "",
) : CommonRequest(Constants.MB_BRANCH)
