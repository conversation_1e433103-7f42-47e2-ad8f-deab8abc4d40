package com.vietinbank.core_data.models.ott

import com.google.gson.annotations.JsonAdapter
import com.google.gson.annotations.SerializedName

/**
 * Created by vandz on 11/6/25.
 */

data class OttMessageItem(
    @SerializedName("messageId") val messageId: String,
    @SerializedName("title") val title: String?,
    @SerializedName("content") val content: String,
    @SerializedName("type") val type: String?,
    @SerializedName("timestamp") val timestamp: String?,
    @SerializedName("sendTime") val sendTime: String?,
    @JsonAdapter(StatusDeserializer::class) val status: Int = 0,
    @SerializedName("data") val data: Map<String, Any>? = null,
)