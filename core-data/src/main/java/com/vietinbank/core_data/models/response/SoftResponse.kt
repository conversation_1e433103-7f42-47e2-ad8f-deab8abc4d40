package com.vietinbank.core_data.models.response

import com.google.gson.annotations.SerializedName
import java.io.Serializable

class SoftResponse(
    @SerializedName("requestId")
    val requestId: String? = null,
    @SerializedName("sessionId")
    val sessionId: String? = null,
    @SerializedName("status")
    val status: StatusResponse? = null,
) : Serializable

data class StatusResponse(
    @SerializedName("code")
    val code: String? = null,
    @SerializedName("message")
    val message: String? = null,
) : Serializable
