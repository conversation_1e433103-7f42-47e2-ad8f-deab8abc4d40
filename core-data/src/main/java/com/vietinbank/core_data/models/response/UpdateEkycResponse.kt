package com.vietinbank.core_data.models.response

import com.google.gson.annotations.SerializedName

data class UpdateEkycResponse(
    @SerializedName("authenType")
    val authenType: String?,
    @SerializedName("requestId")
    val requestId: String?,
    @SerializedName("requestType")
    val requestType: Int?,
    @SerializedName("serviceType")
    val serviceType: String?,
    @SerializedName("sessionId")
    val sessionId: String?,
    @SerializedName("softOtpTransId")
    val softOtpTransId: String?,
    @SerializedName("status")
    val status: StatusUpdateEkyc?,
    @SerializedName("token")
    val token: String?,
    @SerializedName("tranType")
    val tranType: String?,
    @SerializedName("transactions")
    val transactions: List<String?>?,
    @SerializedName("transactionsTp")
    val transactionsTp: List<TransactionsTp?>?,
)

data class StatusUpdateEkyc(
    @SerializedName("code")
    val code: String?,
    @SerializedName("message")
    val message: String?,
    @SerializedName("subCode")
    val subCode: String?,
)

data class TransactionsTp(
    @SerializedName("fileId")
    val fileId: String?,
    @SerializedName("studentCode")
    val studentCode: String?,
)
