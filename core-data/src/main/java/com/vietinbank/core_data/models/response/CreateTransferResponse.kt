package com.vietinbank.core_data.models.response

import com.google.gson.annotations.SerializedName

data class CreateTransferResponse(
    @SerializedName("createdDate")
    val createdDate: String,
    @SerializedName("mtId")
    val mtId: String,
    @SerializedName("userNameCreate")
    val userNameCreate: String,
    @SerializedName("fullNameCreate")
    val fullNameCreate: String,
    @SerializedName("status")
    val status: Status,
    @SerializedName("userApproval")
    val userApprovalList: MutableList<UserApprovalItemResponse>,

)
data class UserApprovalItemResponse(
    @SerializedName("username")
    val username: String,
    @SerializedName("fullname")
    val fullname: String,
    @SerializedName("approverlevel")
    val approverlevel: String,
)
data class Status(
    @SerializedName("code")
    val code: String,
    @SerializedName("message")
    val message: String,
)
