package com.vietinbank.core_data.models.response

import com.google.gson.annotations.SerializedName

data class TransactionListResponse(
    @SerializedName("countTransaction") val countTransaction: String?,
    @SerializedName("countTransactionApprove") val countTransactionApprove: String?,
    @SerializedName("mtIds") val mtIds: List<String>?,
    @SerializedName("requestId") val requestId: String?,
    @SerializedName("serviceType") val serviceType: String?,
    @SerializedName("sessionId") val sessionId: String?,
    @SerializedName("subTranTypeList") val subTranTypeList: List<SubTranTypeResponse>?,
    @SerializedName("totalAmount") val totalAmount: String?,
    @SerializedName("totalFee") val totalFee: String?,
    @SerializedName("transactions") val transactions: List<TransactionResponse>?,
)

data class SubTranTypeResponse(
    @SerializedName("count_detail") val count_detail: String?,
    @SerializedName("count_transaction") val count_transaction: String?,
    @SerializedName("count_valid_transaction") val count_valid_transaction: String?,
    @SerializedName("groupType") val groupType: String?,
    @SerializedName("servicetype") val servicetype: String?,
    @SerializedName("servicetypename") val servicetypename: String?,
    @SerializedName("tranType") val tranType: String?,
)
