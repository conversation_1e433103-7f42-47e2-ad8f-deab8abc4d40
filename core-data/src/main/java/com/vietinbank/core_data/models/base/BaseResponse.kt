package com.vietinbank.core_data.models.base

import com.google.gson.annotations.SerializedName

/**
 * Created by vandz on 18/12/24.
 */
data class BaseResponse<T>(
    @SerializedName("requestId")
    val requestId: String,
    @SerializedName("sessionId")
    val sessionId: String,
    @SerializedName("status")
    val status: Status? = Status(code = "", message = "", subCode = ""),
    @SerializedName("message")
    val message: String?,
    @Transient
    val data: T? = null,
)

data class Status(
    @SerializedName("code")
    val code: String?,
    @SerializedName("message")
    val message: String?,
    @SerializedName("subCode")
    val subCode: String?,
)
