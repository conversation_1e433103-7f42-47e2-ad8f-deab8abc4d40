package com.vietinbank.core_data.models.response

import com.google.gson.annotations.SerializedName

data class ContactListResponse(
    @SerializedName("contacts")
    val contacts: MutableList<ContactResponse>?,
)

data class ContactResponse(
    @SerializedName("iconUrl")
    val iconUrl: String?,
    @SerializedName("objectversion")
    val objectversion: String?,
    @SerializedName("paymentcount")
    val paymentcount: String?,
    @SerializedName("lastpaymentdate")
    val lastpaymentdate: String?,
    @SerializedName("createdAt")
    val createdAt: String?,
    @SerializedName("issuedate")
    val issuedate: String?,
    @SerializedName("id")
    val id: String?,
    @SerializedName("customerid")
    val customerid: String?,
    @SerializedName("customercode")
    val customercode: String?,
    @SerializedName("payeename")
    val payeename: String?,
    @SerializedName("payeenickname")
    val payeenickname: String?,
    @SerializedName("bsb")
    val bsb: String?,
    @SerializedName("account")
    val account: String?,
    @SerializedName("referenceid")
    val referenceid: String?,
    @SerializedName("duaid")
    val duaid: String?,
    @SerializedName("bank")
    val bank: String?,
    @SerializedName("branch")
    val branch: String?,
    @SerializedName("payeecurrency")
    val payeecurrency: String?,
    @SerializedName("entitynumber")
    val entitynumber: String?,
    @SerializedName("payeeemailaddress")
    val payeeemailaddress: String?,
    @SerializedName("payeesmsnumber")
    val payeesmsnumber: String?,
    @SerializedName("istrusted")
    val istrusted: String?,
    @SerializedName("reference2")
    val reference2: String?,
    @SerializedName("reference3")
    val reference3: String?,
    @SerializedName("cardnumber")
    val cardnumber: String?,
    @SerializedName("obuid")
    val obuid: String?,
    @SerializedName("cardtype")
    val cardtype: String?,
    @SerializedName("idtype")
    val idtype: String?,
    @SerializedName("issueplace")
    val issueplace: String?,
    @SerializedName("placetoreceive")
    val placetoreceive: String?,
    @SerializedName("mobile")
    val mobile: String?,
    @SerializedName("line1")
    val line1: String?,
    @SerializedName("line2")
    val line2: String?,
    @SerializedName("line3")
    val line3: String?,
    @SerializedName("postcode")
    val postcode: String?,
    @SerializedName("state")
    val state: String?,
    @SerializedName("suburb")
    val suburb: String?,
    @SerializedName("country")
    val country: String?,
    @SerializedName("countrycode")
    val countrycode: String?,
    @SerializedName("otheraddress")
    val otheraddress: String?,
    @SerializedName("payeecustomernumber")
    val payeecustomernumber: String?,
    @SerializedName("cardgroup")
    val cardgroup: String?,
    @SerializedName("accounttype")
    val accounttype: String?,
    @SerializedName("provinceId")
    val provinceId: String?,
    @SerializedName("trantype")
    val trantype: String?,
    @SerializedName("tranTypeName")
    val tranTypeName: String?,
    @SerializedName("revBankcode")
    val revBankcode: String?,
    @SerializedName("revBankName")
    val revBankName: String?,
    @SerializedName("midBankcode")
    val midBankcode: String?,
    @SerializedName("midBankName")
    val midBankName: String?,
    @SerializedName("status")
    val status: String?,
    @SerializedName("actionId")
    val actionId: String?,
    @SerializedName("manager")
    val manager: String?,
    @SerializedName("creator")
    val creator: String?,
    @SerializedName("action")
    val action: String?,
    @SerializedName("provinceName")
    val provinceName: String?,
    @SerializedName("orgId")
    val orgId: String?,
    @SerializedName("orgName")
    val orgName: String?,
    @SerializedName("orgAcctName")
    val orgAcctName: String?,
    @SerializedName("orgAcctBranch")
    val orgAcctBranch: String?,
)
