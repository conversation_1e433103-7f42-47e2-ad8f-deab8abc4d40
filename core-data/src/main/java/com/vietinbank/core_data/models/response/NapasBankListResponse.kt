package com.vietinbank.core_data.models.response

import com.google.gson.annotations.SerializedName

data class NapasBankListResponse(
    @SerializedName("banks")
    val dataBanks: List<DataBanks>?,
)

data class DataBanks(
    @SerializedName("binCode")
    val binCode: String?,
    @SerializedName("bankName")
    val bankName: String?,
    @SerializedName("shortName")
    val shortName: String?,
    @SerializedName("ebankCode")
    val ebankCode: String?,
    @SerializedName("icon")
    val icon: String?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("type")
    val type: String?,
    @SerializedName("status")
    val status: String?,
)
