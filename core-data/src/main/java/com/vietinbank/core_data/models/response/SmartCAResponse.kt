package com.vietinbank.core_data.models.response

import com.google.gson.annotations.SerializedName

// danh sach
data class SmartCAListResponse(
    @SerializedName("signInfoList") val signInfoList: List<SmartCAEsignModel>?,
)

data class SmartCAEsignModel(
    @SerializedName("serial") val serial: String?,
    @SerializedName("userName") val userName: String?,
    @SerializedName("esignType") val esignType: String?,
    @SerializedName("role") val role: String?,
    @SerializedName("startDate") val startDate: String?,
    @SerializedName("esignFrom") val esignFrom: String?,
    @SerializedName("statusCode") val statusCode: String?,
    @SerializedName("serviceType") val serviceType: String?,
    @SerializedName("endtDate") val endtDate: String?,
    @SerializedName("statusName") val statusName: String?,
    @SerializedName("mtId") val mtId: String?,
    @SerializedName("createDate") val createDate: String?,
    @SerializedName("createDateDesc") val createDateDesc: String?,
    @SerializedName("roleDesc") val roleDesc: List<String>?,
)

// chi tiet
data class SmartCADetailResponse(
    @SerializedName("data") val data: SmartCADataModel?,
)

data class SmartCADataModel(
    @SerializedName("mtId") val mtId: String?,
    @SerializedName("creator") val creator: String?,
    @SerializedName("createDate") val createDate: String?,
    @SerializedName("manager") val manager: String?,
    @SerializedName("verifiedDate") val verifiedDate: String?,
    @SerializedName("cifNo") val cifNo: String?,
    @SerializedName("userName") val userName: String?,
    @SerializedName("esignType") val esignType: String?,
    @SerializedName("serviceType") val serviceType: String?,
    @SerializedName("role") val role: String?,
    @SerializedName("name") val name: String?,
    @SerializedName("no") val no: String?,
    @SerializedName("serial") val serial: String?,
    @SerializedName("startDate") val startDate: String?,
    @SerializedName("endDate") val endDate: String?,
    @SerializedName("esignFrom") val esignFrom: String?,
    @SerializedName("nameEsign") val nameEsign: String?,
    @SerializedName("idEsign") val idEsign: String?,
    @SerializedName("branch") val branch: String?,
    @SerializedName("statusCode") val statusCode: String?,
    @SerializedName("message") val message: String?,
    @SerializedName("statusName") val statusName: String?,
    @SerializedName("taxNumber") val taxNumber: String?,
    @SerializedName("hostId") val hostId: String?,
    @SerializedName("statusDsm") val statusDsm: String?,
    @SerializedName("messError") val messError: String?,
    @SerializedName("chanOrgName") val chanOrgName: String?,
    @SerializedName("serviceTypeName") val serviceTypeName: String?,
    @SerializedName("roleName") val roleName: String?,
    @SerializedName("branchName") val branchName: String?,
    @SerializedName("esignTypeName") val esignTypeName: String?,
    @SerializedName("createDateDesc") val createDateDesc: String?,
    @SerializedName("verifiedDateDesc") val verifiedDateDesc: String?,
    @SerializedName("startDateDesc") val startDateDesc: String?,
    @SerializedName("endDateDesc") val endDateDesc: String?,
    @SerializedName("files") val files: List<SmartCAFileModel>?,
)

data class SmartCAFileModel(
    @SerializedName("id") val id: String?,
    @SerializedName("mtId") val mtId: String?,
    @SerializedName("fileName") val fileName: String?,
    @SerializedName("fileAttach") val fileAttach: String?,
    @SerializedName("createDate") val createDate: String?,
    @SerializedName("creator") val creator: String?,
    @SerializedName("fileType") val fileType: String?,
    @SerializedName("fileContent") val fileContent: String?,
    @SerializedName("objectId") val objectId: String?,
)

// dang ky
data class SmartCARegisterResponse(
    @SerializedName("esignRegister") val esignRegister: SmartCAEsignRegisterModel?,
    @SerializedName("messageDsm") val messageDsm: String?,
    @SerializedName("toaDo") val toaDo: SmartCAToaDoModel?,
    @SerializedName("androidKey") val androidKey: String?,
    @SerializedName("iosKey") val iosKey: String?,
    @SerializedName("data") val data: DataSignEntity?,
    @SerializedName("status") val status: Status?,
)

data class DataSignEntity(
    @SerializedName("transTime") val transTime: String?,
    @SerializedName("transId") val transId: String?,
    @SerializedName("addinfo") val addinfo: String?,
    @SerializedName("custCode") val custCode: String?,
    @SerializedName("addInfoDetail") val addInfoDetail: List<AddInfoDetailEntity>?,
)

data class AddInfoDetailEntity(
//    @SerializedName("service_type") val service_type: String?,
//    @SerializedName("service_name") val service_name: String?,
//    @SerializedName("cert_id") val cert_id: String?,
//    @SerializedName("cert_data") val cert_data: String?,
//    @SerializedName("cert_subject") val cert_subject: String?,
//    @SerializedName("certSubject") val certSubject: String?,
//    @SerializedName("cert_valid_from") val cert_valid_from: String?,
//    @SerializedName("cert_valid_to") val cert_valid_to: String?,
//    @SerializedName("chain_data") val chain_data: String?,
//    @SerializedName("serial_number") val serial_number: String?,
//    @SerializedName("transaction_id") val transaction_id: String?,
//    @SerializedName("signature_value") val signature_value: String?,
//    @SerializedName("doc_id") val doc_id: String?,
    @SerializedName("tran_code") val tran_code: String?,
//    @SerializedName("expired_in") val expired_in: String?,
)

data class SmartCAEsignRegisterModel(
    @SerializedName("mtId") val mtId: String?,
    @SerializedName("creator") val creator: String?,
    @SerializedName("createDate") val createDate: String?,
    @SerializedName("manager") val manager: String?,
    @SerializedName("verifiedDate") val verifiedDate: String?,
    @SerializedName("cifNo") val cifNo: String?,
    @SerializedName("userName") val userName: String?,
    @SerializedName("esignType") val esignType: String?,
    @SerializedName("serviceType") val serviceType: String?,
    @SerializedName("role") val role: String?,
    @SerializedName("name") val name: String?,
    @SerializedName("no") val no: String?,
    @SerializedName("serial") val serial: String?,
    @SerializedName("startDate") val startDate: String?,
    @SerializedName("endDate") val endDate: String?,
    @SerializedName("esignFrom") val esignFrom: String?,
    @SerializedName("nameEsign") val nameEsign: String?,
    @SerializedName("idEsign") val idEsign: String?,
    @SerializedName("branch") val branch: String?,
    @SerializedName("statusCode") val statusCode: String?,
    @SerializedName("message") val message: String?,
    @SerializedName("statusName") val statusName: String?,
    @SerializedName("taxNumber") val taxNumber: String?,
    @SerializedName("hostId") val hostId: String?,
    @SerializedName("statusDsm") val statusDsm: String?,
    @SerializedName("messError") val messError: String?,
    @SerializedName("chanOrgName") val chanOrgName: String?,
    @SerializedName("serviceTypeName") val serviceTypeName: String?,
    @SerializedName("roleName") val roleName: String?,
    @SerializedName("branchName") val branchName: String?,
    @SerializedName("files") val files: List<Any>?,
    @SerializedName("createDateDesc") val createDateDesc: String?,
    @SerializedName("verifiedDateDesc") val verifiedDateDesc: String?,
    @SerializedName("startDateDesc") val startDateDesc: String?,
    @SerializedName("esignTypeName") val esignTypeName: String?,
    @SerializedName("endDateDesc") val endDateDesc: String?,
)

data class SmartCAToaDoModel(
    @SerializedName("pageNo") val pageNo: String?,
    @SerializedName("x") val x: String?,
    @SerializedName("y") val y: String?,
    @SerializedName("pageHeight") val pageHeight: String?,
    @SerializedName("pageWidth") val pageWidth: String?,
    @SerializedName("file") val file: String?,
    @SerializedName("fileName") val fileName: String?,
    @SerializedName("keyValue") val keyValue: String?,
    @SerializedName("fileByte") val fileByte: String?,
    @SerializedName("fileId") val fileId: String?,
)

// chi nhanh
data class SmartCABranchResponse(
    @SerializedName("listSavings") val listSavings: List<SmartCABranchModel>?,
    @SerializedName("listLoans") val listLoans: List<SmartCABranchModel>?,
)

data class SmartCABranchModel(
    @SerializedName("branchId") val branchId: String?,
    @SerializedName("branchName") val branchName: String?,
)

class SmartCAUpdateResponse()

data class SmartCAGetParamResponse(
//    @SerializedName("serviceTypeList") val serviceTypeList: List<SmartCARoleModel>?,
//    @SerializedName("roleList") val roleList: List<SmartCARoleModel>?,
    @SerializedName("userList") val userList: List<SmartCAUserModel>?,
    @SerializedName("serviceTypeListFull") val serviceTypeListFull: List<SmartCAServiceModel>?,
    @SerializedName("esignTypeList") val esignTypeList: List<SmartCARoleModel>?,
    @SerializedName("providerList") val providerList: List<SmartCARoleModel>?,

)

data class SmartCARoleModel(
    @SerializedName("key") val key: String?,
    @SerializedName("value") val value: String?,
)

data class SmartCAUserModel(
    @SerializedName("fullName") val fullName: String?,
    @SerializedName("username") val username: String?,
    @SerializedName("idNumber") val idNumber: String?,
    @SerializedName("emailAddress") val emailAddress: String?,
    @SerializedName("title") val title: String?,
    @SerializedName("titleName") val titleName: String?,
)

data class SmartCACertVNPTResponse(
    @SerializedName("esignRegisterList") val esignRegisterList: List<SmartCAEsignRegisterModel>?,
)

data class SmartCAServiceModel(
    @SerializedName("key") val key: String?,
    @SerializedName("value") val value: String?,
    @SerializedName("roleList") val roleList: List<SmartCARoleModel>?,
)
