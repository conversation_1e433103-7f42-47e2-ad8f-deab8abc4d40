package com.vietinbank.core_data.models.response.home_response

import com.google.gson.annotations.SerializedName

data class HomeAccountListResponse(

    @SerializedName("assetAmount")
    val assetAmount: String? = "",
    @SerializedName("cdAccounts")
    val cdAccounts: ArrayList<CdAccount>?,
    @SerializedName("cdSumAmount")
    val cdSumAmount: String? = "",
//    @SerializedName("ddaAccounts")
//    val ddaAccounts: ArrayList<DdaAccount>?,
    @SerializedName("ddaSumAmount")
    val ddaSumAmount: String? = "",
    @SerializedName("lnAccounts")
    val lnAccounts: ArrayList<LnAccount>?,
    @SerializedName("lnSumAmount")
    val lnSumAmount: String? = "",
    @SerializedName("requestId")
    val requestId: String? = "",
//    @SerializedName("sessionId")
//    val sessionId: String? = "",
    @SerializedName("status")
    val status: Status? = Status(),
)

data class CdAccount(
    @SerializedName("accountName")
    val accountName: String? = "",
    @SerializedName("accountNo")
    val accountNo: String? = "",
    @SerializedName("accountType")
    val accountType: String? = "",
    @SerializedName("accrueInterest")
    val accrueInterest: String? = "",
    @SerializedName("accruedInterest")
    val accruedInterest: String? = "",
    @SerializedName("acctNbr")
    val acctNbr: String? = "",
    @SerializedName("acctType")
    val acctType: String? = "",
    @SerializedName("aliasName")
    val aliasName: String? = "",
    @SerializedName("availableBalance")
    val availableBalance: String? = "",
    @SerializedName("benBankName")
    val benBankName: String? = "",
    @SerializedName("beneficiaryName")
    val beneficiaryName: String? = "",
    @SerializedName("branchId")
    val branchId: String? = "",
    @SerializedName("branchName")
    val branchName: String? = "",
    @SerializedName("cifName")
    val cifName: String? = "",
    @SerializedName("cifNo")
    val cifNo: String? = "",
    @SerializedName("closeDate")
    val closeDate: String? = "",
    @SerializedName("closingOutstandingBalance")
    val closingOutstandingBalance: String? = "",
    @SerializedName("contractNbr")
    val contractNbr: String? = "",
    @SerializedName("corpName")
    val corpName: String? = "",
    @SerializedName("creditCardLst")
    val creditCardLst: ArrayList<CreditCardLst>?,
    @SerializedName("creditLimit")
    val creditLimit: String? = "",
    @SerializedName("currency")
    val currency: String? = "",
    @SerializedName("currentBalance")
    val currentBalance: String? = "",
    @SerializedName("depositCardSerialNumber")
    val depositCardSerialNumber: String? = "",
    @SerializedName("depositContractNumber")
    val depositContractNumber: String? = "",
    @SerializedName("districtId")
    val districtId: String? = "",
    @SerializedName("districtName")
    val districtName: String? = "",
    @SerializedName("dueDate")
    val dueDate: String? = "",
    @SerializedName("escrowAmt")
    val escrowAmt: String? = "",
    @SerializedName("feeAcctNo")
    val feeAcctNo: String? = "",
    @SerializedName("feePlans")
    val feePlans: ArrayList<FeePlan>?,
    @SerializedName("fullPayLeft")
    val fullPayLeft: String? = "",
    @SerializedName("holdBalance")
    val holdBalance: String? = "",
    @SerializedName("interestAmount")
    val interestAmount: String? = "",
    @SerializedName("interestNotDueBilled")
    val interestNotDueBilled: String? = "",
    @SerializedName("interestPastDue")
    val interestPastDue: String? = "",
    @SerializedName("interestRate")
    val interestRate: String? = "",
    @SerializedName("interestTerm")
    val interestTerm: String? = "",
    @SerializedName("issuedDate")
    val issuedDate: String? = "",
    @SerializedName("lang")
    val lang: String? = "",
    @SerializedName("lateCharge")
    val lateCharge: String? = "",
    @SerializedName("mainAccountId")
    val mainAccountId: String? = "",
    @SerializedName("maturityDate")
    val maturityDate: String? = "",
    @SerializedName("minPayLeft")
    val minPayLeft: String? = "",
    @SerializedName("minimumAmount")
    val minimumAmount: String? = "",
    @SerializedName("nextPaymentDate")
    val nextPaymentDate: String? = "",
    @SerializedName("openDate")
    val openDate: String? = "",
    @SerializedName("origIssueDt")
    val origIssueDt: String? = "",
    @SerializedName("originalAccountType")
    val originalAccountType: String? = "",
    @SerializedName("outstandingBalance")
    val outstandingBalance: String? = "",
    @SerializedName("payOffAmount")
    val payOffAmount: String? = "",
    @SerializedName("penaltyAmount")
    val penaltyAmount: String? = "",
    @SerializedName("principalAmount")
    val principalAmount: String? = "",
    @SerializedName("principalNotDueBilled")
    val principalNotDueBilled: String? = "",
    @SerializedName("principalPastDue")
    val principalPastDue: String? = "",
    @SerializedName("productId")
    val productId: String? = "",
    @SerializedName("productName")
    val productName: String? = "",
    @SerializedName("provinceId")
    val provinceId: String? = "",
    @SerializedName("provinceName")
    val provinceName: String? = "",
    @SerializedName("remainingLoanTotal")
    val remainingLoanTotal: String? = "",
    @SerializedName("settlementDate")
    val settlementDate: String? = "",
    @SerializedName("statementDate")
    val statementDate: String? = "",
    @SerializedName("status")
    val status: String? = "",
    @SerializedName("statusName")
    val statusName: String? = "",
    @SerializedName("term")
    val term: String? = "",
    @SerializedName("totalCredit")
    val totalCredit: String? = "",
    @SerializedName("totalPayment")
    val totalPayment: String? = "",
)

data class DdaAccount(
    @SerializedName("accountName")
    val accountName: String? = "",
    @SerializedName("accountNo")
    val accountNo: String? = "",
    @SerializedName("accountType")
    val accountType: String? = "",
    @SerializedName("accrueInterest")
    val accrueInterest: String? = "",
    @SerializedName("accruedInterest")
    val accruedInterest: String? = "",
    @SerializedName("acctNbr")
    val acctNbr: String? = "",
    @SerializedName("acctType")
    val acctType: String? = "",
    @SerializedName("aliasName")
    val aliasName: String? = "",
    @SerializedName("availableBalance")
    val availableBalance: String? = "",
    @SerializedName("benBankName")
    val benBankName: String? = "",
    @SerializedName("beneficiaryName")
    val beneficiaryName: String? = "",
    @SerializedName("branchId")
    val branchId: String? = "",
    @SerializedName("branchName")
    val branchName: String? = "",
    @SerializedName("cifName")
    val cifName: String? = "",
    @SerializedName("cifNo")
    val cifNo: String? = "",
    @SerializedName("closeDate")
    val closeDate: String? = "",
    @SerializedName("closingOutstandingBalance")
    val closingOutstandingBalance: String? = "",
    @SerializedName("contractNbr")
    val contractNbr: String? = "",
    @SerializedName("corpName")
    val corpName: String? = "",
    @SerializedName("creditCardLst")
    val creditCardLst: ArrayList<CreditCardLst>?,
    @SerializedName("creditLimit")
    val creditLimit: String? = "",
    @SerializedName("currency")
    val currency: String? = "",
    @SerializedName("currentBalance")
    val currentBalance: String? = "",
    @SerializedName("depositCardSerialNumber")
    val depositCardSerialNumber: String? = "",
    @SerializedName("depositContractNumber")
    val depositContractNumber: String? = "",
    @SerializedName("districtId")
    val districtId: String? = "",
    @SerializedName("districtName")
    val districtName: String? = "",
    @SerializedName("dueDate")
    val dueDate: String? = "",
    @SerializedName("escrowAmt")
    val escrowAmt: String? = "",
    @SerializedName("feeAcctNo")
    val feeAcctNo: String? = "",
    @SerializedName("feePlans")
    val feePlans: ArrayList<FeePlan>?,
    @SerializedName("fullPayLeft")
    val fullPayLeft: String? = "",
    @SerializedName("holdBalance")
    val holdBalance: String? = "",
    @SerializedName("interestAmount")
    val interestAmount: String? = "",
    @SerializedName("interestNotDueBilled")
    val interestNotDueBilled: String? = "",
    @SerializedName("interestPastDue")
    val interestPastDue: String? = "",
    @SerializedName("interestRate")
    val interestRate: String? = "",
    @SerializedName("interestTerm")
    val interestTerm: String? = "",
    @SerializedName("issuedDate")
    val issuedDate: String? = "",
    @SerializedName("lang")
    val lang: String? = "",
    @SerializedName("lateCharge")
    val lateCharge: String? = "",
    @SerializedName("mainAccountId")
    val mainAccountId: String? = "",
    @SerializedName("maturityDate")
    val maturityDate: String? = "",
    @SerializedName("minPayLeft")
    val minPayLeft: String? = "",
    @SerializedName("minimumAmount")
    val minimumAmount: String? = "",
    @SerializedName("nextPaymentDate")
    val nextPaymentDate: String? = "",
    @SerializedName("openDate")
    val openDate: String? = "",
    @SerializedName("origIssueDt")
    val origIssueDt: String? = "",
    @SerializedName("originalAccountType")
    val originalAccountType: String? = "",
    @SerializedName("outstandingBalance")
    val outstandingBalance: String? = "",
    @SerializedName("payOffAmount")
    val payOffAmount: String? = "",
    @SerializedName("penaltyAmount")
    val penaltyAmount: String? = "",
    @SerializedName("principalAmount")
    val principalAmount: String? = "",
    @SerializedName("principalNotDueBilled")
    val principalNotDueBilled: String? = "",
    @SerializedName("principalPastDue")
    val principalPastDue: String? = "",
    @SerializedName("productId")
    val productId: String? = "",
    @SerializedName("productName")
    val productName: String? = "",
    @SerializedName("provinceId")
    val provinceId: String? = "",
    @SerializedName("provinceName")
    val provinceName: String? = "",
    @SerializedName("remainingLoanTotal")
    val remainingLoanTotal: String? = "",
    @SerializedName("settlementDate")
    val settlementDate: String? = "",
    @SerializedName("statementDate")
    val statementDate: String? = "",
    @SerializedName("status")
    val status: String? = "",
    @SerializedName("statusName")
    val statusName: String? = "",
    @SerializedName("term")
    val term: String? = "",
    @SerializedName("totalCredit")
    val totalCredit: String? = "",
    @SerializedName("totalPayment")
    val totalPayment: String? = "",
)
data class FeePlan(
    @SerializedName("feeCategory")
    val feeCategory: String? = "",
    @SerializedName("feeType")
    val feeType: String? = "",
    @SerializedName("name")
    val name: String? = "",
)

data class LnAccount(
    @SerializedName("accountName")
    val accountName: String? = "",
    @SerializedName("accountNo")
    val accountNo: String? = "",
    @SerializedName("accountType")
    val accountType: String? = "",
    @SerializedName("accrueInterest")
    val accrueInterest: String? = "",
    @SerializedName("accruedInterest")
    val accruedInterest: String? = "",
    @SerializedName("acctNbr")
    val acctNbr: String? = "",
    @SerializedName("acctType")
    val acctType: String? = "",
    @SerializedName("aliasName")
    val aliasName: String? = "",
    @SerializedName("availableBalance")
    val availableBalance: String? = "",
    @SerializedName("benBankName")
    val benBankName: String? = "",
    @SerializedName("beneficiaryName")
    val beneficiaryName: String? = "",
    @SerializedName("branchId")
    val branchId: String? = "",
    @SerializedName("branchName")
    val branchName: String? = "",
    @SerializedName("cifName")
    val cifName: String? = "",
    @SerializedName("cifNo")
    val cifNo: String? = "",
    @SerializedName("closeDate")
    val closeDate: String? = "",
    @SerializedName("closingOutstandingBalance")
    val closingOutstandingBalance: String? = "",
    @SerializedName("contractNbr")
    val contractNbr: String? = "",
    @SerializedName("corpName")
    val corpName: String? = "",
    @SerializedName("creditCardLst")
    val creditCardLst: ArrayList<CreditCardLst>?,
    @SerializedName("creditLimit")
    val creditLimit: String? = "",
    @SerializedName("currency")
    val currency: String? = "",
    @SerializedName("currentBalance")
    val currentBalance: String? = "",
    @SerializedName("depositCardSerialNumber")
    val depositCardSerialNumber: String? = "",
    @SerializedName("depositContractNumber")
    val depositContractNumber: String? = "",
    @SerializedName("districtId")
    val districtId: String? = "",
    @SerializedName("districtName")
    val districtName: String? = "",
    @SerializedName("dueDate")
    val dueDate: String? = "",
    @SerializedName("escrowAmt")
    val escrowAmt: String? = "",
    @SerializedName("feeAcctNo")
    val feeAcctNo: String? = "",
    @SerializedName("feePlans")
    val feePlans: ArrayList<FeePlan>?,
    @SerializedName("fullPayLeft")
    val fullPayLeft: String? = "",
    @SerializedName("holdBalance")
    val holdBalance: String? = "",
    @SerializedName("interestAmount")
    val interestAmount: String? = "",
    @SerializedName("interestNotDueBilled")
    val interestNotDueBilled: String? = "",
    @SerializedName("interestPastDue")
    val interestPastDue: String? = "",
    @SerializedName("interestRate")
    val interestRate: String? = "",
    @SerializedName("interestTerm")
    val interestTerm: String? = "",
    @SerializedName("issuedDate")
    val issuedDate: String? = "",
    @SerializedName("lang")
    val lang: String? = "",
    @SerializedName("lateCharge")
    val lateCharge: String? = "",
    @SerializedName("mainAccountId")
    val mainAccountId: String? = "",
    @SerializedName("maturityDate")
    val maturityDate: String? = "",
    @SerializedName("minPayLeft")
    val minPayLeft: String? = "",
    @SerializedName("minimumAmount")
    val minimumAmount: String? = "",
    @SerializedName("nextPaymentDate")
    val nextPaymentDate: String? = "",
    @SerializedName("openDate")
    val openDate: String? = "",
    @SerializedName("origIssueDt")
    val origIssueDt: String? = "",
    @SerializedName("originalAccountType")
    val originalAccountType: String? = "",
    @SerializedName("outstandingBalance")
    val outstandingBalance: String? = "",
    @SerializedName("payOffAmount")
    val payOffAmount: String? = "",
    @SerializedName("penaltyAmount")
    val penaltyAmount: String? = "",
    @SerializedName("principalAmount")
    val principalAmount: String? = "",
    @SerializedName("principalNotDueBilled")
    val principalNotDueBilled: String? = "",
    @SerializedName("principalPastDue")
    val principalPastDue: String? = "",
    @SerializedName("productId")
    val productId: String? = "",
    @SerializedName("productName")
    val productName: String? = "",
    @SerializedName("provinceId")
    val provinceId: String? = "",
    @SerializedName("provinceName")
    val provinceName: String? = "",
    @SerializedName("remainingLoanTotal")
    val remainingLoanTotal: String? = "",
    @SerializedName("settlementDate")
    val settlementDate: String? = "",
    @SerializedName("statementDate")
    val statementDate: String? = "",
    @SerializedName("status")
    val status: String? = "",
    @SerializedName("statusName")
    val statusName: String? = "",
    @SerializedName("term")
    val term: String? = "",
    @SerializedName("totalCredit")
    val totalCredit: String? = "",
    @SerializedName("totalPayment")
    val totalPayment: String? = "",
)

data class CreditCardLst(
    @SerializedName("availLimit")
    val availLimit: String? = "",
    @SerializedName("branchId")
    val branchId: String? = "",
    @SerializedName("cardEmbossNum")
    val cardEmbossNum: String? = "",
    @SerializedName("cardGrp")
    val cardGrp: String? = "",
    @SerializedName("cardId")
    val cardId: String? = "",
    @SerializedName("cardNumber")
    val cardNumber: String? = "",
    @SerializedName("cardType")
    val cardType: String? = "",
    @SerializedName("cardTypeName")
    val cardTypeName: String? = "",
    @SerializedName("creditLimit")
    val creditLimit: String? = "",
    @SerializedName("curCode")
    val curCode: String? = "",
    @SerializedName("customerNumber")
    val customerNumber: String? = "",
    @SerializedName("ecommerceInd")
    val ecommerceInd: String? = "",
    @SerializedName("fullname")
    val fullname: String? = "",
    @SerializedName("group")
    val group: String? = "",
    @SerializedName("internetBankingInd")
    val internetBankingInd: String? = "",
    @SerializedName("issuedDt")
    val issuedDt: String? = "",
    @SerializedName("status")
    val status: String? = "",
    @SerializedName("statusName")
    val statusName: String? = "",
)

data class ParentSubsidiaryInfo(
    @SerializedName("cifNo")
    val cifNo: String? = "",
    @SerializedName("enterpriseName")
    val enterpriseName: String? = "",
    @SerializedName("parentSubsidiary")
    val parentSubsidiary: String? = "",
    @SerializedName("stateAddress")
    val stateAddress: String? = "",
)

data class Status(
    @SerializedName("code")
    val code: String? = "",
    @SerializedName("message")
    val message: String? = "",
    @SerializedName("subCode")
    val subCode: String? = "",
)
