package com.vietinbank.core_data.models.response

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_domain.models.maker.ApproverDomains

class ValidatePaymentOrderTransferResponse(
    @SerializedName("amount") val amount: String?,
    @SerializedName("availableBalance") val availableBalance: String?,
    @SerializedName("currency") val currency: String?,
    @SerializedName("feeAmt") val feeAmt: String?,
    @SerializedName("feePayMethod") val feePayMethod: String?,
    @SerializedName("feeVat") val feeVat: String?,
    @SerializedName("fromAccountName") val fromAccountName: String?,
    @SerializedName("fromAcctNo") val fromAcctNo: String?,
    @SerializedName("mtId") val mtId: String?,
    @SerializedName("processDate") val processDate: String?,
    @SerializedName("receiveBankName") val receiveBankName: String?,
    @SerializedName("receiveName") val receiveName: String?,
    @SerializedName("remark") val remark: String?,
    @SerializedName("toAcctNo") val toAcctNo: String?,
    @SerializedName("branchResponse") val branch: BranchResponse?,
    @SerializedName("nextApprovers") val nextApprovers: List<ApproverDomains>?,

)
