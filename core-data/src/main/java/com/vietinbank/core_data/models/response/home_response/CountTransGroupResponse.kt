package com.vietinbank.core_data.models.response.home_response

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_data.models.base.Status

data class CountTransGroupResponse(
    @SerializedName("status")
    val status: Status = Status(
        code = "",
        message = "",
        subCode = "",
    ),
    @SerializedName("data") val transGroup: ArrayList<DataTransGroup>?,
)

data class DataTransGroup(
    @SerializedName("desc")
    val desc: String?,
    @SerializedName("code")
    val code: String?,
    @SerializedName("count")
    val count: String?,
)