package com.vietinbank.core_data.models.request

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_common.constants.Constants

data class AccountListNewRequest(
    @SerializedName("serviceAcctType") val serviceAcctType: String,
    @SerializedName("username") val username: String,
    @SerializedName("isForceReq") val isForceReq: String = "0",
) : CommonRequest(Constants.MB_ACCOUNT_LIST_NEW)

data class AccountSaveQRRequest(
    @SerializedName("accountName") val accountName: String? = null,
    @SerializedName("accountNo") val accountNo: String? = null,
    @SerializedName("addInfo1") val addInfo1: String? = null,
    @SerializedName("addInfo2") val addInfo2: String? = null,
    @SerializedName("addInfo3") val addInfo3: String? = null,
    @SerializedName("addInfo4") val addInfo4: String? = null,
    @SerializedName("addInfo5") val addInfo5: String? = null,
    @SerializedName("aliasName") val aliasName: String? = null,
    @SerializedName("amount") val amount: String? = null,
    @SerializedName("branchId") val branchId: String? = null,
    @SerializedName("branchName") val branchName: String? = null,
    @SerializedName("currency") val currency: String? = null,
    @SerializedName("expireTime") val expireTime: String? = null,
    @SerializedName("notes") val notes: String? = null,
    @SerializedName("qrData") val qrData: String? = null,
    @SerializedName("qrType") val qrType: String? = null,
    @SerializedName("username") val username: String? = null,
) : CommonRequest(Constants.MB_ACCOUNT_SAVE_QR)

data class AccountDetailRequest(
    @SerializedName("accountNo") val accountNo: String?,
    @SerializedName("accountType") val accountType: String?,
    @SerializedName("currency") val currency: String?,
    @SerializedName("username") val username: String?,
) : CommonRequest(Constants.MB_ACCOUNT_DETAIL)

data class AccountHistoryDetailRequest(
    @SerializedName("accountNo") val accountNo: String?,
    @SerializedName("dorc") val dorc: String?,
    @SerializedName("pmtType") val pmtType: String?,
    @SerializedName("refNo") val refNo: String?,
    @SerializedName("transactionId") val transactionId: String?,
    @SerializedName("trxChannelId") val trxChannelId: String?,
    @SerializedName("username") val username: String?,
) : CommonRequest(Constants.MB_ACCOUNT_HISTORY_DETAIL)

data class AccountHistoryListRequest(
    @SerializedName("accountNo") val accountNo: String?,
    @SerializedName("accountType") val accountType: String?,
    @SerializedName("fromDate") val fromDate: String?,
    @SerializedName("toDate") val toDate: String?,
//    @SerializedName("currency") val currency: String?,

//    @SerializedName("actionId") val actionId: String?,
//    @SerializedName("cardNo") val cardNo: String?,
    @SerializedName("dorcC") val dorcC: String?,
    @SerializedName("dorcD") val dorcD: String?,
    @SerializedName("fromAmount") val fromAmount: String?,
//    @SerializedName("fromH") val fromH: String?,
//    @SerializedName("lastRecord") val lastRecord: String?,
    @SerializedName("pageIndex") val pageIndex: String?,
    @SerializedName("pageSize") val pageSize: String?,
    @SerializedName("queryType") val queryType: String?,
    @SerializedName("searchKey") val searchKey: String?,
    @SerializedName("toAmount") val toAmount: String?,
//    @SerializedName("toH") val toH: String?,
    @SerializedName("username") val username: String?,
) : CommonRequest(Constants.MB_ACCOUNT_HISTORY_LIST)

class AliasAccountListRequest(
    @SerializedName("account") val account: List<AliasModel>?,
    @SerializedName("username") val username: String?,
    @SerializedName("action") val action: String = "save",
) : CommonRequest(Constants.MB_ACCOUNT_UPDATE_ALIAS)

data class AliasModel(
    @SerializedName("accountNo") val accountNo: String?,
    @SerializedName("aliasName") val aliasName: String?,
    @SerializedName("aliasNameGroups") val aliasNameGroups: String?,
    @SerializedName("cifSubsidiary") val cifSubsidiary: String?,

)

data class AccountImagesQRRequest(
    @SerializedName("type") val type: String? = null,
    @SerializedName("username") val username: String? = null,
) : CommonRequest(Constants.MB_ACCOUNT_IMAGE_QR)

data class TransactionStatusRequest(
    @SerializedName("data") val data: HostRefNoModel? = null,
    @SerializedName("username") val username: String? = null,
) : CommonRequest(Constants.MB_ACCOUNT_HISTORY_STATUS)

data class HostRefNoModel(
    @SerializedName("hostrefno") val hostrefno: String? = null,
)

data class AccountFileSavingRequest(
    @SerializedName("accountNo") val accountNo: String?,
    @SerializedName("username") val username: String?,
    @SerializedName("currency") val currency: String?,
    @SerializedName("accountType") val accountType: String = "T",
) : CommonRequest(Constants.MB_ACCOUNT_EXPORT_FILE)