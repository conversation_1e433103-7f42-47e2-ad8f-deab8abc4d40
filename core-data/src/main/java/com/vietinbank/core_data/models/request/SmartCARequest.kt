package com.vietinbank.core_data.models.request

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_common.constants.Constants

data class SmartCAListRequest(
    @SerializedName("username") val username: String? = "",
    @SerializedName("role") val role: String? = "",
) : CommonRequest(Constants.MB_SMART_CA_LIST)

data class SmartCADetailRequest(
    @SerializedName("action") val action: String? = "",
    @SerializedName("certType") val certType: String? = "",
    @SerializedName("mtId") val mtId: String? = "",
    @SerializedName("remark") val remark: String? = "",
    @SerializedName("role") val role: String? = "",
    @SerializedName("username") val username: String? = "",
) : CommonRequest(Constants.MB_SMART_CA_DETAIL)

data class SmartCARegisterRequest(
    @SerializedName("username") val username: String? = "",
    @SerializedName("userRegister") val userRegister: String? = "",
    @SerializedName("serviceType") val serviceType: String? = "",
    @SerializedName("roleList") val roleList: String? = "",
    @SerializedName("fullName") val fullName: String? = "",
    @SerializedName("no") val no: String? = "",
    @SerializedName("esignType") val esignType: String? = "",
    @SerializedName("serialNo") val serialNo: String? = "",
    @SerializedName("startDate") val startDate: String? = "",
    @SerializedName("endDate") val endDate: String? = "",
    @SerializedName("esignFrom") val esignFrom: String? = "",
    @SerializedName("esignName") val esignName: String? = "",
    @SerializedName("idEsign") val idEsign: String? = "",
    @SerializedName("branch") val branch: String? = "",
    @SerializedName("mtId") val mtId: String? = "",
    @SerializedName("confirm") val confirm: String? = "",
    @SerializedName("userConfirm") val userConfirm: String? = "",
    @SerializedName("taxNumber") val taxNumber: String? = "",
    @SerializedName("chanCert") val chanCert: String? = "",
    @SerializedName("role") val role: String? = "",
    @SerializedName("ou") val ou: String? = "",
    @SerializedName("type") val type: String? = "",
) : CommonRequest(Constants.MB_SMART_CA_GET_CERT)

data class SmartCABranchRequest(
    @SerializedName("username") val username: String? = "",
) : CommonRequest(Constants.MB_SMART_CA_BRANCH_LIST)

// update
// đóng - xóa bản ghi
data class SmartCAUpdateRequest(
    @SerializedName("role") val role: String?,
    @SerializedName("certType") val certType: String?,
    @SerializedName("mtId") val mtId: String?,
    @SerializedName("action") val action: String?,
    @SerializedName("remark") val remark: String? = "",
    @SerializedName("username") val username: String?,
) : CommonRequest(Constants.MB_SMART_CA_UPDATE)

// params dang ky
data class SmartCAGetParamsRequest(
    @SerializedName("role") val role: String?,
    @SerializedName("type") val type: String?,
    @SerializedName("username") val username: String?,
) : CommonRequest(Constants.MB_SMART_CA_GET_PARAM)

// lay thong tin tu vnpt
data class SmartCACertVNPTRequest(
    @SerializedName("no") val no: String? = "",
    @SerializedName("role") val role: String? = "",
    @SerializedName("username") val username: String? = "",
) : CommonRequest(Constants.MB_SMART_CA_GET_CERT_VNPT)