package com.vietinbank.core_data.models.request

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_common.constants.Constants

data class VGetSoftOtpActivationCodeRequest(
    @SerializedName("sendType") var sendType: String,
    @SerializedName("username") var username: String,
) : CommonRequest(Constants.MB_GET_ACTIVATION_CODE)

data class VBlockSoftOtpRequest(
    @SerializedName("tokenIds") var tokenIds: List<String>,
) : CommonRequest(Constants.MB_BLOCK_SOFT)

class KeypassRequest(
    @SerializedName("puk") var puk: String,
    @SerializedName("sendType") var sendType: String, // phương thức nhận mã PIN; 1: Email, 2: SMS
    @SerializedName("username") var username: String,
) : CommonRequest(Constants.MB_KEYPASS_FORGOT_PIN)
