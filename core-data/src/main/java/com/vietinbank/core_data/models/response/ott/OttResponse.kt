package com.vietinbank.core_data.models.response.ott

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_data.models.base.Status
import com.vietinbank.core_data.models.ott.OttMessageItem

/**
 * Created by vandz on 17/4/25.
 */

open class OttSecureResponse(
    @SerializedName("status") val status: Status,
)

class OttRegisterResponse(
    status: Status,
    @SerializedName("data") val data: RegisterData?,
) : OttSecureResponse(status)

data class RegisterData(
    @SerializedName("registered") val registered: Boolean,
    @SerializedName("deviceId") val deviceId: String?,
)

class OttUpdateStatusResponse(
    status: Status,
    @SerializedName("data") val data: UpdateStatusData?,
) : OttSecureResponse(status)

data class UpdateStatusData(
    @SerializedName("updated") val updated: <PERSON><PERSON><PERSON>,
    @SerializedName("messageId") val messageId: String,
)

class OttRegisterSocketResponse(
    status: Status,
    @SerializedName("data") val data: RegisterData?,
) : OttSecureResponse(status)

class OttGetAllMessageResponse(
    @SerializedName("requestId")
    val requestId: String,
    @SerializedName("sessionId")
    val sessionId: String,
    @SerializedName("status")
    val status: com.vietinbank.core_data.models.base.Status? = com.vietinbank.core_data.models.base.Status(
        code = "",
        message = "",
        subCode = "",
    ),
    @SerializedName("message")
    val message: String?,
    @SerializedName("data") val data: GetAllMessageData?,
)

data class Sort(
    @SerializedName("unsorted") val unsorted: Boolean?,
    @SerializedName("sorted") val sorted: Boolean?,
    @SerializedName("empty") val empty: Boolean?,
)

data class Pageable(
    @SerializedName("sort") val sort: Sort?,
    @SerializedName("pageNumber") val pageNumber: Int?,
    @SerializedName("pageSize") val pageSize: Int?,
    @SerializedName("offset") val offset: Int?,
    @SerializedName("unpaged") val unpaged: Boolean?,
    @SerializedName("paged") val paged: Boolean?,
)

data class GetAllMessageData(
    @SerializedName("content") val content: List<OttMessageItem?>?,
    @SerializedName("pageable") val pageable: Pageable?,
    @SerializedName("totalElements") val totalElements: Int?,
    @SerializedName("last") val last: Boolean?,
    @SerializedName("totalPages") val totalPages: Int?,
    @SerializedName("sort") val sort: Sort?,
    @SerializedName("first") val first: Boolean?,
    @SerializedName("numberOfElements") val numberOfElements: Int?,
    @SerializedName("size") val size: Int?,
    @SerializedName("number") val number: Int?,
    @SerializedName("empty") val empty: Boolean?,
)