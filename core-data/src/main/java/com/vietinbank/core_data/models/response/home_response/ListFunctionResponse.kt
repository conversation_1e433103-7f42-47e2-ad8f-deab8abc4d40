package com.vietinbank.core_data.models.response.home_response

import com.google.gson.annotations.SerializedName

class ListFunctionResponse(
    @SerializedName("data")
    val data: ArrayList<FunctionData?>? = arrayListOf(),
)
class FunctionData(
    @SerializedName("functionId")
    val functionId: String? = "",
    @SerializedName("functionName")
    val functionName: String? = "",
    @SerializedName("groupId")
    val groupId: String? = "",
    @SerializedName("groupName")
    val groupName: String? = "",
    @SerializedName("role")
    val role: String? = "",
    @SerializedName("orderNumber")
    val orderNumber: String? = "",
)