package com.vietinbank.core_data.models.response.ott_feature

import com.google.gson.annotations.SerializedName

data class OttStatusListResponse(
    @SerializedName("requestId")
    val requestId: String? = null,
    @SerializedName("data")
    val data: Data? = null,
    @SerializedName("status")
    val status: Status? = null,
    @SerializedName("statusRegis")
    val statusRegis: List<String>? = null,
)

data class Data(
    val something: String?,
)

data class Status(
    @SerializedName("code")
    val code: String?,
    @SerializedName("message")
    val message: String?,
)