package com.vietinbank.core_data.models.response

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_domain.models.checker.DataFXDomain

data class FilterReportResponse(
    @SerializedName("contacts") val contacts: List<ContactsModel>?,
    @SerializedName("frequencyTranferList") val frequencyTranferList: List<FrequencyTranferModel>?,
    @SerializedName("fromAccounts") val fromAccounts: List<AccountsModel>?,
    @SerializedName("groupTypes") val groupTypes: List<GroupTypesModel>?,
    @SerializedName("havePermissionReport") val havePermissionReport: Boolean?,
    @SerializedName("maxAmount") val maxAmount: String?,
    @SerializedName("minAmount") val minAmount: String?,
    @SerializedName("status") val status: StatusModel?,
    @SerializedName("statusGroups") val statusGroups: List<StatusModel>?,
    @SerializedName("toAccounts") val toAccounts: List<AccountsModel>?,
    @SerializedName("tuitionPaymentStatus") val tuitionPaymentStatus: List<StatusModel>?,
//    val requestId: String? ,
//    val sessionId: String? ,
)

data class ContactsModel(
    @SerializedName("account") val account: String?,
    @SerializedName("bank") val bank: String?,
    @SerializedName("branch") val branch: String?,
    @SerializedName("cardnumber") val cardnumber: String?,
    @SerializedName("cifno") val cifno: String?,
    @SerializedName("currency") val currency: String?,
    @SerializedName("customercode") val customercode: String?,
    @SerializedName("id") val id: String?,
    @SerializedName("idtype") val idtype: String?,
    @SerializedName("issuedate") val issuedate: String?,
    @SerializedName("issueplace") val issueplace: String?,
    @SerializedName("payeename") val payeename: String?,
    @SerializedName("place") val place: String?,
    @SerializedName("provinceId") val provinceId: String?,
    @SerializedName("trantype") val trantype: String?,
    @SerializedName("iconUrl") val iconUrl: String?,
)

data class FrequencyTranferModel(
    @SerializedName("count") val count: String?,
    @SerializedName("toAccount") val toAccount: String?,
)

data class AccountsModel(
    @SerializedName("accountId") val accountId: String?,
    @SerializedName("accountName") val accountName: String?,
    @SerializedName("accountNo") val accountNo: String?,
    @SerializedName("accountType") val accountType: String?,
    @SerializedName("availableBalance") val availableBalance: String?,
    @SerializedName("currency") val currency: String?,
)

data class GroupTypesModel(
    @SerializedName("groupName") val groupName: String?,
    @SerializedName("groupType") val groupType: String?,
    @SerializedName("types") val types: List<TypeModel>?,
    @SerializedName("statusGroups") val statusGroups: List<StatusModel>?,

)

data class TypeModel(
    @SerializedName("transactionName") val transactionName: String?,
    @SerializedName("transactionType") val transactionType: String?,
)

data class StatusModel(
    @SerializedName("statusCode") val statusCode: String?,
    @SerializedName("statusName") val statusName: String?,
)

data class ListReportResponse(
    @SerializedName("countTrans") val countTrans: Int?,
    @SerializedName("total") val total: Int?,
//    @SerializedName(value = "transListRs", alternate = ["btxTranItemList"]) var transListRs: List<TransRsModel>?,
    @SerializedName("transListRs") val transListRs: List<TransRsModel>?,
    @SerializedName("btxTranItemList") val btxTranItemList: List<TransRsModel>?,
    @SerializedName("tuitionBillList") val tuitionBillList: List<TuitionBillModel>?,

)

data class TuitionBillModel(
    @SerializedName("billId") val billId: String?,
    @SerializedName("cifNo") val cifNo: String?,
    @SerializedName("fileId") val fileId: String?,
    @SerializedName("fileName") val fileName: String?,
    @SerializedName("importDate") val importDate: String?,
    @SerializedName("providerId") val providerId: String?,
    @SerializedName("providerName") val providerName: String?,
    @SerializedName("totalAmount") val totalAmount: Int?,
    @SerializedName("totalCount") val totalCount: Int?,
    @SerializedName("userName") val userName: String?,

)

data class TransRsModel(
    @SerializedName("mtId") val mtId: String?,
    @SerializedName("statusCode") val statusCode: String?,
    @SerializedName("statusName") val statusName: String?,
    @SerializedName("createdDate") val createdDate: String?,
    @SerializedName("stt") val stt: String?,
    @SerializedName("tranType") val tranType: String?,
    @SerializedName("tranSubType") val tranSubType: String?,
    @SerializedName("tranTypeName") val tranTypeName: String?,
    @SerializedName("process_time") val process_time: String?,
    @SerializedName("type") val type: String?,
    @SerializedName("branchName") val branchName: String?,
    @SerializedName("branchId") val branchId: String?,
    @SerializedName("fromAccountName") val fromAccountName: String?,
    @SerializedName("toAccountNo") val toAccountNo: String?,
    @SerializedName("addressOfPayer") val addressOfPayer: String?,
    @SerializedName("feePayMethodDesc") val feePayMethodDesc: String?,
    @SerializedName("receiveAcct") val receiveAcct: String?,
    @SerializedName("tranDesc") val tranDesc: String?,
    @SerializedName("fromAccountNo") val fromAccountNo: String?,
    @SerializedName("valueDate") val valueDate: String?,
    @SerializedName("hostmtid") val hostmtid: String?,
    @SerializedName("manager") val manager: String?,
    @SerializedName("cardNumber") val cardNumber: String?,
    @SerializedName("cardName") val cardName: String?,
    @SerializedName("cardType") val cardType: String?,
    @SerializedName("creator") val creator: String?,
    @SerializedName("verifiedDate") val verifiedDate: String?,
    @SerializedName("language") val language: String?,
    @SerializedName("fee") val fee: String?,
    @SerializedName("executeBrn") val executeBrn: String?,
    @SerializedName("debitAccountNo2") val debitAccountNo2: String?,
    @SerializedName("feeAccountNo") val feeAccountNo: String?,
    @SerializedName("feeType") val feeType: String?,
    @SerializedName("debitAmt1") val debitAmt1: String?,
    @SerializedName("debitAmt2") val debitAmt2: String?,
    @SerializedName("debitCurrency1") val debitCurrency1: String?,
    @SerializedName("debitCurrency2") val debitCurrency2: String?,
    @SerializedName("beneficiaryAccount") val beneficiaryAccount: String?,
    @SerializedName("receiveBank") val receiveBank: String?,
    @SerializedName("receiveBankName") val receiveBankName: String?,
    @SerializedName("midBankCode") val midBankCode: String?,
    @SerializedName("midBankName") val midBankName: String?,
    @SerializedName("beneficiaryName") val beneficiaryName: String?,
    @SerializedName("remark") val remark: String?,
    @SerializedName("receiveName") val receiveName: String?,
    @SerializedName("amount") val amount: String?,
    @SerializedName("fileName") val fileName: String?,
    @SerializedName("company_code") val company_code: String?,
    @SerializedName("code_distributor") val code_distributor: String?,
    @SerializedName("name_distributor") val name_distributor: String?,
    @SerializedName("invoiceId") val invoiceId: String?,
    @SerializedName("providerCode") val providerCode: String?,
    @SerializedName("docId") val docId: String?,
    @SerializedName("billId") val billId: String?,
    @SerializedName("bill_amount") val bill_amount: String?,
    @SerializedName("saleOrder_no") val saleOrder_no: String?,
    @SerializedName("invoice_no") val invoice_no: String?,
    @SerializedName("content") val content: String?,
    @SerializedName("doc_date") val doc_date: String?,
    @SerializedName("net_due_date") val net_due_date: String?,
    @SerializedName("currency") val currency: String?,
    @SerializedName("check_result") val check_result: String?,
    @SerializedName("soff") val soff: String?,
    @SerializedName("verified_date") val verified_date: String?,
    @SerializedName("deal_amount") val deal_amount: String?,
    @SerializedName("origin_amount") val origin_amount: String?,
    @SerializedName("payment_date") val payment_date: String?,
    @SerializedName("origin_amount_desc") val origin_amount_desc: String?,
    @SerializedName("deal_amount_desc") val deal_amount_desc: String?,
    @SerializedName("sendedDate") val sendedDate: String?,
    @SerializedName("totalAmount") val totalAmount: String?,
    @SerializedName("exchangeRateLn") val exchangeRateLn: String?,
    @SerializedName("curencyTotalAmount") val curencyTotalAmount: String?,
    @SerializedName("uid") val uid: String?,
    @SerializedName("prisentStatus") val prisentStatus: String?,
    @SerializedName("rejectReason") val rejectReason: String?,
    @SerializedName("verifiers") val verifiers: String?,
    @SerializedName("refNo") val refNo: String?,
    @SerializedName("beneficiaryNo") val beneficiaryNo: String?,
    @SerializedName("disbursementPurposeCode") val disbursementPurposeCode: String?,
    @SerializedName("transferLoanCurType") val transferLoanCurType: String?,
    @SerializedName("transferLoanAmountCal") val transferLoanAmountCal: String?,
    @SerializedName("haveCommitment") val haveCommitment: String?,
    @SerializedName("companyName") val companyName: String?,
    @SerializedName("itemLst") val itemLst: List<Any>?,
    @SerializedName("activityLogs") val activityLogs: ActivityLogEntity?,
    @SerializedName("expireTime") val expireTime: String?,
    @SerializedName("term") val term: String?,
    @SerializedName("bulkId") val bulkId: String?,
    @SerializedName("interestMethodNameNew") val interestMethodNameNew: String?,
    @SerializedName("pricipalMethodNameNew") val pricipalMethodNameNew: String?,
    @SerializedName("productName") val productName: String?,
    @SerializedName("productId") val productId: String?,
    @SerializedName("interestRate") val interestRate: String?,
    @SerializedName("interestTerm") val interestTerm: String?,
    @SerializedName("issuedDate") val issuedDate: String?,
    @SerializedName("maturityDate") val maturityDate: String?,
    @SerializedName("pricipalMethodName") val pricipalMethodName: String?,
    @SerializedName("interestMethodName") val interestMethodName: String?,
    @SerializedName("accountStatusName") val accountStatusName: String?,
    @SerializedName("holdBalance") val holdBalance: String?,
    @SerializedName("providerName") val providerName: String?,
    @SerializedName("walletId") val walletId: String?,
    @SerializedName("walletName") val walletName: String?,
    @SerializedName("action") val action: String?,
    @SerializedName("cifno") val cifno: String?,
    @SerializedName("createDate") val createDate: String?,
    @SerializedName("fileAttach") val fileAttach: String?,
    @SerializedName("mainAccNo") val mainAccNo: String?,
    @SerializedName("status") val status: String?,
    @SerializedName("sysFileName") val sysFileName: String?,
    @SerializedName("totaltrxno") val totaltrxno: String?,
    @SerializedName("vaCode") val vaCode: String?,
    @SerializedName("corpAddr") val corpAddr: String?,
    @SerializedName("corpPhone") val corpPhone: String?,
    @SerializedName("updatedBy") val updatedBy: String?,
    @SerializedName("updatedDate") val updatedDate: String?,
    @SerializedName("lastManager") val lastManager: String?,
    @SerializedName("lastVerifiedDate") val lastVerifiedDate: String?,
    @SerializedName("updatedManager") val updatedManager: String?,
    @SerializedName("updatedVerifiedDate") val updatedVerifiedDate: String?,
    @SerializedName("editedBy") val editedBy: String?,
    @SerializedName("editedDate") val editedDate: String?,
    @SerializedName("editedManager") val editedManager: String?,
    @SerializedName("editedVerifiedDate") val editedVerifiedDate: String?,
    @SerializedName("updatedType") val updatedType: String?,
    @SerializedName("cancelledBy") val cancelledBy: String?,
    @SerializedName("cancelledDate") val cancelledDate: String?,
    @SerializedName("cancelledManager") val cancelledManager: String?,
    @SerializedName("cancelledVerifiedDate") val cancelledVerifiedDate: String?,
    @SerializedName("channel") val channel: String?,
    @SerializedName("send_bank") val send_bank: String?,
    @SerializedName("sendBankName") val sendBankName: String?,
    @SerializedName("corpName") val corpName: String?,
    @SerializedName("corpCif") val corpCif: String?,
    @SerializedName("receiveAcctType") val receiveAcctType: String?,
    @SerializedName("bulkID") val bulkID: String?,
    @SerializedName("processTime") val processTime: String?,
    @SerializedName("process_date") val process_date: String?,
    @SerializedName("exchangeRate") val exchangeRate: String?,
    @SerializedName("exchangeAmount") val exchangeAmount: String?,
    @SerializedName("debitCurrency") val debitCurrency: String?,
    @SerializedName("creditCurrency") val creditCurrency: String?,
    @SerializedName("debitAmount") val debitAmount: String?,
    @SerializedName("creditAmount") val creditAmount: String?,
    @SerializedName("vfxTrantype") val vfxTrantype: String?,
    @SerializedName("fractionDigits") val fractionDigits: String?,
    @SerializedName("fromAccountNo2") val fromAccountNo2: String?,
    @SerializedName("serviceName") val serviceName: String?,
    @SerializedName("paymentDay") val paymentDay: String?,
    @SerializedName("serviceType") val serviceType: String?,
    @SerializedName("billOwnerCode") val billOwnerCode: String?,
    @SerializedName("serviceId") val serviceId: String?,
    @SerializedName("nextPaymentDay") val nextPaymentDay: String?,
    @SerializedName("expiredPaymentDay") val expiredPaymentDay: String?,
    @SerializedName("identificationNo") val identificationNo: String?,
    @SerializedName("addressOfReceiver") val addressOfReceiver: String?,
    @SerializedName("groupType") val groupType: String?,
    @SerializedName("feeAmount") val feeAmount: String?,
    @SerializedName("feePayMethod") val feePayMethod: String?,
    @SerializedName("feePayMethodDesc1") val feePayMethodDesc1: String?,
    @SerializedName("exchangeRate2") val exchangeRate2: String?,
    @SerializedName("exchangeAmount2") val exchangeAmount2: String?,
    @SerializedName("receiveAcctType2") val receiveAcctType2: String?,
    @SerializedName("exChangeRateType") val exChangeRateType: String?,
    @SerializedName("expiredTime") val expiredTime: String?,
    @SerializedName("totalTrxNo") val totalTrxNo: String?,
    @SerializedName("amountWord") val amountWord: String?,
    @SerializedName("accountingDate") val accountingDate: String?,
    @SerializedName("feePayMethod1") val feePayMethod1: String?,
    @SerializedName("image") val image: String?,
    @SerializedName("image1") val image1: String?,
    @SerializedName("logo") val logo: String?,
    @SerializedName("statusNote") val statusNote: String?,
    @SerializedName("feePrePaid") val feePrePaid: String?,
    @SerializedName("prePrincipal") val prePrincipal: String?,
    @SerializedName("current") val current: String?,
    @SerializedName("accruedInterest") val accruedInterest: String?,
    @SerializedName("loanType") val loanType: String?,
    @SerializedName("debitRate1") val debitRate1: String?,
    @SerializedName("debitRate2") val debitRate2: String?,
    @SerializedName("sendDate") val sendDate: String?,
    @SerializedName("numberSuccess") val numberSuccess: Int?,
    @SerializedName("numberEror") val numberEror: Int?,
    @SerializedName("numberWait") val numberWait: Int?,
    @SerializedName("numberReject") val numberReject: Int?,
    @SerializedName("numberPending") val numberPending: Int?,
    @SerializedName("numberProcessing") val numberProcessing: Int?,
    @SerializedName("numberDelete") val numberDelete: Int?,
    @SerializedName("numberBankPending") val numberBankPending: Int?,
    @SerializedName("numberBankWait") val numberBankWait: Int?,
    @SerializedName("numberBankRefund") val numberBankRefund: Int?,
    @SerializedName("fromAccountName1") val fromAccountName1: String?,
    @SerializedName("fromAccountName2") val fromAccountName2: String?,
    @SerializedName("companyStock") val companyStock: String?,
    @SerializedName("recipAccount") val recipAccount: String?,
    @SerializedName("isOneDebit") val isOneDebit: String?,
    @SerializedName("model") val model: String?,
    @SerializedName("transID") val transID: String?,
    @SerializedName("amount2") val amount2: String?,
    @SerializedName("currency2") val currency2: String?,
    @SerializedName("amount3") val amount3: String?,
    @SerializedName("currency3") val currency3: String?,
    @SerializedName("amountChange") val amountChange: String?,
    @SerializedName("amountChange2") val amountChange2: String?,
    @SerializedName("amountChange3") val amountChange3: String?,
    @SerializedName("bdAmount") val bdAmount: String?,
    @SerializedName("statusNameSn") val statusNameSn: String?,
    @SerializedName("amountToAst") val amountToAst: String?,
    @SerializedName("tracerMtId") val tracerMtId: String?,
    @SerializedName("tracerContent") val tracerContent: String?,
    // giai ngan online
    @SerializedName("currencyCode") val currencyCode: String?,
    @SerializedName("disbursementDate") val disbursementDate: String?,
    @SerializedName("loanRate") val loanRate: String?,
    @SerializedName("loanLimit") val loanLimit: String?,
    @SerializedName("loanTerm") val loanTerm: String?,
    // Nộp NSNN
    @SerializedName("treasuryCode") val treasuryCode: String?,
    @SerializedName("payname") val payname: String?,
    @SerializedName("total") val total: String?,
    @SerializedName("totalFee") val totalFee: String?,
    @SerializedName("groupName") val groupName: String?,
    @SerializedName("countTransaction") val countTransaction: Int?,
    @SerializedName("ibFile") val ibFile: String?,
    @SerializedName("mtIdList") val mtIdList: List<String>?,
    @SerializedName("subType") val subType: String?,
    @SerializedName("amountProposed") val amountProposed: String?,
    @SerializedName("typeChange") val typeChange: String?,
    @SerializedName("statusNameBill") val statusNameBill: String?,
)

data class DetailReportResponse(
    @SerializedName("transaction") val transaction: TransDetailModel?,
    @SerializedName("btxTranItem") val btxTranItem: TransDetailModel?,
)

class TransDetailModel {
    @SerializedName("language")
    val language: String? = null

    @SerializedName("amount")
    val amount: String? = null

    @SerializedName("bulkID")
    val bulkID: String? = null

    @SerializedName("createdDate")
    val createdDate: String? = null

    @SerializedName("currency")
    val currency: String? = null

    @SerializedName("feeAmount")
    val feeAmount: String? = null

    @SerializedName("fileName")
    val fileName: String? = null

    @SerializedName("fromAccountNo")
    val fromAccountNo: String? = null

    @SerializedName("groupType")
    val groupType: String? = null

    @SerializedName("idCard")
    val idCard: String? = null

    @SerializedName("mtId")
    val mtId: String? = null

    @SerializedName("receiveBank")
    val receiveBank: String? = null

    @SerializedName("receiveBankName")
    val receiveBankName: String? = null

    @SerializedName("receiveName")
    val receiveName: String? = null

    @SerializedName("remark")
    val remark: String? = null

    @SerializedName("status")
    val status: String? = null

    @SerializedName("statusName")
    val statusName: String? = null

    @SerializedName("toAccountNo")
    val toAccountNo: String? = null

    @SerializedName("tranType")
    val tranType: String? = null

    @SerializedName("tranTypeName")
    val tranTypeName: String? = null

    @SerializedName("tranSubType")
    val tranSubType: String? = null

    @SerializedName("process_time")
    val process_time: String? = null

    @SerializedName("feePayMethod")
    val feePayMethod: String? = null

    @SerializedName("feePayMethodDesc")
    val feePayMethodDesc: String? = null

    @SerializedName("feePayMethodDesc1")
    val feePayMethodDesc1: String? = null

    @SerializedName("cifno")
    val cifno: String? = null

    @SerializedName("cardNumber")
    val cardNumber: String? = null

    @SerializedName("activityLogs")
    val activityLogs: ActivityLogEntity? = null

    @SerializedName("addressOfReceiver")
    val addressOfReceiver: String? = null

    @SerializedName("issuedDate")
    val issuedDate: String? = null

    @SerializedName("totalAmount")
    val totalAmount: String? = null

    @SerializedName("maturityDate")
    val maturityDate: String? = null

    @SerializedName("current")
    val current: String? = null

    @SerializedName("accruedInterest")
    val accruedInterest: String? = null

    @SerializedName("debitCurrency")
    val debitCurrency: String? = null

    @SerializedName("exchangeRate")
    val exchangeRate: String? = null

    @SerializedName("provinceName")
    val provinceName: String? = null

    @SerializedName("serviceId")
    val serviceId: String? = null

    @SerializedName("serviceType")
    val serviceType: String? = null

    @SerializedName("totalTrxNo")
    val totalTrxNo: String? = null

    @SerializedName("account")
    val account: String? = null

    @SerializedName("bank")
    val bank: String? = null

    @SerializedName("beneficiaryName")
    val beneficiaryName: String? = null

    @SerializedName("feeAccountNo")
    val feeAccountNo: String? = null

    @SerializedName("feeType")
    val feeType: String? = null

    @SerializedName("listFile")
    val listFile: List<FileTransaction>? = null

    @SerializedName("listFile2")
    val listFile2: List<FileTransaction>? = null

    @SerializedName("debitAmount")
    val debitAmount: String? = null

    @SerializedName("exchangeRateTypeDesc")
    val exchangeRateTypeDesc: String? = null

    @SerializedName("creator")
    val creator: String? = null

    @SerializedName("term")
    val term: String? = null

    @SerializedName("approver")
    val approver: String? = null

    @SerializedName("toAccountName")
    val toAccountName: String? = null

    @SerializedName("startDate")
    val startDate: String? = null

    @SerializedName("endDate")
    val endDate: String? = null

    @SerializedName("branchId")
    val branchId: String? = null

    @SerializedName("branchName")
    val branchName: String? = null

    @SerializedName("corpName")
    val corpName: String? = null

    @SerializedName("fromAccountName")
    val fromAccountName: String? = null

    @SerializedName("availableBalance")
    val availableBalance: String? = null

    @SerializedName("depositAccount")
    val depositAccount: String? = null

    @SerializedName("aliasName")
    val aliasName: String? = null

    @SerializedName("dueDate")
    val dueDate: String? = null

    @SerializedName("pref")
    val pref: TransDetailModel? = null

    @SerializedName("referenceNo")
    val referenceNo: String? = null

    @SerializedName("transferLoanAmountCal")
    val transferLoanAmountCal: String? = null

    @SerializedName("representativeName")
    val representativeName: String? = null

    @SerializedName("authority")
    val authority: String? = null

    @SerializedName("authorityNumber")
    val authorityNumber: String? = null

    @SerializedName("contractNo")
    val contractNo: String? = null

    @SerializedName("contractDate")
    val contractDate: String? = null

    @SerializedName("loanLimit")
    val loanLimit: String? = null

    @SerializedName("disbursedAmount")
    val disbursedAmount: String? = null

    @SerializedName("amountProposed")
    val amountProposed: String? = null

    @SerializedName("currencyCode")
    val currencyCode: String? = null

    @SerializedName("purpose")
    val purpose: String? = null

    @SerializedName("loanRate")
    val loanRate: String? = null

    @SerializedName("disbursementDate")
    val disbursementDate: String? = null

    @SerializedName("loanTerm")
    val loanTerm: String? = null

    @SerializedName("autoRepayment")
    val autoRepayment: String? = null

    @SerializedName("accountId")
    val accountId: String? = null

    @SerializedName("firstPaymentDate")
    val firstPaymentDate: String? = null

    @SerializedName("transferAmount")
    val transferAmount: String? = null

    @SerializedName("transferCurType")
    val transferCurType: String? = null

    @SerializedName("transferLoanCurType")
    val transferLoanCurType: String? = null

    @SerializedName("transferLoanAmount")
    val transferLoanAmount: String? = null

    @SerializedName("fileGNN")
    val fileGNN: List<FileTransaction>? = null

    @SerializedName("listDocumentFileVND")
    val listDocumentFileVND: List<FileTransaction>? = null

    @SerializedName("dataFx")
    val dataFx: List<DataFXDomain>? = null

    @SerializedName("file")
    val file: FileTransaction? = null

    @SerializedName("companyName")
    val companyName: String? = null

    @SerializedName("guaranteeCode")
    val guaranteeCode: String? = null

    @SerializedName("branch")
    val branch: String? = null

    @SerializedName("guaranteePurpose")
    val guaranteePurpose: String? = null

    @SerializedName("effectiveStartDate")
    val effectiveStartDate: String? = null

    @SerializedName("effectiveEndDate")
    val effectiveEndDate: String? = null

    @SerializedName("beneficiaryAddress")
    val beneficiaryAddress: String? = null

    @SerializedName("beneficiaryCode")
    val beneficiaryCode: String? = null

    @SerializedName("receiverIssuedBy")
    val receiverIssuedBy: String? = null

    @SerializedName("receiverDateRange")
    val receiverDateRange: String? = null

    @SerializedName("message")
    val message: String? = null

    @SerializedName("messageType")
    val messageType: String? = null

    @SerializedName("issueTypeDesc")
    val issueTypeDesc: String? = null

    @SerializedName("issueType")
    val issueType: String? = null

    @SerializedName("conditionPerform")
    val conditionPerform: String? = null

    @SerializedName("conditionReduction")
    val conditionReduction: String? = null

    @SerializedName("sendTypeDesc")
    val sendTypeDesc: String? = null

    @SerializedName("sendTypeCmnd")
    val sendTypeCmnd: String? = null

    @SerializedName("sendTypeNo")
    val sendTypeNo: String? = null

    @SerializedName("sendTypeDate")
    val sendTypeDate: String? = null

    @SerializedName("sendTypeBy")
    val sendTypeBy: String? = null

    @SerializedName("feeTimeDesc")
    val feeTimeDesc: String? = null

    @SerializedName("feeTime")
    val feeTime: String? = null

    @SerializedName("measure")
    val measure: String? = null

    @SerializedName("measureAcct")
    val measureAcct: String? = null

    @SerializedName("measureAmount")
    val measureAmount: String? = null

    @SerializedName("measureCurrency")
    val measureCurrency: String? = null

    @SerializedName("measureDesc")
    val measureDesc: String? = null

    @SerializedName("documentTypeName")
    val documentTypeName: String? = null

    @SerializedName("headQuarters")
    val headQuarters: String? = null

    @SerializedName("cifName")
    val cifName: String? = null

    @SerializedName("host_mtid")
    val host_mtid: String? = null

    @SerializedName("releaseDate")
    val releaseDate: String? = null

    @SerializedName("dateRange")
    val dateRange: String? = null

    @SerializedName("issuesBy")
    val issuesBy: String? = null

    @SerializedName("reasonChange")
    val reasonChange: String? = null

    @SerializedName("feeDesc")
    val feeDesc: String? = null

    @SerializedName("depositAmount")
    val depositAmount: String? = null

    @SerializedName("depositCurrency")
    val depositCurrency: String? = null

    @SerializedName("biCodeInfo")
    val biCodeInfo: BiCodeModel? = null

    @SerializedName("listFilesGO")
    val listFilesGO: List<FileTransaction>? = null

    @SerializedName("amount2")
    val amount2: String? = null

    @SerializedName("currency2")
    val currency2: String? = null

    @SerializedName("amount3")
    val amount3: String? = null

    @SerializedName("currency3")
    val currency3: String? = null

    @SerializedName("amountChange")
    val amountChange: String? = null

    @SerializedName("amountChange2")
    val amountChange2: String? = null

    @SerializedName("amountChange3")
    val amountChange3: String? = null

    @SerializedName("companyCode")
    val companyCode: String? = null

    @SerializedName("subType")
    val subType: String? = null

    @SerializedName("disbursedBranchCode")
    val disbursedBranchCode: String? = null

    @SerializedName("disbursedBranchName")
    val disbursedBranchName: String? = null

    @SerializedName("feeAccount")
    val feeAccount: String? = null

    @SerializedName("disbursementPurposeCode")
    val disbursementPurposeCode: String? = null

    @SerializedName("transferListFileVND")
    val transferListFileVND: FileTransaction? = null

    @SerializedName("transferListFile")
    val transferListFile: List<FileTransaction>? = null

    @SerializedName("ibFile")
    val ibFile: List<FileTransaction>? = null

    @SerializedName("items")
    val items: List<ItemTransactionMangerModel>? = null

    @SerializedName("taxMethodName")
    val taxMethodName: String? = null

    @SerializedName("payname")
    val payname: String? = null

    @SerializedName("paycode")
    val paycode: String? = null

    @SerializedName("payadd")
    val payadd: String? = null

    @SerializedName("provinceCode")
    val provinceCode: String? = null

    @SerializedName("districtCode")
    val districtCode: String? = null

    @SerializedName("districtName")
    val districtName: String? = null

    @SerializedName("areaCode")
    val areaCode: String? = null

    @SerializedName("areaName")
    val areaName: String? = null

    @SerializedName("treasuryCode")
    val treasuryCode: String? = null

    @SerializedName("treasuryName")
    val treasuryName: String? = null

    @SerializedName("collectionAccountNo")
    val collectionAccountNo: String? = null

    @SerializedName("collectionAccountName")
    val collectionAccountName: String? = null

    @SerializedName("collectAgencyCode")
    val collectAgencyCode: String? = null

    @SerializedName("collectAgencyName")
    val collectAgencyName: String? = null

    @SerializedName("bucode")
    val bucode: String? = null

    @SerializedName("buName")
    val buname: String? = null

    @SerializedName("oficode")
    val oficode: String? = null

    @SerializedName("ofiName")
    val ofiname: String? = null

    @SerializedName("chapCode")
    val chapCode: String? = null

    @SerializedName("chapName")
    val chapName: String? = null

    @SerializedName("declareNumber")
    val declareNumber: String? = null

    @SerializedName("declareDate")
    val declareDate: String? = null

    @SerializedName("ieType")
    val ieType: String? = null

    @SerializedName("ieName")
    val ieName: String? = null

    @SerializedName("taxType")
    val taxType: String? = null

    @SerializedName("rejectReason")
    val rejectReason: String? = null

    @SerializedName("effectiveEndDateChange")
    val effectiveEndDateChange: String? = null

    @SerializedName("effectiveStartDateChange")
    val effectiveStartDateChange: String? = null

    @SerializedName("guaranteeCommitContentChange")
    val guaranteeCommitContentChange: String? = null

    @SerializedName("guaranteeCommitContent")
    val guaranteeCommitContent: String? = null

    @SerializedName("sendType")
    val sendType: String? = null

    @SerializedName("authorizationDate")
    val authorizationDate: String? = null

    @SerializedName("typeChange")
    val typeChange: String? = null

    @SerializedName("companyAddress")
    val companyAddress: String? = null

    @SerializedName("messageTypeDes")
    val messageTypeDes: String? = null

    @SerializedName("providerCode")
    val providerCode: String? = null

    @SerializedName("providerName")
    val providerName: String? = null

    @SerializedName("feeUnitCode")
    val feeUnitCode: String? = null

    @SerializedName("feeUnitName")
    val feeUnitName: String? = null

    @SerializedName("invoiceId")
    val invoiceId: String? = null

    @SerializedName("docId")
    val docId: String? = null

    @SerializedName("voucherNumber")
    val voucherNumber: String? = null

    @SerializedName("docNum")
    val docNum: String? = null

    @SerializedName("voucherSymbol")
    val voucherSymbol: String? = null

    @SerializedName("docSign")
    val docSign: String? = null

    @SerializedName("voucherDate")
    val voucherDate: String? = null

    @SerializedName("docDate")
    val docDate: String? = null

    @SerializedName("subsect")
    val subsect: String? = null

    @SerializedName("countTransaction")
    val countTransaction: Int? = null

    @SerializedName("countSuccessTransaction")
    val countSuccessTransaction: Int? = null

    @SerializedName("countPendingTransaction")
    val countPendingTransaction: Int? = null

    @SerializedName("countErrTransaction")
    val countErrTransaction: Int? = null

    @SerializedName("subTranItemList")
    val subTranItemList: List<SubTranItemListModel>? = null

    @SerializedName("accountPayment")
    val accountPayment: String? = null

    @SerializedName("paymentAccount")
    val paymentAccount: String? = null

    @SerializedName("typeSend")
    val typeSend: String? = null

    @SerializedName("remitterName")
    val remitterName: String? = null

    @SerializedName("remitterCountry")
    val remitterCountry: String? = null

    @SerializedName("remitterDistrict")
    val remitterDistrict: String? = null

    @SerializedName("remitterWard")
    val remitterWard: String? = null

    @SerializedName("remitterStreet")
    val remitterStreet: String? = null

    @SerializedName("introducer")
    val introducer: String? = null

    @SerializedName("note")
    val note: String? = null

    @SerializedName("valueDate")
    val valueDate: String? = null

    @SerializedName("purposeTransferName")
    val purposeTransferName: String? = null

    @SerializedName("lcReturnDate")
    val lcReturnDate: String? = null

    @SerializedName("debitAmt1")
    val debitAmt1: String? = null

    @SerializedName("debitAmtByRate1")
    val debitAmtByRate1: String? = null

    @SerializedName("debitCurrency1")
    val debitCurrency1: String? = null

    @SerializedName("debitAccountNo2")
    val debitAccountNo2: String? = null

    @SerializedName("fromAccountName2")
    val fromAccountName2: String? = null

    @SerializedName("debitAmt2")
    val debitAmt2: String? = null

    @SerializedName("debitAmtByRate2")
    val debitAmtByRate2: String? = null

    @SerializedName("debitCurrency2")
    val debitCurrency2: String? = null

    @SerializedName("beneficiaryAccount")
    val beneficiaryAccount: String? = null

    @SerializedName("benName")
    val benName: String? = null

    @SerializedName("benCountry")
    val benCountry: String? = null

    @SerializedName("benDistrict")
    val benDistrict: String? = null

    @SerializedName("benWard")
    val benWard: String? = null

    @SerializedName("benStreet")
    val benStreet: String? = null

    @SerializedName("finalBenName")
    val finalBenName: String? = null

    @SerializedName("finalBenCountry")
    val finalBenCountry: String? = null

    @SerializedName("finalBenDistrict")
    val finalBenDistrict: String? = null

    @SerializedName("finalBenWard")
    val finalBenWard: String? = null

    @SerializedName("finalBenStreet")
    val finalBenStreet: String? = null

    @SerializedName("benBankName")
    val benBankName: String? = null

    @SerializedName("midBankName2")
    val midBankName2: String? = null

    @SerializedName("midBankCode")
    val midBankCode: String? = null

    @SerializedName("benBankCountry")
    val benBankCountry: String? = null

    @SerializedName("benBankDistrict")
    val benBankDistrict: String? = null

    @SerializedName("benBankWard")
    val benBankWard: String? = null

    @SerializedName("benBankStreet")
    val benBankStreet: String? = null

    @SerializedName("midBankCountry")
    val midBankCountry: String? = null

    @SerializedName("midBankDistrict")
    val midBankDistrict: String? = null

    @SerializedName("midBankWard")
    val midBankWard: String? = null

    @SerializedName("midBankStreet")
    val midBankStreet: String? = null

    @SerializedName("statusNameBill")
    val statusNameBill: String? = null
}

data class SubTranItemListModel(
    @SerializedName("mtId") val mtId: String?,
    @SerializedName("tranType") val tranType: String?,
    @SerializedName("tranTypeName") val tranTypeName: String?,
    @SerializedName("amount") val amount: String?,
    @SerializedName("fromAccountNo") val fromAccountNo: String?,
    @SerializedName("fromAccountName") val fromAccountName: String?,
    @SerializedName("payCode") val payCode: String?,
    @SerializedName("payName") val payName: String?,
    @SerializedName("status") val status: String?,
    @SerializedName("statusName") val statusName: String?,
)

data class ItemTransactionMangerModel(
    @SerializedName("businessCode") val businessCode: String?,
    @SerializedName("content") val content: String?,
    @SerializedName("taxPeriod") val taxPeriod: String?,
    @SerializedName("amount") val amount: String?,
    @SerializedName("currency") val currency: String?,
    @SerializedName("itemCode") val itemCode: String?,
    @SerializedName("declareNumber") val declareNumber: String?,
    @SerializedName("declareDate") val declareDate: String?,
    @SerializedName("sitCode") val sitCode: String?,
    @SerializedName("taxId") val taxId: String?,
    @SerializedName("typePeriod") val typePeriod: String?,
    @SerializedName("hbNbno") val hbNbno: String?,
)

data class InquiryApproverResponse(
    @SerializedName("users") val usersList: List<UsersListModel>?,
)

data class UsersListModel(
    @SerializedName("approvalLevel") val approvalLevel: String?,
    @SerializedName("dailyLimit") val dailyLimit: String?,
    @SerializedName("fullName") val fullName: String?,
    @SerializedName("transLimit") val transLimit: String?,
    @SerializedName("userName") val userName: String?,
)

data class TraceInquiryResponse(
    @SerializedName("mtId") val mtId: String?,
    @SerializedName("serviceType") val serviceType: String?,
    @SerializedName("totalFee") val totalFee: String?,
    @SerializedName("tranType") val tranType: String?,
)
