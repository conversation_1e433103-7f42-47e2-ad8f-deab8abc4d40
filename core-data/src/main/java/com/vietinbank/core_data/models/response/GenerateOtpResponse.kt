package com.vietinbank.core_data.models.response

import com.google.gson.annotations.SerializedName

data class GenerateOtpResponse(
    @SerializedName("efastId")
    val efastId: String?,
    @SerializedName("messPopup")
    val messPopup: String?,
    @SerializedName("requestId")
    val requestId: String?,
    @SerializedName("sessionId")
    val sessionId: String?,
    @SerializedName("status")
    val status: Status?,
)

data class VerifyOtpEkycResponse(
    @SerializedName("biometricFace")
    val biometricFace: String?,
    @SerializedName("citizenship")
    val citizenship: String?,
    @SerializedName("contactAddDistrict")
    val contactAddDistrict: String?,
    @SerializedName("contactAddDistrictCode")
    val contactAddDistrictCode: String?,
    @SerializedName("contactAddProvince")
    val contactAddProvince: String?,
    @SerializedName("contactAddProvinceCode")
    val contactAddProvinceCode: String?,
    @SerializedName("contactAddStreet")
    val contactAddStreet: String?,
    @SerializedName("dataRetail")
    val dataRetail: DataRetail?,
    @SerializedName("differentData")
    val differentData: DifferentData?,
    @SerializedName("emailAddress")
    val emailAddress: String?,
    @SerializedName("fileGTDTT")
    val fileGTDTT: FileGTDTT?,
    @SerializedName("image")
    val imageUpdate: List<ImageUpdate?>?,
    @SerializedName("isRequireEmail")
    val isRequireEmail: String?,
    @SerializedName("marketingFlag")
    val marketingFlag: String?,
    @SerializedName("requestId")
    val requestId: String?,
    @SerializedName("sessionId")
    val sessionId: String?,
    @SerializedName("status")
    val status: Status?,
)

data class DataRetail(
    @SerializedName("dateOfBirth")
    val dateOfBirth: String?,
    @SerializedName("dateOfExpiry")
    val dateOfExpiry: String?,
    @SerializedName("dateOfIssuance")
    val dateOfIssuance: String?,
    @SerializedName("gender")
    val gender: String?,
    @SerializedName("idCard")
    val idCard: String?,
    @SerializedName("idType")
    val idType: String?,
    @SerializedName("issueLocation")
    val issueLocation: String?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("nationality")
    val nationality: String?,
    @SerializedName("religion")
    val religion: String?,
)

data class DifferentData(
    @SerializedName("dateOfBirth")
    val dateOfBirth: String?,
    @SerializedName("dateOfExpiry")
    val dateOfExpiry: String?,
    @SerializedName("dateOfIssuance")
    val dateOfIssuance: String?,
    @SerializedName("gender")
    val gender: String?,
    @SerializedName("idCard")
    val idCard: String?,
    @SerializedName("issueLocation")
    val issueLocation: String?,
    @SerializedName("name")
    val name: String?,
    @SerializedName("nationality")
    val nationality: String?,
    @SerializedName("religion")
    val religion: String?,
)

data class FileGTDTT(
    @SerializedName("file")
    val file: String?,
    @SerializedName("fileName")
    val fileName: String?,
    @SerializedName("status")
    val status: Status?,
)

data class ImageUpdate(
    @SerializedName("file")
    val file: String?,
    @SerializedName("fileName")
    val fileName: String?,
    @SerializedName("status")
    val status: Status?,
)
