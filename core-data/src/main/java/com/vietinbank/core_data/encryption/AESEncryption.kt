package com.vietinbank.core_data.encryption

import android.util.Base64
import java.security.MessageDigest
import javax.crypto.Cipher
import javax.crypto.SecretKey
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec
import kotlin.random.Random

/**
 * Utility object providing AES (Advanced Encryption Standard) encryption and decryption functionality.
 * Implements CBC (Cipher Block Chaining) mode with PKCS5 padding for secure data encryption.
 */
object AESEncryption {
    private const val ALGORITHM = "AES"
    private const val TRANSFORMATION = "AES/CBC/PKCS5Padding"

    /**
     * Encrypts a plain text string using AES encryption in CBC mode.
     *
     * The encryption process:
     * 1. Converts input text to bytes
     * 2. Initializes cipher with secret key and IV
     * 3. Performs encryption
     * 4. Combines IV with encrypted data
     * 5. Encodes result in Base64
     *
     * @param text The plain text string to encrypt
     * @param secretKey The secret key used for encryption
     * @param iv The initialization vector for CBC mode
     * @return Base64 encoded string of the encrypted data
     */
    fun encrypt(
        text: String,
        secretKey: SecretKey,
        iv: ByteArray,
    ): String {
        val clean = text.toByteArray()
        val ivSize = 16
        // Encrypt.
        val cipher = Cipher.getInstance(TRANSFORMATION)
        cipher.init(Cipher.ENCRYPT_MODE, secretKey, IvParameterSpec(iv))
        val encrypted = cipher.doFinal(clean)
        // Combine IV and encrypted part.
        val encryptedIVAndText = ByteArray(ivSize + encrypted.size)
        System.arraycopy(iv, 0, encryptedIVAndText, 0, ivSize)
        System.arraycopy(encrypted, 0, encryptedIVAndText, ivSize, encrypted.size)
        return Base64.encodeToString(encryptedIVAndText, Base64.DEFAULT)
    }

    /**
     * Decrypts an encrypted string using AES decryption in CBC mode.
     *
     * The decryption process:
     * 1. Decodes Base64 input
     * 2. Initializes cipher with secret key and IV
     * 3. Performs decryption
     * 4. Extracts and returns the original plain text
     *
     * @param encryptedText The Base64 encoded encrypted string
     * @param secretKey The secret key used for decryption
     * @param iv The initialization vector used during encryption
     * @return The original decrypted plain text
     */
    fun decrypt(
        encryptedText: String,
        secretKey: SecretKey,
        iv: ByteArray,
    ): String {
        val cipherDecrypt = Cipher.getInstance(TRANSFORMATION)
        cipherDecrypt.init(Cipher.DECRYPT_MODE, secretKey, IvParameterSpec(iv))
        val decrypted = cipherDecrypt.doFinal(Base64.decode(encryptedText, Base64.DEFAULT))
        val realBytes = decrypted.copyOfRange(iv.size, decrypted.size)
        return String(realBytes)
    }

    /**
     * Generates a random string of specified length using uppercase letters and numbers.
     * Used for creating random keys and other cryptographic parameters.
     *
     * @param length The desired length of the random string
     * @return A random string containing uppercase letters and numbers
     */
    fun generateRandomString(length: Int): String {
        val charPool: List<Char> = ('A'..'Z') + ('0'..'9')

        return (1..length)
            .map { Random.nextInt(0, charPool.size) }
            .map(charPool::get)
            .joinToString("")
    }

    /**
     * Generates an AES secret key from an input string using SHA-256 hashing.
     *
     * The process:
     * 1. Hashes the input string using SHA-256
     * 2. Takes the first 16 bytes of the hash
     * 3. Creates an AES secret key specification
     *
     * @param input The input string to generate the key from
     * @return SecretKey instance for AES encryption
     */
    fun generateKey(input: String): SecretKey {
        val digest = MessageDigest.getInstance("SHA-256")
        digest.update(input.toByteArray(charset("UTF-8")))
        val keyBytes = ByteArray(16)
        System.arraycopy(digest.digest(), 0, keyBytes, 0, keyBytes.size)
        return SecretKeySpec(keyBytes, ALGORITHM)
    }

    /**
     * Generates an initialization vector (IV) from an input string.
     * The IV is used in CBC mode to ensure that identical plain texts
     * encrypt to different cipher texts.
     *
     * @param input The input string to generate the IV from
     * @return ByteArray containing the IV
     */
    fun generateIV(input: String): ByteArray {
        return input.toByteArray()
    }
}
