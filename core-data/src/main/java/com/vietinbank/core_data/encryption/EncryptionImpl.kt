package com.vietinbank.core_data.encryption

import com.vietinbank.securitylib.NativeLibrary
import java.math.BigInteger
import java.nio.charset.StandardCharsets
import java.security.MessageDigest
import javax.crypto.SecretKey

/**
 * Implementation of the IEncryption interface that provides encryption and decryption functionality.
 * This class implements a hybrid encryption system using both AES and RSA algorithms.
 *
 * Key features:
 * - Uses AES for data encryption with randomly generated keys
 * - Employs RSA for key encryption
 * - Maintains a secure key storage mechanism
 * - Provides signature generation for API requests
 */
class EncryptionImpl() : IEncryption {
    companion object {
        /**
         * Stores the mapping between differentiate keys and their corresponding encryption parameters (IV and SecretKey)
         */
        private val differentiateLinkList = mutableListOf<StoreKeyAndLink>()
    }

    /**
     * Encrypts the provided text using a hybrid encryption system (AES + RSA).
     *
     * The encryption process:
     * 1. Generates a random AES secret key and IV
     * 2. Encrypts the input text using AES
     * 3. Encrypts the AES key and IV using RSA
     * 4. Stores the encryption parameters for later decryption
     *
     * @param text The plain text to be encrypted
     * @param key Optional differentiate key for storing encryption parameters. If empty, a random key is generated
     * @return Encryption object containing the encrypted text, encrypted secret key, and encrypted IV
     */
    override fun encrypt(
        text: String,
        key: String,
    ): Encryption {
        // Generate a random secret key and IV
        val secretKeyString = AESEncryption.generateRandomString(16)
        val secretKey = AESEncryption.generateKey(secretKeyString)

//        printLog("Pub key: ${NativeLibrary.getPublicKey(1)}")

        // Create IV
        val ivString = NativeLibrary.getIVApiKey()
        val iv = AESEncryption.generateIV(ivString)

        val encryptedText = AESEncryption.encrypt(text, secretKey, iv)
            .replace("\n", "").replace("\r", "")
//        printLog("encryptedvalue before payload$encryptedText")

        val signaturevKey = encryptedText
//        printLog("signaturevKey $signaturevKey")

        // encrypt RSA secret key
        val encryptedSecretKey =
            RSAEncryption.encryptData(secretKeyString, NativeLibrary.getPublicKey(1))
//        printLog("encryptedSecretKey $encryptedSecretKey")

        // encrypt RSA iv
        val encryptedIV = RSAEncryption.encryptData(ivString, NativeLibrary.getPublicKey(1))

        // random key
        val differentiateKey =
            if (key == "") {
                AESEncryption.generateRandomString(16)
            } else {
                key
            }
        differentiateLinkList.add(StoreKeyAndLink(differentiateKey, iv, secretKey))
        // author ninhio
//        if(differentiateLinkList.find { it.differentiate == differentiateKey }==null){
//            differentiateLinkList.add(StoreKeyAndLink(differentiateKey, iv, secretKey))
//        }
//        printLog("DifferentiateLinkList size after encrypt: ${differentiateLinkList.size}")
//        printLog("Stored differentiateKey: $differentiateKey")
        return Encryption(
            encryptionValue = encryptedText,
            secretKey = encryptedSecretKey,
            iv = encryptedIV,
        )
    }

    /**
     * Decrypts the provided encrypted text using stored encryption parameters.
     *
     * The decryption process:
     * 1. Looks up the encryption parameters using the provided key
     * 2. Uses the stored SecretKey and IV to decrypt the text
     * 3. Removes the used encryption parameters from storage
     *
     * @param text The encrypted text to be decrypted
     * @param key The differentiate key used to look up encryption parameters
     * @return The decrypted plain text
     * @throws DecryptionException if the key is not found or decryption fails
     */
    override fun decrypt(
        text: String,
        key: String,
    ): String {
//        printLog("DifferentiateLinkList size before decrypt: ${differentiateLinkList.size}")
//        printLog("Trying to find key: $key")
        if (differentiateLinkList.isEmpty()) {
            throw DecryptionException("Không có khóa giải mã khả dụng")
        }
        val link = differentiateLinkList.find { it.differentiate == key }
        return if (link != null) {
            val secretKey = link.secretKey
            val iv = link.iv
            differentiateLinkList.remove(link)
            try {
                AESEncryption.decrypt(text, secretKey, iv)
            } catch (e: Exception) {
                throw DecryptionException("Lỗi giải mã: ${e.message}", e)
            }
        } else {
            throw DecryptionException("Không tìm thấy khóa giải mã phù hợp")
        }
    }

    /**
     * Creates a signature key for API request authentication.
     *
     * The signature is generated by:
     * 1. Concatenating request parameters with a secret key
     * 2. Computing the SHA-256 hash of the concatenated string
     * 3. Converting the hash to a hexadecimal string
     *
     * @param requestPath The API endpoint path
     * @param requestId Unique identifier for the request
     * @param sessionId Current session identifier
     * @return The generated signature key as a hexadecimal string
     */
    override fun createSignatureKey(
        requestPath: String,
        requestId: String,
        sessionId: String,
    ): String {
        // Tạo chuỗi kết hợp
        val key = "${requestPath}${requestId}${sessionId}${NativeLibrary.getSecretKeySha256(1)}"
        // Chuyển chuỗi thành hash SHA-256
        val md = MessageDigest.getInstance("SHA-256")
        val keyBytes = md.digest(key.toByteArray(StandardCharsets.UTF_8))

        // Chuyển byte SHA-256 thành chuỗi hex
        val number = BigInteger(1, keyBytes)
        val hexString = java.lang.StringBuilder(number.toString(16))
        while (hexString.length < 64) {
            hexString.insert(0, '0')
        }
        return hexString.toString()
    }

    /**
     * Data class to store encryption parameters (differentiate key, IV, and SecretKey) for later decryption.
     *
     * @param differentiate Unique key to differentiate between encryption parameters
     * @param iv Initialization vector used for encryption
     * @param secretKey Secret key used for encryption
     */
    data class StoreKeyAndLink(
        val differentiate: String,
        val iv: ByteArray,
        val secretKey: SecretKey,
    ) {
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false

            other as StoreKeyAndLink

            if (differentiate != other.differentiate) return false
            return iv.contentEquals(other.iv)
        }

        override fun hashCode(): Int {
            var result = differentiate.hashCode()
            result = 31 * result + iv.contentHashCode()
            return result
        }
    }
}

/**
 * Custom exception class for handling decryption-related errors.
 * Provides specific error messages for different decryption failure scenarios.
 *
 * @param message Detailed error message
 * @param cause Optional underlying cause of the error
 */
class DecryptionException(message: String, cause: Throwable? = null) : Exception(message, cause)
