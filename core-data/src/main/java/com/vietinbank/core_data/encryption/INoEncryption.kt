package com.vietinbank.core_data.encryption

/**
 * Implementation của IEncryption không thực hiện mã hóa.
 * Trả về text gốc mà không thực hiện bất kỳ mã hóa/gi<PERSON>i mã nào.
 */
class INoEncryption : IEncryption {
    override fun encrypt(text: String, key: String): Encryption {
        return Encryption(
            encryptionValue = text,
            secretKey = "",
            iv = "",
        )
    }

    override fun decrypt(text: String, key: String): String {
        return text
    }

    override fun createSignatureKey(
        requestPath: String,
        requestId: String,
        sessionId: String,
    ): String {
        return "" // Không cần signature khi không mã hóa
    }
}
