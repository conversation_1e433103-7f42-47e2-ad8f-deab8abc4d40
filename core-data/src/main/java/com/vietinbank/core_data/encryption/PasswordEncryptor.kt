package com.vietinbank.core_data.encryption

import android.util.Base64
import com.vietinbank.securitylib.NativeLibrary
import java.security.MessageDigest
import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Created by vandz on 3/3/25.
 * Mã hóa AES-128/CBC/PKCS7Padding.
 */
@Singleton
class PasswordEncryptor @Inject constructor() {

    /**
     * Tính SHA-256 trên mảng byte => 32 byte.
     */
    fun sha256(data: ByteArray): ByteArray {
        val digest = MessageDigest.getInstance("SHA-256")
        return digest.digest(data)
    }

    /**
     * Mã hoá AES-128/CBC/PKCS5Padding (PKCS7).
     * - key16: phải đúng 16 byte (AES-128).
     * - iv16: 16 byte IV.
     * - Chỉ trả về ciphertext (chưa gắn IV).
     */
    fun aesCBCEncrypt(
        plainData: ByteArray,
        key16: ByteArray,
        iv16: ByteArray,
    ): ByteArray {
        val cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
        val secretKeySpec = SecretKeySpec(key16, "AES")
        val ivSpec = IvParameterSpec(iv16)

        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivSpec)
        return cipher.doFinal(plainData)
    }

    /**
     * Giải mã AES-128/CBC (nếu cần dùng).
     */
    fun aesCBCDecrypt(
        cipherData: ByteArray,
        key16: ByteArray,
        iv16: ByteArray,
    ): ByteArray {
        val cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
        val secretKeySpec = SecretKeySpec(key16, "AES")
        val ivSpec = IvParameterSpec(iv16)

        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivSpec)
        return cipher.doFinal(cipherData)
    }

    /**
     * Lấy 16 byte IV từ 1 chuỗi ciphertext base64 .
     *
     * VD gởi: "E5xJi97FcD7ysZyL2D2WEiDLJhd63uLISI7/KRAVHn8=",
     *  -> decode ra mảng byte,
     *  -> 16 byte đầu = IV.
     */
    fun extractIVFromTextBase64(cipherBase64: String): ByteArray {
        val allBytes = Base64.decode(cipherBase64, Base64.NO_WRAP)
        return allBytes.copyOfRange(0, 16) // 16 byte đầu
    }

    /**
     * Hàm để mã hoá password user
     *  1) Lấy passphrase từ native lib
     *  2) SHA-256 => 16 byte => AES-128 key
     *  3) Lấy IV base64 từ native lib => 16 byte
     *  4) AES CBC encrypt userPlainPassword => ciphertext
     *  5) Ghép IV + ciphertext => base64 => return
     */
    fun getEncryptedPassword(userPlainPassword: String): String {
        // 1) Lấy passphrase
        val passphrase = NativeLibrary.getLoginParsePhase()
        val passBytes = passphrase.toByteArray(Charsets.UTF_8)
        val hash256 = sha256(passBytes) // 32 byte
        val aesKey16 = hash256.copyOfRange(0, 16)

        // 2) Lấy IV base64, cắt 16 byte
        val ivBase64 = NativeLibrary.getHaspLoginPwd()
        val ivBytes = extractIVFromTextBase64(ivBase64)

        // 3) Encrypt userPlainPassword
        val plainData = userPlainPassword.toByteArray(Charsets.UTF_8)
        val cipherData = aesCBCEncrypt(plainData, aesKey16, ivBytes)

        // 4) Ghép IV + ciphertext => base64
        val finalBytes = ByteArray(ivBytes.size + cipherData.size)
        System.arraycopy(ivBytes, 0, finalBytes, 0, ivBytes.size)
        System.arraycopy(cipherData, 0, finalBytes, ivBytes.size, cipherData.size)

        return Base64.encodeToString(finalBytes, Base64.NO_WRAP)
    }
}
