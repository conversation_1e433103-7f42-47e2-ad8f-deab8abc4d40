package com.vietinbank.core_data.encryption

/**
 * Created by vand<PERSON> on 19/12/24.
 */
interface IEncryption {
    /**
     * encrypt value before forwarder
     * @param text: value need encrypt
     * @param key: to differentiate a link corresponding the iv, secreteKey
     * @return encrypted value
     */
    fun encrypt(
        text: String,
        key: String,
    ): Encryption

    /**
     * decrypt value get from server
     * @param text: string need decryption
     * @return Decrypted Value
     */
    fun decrypt(
        text: String,
        key: String,
    ): String

    /**
     * create signature key
     */
    fun createSignatureKey(
        requestPath: String = "",
        requestId: String = "",
        sessionId: String = "",
    ): String
}
