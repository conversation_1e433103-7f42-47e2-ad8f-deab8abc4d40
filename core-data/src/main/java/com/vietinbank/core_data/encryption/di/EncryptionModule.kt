package com.vietinbank.core_data.encryption.di

import com.vietinbank.core_common.utils.GsonProvider
import com.vietinbank.core_data.encryption.EncryptionImpl
import com.vietinbank.core_data.encryption.EncryptionService
import com.vietinbank.core_data.encryption.IEncryption
import com.vietinbank.core_data.encryption.INoEncryption
import com.vietinbank.core_data.encryption.nativeinital.ILibraryInitializer
import com.vietinbank.core_data.encryption.nativeinital.LibraryInitializerImpl
import com.vietinbank.core_data.network.di.Default
import com.vietinbank.core_data.network.di.NoEncryption
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Module quản lý các dependency liên quan đến mã hóa trong ứng dụng.
 *
 * Module này cung cấp các implementation khác nhau cho mã hóa dữ liệu,
 * bao gồm cả mã hóa mặc định và không mã hóa, phục vụ cho các use case kh<PERSON>c nhau.
 *
 * Cấu trúc phân tầng:
 * 1. Library Initialization - Khởi tạo thư viện mã hóa
 * 2. Base Encryption - Các implementation cơ bản (Default và NoEncryption)
 * 3. Encryption Services - Các service wrapper cho việc mã hóa
 *
 * @see IEncryption
 * @see EncryptionService
 * @see Default
 * @see NoEncryption
 */
@Module
@InstallIn(SingletonComponent::class)
object EncryptionModule {
    /**
     * Cung cấp initializer cho thư viện mã hóa native.
     */
    @Singleton
    @Provides
    fun provideLibraryInitializer(): ILibraryInitializer {
        return LibraryInitializerImpl()
    }

    // 2. Base encryption implementations
    @Singleton
    @Provides
    @Default
    fun provideIEncryption(): IEncryption {
        return EncryptionImpl()
    }

    @Provides
    @NoEncryption
    fun provideNoEncryption(): IEncryption {
        return INoEncryption()
    }

    // 3. Encryption services
    @Singleton
    @Provides
    @Default
    fun provideEncryptionService(
        @Default encryptionImpl: IEncryption,
        gsonProvider: GsonProvider,
    ): EncryptionService {
        return EncryptionService(encryptionImpl, gsonProvider)
    }

    @Singleton
    @Provides
    @NoEncryption
    fun provideNoEncryptionService(
        @NoEncryption encryption: IEncryption,
        gsonProvider: GsonProvider,
    ): EncryptionService {
        return EncryptionService(encryption, gsonProvider)
    }
}
