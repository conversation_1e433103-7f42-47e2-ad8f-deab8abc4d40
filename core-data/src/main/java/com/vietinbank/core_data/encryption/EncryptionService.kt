package com.vietinbank.core_data.encryption

import android.util.Log
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.utils.GsonProvider
import com.vietinbank.core_data.models.base.BaseEncryptRequest
import com.vietinbank.core_data.models.request.CommonRequest
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Created by vandz on 25/12/24.
 */
interface IEncryptionService {
    fun <T : CommonRequest> encryptRequest(request: T, differentiateKey: String): BaseEncryptRequest
    fun decrypt(encryptedData: String, differentiateKey: String): String
}

class EncryptionService(
    private val encryption: IEncryption,
    private val gsonProvider: GsonProvider,
) : IEncryptionService {

    /**
     * Hàm mã hóa request.
     * @param request Dữ liệu request cần mã hóa.
     * @param differentiateKey khóa khác nhau cho mỗi request để sinh khóa mã hóa.
     * @return Dữ liệu đã mã hóa.
     */
    override fun <T : CommonRequest> encryptRequest(
        request: T,
        differentiateKey: String,
    ): BaseEncryptRequest {
        return try {
            // Extract required fields
            val requestId: String = request.requestId
            val sessionId: String = request.sessionId
            val requestPath: String = request.requestPath

            // Create signature
            val signature = encryption.createSignatureKey(requestPath, requestId, sessionId)

            // Serialize request to JSON
            val requestJson = gsonProvider.provideGson().toJson(request)

            // Update signature in JSON
            val modifiedRequestJson = updateSignature(requestJson, signature)
            printLog("Full REQ: $modifiedRequestJson")

//            printLog("Generated key: $differentiateKey")

            // Encrypt the JSON
            val encryptedData = encryption.encrypt(modifiedRequestJson, differentiateKey)

            // Wrap into BaseEncryptRequest
            BaseEncryptRequest(
                encrypted = encryptedData.encryptionValue,
                publicKey = encryptedData.secretKey,
            )
        } catch (e: Exception) {
            Log.e("API_CALL_LOG", "Encryption failed: ${e.message}", e)
            throw AppException.EncryptionException("Failed to encrypt request.", e, "", "118")
        }
    }

    /**
     * Hàm giải mã response.
     * @param encryptedData Dữ liệu response đã mã hóa cần giải mã.
     * @param differentiateKey Khóa khác nhau cho mỗi request để sinh khóa mã hóa.
     * @return Dữ liệu đã giải mã.
     */
    override fun decrypt(encryptedData: String, differentiateKey: String): String {
        return try {
            encryption.decrypt(encryptedData, differentiateKey)
        } catch (e: Exception) {
            throw AppException.EncryptionException("Failed to decrypt response.", e, "", "118")
        }
    }

    /**
     * Catch all exceptions that occurred while decrypting the response.
     * Log the error with the decrypted message and rethrow the exception.
     *
     * @param encryptedData dữ liệu response đã mã hóa cần giải mã.
     * @param differentiateKey Khóa khác nhau cho mỗi request để sinh khóa mã hóa.
     */
    private fun updateSignature(requestJson: String, signature: String): String {
        val jsonObject = JSONObject(requestJson)
        jsonObject.put("signature", signature)
        return jsonObject.toString()
    }

    /**
     * Update signature in JSON.
     * @param requestJson JSON string of the request.
     * @param signature signature generated by {@link #createSignatureKey(String, String, String)}.
     * @return modified JSON string with signature added.
     */
    fun generateDifferentiateKey(requestID: String): String {
        val currentTime = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        return "$currentTime$requestID"
    }
}
