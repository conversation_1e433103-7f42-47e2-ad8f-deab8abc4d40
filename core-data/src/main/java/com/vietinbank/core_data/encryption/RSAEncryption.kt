package com.vietinbank.core_data.encryption

import android.util.Base64
import java.security.KeyFactory
import java.security.PublicKey
import java.security.spec.X509EncodedKeySpec
import javax.crypto.Cipher

/**
 * Utility object providing RSA (<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>-<PERSON>) encryption functionality.
 * Implements RSA encryption with OAEP (Optimal Asymmetric Encryption Padding) for enhanced security.
 *
 * Features:
 * - Uses RSA/ECB/OAEPWithSHA1AndMGF1Padding transformation
 * - Supports public key encryption
 * - Handles PEM formatted public keys
 */
object RSAEncryption {
    private const val RSA_ALGORITHM = "RSA"
    private const val TRANSFORMATION = "RSA/ECB/OAEPWithSHA1AndMGF1Padding"

    /**
     * Encrypts data using RSA public key encryption.
     *
     * The encryption process:
     * 1. Processes the PEM formatted public key
     * 2. Converts the key to X509 specification
     * 3. Initializes RSA cipher with <PERSON>A<PERSON> padding
     * 4. Performs encryption
     * 5. Encodes the result in Base64
     *
     * @param input The plain text string to encrypt
     * @param key The PEM formatted public key string
     * @return Base64 encoded encrypted string
     * @throws IllegalArgumentException if the public key format is invalid
     * @throws javax.crypto.IllegalBlockSizeException if the input data is too large for RSA encryption
     */
    fun encryptData(
        input: String,
        key: String,
    ): String {
        val publicKeyPEM =
            key
                .replace("-----BEGIN PUBLIC KEY-----", "")
                .replace(System.lineSeparator(), "")
                .replace("-----END PUBLIC KEY-----", "")

        val publicKey = Base64.decode(publicKeyPEM, Base64.DEFAULT)
        val spec = X509EncodedKeySpec(publicKey)
        val factory = KeyFactory.getInstance(RSA_ALGORITHM)
        val pubKey: PublicKey = factory.generatePublic(spec)

        val c = Cipher.getInstance(TRANSFORMATION)
        c.init(Cipher.ENCRYPT_MODE, pubKey)
        val encryptOut = c.doFinal(input.toByteArray())
        return Base64.encodeToString(encryptOut, Base64.DEFAULT).replace("\n", "")
    }
}
