package com.vietinbank.core_data.qr

import android.graphics.Bitmap
import android.text.TextUtils
import androidx.core.graphics.createBitmap
import androidx.core.graphics.set
import com.google.zxing.BarcodeFormat
import com.google.zxing.BinaryBitmap
import com.google.zxing.DecodeHintType
import com.google.zxing.EncodeHintType
import com.google.zxing.LuminanceSource
import com.google.zxing.MultiFormatReader
import com.google.zxing.RGBLuminanceSource
import com.google.zxing.Reader
import com.google.zxing.common.HybridBinarizer
import com.google.zxing.qrcode.QRCodeWriter
import com.vietinbank.core_common.extensions.removeAllSpaces
import com.vietinbank.core_common.extensions.removeVietNam
import com.vietinbank.core_domain.models.qr.QrCodeDomain
import com.vietinbank.core_domain.qr.IQrCodeManager
import java.util.EnumSet
import java.util.Hashtable
import java.util.Locale
import javax.inject.Inject
import kotlin.collections.set

class QrCodeManagerImpl @Inject constructor() : IQrCodeManager {
    companion object {
        const val SERVICEACCOUNT = "QRIBFTTA"
        const val SERVICECARD = "QRIBFTTC"
    }

    override fun generateQRData(
        bankCode: String,
        accountNumber: String,
        amount: String?,
        desc: String?,
        isTransferAccount: Boolean,
    ): String? {
        val dataFormat = HashMap<String, String>()
        // init data
        dataFormat["00"] = "01"
        dataFormat["01"] = if (amount == null) "11" else "12"
        dataFormat["53"] = "704"
        dataFormat["58"] = "VN"
        // key 54 amount
        if (!TextUtils.isEmpty(amount)) {
            dataFormat["54"] = amount!!
        }
        // key 62 content
        if (!TextUtils.isEmpty(desc)) {
            dataFormat["62"] = "08${format(desc?.length!!)}${desc.removeVietNam()}"
        }
        // account
        if (!TextUtils.isEmpty(accountNumber)) {
            // bank + account
            val infoBank = format(bankCode.length) + bankCode
            val infoAccountNumber =
                format(accountNumber.removeAllSpaces().length) + accountNumber.removeAllSpaces()
            val accountQR = "00$infoBank" + "01$infoAccountNumber"
            // format bank + account
            val infoAccountQR = format(accountQR.length) + accountQR

            // format type qr
            val infoType = if (isTransferAccount) {
                format(SERVICEACCOUNT.length) + SERVICEACCOUNT
            } else {
                format(SERVICECARD.length) + SERVICECARD
            }
            // format account final
            val infoFinal = "0010A00000072701$infoAccountQR" + "02$infoType"

            dataFormat["38"] = infoFinal
        }

        dataFormat["63"] = generateChecksumCRC16(getPayloadDataFormat(dataFormat) + "6304")

        return getPayloadDataFormat(dataFormat)
    }

    override fun generateQRCodeImage(
        data: String,
        colorQr: Int,
        colorBg: Int,
    ): Bitmap? {
        val writer = QRCodeWriter()
        try {
            val hintsMap = Hashtable<EncodeHintType, Any>()
            hintsMap[EncodeHintType.CHARACTER_SET] = "UTF-8"

            val bitMatrix = writer.encode(
                data,
                BarcodeFormat.QR_CODE,
                512,
                512,
                hintsMap,
            ) // Adjust size as needed

            val width = bitMatrix.width
            val height = bitMatrix.height
            val bmp = createBitmap(width, height, Bitmap.Config.RGB_565)

            for (x in 0 until width) {
                for (y in 0 until height) {
                    bmp[x, y] = if (bitMatrix.get(x, y)) {
                        colorQr
                    } else {
                        colorBg
                    }
                }
            }
            return bmp
        } catch (_: Exception) {
            return null
        }
    }

    override fun handleQRCodeNapasInfo(qrCodeString: String?): QrCodeDomain? {
        try {
            val qrCodeDomain = QrCodeDomain()
            val mapData = mapQrTags(qrCodeString)
            // 38 - thong tin chi tiet
            if (mapData.containsKey("38")) {
                val valueKey38 = mapData["38"]
                val mapDataKey38 = mapQrTags(valueKey38)
                if (mapDataKey38.containsKey("01")) {
                    val valueSubTagKey01 = mapDataKey38["01"]
                    val mapDataKeySubTag01 = mapQrTags(valueSubTagKey01)
                    if (mapDataKeySubTag01.containsKey("00")) {
                        qrCodeDomain.binCode = (mapDataKeySubTag01["00"])
                    }
                    if (mapDataKeySubTag01.containsKey("01")) {
                        qrCodeDomain.accountNumber = mapDataKeySubTag01["01"]
                    }
                }
            }
            // 54 - ma tien te
            if (mapData.containsKey("54")) {
                qrCodeDomain.amount = mapData["54"]
            }
            // 62 - moi dung
            if (mapData.containsKey("62")) {
                // check subtagID
                var mDescription = mapData["62"]
                try {
                    // sample 0810chuyentien
                    //  "01", "02", "03", "04", "05", "06", "07", "08"
                    mDescription?.substring(0, 2)?.toIntOrNull()?.let {
                        if (it < 9 && it > 0) {
                            mDescription = if (mDescription.length >= 4) {
                                mDescription.substring(4)
                            } else {
                                mDescription.substring(2)
                            }
                        }
                    }
                } catch (_: Exception) {
                }
                qrCodeDomain.remark = mDescription
            }
            return qrCodeDomain
        } catch (_: Exception) {
        }
        return null
    }

    override fun scanWithBitmap(
        isZxing: Boolean,
        bitmap: Bitmap?,
    ): String? {
        if (bitmap == null) return null
        if (isZxing) {
            val intArray = IntArray(bitmap.width * bitmap.height)
            // copy pixel data from the Bitmap into the 'intArray' array
            bitmap.getPixels(intArray, 0, bitmap.width, 0, 0, bitmap.width, bitmap.height)
            val source: LuminanceSource = RGBLuminanceSource(bitmap.width, bitmap.height, intArray)
            val binaryBitmap = BinaryBitmap(HybridBinarizer(source))
            val reader: Reader = MultiFormatReader() // use this otherwise
            // ChecksumException
            try {
                val decodeHints = Hashtable<DecodeHintType, Any?>()
                decodeHints[DecodeHintType.TRY_HARDER] = java.lang.Boolean.TRUE
                decodeHints[DecodeHintType.POSSIBLE_FORMATS] = EnumSet.allOf(
                    BarcodeFormat::class.java,
                )
                val result = reader.decode(binaryBitmap, decodeHints)
                return result.text
            } catch (_: Exception) {
            }
        }
        return null
    }

    // util
    private fun format(number: Int): String {
        return String.format(Locale.getDefault(), "%02d", number)
    }

    private fun getPayloadDataFormat(dataFormat: Map<String, String>): String {
        val keys = dataFormat.keys.sorted()
        var payloadStr = ""
        keys.forEach { key ->
            dataFormat[key]?.let { value ->
                payloadStr += "$key${format(value.length)}$value"
            }
        }
        return payloadStr
    }

    private fun generateChecksumCRC16(text: String): String {
        try {
            val bytes = text.toByteArray()
            var crc = 0xFFFF // initial value
            val polynomial = 0x1021 // 0001 0000 0010 0001 (0, 5, 12)
            for (b in bytes) {
                for (i in 0..7) {
                    val bit = b.toInt() shr 7 - i and 1 == 1
                    val c15 = crc shr 15 and 1 == 1
                    crc = crc shl 1
                    if (c15 xor bit) crc = crc xor polynomial
                }
            }
            crc = crc and 0xffff
            return String.format("%04X", crc)
        } catch (_: Exception) {
        }
        return ""
    }

    // 000201 - phien ban du lieu
    // 010212 - phuong thuc khoi tao
    // thong tin chi tiet
    // 38560010A0000007270126
    // 00 06 970415 // thong tin bank code
    // 01 12 ************ // thong tin tai khoan
    // 0208QRIBFTTA - chuyen tien toi tk hoac chuyen tien toi the
    // 5303704 - ma tien te
    // ********** - so tien giao dich
    // 5802VN - ma quoc gia
    // 62080804tuna - noi dung
    // 6304B6E7 - crc
    private fun mapQrTags(qrCodeString: String?): MutableMap<String, String> {
        val mapKey = HashMap<String, String>()
        try {
            var textString = qrCodeString
            while (!TextUtils.isEmpty(textString)) {
                val key = textString!!.substring(0, 2)
                val lengthValue = textString.substring(2, 4).toInt()
                val value = textString.substring(4, lengthValue + 4)
                mapKey.put(key, value)
//                println("TuNA5: $key - $value")
                textString = textString.substring(lengthValue + 4)
            }
        } catch (_: Exception) {
        }
        return mapKey
    }
}