package com.vietinbank.core_data.di

import android.content.Context
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_data.util.impl.ResourceProviderImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Created by vandz on 25/4/25.
 */
@Module
@InstallIn(SingletonComponent::class)
object UtilitiesModule {

    @Provides
    @Singleton
    fun provideResourceProvider(@ApplicationContext context: Context): IResourceProvider {
        return ResourceProviderImpl(context)
    }
}
