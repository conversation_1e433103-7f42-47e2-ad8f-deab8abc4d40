package com.vietinbank.core_data.di

import com.vietinbank.core_common.utils.GsonProvider
import com.vietinbank.core_data.encryption.PasswordEncryptor
import com.vietinbank.core_data.network.api.ApiService
import com.vietinbank.core_data.network.api.IApiClient
import com.vietinbank.core_data.network.di.Default
import com.vietinbank.core_data.network.download.IFileDownloadClient
import com.vietinbank.core_data.repository.AccountRepositoryImpl
import com.vietinbank.core_data.repository.CSatRepositoryImpl
import com.vietinbank.core_data.repository.CheckPasswordRepositoryImpl
import com.vietinbank.core_data.repository.CheckerRepositoryImpl
import com.vietinbank.core_data.repository.EkycFeatureRepositoryImpl
import com.vietinbank.core_data.repository.HomeRepositoryImpl
import com.vietinbank.core_data.repository.LoginRepositoryImpl
import com.vietinbank.core_data.repository.OttFeatureRepositoryImpl
import com.vietinbank.core_data.repository.ReportTransferRepositoryImpl
import com.vietinbank.core_data.repository.SmartCARepositoryImpl
import com.vietinbank.core_data.repository.SoftRepositoryImpl
import com.vietinbank.core_data.repository.TransactionManageRepositoryImpl
import com.vietinbank.core_data.repository.TransferRepositoryImpl
import com.vietinbank.core_data.repository.UpdateEkycRepositoryImpl
import com.vietinbank.core_domain.repository.AccountRepository
import com.vietinbank.core_domain.repository.CSatRepository
import com.vietinbank.core_domain.repository.CheckPasswordRepository
import com.vietinbank.core_domain.repository.CheckerRepository
import com.vietinbank.core_domain.repository.EkycFeatureRepository
import com.vietinbank.core_domain.repository.HomeRepository
import com.vietinbank.core_domain.repository.LoginRepository
import com.vietinbank.core_domain.repository.OttFeatureRepository
import com.vietinbank.core_domain.repository.ReportTransferRepository
import com.vietinbank.core_domain.repository.SmartCARepository
import com.vietinbank.core_domain.repository.SoftRepository
import com.vietinbank.core_domain.repository.TransactionManageRepository
import com.vietinbank.core_domain.repository.TransferRepository
import com.vietinbank.core_domain.repository.UpdateEkycRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Module cung cấp các dependency cho core-data layer trong kiến trúc Clean Architecture.
 *
 * Module này được cài đặt trong [SingletonComponent], đảm bảo các dependency được duy trì
 * trong suốt vòng đời của ứng dụng.
 *
 * Chức năng chính:
 * - Cung cấp các Repository implementation cho domain layer
 * - Quản lý việc inject các dependency cần thiết cho Repository
 * - Đảm bảo tính nhất quán của dữ liệu thông qua Singleton pattern
 *
 * @see SingletonComponent
 * @see LoginRepository
 */
@Module
@InstallIn(SingletonComponent::class)
object DataModule {
    /**
     * Cung cấp implementation của [LoginRepository].
     *
     * @param apiClient Client API với mã hóa mặc định [@Default]
     * @param apiService Service interface cho các API calls
     * @param gsonProvider Provider cho JSON serialization/deserialization
     * @param passwordEncryptor Service xử lý má hóa password
     * @return Implementation của [LoginRepository]
     */
    @Provides
    @Singleton
    fun provideLoginRepository(
        @Default apiClient: IApiClient,
        apiService: ApiService,
        gsonProvider: GsonProvider,
        passwordEncryptor: PasswordEncryptor,
    ): LoginRepository {
        return LoginRepositoryImpl(apiClient, apiService, gsonProvider, passwordEncryptor)
    }

    /**
     * Cung cấp implementation của [HomeRepository].
     *
     * @param apiClient Client API với mã hóa mặc định [@Default]
     * @param apiService Service interface cho các API calls
     * @param gsonProvider Provider cho JSON serialization/deserialization
     * @param passwordEncryptor Service xử lý má hóa password
     * @return Implementation của [HomeRepository]
     */
    @Provides
    @Singleton
    fun provideHomeRepository(
        @Default apiClient: IApiClient,
        apiService: ApiService,
        gsonProvider: GsonProvider,
        passwordEncryptor: PasswordEncryptor,
    ): HomeRepository {
        return HomeRepositoryImpl(apiClient, apiService, gsonProvider, passwordEncryptor)
    }

    /**
     * Cung cấp implementation của [CheckerRepository].
     *
     * @param apiClient Client API với mã hóa mặc định [@Default]
     * @param apiService Service interface cho các API calls
     * @param gsonProvider Provider cho JSON serialization/deserialization
     * @param fileDownloadClient Client để xử lý download file
     * @return Implementation của [CheckerRepository]
     */
    @Provides
    @Singleton
    fun provideCheckerRepository(
        @Default apiClient: IApiClient,
        apiService: ApiService,
        gsonProvider: GsonProvider,
        fileDownloadClient: IFileDownloadClient,
    ): CheckerRepository {
        return CheckerRepositoryImpl(apiClient, apiService, gsonProvider, fileDownloadClient)
    }

    @Provides
    @Singleton
    fun provideTransferRepository(
        @Default apiClient: IApiClient,
        apiService: ApiService,
        gsonProvider: GsonProvider,
    ): TransferRepository {
        return TransferRepositoryImpl(apiClient, apiService, gsonProvider)
    }

    @Provides
    @Singleton
    fun provideSoftRepository(
        @Default apiClient: IApiClient,
        apiService: ApiService,
        gsonProvider: GsonProvider,
        passwordEncryptor: PasswordEncryptor,
    ): SoftRepository {
        return SoftRepositoryImpl(apiClient, apiService, gsonProvider, passwordEncryptor)
    }

    // feature transaction manage
    @Provides
    @Singleton
    fun provideTransactionManagerRepository(
        @Default apiClient: IApiClient,
        apiService: ApiService,
        gsonProvider: GsonProvider,
    ): TransactionManageRepository {
        return TransactionManageRepositoryImpl(apiClient, apiService, gsonProvider)
    }

    // feature account
    @Provides
    @Singleton
    fun provideAccountRepository(
        @Default apiClient: IApiClient,
        apiService: ApiService,
    ): AccountRepository {
        return AccountRepositoryImpl(apiClient, apiService)
    }

    // feature report transfer
    @Provides
    @Singleton
    fun provideReportTransferRepository(
        @Default apiClient: IApiClient,
        apiService: ApiService,
    ): ReportTransferRepository {
        return ReportTransferRepositoryImpl(apiClient, apiService)
    }

    @Provides
    @Singleton
    fun provideCSatRepository(
        @Default apiClient: IApiClient,
        apiService: ApiService,
    ): CSatRepository {
        return CSatRepositoryImpl(apiClient, apiService)
    }

    /**
     * Smart CA
     * tuna5
     * */
    @Provides
    @Singleton
    fun provideSmartCARepository(
        @Default apiClient: IApiClient,
        apiService: ApiService,
    ): SmartCARepository {
        return SmartCARepositoryImpl(apiClient, apiService)
    }

    @Provides
    @Singleton
    fun provideUpdateEkycRepository(
        @Default apiClient: IApiClient,
        apiService: ApiService,
    ): UpdateEkycRepository {
        return UpdateEkycRepositoryImpl(apiClient, apiService)
    }

    // feature OTT
    @Provides
    @Singleton
    fun provideOttFeatureRepository(
        @Default apiClient: IApiClient,
        apiService: ApiService,
    ): OttFeatureRepository {
        return OttFeatureRepositoryImpl(apiClient, apiService)
    }

    @Provides
    @Singleton
    fun provideCheckMessageRepository(
        @Default apiClient: IApiClient,
        apiService: ApiService,
        passwordEncryptor: PasswordEncryptor,
    ): CheckPasswordRepository {
        return CheckPasswordRepositoryImpl(apiClient, apiService, passwordEncryptor)
    }

    // feature EKYC
    @Provides
    @Singleton
    fun provideEkycFeatureRepository(
        @Default apiClient: IApiClient,
        apiService: ApiService,
    ): EkycFeatureRepository {
        return EkycFeatureRepositoryImpl(apiClient, apiService)
    }
}
