package com.vietinbank.core_data.di

import android.content.Context
import com.vietinbank.core_data.database.ott.dao.DeviceTokenDao
import com.vietinbank.core_data.database.ott.dao.OttMessageDao
import com.vietinbank.core_data.network.api.IApiClient
import com.vietinbank.core_data.network.api.ott.OttApiService
import com.vietinbank.core_data.network.di.Default
import com.vietinbank.core_data.repository.ott.ConfigRepositoryImpl
import com.vietinbank.core_data.repository.ott.OttRepositoryImpl
import com.vietinbank.core_domain.repository.ConfigRepository
import com.vietinbank.core_domain.repository.OttRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Singleton

/**
 * Created by vandz on 15/4/25.
 */
@Module
@InstallIn(SingletonComponent::class)
object OttNetworkModule {

    @Provides
    @Singleton
    fun provideOttApiService(retrofit: Retrofit): OttApiService {
        return retrofit.create(OttApiService::class.java)
    }

    @Provides
    @Singleton
    fun provideOttRepository(
        apiService: OttApiService,
        deviceTokenDao: DeviceTokenDao,
        messageDao: OttMessageDao,
        @ApplicationContext context: Context,
        @Default apiClient: IApiClient,
    ): OttRepository {
        return OttRepositoryImpl(
            apiService,
            deviceTokenDao,
            messageDao,
            context,
            apiClient,
        )
    }

    @Provides
    @Singleton
    fun provideConfigRepository(
        @ApplicationContext context: Context,
    ): ConfigRepository {
        return ConfigRepositoryImpl(context)
    }
}
