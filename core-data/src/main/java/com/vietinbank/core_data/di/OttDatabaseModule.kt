package com.vietinbank.core_data.di

import android.content.Context
import com.vietinbank.core_data.database.ott.OttDatabase
import com.vietinbank.core_data.database.ott.dao.DeviceTokenDao
import com.vietinbank.core_data.database.ott.dao.OttMessageDao
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Created by vandz on 15/4/25.
 */
@Module
@InstallIn(SingletonComponent::class)
object OttDatabaseModule {

    @Provides
    @Singleton
    fun provideOttDatabase(@ApplicationContext context: Context): OttDatabase {
        return OttDatabase.getDatabase(context)
    }

    @Provides
    @Singleton
    fun provideOttMessageDao(database: OttDatabase): OttMessageDao {
        return database.ottMessageDao()
    }

    @Provides
    @Singleton
    fun provideDeviceTokenDao(database: OttDatabase): DeviceTokenDao {
        return database.deviceTokenDao()
    }
}
