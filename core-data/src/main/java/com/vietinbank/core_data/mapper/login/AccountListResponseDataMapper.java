package com.vietinbank.core_data.mapper.login;


import com.vietinbank.core_data.models.response.AccountListResponseData;
import com.vietinbank.core_data.models.response.CreaditCardList;
import com.vietinbank.core_data.models.response.FeePlans;
import com.vietinbank.core_domain.models.login.AccountListDomain;
import com.vietinbank.core_domain.models.login.CreaditCardListDomain;
import com.vietinbank.core_domain.models.login.FeePlansDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AccountListResponseDataMapper {
    AccountListResponseDataMapper INSTANCE = Mappers.getMapper(AccountListResponseDataMapper.class);

    AccountListDomain toDomain(AccountListResponseData dto);

    CreaditCardListDomain toDomain(CreaditCardList dto);

    FeePlansDomain toDomain(FeePlans dto);

    @InheritInverseConfiguration
    AccountListResponseData fromDomain(AccountListDomain domain);
}
