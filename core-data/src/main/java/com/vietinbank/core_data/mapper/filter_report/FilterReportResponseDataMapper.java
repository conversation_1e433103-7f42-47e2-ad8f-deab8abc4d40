package com.vietinbank.core_data.mapper.filter_report;

import com.vietinbank.core_data.mapper.login.AccountListResponseDataMapper;
import com.vietinbank.core_data.models.response.AccountListResponseData;
import com.vietinbank.core_data.models.response.AccountsModel;
import com.vietinbank.core_data.models.response.ContactsModel;
import com.vietinbank.core_data.models.response.CreaditCardList;
import com.vietinbank.core_data.models.response.FeePlans;
import com.vietinbank.core_data.models.response.FilterReportResponse;
import com.vietinbank.core_data.models.response.FrequencyTranferModel;
import com.vietinbank.core_data.models.response.GroupTypesModel;
import com.vietinbank.core_data.models.response.StatusModel;
import com.vietinbank.core_data.models.response.TypeModel;
import com.vietinbank.core_domain.models.login.AccountListDomain;
import com.vietinbank.core_domain.models.login.CreaditCardListDomain;
import com.vietinbank.core_domain.models.login.FeePlansDomain;
import com.vietinbank.core_domain.models.manage.FilterReportDomain;
import com.vietinbank.core_domain.models.manage.TAccountsDomain;
import com.vietinbank.core_domain.models.manage.TContactsDomain;
import com.vietinbank.core_domain.models.manage.TFrequencyTranferDomain;
import com.vietinbank.core_domain.models.manage.TGroupTypesDomain;
import com.vietinbank.core_domain.models.manage.TStatusDomain;
import com.vietinbank.core_domain.models.manage.TTypeDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface FilterReportResponseDataMapper {
    FilterReportResponseDataMapper INSTANCE = Mappers.getMapper(FilterReportResponseDataMapper.class);

    FilterReportDomain toDomain(FilterReportResponse dto);
    TContactsDomain toDomain(ContactsModel dto);
    TFrequencyTranferDomain toDomain(FrequencyTranferModel dto);
    TAccountsDomain toDomain(AccountsModel dto);
    TGroupTypesDomain toDomain(GroupTypesModel dto);
    TStatusDomain toDomain(StatusModel dto);
    TTypeDomain toDomain(TypeModel dto);

    @InheritInverseConfiguration
    FilterReportResponse fromDomain(FilterReportDomain domain);
}