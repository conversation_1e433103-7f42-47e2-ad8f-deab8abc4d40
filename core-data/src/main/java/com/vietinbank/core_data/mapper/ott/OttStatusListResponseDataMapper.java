package com.vietinbank.core_data.mapper.ott;

import com.vietinbank.core_data.models.response.ott_feature.OttStatusListResponse;
import com.vietinbank.core_domain.models.ott_feature.OttStatusListDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface OttStatusListResponseDataMapper {
    OttStatusListResponseDataMapper INSTANCE = Mappers.getMapper(OttStatusListResponseDataMapper.class);

    OttStatusListDomain toDomain(OttStatusListResponse dto);

    @InheritInverseConfiguration
    OttStatusListResponse fromDomain(OttStatusListDomain domain);
}
