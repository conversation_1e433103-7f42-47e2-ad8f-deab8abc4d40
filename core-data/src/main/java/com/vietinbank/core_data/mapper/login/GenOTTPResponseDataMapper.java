package com.vietinbank.core_data.mapper.login;

import com.vietinbank.core_data.models.response.GenOTPResponseData;
import com.vietinbank.core_domain.models.login.GenOTPDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface GenOTTPResponseDataMapper {
    GenOTTPResponseDataMapper INSTANCE = Mappers.getMapper(GenOTTPResponseDataMapper.class);

    GenOTPDomain toDomain(GenOTPResponseData dto);

    @InheritInverseConfiguration
    GenOTPResponseData fromDomain(GenOTPDomain domain);
}
