package com.vietinbank.core_data.mapper.ott_feature;


import com.vietinbank.core_data.models.response.ott_feature.ListRegResponse;
import com.vietinbank.core_domain.models.ott_feature.ListRegDomains;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ListRegResponseDataMapper {
    ListRegResponseDataMapper INSTANCE = Mappers.getMapper(ListRegResponseDataMapper.class);

    ListRegDomains toDomain(ListRegResponse dto);

    @InheritInverseConfiguration
    ListRegResponse fromDomain(ListRegDomains domain);

}
