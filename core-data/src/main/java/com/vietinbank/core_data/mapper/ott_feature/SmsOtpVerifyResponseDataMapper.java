package com.vietinbank.core_data.mapper.ott_feature;


import com.vietinbank.core_data.models.response.ott_feature.SmsOtpVerifyRequestResponse;
import com.vietinbank.core_domain.models.ott_feature.SmsOtpVerifyRequestDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface SmsOtpVerifyResponseDataMapper {
    SmsOtpVerifyResponseDataMapper INSTANCE = Mappers.getMapper(SmsOtpVerifyResponseDataMapper.class);

    SmsOtpVerifyRequestDomain toDomain(SmsOtpVerifyRequestResponse dto);

    @InheritInverseConfiguration
    SmsOtpVerifyRequestResponse fromDomain(SmsOtpVerifyRequestDomain domain);

}
