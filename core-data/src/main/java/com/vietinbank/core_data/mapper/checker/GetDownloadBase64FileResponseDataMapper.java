package com.vietinbank.core_data.mapper.checker;

import com.vietinbank.core_data.models.response.GetDownloadBase64FileResponseData;
import com.vietinbank.core_domain.models.checker.GetDownloadBase64FileDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface GetDownloadBase64FileResponseDataMapper {
    GetDownloadBase64FileResponseDataMapper INSTANCE = Mappers.getMapper(GetDownloadBase64FileResponseDataMapper.class);

    GetDownloadBase64FileDomain toDomain(GetDownloadBase64FileResponseData dto);

    @InheritInverseConfiguration
    GetDownloadBase64FileResponseData fromDomain(GetDownloadBase64FileDomain domain);
}