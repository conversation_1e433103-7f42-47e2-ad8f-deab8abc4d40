package com.vietinbank.core_data.mapper.checker;

import com.vietinbank.core_data.models.response.PreApproveResponseData;
import com.vietinbank.core_domain.models.checker.PreApproveDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface PreApproveResponseDataMapper {
    PreApproveResponseDataMapper INSTANCE = Mappers.getMapper(PreApproveResponseDataMapper.class);

    PreApproveDomain toDomain(PreApproveResponseData dto);

    @InheritInverseConfiguration
    PreApproveResponseData fromDomain(PreApproveDomain domain);
}
