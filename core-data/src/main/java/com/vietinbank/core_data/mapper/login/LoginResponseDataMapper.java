package com.vietinbank.core_data.mapper.login;

import com.vietinbank.core_domain.models.login.LoginDomain;
import com.vietinbank.core_data.models.response.LoginResponseData;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface LoginResponseDataMapper {
    LoginResponseDataMapper INSTANCE = Mappers.getMapper(LoginResponseDataMapper.class);

    LoginDomain toDomain(LoginResponseData dto);

    @InheritInverseConfiguration
    LoginResponseData fromDomain(LoginDomain domain);
}
