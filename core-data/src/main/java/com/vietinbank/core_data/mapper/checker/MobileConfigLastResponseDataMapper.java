package com.vietinbank.core_data.mapper.checker;

import com.vietinbank.core_data.models.response.MobileConfigLastResponseData;
import com.vietinbank.core_domain.models.checker.MobileConfigLastDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface MobileConfigLastResponseDataMapper {
    MobileConfigLastResponseDataMapper INSTANCE = Mappers.getMapper(MobileConfigLastResponseDataMapper.class);

    MobileConfigLastDomain toDomain(MobileConfigLastResponseData dto);

    @InheritInverseConfiguration
    MobileConfigLastResponseData fromDomain(MobileConfigLastDomain domain);
}
