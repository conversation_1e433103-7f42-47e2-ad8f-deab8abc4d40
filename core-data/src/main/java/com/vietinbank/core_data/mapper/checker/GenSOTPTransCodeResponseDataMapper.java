package com.vietinbank.core_data.mapper.checker;

import com.vietinbank.core_data.models.response.GenSOTPTransCodeResponseData;
import com.vietinbank.core_domain.models.checker.GenSOTPTransCodeDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface GenSOTPTransCodeResponseDataMapper {
    GenSOTPTransCodeResponseDataMapper INSTANCE = Mappers.getMapper(GenSOTPTransCodeResponseDataMapper.class);

    GenSOTPTransCodeDomain toDomain(GenSOTPTransCodeResponseData dto);

    @InheritInverseConfiguration
    GenSOTPTransCodeResponseData fromDomain(GenSOTPTransCodeDomain domain);
}
