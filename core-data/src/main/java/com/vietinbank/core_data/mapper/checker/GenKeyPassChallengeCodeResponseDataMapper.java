package com.vietinbank.core_data.mapper.checker;

import com.vietinbank.core_data.models.response.GenKeyPassChallengeCodeResponseData;
import com.vietinbank.core_domain.models.checker.GenKeyPassChallengeCodeDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface GenKeyPassChallengeCodeResponseDataMapper {
    GenKeyPassChallengeCodeResponseDataMapper INSTANCE = Mappers.getMapper(GenKeyPassChallengeCodeResponseDataMapper.class);

    GenKeyPassChallengeCodeDomain toDomain(GenKeyPassChallengeCodeResponseData dto);

    @InheritInverseConfiguration
    GenKeyPassChallengeCodeResponseData fromDomain(GenKeyPassChallengeCodeDomain domain);
}
