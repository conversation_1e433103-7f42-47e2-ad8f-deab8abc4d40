package com.vietinbank.core_data.mapper.account;

import com.vietinbank.core_data.models.base.Status;
import com.vietinbank.core_data.models.response.AccountDefaultNew;
import com.vietinbank.core_data.models.response.AccountDetailResponse;
import com.vietinbank.core_data.models.response.AccountFileSavingResponse;
import com.vietinbank.core_data.models.response.AccountHistoryDetailResponse;
import com.vietinbank.core_data.models.response.AccountHistoryListResponse;
import com.vietinbank.core_data.models.response.AccountImageQRResponse;
import com.vietinbank.core_data.models.response.AccountListNewResponse;
import com.vietinbank.core_data.models.response.AccountSaveQRResponse;
import com.vietinbank.core_data.models.response.AliasAccountModel;
import com.vietinbank.core_data.models.response.AliasAccountResponse;
import com.vietinbank.core_data.models.response.AliasUpdateModel;
import com.vietinbank.core_data.models.response.HistoryDetailModel;
import com.vietinbank.core_data.models.response.HistoryModel;
import com.vietinbank.core_data.models.response.ImageModel;
import com.vietinbank.core_data.models.response.StatusDescriptionModel;
import com.vietinbank.core_data.models.response.SubsidiaryInfoModel;
import com.vietinbank.core_data.models.response.TransactionStatusModel;
import com.vietinbank.core_data.models.response.TransactionStatusResponse;
import com.vietinbank.core_domain.models.account.AccountDetailDomain;
import com.vietinbank.core_domain.models.account.AccountFileSavingDomain;
import com.vietinbank.core_domain.models.account.AccountHistoryDetailDomain;
import com.vietinbank.core_domain.models.account.AccountHistoryListDomain;
import com.vietinbank.core_domain.models.account.AccountImageQRDomain;
import com.vietinbank.core_domain.models.account.AccountListNewDomain;
import com.vietinbank.core_domain.models.account.AccountSaveQRDomain;
import com.vietinbank.core_domain.models.account.AliasAccountDomain;
import com.vietinbank.core_domain.models.account.AliasUpdateDomain;
import com.vietinbank.core_domain.models.account.HistoryDetailDomain;
import com.vietinbank.core_domain.models.account.HistoryDomain;
import com.vietinbank.core_domain.models.account.ImageDomain;
import com.vietinbank.core_domain.models.account.SetAliasAccountDomain;
import com.vietinbank.core_domain.models.account.StatusDescriptionDomain;
import com.vietinbank.core_domain.models.account.StatusDomain;
import com.vietinbank.core_domain.models.account.SubsidiaryInfoDomain;
import com.vietinbank.core_domain.models.account.TransactionStatusDomain;
import com.vietinbank.core_domain.models.login.AccountDefaultDomain;
import com.vietinbank.core_domain.models.login.StatusParams;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AccountResponseMapper {
    AccountResponseMapper INSTANCE = Mappers.getMapper(AccountResponseMapper.class);

    AccountDetailDomain toDomain(AccountDetailResponse dto);

    AliasAccountDomain toDomain(AliasAccountModel dto);

    StatusParams toDomain(Status dto);

    // danh sach tai khoan
    AccountListNewDomain toDomain(AccountListNewResponse dto);

    AccountDefaultDomain toDomain(AccountDefaultNew dto);

    SubsidiaryInfoDomain toDomain(SubsidiaryInfoModel dto);

    // detail history transaction
    AccountHistoryDetailDomain toDomain(AccountHistoryDetailResponse dto);

    HistoryDetailDomain toDomain(HistoryDetailModel dto);

    // lich su giao dich
    AccountHistoryListDomain toDomain(AccountHistoryListResponse dto);

    HistoryDomain toDomain(HistoryModel dto);

    // alias
    SetAliasAccountDomain toDomain(AliasAccountResponse dto);

    AliasUpdateDomain toDomain(AliasUpdateModel dto);

    // default

    // qr
    AccountSaveQRDomain toDomain(AccountSaveQRResponse dto);

    // list images qr
    AccountImageQRDomain toDomain(AccountImageQRResponse dto);

    ImageDomain toDomain(ImageModel dto);

    // trang thai giao dich
    TransactionStatusDomain toDomain(TransactionStatusResponse dto);

    StatusDomain toDomain(TransactionStatusModel dto);

    StatusDescriptionDomain toDomain(StatusDescriptionModel dto);

    // download tien gui
    AccountFileSavingDomain toDomain(AccountFileSavingResponse dto);



    @InheritInverseConfiguration
    AccountListNewResponse fromDomain(AccountListNewDomain domain);

    @InheritInverseConfiguration
    AccountDetailResponse fromDomain(AccountDetailDomain domain);

    @InheritInverseConfiguration
    AccountHistoryDetailResponse fromDomain(AccountHistoryDetailDomain domain);

    @InheritInverseConfiguration
    AccountHistoryListResponse fromDomain(AccountHistoryListDomain domain);

    @InheritInverseConfiguration
    AliasAccountResponse fromDomain(SetAliasAccountDomain domain);

    @InheritInverseConfiguration
    AccountSaveQRResponse fromDomain(AccountSaveQRDomain domain);

    @InheritInverseConfiguration
    AccountImageQRResponse fromDomain(AccountImageQRDomain domain);

    @InheritInverseConfiguration
    TransactionStatusResponse fromDomain(TransactionStatusDomain domain);
}