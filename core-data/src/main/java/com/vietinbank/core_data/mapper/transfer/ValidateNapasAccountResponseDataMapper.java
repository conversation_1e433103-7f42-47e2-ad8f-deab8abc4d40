package com.vietinbank.core_data.mapper.transfer;



import com.vietinbank.core_data.models.response.ValidateNapasAccountResponse;
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ValidateNapasAccountResponseDataMapper {
    ValidateNapasAccountResponseDataMapper INSTANCE = Mappers.getMapper(ValidateNapasAccountResponseDataMapper.class);

    ValidateNapasAccountDomain toDomain(ValidateNapasAccountResponse dto);

    @InheritInverseConfiguration
    ValidateNapasAccountResponse fromDomain(ValidateNapasAccountDomain domain);

}
