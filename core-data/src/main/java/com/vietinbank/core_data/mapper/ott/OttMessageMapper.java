package com.vietinbank.core_data.mapper.ott;

import com.vietinbank.core_data.models.response.ott.OttRegisterResponse;
import com.vietinbank.core_domain.models.ott.OttRegisterTokenDomain;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * Created by vandz on 17/4/25.
 * MapStruct mapper for OTT related entities.
 */
@Mapper
public interface OttMessageMapper {
    OttMessageMapper INSTANCE = Mappers.getMapper(OttMessageMapper.class);

    /**
     * Converts OttRegisterResponse DTO to OttRegisterTokenDomain
     *
     * @param dto the data transfer object from API
     * @return domain object for use in business logic
     */
//    @Mapping(source = "data.registered", target = "registered")
//    @Mapping(source = "data.deviceId", target = "deviceId")
    OttRegisterTokenDomain toDomain(OttRegisterResponse dto);
}