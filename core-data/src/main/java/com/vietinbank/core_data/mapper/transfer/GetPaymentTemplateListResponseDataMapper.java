package com.vietinbank.core_data.mapper.transfer;


import com.vietinbank.core_data.models.response.CreateTransferResponse;
import com.vietinbank.core_data.models.response.GetPaymentTemplateListResponse;
import com.vietinbank.core_domain.models.maker.CreateTransferDomain;
import com.vietinbank.core_domain.models.maker.GetPaymentTemplateListDomains;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface GetPaymentTemplateListResponseDataMapper {
    GetPaymentTemplateListResponseDataMapper INSTANCE = Mappers.getMapper(GetPaymentTemplateListResponseDataMapper.class);

    GetPaymentTemplateListDomains toDomain(GetPaymentTemplateListResponse dto);

    @InheritInverseConfiguration
    GetPaymentTemplateListResponse fromDomain(GetPaymentTemplateListDomains domain);

}
