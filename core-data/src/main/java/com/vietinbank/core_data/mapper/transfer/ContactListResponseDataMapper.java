package com.vietinbank.core_data.mapper.transfer;


import com.vietinbank.core_data.models.response.ContactListResponse;
import com.vietinbank.core_data.models.response.ContactResponse;
import com.vietinbank.core_data.models.response.DataBanks;
import com.vietinbank.core_domain.models.maker.ContactDomains;
import com.vietinbank.core_domain.models.maker.ContactListDomains;
import com.vietinbank.core_domain.models.maker.DataBankDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ContactListResponseDataMapper {
    ContactListResponseDataMapper INSTANCE = Mappers.getMapper(ContactListResponseDataMapper.class);

    ContactListDomains toDomain(ContactListResponse dto);

    @InheritInverseConfiguration
    ContactListResponse fromDomain(ContactListDomains domain);

}
