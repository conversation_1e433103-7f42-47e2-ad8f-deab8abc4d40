package com.vietinbank.core_data.mapper.transfer;


import com.vietinbank.core_data.models.response.BranchListResponse;
import com.vietinbank.core_data.models.response.ContactCreateResponse;
import com.vietinbank.core_domain.models.maker.BranchListDomains;
import com.vietinbank.core_domain.models.maker.ContactCreateDomains;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface BranchListResponseDataMapper {
    BranchListResponseDataMapper INSTANCE = Mappers.getMapper(BranchListResponseDataMapper.class);

    BranchListDomains toDomain(BranchListResponse dto);

    @InheritInverseConfiguration
    BranchListResponse fromDomain(BranchListDomains domain);

}
