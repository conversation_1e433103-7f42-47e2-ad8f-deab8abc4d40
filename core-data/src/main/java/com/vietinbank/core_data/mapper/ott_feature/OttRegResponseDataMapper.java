package com.vietinbank.core_data.mapper.ott_feature;


import com.vietinbank.core_data.models.response.ott_feature.OttRegResponse;
import com.vietinbank.core_domain.models.ott_feature.OttRegisterDomains;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface OttRegResponseDataMapper {
    OttRegResponseDataMapper INSTANCE = Mappers.getMapper(OttRegResponseDataMapper.class);

    OttRegisterDomains toDomain(OttRegResponse dto);

    @InheritInverseConfiguration
    OttRegResponse fromDomain(OttRegisterDomains domain);

}
