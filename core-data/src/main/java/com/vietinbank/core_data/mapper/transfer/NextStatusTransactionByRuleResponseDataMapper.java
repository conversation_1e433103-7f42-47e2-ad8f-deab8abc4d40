package com.vietinbank.core_data.mapper.transfer;


import com.vietinbank.core_data.models.response.NextStatusTransactionByRuleResponse;
import com.vietinbank.core_domain.models.maker.NextStatusTransactionByRuleDomains;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface NextStatusTransactionByRuleResponseDataMapper {
    NextStatusTransactionByRuleResponseDataMapper INSTANCE = Mappers.getMapper(NextStatusTransactionByRuleResponseDataMapper.class);

    NextStatusTransactionByRuleDomains toDomain(NextStatusTransactionByRuleResponse dto);

    @InheritInverseConfiguration
    NextStatusTransactionByRuleResponse fromDomain(NextStatusTransactionByRuleDomains domain);

}
