package com.vietinbank.core_data.mapper.checker;

import com.vietinbank.core_data.models.response.GetTransactionListResponseData;
import com.vietinbank.core_domain.models.checker.GetTransactionListDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface GetTransactionListResponseDataMapper {
    GetTransactionListResponseDataMapper INSTANCE = Mappers.getMapper(GetTransactionListResponseDataMapper.class);

    @Mapping(target = "countTransaction", source = "countTransaction")
    @Mapping(target = "subTranTypeList", source = "subTranTypeList")
    @Mapping(target = "transactions", source = "transactions")
    GetTransactionListDomain toDomain(GetTransactionListResponseData dto);

    @InheritInverseConfiguration
    GetTransactionListResponseData fromDomain(GetTransactionListDomain domain);
}
