package com.vietinbank.core_data.mapper.filter_report;

import com.vietinbank.core_data.models.response.ListReportResponse;
import com.vietinbank.core_data.models.response.TransRsModel;
import com.vietinbank.core_data.models.response.TuitionBillModel;
import com.vietinbank.core_domain.models.manage.ListReportDomain;
import com.vietinbank.core_domain.models.manage.TransRsDomain;
import com.vietinbank.core_domain.models.manage.TuitionBillDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ListReportReponseDataMapper {
    ListReportReponseDataMapper INSTANCE = Mappers.getMapper(ListReportReponseDataMapper.class);

    ListReportDomain toDomain(ListReportResponse dto);

    TransRsDomain toDomain(TransRsModel dto);
    TuitionBillDomain toDomain(TuitionBillModel dto);

    @InheritInverseConfiguration
    ListReportResponse fromDomain(ListReportDomain domain);
}