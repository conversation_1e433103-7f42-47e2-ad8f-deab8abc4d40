package com.vietinbank.core_data.mapper.ekyc_feature;


import com.vietinbank.core_data.models.response.ekyc_feature.EkycConfirmInputResponse;
import com.vietinbank.core_domain.models.ekyc_feature.EkycConfirmInputDomains;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface EkycConfirmInputResponseDataMapper {
    EkycConfirmInputResponseDataMapper INSTANCE = Mappers.getMapper(EkycConfirmInputResponseDataMapper.class);

    EkycConfirmInputDomains toDomain(EkycConfirmInputResponse dto);

    @InheritInverseConfiguration
    EkycConfirmInputResponse fromDomain(EkycConfirmInputDomains domain);

}
