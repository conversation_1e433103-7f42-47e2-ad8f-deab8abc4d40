package com.vietinbank.core_data.mapper.transfer;


import com.vietinbank.core_data.models.response.ValidateNapasAccountTransferResponse;
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountTransferDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ValidateNapasAccountTransferResponseDataMapper {
    ValidateNapasAccountTransferResponseDataMapper INSTANCE = Mappers.getMapper(ValidateNapasAccountTransferResponseDataMapper.class);

    ValidateNapasAccountTransferDomain toDomain(ValidateNapasAccountTransferResponse dto);

    @InheritInverseConfiguration
    ValidateNapasAccountTransferResponse fromDomain(ValidateNapasAccountTransferDomain domain);

}
