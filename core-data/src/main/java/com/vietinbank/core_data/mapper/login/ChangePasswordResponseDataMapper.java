package com.vietinbank.core_data.mapper.login;

import com.vietinbank.core_data.models.response.ChangePasswordResponseData;
import com.vietinbank.core_domain.models.login.ChangePasswordDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ChangePasswordResponseDataMapper {
    ChangePasswordResponseDataMapper INSTANCE = Mappers.getMapper(ChangePasswordResponseDataMapper.class);

    ChangePasswordDomain toDomain(ChangePasswordResponseData dto);

    @InheritInverseConfiguration
    ChangePasswordResponseData fromDomain(ChangePasswordDomain domain);
}
