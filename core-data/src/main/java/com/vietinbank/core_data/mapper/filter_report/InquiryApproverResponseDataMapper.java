package com.vietinbank.core_data.mapper.filter_report;

import com.vietinbank.core_data.models.response.InquiryApproverResponse;
import com.vietinbank.core_data.models.response.UsersListModel;
import com.vietinbank.core_domain.models.manage.InquiryApproverDomains;
import com.vietinbank.core_domain.models.manage.UsersListDomains;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface InquiryApproverResponseDataMapper {
    InquiryApproverResponseDataMapper INSTANCE = Mappers.getMapper(InquiryApproverResponseDataMapper.class);

    InquiryApproverDomains toDomain(InquiryApproverResponse dto);

    UsersListDomains toDomain(UsersListModel dto);

    @InheritInverseConfiguration
    InquiryApproverResponse fromDomain(InquiryApproverDomains domain);
}