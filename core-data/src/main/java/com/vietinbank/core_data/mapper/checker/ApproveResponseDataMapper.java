package com.vietinbank.core_data.mapper.checker;

import com.vietinbank.core_data.models.response.ApproveResponseData;
import com.vietinbank.core_domain.models.checker.ApproveDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ApproveResponseDataMapper {
    ApproveResponseDataMapper INSTANCE = Mappers.getMapper(ApproveResponseDataMapper.class);

    ApproveDomain toDomain(ApproveResponseData dto);

    @InheritInverseConfiguration
    ApproveResponseData fromDomain(ApproveDomain domain);
}
