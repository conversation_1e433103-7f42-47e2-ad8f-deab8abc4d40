package com.vietinbank.core_data.mapper.report_transfer;


import com.vietinbank.core_data.models.response.TransactionListResponse;
import com.vietinbank.core_domain.models.transfer_report.TransactionListDomains;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface TransactionListResponseDataMapper {
    TransactionListResponseDataMapper INSTANCE = Mappers.getMapper(TransactionListResponseDataMapper.class);

    TransactionListDomains toDomain(TransactionListResponse dto);

    @InheritInverseConfiguration
    TransactionListResponse fromDomain(TransactionListDomains domain);

}
