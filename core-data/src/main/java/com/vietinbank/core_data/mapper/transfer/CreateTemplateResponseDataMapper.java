package com.vietinbank.core_data.mapper.transfer;


import com.vietinbank.core_data.models.response.ContactCreateResponse;
import com.vietinbank.core_data.models.response.CreateTemplateResponse;
import com.vietinbank.core_domain.models.maker.ContactCreateDomains;
import com.vietinbank.core_domain.models.maker.CreateTemplateDomains;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CreateTemplateResponseDataMapper {
    CreateTemplateResponseDataMapper INSTANCE = Mappers.getMapper(CreateTemplateResponseDataMapper.class);

    CreateTemplateDomains toDomain(CreateTemplateResponse dto);

    @InheritInverseConfiguration
    CreateTemplateResponse fromDomain(CreateTemplateDomains domain);

}
