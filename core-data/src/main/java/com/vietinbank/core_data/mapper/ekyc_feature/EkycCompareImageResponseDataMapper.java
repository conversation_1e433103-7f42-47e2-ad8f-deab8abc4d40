package com.vietinbank.core_data.mapper.ekyc_feature;


import com.vietinbank.core_data.models.response.ekyc_feature.EkycCompareImageResponse;
import com.vietinbank.core_domain.models.ekyc_feature.EkycCompareImageDomains;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface EkycCompareImageResponseDataMapper {
    EkycCompareImageResponseDataMapper INSTANCE = Mappers.getMapper(EkycCompareImageResponseDataMapper.class);

    EkycCompareImageDomains toDomain(EkycCompareImageResponse dto);

    @InheritInverseConfiguration
    EkycCompareImageResponse fromDomain(EkycCompareImageDomains domain);

}
