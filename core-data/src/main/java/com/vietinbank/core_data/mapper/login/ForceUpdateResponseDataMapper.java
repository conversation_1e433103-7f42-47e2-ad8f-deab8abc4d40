package com.vietinbank.core_data.mapper.login;

import com.vietinbank.core_common.models.ForceUpdateDomain;
import com.vietinbank.core_data.models.response.ForceUpdateResponseData;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ForceUpdateResponseDataMapper {
    ForceUpdateResponseDataMapper INSTANCE = Mappers.getMapper(ForceUpdateResponseDataMapper.class);

    ForceUpdateDomain toDomain(ForceUpdateResponseData dto);

    @InheritInverseConfiguration
    ForceUpdateResponseData fromDomain(ForceUpdateDomain domain);
}
