package com.vietinbank.core_data.mapper.filter_report;

import com.vietinbank.core_data.models.response.InquiryApproverResponse;
import com.vietinbank.core_data.models.response.TraceInquiryResponse;
import com.vietinbank.core_data.models.response.UsersListModel;
import com.vietinbank.core_domain.models.manage.InquiryApproverDomains;
import com.vietinbank.core_domain.models.manage.UsersListDomains;
import com.vietinbank.core_domain.models.trace_payment.TraceInquiryDomains;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface TraceInquiryResponseDataMapper {
    TraceInquiryResponseDataMapper INSTANCE = Mappers.getMapper(TraceInquiryResponseDataMapper.class);

    TraceInquiryDomains toDomain(TraceInquiryResponse dto);


    @InheritInverseConfiguration
    TraceInquiryResponse fromDomain(TraceInquiryDomains domain);
}