package com.vietinbank.core_data.mapper.ott;

import com.vietinbank.core_data.models.response.ott_feature.OttStatusUpdateResponse;
import com.vietinbank.core_domain.models.ott_feature.OttStatusUpdateDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface OttStatusUpdateResponseDataMapper {
    OttStatusUpdateResponseDataMapper INSTANCE = Mappers.getMapper(OttStatusUpdateResponseDataMapper.class);

    OttStatusUpdateDomain toDomain(OttStatusUpdateResponse dto);

    @InheritInverseConfiguration
    OttStatusUpdateResponse fromDomain(OttStatusUpdateDomain domain);
}
