package com.vietinbank.core_data.mapper.filter_report;

import com.vietinbank.core_data.models.response.ReportDetailsResponse;
import com.vietinbank.core_data.models.response.TransactionResponse;
import com.vietinbank.core_domain.models.trace_payment.ReportDetailsDomains;
import com.vietinbank.core_domain.models.trace_payment.TransactionDomains;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ReportDetailResponseDataMapper {
    ReportDetailResponseDataMapper INSTANCE = Mappers.getMapper(ReportDetailResponseDataMapper.class);

    @Mapping(source = "transactionResponse", target = "transaction")
    ReportDetailsDomains toDomain(ReportDetailsResponse dto);

    TransactionDomains toDomain(TransactionResponse dto);

    @InheritInverseConfiguration
    ReportDetailsResponse fromDomain(ReportDetailsDomains domain);
}