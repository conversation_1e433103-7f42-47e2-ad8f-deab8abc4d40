package com.vietinbank.core_data.mapper.login;

import com.vietinbank.core_domain.models.login.CorpUserDomain;
import com.vietinbank.core_data.models.response.CorpUser;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * Created by vandz on 10/1/25.
 */
@Mapper
public interface CorpUserMapper {
    CorpUserMapper INSTANCE = Mappers.getMapper(CorpUserMapper.class);

    CorpUserDomain toDomain(CorpUser dto);

    @InheritInverseConfiguration
    CorpUser fromDomain(CorpUserDomain domain);
}

