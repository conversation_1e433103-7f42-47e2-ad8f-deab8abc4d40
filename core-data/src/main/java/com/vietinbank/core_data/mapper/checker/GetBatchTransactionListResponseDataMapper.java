package com.vietinbank.core_data.mapper.checker;

import com.vietinbank.core_data.models.response.GetBatchTransactionListResponseData;
import com.vietinbank.core_domain.models.checker.GetBatchTransactionListDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface GetBatchTransactionListResponseDataMapper {
    GetBatchTransactionListResponseDataMapper INSTANCE = Mappers.getMapper(GetBatchTransactionListResponseDataMapper.class);

    GetBatchTransactionListDomain toDomain(GetBatchTransactionListResponseData dto);

    @InheritInverseConfiguration
    GetBatchTransactionListResponseData fromDomain(GetBatchTransactionListDomain domain);
}
