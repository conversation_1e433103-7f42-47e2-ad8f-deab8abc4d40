package com.vietinbank.core_data.mapper.smart_ca;

import com.vietinbank.core_data.models.response.SmartCABranchResponse;
import com.vietinbank.core_data.models.response.SmartCACertVNPTResponse;
import com.vietinbank.core_data.models.response.SmartCADetailResponse;
import com.vietinbank.core_data.models.response.SmartCAEsignModel;
import com.vietinbank.core_data.models.response.SmartCAFileModel;
import com.vietinbank.core_data.models.response.SmartCAGetParamResponse;
import com.vietinbank.core_data.models.response.SmartCAListResponse;
import com.vietinbank.core_data.models.response.SmartCARegisterResponse;
import com.vietinbank.core_data.models.response.SmartCAUpdateResponse;
import com.vietinbank.core_domain.models.smartCA.SmartCABranchDomain;
import com.vietinbank.core_domain.models.smartCA.SmartCACertVNPTPDomain;
import com.vietinbank.core_domain.models.smartCA.SmartCADetailDomain;
import com.vietinbank.core_domain.models.smartCA.SmartCAEsignDomain;
import com.vietinbank.core_domain.models.smartCA.SmartCAFileDomain;
import com.vietinbank.core_domain.models.smartCA.SmartCAGetParamDomain;
import com.vietinbank.core_domain.models.smartCA.SmartCAListDomain;
import com.vietinbank.core_domain.models.smartCA.SmartCARegisterDomain;
import com.vietinbank.core_domain.models.smartCA.SmartCAUpdateDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


@Mapper
public interface SmartCAMapper {
    SmartCAMapper INSTANCE = Mappers.getMapper(SmartCAMapper.class);

    // list
    SmartCAListDomain toDomain(SmartCAListResponse dto);

    SmartCAEsignDomain toDomain(SmartCAEsignModel dto);

    @InheritInverseConfiguration
    SmartCAListResponse fromDomain(SmartCAListDomain domain);

    // detail
    SmartCADetailDomain toDomain(SmartCADetailResponse dto);
//    SmartCAFileDomain toDomain(SmartCAFileModel dto);

    @InheritInverseConfiguration
    SmartCADetailResponse fromDomain(SmartCADetailDomain domain);

    // register
    SmartCARegisterDomain toDomain(SmartCARegisterResponse dto);

    @InheritInverseConfiguration
    SmartCARegisterResponse fromDomain(SmartCARegisterDomain domain);

    // branch
    SmartCABranchDomain toDomain(SmartCABranchResponse dto);

    @InheritInverseConfiguration
    SmartCABranchResponse fromDomain(SmartCABranchDomain domain);

    // update
    SmartCAUpdateDomain toDomain(SmartCAUpdateResponse domain);

    // params
    SmartCAGetParamDomain toDomain(SmartCAGetParamResponse dto);

    @InheritInverseConfiguration
    SmartCAGetParamResponse fromDomain(SmartCAGetParamDomain domain);

    SmartCACertVNPTPDomain toDomain(SmartCACertVNPTResponse dto);


}