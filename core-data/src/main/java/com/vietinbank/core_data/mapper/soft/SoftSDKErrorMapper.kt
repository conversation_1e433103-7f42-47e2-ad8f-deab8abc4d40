package com.vietinbank.core_data.mapper.soft

import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.soft.SoftSDKErrorCode

internal fun handleSDKResultCode(
    resultBytes: ByteArray,
    sdkFuncName: String? = null,
): Resource<Unit> {
    val code = resultBytes.toString(Charsets.UTF_8).toIntOrNull()
        ?: return Resource.Error(
            message = "Mã lỗi không hợp lệ",
            code = "999",
            exception = AppException.IllegalArgumentException(
                message = "Không parse được code từ byteArray",
                code = "999",
                requestId = sdkFuncName,
            ),
        )

    val errorCode = SoftSDKErrorCode.Companion.fromCode(code)

    return if (errorCode == SoftSDKErrorCode.SUCCESS ||
        errorCode == SoftSDKErrorCode.OTHER_SUCCESS
    ) {
        Resource.Success(Unit)
    } else {
        Resource.Error(
            code = errorCode.code.toString(),
            message = errorCode.toString(),
        )
    }
}

internal fun handleSDKResultCode(
    resultCode: Int,
): Resource<Unit> {
    val errorCode = SoftSDKErrorCode.fromCode(resultCode)

    return if (errorCode == SoftSDKErrorCode.SUCCESS ||
        errorCode == SoftSDKErrorCode.OTHER_SUCCESS
    ) {
        Resource.Success(Unit)
    } else {
        Resource.Error(
            code = errorCode.code.toString(),
            message = errorCode.toString(),
        )
    }
}
