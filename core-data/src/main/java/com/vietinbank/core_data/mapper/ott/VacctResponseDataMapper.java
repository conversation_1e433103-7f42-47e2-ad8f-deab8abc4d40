package com.vietinbank.core_data.mapper.ott;

import com.vietinbank.core_data.models.response.ott_feature.CancelVacctOttResponse;
import com.vietinbank.core_domain.models.ott_feature.CancelVacctOttDomain;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface VacctResponseDataMapper {
    VacctResponseDataMapper INSTANCE = Mappers.getMapper(VacctResponseDataMapper.class);
    CancelVacctOttDomain toDomain(CancelVacctOttResponse dto);
}
