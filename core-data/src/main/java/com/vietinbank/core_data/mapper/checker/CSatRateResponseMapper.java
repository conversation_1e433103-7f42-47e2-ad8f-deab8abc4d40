package com.vietinbank.core_data.mapper.checker;

import com.vietinbank.core_data.models.response.CSatConfigResponse;
import com.vietinbank.core_data.models.response.CSatRateResponse;
import com.vietinbank.core_domain.models.csat.CSatConfigDomain;
import com.vietinbank.core_domain.models.csat.CSatRateDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CSatRateResponseMapper {
    CSatRateResponseMapper INSTANCE = Mappers.getMapper(CSatRateResponseMapper.class);

    CSatRateDomain toDomain(CSatRateResponse dto);

    CSatConfigDomain toDomain(CSatConfigResponse dto);

    @InheritInverseConfiguration
    CSatRateResponse fromDomain(CSatRateDomain domain);

    @InheritInverseConfiguration
    CSatConfigResponse fromDomain(CSatConfigDomain domain);
}
