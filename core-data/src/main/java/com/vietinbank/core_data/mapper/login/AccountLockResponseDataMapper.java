package com.vietinbank.core_data.mapper.login;

import com.vietinbank.core_data.models.response.account_lock.AccountLockOTPResponse;
import com.vietinbank.core_data.models.response.account_lock.AccountLockResponse;
import com.vietinbank.core_domain.models.login.AccountLockDomain;
import com.vietinbank.core_domain.models.login.AccountLockOTPDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AccountLockResponseDataMapper {
    AccountLockResponseDataMapper INSTANCE = Mappers.getMapper(AccountLockResponseDataMapper.class);

    AccountLockDomain toDomain(AccountLockResponse model);

    AccountLockOTPDomain toDomain(AccountLockOTPResponse model);

    @InheritInverseConfiguration
    AccountLockResponse fromDomain(AccountLockDomain domain);

    @InheritInverseConfiguration
    AccountLockOTPResponse fromDomain(AccountLockOTPDomain domain);
}
