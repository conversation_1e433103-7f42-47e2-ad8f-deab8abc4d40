package com.vietinbank.core_data.mapper.transfer;


import com.vietinbank.core_data.models.response.DataBanks;
import com.vietinbank.core_data.models.response.NapasBankListResponse;
import com.vietinbank.core_domain.models.maker.DataBankDomain;
import com.vietinbank.core_domain.models.maker.NapasBankListDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface NapasBankListResponseDataMapper {
    NapasBankListResponseDataMapper INSTANCE = Mappers.getMapper(NapasBankListResponseDataMapper.class);

    NapasBankListDomain toDomain(NapasBankListResponse dto);

    @InheritInverseConfiguration
    NapasBankListResponse fromDomain(NapasBankListDomain domain);

    DataBankDomain toDomain(DataBanks dataBanks);

    DataBanks fromDomain(DataBankDomain dataBankDomain);
}
