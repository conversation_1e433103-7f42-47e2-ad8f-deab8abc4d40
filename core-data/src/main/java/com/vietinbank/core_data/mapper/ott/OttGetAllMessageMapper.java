package com.vietinbank.core_data.mapper.ott;

import com.vietinbank.core_data.models.response.ott.OttGetAllMessageResponse;
import com.vietinbank.core_domain.models.ott.OttGetAllMessageDomain;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * Created by vand<PERSON> on 17/4/25.
 * MapStruct mapper for OTT related entities.
 */
@Mapper
public interface OttGetAllMessageMapper {
    OttGetAllMessageMapper INSTANCE = Mappers.getMapper(OttGetAllMessageMapper.class);
    OttGetAllMessageDomain toDomain(OttGetAllMessageResponse dto);
}