package com.vietinbank.core_data.mapper.checker;

import com.vietinbank.core_data.models.response.GetDownloadFileIDResponseData;
import com.vietinbank.core_domain.models.checker.GetDownloadFileIDDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface GetDownloadFileIDResponseDataMapper {
    GetDownloadFileIDResponseDataMapper INSTANCE = Mappers.getMapper(GetDownloadFileIDResponseDataMapper.class);

    GetDownloadFileIDDomain toDomain(GetDownloadFileIDResponseData dto);

    @InheritInverseConfiguration
    GetDownloadFileIDResponseData fromDomain(GetDownloadFileIDDomain domain);
}
