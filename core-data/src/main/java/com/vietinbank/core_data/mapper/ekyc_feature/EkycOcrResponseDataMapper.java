package com.vietinbank.core_data.mapper.ekyc_feature;


import com.vietinbank.core_data.models.response.ekyc_feature.EkycOcrResponse;
import com.vietinbank.core_data.models.response.ott_feature.CheckRegResponse;
import com.vietinbank.core_domain.models.ekyc_feature.EkycOcrDomains;
import com.vietinbank.core_domain.models.ott_feature.CheckRegDomains;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface EkycOcrResponseDataMapper {
    EkycOcrResponseDataMapper INSTANCE = Mappers.getMapper(EkycOcrResponseDataMapper.class);

    EkycOcrDomains toDomain(EkycOcrResponse dto);

    @InheritInverseConfiguration
    EkycOcrResponse fromDomain(EkycOcrDomains domain);

}
