package com.vietinbank.core_data.mapper.transfer;


import com.vietinbank.core_data.models.response.ContactCreateResponse;
import com.vietinbank.core_data.models.response.GetPaymentTemplateListResponse;
import com.vietinbank.core_domain.models.maker.ContactCreateDomains;
import com.vietinbank.core_domain.models.maker.GetPaymentTemplateListDomains;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ContactCreateResponseDataMapper {
    ContactCreateResponseDataMapper INSTANCE = Mappers.getMapper(ContactCreateResponseDataMapper.class);

    ContactCreateDomains toDomain(ContactCreateResponse dto);

    @InheritInverseConfiguration
    ContactCreateResponse fromDomain(ContactCreateDomains domain);

}
