package com.vietinbank.core_data.mapper.checker;

import com.vietinbank.core_data.models.response.RejectResponseData;
import com.vietinbank.core_domain.models.checker.RejectDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface RejectResponseDataMapper {
    RejectResponseDataMapper INSTANCE = Mappers.getMapper(RejectResponseDataMapper.class);

    RejectDomain toDomain(RejectResponseData dto);

    @InheritInverseConfiguration
    RejectResponseData fromDomain(RejectDomain domain);
}
