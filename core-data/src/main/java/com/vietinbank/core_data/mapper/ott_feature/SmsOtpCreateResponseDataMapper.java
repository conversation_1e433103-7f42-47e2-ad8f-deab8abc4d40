package com.vietinbank.core_data.mapper.ott_feature;


import com.vietinbank.core_data.models.response.ott_feature.SmsOtpCreateRequestResponse;
import com.vietinbank.core_domain.models.ott_feature.SmsOtpCreateRequestDomains;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface SmsOtpCreateResponseDataMapper {
    SmsOtpCreateResponseDataMapper INSTANCE = Mappers.getMapper(SmsOtpCreateResponseDataMapper.class);

    SmsOtpCreateRequestDomains toDomain(SmsOtpCreateRequestResponse dto);

    @InheritInverseConfiguration
    SmsOtpCreateRequestResponse fromDomain(SmsOtpCreateRequestDomains domain);

}
