package com.vietinbank.core_data.mapper.transfer;


import com.vietinbank.core_data.models.response.CreateTemplateResponse;
import com.vietinbank.core_data.models.response.ValidatePaymentOrderTransferResponse;
import com.vietinbank.core_domain.models.maker.CreateTemplateDomains;
import com.vietinbank.core_domain.models.maker.ValidatePaymentOrderTransferDomains;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ValidatePaymentOrderTransferResponseDataMapper {
    ValidatePaymentOrderTransferResponseDataMapper INSTANCE = Mappers.getMapper(ValidatePaymentOrderTransferResponseDataMapper.class);

    ValidatePaymentOrderTransferDomains toDomain(ValidatePaymentOrderTransferResponse dto);

    @InheritInverseConfiguration
    ValidatePaymentOrderTransferResponse fromDomain(ValidatePaymentOrderTransferDomains domain);

}
