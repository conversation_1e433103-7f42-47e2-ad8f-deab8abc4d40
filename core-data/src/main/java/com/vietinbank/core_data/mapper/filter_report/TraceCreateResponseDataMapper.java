package com.vietinbank.core_data.mapper.filter_report;

import com.vietinbank.core_data.models.response.CreateTransferResponse;
import com.vietinbank.core_domain.models.maker.CreateTransferDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface TraceCreateResponseDataMapper {
    TraceCreateResponseDataMapper INSTANCE = Mappers.getMapper(TraceCreateResponseDataMapper.class);

    CreateTransferDomain toDomain(CreateTransferResponse dto);


    @InheritInverseConfiguration
    CreateTransferResponse fromDomain(CreateTransferDomain domain);
}