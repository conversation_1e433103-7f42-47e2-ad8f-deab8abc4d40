package com.vietinbank.core_data.mapper.checker;

import com.vietinbank.core_data.models.response.CountPendingResponseData;
import com.vietinbank.core_domain.models.checker.CountPendingDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CountPendingResponseDataMapper {
    CountPendingResponseDataMapper INSTANCE = Mappers.getMapper(CountPendingResponseDataMapper.class);

    CountPendingDomain toDomain(CountPendingResponseData dto);

    @InheritInverseConfiguration
    CountPendingResponseData fromDomain(CountPendingDomain domain);
}
