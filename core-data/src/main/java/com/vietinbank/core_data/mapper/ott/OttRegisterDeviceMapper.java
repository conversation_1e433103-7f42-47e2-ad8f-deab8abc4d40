package com.vietinbank.core_data.mapper.ott;

import com.vietinbank.core_data.models.response.ott.OttRegisterResponse;
import com.vietinbank.core_domain.models.ott.OttRegisterDeviceDomain;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * Created by vandz on 17/4/25.
 * MapStruct mapper for OTT related entities.
 */
@Mapper
public interface OttRegisterDeviceMapper {
    OttRegisterDeviceMapper INSTANCE = Mappers.getMapper(OttRegisterDeviceMapper.class);

    /**
     * Converts OttRegisterResponse DTO to OttRegisterTokenDomain
     *
     * @param dto the data transfer object from API
     * @return domain object for use in business logic
     */
    OttRegisterDeviceDomain toDomain(OttRegisterResponse dto);
}