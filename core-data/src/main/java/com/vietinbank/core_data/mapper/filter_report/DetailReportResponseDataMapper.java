package com.vietinbank.core_data.mapper.filter_report;

import com.vietinbank.core_data.models.response.TransDetailModel;
import com.vietinbank.core_domain.models.manage.TransDetailDomain;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface DetailReportResponseDataMapper {
    DetailReportResponseDataMapper INSTANCE = Mappers.getMapper(DetailReportResponseDataMapper.class);

    TransDetailDomain toDomain(TransDetailModel dto);
}

