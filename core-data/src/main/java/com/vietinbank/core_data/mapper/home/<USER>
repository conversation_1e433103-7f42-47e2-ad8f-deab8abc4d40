package com.vietinbank.core_data.mapper.home;

import com.vietinbank.core_data.models.response.home_response.ListLatestResponse;
import com.vietinbank.core_domain.models.home.ListLatestDomain;
import com.vietinbank.core_domain.models.home.TransactionInfo;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


@Mapper
public interface ListLatestResponseDataMapper {
    ListLatestResponseDataMapper INSTANCE = Mappers.getMapper(ListLatestResponseDataMapper.class);

    ListLatestDomain toDomain(ListLatestResponse dto);
    
    // Map TransactionInfo from response to domain
    TransactionInfo mapTransactionInfo(
        com.vietinbank.core_data.models.response.home_response.TransactionInfo source
    );
}

