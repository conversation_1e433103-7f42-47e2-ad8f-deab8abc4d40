package com.vietinbank.core_data.mapper.home;

import com.vietinbank.core_data.models.response.home_response.ListFunctionResponse;
import com.vietinbank.core_domain.models.home.ListFunctionDomain;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ListFunctionResponseDataMapper {
    ListFunctionResponseDataMapper INSTANCE = Mappers.getMapper(ListFunctionResponseDataMapper.class);

    ListFunctionDomain toDomain(ListFunctionResponse dto);
}
