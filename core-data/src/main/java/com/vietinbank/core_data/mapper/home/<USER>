package com.vietinbank.core_data.mapper.home;

import com.vietinbank.core_data.models.response.CheckUserEkycRespone;
import com.vietinbank.core_domain.models.home.CheckUserEkycDomain;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CheckUserEkycResponseDataMapper {
    CheckUserEkycResponseDataMapper INSTANCE = Mappers.getMapper(CheckUserEkycResponseDataMapper.class);

    CheckUserEkycDomain toDomain(CheckUserEkycRespone dto);
}
