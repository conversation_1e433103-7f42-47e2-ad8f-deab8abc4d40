package com.vietinbank.core_data.mapper.home;

import com.vietinbank.core_data.models.response.CheckPasswordResponse;
import com.vietinbank.core_domain.models.home.CheckPasswordDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CheckPasswordResponseDataMapper {
    CheckPasswordResponseDataMapper INSTANCE = Mappers.getMapper(CheckPasswordResponseDataMapper.class);

    CheckPasswordDomain toDomain(CheckPasswordResponse dto);

    @InheritInverseConfiguration
    CheckPasswordResponse fromDomain(CheckPasswordDomain domain);
}
