package com.vietinbank.core_data.mapper.home;

import com.vietinbank.core_data.models.response.UpdateEkycResponse;
import com.vietinbank.core_domain.models.home.UpdateEkycDomain;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface UpdateEkycResponseDataMapper {
    UpdateEkycResponseDataMapper INSTANCE = Mappers.getMapper(UpdateEkycResponseDataMapper.class);

    UpdateEkycDomain toDomain(UpdateEkycResponse dto);
}
