package com.vietinbank.core_data.mapper.home;

import com.vietinbank.core_data.models.response.GetBiometricFaceResponse;
import com.vietinbank.core_domain.models.home.GetBiometricFaceDomain;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface GetBiometricFaceResponseDataMapper {
    GetBiometricFaceResponseDataMapper INSTANCE = Mappers.getMapper(GetBiometricFaceResponseDataMapper.class);
    GetBiometricFaceDomain toDomain(GetBiometricFaceResponse dto);
}
