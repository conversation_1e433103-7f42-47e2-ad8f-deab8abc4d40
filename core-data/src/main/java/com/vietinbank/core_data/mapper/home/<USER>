package com.vietinbank.core_data.mapper.home;

import com.vietinbank.core_data.models.response.RegisterTouchIDResponseData;
import com.vietinbank.core_domain.models.home.RegisterTouchIDDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface RegisterTouchIDResponseDataMapper {
    RegisterTouchIDResponseDataMapper INSTANCE = Mappers.getMapper(RegisterTouchIDResponseDataMapper.class);

    RegisterTouchIDDomain toDomain(RegisterTouchIDResponseData dto);

    @InheritInverseConfiguration
    RegisterTouchIDResponseData fromDomain(RegisterTouchIDDomain domain);
}
