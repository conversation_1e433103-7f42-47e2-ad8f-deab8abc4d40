package com.vietinbank.core_data.mapper.home;

import com.vietinbank.core_data.models.response.home_response.HomeAccountListResponse;
import com.vietinbank.core_domain.models.home.CreditCardLst;
import com.vietinbank.core_domain.models.home.FeePlan;
import com.vietinbank.core_domain.models.home.HomeAccountListDomain;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface HomeAccountListResponseDataMapper {
    HomeAccountListResponseDataMapper INSTANCE = Mappers.getMapper(HomeAccountListResponseDataMapper.class);

    HomeAccountListDomain toDomain(HomeAccountListResponse dto);


    // Map CreditCardLst from response to domain
    CreditCardLst mapCreditCardLst(
            com.vietinbank.core_data.models.response.home_response.CreditCardLst source
    );

    // Map FeePlan from response to domain
    FeePlan mapFeePlan(
            com.vietinbank.core_data.models.response.home_response.FeePlan source
    );

}
