package com.vietinbank.core_data.mapper.home;

import com.vietinbank.core_data.models.response.home_response.ListKeyResponse;
import com.vietinbank.core_domain.models.home.ListKeyDomain;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


@Mapper
public interface ListKeyResponseDataMapper {
    ListKeyResponseDataMapper INSTANCE = Mappers.getMapper(ListKeyResponseDataMapper.class);

    ListKeyDomain toDomain(ListKeyResponse dto);
}