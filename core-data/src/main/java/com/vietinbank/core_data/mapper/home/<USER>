package com.vietinbank.core_data.mapper.home;

import com.vietinbank.core_data.models.response.GenerateOtpResponse;
import com.vietinbank.core_data.models.response.VerifyOtpEkycResponse;
import com.vietinbank.core_domain.models.home.GenerateOtpDomain;
import com.vietinbank.core_domain.models.home.VerifyOtpEkycDomain;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface GenerateOtpDataMapper {
    GenerateOtpDataMapper INSTANCE = Mappers.getMapper(GenerateOtpDataMapper.class);

    GenerateOtpDomain toDomain(GenerateOtpResponse dto);

    VerifyOtpEkycDomain toDomain(VerifyOtpEkycResponse dto);
}
