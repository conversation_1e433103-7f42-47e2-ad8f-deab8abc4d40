package com.vietinbank.core_data.mapper.home;

import com.vietinbank.core_data.models.response.home_response.CountTransGroupResponse;
import com.vietinbank.core_domain.models.home.CountTransGroupDomain;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CountTransGroupResponseDataMapper {
    CountTransGroupResponseDataMapper INSTANCE = Mappers.getMapper(CountTransGroupResponseDataMapper.class);

    CountTransGroupDomain toDomain(CountTransGroupResponse dto);
}

