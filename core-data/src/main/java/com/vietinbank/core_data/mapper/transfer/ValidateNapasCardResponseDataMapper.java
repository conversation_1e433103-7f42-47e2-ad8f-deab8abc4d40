package com.vietinbank.core_data.mapper.transfer;


import com.vietinbank.core_data.models.response.ValidateNapasCardResponse;
import com.vietinbank.core_domain.models.maker.ValidateNapasCardDomains;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ValidateNapasCardResponseDataMapper {
    ValidateNapasCardResponseDataMapper INSTANCE = Mappers.getMapper(ValidateNapasCardResponseDataMapper.class);

    ValidateNapasCardDomains toDomain(ValidateNapasCardResponse dto);

    @InheritInverseConfiguration
    ValidateNapasCardResponse fromDomain(ValidateNapasCardDomains domain);

}
