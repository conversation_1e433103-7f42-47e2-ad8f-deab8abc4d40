package com.vietinbank.core_data.mapper.login;

import com.vietinbank.core_data.models.response.VerifyOTPResponseData;
import com.vietinbank.core_domain.models.login.VerifyOTPDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface VerifyOTPResponseDataMapper {
    VerifyOTPResponseDataMapper INSTANCE = Mappers.getMapper(VerifyOTPResponseDataMapper.class);

    VerifyOTPDomain toDomain(VerifyOTPResponseData dto);

    @InheritInverseConfiguration
    VerifyOTPResponseData fromDomain(VerifyOTPDomain domain);
}
