package com.vietinbank.core_data.mapper.ott_feature;


import com.vietinbank.core_data.models.response.ott_feature.CheckRegResponse;
import com.vietinbank.core_domain.models.ott_feature.CheckRegDomains;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CheckRegResponseDataMapper {
    CheckRegResponseDataMapper INSTANCE = Mappers.getMapper(CheckRegResponseDataMapper.class);

    CheckRegDomains toDomain(CheckRegResponse dto);

    @InheritInverseConfiguration
    CheckRegResponse fromDomain(CheckRegDomains domain);

}
