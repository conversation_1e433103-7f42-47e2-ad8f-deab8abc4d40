package com.vietinbank.core_data.mapper.ekyc_feature;


import com.vietinbank.core_data.models.response.ekyc_feature.EkycNFCResponse;
import com.vietinbank.core_data.models.response.ekyc_feature.EkycOcrResponse;
import com.vietinbank.core_domain.models.ekyc_feature.EkycNFCDomains;
import com.vietinbank.core_domain.models.ekyc_feature.EkycOcrDomains;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface EkycNFCResponseDataMapper {
    EkycNFCResponseDataMapper INSTANCE = Mappers.getMapper(EkycNFCResponseDataMapper.class);

    EkycNFCDomains toDomain(EkycNFCResponse dto);

    @InheritInverseConfiguration
    EkycNFCResponse fromDomain(EkycNFCDomains domain);

}
