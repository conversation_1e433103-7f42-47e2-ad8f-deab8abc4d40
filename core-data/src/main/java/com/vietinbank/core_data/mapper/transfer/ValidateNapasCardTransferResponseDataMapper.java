package com.vietinbank.core_data.mapper.transfer;


import com.vietinbank.core_data.models.response.ValidateNapasCardResponse;
import com.vietinbank.core_data.models.response.ValidateNapasCardTransferResponse;
import com.vietinbank.core_domain.models.maker.ValidateNapasCardDomains;
import com.vietinbank.core_domain.models.maker.ValidateNapasCardTransferDomains;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ValidateNapasCardTransferResponseDataMapper {
    ValidateNapasCardTransferResponseDataMapper INSTANCE = Mappers.getMapper(ValidateNapasCardTransferResponseDataMapper.class);

    ValidateNapasCardTransferDomains toDomain(ValidateNapasCardTransferResponse dto);

    @InheritInverseConfiguration
    ValidateNapasCardTransferResponse fromDomain(ValidateNapasCardTransferDomains domain);

}
