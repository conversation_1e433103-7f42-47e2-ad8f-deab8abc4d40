package com.vietinbank.core_data.mapper.checker;

import com.vietinbank.core_data.models.response.GetTransactionDetailResponseData;
import com.vietinbank.core_data.models.response.SubTransactionResponseData;
import com.vietinbank.core_domain.models.checker.GetTransactionDetailDomain;
import com.vietinbank.core_domain.models.checker.SubTransactionDomain;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface GetTransactionDetailResponseDataMapper {
    GetTransactionDetailResponseDataMapper INSTANCE = Mappers.getMapper(GetTransactionDetailResponseDataMapper.class);

    GetTransactionDetailDomain toDomain(GetTransactionDetailResponseData dto);
    @InheritInverseConfiguration
    GetTransactionDetailResponseData fromDomain(GetTransactionDetailDomain domain);


    SubTransactionDomain toDomain(SubTransactionResponseData dto);
    @InheritInverseConfiguration
    SubTransactionResponseData fromDomain(SubTransactionDomain domain);


}
