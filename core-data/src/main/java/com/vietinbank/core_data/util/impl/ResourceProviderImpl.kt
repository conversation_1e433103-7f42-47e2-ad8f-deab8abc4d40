package com.vietinbank.core_data.util.impl

import android.content.Context
import androidx.core.content.ContextCompat
import com.vietinbank.core_common.utils.IResourceProvider
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject

/**
 * Created by vandz on 25/4/25.
 */
class ResourceProviderImpl @Inject constructor(
    @ApplicationContext private val context: Context,
) : IResourceProvider {
    override fun getString(resId: Int): String {
        return context.getString(resId)
    }

    override fun getString(resId: Int, vararg formatArgs: Any): String {
        return context.getString(resId, *formatArgs)
    }

    override fun getResColor(resId: Int): Int {
        return ContextCompat.getColor(context, resId)
    }

    override fun getComposeColor(resId: Int): androidx.compose.ui.graphics.Color {
        return androidx.compose.ui.graphics.Color(ContextCompat.getColor(context, resId))
    }
}
