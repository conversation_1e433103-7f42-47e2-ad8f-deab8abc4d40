package com.vietinbank.core_data.repository

import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_data.mapper.ekyc_feature.EkycConfirmInputResponseDataMapper
import com.vietinbank.core_data.mapper.ekyc_feature.EkycNFCResponseDataMapper
import com.vietinbank.core_data.mapper.ekyc_feature.EkycOcrResponseDataMapper
import com.vietinbank.core_data.mapper.home.GenerateOtpDataMapper
import com.vietinbank.core_data.models.request.ekyc_feature.EkycCompareImageRequest
import com.vietinbank.core_data.models.request.ekyc_feature.EkycConfirmInputRequest
import com.vietinbank.core_data.models.request.ekyc_feature.EkycNFCRequest
import com.vietinbank.core_data.models.request.ekyc_feature.EkycOcrRequest
import com.vietinbank.core_data.models.request.ekyc_feature.ImageLivenessItemRequest
import com.vietinbank.core_data.models.response.VerifyOtpEkycResponse
import com.vietinbank.core_data.models.response.ekyc_feature.EkycConfirmInputResponse
import com.vietinbank.core_data.models.response.ekyc_feature.EkycNFCResponse
import com.vietinbank.core_data.models.response.ekyc_feature.EkycOcrResponse
import com.vietinbank.core_data.network.api.ApiService
import com.vietinbank.core_data.network.api.IApiClient
import com.vietinbank.core_data.network.di.Default
import com.vietinbank.core_domain.models.ekyc_feature.EkycCompareImageRequestParams
import com.vietinbank.core_domain.models.ekyc_feature.EkycConfirmInputDomains
import com.vietinbank.core_domain.models.ekyc_feature.EkycConfirmInputRequestParams
import com.vietinbank.core_domain.models.ekyc_feature.EkycImageLivenessItemParam
import com.vietinbank.core_domain.models.ekyc_feature.EkycNFCDomains
import com.vietinbank.core_domain.models.ekyc_feature.EkycNFCRequestParams
import com.vietinbank.core_domain.models.ekyc_feature.EkycOcrDomains
import com.vietinbank.core_domain.models.ekyc_feature.EkycOcrRequestParams
import com.vietinbank.core_domain.models.home.VerifyOtpEkycDomain
import com.vietinbank.core_domain.repository.EkycFeatureRepository
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class EkycFeatureRepositoryImpl @Inject constructor(
    @Default private val apiClient: IApiClient,
    private val apiService: ApiService,
) : EkycFeatureRepository {
    override suspend fun ekycOcr(params: EkycOcrRequestParams): Resource<EkycOcrDomains> {
        val request = EkycOcrRequest(
            imageBack = params.imageBack,
            imageFront = params.imageFront,
            username = params.username,
            type = params.type,
            userType = params.userType,
        )

        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<EkycOcrResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val checkRegResponse =
                    base.data as? EkycOcrResponse ?: return Resource.Error(
                        "Cannot parse to EkycOcrResponse",
                        "999",
                    )
                val ekycOcrDomains =
                    EkycOcrResponseDataMapper.INSTANCE.toDomain(
                        checkRegResponse,
                    )
                Resource.Success(ekycOcrDomains)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun ekycNFC(params: EkycNFCRequestParams): Resource<EkycNFCDomains> {
        val request = EkycNFCRequest(
            userType = params.userType,
            aAResult = params.aAResult,
            challenge = params.challenge,
            dg1 = params.dg1,
            dg2 = params.dg2,
            dg3 = params.dg3,
            dg4 = params.dg4,
            dg5 = params.dg5,
            dg6 = params.dg6,
            dg7 = params.dg7,
            dg8 = params.dg8,
            dg9 = params.dg9,
            dg10 = params.dg10,
            dg11 = params.dg11,
            dg12 = params.dg12,
            dg13 = params.dg13,
            dg14 = params.dg14,
            dg15 = params.dg15,
            dg16 = params.dg16,
            eACCAResult = params.eACCAResult,
            sod = params.sod,
            username = params.username,
            eKycId = params.eKycId,
            type = params.type,
        )

        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<EkycNFCResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val checkRegResponse =
                    base.data as? EkycNFCResponse ?: return Resource.Error(
                        "Cannot parse to EkycNFCResponse",
                        "999",
                    )
                val ekycNFCDomains =
                    EkycNFCResponseDataMapper.INSTANCE.toDomain(
                        checkRegResponse,
                    )
                Resource.Success(ekycNFCDomains)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun ekycConfirmInput(params: EkycConfirmInputRequestParams): Resource<EkycConfirmInputDomains> {
        fun EkycImageLivenessItemParam.toRequest(): ImageLivenessItemRequest {
            return ImageLivenessItemRequest(
                image = this.image,
                time = this.time,
            )
        }

        fun List<EkycImageLivenessItemParam>.toRequestList(): MutableList<ImageLivenessItemRequest> {
            return this.map { it.toRequest() }.toMutableList()
        }

        val request = EkycConfirmInputRequest(
            eKycId = params.eKycId,
            username = params.username,
            images = params.images.toRequestList(),
        )
        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<EkycConfirmInputResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val ekycConfirmInputResponse =
                    base.data as? EkycConfirmInputResponse ?: return Resource.Error(
                        "Cannot parse to EkycConfirmInputResponse",
                        "999",
                    )
                val domains =
                    EkycConfirmInputResponseDataMapper.INSTANCE.toDomain(
                        ekycConfirmInputResponse,
                    )
                Resource.Success(domains)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun ekycCompareImage(params: EkycCompareImageRequestParams): Resource<VerifyOtpEkycDomain> {
        val request = EkycCompareImageRequest(
            eKycId = params.eKycId,
            username = params.username,
            image = params.image,
        )
        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<VerifyOtpEkycResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val response =
                    base.data as? VerifyOtpEkycResponse ?: return Resource.Error(
                        "Cannot parse to VerifyOtpEkycResponse",
                        "999",
                    )
                val domains =
                    GenerateOtpDataMapper.INSTANCE.toDomain(
                        response,
                    )
                Resource.Success(domains)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }
}
