package com.vietinbank.core_data.repository

import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_data.mapper.report_transfer.TransactionListResponseDataMapper
import com.vietinbank.core_data.models.request.TransactionListRequest
import com.vietinbank.core_data.models.response.TransactionListResponse
import com.vietinbank.core_data.network.api.ApiService
import com.vietinbank.core_data.network.api.IApiClient
import com.vietinbank.core_data.network.di.Default
import com.vietinbank.core_domain.models.transfer_report.TransactionListDomains
import com.vietinbank.core_domain.models.transfer_report.TransactionListParams
import com.vietinbank.core_domain.repository.ReportTransferRepository
import javax.inject.Inject

class ReportTransferRepositoryImpl @Inject constructor(
    @Default private val apiClient: IApiClient,
    private val apiService: ApiService,
) : ReportTransferRepository {
    override suspend fun transactionList(params: TransactionListParams): Resource<TransactionListDomains> {
        val request = TransactionListRequest(
            orderByAmount = params.orderByAmount,
            content = params.content,
            username = params.username,
            serviceType = params.serviceType,
            all = params.all,
            pageNum = params.pageNum,
            orderByApproveDate = params.orderByApproveDate,
            pageSize = params.pageSize,
            tranType = params.tranType,
            mtId = params.mtId,
        )
        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<TransactionListResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val acclistResponseData = base.data as? TransactionListResponse
                    ?: return Resource.Error("Cannot parse to TransactionListResponse", "999")
                val acclistDomain =
                    TransactionListResponseDataMapper.INSTANCE.toDomain(acclistResponseData)
                Resource.Success(acclistDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }
}
