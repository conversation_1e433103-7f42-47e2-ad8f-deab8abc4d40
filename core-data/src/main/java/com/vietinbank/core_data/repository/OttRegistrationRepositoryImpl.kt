package com.vietinbank.core_data.repository

import com.vietinbank.core_common.cache.CacheFactory
import com.vietinbank.core_common.cache.CacheManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_domain.models.ott_feature.ListRegDomains
import com.vietinbank.core_domain.repository.cache.IOttRegistrationRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of IOttRegistrationRepository
 * Maintains backward compatibility with existing OttFeatureCacheManager behavior
 */
@Singleton
class OttRegistrationRepositoryImpl @Inject constructor(
    cacheFactory: CacheFactory,
) : IOttRegistrationRepository {

    private val cache: CacheManager<ListRegDomains> = cacheFactory.create(
        key = Tags.LIST_REGISTER_OTT_CACHE,
        expirationTime = Long.MAX_VALUE,
        encrypted = false,
    )
    private val countTimeShowRegisterOTT: CacheManager<Int> = cacheFactory.create(
        key = Tags.COUNT_TIME_SHOW_REGISTER_OTT_CACHE,
        expirationTime = Long.MAX_VALUE,
        encrypted = false,
    )

    private val emptyListRegDomains = ListRegDomains(
        encryptKey = "",
        encryptType = "",
        fees = emptyList(),
        results = emptyList(),
    )

    private val _registrationFlow = MutableStateFlow(emptyListRegDomains)
    override val registrationFlow: StateFlow<ListRegDomains> = _registrationFlow.asStateFlow()

    private val _countTimeShowRegisterOTTFlow = MutableStateFlow(1)
    override val countTimeShowRegisterOTTFlow: StateFlow<Int> =
        _countTimeShowRegisterOTTFlow.asStateFlow()

    init {
        countTimeShowRegisterOTT.loadIfNeeded()
        val cachedCount = countTimeShowRegisterOTT.getCached() ?: 1
        _countTimeShowRegisterOTTFlow.value = cachedCount.coerceAtMost(5)
    }

    override fun getCachedRegistrations(): ListRegDomains? {
        return _registrationFlow.value
    }

    override fun saveRegistrations(data: ListRegDomains) {
        _registrationFlow.value = data
        cache.saveAsync(data)
    }

    override fun incrementCountTimeShowRegisterOTT() {
        val currentCount = getCahceCountTimeShowRegisterOTT()
        if (currentCount <= 5) {
            val newCount = currentCount + 1
            _countTimeShowRegisterOTTFlow.value = newCount
            countTimeShowRegisterOTT.saveAsync(data = newCount)
        }
    }

    override fun getCahceCountTimeShowRegisterOTT(): Int {
        val memoryValue = _countTimeShowRegisterOTTFlow.value
        val cachedValue = countTimeShowRegisterOTT.getCached()
        return cachedValue ?: memoryValue
    }

    override fun clearRegistrations() {
        _registrationFlow.value = emptyListRegDomains
        cache.clearAsync()
    }

    override fun clearCahceCountTimeShowRegisterOTT() {
        _countTimeShowRegisterOTTFlow.value = 1
        countTimeShowRegisterOTT.clearAsync()
    }

    override fun clearAllOttCache(needClearCahceCountTimeShowRegisterOTT: Boolean) {
        if (needClearCahceCountTimeShowRegisterOTT) {
            clearCahceCountTimeShowRegisterOTT()
        }
        clearRegistrations()
    }

    override suspend fun hasCachedData(): Boolean {
        return cache.exists() && cache.get()?.results?.isNotEmpty() == true
    }
}