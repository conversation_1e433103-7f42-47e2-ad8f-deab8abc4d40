package com.vietinbank.core_data.repository

import com.vietinbank.core_common.cache.CacheFactory
import com.vietinbank.core_common.cache.CacheManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_domain.models.home.ListLatestDomain
import com.vietinbank.core_domain.models.home.TransactionInfo
import com.vietinbank.core_domain.models.login.AccountDefaultDomain
import com.vietinbank.core_domain.models.login.AccountListDomain
import com.vietinbank.core_domain.models.maker.BranchDomains
import com.vietinbank.core_domain.models.maker.BranchListDomains
import com.vietinbank.core_domain.models.maker.ContactDomains
import com.vietinbank.core_domain.models.maker.ContactListDomains
import com.vietinbank.core_domain.models.maker.DataBankDomain
import com.vietinbank.core_domain.models.maker.GetPaymentTemplateListDomains
import com.vietinbank.core_domain.models.maker.NapasBankListDomain
import com.vietinbank.core_domain.models.maker.TempTransactionDomains
import com.vietinbank.core_domain.repository.cache.ITransferCacheManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class TransferCacheManagerImpl @Inject constructor(
    cacheFactory: CacheFactory,
) : ITransferCacheManager {

    // Cache managers for each data type
    private val banksCache: CacheManager<MutableList<DataBankDomain>> = cacheFactory.create(
        key = Tags.LIST_BANKS_CACHE,
        expirationTime = Long.MAX_VALUE,
        encrypted = false,
    )

    private val contactsCache: CacheManager<MutableList<ContactDomains>> = cacheFactory.create(
        key = Tags.LIST_CONTACTS_CACHE,
        expirationTime = Long.MAX_VALUE,
        encrypted = true,
    )

    private val tempsCache: CacheManager<MutableList<TempTransactionDomains>> = cacheFactory.create(
        key = Tags.LIST_TEMPS_CACHE,
        expirationTime = Long.MAX_VALUE,
        encrypted = true,
    )

    private val latestListCache: CacheManager<MutableList<TransactionInfo>> = cacheFactory.create(
        key = Tags.LIST_LATEST_CACHE,
        expirationTime = Long.MAX_VALUE,
        encrypted = true,
    )

    private val accountsINCache: CacheManager<MutableList<AccountDefaultDomain>> = cacheFactory.create(
        key = Tags.LIST_ACCOUNTS_IN_CACHE,
        expirationTime = Long.MAX_VALUE,
        encrypted = true,
    )

    private val accountsOUTCache: CacheManager<MutableList<AccountDefaultDomain>> = cacheFactory.create(
        key = Tags.LIST_ACCOUNTS_OUT_CACHE,
        expirationTime = Long.MAX_VALUE,
        encrypted = true,
    )

    private val accountsNP247Cache: CacheManager<MutableList<AccountDefaultDomain>> = cacheFactory.create(
        key = Tags.LIST_ACCOUNTS_NP247_CACHE,
        expirationTime = Long.MAX_VALUE,
        encrypted = true,
    )

    private val accountsCARDCache: CacheManager<MutableList<AccountDefaultDomain>> = cacheFactory.create(
        key = Tags.LIST_ACCOUNTS_CARD_CACHE,
        expirationTime = Long.MAX_VALUE,
        encrypted = true,
    )

    private val accountsPaymentOrderCache: CacheManager<MutableList<AccountDefaultDomain>> = cacheFactory.create(
        key = Tags.LIST_ACCOUNTS_PM_CACHE,
        expirationTime = Long.MAX_VALUE,
        encrypted = true,
    )

    private val branchCache: CacheManager<MutableList<BranchDomains>> = cacheFactory.create(
        key = Tags.LIST_BRANCH_CACHE,
        expirationTime = Long.MAX_VALUE,
        encrypted = false, // Branch list doesn't contain sensitive data
    )

    // Initialize with empty data to match old behavior
    private val emptyBankList = NapasBankListDomain(dataBanks = mutableListOf())
    private val emptyContactList = ContactListDomains(contacts = mutableListOf())
    private val emptyTempList = GetPaymentTemplateListDomains(tempTransactionList = mutableListOf())
    private val emptyLatestList = ListLatestDomain(transactionInfoList = arrayListOf())
    private val emptyAccountList = AccountListDomain(accountDefault = mutableListOf())
    private val emptyBranchList = BranchListDomains(data = mutableListOf())

    // StateFlows for backward compatibility
    private val _banksCacheFlow = MutableStateFlow(emptyBankList)
    override val banksCacheFlow: StateFlow<NapasBankListDomain> = _banksCacheFlow.asStateFlow()

    private val _contactsCacheFlow = MutableStateFlow(emptyContactList)
    override val contactsCacheFlow: StateFlow<ContactListDomains> = _contactsCacheFlow.asStateFlow()

    private val _tempCacheFlow = MutableStateFlow(emptyTempList)
    override val tempCacheFlow: StateFlow<GetPaymentTemplateListDomains> = _tempCacheFlow.asStateFlow()

    private val _latestListCacheFlow = MutableStateFlow(emptyLatestList)
    override val latestListCacheFlow: StateFlow<ListLatestDomain> = _latestListCacheFlow.asStateFlow()

    private val _accountsINCacheFlow = MutableStateFlow(emptyAccountList)
    override val accountsINCacheFlow: StateFlow<AccountListDomain> = _accountsINCacheFlow.asStateFlow()

    private val _accountsOUTCacheFlow = MutableStateFlow(emptyAccountList)
    override val accountsOUTCacheFlow: StateFlow<AccountListDomain> = _accountsOUTCacheFlow.asStateFlow()

    private val _accountsNP247CacheFlow = MutableStateFlow(emptyAccountList)
    override val accountsNP247CacheFlow: StateFlow<AccountListDomain> = _accountsNP247CacheFlow.asStateFlow()

    private val _accountsCARDCacheFlow = MutableStateFlow(emptyAccountList)
    override val accountsCARDCacheFlow: StateFlow<AccountListDomain> = _accountsCARDCacheFlow.asStateFlow()

    private val _accountsPaymentOrderCacheFlow = MutableStateFlow(emptyAccountList)
    override val accountsPaymentOrderCacheFlow: StateFlow<AccountListDomain> = _accountsPaymentOrderCacheFlow.asStateFlow()

    private val _branchListCacheFlow = MutableStateFlow(emptyBranchList)
    override val branchListCacheFlow: StateFlow<BranchListDomains> = _branchListCacheFlow.asStateFlow()

    // Banks danh sach ngan hang
    override fun saveBankList(dataBanks: MutableList<DataBankDomain>) {
        _banksCacheFlow.value = NapasBankListDomain(dataBanks = dataBanks)
        banksCache.saveAsync(dataBanks)
    }

    override fun clearBankList() {
        _banksCacheFlow.value = NapasBankListDomain(dataBanks = mutableListOf())
        banksCache.clearAsync()
    }

    override fun getBankList(): List<DataBankDomain>? {
        return _banksCacheFlow.value.dataBanks
    }

    // Danh sach nguoi thu huong
    override fun saveContactist(dataContacts: MutableList<ContactDomains>) {
        _contactsCacheFlow.value = ContactListDomains(contacts = dataContacts)
        contactsCache.saveAsync(dataContacts)
    }
    override fun saveTempList(dataTemps: MutableList<TempTransactionDomains>) {
        _tempCacheFlow.value = GetPaymentTemplateListDomains(tempTransactionList = dataTemps)
        tempsCache.saveAsync(dataTemps)
    }
    override fun saveLatestList(dataLatest: MutableList<TransactionInfo>) {
        _latestListCacheFlow.value = ListLatestDomain(transactionInfoList = ArrayList(dataLatest))
        latestListCache.saveAsync(dataLatest)
    }
    override fun clearContactList() {
        _contactsCacheFlow.value = ContactListDomains(contacts = mutableListOf())
        contactsCache.clearAsync()
    }
    override fun clearTempList() {
        _tempCacheFlow.value = GetPaymentTemplateListDomains(tempTransactionList = mutableListOf())
        tempsCache.clearAsync()
    }
    override fun clearLatestList() {
        _latestListCacheFlow.value = ListLatestDomain(transactionInfoList = arrayListOf())
        latestListCache.clearAsync()
    }

    override fun getContactList(): List<ContactDomains>? {
        return _contactsCacheFlow.value.contacts
    }
    override fun getTempList(): List<TempTransactionDomains>? {
        return _tempCacheFlow.value.tempTransactionList
    }

    override fun getLatTestList(): List<TransactionInfo>? {
        return _latestListCacheFlow.value.transactionInfoList
    }

    // Accounts IN
    override fun saveAccountsIN(dataAccounts: MutableList<AccountDefaultDomain>) {
        _accountsINCacheFlow.value = AccountListDomain(accountDefault = dataAccounts)
        accountsINCache.saveAsync(dataAccounts)
    }

    override fun clearAccountsIN() {
        _accountsINCacheFlow.value = AccountListDomain(accountDefault = mutableListOf())
        accountsINCache.clearAsync()
    }

    override fun getAccountsIN(): MutableList<AccountDefaultDomain>? {
        return _accountsINCacheFlow.value.accountDefault
    }

    // Accounts OUT
    override fun saveAccountsOUT(dataAccounts: MutableList<AccountDefaultDomain>) {
        _accountsOUTCacheFlow.value = AccountListDomain(accountDefault = dataAccounts)
        accountsOUTCache.saveAsync(dataAccounts)
    }

    override fun clearAccountsOUT() {
        _accountsOUTCacheFlow.value = AccountListDomain(accountDefault = mutableListOf())
        accountsOUTCache.clearAsync()
    }

    override fun getAccountsOUT(): MutableList<AccountDefaultDomain>? {
        return _accountsOUTCacheFlow.value.accountDefault
    }

    // Accounts NP247
    override fun saveAccountsNP247(dataAccounts: MutableList<AccountDefaultDomain>) {
        _accountsNP247CacheFlow.value = AccountListDomain(accountDefault = dataAccounts)
        accountsNP247Cache.saveAsync(dataAccounts)
    }

    override fun clearAccountsNP247() {
        _accountsNP247CacheFlow.value = AccountListDomain(accountDefault = mutableListOf())
        accountsNP247Cache.clearAsync()
    }

    override fun getAccountsNP247(): MutableList<AccountDefaultDomain>? {
        return _accountsNP247CacheFlow.value.accountDefault
    }

    // Accounts CARD
    override fun saveAccountsCARD(dataAccounts: MutableList<AccountDefaultDomain>) {
        _accountsCARDCacheFlow.value = AccountListDomain(accountDefault = dataAccounts)
        accountsCARDCache.saveAsync(dataAccounts)
    }

    override fun clearAccountsCARD() {
        _accountsCARDCacheFlow.value = AccountListDomain(accountDefault = mutableListOf())
        accountsCARDCache.clearAsync()
    }

    override fun getAccountsCARD(): MutableList<AccountDefaultDomain>? {
        return _accountsCARDCacheFlow.value.accountDefault
    }

    // Accounts Payment Order
    override fun saveAccountsPM(dataAccounts: MutableList<AccountDefaultDomain>) {
        _accountsPaymentOrderCacheFlow.value = AccountListDomain(accountDefault = dataAccounts)
        accountsPaymentOrderCache.saveAsync(dataAccounts)
    }

    override fun clearAccountsPM() {
        _accountsPaymentOrderCacheFlow.value = AccountListDomain(accountDefault = mutableListOf())
        accountsPaymentOrderCache.clearAsync()
    }

    override fun getAccountsPM(): MutableList<AccountDefaultDomain>? {
        return _accountsPaymentOrderCacheFlow.value.accountDefault
    }

    // Branches
    override fun saveBranch(dataBranch: MutableList<BranchDomains>) {
        _branchListCacheFlow.value = BranchListDomains(data = dataBranch)
        branchCache.saveAsync(dataBranch)
    }

    override fun clearBranch() {
        _branchListCacheFlow.value = BranchListDomains(data = mutableListOf())
        branchCache.clearAsync()
    }

    override fun getBranchList(): MutableList<BranchDomains>? {
        return _branchListCacheFlow.value.data
    }

    override fun clearTransferALL() {
        clearBankList()
        clearContactList()
        clearTempList()
        clearLatestList()
        clearAccountsIN()
        clearAccountsOUT()
        clearAccountsNP247()
        clearAccountsCARD()
        clearAccountsPM()
        clearBranch()
    }

    override suspend fun hasCachedData(): Boolean {
        return banksCache.exists() ||
            contactsCache.exists() ||
            tempsCache.exists() ||
            accountsINCache.exists() ||
            accountsOUTCache.exists() ||
            accountsNP247Cache.exists() ||
            accountsCARDCache.exists() ||
            accountsPaymentOrderCache.exists() ||
            latestListCache.exists() ||
            branchCache.exists()
    }
}