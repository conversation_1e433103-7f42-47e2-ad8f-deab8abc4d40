package com.vietinbank.core_data.repository

import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_data.mapper.home.CheckUserEkycResponseDataMapper
import com.vietinbank.core_data.mapper.home.GenerateOtpDataMapper
import com.vietinbank.core_data.mapper.home.GetBiometricFaceResponseDataMapper
import com.vietinbank.core_data.mapper.home.UpdateEkycResponseDataMapper
import com.vietinbank.core_data.models.request.CheckUserEkycRequest
import com.vietinbank.core_data.models.request.GenerateOtpRequest
import com.vietinbank.core_data.models.request.GetBiometricFaceRequest
import com.vietinbank.core_data.models.request.UpdateEkycRequest
import com.vietinbank.core_data.models.request.VerifyOtpEkycRequest
import com.vietinbank.core_data.models.response.CheckUserEkycRespone
import com.vietinbank.core_data.models.response.GenerateOtpResponse
import com.vietinbank.core_data.models.response.GetBiometricFaceResponse
import com.vietinbank.core_data.models.response.UpdateEkycResponse
import com.vietinbank.core_data.models.response.VerifyOtpEkycResponse
import com.vietinbank.core_data.network.api.ApiService
import com.vietinbank.core_data.network.api.IApiClient
import com.vietinbank.core_data.network.di.Default
import com.vietinbank.core_domain.models.home.CheckUserEkycDomain
import com.vietinbank.core_domain.models.home.CheckUserEkycParams
import com.vietinbank.core_domain.models.home.GenerateOtpDomain
import com.vietinbank.core_domain.models.home.GenerateOtpParam
import com.vietinbank.core_domain.models.home.GetBiometricFaceDomain
import com.vietinbank.core_domain.models.home.GetBiometricFaceParams
import com.vietinbank.core_domain.models.home.UpdateEkycDomain
import com.vietinbank.core_domain.models.home.UpdateEkycParams
import com.vietinbank.core_domain.models.home.VerifyOtpEkycDomain
import com.vietinbank.core_domain.models.home.VerifyOtpEkycParam
import com.vietinbank.core_domain.repository.UpdateEkycRepository
import javax.inject.Inject

class UpdateEkycRepositoryImpl @Inject constructor(
    @Default private val apiClient: IApiClient,
    private val apiService: ApiService,
) : UpdateEkycRepository {

    override suspend fun checkUserEkyc(params: CheckUserEkycParams): Resource<CheckUserEkycDomain> {
        val checkUserEkycRequest = CheckUserEkycRequest(
            username = params.username,
        )
        val result = apiClient.makeApiCall(
            request = checkUserEkycRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<CheckUserEkycRespone>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val checkUserEkycRespone = base.data as? CheckUserEkycRespone
                    ?: return Resource.Error("Cannot parse to checkUserEkycDomain", "999")
                val checkUserEkycDomain =
                    CheckUserEkycResponseDataMapper.INSTANCE.toDomain(checkUserEkycRespone)
                Resource.Success(checkUserEkycDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun generateOtpRetail(params: GenerateOtpParam): Resource<GenerateOtpDomain> {
        val generateOtpRequest = GenerateOtpRequest(
            username = params.username,
            mobilePhone = params.phoneNum,
        )
        val result = apiClient.makeApiCall(
            request = generateOtpRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<GenerateOtpResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val generateOtpResponse = base.data as? GenerateOtpResponse
                    ?: return Resource.Error("Cannot parse to updateEkycDomain", "999")
                val generateOtpDomain =
                    GenerateOtpDataMapper.INSTANCE.toDomain(generateOtpResponse)
                Resource.Success(generateOtpDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun verifyOtpRetail(params: VerifyOtpEkycParam): Resource<VerifyOtpEkycDomain> {
        val verifyOTPEkycRequest = VerifyOtpEkycRequest(
            username = params.username,
            efastId = params.efastId,
            otp = params.otp,
        )
        val result = apiClient.makeApiCall(
            request = verifyOTPEkycRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<VerifyOtpEkycResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val verifyOtpEkycResponse = base.data as? VerifyOtpEkycResponse
                    ?: return Resource.Error("Cannot parse to updateEkycDomain", "999")
                val verifyOtpDomain =
                    GenerateOtpDataMapper.INSTANCE.toDomain(verifyOtpEkycResponse)
                Resource.Success(verifyOtpDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun updateEkyc(params: UpdateEkycParams): Resource<UpdateEkycDomain> {
        val updateEkycRequest = UpdateEkycRequest(
            username = params.username,
            authenType = params.authenType,
            softOtpTransId = params.softOtpTransId,
            token = params.token,
            tranType = params.tranType,
            transactions = params.transactions,
            serviceType = params.serviceType,
            roleId = params.roleId,
            transactionsTp = params.transactionsTp,
        )
        val result = apiClient.makeApiCall(
            request = updateEkycRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<UpdateEkycResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val updateEkycResponse = base.data as? UpdateEkycResponse
                    ?: return Resource.Error("Cannot parse to updateEkycDomain", "999")
                val verifyOtpDomain =
                    UpdateEkycResponseDataMapper.INSTANCE.toDomain(updateEkycResponse)
                Resource.Success(verifyOtpDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun getBiometricFace(params: GetBiometricFaceParams): Resource<GetBiometricFaceDomain> {
        val getBiometricFaceRequest = GetBiometricFaceRequest(
            biometricId = params.biometricId,
            username = params.username,
            roleId = params.roleId,
        )
        val result = apiClient.makeApiCall(
            request = getBiometricFaceRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<GetBiometricFaceResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val getBiometricFaceResponse = base.data as? GetBiometricFaceResponse
                    ?: return Resource.Error("Cannot parse to getBiometricFaceDomain", "999, ")
                val getBiometricFaceDomain =
                    GetBiometricFaceResponseDataMapper.INSTANCE.toDomain(getBiometricFaceResponse)
                Resource.Success(getBiometricFaceDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }
}