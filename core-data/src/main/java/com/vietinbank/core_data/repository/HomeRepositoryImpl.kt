package com.vietinbank.core_data.repository

import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.utils.GsonProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_data.encryption.PasswordEncryptor
import com.vietinbank.core_data.mapper.home.CountTransGroupResponseDataMapper
import com.vietinbank.core_data.mapper.home.HomeAccountListResponseDataMapper
import com.vietinbank.core_data.mapper.home.ListFunctionResponseDataMapper
import com.vietinbank.core_data.mapper.home.ListKeyResponseDataMapper
import com.vietinbank.core_data.mapper.home.ListLatestResponseDataMapper
import com.vietinbank.core_data.mapper.home.RegisterTouchIDResponseDataMapper
import com.vietinbank.core_data.models.request.RegisterTouchIDRequest
import com.vietinbank.core_data.models.request.home_request.CountTransGroupRequest
import com.vietinbank.core_data.models.request.home_request.HomeAccountListRequest
import com.vietinbank.core_data.models.request.home_request.ListFuncFavouriteRequest
import com.vietinbank.core_data.models.request.home_request.ListFunctionRequest
import com.vietinbank.core_data.models.request.home_request.ListKeyRequest
import com.vietinbank.core_data.models.request.home_request.ListLatestRequest
import com.vietinbank.core_data.models.request.home_request.UpdateListFuncFavouriteRequest
import com.vietinbank.core_data.models.response.CheckPasswordResponse
import com.vietinbank.core_data.models.response.RegisterTouchIDResponseData
import com.vietinbank.core_data.models.response.home_response.CountTransGroupResponse
import com.vietinbank.core_data.models.response.home_response.FunctionData
import com.vietinbank.core_data.models.response.home_response.HomeAccountListResponse
import com.vietinbank.core_data.models.response.home_response.ListFunctionResponse
import com.vietinbank.core_data.models.response.home_response.ListKeyResponse
import com.vietinbank.core_data.models.response.home_response.ListLatestResponse
import com.vietinbank.core_data.network.api.ApiService
import com.vietinbank.core_data.network.api.IApiClient
import com.vietinbank.core_data.network.di.Default
import com.vietinbank.core_domain.models.home.CountTransGroupDomain
import com.vietinbank.core_domain.models.home.CountTransGroupParams
import com.vietinbank.core_domain.models.home.HomeAccountListDomain
import com.vietinbank.core_domain.models.home.HomeAccountListParams
import com.vietinbank.core_domain.models.home.ListFunctionDomain
import com.vietinbank.core_domain.models.home.ListFunctionParams
import com.vietinbank.core_domain.models.home.ListKeyDomain
import com.vietinbank.core_domain.models.home.ListKeyParams
import com.vietinbank.core_domain.models.home.ListLatestDomain
import com.vietinbank.core_domain.models.home.ListLatestParams
import com.vietinbank.core_domain.models.home.RegisterTouchIDDomain
import com.vietinbank.core_domain.models.home.RegisterTouchIDParams
import com.vietinbank.core_domain.models.home.UpdateFunctionParams
import com.vietinbank.core_domain.repository.HomeRepository
import javax.inject.Inject

/**
 * Created by vandz on 5/3/25.
 */
class HomeRepositoryImpl @Inject constructor(
    @Default private val apiClient: IApiClient,
    private val apiService: ApiService,
    private val gsonProvider: GsonProvider,
    private val passwordEncryptor: PasswordEncryptor,
) : HomeRepository {
    override suspend fun registerTouchID(params: RegisterTouchIDParams): Resource<RegisterTouchIDDomain> {
        val passwordEncrypted = passwordEncryptor.getEncryptedPassword(params.password)

        val registerTouchIDRequest = RegisterTouchIDRequest(
            username = params.userName,
            password = passwordEncrypted,
        )

        val result = apiClient.makeApiCall(
            request = registerTouchIDRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<RegisterTouchIDResponseData>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val registerResponseData = base.data as? RegisterTouchIDResponseData
                    ?: return Resource.Error("Cannot parse to RegisterTouchIDResponseData", "999")

                // Map data model (RegisterTouchIDResponseData) -> domain
                val registerTouchIDDomain =
                    RegisterTouchIDResponseDataMapper.INSTANCE.toDomain(registerResponseData)

                Resource.Success(registerTouchIDDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun countTransGroup(params: CountTransGroupParams): Resource<CountTransGroupDomain> {
        val countTransGroupRequest = CountTransGroupRequest(
            accountNo = params.accountNo,
            type = params.type,
            username = params.username,
            role = params.role,
            roleId = params.roleId,
        )

        val result = apiClient.makeApiCall(
            request = countTransGroupRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<CountTransGroupResponse>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val responseData = base.data as? CountTransGroupResponse
                    ?: return Resource.Error("Cannot parse to CountTransGroupResponse", "999")
                val countTransGroupDomain = CountTransGroupResponseDataMapper.INSTANCE.toDomain(responseData)
                Resource.Success(countTransGroupDomain)
            }
            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun listLatest(params: ListLatestParams): Resource<ListLatestDomain> {
        val request = ListLatestRequest(
            accountNo = params.accountNo,
            username = params.username,
            role = params.role,
            roleId = params.roleId,
            pageSize = params.pageSize,
            pageNum = params.pageNum,
            status = params.status,
        )

        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<ListLatestResponse>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val responseData = base.data as? ListLatestResponse
                    ?: return Resource.Error("Cannot parse to ListLatestResponse", "999")
                val domain = ListLatestResponseDataMapper.INSTANCE.toDomain(responseData)
                Resource.Success(domain)
            }
            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun homeAccountList(params: HomeAccountListParams): Resource<HomeAccountListDomain> {
        val request = HomeAccountListRequest(
            username = params.username,
        )

        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<HomeAccountListResponse>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val responseData = base.data as? HomeAccountListResponse
                    ?: return Resource.Error("Cannot parse to HomeAccountListResponse", "999")
                val domain = HomeAccountListResponseDataMapper.INSTANCE.toDomain(responseData)
                Resource.Success(domain)
            }
            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun listKey(params: ListKeyParams): Resource<ListKeyDomain> {
        val request = ListKeyRequest(
            username = params.username ?: "",
            role = params.role ?: "",
            type = params.type ?: "",
        )

        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<ListKeyResponse>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val responseData = base.data as? ListKeyResponse
                    ?: return Resource.Error("Cannot parse to ListKeyResponse", "999")
                val domain = ListKeyResponseDataMapper.INSTANCE.toDomain(responseData)
                Resource.Success(domain)
            }
            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun listFunction(params: ListFunctionParams): Resource<ListFunctionDomain> {
        val request = ListFunctionRequest(
            username = params.username ?: "",
            role = params.role ?: "",
            roleId = params.roleId ?: "",
        )

        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<ListFunctionResponse>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val responseData = base.data as? ListFunctionResponse
                    ?: return Resource.Error("Cannot parse to ListFunctionResponse", "999")
                val domain = ListFunctionResponseDataMapper.INSTANCE.toDomain(responseData)
                Resource.Success(domain)
            }
            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun listFavoriteFunction(params: ListFunctionParams): Resource<ListFunctionDomain> {
        val request = ListFuncFavouriteRequest(
            username = params.username ?: "",
            role = params.role ?: "",
            roleId = params.roleId ?: "",
        )

        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<ListFunctionResponse>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val responseData = base.data as? ListFunctionResponse
                    ?: return Resource.Error("Cannot parse to ListFuncFavourite", "999")
                val domain = ListFunctionResponseDataMapper.INSTANCE.toDomain(responseData)
                Resource.Success(domain)
            }
            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun updateFavoriteFunction(params: UpdateFunctionParams): Resource<String> {
        val functions = params.functions?.map {
            FunctionData(
                functionName = it?.functionName ?: "",
                functionId = it?.functionId ?: "",
                groupId = it?.groupId ?: "",
                groupName = it?.groupName ?: "",
                orderNumber = it?.orderNumber ?: "",
                role = it?.role ?: "",
            )
        } ?: listOf()

        val request = UpdateListFuncFavouriteRequest(
            username = params.username ?: "",
            role = params.role ?: "",
            roleId = params.roleId ?: "",
            functions = functions,
        )

        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { requestBody -> apiService.callSITEnv(requestBody) },
            parseType = object : TypeToken<CheckPasswordResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                Resource.Success("domain")
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }
}