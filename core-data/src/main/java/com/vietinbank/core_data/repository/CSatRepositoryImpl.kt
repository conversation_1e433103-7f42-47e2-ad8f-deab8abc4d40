package com.vietinbank.core_data.repository

import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_data.mapper.checker.CSatRateResponseMapper
import com.vietinbank.core_data.models.request.CSatConfigRequest
import com.vietinbank.core_data.models.request.CSatRateRequest
import com.vietinbank.core_data.models.response.CSatConfigResponse
import com.vietinbank.core_data.models.response.CSatRateResponse
import com.vietinbank.core_data.network.api.ApiService
import com.vietinbank.core_data.network.api.IApiClient
import com.vietinbank.core_data.network.di.Default
import com.vietinbank.core_domain.models.csat.CSatConfigDomain
import com.vietinbank.core_domain.models.csat.CSatConfigParams
import com.vietinbank.core_domain.models.csat.CSatRateDomain
import com.vietinbank.core_domain.models.csat.CSatRateParams
import com.vietinbank.core_domain.repository.CSatRepository
import javax.inject.Inject

class CSatRepositoryImpl @Inject constructor(
    @Default private val apiClient: IApiClient,
    private val apiService: ApiService,
) : CSatRepository {
    override suspend fun rateCSAT(params: CSatRateParams): Resource<CSatRateDomain> {
        val request = CSatRateRequest(
            comment = params.comment,
            functionId = params.functionId,
            ratePoint = params.ratePoint,
            userName = params.userName,
        )
        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<CSatRateResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val response = base.data as? CSatRateResponse ?: return Resource.Error(
                    "Cannot parse to GetDownloadFileIDResponseData",
                    "999",
                )

                // Map data model -> domain
                val domain = CSatRateResponseMapper.INSTANCE.toDomain(response)
                Resource.Success(domain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun configCSAT(params: CSatConfigParams): Resource<CSatConfigDomain> {
        val request = CSatConfigRequest(
            functionId = params.functionId,
            userName = params.userName,
        )
        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<CSatConfigResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val response = base.data as? CSatConfigResponse ?: return Resource.Error(
                    "Cannot parse to GetDownloadFileIDResponseData",
                    "999",
                )

                // Map data model -> domain
                val domain = CSatRateResponseMapper.INSTANCE.toDomain(response)
                Resource.Success(domain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }
}
