package com.vietinbank.core_data.repository

import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_data.mapper.smart_ca.SmartCAMapper
import com.vietinbank.core_data.models.request.SmartCABranchRequest
import com.vietinbank.core_data.models.request.SmartCACertVNPTRequest
import com.vietinbank.core_data.models.request.SmartCADetailRequest
import com.vietinbank.core_data.models.request.SmartCAGetParamsRequest
import com.vietinbank.core_data.models.request.SmartCAListRequest
import com.vietinbank.core_data.models.request.SmartCARegisterRequest
import com.vietinbank.core_data.models.request.SmartCAUpdateRequest
import com.vietinbank.core_data.models.response.SmartCABranchResponse
import com.vietinbank.core_data.models.response.SmartCACertVNPTResponse
import com.vietinbank.core_data.models.response.SmartCADetailResponse
import com.vietinbank.core_data.models.response.SmartCAGetParamResponse
import com.vietinbank.core_data.models.response.SmartCAListResponse
import com.vietinbank.core_data.models.response.SmartCARegisterResponse
import com.vietinbank.core_data.models.response.SmartCAUpdateResponse
import com.vietinbank.core_data.network.api.ApiService
import com.vietinbank.core_data.network.api.IApiClient
import com.vietinbank.core_data.network.di.Default
import com.vietinbank.core_domain.models.smartCA.SmartCABranchDomain
import com.vietinbank.core_domain.models.smartCA.SmartCACertVNPTPDomain
import com.vietinbank.core_domain.models.smartCA.SmartCACertVNPTParams
import com.vietinbank.core_domain.models.smartCA.SmartCADetailDomain
import com.vietinbank.core_domain.models.smartCA.SmartCADetailParams
import com.vietinbank.core_domain.models.smartCA.SmartCAGetParamDomain
import com.vietinbank.core_domain.models.smartCA.SmartCAGetParams
import com.vietinbank.core_domain.models.smartCA.SmartCAListDomain
import com.vietinbank.core_domain.models.smartCA.SmartCAParams
import com.vietinbank.core_domain.models.smartCA.SmartCARegisterDomain
import com.vietinbank.core_domain.models.smartCA.SmartCARegisterParams
import com.vietinbank.core_domain.models.smartCA.SmartCAUpdateDomain
import com.vietinbank.core_domain.repository.SmartCARepository
import javax.inject.Inject

class SmartCARepositoryImpl @Inject constructor(
    @Default private val apiClient: IApiClient,
    private val apiService: ApiService,
) : SmartCARepository {
    override suspend fun getListCKS(params: SmartCAParams): Resource<SmartCAListDomain> {
        val request = SmartCAListRequest(params.username, params.role)
        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<SmartCAListResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val responseData = base.data as? SmartCAListResponse
                    ?: return Resource.Error("Cannot parse to SmartCAListResponse", "999")
                val domainData = SmartCAMapper.INSTANCE.toDomain(responseData)
                Resource.Success(domainData)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun getDetailCKS(params: SmartCADetailParams): Resource<SmartCADetailDomain> {
        val request = SmartCADetailRequest(
            certType = params.certType,
            mtId = params.mtId,
            role = params.role,
            username = params.username,
        )
        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<SmartCADetailResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val responseData = base.data as? SmartCADetailResponse
                    ?: return Resource.Error("Cannot parse to SmartCADetailResponse", "999")
                val domainData = SmartCAMapper.INSTANCE.toDomain(responseData)
                Resource.Success(domainData)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun registerCKS(params: SmartCARegisterParams): Resource<SmartCARegisterDomain> {
        val request = SmartCARegisterRequest(
            username = params.username ?: "",
            userRegister = params.userRegister ?: "",
            serviceType = params.serviceType ?: "",
            roleList = params.roleList ?: "",
            fullName = params.fullName ?: "",
            no = params.no ?: "",
            esignType = params.esignType ?: "",
            serialNo = params.serialNo ?: "",
            startDate = params.startDate ?: "",
            endDate = params.endDate ?: "",
            esignFrom = params.esignFrom ?: "",
            esignName = params.esignName ?: "",
            idEsign = params.idEsign ?: "",
            branch = params.branch ?: "",
            mtId = params.mtId ?: "",
//            confirm = params.confirm ?:"",
//            userConfirm = params.userConfirm ?:"",
//            taxNumber = params.taxNumber ?:"",
//            chanCert = params.chanCert ?:"",
            role = params.role ?: "",
            ou = params.ou ?: "",
            type = params.type ?: "",
        )
        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<SmartCARegisterResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val responseData = base.data as? SmartCARegisterResponse
                    ?: return Resource.Error("Cannot parse to SmartCARegisterResponse", "999")
                val domainData = SmartCAMapper.INSTANCE.toDomain(responseData)
                Resource.Success(domainData)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun getBranchCKS(params: SmartCAParams): Resource<SmartCABranchDomain> {
        val request = SmartCABranchRequest(params.username)
        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<SmartCABranchResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val responseData = base.data as? SmartCABranchResponse
                    ?: return Resource.Error("Cannot parse to SmartCABranchResponse", "999")
                val domainData = SmartCAMapper.INSTANCE.toDomain(responseData)
                Resource.Success(domainData)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun updateCKS(params: SmartCADetailParams): Resource<SmartCAUpdateDomain> {
        val request = SmartCAUpdateRequest(
            role = params.role ?: "",
            certType = params.certType ?: "",
            mtId = params.mtId ?: "",
            action = params.action ?: "",
            username = params.username ?: "",
        )
        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<SmartCAUpdateResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val responseData = base.data as? SmartCAUpdateResponse
                    ?: return Resource.Error("Cannot parse to SmartCAUpdateResponse", "999")
                val domainData = SmartCAMapper.INSTANCE.toDomain(responseData)
                Resource.Success(domainData)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun getParamsCKS(params: SmartCAGetParams): Resource<SmartCAGetParamDomain> {
        val request = SmartCAGetParamsRequest(
            role = params.role ?: "",
            type = params.type ?: "",
            username = params.username ?: "",
        )
        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<SmartCAGetParamResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val responseData = base.data as? SmartCAGetParamResponse
                    ?: return Resource.Error("Cannot parse to SmartCAGetParamResponse", "999")
                val domainData = SmartCAMapper.INSTANCE.toDomain(responseData)
                Resource.Success(domainData)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun getCertVNPT(params: SmartCACertVNPTParams): Resource<SmartCACertVNPTPDomain> {
        val request = SmartCACertVNPTRequest(
            role = params.role ?: "",
            no = params.no ?: "",
            username = params.username ?: "",
        )
        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<SmartCACertVNPTResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val responseData = base.data as? SmartCACertVNPTResponse
                    ?: return Resource.Error("Cannot parse to SmartCACertVNPTResponse", "999")
                val domainData = SmartCAMapper.INSTANCE.toDomain(responseData)
                Resource.Success(domainData)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }
}
