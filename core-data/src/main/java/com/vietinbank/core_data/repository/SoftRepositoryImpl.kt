package com.vietinbank.core_data.repository

import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.utils.GsonProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_data.encryption.PasswordEncryptor
import com.vietinbank.core_data.models.request.KeypassRequest
import com.vietinbank.core_data.models.request.VBlockSoftOtpRequest
import com.vietinbank.core_data.models.request.VGetSoftOtpActivationCodeRequest
import com.vietinbank.core_data.models.response.SoftResponse
import com.vietinbank.core_data.network.api.ApiService
import com.vietinbank.core_data.network.api.IApiClient
import com.vietinbank.core_data.network.di.Default
import com.vietinbank.core_domain.models.soft.KeypassParams
import com.vietinbank.core_domain.models.soft.VBlockSoftOtpParams
import com.vietinbank.core_domain.models.soft.VGetSoftOtpActivationCodeParams
import com.vietinbank.core_domain.models.soft.VSoftResponseParams
import com.vietinbank.core_domain.repository.SoftRepository
import javax.inject.Inject

class SoftRepositoryImpl @Inject constructor(
    @Default private val apiClient: IApiClient,
    private val apiService: ApiService,
    private val gsonProvider: GsonProvider,
    private val passwordEncryptor: PasswordEncryptor,
) : SoftRepository {
    override suspend fun getActivationCode(params: VGetSoftOtpActivationCodeParams): Resource<VSoftResponseParams> {
        val request = VGetSoftOtpActivationCodeRequest(
            params.sendType,
            params.username,
        )

        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<SoftResponse>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val responseData = base.data as? SoftResponse
                Resource.Success(
                    VSoftResponseParams(
                        responseData?.status?.code,
                        responseData?.status?.message,
                    ),
                )
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun blockToken(params: VBlockSoftOtpParams): Resource<VSoftResponseParams> {
        val request = VBlockSoftOtpRequest(params.tokenIds)

        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<SoftResponse>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val responseData = base.data as? SoftResponse
                Resource.Success(
                    VSoftResponseParams(
                        responseData?.status?.code,
                        responseData?.status?.message,
                    ),
                )
            }
            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun forgotKeypassPin(params: KeypassParams): Resource<VSoftResponseParams> {
        val request = KeypassRequest(
            puk = params.puk,
            sendType = params.sendType,
            username = params.username,
        )

        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<SoftResponse>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val responseData = base.data as? SoftResponse
                Resource.Success(
                    VSoftResponseParams(
                        responseData?.status?.code,
                        responseData?.status?.message,
                    ),
                )
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }
}
