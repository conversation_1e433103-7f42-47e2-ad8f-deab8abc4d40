package com.vietinbank.core_data.repository.ott

import android.content.Context
import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_data.database.ott.dao.DeviceTokenDao
import com.vietinbank.core_data.database.ott.dao.OttMessageDao
import com.vietinbank.core_data.mapper.ott.OttGetAllMessageMapper
import com.vietinbank.core_data.mapper.ott.OttRegisterDeviceMapper
import com.vietinbank.core_data.models.request.ott.OttGetAllMessageRequest
import com.vietinbank.core_data.models.request.ott.OttRegisterDeviceRequest
import com.vietinbank.core_data.models.response.ott.OttGetAllMessageResponse
import com.vietinbank.core_data.models.response.ott.OttRegisterResponse
import com.vietinbank.core_data.models.response.ott.OttRegisterSocketResponse
import com.vietinbank.core_data.network.api.IApiClient
import com.vietinbank.core_data.network.api.ott.OttApiService
import com.vietinbank.core_data.network.di.Default
import com.vietinbank.core_domain.models.ott.OttGetAllMessageDomain
import com.vietinbank.core_domain.models.ott.OttGetAllMessageParams
import com.vietinbank.core_domain.models.ott.OttRegisterDeviceDomain
import com.vietinbank.core_domain.models.ott.OttRegisterDeviceParams
import com.vietinbank.core_domain.repository.OttRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Created by vandz on 15/4/25.
 */
@Singleton
class OttRepositoryImpl @Inject constructor(
    private val apiService: OttApiService,
    private val deviceTokenDao: DeviceTokenDao,
    private val messageDao: OttMessageDao,
    @ApplicationContext private val context: Context,
    @Default private val apiClient: IApiClient,
) : OttRepository {

    companion object {
        private const val TAG = "OttRepository"
    }

    override suspend fun registerToken(params: OttRegisterDeviceParams): Resource<OttRegisterDeviceDomain> {
        // Tạo request object
        val request = OttRegisterDeviceRequest(
            channelType = params.channelType,
            cif = params.cif,
            phone = params.phone,
            username = params.username,
            tokenType = params.tokenType,
            fcmToken = params.fcmToken,
            socketId = params.socketId,
        ).apply {
            channel = "efast"
        }
        return try {
            // Gọi API thông qua apiClient
            val result = apiClient.makeApiCall(
                request = request,
                apiCall = { reqBody -> apiService.callSecureApi(reqBody) },
                parseType = object : TypeToken<OttRegisterResponse>() {},
            )

            when (result) {
                is Resource.Success -> {
                    val base = result.data
                    val registerResponseData = base.data as? OttRegisterResponse
                        ?: return Resource.Error("Cannot parse to OttRegisterResponse", "999")

                    // Chuyển đổi từ DTO sang domain model
                    val domain = OttRegisterDeviceMapper.INSTANCE.toDomain(registerResponseData)
                    Resource.Success(domain)
                }

                is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
            }
        } catch (e: Exception) {
            Resource.Error(
                "Error registering token: ${e.message}",
                "999",
                AppException.UnknownException(e.message.toString()),
            )
        }
    }

    override suspend fun registerSocket(params: OttRegisterDeviceParams): Resource<OttRegisterDeviceDomain> {
        val request = OttRegisterDeviceRequest(
            channelType = params.channelType,
            cif = params.cif,
            phone = params.phone,
            username = params.username,
            tokenType = params.tokenType,
            fcmToken = params.fcmToken,
            socketId = params.socketId,
        ).apply {
            channel = "efast"
        }

        return try {
            // Gọi API thông qua apiClient
            val result = apiClient.makeApiCall(
                request = request,
                apiCall = { reqBody -> apiService.callSecureApi(reqBody) },
                parseType = object : TypeToken<OttRegisterSocketResponse>() {},
            )

            when (result) {
                is Resource.Success -> {
                    val base = result.data
                    val registerSocketResponseData = base.data as? OttRegisterResponse
                        ?: return Resource.Error("Cannot parse to OttRegisterResponse", "999")

                    // Chuyển đổi từ DTO sang domain model
                    val domain =
                        OttRegisterDeviceMapper.INSTANCE.toDomain(registerSocketResponseData)
                    Resource.Success(domain)
                }

                is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
            }
        } catch (e: Exception) {
            Resource.Error(
                "Error registering token: ${e.message}",
                "999",
                AppException.UnknownException(e.message.toString()),
            )
        }
    }

    override suspend fun getAllMessage(params: OttGetAllMessageParams): Resource<OttGetAllMessageDomain> {
        return try {
            val request = OttGetAllMessageRequest(
                channelType = params.channelType,
                cif = params.cif,
                phone = params.phone,
                username = params.username,
                page = params.page,
                size = params.size,
                channelId = params.channelId,
            ).apply {
                channel = "efast"
            }
            val response = apiClient.makeApiCall(
                request = request,
                apiCall = { reqBody -> apiService.callSecureApi(reqBody) },
                parseType = object : TypeToken<OttGetAllMessageResponse>() {},
            )

            when (response) {
                is Resource.Success -> {
                    val base = response.data
                    val getAllMessageResponseData = base.data as? OttGetAllMessageResponse
                        ?: return Resource.Error("Cannot parse to OttRegisterResponse", "999")

                    // Chuyển đổi từ DTO sang domain model
                    val domain = OttGetAllMessageMapper.INSTANCE.toDomain(getAllMessageResponseData)
                    Resource.Success(domain)
                }

                is Resource.Error -> Resource.Error(response.message, response.code, response.exception)
            }
        } catch (e: Exception) {
            Resource.Error(
                "Error get all message: ${e.message}",
                "999",
                AppException.UnknownException(e.message.toString()),
            )
        }
    }
}
