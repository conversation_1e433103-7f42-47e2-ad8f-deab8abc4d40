package com.vietinbank.core_data.repository.ott

import android.content.Context
import android.content.SharedPreferences
import com.vietinbank.core_domain.repository.ConfigRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Created by vandz on 16/4/25.
 */
@Singleton
class ConfigRepositoryImpl @Inject constructor(
    @ApplicationContext private val context: Context,
) : ConfigRepository {
    // SharedPreferences khởi tạo lazy để tối ưu hiệu suất
    private val sharedPreferences: SharedPreferences by lazy {
        context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    }

    override fun getSocketBaseUrl(): String {
        return sharedPreferences.getString(KEY_SOCKET_BASE_URL, DEFAULT_SOCKET_BASE_URL)
            ?: DEFAULT_SOCKET_BASE_URL
    }

    override fun getSocketPath(): String {
        return sharedPreferences.getString(KEY_SOCKET_PATH, DEFAULT_SOCKET_PATH)
            ?: DEFAULT_SOCKET_PATH
    }

    override fun updateConfigFromServer(socketBaseUrl: String?, socketPath: String?) {
        val editor = sharedPreferences.edit()

        socketBaseUrl?.let {
            editor.putString(KEY_SOCKET_BASE_URL, it)
        }

        socketPath?.let {
            editor.putString(KEY_SOCKET_PATH, it)
        }

        editor.apply()
    }

    companion object {
        private const val PREF_NAME = "vietinbank_config"
        private const val KEY_SOCKET_BASE_URL = "socket_base_url"
        private const val KEY_SOCKET_PATH = "socket_path"

        // Giá trị mặc định dựa trên thông tin đã cung cấp
        private const val DEFAULT_SOCKET_BASE_URL = "https://webdemo.vietinbank.vn"
        private const val DEFAULT_SOCKET_PATH = "/socket-decode/socket.io"
    }
}
