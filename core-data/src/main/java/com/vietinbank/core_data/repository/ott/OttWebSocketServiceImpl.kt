package com.vietinbank.core_data.websocket

import com.vietinbank.core_domain.models.ott.WebSocketMessage
import com.vietinbank.core_domain.ott.IOttWebSocketListener
import com.vietinbank.core_domain.ott.IOttWebSocketService
import com.vietinbank.core_domain.ott.ISocketClient
import java.util.concurrent.CopyOnWriteArrayList
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Triển khai IOttWebSocketService sử dụng ISocketClient từ domain layer
 */
@Singleton
class OttWebSocketServiceImpl @Inject constructor(
    private val socketClient: ISocketClient,
) : IOttWebSocketService, ISocketClient.ISocketCallback {

    private val listeners = CopyOnWriteArrayList<IOttWebSocketListener>()
    private var cif: String? = null
    private var phone: String? = null

    init {
        socketClient.registerCallback(this)
    }

    override fun connect(
        baseUrl: String,
        path: String,
        token: String,
        cif: String,
        phone: String,
    ): Boolean {
        this.cif = cif
        this.phone = phone

        socketClient.connect(baseUrl, path, token, cif, phone)
        return true
    }

    override fun disconnect() {
        socketClient.disconnect()
    }

    override fun isConnected(): Boolean {
        return socketClient.isConnected()
    }

    override fun updateMessageStatus(messageId: String, status: Int) {
        if (!socketClient.isConnected()) {
            return
        }

        // Tạo tin nhắn cập nhật trạng thái dùng domain model
        val message = WebSocketMessage(
            type = WebSocketMessage.TYPE_STATUS_UPDATE,
            messageId = messageId,
            content = status.toString(),
            data = mapOf(
                "status" to status.toString(),
                "cif" to (cif ?: ""),
                "phone" to (phone ?: ""),
            ),
        )

        // Gửi tin nhắn qua interface domain
        socketClient.sendMessage(message, "status_update")
    }

    override fun registerListener(listener: IOttWebSocketListener) {
        if (!listeners.contains(listener)) {
            listeners.add(listener)
        }
    }

    override fun unregisterListener(listener: IOttWebSocketListener) {
        listeners.remove(listener)
    }

    // --- Triển khai ISocketClient.ISocketCallback ---//

    override fun onConnected() {
        listeners.forEach { it.onConnected() }
    }

    override fun onDisconnected() {
        listeners.forEach { it.onDisconnected() }
    }

    override fun onMessage(message: WebSocketMessage) {
        when (message.type) {
            WebSocketMessage.TYPE_TEXT,
            WebSocketMessage.TYPE_NOTIFICATION,
            WebSocketMessage.TYPE_TRANSACTION,
            -> {
                listeners.forEach {
                    it.onMessageReceived(message.messageId, message.content, message.data)
                }
            }

            WebSocketMessage.TYPE_STATUS_UPDATE -> {
                try {
                    val status = message.content.toInt()
                    listeners.forEach { it.onStatusUpdate(message.messageId, status) }
                } catch (e: NumberFormatException) {
                    // Xử lý lỗi
                }
            }
        }
    }

    override fun onError(error: Throwable) {
        listeners.forEach { it.onError(error) }
    }
}
