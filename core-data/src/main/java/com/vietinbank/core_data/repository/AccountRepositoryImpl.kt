package com.vietinbank.core_data.repository

import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_data.mapper.account.AccountResponseMapper
import com.vietinbank.core_data.models.request.AccountDetailRequest
import com.vietinbank.core_data.models.request.AccountFileSavingRequest
import com.vietinbank.core_data.models.request.AccountHistoryDetailRequest
import com.vietinbank.core_data.models.request.AccountHistoryListRequest
import com.vietinbank.core_data.models.request.AccountImagesQRRequest
import com.vietinbank.core_data.models.request.AccountListNewRequest
import com.vietinbank.core_data.models.request.AccountSaveQRRequest
import com.vietinbank.core_data.models.request.AliasAccountListRequest
import com.vietinbank.core_data.models.request.AliasModel
import com.vietinbank.core_data.models.request.HostRefNoModel
import com.vietinbank.core_data.models.request.TransactionStatusRequest
import com.vietinbank.core_data.models.response.AccountDetailResponse
import com.vietinbank.core_data.models.response.AccountFileSavingResponse
import com.vietinbank.core_data.models.response.AccountHistoryDetailResponse
import com.vietinbank.core_data.models.response.AccountHistoryListResponse
import com.vietinbank.core_data.models.response.AccountImageQRResponse
import com.vietinbank.core_data.models.response.AccountListNewResponse
import com.vietinbank.core_data.models.response.AccountSaveQRResponse
import com.vietinbank.core_data.models.response.AliasAccountResponse
import com.vietinbank.core_data.models.response.TransactionStatusResponse
import com.vietinbank.core_data.network.api.ApiService
import com.vietinbank.core_data.network.api.IApiClient
import com.vietinbank.core_data.network.di.Default
import com.vietinbank.core_domain.models.account.AccountDetailDomain
import com.vietinbank.core_domain.models.account.AccountDetailParams
import com.vietinbank.core_domain.models.account.AccountFileSavingDomain
import com.vietinbank.core_domain.models.account.AccountFileSavingParams
import com.vietinbank.core_domain.models.account.AccountHistoryDetailDomain
import com.vietinbank.core_domain.models.account.AccountHistoryDetailParams
import com.vietinbank.core_domain.models.account.AccountHistoryListDomain
import com.vietinbank.core_domain.models.account.AccountHistoryListParams
import com.vietinbank.core_domain.models.account.AccountImageQRDomain
import com.vietinbank.core_domain.models.account.AccountImagesQRParams
import com.vietinbank.core_domain.models.account.AccountListNewDomain
import com.vietinbank.core_domain.models.account.AccountLstParams
import com.vietinbank.core_domain.models.account.AccountSaveQRDomain
import com.vietinbank.core_domain.models.account.AccountSaveQRParams
import com.vietinbank.core_domain.models.account.AliasAccountParams
import com.vietinbank.core_domain.models.account.SetAliasAccountDomain
import com.vietinbank.core_domain.models.account.TransactionStatusDomain
import com.vietinbank.core_domain.models.account.TransactionStatusParams
import com.vietinbank.core_domain.repository.AccountRepository
import javax.inject.Inject

class AccountRepositoryImpl @Inject constructor(
    @Default private val apiClient: IApiClient,
    private val apiService: ApiService,
) : AccountRepository {
    override suspend fun getAccountList(params: AccountLstParams): Resource<AccountListNewDomain> {
        val accListRequest = AccountListNewRequest(
            serviceAcctType = params.serviceAcctType ?: "",
            username = params.username ?: "",
            isForceReq = params.isForceReq ?: "0",
        )
        val result = apiClient.makeApiCall(
            request = accListRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<AccountListNewResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val acclistResponseData = base.data as? AccountListNewResponse
                    ?: return Resource.Error("Cannot parse to AccountListNewResponse", "999")
                val acclistDomain = AccountResponseMapper.INSTANCE.toDomain(acclistResponseData)
                Resource.Success(acclistDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun getAccountDetail(params: AccountDetailParams): Resource<AccountDetailDomain> {
        val request = AccountDetailRequest(
            accountNo = params.accountNo,
            accountType = params.accountType,
            currency = params.currency,
            username = params.username,
        )
        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<AccountDetailResponse>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val response = base.data as? AccountDetailResponse
                    ?: return Resource.Error("Cannot parse to AccountDetailResponse", "999")
                // Map data model -> domain
                val domain = AccountResponseMapper.INSTANCE.toDomain(response)
                Resource.Success(domain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun getTransactionDetail(params: AccountHistoryDetailParams): Resource<AccountHistoryDetailDomain> {
        val request = AccountHistoryDetailRequest(
            accountNo = params.accountNo,
            dorc = params.dorc,
            pmtType = params.pmtType,
            refNo = params.refNo,
            transactionId = params.transactionId,
            trxChannelId = params.trxChannelId,
            username = params.username,
        )
        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<AccountHistoryDetailResponse>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val response = base.data as? AccountHistoryDetailResponse
                    ?: return Resource.Error("Cannot parse to AccountHistoryDetailResponse", "999")
                // Map data model -> domain
                val domain = AccountResponseMapper.INSTANCE.toDomain(response)
                Resource.Success(domain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun getTransactionList(params: AccountHistoryListParams): Resource<AccountHistoryListDomain> {
        val request = AccountHistoryListRequest(
            accountNo = params.accountNo,
            accountType = params.accountType,
            fromDate = params.fromDate,
            toDate = params.toDate,
            queryType = params.queryType,
            pageIndex = params.pageIndex,
            pageSize = params.pageSize,
            dorcC = params.dorcC,
            dorcD = params.dorcD,
            fromAmount = params.fromAmount,
            searchKey = params.searchKey,
            toAmount = params.toAmount,
            username = params.userName,
        )
        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<AccountHistoryListResponse>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val response = base.data as? AccountHistoryListResponse
                    ?: return Resource.Error("Cannot parse to AccountHistoryListResponse", "999")
                // Map data model -> domain
                val domain = AccountResponseMapper.INSTANCE.toDomain(response)
                Resource.Success(domain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun setAliasAccount(params: AliasAccountParams): Resource<SetAliasAccountDomain> {
        val request = AliasAccountListRequest(
            account = listOf(
                AliasModel(
                    accountNo = params.accountNo,
                    aliasName = params.aliasName,
                    aliasNameGroups = params.aliasNameGroups,
                    cifSubsidiary = params.cifSubsidiary,
                ),
            ),
            username = params.username,
        )
        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<AliasAccountResponse>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val response = base.data as? AliasAccountResponse
                    ?: return Resource.Error("Cannot parse to AliasAccountResponse", "999")
                // Map data model -> domain
                val domain = AccountResponseMapper.INSTANCE.toDomain(response)
                Resource.Success(domain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun setQRAccount(params: AccountSaveQRParams): Resource<AccountSaveQRDomain> {
        val request = AccountSaveQRRequest(
            accountName = params.accountName,
            accountNo = params.accountNo,
//            addInfo1 = params.addInfo1,
//            addInfo2 = params.addInfo2,
//            addInfo3 = params.addInfo3,
//            addInfo4 = params.addInfo4,
//            addInfo5 = params.addInfo5,
            aliasName = params.aliasName,
            amount = params.amount,
            branchId = params.branchId,
            branchName = params.branchName,
            currency = params.currency,
//            expireTime = params.expireTime,
            notes = params.notes,
            qrData = params.qrData,
            qrType = params.qrType,
            username = params.username,
        )
        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<AccountSaveQRResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val response = base.data as? AccountSaveQRResponse
                    ?: return Resource.Error("Cannot parse to AccountSaveQRResponse", "999")
                // Map data model -> domain
                val domain = AccountResponseMapper.INSTANCE.toDomain(response)
                Resource.Success(domain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun getImageQRAccount(params: AccountImagesQRParams): Resource<AccountImageQRDomain> {
        val request = AccountImagesQRRequest(
            params.type,
            params.username,
        )
        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<AccountImageQRResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val response = base.data as? AccountImageQRResponse
                    ?: return Resource.Error("Cannot parse to AccountImageQRResponse", "999")
                // Map data model -> domain
                val domain = AccountResponseMapper.INSTANCE.toDomain(response)
                Resource.Success(domain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun getTransactionStatus(params: TransactionStatusParams): Resource<TransactionStatusDomain> {
        val request = TransactionStatusRequest(
            HostRefNoModel(params.hostrefno),
            params.username,
        )
        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<TransactionStatusResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val response = base.data as? TransactionStatusResponse
                    ?: return Resource.Error("Cannot parse to TransactionStatusResponse", "999")
                // Map data model -> domain
                val domain = AccountResponseMapper.INSTANCE.toDomain(response)
                Resource.Success(domain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun exportFile(params: AccountFileSavingParams): Resource<AccountFileSavingDomain> {
        val request = AccountFileSavingRequest(
            accountNo = params.accountNo,
            username = params.username,
            currency = params.currency,
        )
        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<AccountFileSavingResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val response = base.data as? AccountFileSavingResponse
                    ?: return Resource.Error("Cannot parse to AccountFileSavingResponse", "999")
                val domain = AccountResponseMapper.INSTANCE.toDomain(response)
                Resource.Success(domain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }
}
