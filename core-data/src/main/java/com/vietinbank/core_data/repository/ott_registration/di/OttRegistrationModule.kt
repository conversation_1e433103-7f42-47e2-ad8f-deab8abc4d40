package com.vietinbank.core_data.repository.ott_registration.di

import com.vietinbank.core_common.cache.CacheFactory
import com.vietinbank.core_data.repository.OttRegistrationRepositoryImpl
import com.vietinbank.core_domain.repository.cache.IOttRegistrationRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Dagger module for providing OTT registration repository
 */
@Module
@InstallIn(SingletonComponent::class)
object OttRegistrationModule {

    @Provides
    @Singleton
    fun provideOttRegistrationRepository(
        cacheFactory: CacheFactory,
    ): IOttRegistrationRepository {
        return OttRegistrationRepositoryImpl(cacheFactory)
    }
}