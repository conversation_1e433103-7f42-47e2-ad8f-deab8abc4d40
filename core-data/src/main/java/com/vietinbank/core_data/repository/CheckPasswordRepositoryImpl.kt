package com.vietinbank.core_data.repository

import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_data.encryption.PasswordEncryptor
import com.vietinbank.core_data.mapper.home.CheckPasswordResponseDataMapper
import com.vietinbank.core_data.models.request.CheckPasswordRequest
import com.vietinbank.core_data.models.response.CheckPasswordResponse
import com.vietinbank.core_data.network.api.ApiService
import com.vietinbank.core_data.network.api.IApiClient
import com.vietinbank.core_data.network.di.Default
import com.vietinbank.core_domain.models.home.CheckPasswordDomain
import com.vietinbank.core_domain.models.home.CheckPasswordParams
import com.vietinbank.core_domain.repository.CheckPasswordRepository
import javax.inject.Inject

class CheckPasswordRepositoryImpl @Inject constructor(
    @Default private val apiClient: IApiClient,
    private val apiService: ApiService,
    private val passwordEncryptor: PasswordEncryptor,
) : CheckPasswordRepository {

    override suspend fun checkPassword(params: CheckPasswordParams): Resource<CheckPasswordDomain> {
        val encryptedPassword = passwordEncryptor.getEncryptedPassword(params.password)

        val request = CheckPasswordRequest(
            username = params.userName,
            password = encryptedPassword,
        )

        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { requestBody -> apiService.callSITEnv(requestBody) },
            parseType = object : TypeToken<CheckPasswordResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val response = base.data as? CheckPasswordResponse
                    ?: return Resource.Error("Cannot parse to CheckPasswordResponse", "999")

                // Map data model (LoginResponseData) -> domain
                val domain = CheckPasswordResponseDataMapper.INSTANCE.toDomain(response)

                Resource.Success(domain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }
}