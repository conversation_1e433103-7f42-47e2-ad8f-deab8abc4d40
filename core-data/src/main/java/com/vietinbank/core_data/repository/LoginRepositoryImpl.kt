package com.vietinbank.core_data.repository

import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.models.ForceUpdateDomain
import com.vietinbank.core_common.utils.GsonProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_data.encryption.PasswordEncryptor
import com.vietinbank.core_data.mapper.login.AccountListResponseDataMapper
import com.vietinbank.core_data.mapper.login.AccountLockResponseDataMapper
import com.vietinbank.core_data.mapper.login.ChangePasswordResponseDataMapper
import com.vietinbank.core_data.mapper.login.ForceUpdateResponseDataMapper
import com.vietinbank.core_data.mapper.login.GenOTTPResponseDataMapper
import com.vietinbank.core_data.mapper.login.LoginResponseDataMapper
import com.vietinbank.core_data.mapper.login.VerifyOTPResponseDataMapper
import com.vietinbank.core_data.models.request.AccountListRequest
import com.vietinbank.core_data.models.request.ChangePasswordRequest
import com.vietinbank.core_data.models.request.ForceUpdateRequest
import com.vietinbank.core_data.models.request.GenOTPRequest
import com.vietinbank.core_data.models.request.LoginRequest
import com.vietinbank.core_data.models.request.VerifyOTPRequest
import com.vietinbank.core_data.models.request.account_lock.AccountLockFaceRequest
import com.vietinbank.core_data.models.request.account_lock.AccountLockOTPRequest
import com.vietinbank.core_data.models.request.account_lock.AccountLockRequest
import com.vietinbank.core_data.models.response.AccountListResponseData
import com.vietinbank.core_data.models.response.ChangePasswordResponseData
import com.vietinbank.core_data.models.response.ForceUpdateResponseData
import com.vietinbank.core_data.models.response.GenOTPResponseData
import com.vietinbank.core_data.models.response.LoginResponseData
import com.vietinbank.core_data.models.response.VerifyOTPResponseData
import com.vietinbank.core_data.models.response.account_lock.AccountLockOTPResponse
import com.vietinbank.core_data.models.response.account_lock.AccountLockResponse
import com.vietinbank.core_data.network.api.ApiService
import com.vietinbank.core_data.network.api.IApiClient
import com.vietinbank.core_data.network.di.Default
import com.vietinbank.core_domain.models.login.AccountListDomain
import com.vietinbank.core_domain.models.login.AccountListParams
import com.vietinbank.core_domain.models.login.AccountLockDomain
import com.vietinbank.core_domain.models.login.AccountLockFaceParams
import com.vietinbank.core_domain.models.login.AccountLockOTPDomain
import com.vietinbank.core_domain.models.login.AccountLockOTPParams
import com.vietinbank.core_domain.models.login.AccountLockParams
import com.vietinbank.core_domain.models.login.ChangePasswordDomain
import com.vietinbank.core_domain.models.login.ChangePasswordParams
import com.vietinbank.core_domain.models.login.ForceUpdateParams
import com.vietinbank.core_domain.models.login.GenOTPDomain
import com.vietinbank.core_domain.models.login.GenOTPParams
import com.vietinbank.core_domain.models.login.LoginDomain
import com.vietinbank.core_domain.models.login.LoginParams
import com.vietinbank.core_domain.models.login.VerifyOTPDomain
import com.vietinbank.core_domain.models.login.VerifyOTPParams
import com.vietinbank.core_domain.repository.LoginRepository
import javax.inject.Inject

/**
 * Created by vandz on 18/12/24.
 *
 * Implementation of the LoginRepository interface that handles user authentication.
 * This repository implements the Clean Architecture pattern, separating domain and data layers.
 *
 * Features:
 * - Converts between domain and data models
 * - Handles API communication through ApiClient
 * - Provides error handling and resource states
 * - Uses dependency injection for better testability
 */
class LoginRepositoryImpl @Inject constructor(
    @Default private val apiClient: IApiClient,
    private val apiService: ApiService,
    private val gsonProvider: GsonProvider,
    private val passwordEncryptor: PasswordEncryptor,
) : LoginRepository {

    /**
     * Performs user login by making an API call with the provided credentials.
     *
     * @param params The login parameters containing username, password, and CIF number
     * @return Resource<LoginDomain> containing either the successful login response or error details
     * @throws AppException if there's an error during the login process
     */
    override suspend fun login(params: LoginParams): Resource<LoginDomain> {
        // 1) Mã hoá password
        val encryptedPassword = passwordEncryptor.getEncryptedPassword(params.password)

        // 2) Tạo request data class
        val loginRequest = LoginRequest(
            username = params.username,
            password = encryptedPassword, // Mật khẩu đã mã hóa
            authenType = params.authenType,
            regTouchId = params.regTouchId,
            touchId = params.touchId,
        ).apply {
            cifno = params.cifno
        }

        // 3) Call generic makeApiCall
        val result = apiClient.makeApiCall(
            request = loginRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<LoginResponseData>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val loginResponseData = base.data as? LoginResponseData
                    ?: return Resource.Error("Cannot parse to LoginResponseData", "999")

                // Map data model (LoginResponseData) -> domain
                val loginDomain = LoginResponseDataMapper.INSTANCE.toDomain(loginResponseData)

                Resource.Success(loginDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun changePassword(params: ChangePasswordParams): Resource<ChangePasswordDomain> {
        val oldPassEncrypted = passwordEncryptor.getEncryptedPassword(params.oldPass)
        val newPassEncrypted = passwordEncryptor.getEncryptedPassword(params.newPass)

        // 2) Tạo request data class
        val changePasswordRequest = ChangePasswordRequest(
            username = params.username,
            oldPass = oldPassEncrypted,
            newPass = newPassEncrypted,
            status = params.status,
        )

        // 3) Call generic makeApiCall
        val result = apiClient.makeApiCall(
            request = changePasswordRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<ChangePasswordResponseData>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val changePasswordResponseData = base.data as? ChangePasswordResponseData
                    ?: return Resource.Error("Cannot parse to ChangePasswordResponseData", "999")

                // Map data model (LoginResponseData) -> domain
                val changePasswordDomain =
                    ChangePasswordResponseDataMapper.INSTANCE.toDomain(changePasswordResponseData)

                Resource.Success(changePasswordDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun forceUpdate(params: ForceUpdateParams): Resource<ForceUpdateDomain> {
        // 2) Tạo request data class
        val forceUpdateRequest = ForceUpdateRequest(
            roleId = params.roleId,
//            username = params.username,
        )

        // 3) Call generic makeApiCall
        val result = apiClient.makeApiCall(
            request = forceUpdateRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<ForceUpdateResponseData>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val forceUpdateResponseData = base.data as? ForceUpdateResponseData
                    ?: return Resource.Error("Cannot parse to ForceUpdateResponseData", "999")

                // Map data model (LoginResponseData) -> domain
                val forceUpdatedDomain =
                    ForceUpdateResponseDataMapper.INSTANCE.toDomain(forceUpdateResponseData)

                Resource.Success(forceUpdatedDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun accountList(params: AccountListParams): Resource<AccountListDomain> {
        val accListRequest = AccountListRequest(
            accountType = params.accountType,
            currencySort = params.currencySort,
            username = params.username,
            serviceType = params.serviceType,
        )
        val result = apiClient.makeApiCall(
            request = accListRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<AccountListResponseData>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val acclistResponseData = base.data as? AccountListResponseData
                    ?: return Resource.Error("Cannot parse to AccountListResponseData", "999")
                val acclistDomain =
                    AccountListResponseDataMapper.INSTANCE.toDomain(acclistResponseData)
                Resource.Success(acclistDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun genOTP(params: GenOTPParams): Resource<GenOTPDomain> {
        val genOTPRequest = GenOTPRequest(
            type = params.type,
            username = params.username,
            addition1 = params.addition1,
        ).apply {
            cifno = params.cifno.toString()
        }
        val result = apiClient.makeApiCall(
            request = genOTPRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<GenOTPResponseData>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val genOTPResponseData = base.data as? GenOTPResponseData
                    ?: return Resource.Error("Cannot parse to GenOTPResponseData", "999")
                val genOTPDomain =
                    GenOTTPResponseDataMapper.INSTANCE.toDomain(genOTPResponseData)
                Resource.Success(genOTPDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun verifyOTP(params: VerifyOTPParams): Resource<VerifyOTPDomain> {
        val verifyOTPRequest = VerifyOTPRequest(
            type = params.type,
            username = params.username,
            addition1 = params.addition1,
            efastId = params.efastId,
            otp = params.otp,
        )
        val result = apiClient.makeApiCall(
            request = verifyOTPRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<VerifyOTPResponseData>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val verifyOTPResponseData = base.data as? VerifyOTPResponseData
                    ?: return Resource.Error("Cannot parse to VerifyOTPResponseData", "999")
                val verifyOTPDomain =
                    VerifyOTPResponseDataMapper.INSTANCE.toDomain(verifyOTPResponseData)
                Resource.Success(verifyOTPDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun checkInformation(params: AccountLockParams): Resource<AccountLockDomain> {
        val request = AccountLockRequest(
            username = params.username,
            accountPayment = params.accountPayment,
            idCard = params.idCard,
            typeReset = params.type,
        ).apply {
            cifno = params.cifNo ?: ""
        }
        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<AccountLockResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val response = base.data as? AccountLockResponse
                    ?: return Resource.Error("Cannot parse to AccountLockResponse", "999")
                val domain = AccountLockResponseDataMapper.INSTANCE.toDomain(response)
                Resource.Success(domain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun sendOTPLockAccount(params: AccountLockOTPParams): Resource<AccountLockOTPDomain> {
        val request = AccountLockOTPRequest(
            username = params.username,
            type = params.type,
            accountPayment = params.accountPayment,
            idCard = params.idCard,
            typeReset = params.typeReset,
            approver = params.approver,
            roleChecker = params.roleChecker,
            token = params.token,
        ).apply {
            cifno = params.cifNo ?: ""
        }
        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<AccountLockOTPResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val response = base.data as? AccountLockOTPResponse
                    ?: return Resource.Error("Cannot parse to AccountLockOTPResponse", "999")
                val domain = AccountLockResponseDataMapper.INSTANCE.toDomain(response)
                Resource.Success(domain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun compareFaceLockAccount(params: AccountLockFaceParams): Resource<AccountLockOTPDomain> {
        val request = AccountLockFaceRequest(
            username = params.username,
            image = params.image,
        ).apply {
            cifno = params.cifNo ?: ""
        }
        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<AccountLockOTPResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val response = base.data as? AccountLockOTPResponse
                    ?: return Resource.Error("Cannot parse to AccountLockOTPResponse", "999")
                val domain = AccountLockResponseDataMapper.INSTANCE.toDomain(response)
                Resource.Success(domain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }
}
