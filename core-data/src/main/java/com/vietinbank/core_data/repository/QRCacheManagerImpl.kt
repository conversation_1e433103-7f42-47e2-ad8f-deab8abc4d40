package com.vietinbank.core_data.repository
import com.vietinbank.core_common.cache.CacheFactory
import com.vietinbank.core_common.cache.CacheManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_domain.models.account.AccountSaveQR
import com.vietinbank.core_domain.repository.cache.IQRCacheManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Triển khai theo đúng style TransferCacheManagerImpl:
 * - Dùng CacheFactory.create(...) -> CacheManager
 * - Lưu/clear bằng saveAsync()/clearAsync()
 * - Expose StateFlow để layer UI/ViewModel quan sát.
 */
@Singleton
class QRCacheManagerImpl @Inject constructor(
    cacheFactory: CacheFactory,
) : IQRCacheManager {

    // Lưu toàn bộ dữ liệu dưới dạng Map<accountNo, AccountSaveQR>
    private val qrCache: CacheManager<Map<String, AccountSaveQR>> = cacheFactory.create(
        key = Tags.QR_CACHE,
        expirationTime = Long.MAX_VALUE,
        encrypted = true, // QR chứa thông tin tài khoản -> nên mã hóa
    )

    private val _qrMapFlow = MutableStateFlow<Map<String, AccountSaveQR>>(emptyMap())
    override val qrMapFlow: StateFlow<Map<String, AccountSaveQR>> = _qrMapFlow.asStateFlow()

    @Volatile private var loaded = false
    private val mutex = kotlinx.coroutines.sync.Mutex()

    /** Gọi 1 lần khi app/VM khởi tạo để hydrate từ disk
     * Gom đúng “combo” hành vi (đảm bảo nạp 1 lần, thread-safety, IO trên Dispatchers.IO)
     * thay vì bắt caller biết thứ tự loadIfNeeded() → getCached().
     * Cho phép đổi implementation (từ loadIfNeeded()+getCached() sang get() duy nhất,
     * hoặc đọc từ DB…) mà không đổi interface/call site.
     * Dễ thêm guard (Mutex/flag), log, metric… mà không rò rỉ ra ngoài.*/
    override suspend fun warmUp() {
        if (loaded) return
        mutex.lock()
        try {
            val persisted = qrCache.get() ?: emptyMap() // <— đọc từ prefs (suspend)
            _qrMapFlow.value = persisted
            loaded = true
        } finally {
            mutex.unlock()
        }
    }

    override fun saveQR(accountNo: String, data: AccountSaveQR) {
        val updated = _qrMapFlow.value.toMutableMap().apply { put(accountNo, data) }
        qrCache.saveAsync(updated)
        _qrMapFlow.value = updated
    }

    override fun getQR(accountNo: String): AccountSaveQR? = _qrMapFlow.value[accountNo]

    override fun getAllQR(): Map<String, AccountSaveQR> = _qrMapFlow.value

    override fun clearQR(accountNo: String) {
        if (_qrMapFlow.value.containsKey(accountNo)) {
            val updated = _qrMapFlow.value.toMutableMap().apply { remove(accountNo) }
            // Lưu lại toàn bộ map sau khi remove (giữ đúng pattern saveAsync)
            qrCache.saveAsync(updated)
            _qrMapFlow.value = updated
        }
    }

    override fun clearAllQR() {
        _qrMapFlow.value = emptyMap()
        qrCache.clearAsync()
    }

    override suspend fun hasCachedData(): Boolean = qrCache.exists()
}