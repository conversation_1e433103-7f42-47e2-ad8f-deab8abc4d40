package com.vietinbank.core_data.repository

import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_data.mapper.ott.OttStatusListResponseDataMapper
import com.vietinbank.core_data.mapper.ott.OttStatusUpdateResponseDataMapper
import com.vietinbank.core_data.mapper.ott.VacctResponseDataMapper
import com.vietinbank.core_data.mapper.ott_feature.CheckRegResponseDataMapper
import com.vietinbank.core_data.mapper.ott_feature.ListRegResponseDataMapper
import com.vietinbank.core_data.mapper.ott_feature.OttRegResponseDataMapper
import com.vietinbank.core_data.mapper.ott_feature.SmsOtpCreateResponseDataMapper
import com.vietinbank.core_data.mapper.ott_feature.SmsOtpVerifyResponseDataMapper
import com.vietinbank.core_data.models.request.ott_feature.CancelVacctRequest
import com.vietinbank.core_data.models.request.ott_feature.CheckRegisterRequest
import com.vietinbank.core_data.models.request.ott_feature.ConfirmVacctRequest
import com.vietinbank.core_data.models.request.ott_feature.ItemRegistedInfoRequest
import com.vietinbank.core_data.models.request.ott_feature.ListRegisterRequest
import com.vietinbank.core_data.models.request.ott_feature.OttRegisterRequest
import com.vietinbank.core_data.models.request.ott_feature.OttStatusListRequest
import com.vietinbank.core_data.models.request.ott_feature.OttStatusUpdateRequest
import com.vietinbank.core_data.models.request.ott_feature.RecordRequest
import com.vietinbank.core_data.models.request.ott_feature.SmsOtpCreateRequest
import com.vietinbank.core_data.models.request.ott_feature.SmsOtpVerifyRequest
import com.vietinbank.core_data.models.response.ott_feature.CancelVacctOttResponse
import com.vietinbank.core_data.models.response.ott_feature.CheckRegResponse
import com.vietinbank.core_data.models.response.ott_feature.ListRegResponse
import com.vietinbank.core_data.models.response.ott_feature.OttRegResponse
import com.vietinbank.core_data.models.response.ott_feature.OttStatusListResponse
import com.vietinbank.core_data.models.response.ott_feature.OttStatusUpdateResponse
import com.vietinbank.core_data.models.response.ott_feature.ResultItemResponse
import com.vietinbank.core_data.models.response.ott_feature.SmsOtpCreateRequestResponse
import com.vietinbank.core_data.models.response.ott_feature.SmsOtpVerifyRequestResponse
import com.vietinbank.core_data.network.api.ApiService
import com.vietinbank.core_data.network.api.IApiClient
import com.vietinbank.core_data.network.di.Default
import com.vietinbank.core_domain.models.ott_feature.CancelRecord
import com.vietinbank.core_domain.models.ott_feature.CancelVacctOttDomain
import com.vietinbank.core_domain.models.ott_feature.CancelVacctOttParams
import com.vietinbank.core_domain.models.ott_feature.CheckRegDomains
import com.vietinbank.core_domain.models.ott_feature.CheckRegRequestParams
import com.vietinbank.core_domain.models.ott_feature.ConfirmVacctParams
import com.vietinbank.core_domain.models.ott_feature.ListRegDomains
import com.vietinbank.core_domain.models.ott_feature.ListRegRequestParams
import com.vietinbank.core_domain.models.ott_feature.OttRegisterDomains
import com.vietinbank.core_domain.models.ott_feature.OttRegisterRequestParams
import com.vietinbank.core_domain.models.ott_feature.OttStatusListDomain
import com.vietinbank.core_domain.models.ott_feature.OttStatusUpdateDomain
import com.vietinbank.core_domain.models.ott_feature.OttStatusUpdateParams
import com.vietinbank.core_domain.models.ott_feature.ResultItemDomains
import com.vietinbank.core_domain.models.ott_feature.SmsOtpCreateRequestDomains
import com.vietinbank.core_domain.models.ott_feature.SmsOtpCreateRequestParams
import com.vietinbank.core_domain.models.ott_feature.SmsOtpVerifyRequestDomain
import com.vietinbank.core_domain.models.ott_feature.SmsOtpVerifyRequestParams
import com.vietinbank.core_domain.repository.OttFeatureRepository
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class OttFeatureRepositoryImpl @Inject constructor(
    @Default private val apiClient: IApiClient,
    private val apiService: ApiService,
) : OttFeatureRepository {
    override suspend fun checkRegister(params: CheckRegRequestParams): Resource<CheckRegDomains> {
        val itemRegistedInfoRequestList = mutableListOf<ItemRegistedInfoRequest>()
        params.registedInfoList.forEach { item ->
            itemRegistedInfoRequestList.add(
                ItemRegistedInfoRequest(
                    cifno = item.cifno,
                    phoneNumber = item.phoneNumber,
                    username = item.username,
                    accountName = item.accountName,
                ),
            )
        }

        val request = CheckRegisterRequest(
            registedInfoList = itemRegistedInfoRequestList,
            tranId = params.tranId,
            username = params.username,
            isVacct = params.isVacct,
        ).apply {
            tranId = requestId
        }

        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<CheckRegResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val checkRegResponse =
                    base.data as? CheckRegResponse ?: return Resource.Error(
                        "Cannot parse to CheckRegResponse",
                        "999",
                    )
                val checkRegDomains =
                    CheckRegResponseDataMapper.INSTANCE.toDomain(
                        checkRegResponse,
                    )
                Resource.Success(checkRegDomains)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun listReg(params: ListRegRequestParams): Resource<ListRegDomains> {
        val request = ListRegisterRequest(
            alertMethod = params.alertMethod,
            isVacct = params.isVacct,
            mobileNumber = params.mobileNumber,
            roleId = params.roleId,
            tranId = params.tranId,
            typeCheck = params.typeCheck,
            username = params.username,
        ).apply {
            tranId = requestId
        }
        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<ListRegResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val listRegResponse =
                    base.data as? ListRegResponse ?: return Resource.Error(
                        "Cannot parse to ListRegResponse",
                        "999",
                    )
                val listRegDomains =
                    ListRegResponseDataMapper.INSTANCE.toDomain(
                        listRegResponse,
                    )
                Resource.Success(listRegDomains)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun smsOtpCreate(params: SmsOtpCreateRequestParams): Resource<SmsOtpCreateRequestDomains> {
        val request = SmsOtpCreateRequest(
            accountNumber = params.accountNumber,
            isVacct = params.isVacct,
            phoneNo = params.phoneNo,
            roleId = params.roleId,
            type = params.type,
            username = params.username,
        )
        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<SmsOtpCreateRequestResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val smsOtpCreateRequestResponse =
                    base.data as? SmsOtpCreateRequestResponse ?: return Resource.Error(
                        "Cannot parse to SmsOtpCreateRequestResponse",
                        "999",
                    )
                val smsOtpCreateRequestDomains =
                    SmsOtpCreateResponseDataMapper.INSTANCE.toDomain(
                        smsOtpCreateRequestResponse,
                    )
                Resource.Success(smsOtpCreateRequestDomains)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun smsOtpVerify(params: SmsOtpVerifyRequestParams): Resource<SmsOtpVerifyRequestDomain> {
        val request = SmsOtpVerifyRequest(
            isVacct = params.isVacct,
            otpNumber = params.otpNumber,
            phoneNo = params.phoneNo,
            roleId = params.roleId,
            typeCheck = params.typeCheck,
            username = params.username,
        )
        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<SmsOtpVerifyRequestResponse>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val smsOtpVerifyRequestResponse =
                    base.data as? SmsOtpVerifyRequestResponse ?: return Resource.Error(
                        "Cannot parse to SmsOtpCreateRequestResponse",
                        "999",
                    )
                val smsOtpVerifyRequestDomains =
                    SmsOtpVerifyResponseDataMapper.INSTANCE.toDomain(
                        smsOtpVerifyRequestResponse,
                    )
                Resource.Success(smsOtpVerifyRequestDomains)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun registerOTT(params: OttRegisterRequestParams): Resource<OttRegisterDomains> {
        fun ResultItemDomains.toRequest(): ResultItemResponse {
            return ResultItemResponse(
                accountName = this.accountName,
                accountNumber = this.accountNumber,
                accountType = this.accountType,
                action = this.action,
                alertAddr = this.alertAddr,
                alertMethod = this.alertMethod,
                alertType = this.alertType,
                customerNumber = this.customerNumber,
                feeAccountNumber = this.feeAccountNumber,
                feeAmount = this.feeAmount,
                feeType = this.feeType,
                isVacct = this.isVacct,
                phonenumber = this.phonenumber,
                startDate = this.startDate,
                status = this.status,
                username = this.username,
                branchId = this.branchId,
            )
        }

        fun List<ResultItemDomains>.toRequestList(): MutableList<ResultItemResponse> {
            return this.map { it.toRequest() }.toMutableList()
        }

        val request = OttRegisterRequest(
            isVacct = params.isVacct,
            mobileNumber = params.mobileNumber,
            roleId = params.roleId,
            tranId = params.tranId,
            username = params.username,
            recordsParams = params.recordsParams.toRequestList(),
        ).apply {
            tranId = requestId
        }

        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<OttRegResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val ottRegResponse =
                    base.data as? OttRegResponse ?: return Resource.Error(
                        "Cannot parse to OttRegResponse",
                        "999",
                    )
                val ottRegDomains =
                    OttRegResponseDataMapper.INSTANCE.toDomain(
                        ottRegResponse,
                    )
                Resource.Success(ottRegDomains)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun ottStatusUpdate(params: OttStatusUpdateParams): Resource<OttStatusUpdateDomain> {
        val request = OttStatusUpdateRequest(
            userName = params.username,
            status = params.status,
            channelType = "default",
        )

        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { requestBody -> apiService.callSITEnv(requestBody) },
            parseType = object : TypeToken<OttStatusUpdateResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val response = base.data as? OttStatusUpdateResponse
                    ?: return Resource.Error("Cannot parse to OttStatusUpdateResponse", "999")

                val domain = OttStatusUpdateResponseDataMapper.INSTANCE.toDomain(response)

                Resource.Success(domain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun ottStatusList(params: String): Resource<OttStatusListDomain> {
        val request = OttStatusListRequest(
            userName = params,
            channelType = "default",
        )

        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { requestBody -> apiService.callSITEnv(requestBody) },
            parseType = object : TypeToken<OttStatusListResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val response = base.data as? OttStatusListResponse
                    ?: return Resource.Error("Cannot parse to OttStatusListResponse", "999")

                val domain = OttStatusListResponseDataMapper.INSTANCE.toDomain(response)

                Resource.Success(domain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    private fun List<CancelRecord?>?.toRecordRequestList(): List<RecordRequest?>? {
        return this?.map { cancelRecord ->
            cancelRecord?.let {
                RecordRequest(
                    username = it.username,
                    tranId = it.tranId,
                    virAcctNo = it.virAcctNo,
                    virAcctName = it.virAcctName,
                    virPhoneNo = it.virPhoneNo,
                )
            }
        }
    }

    override suspend fun confirmVacct(params: ConfirmVacctParams): Resource<CancelVacctOttDomain> {
        val request = ConfirmVacctRequest(
            phoneNo = params.phoneNo,
            userAction = params.userAction,
            username = params.username,
            userRole = params.userRole,
            records = params.records.toRecordRequestList(),
        )

        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { requestBody -> apiService.callSITEnv(requestBody) },
            parseType = object : TypeToken<CancelVacctOttResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val response = base.data as? CancelVacctOttResponse
                    ?: return Resource.Error("Cannot parse to confirmVacctOttResponse", "999")

                val domain = VacctResponseDataMapper.INSTANCE.toDomain(response)

                Resource.Success(domain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun cancelVacct(params: CancelVacctOttParams): Resource<CancelVacctOttDomain> {
        val request = CancelVacctRequest(
            phoneNo = params.phoneNo,
            username = params.username,
            records = params.record.toRecordRequestList(),
        )

        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { requestBody -> apiService.callSITEnv(requestBody) },
            parseType = object : TypeToken<CancelVacctOttResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val response = base.data as? CancelVacctOttResponse
                    ?: return Resource.Error("Cannot parse to CancelVacctOttResponse", "999")

                val domain = VacctResponseDataMapper.INSTANCE.toDomain(response)

                Resource.Success(domain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }
}
