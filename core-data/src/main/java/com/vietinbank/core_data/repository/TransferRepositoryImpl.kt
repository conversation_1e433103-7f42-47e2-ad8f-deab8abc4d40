package com.vietinbank.core_data.repository

import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.utils.GsonProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_data.mapper.transfer.BranchListResponseDataMapper
import com.vietinbank.core_data.mapper.transfer.ContactCreateResponseDataMapper
import com.vietinbank.core_data.mapper.transfer.ContactListResponseDataMapper
import com.vietinbank.core_data.mapper.transfer.CreateNapasAccountTransferResponseDataMapper
import com.vietinbank.core_data.mapper.transfer.CreateTemplateResponseDataMapper
import com.vietinbank.core_data.mapper.transfer.GetPaymentTemplateListResponseDataMapper
import com.vietinbank.core_data.mapper.transfer.NapasBankListResponseDataMapper
import com.vietinbank.core_data.mapper.transfer.NextStatusTransactionByRuleResponseDataMapper
import com.vietinbank.core_data.mapper.transfer.ValidateNapasAccountResponseDataMapper
import com.vietinbank.core_data.mapper.transfer.ValidateNapasAccountTransferResponseDataMapper
import com.vietinbank.core_data.mapper.transfer.ValidateNapasCardResponseDataMapper
import com.vietinbank.core_data.mapper.transfer.ValidateNapasCardTransferResponseDataMapper
import com.vietinbank.core_data.mapper.transfer.ValidatePaymentOrderTransferResponseDataMapper
import com.vietinbank.core_data.models.request.BranchRequest
import com.vietinbank.core_data.models.request.ContactCreateRequest
import com.vietinbank.core_data.models.request.ContactListRequest
import com.vietinbank.core_data.models.request.CreateNapasAccountTransferRequest
import com.vietinbank.core_data.models.request.CreateNapasCardTransferRequest
import com.vietinbank.core_data.models.request.CreatePaymentOrderTransfer
import com.vietinbank.core_data.models.request.CreateTemplateRequest
import com.vietinbank.core_data.models.request.GetPaymentTemplateListRequest
import com.vietinbank.core_data.models.request.NapasBankListRequest
import com.vietinbank.core_data.models.request.NextApproversRequest
import com.vietinbank.core_data.models.request.NextStatusTransactionByRuleRequest
import com.vietinbank.core_data.models.request.TempTransactionRequest
import com.vietinbank.core_data.models.request.ValidateNapasAccountRequest
import com.vietinbank.core_data.models.request.ValidateNapasAccountTransferRequest
import com.vietinbank.core_data.models.request.ValidateNapasCardRequest
import com.vietinbank.core_data.models.request.ValidateNapasCardTransferRequest
import com.vietinbank.core_data.models.request.ValidatePaymentOrderTransferRequest
import com.vietinbank.core_data.models.response.BranchListResponse
import com.vietinbank.core_data.models.response.ContactCreateResponse
import com.vietinbank.core_data.models.response.ContactListResponse
import com.vietinbank.core_data.models.response.CreateTemplateResponse
import com.vietinbank.core_data.models.response.CreateTransferResponse
import com.vietinbank.core_data.models.response.GetPaymentTemplateListResponse
import com.vietinbank.core_data.models.response.NapasBankListResponse
import com.vietinbank.core_data.models.response.NextStatusTransactionByRuleResponse
import com.vietinbank.core_data.models.response.ValidateNapasAccountResponse
import com.vietinbank.core_data.models.response.ValidateNapasAccountTransferResponse
import com.vietinbank.core_data.models.response.ValidateNapasCardResponse
import com.vietinbank.core_data.models.response.ValidateNapasCardTransferResponse
import com.vietinbank.core_data.models.response.ValidatePaymentOrderTransferResponse
import com.vietinbank.core_data.network.api.ApiService
import com.vietinbank.core_data.network.api.IApiClient
import com.vietinbank.core_data.network.di.Default
import com.vietinbank.core_domain.models.maker.BranchListDomains
import com.vietinbank.core_domain.models.maker.BranchParams
import com.vietinbank.core_domain.models.maker.ContactCreateDomains
import com.vietinbank.core_domain.models.maker.ContactCreateParams
import com.vietinbank.core_domain.models.maker.ContactListDomains
import com.vietinbank.core_domain.models.maker.ContactListParams
import com.vietinbank.core_domain.models.maker.CreateTemplateDomains
import com.vietinbank.core_domain.models.maker.CreateTemplateParams
import com.vietinbank.core_domain.models.maker.CreateTransferDomain
import com.vietinbank.core_domain.models.maker.CreateTransferParams
import com.vietinbank.core_domain.models.maker.GetPaymentTemplateListDomains
import com.vietinbank.core_domain.models.maker.GetPaymentTemplateListParams
import com.vietinbank.core_domain.models.maker.NapasBankListDomain
import com.vietinbank.core_domain.models.maker.NapasBankListParams
import com.vietinbank.core_domain.models.maker.NextStatusTransactionByRuleDomains
import com.vietinbank.core_domain.models.maker.NextStatusTransactionByRuleParams
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountDomain
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountParams
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountTransferDomain
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountTransferParams
import com.vietinbank.core_domain.models.maker.ValidateNapasCardDomains
import com.vietinbank.core_domain.models.maker.ValidateNapasCardParams
import com.vietinbank.core_domain.models.maker.ValidateNapasCardTransferDomains
import com.vietinbank.core_domain.models.maker.ValidateNapasCardTransferParams
import com.vietinbank.core_domain.models.maker.ValidatePaymentOrderTransferDomains
import com.vietinbank.core_domain.models.maker.ValidatePaymentOrderTransferParams
import com.vietinbank.core_domain.repository.TransferRepository
import javax.inject.Inject

class TransferRepositoryImpl @Inject constructor(
    @Default private val apiClient: IApiClient,
    private val apiService: ApiService,
    private val gsonProvider: GsonProvider,
) : TransferRepository {
    override suspend fun getNapasBankList(params: NapasBankListParams): Resource<NapasBankListDomain> {
        val napasBankListRequest = NapasBankListRequest(
            username = params.username,
        )
        val result = apiClient.makeApiCall(
            request = napasBankListRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<NapasBankListResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val napasBankListResponse = base.data as? NapasBankListResponse
                    ?: return Resource.Error("Cannot parse to napasBankListDomain", "999")
                val napasBankListDomain =
                    NapasBankListResponseDataMapper.INSTANCE.toDomain(napasBankListResponse)
                Resource.Success(napasBankListDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun validateNapasAccount(params: ValidateNapasAccountParams): Resource<ValidateNapasAccountDomain> {
        val validateNapasAccountRequest = ValidateNapasAccountRequest(
            receiveAccount = params.receiveAccount,
            receiveBin = params.receiveBin,
            debitAccount = params.debitAccount,
            debitFullname = params.debitFullname,
            currency = params.currency,
            username = params.username,
            tranType = params.tranType,
            receiveBankName = params.receiveBankName,
        )
        val result = apiClient.makeApiCall(
            request = validateNapasAccountRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<ValidateNapasAccountResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val validateNapasAccountResponse = base.data as? ValidateNapasAccountResponse
                    ?: return Resource.Error("Cannot parse to validateNapasAccountResponse", "999")

                val validateNapasAccountDomain =
                    ValidateNapasAccountResponseDataMapper.INSTANCE.toDomain(
                        validateNapasAccountResponse,
                    )
                Resource.Success(validateNapasAccountDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun createNapasAccountTransfer(params: CreateTransferParams): Resource<CreateTransferDomain> {
        val createNapasAccountTransferParams = when (params.transferType) {
            Tags.TransferType.TYPE_ACCOUNT -> {
                CreateNapasAccountTransferRequest(
                    // chuyển qua tài khoan
                    username = params.username,
                    tranType = params.tranType,
                )
            }

            Tags.TransferType.TYPE_ACCOUNT_SPLIT -> {
                CreateNapasAccountTransferRequest(
                    // chuyển tách ệnh
                    username = params.username,
                    tranType = params.tranType,
                    confirm = "N",
                )
            }

            Tags.TransferType.TYPE_CARD -> CreateNapasCardTransferRequest(
                // chuyển qua thẻ
                username = params.username,
            )

            Tags.TransferType.TYPE_PAYMENT_ORDER -> CreatePaymentOrderTransfer(
                // lệnh chi
                username = params.username,
                tranType = params.tranType,
            )

            else -> {
                CreatePaymentOrderTransfer(
                    //
                    username = "",
                    tranType = "",
                )
            }
        }

        val result = apiClient.makeApiCall(
            request = createNapasAccountTransferParams,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<CreateTransferResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val createTransferResponse =
                    base.data as? CreateTransferResponse ?: return Resource.Error(
                        "Cannot parse to createTransferResponse",
                        "999",
                    )
                val createNapasAccountTransferDomain =
                    CreateNapasAccountTransferResponseDataMapper.INSTANCE.toDomain(
                        createTransferResponse,
                    )

                Resource.Success(createNapasAccountTransferDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun validateNapasAccountTransfer(params: ValidateNapasAccountTransferParams): Resource<ValidateNapasAccountTransferDomain> {
        val validateNapasAccountTransferRequest = params.nextApprovers.map { approverDomain ->
            NextApproversRequest(
                approverlevel = approverDomain.approverlevel,
                birthdate = approverDomain.birthdate,
                email = approverDomain.email,
                endate = approverDomain.endate,
                enterpriseid = approverDomain.enterpriseid,
                faillogon = approverDomain.faillogon,
                fullname = approverDomain.fullname,
                gender = approverDomain.gender,
                grouptype = approverDomain.grouptype,
                id = approverDomain.id,
                idnumber = approverDomain.idnumber,
                idtype = approverDomain.idtype,
                keypassid = approverDomain.keypassid,
                keypassprofile = approverDomain.keypassprofile,
                keypasssoftotp = approverDomain.keypasssoftotp,
                keypasswaitactive = approverDomain.keypasswaitactive,
                lastlogon = approverDomain.lastlogon,
                mailalert = approverDomain.mailalert,
                mobile = approverDomain.mobile,
                nationality = approverDomain.nationality,
                pStatus = approverDomain.pStatus,
                password = approverDomain.password,
                rsaprofile = approverDomain.rsaprofile,
                rsaserial = approverDomain.rsaserial,
                startdate = approverDomain.startdate,
                status = approverDomain.status,
                title = approverDomain.title,
                username = approverDomain.username,
                version = approverDomain.version,
                isSelected = approverDomain.isSelected,
                enterpriseUserId = approverDomain.enterpriseUserId,
                endDate = approverDomain.endDate,
            )
        }.let {
            ValidateNapasAccountTransferRequest(
                amount = params.amount,
                currency = params.currency,
                feePayMethod = params.feePayMethod,
                fromAcctNo = params.fromAcctNo,
                isQRTransfer = params.isQRTransfer,
                processDate = params.processDate,
                receiveBin = params.receiveBin,
                remark = params.remark,
                sendBank = params.sendBank,
                toAcctName = params.toAcctName,
                toAcctNo = params.toAcctNo,
                toBankName = params.toBankName,
                username = params.username,
                tranType = params.tranType,
                nextApprovers = it,
            )
        }
        val result = apiClient.makeApiCall(
            request = validateNapasAccountTransferRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<ValidateNapasAccountTransferResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val validateNapasAccountTransferResponse =
                    base.data as? ValidateNapasAccountTransferResponse
                        ?: return Resource.Error("Cannot parse to LoginResponseData", "999")
                            ?: return Resource.Error(
                                "Cannot parse to validateNapasAccountTransferResponse",
                                "999",
                            )
                val validateNapasAccountTransferDomain =
                    ValidateNapasAccountTransferResponseDataMapper.INSTANCE.toDomain(
                        validateNapasAccountTransferResponse,
                    )
                Resource.Success(validateNapasAccountTransferDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun validateNapasCard(params: ValidateNapasCardParams): Resource<ValidateNapasCardDomains> {
        val validateNapasCardRequest = ValidateNapasCardRequest(
            branch = params.branch,
            cardNumber = params.cardNumber,
            currency = params.currency,
            debitAccount = params.debitAccount,
            debitFullname = params.debitFullname,
            username = params.username,
        )
        val result = apiClient.makeApiCall(
            request = validateNapasCardRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<ValidateNapasCardResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val validateNapasCardResponse = base.data as? ValidateNapasCardResponse
                    ?: return Resource.Error("Cannot parse to validateNapasCardResponse", "999")
                val validateNapasCardDomains =
                    ValidateNapasCardResponseDataMapper.INSTANCE.toDomain(
                        validateNapasCardResponse,
                    )
                Resource.Success(validateNapasCardDomains)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun validateNapasCardTransfer(params: ValidateNapasCardTransferParams): Resource<ValidateNapasCardTransferDomains> {
        val validateNapasCardTransferRequest = ValidateNapasCardTransferRequest(
            amount = params.amount,
            currency = params.currency,
            feePayMethod = params.feePayMethod,
            fromAcctNo = params.fromAcctNo,
            isQRTransfer = params.isQRTransfer,
            processDate = params.processDate,
            remark = params.remark,
            sendBank = params.sendBank,
            toBankName = params.toBankName,
            toCardName = params.toCardName,
            toCardNo = params.toCardNo,
            username = params.username,
            nextApprovers = params.nextApprovers,
        )
        val result = apiClient.makeApiCall(
            request = validateNapasCardTransferRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<ValidateNapasCardTransferResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val validateNapasCardTransferResponse =
                    base.data as? ValidateNapasCardTransferResponse ?: return Resource.Error(
                        "Cannot parse to validateNapasCardTransferResponse",
                        "999",
                    )
                val validateNapasCardTransferDomains =
                    ValidateNapasCardTransferResponseDataMapper.INSTANCE.toDomain(
                        validateNapasCardTransferResponse,
                    )
                Resource.Success(validateNapasCardTransferDomains)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun contactList(params: ContactListParams): Resource<ContactListDomains> {
        val contactListRequest = ContactListRequest(
            accountNo = params.accountNo,
            actionId = params.actionId,
            customercode = params.customercode,
            keySearch = params.keySearch,
            payeeName = params.payeeName,
            place = params.place,
            provinceId = params.provinceId,
            revBankCode = params.revBankCode,
            username = params.username,
            serviceId = params.serviceId,
        )

        val result = apiClient.makeApiCall(
            request = contactListRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<ContactListResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val contactListResponse =
                    base.data as? ContactListResponse ?: return Resource.Error(
                        "Cannot parse to contactLisstResponse",
                        "999",
                    )
                val contactListDomains =
                    ContactListResponseDataMapper.INSTANCE.toDomain(contactListResponse)
                Resource.Success(contactListDomains)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun getPaymentTemplateList(params: GetPaymentTemplateListParams): Resource<GetPaymentTemplateListDomains> {
        val getPaymentTemplateListRequest = GetPaymentTemplateListRequest(
            username = params.username,
            tempTransaction = TempTransactionRequest(
                amount = params.tempTransaction.amount,
                confirm = params.tempTransaction.confirm,
                content = params.tempTransaction.content,
                currency = params.tempTransaction.currency,
                fromAccountNo = params.tempTransaction.fromAccountNo,
                id = params.tempTransaction.id,
                numberTransacion = params.tempTransaction.numberTransacion,
                processDate = params.tempTransaction.processDate,
                toAccountName = params.tempTransaction.toAccountName,
                toAccountNo = params.tempTransaction.toAccountNo,
                toBankCode = params.tempTransaction.toBankCode,
                toBankName = params.tempTransaction.toBankName,
                tranType = params.tempTransaction.tranType,
                userId = params.tempTransaction.userId,
                toCardNo = params.tempTransaction.toCardNo,
            ),
        )
        val result = apiClient.makeApiCall(
            request = getPaymentTemplateListRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<GetPaymentTemplateListResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val getPaymentTemplateListResponse =
                    base.data as? GetPaymentTemplateListResponse ?: return Resource.Error(
                        "Cannot parse to GetPaymentTemplateListResponse",
                        "999",
                    )
                val getPaymentTemplateListDomains =
                    GetPaymentTemplateListResponseDataMapper.INSTANCE.toDomain(
                        getPaymentTemplateListResponse,
                    )
                Resource.Success(getPaymentTemplateListDomains)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun contactCreate(params: ContactCreateParams): Resource<ContactCreateDomains> {
        val contactCreateParams = ContactCreateRequest(
            accountNo = params.accountNo,
            actionId = params.actionId,
            cardNo = params.cardNo,
            confirm = params.confirm,
            contactId = params.contactId,
            currency = params.currency,
            customercode = params.customercode,
            idtype = params.idtype,
            isdisplayed = params.isdisplayed,
            issuePlace = params.issuePlace,
            issuedate = params.issuedate,
            istrusted = params.istrusted,
            midBank = params.midBank,
            midBankName = params.midBankName,
            orgAcctBranch = params.orgAcctBranch,
            orgAcctName = params.orgAcctName,
            orgId = params.orgId,
            orgName = params.orgName,
            payeeName = params.payeeName,
            place = params.place,
            provinceId = params.provinceId,
            provinceName = params.provinceName,
            receiveBank = params.receiveBank,
            receiveBankName = params.receiveBankName,
            revBankCode = params.revBankCode,
            serviceId = params.serviceId,
            tranType = params.tranType,
            username = params.username,
        )
        val result = apiClient.makeApiCall(
            request = contactCreateParams,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<ContactCreateResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val contactCreateResponse =
                    base.data as? ContactCreateResponse ?: return Resource.Error(
                        "Cannot parse to ContactCreate",
                        "999",
                    )
                val contactCreateDomains = ContactCreateResponseDataMapper.INSTANCE.toDomain(
                    contactCreateResponse,
                )
                Resource.Success(contactCreateDomains)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun createTemplate(params: CreateTemplateParams): Resource<CreateTemplateDomains> {
        val createTemplateRequest = CreateTemplateRequest(
            username = params.username,
            tempTransaction = TempTransactionRequest(
                amount = params.tempTransaction.amount,
                confirm = params.tempTransaction.confirm,
                content = params.tempTransaction.content,
                currency = params.tempTransaction.currency,
                fromAccountNo = params.tempTransaction.fromAccountNo,
                id = params.tempTransaction.id,
                numberTransacion = params.tempTransaction.numberTransacion,
                processDate = params.tempTransaction.processDate,
                toAccountName = params.tempTransaction.toAccountName,
                toAccountNo = params.tempTransaction.toAccountNo,
                toBankCode = params.tempTransaction.toBankCode,
                toBankName = params.tempTransaction.toBankName,
                tranType = params.tempTransaction.tranType,
                userId = params.tempTransaction.userId,
                bankIcon = params.tempTransaction.bankIcon,
            ),
        )
        val result = apiClient.makeApiCall(
            request = createTemplateRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<CreateTemplateResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val createTemplateResponse =
                    base.data as? CreateTemplateResponse ?: return Resource.Error(
                        "Cannot parse to CreateTemplateResponse",
                        "999",
                    )
                val contactCreateDomains = CreateTemplateResponseDataMapper.INSTANCE.toDomain(
                    createTemplateResponse,
                )
                Resource.Success(contactCreateDomains)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun validatePaymentOrderTransfer(params: ValidatePaymentOrderTransferParams): Resource<ValidatePaymentOrderTransferDomains> {
        val params = ValidatePaymentOrderTransferRequest(
            amount = params.amount,
            file = params.file,
            content = params.content,
            feePayMethod = params.feePayMethod,
            fromAccountNo = params.fromAccountNo,
            toAccountName = params.toAccountName,
            toAccountNo = params.toAccountNo,
            toBankName = params.toBankName,
            username = params.username,
            processTime = params.processTime,
            fileName = params.fileName,
            branchId = params.branchId,
            nextApprovers = params.nextApprovers,

        )
        val result = apiClient.makeApiCall(
            request = params,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<ValidatePaymentOrderTransferResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val validatePaymentOrderTransferResponse =
                    base.data as? ValidatePaymentOrderTransferResponse ?: return Resource.Error(
                        "Cannot parse to CreateTemplateResponse",
                        "999",
                    )
                val validatePaymentOrderTransferDomains =
                    ValidatePaymentOrderTransferResponseDataMapper.INSTANCE.toDomain(
                        validatePaymentOrderTransferResponse,
                    )
                Resource.Success(validatePaymentOrderTransferDomains)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun nextStatusTransactionByRule(params: NextStatusTransactionByRuleParams): Resource<NextStatusTransactionByRuleDomains> {
        val params = NextStatusTransactionByRuleRequest(
            mtId = params.mtId,
            amount = params.amount,
            creator = params.creator,
            currentStatus = params.currentStatus,
            currentUserGroup = params.currentUserGroup,
            currentUserLevel = params.currentUserLevel,
            customerNumber = params.customerNumber,
            fromAccountNo = params.fromAccountNo,
            serviceCode = params.serviceCode,
            toAccountNo = params.toAccountNo,
            username = params.username,
        )
        val result = apiClient.makeApiCall(
            request = params,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<NextStatusTransactionByRuleResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val validatePaymentOrderTransferResponse =
                    base.data as? NextStatusTransactionByRuleResponse ?: return Resource.Error(
                        "Cannot parse to NextStatusTransactionByRuleResponse",
                        "999",
                    )
                val nextStatusTransactionByRuleDomains =
                    NextStatusTransactionByRuleResponseDataMapper.INSTANCE.toDomain(
                        validatePaymentOrderTransferResponse,
                    )
                Resource.Success(nextStatusTransactionByRuleDomains)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun getBranch(params: BranchParams): Resource<BranchListDomains> {
        val params = BranchRequest(
            branchId = params.branchId,
            branchName = params.branchName,
            username = params.username,
        )
        val result = apiClient.makeApiCall(
            request = params,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<BranchListResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val branchListResponse =
                    base.data as? BranchListResponse ?: return Resource.Error(
                        "Cannot parse to BranchListResponse",
                        "999",
                    )
                val branchListDomains =
                    BranchListResponseDataMapper.INSTANCE.toDomain(
                        branchListResponse,
                    )
                Resource.Success(branchListDomains)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }
}
