package com.vietinbank.core_data.repository

import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.utils.GsonProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_data.mapper.checker.ApproveResponseDataMapper
import com.vietinbank.core_data.mapper.checker.CountPendingResponseDataMapper
import com.vietinbank.core_data.mapper.checker.GenKeyPassChallengeCodeResponseDataMapper
import com.vietinbank.core_data.mapper.checker.GenSOTPTransCodeResponseDataMapper
import com.vietinbank.core_data.mapper.checker.GetBatchTransactionListResponseDataMapper
import com.vietinbank.core_data.mapper.checker.GetDownloadBase64FileResponseDataMapper
import com.vietinbank.core_data.mapper.checker.GetDownloadFileIDResponseDataMapper
import com.vietinbank.core_data.mapper.checker.GetTransactionDetailResponseDataMapper
import com.vietinbank.core_data.mapper.checker.GetTransactionListResponseDataMapper
import com.vietinbank.core_data.mapper.checker.MobileConfigLastResponseDataMapper
import com.vietinbank.core_data.mapper.checker.PreApproveResponseDataMapper
import com.vietinbank.core_data.mapper.checker.RejectResponseDataMapper
import com.vietinbank.core_data.models.request.ApproveRequest
import com.vietinbank.core_data.models.request.CountPendingRequest
import com.vietinbank.core_data.models.request.GenKeyPassChallengeCodeRequest
import com.vietinbank.core_data.models.request.GenSOTPTransCodeRequest
import com.vietinbank.core_data.models.request.GetBatchTransactionListRequest
import com.vietinbank.core_data.models.request.GetDownloadBase64FileRequest
import com.vietinbank.core_data.models.request.GetDownloadFileIDRequest
import com.vietinbank.core_data.models.request.GetExportNSNNFileTemplateRequest
import com.vietinbank.core_data.models.request.GetTranListRequest
import com.vietinbank.core_data.models.request.GetTransactionDetailRequest
import com.vietinbank.core_data.models.request.GetTransactionListRequest
import com.vietinbank.core_data.models.request.MobileConfigLastRequest
import com.vietinbank.core_data.models.request.PreApproveRequest
import com.vietinbank.core_data.models.request.PreRejectRequest
import com.vietinbank.core_data.models.request.RejectRequest
import com.vietinbank.core_data.models.request.SubTransactionRequest
import com.vietinbank.core_data.models.response.ApproveResponseData
import com.vietinbank.core_data.models.response.CountPendingResponseData
import com.vietinbank.core_data.models.response.GenKeyPassChallengeCodeResponseData
import com.vietinbank.core_data.models.response.GenSOTPTransCodeResponseData
import com.vietinbank.core_data.models.response.GetBatchTransactionListResponseData
import com.vietinbank.core_data.models.response.GetDownloadBase64FileResponseData
import com.vietinbank.core_data.models.response.GetDownloadFileIDResponseData
import com.vietinbank.core_data.models.response.GetTransactionDetailResponseData
import com.vietinbank.core_data.models.response.GetTransactionListResponseData
import com.vietinbank.core_data.models.response.MobileConfigLastResponseData
import com.vietinbank.core_data.models.response.PreApproveResponseData
import com.vietinbank.core_data.models.response.RejectResponseData
import com.vietinbank.core_data.models.response.SubTransactionResponseData
import com.vietinbank.core_data.network.api.ApiService
import com.vietinbank.core_data.network.api.IApiClient
import com.vietinbank.core_data.network.di.Default
import com.vietinbank.core_data.network.download.IFileDownloadClient
import com.vietinbank.core_domain.models.checker.ApproveDomain
import com.vietinbank.core_domain.models.checker.ApproveParams
import com.vietinbank.core_domain.models.checker.CountPendingDomain
import com.vietinbank.core_domain.models.checker.CountPendingParams
import com.vietinbank.core_domain.models.checker.GenKeyPassChallengeCodeDomain
import com.vietinbank.core_domain.models.checker.GenKeyPassChallengeCodeParams
import com.vietinbank.core_domain.models.checker.GenSOTPTransCodeDomain
import com.vietinbank.core_domain.models.checker.GenSOTPTransCodeParams
import com.vietinbank.core_domain.models.checker.GetBatchTransactionListDomain
import com.vietinbank.core_domain.models.checker.GetBatchTransactionListParams
import com.vietinbank.core_domain.models.checker.GetDownloadBase64FileDomain
import com.vietinbank.core_domain.models.checker.GetDownloadBase64FileParams
import com.vietinbank.core_domain.models.checker.GetDownloadFileIDDomain
import com.vietinbank.core_domain.models.checker.GetDownloadFileIDParams
import com.vietinbank.core_domain.models.checker.GetExportNSNNFileTemplateParams
import com.vietinbank.core_domain.models.checker.GetTransactionDetailDomain
import com.vietinbank.core_domain.models.checker.GetTransactionDetailParams
import com.vietinbank.core_domain.models.checker.GetTransactionListDomain
import com.vietinbank.core_domain.models.checker.GetTransactionListParams
import com.vietinbank.core_domain.models.checker.MobileConfigLastDomain
import com.vietinbank.core_domain.models.checker.MobileConfigLastParams
import com.vietinbank.core_domain.models.checker.PreApproveDomain
import com.vietinbank.core_domain.models.checker.PreApproveParams
import com.vietinbank.core_domain.models.checker.PreRejectParams
import com.vietinbank.core_domain.models.checker.RejectDomain
import com.vietinbank.core_domain.models.checker.RejectParams
import com.vietinbank.core_domain.models.checker.SubTransactionDomain
import com.vietinbank.core_domain.models.checker.SubTransactionParams
import com.vietinbank.core_domain.repository.CheckerRepository
import java.io.File
import javax.inject.Inject

/**
 * Created by vandz on 10/3/25.
 */
class CheckerRepositoryImpl @Inject constructor(
    @Default private val apiClient: IApiClient,
    private val apiService: ApiService,
    private val gsonProvider: GsonProvider,
    private val fileDownloadClient: IFileDownloadClient,
) : CheckerRepository {
    override suspend fun getTransactionList(params: GetTransactionListParams): Resource<GetTransactionListDomain> {
        val getTransactionListRequest = GetTransactionListRequest(
            username = params.username,
            groupType = params.groupType,
            serviceType = params.serviceType,
            tranType = params.tranType,
            content = params.content,
            fromAccountNo = params.fromAccountNo,
            fromDate = params.fromDate,
            maker = params.maker,
            maxAmount = params.maxAmount,
            minAmount = params.minAmount,
            orderByAmount = params.orderByAmount,
            orderByApproveDate = params.orderByApproveDate,
            pageNum = params.pageNum,
            pageSize = params.pageSize,
            receiveName = params.receiveName,
            receiveBankName = params.receiveBankName,
            toDate = params.toDate,
            toAcctNo = params.toAcctNo,
            all = params.all,
            mtId = params.mtId,
            isSchedule = params.isSchedule,
        )
        val result = apiClient.makeApiCall(
            request = getTransactionListRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<GetTransactionListResponseData>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val getTransactionListResponseData = base.data as? GetTransactionListResponseData
                    ?: return Resource.Error(
                        "Cannot parse to GetTransactionListResponseData",
                        "999",
                    )

                printLog("FirstItem: ${getTransactionListResponseData.transactions?.firstOrNull()}")
                // Map data model -> domain
                val getTransactionListDomain =
                    GetTransactionListResponseDataMapper.INSTANCE.toDomain(
                        getTransactionListResponseData,
                    )

                Resource.Success(getTransactionListDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun getTranList(params: GetTransactionListParams): Resource<GetTransactionListDomain> {
        val getTransactionListRequest = GetTranListRequest(
            mtId = params.mtId,
            username = params.username,
            groupType = params.groupType,
            serviceType = params.serviceType,
            tranType = params.tranType,
            content = params.content,
            fromAccountNo = params.fromAccountNo,
            fromDate = params.fromDate,
            maker = params.maker,
            maxAmount = params.maxAmount,
            minAmount = params.minAmount,
            orderByAmount = params.orderByAmount,
            orderByApproveDate = params.orderByApproveDate,
            pageNum = params.pageNum,
            pageSize = params.pageSize,
            receiveName = params.receiveName,
            receiveBankName = params.receiveBankName,
            toDate = params.toDate,
            toAcctNo = params.toAcctNo,
            all = params.all,
            isSchedule = params.isSchedule,
            tranSubType = params.tranSubType,
        )
        val result = apiClient.makeApiCall(
            request = getTransactionListRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<GetTransactionListResponseData>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val getTransactionListResponseData = base.data as? GetTransactionListResponseData
                    ?: return Resource.Error(
                        "Cannot parse to GetTransactionListResponseData",
                        "999",
                    )

                printLog("FirstItem: ${getTransactionListResponseData.transactions?.firstOrNull()}")
                // Map data model -> domain
                val getTransactionListDomain =
                    GetTransactionListResponseDataMapper.INSTANCE.toDomain(
                        getTransactionListResponseData,
                    )

                Resource.Success(getTransactionListDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun getTransactionDetail(params: GetTransactionDetailParams): Resource<GetTransactionDetailDomain> {
        val request = GetTransactionDetailRequest(
            mtId = params.mtId,
            username = params.username,
            serviceType = params.serviceType,
            signType = params.signType,
            trantype = params.tranType,
//            type = params.type,
        )
        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<GetTransactionDetailResponseData>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val getTransactionDetailResponseData =
                    base.data as? GetTransactionDetailResponseData
                        ?: return Resource.Error(
                            "Cannot parse to GetTransactionDetailResponseData",
                            "999",
                        )
                // Map data model -> domain
                val getTransactionListDomain =
                    GetTransactionDetailResponseDataMapper.INSTANCE.toDomain(
                        getTransactionDetailResponseData,
                    )

                Resource.Success(getTransactionListDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun countPending(params: CountPendingParams): Resource<CountPendingDomain> {
        val countPendingRequest = CountPendingRequest(
            username = params.userName,
            role = params.role,
        )
        val result = apiClient.makeApiCall(
            request = countPendingRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<CountPendingResponseData>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val countPendingResponseData = base.data as? CountPendingResponseData
                    ?: return Resource.Error("Cannot parse to CountPendingResponseData", "999")

                printLog("FirstItem: ${countPendingResponseData.countTransactionList?.firstOrNull()}")
                // Map data model -> domain
                val countPendingDomain =
                    CountPendingResponseDataMapper.INSTANCE.toDomain(countPendingResponseData)

                Resource.Success(countPendingDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun mobileConfigLast(params: MobileConfigLastParams): Resource<MobileConfigLastDomain> {
        val mobileConfigLastRequest = MobileConfigLastRequest(
            username = params.username,
            lastModified = params.lastModified,
        ).apply {
            channel = "MobileNew"
        }
        val result = apiClient.makeApiCall(
            request = mobileConfigLastRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<MobileConfigLastResponseData>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val mobileConfigLastResponseData = base.data as? MobileConfigLastResponseData
                    ?: return Resource.Error("Cannot parse to CountPendingResponseData", "999")

                // Map data model -> domain
                val mobileConfigLastDomain =
                    MobileConfigLastResponseDataMapper.INSTANCE.toDomain(mobileConfigLastResponseData)

                Resource.Success(mobileConfigLastDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun preApprove(params: PreApproveParams): Resource<PreApproveDomain> {
        val preApproveRequest = PreApproveRequest(
            username = params.userName,
            serviceType = params.serviceType,
            tranType = params.tranType,
            serviceId = params.serviceId,
            pereAppTp = arrayListOf(),
            transactions = params.transactions,
        )
        val result = apiClient.makeApiCall(
            request = preApproveRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<PreApproveResponseData>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val preApproveResponseData = base.data as? PreApproveResponseData
                    ?: return Resource.Error("Cannot parse to PreApproveResponseData", "999")

                // Map data model -> domain
                val preApproveDomain =
                    PreApproveResponseDataMapper.INSTANCE.toDomain(preApproveResponseData)

                Resource.Success(preApproveDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun doApprove(params: ApproveParams): Resource<ApproveDomain> {
        val doApproveRequest = ApproveRequest(
            username = params.username,
            authenType = params.authenType,
            serviceType = params.serviceType,
            requestType = params.requestType,
            softOtpTransId = params.softOtpTransId,
            token = params.token,
            tranType = params.tranType,
            transactions = params.transactions,
            transactionsTp = arrayListOf(),
            nextApprovers = params.nextApprovers,
        )
        val result = apiClient.makeApiCall(
            request = doApproveRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<ApproveResponseData>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val approveResponseData = base.data as? ApproveResponseData
                    ?: return Resource.Error("Cannot parse to ApproveResponseData", "999")

                // Map data model -> domain
                val approveDomain =
                    ApproveResponseDataMapper.INSTANCE.toDomain(approveResponseData)

                Resource.Success(approveDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun preReject(params: PreRejectParams): Resource<PreApproveDomain> {
        val preRejectRequest = PreRejectRequest(
            username = params.username,
            serviceType = params.serviceType,
            tranType = params.tranType,
            serviceId = params.serviceId,
            pereAppTp = arrayListOf(),
            transactions = params.transactions,
        )
        val result = apiClient.makeApiCall(
            request = preRejectRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<PreApproveResponseData>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val preRejectResponseData = base.data as? PreApproveResponseData
                    ?: return Resource.Error("Cannot parse to PreRejectResponseData", "999")

                // Map data model -> domain
                val preRejectDomain =
                    PreApproveResponseDataMapper.INSTANCE.toDomain(preRejectResponseData)

                Resource.Success(preRejectDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun doReject(params: RejectParams): Resource<RejectDomain> {
        val doRejectRequest = RejectRequest(
            reason = params.reason,
            username = params.username,
            authType = params.authenType,
            serviceType = params.serviceType,
            requestType = params.requestType,
            softOtpTransId = params.softOtpTransId,
            token = params.token,
            tranType = params.tranType,
            transactions = params.transactions,
            serviceId = params.serviceId,
            transactionsTp = arrayListOf(),
        )
        val result = apiClient.makeApiCall(
            request = doRejectRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<RejectResponseData>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val rejectResponseData = base.data as? RejectResponseData
                    ?: return Resource.Error("Cannot parse to RejectResponseData", "999")

                // Map data model -> domain
                val rejectDomain =
                    RejectResponseDataMapper.INSTANCE.toDomain(rejectResponseData)

                Resource.Success(rejectDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun genSOTPTransCode(params: GenSOTPTransCodeParams): Resource<GenSOTPTransCodeDomain> {
        val genSOTPTransCodeRequest = GenSOTPTransCodeRequest(
            username = params.userName,
            actionId = params.actionId,
            groupType = params.groupType,
            mtIds = params.mtIds,
            reason = params.reason,
            transactionData = params.transactionData,
            trxType = params.trxType,
        )
        val result = apiClient.makeApiCall(
            request = genSOTPTransCodeRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<GenSOTPTransCodeResponseData>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val genSOTPTransCodeResponseData = base.data as? GenSOTPTransCodeResponseData
                    ?: return Resource.Error("Cannot parse to GenSOTPTransCodeResponseData", "999")

                // Map data model -> domain
                val genSOTPTransCodeDomain =
                    GenSOTPTransCodeResponseDataMapper.INSTANCE.toDomain(
                        genSOTPTransCodeResponseData,
                    )

                Resource.Success(genSOTPTransCodeDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun genKeypassChallengeCode(params: GenKeyPassChallengeCodeParams): Resource<GenKeyPassChallengeCodeDomain> {
        val genKeyPassChallengeCodeRequest = GenKeyPassChallengeCodeRequest(
            username = params.username,
        )
        val result = apiClient.makeApiCall(
            request = genKeyPassChallengeCodeRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<GenKeyPassChallengeCodeResponseData>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val genKeyPassChallengeCodeResponseData =
                    base.data as? GenKeyPassChallengeCodeResponseData
                        ?: return Resource.Error(
                            "Cannot parse to GenKeyPassChallengeCodeResponseData",
                            "999",
                        )

                // Map data model -> domain
                val genKeyPassChallengeCodeDomain =
                    GenKeyPassChallengeCodeResponseDataMapper.INSTANCE.toDomain(
                        genKeyPassChallengeCodeResponseData,
                    )

                Resource.Success(genKeyPassChallengeCodeDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun getBathTransactionList(params: GetBatchTransactionListParams): Resource<GetBatchTransactionListDomain> {
        val getBatchTransactionListRequest = GetBatchTransactionListRequest(
            username = params.username,
            all = params.all,
            orderByAmount = params.orderByAmount,
            orderByApproveDate = params.orderByApproveDate,
            pageNum = params.pageNum,
            pageSize = params.pageSize,
            transactionId = params.transactionId,
        )
        val result = apiClient.makeApiCall(
            request = getBatchTransactionListRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<GetBatchTransactionListResponseData>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val getBatchTransactionListResponseData =
                    base.data as? GetBatchTransactionListResponseData
                        ?: return Resource.Error(
                            "Cannot parse to GetBatchTransactionListResponseData",
                            "999",
                        )

                // Map data model -> domain
                val getBatchTransactionListDomain =
                    GetBatchTransactionListResponseDataMapper.INSTANCE.toDomain(
                        getBatchTransactionListResponseData,
                    )

                Resource.Success(getBatchTransactionListDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun getDownloadFileID(params: GetDownloadFileIDParams): Resource<GetDownloadFileIDDomain> {
        val getDownloadFileRequest = GetDownloadFileIDRequest(
            username = params.username,
            fileId = params.fileId,
            mtID = params.mtID,
            tranType = params.tranType,
        )
        val result = apiClient.makeApiCall(
            request = getDownloadFileRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<GetDownloadFileIDResponseData>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val getDownloadFileIDResponseData =
                    base.data as? GetDownloadFileIDResponseData
                        ?: return Resource.Error(
                            "Cannot parse to GetDownloadFileIDResponseData",
                            "999",
                        )

                // Map data model -> domain
                val getDownloadFileIDDomain =
                    GetDownloadFileIDResponseDataMapper.INSTANCE.toDomain(
                        getDownloadFileIDResponseData,
                    )

                Resource.Success(getDownloadFileIDDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun getDownloadBase64File(params: GetDownloadBase64FileParams): Resource<GetDownloadBase64FileDomain> {
        val getDownloadFileRequest = GetDownloadBase64FileRequest(
            username = params.username,
            type = params.type,
            mtId = params.mtId,
            signType = params.signType,
        )
        val result = apiClient.makeApiCall(
            request = getDownloadFileRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<GetDownloadBase64FileResponseData>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val getDownloadBase64FileResponseData =
                    base.data as? GetDownloadBase64FileResponseData
                        ?: return Resource.Error(
                            "Cannot parse to GetDownloadBase64FileResponseData",
                            "999",
                        )

                // Map data model -> domain
                val getDownloadBase64FileDomain =
                    GetDownloadBase64FileResponseDataMapper.INSTANCE.toDomain(
                        getDownloadBase64FileResponseData,
                    )

                Resource.Success(getDownloadBase64FileDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun getExportNSNNFileTemplateParams(params: GetExportNSNNFileTemplateParams): Resource<GetDownloadBase64FileDomain> {
        val getDownloadFileRequest = GetExportNSNNFileTemplateRequest(
            username = params.username,
            type = params.type,
            mtId = params.mtId,
        )
        val result = apiClient.makeApiCall(
            request = getDownloadFileRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<GetDownloadBase64FileResponseData>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val getDownloadBase64FileResponseData =
                    base.data as? GetDownloadBase64FileResponseData
                        ?: return Resource.Error(
                            "Cannot parse to GetExportNSNNFileTemplateParams",
                            "999",
                        )

                // Map data model -> domain
                val getDownloadBase64FileDomain =
                    GetDownloadBase64FileResponseDataMapper.INSTANCE.toDomain(
                        getDownloadBase64FileResponseData,
                    )

                Resource.Success(getDownloadBase64FileDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun getSubTransactionDetail(params: SubTransactionParams): Resource<SubTransactionDomain> {
        val request = SubTransactionRequest(
            hostMtId = params.hostMtId,
            username = params.username,
        )
        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<SubTransactionResponseData>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val responseData =
                    base.data as? SubTransactionResponseData ?: return Resource.Error(
                        "Cannot parse to SubTransactionResponseData",
                        "999",
                    )

                // Map data model -> domain
                val getDownloadFileIDDomain =
                    GetTransactionDetailResponseDataMapper.INSTANCE.toDomain(
                        responseData,
                    )

                Resource.Success(getDownloadFileIDDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun downloadPdfFile(url: String): Resource<File> {
        return fileDownloadClient.downloadFileToCache(
            url = url,
            fileName = "document_${System.currentTimeMillis()}.pdf",
        )
    }
}
