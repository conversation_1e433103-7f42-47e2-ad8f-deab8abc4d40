package com.vietinbank.core_data.database.ott.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.vietinbank.core_data.database.ott.entity.DeviceTokenEntity
import kotlinx.coroutines.flow.Flow

/**
 * Created by vand<PERSON> on 16/4/25.
 * suspend cho phép gọi các phương thức này trong coroutine.
 * Flow trong các phương thức query cho phép observe thay đổi dữ liệu theo thời gian thực.
 */
@Dao
interface DeviceTokenDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveToken(token: DeviceTokenEntity)

    @Query("SELECT * FROM device_tokens WHERE id = 1")
    suspend fun getDeviceToken(): DeviceTokenEntity?

    @Query("SELECT * FROM device_tokens WHERE id = 1")
    fun observeDeviceToken(): Flow<DeviceTokenEntity?>

    @Query("UPDATE device_tokens SET activeCif = :cif, activePhone = :phone, lastUpdated = :timestamp WHERE id = 1")
    suspend fun updateActivePair(
        cif: String,
        phone: String,
        timestamp: Long = System.currentTimeMillis(),
    )
}
