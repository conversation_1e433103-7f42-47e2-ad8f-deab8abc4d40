package com.vietinbank.core_data.database.ott

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import com.vietinbank.core_data.database.ott.dao.DeviceTokenDao
import com.vietinbank.core_data.database.ott.dao.OttMessageDao
import com.vietinbank.core_data.database.ott.entity.DeviceTokenEntity
import com.vietinbank.core_data.database.ott.entity.OttMessageEntity

/**
 * Created by vandz on 16/4/25.
 */
@Database(
    entities = [OttMessageEntity::class, DeviceTokenEntity::class],
    version = 4,
    exportSchema = false,
)
abstract class OttDatabase : RoomDatabase() {
    abstract fun ottMessageDao(): OttMessageDao
    abstract fun deviceTokenDao(): DeviceTokenDao

    companion object {
        @Volatile
        private var INSTANCE: OttDatabase? = null

        fun getDatabase(context: Context): OttDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    OttDatabase::class.java,
                    "ott_database",
                )
                    .fallbackToDestructiveMigration()
                    .build()
                INSTANCE = instance
                instance
            }
        }
    }
}
