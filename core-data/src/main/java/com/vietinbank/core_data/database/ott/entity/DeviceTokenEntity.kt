package com.vietinbank.core_data.database.ott.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * Created by vandz on 16/4/25.
 */
@Entity(tableName = "device_tokens")
data class DeviceTokenEntity(
    @PrimaryKey
    val id: Int = 1, // Single instance
    val token: String,
    val activeCif: String? = null,
    val activePhone: String? = null,
    val lastUpdated: Long = System.currentTimeMillis(),
)
