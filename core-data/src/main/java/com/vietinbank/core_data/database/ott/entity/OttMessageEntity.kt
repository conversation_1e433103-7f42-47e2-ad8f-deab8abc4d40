package com.vietinbank.core_data.database.ott.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.vietinbank.core_data.database.ott.converter.DateConverter
import java.util.Date

/**
 * Created by vandz on 15/4/25.
 */

@Entity(tableName = "ott_messages")
@TypeConverters(DateConverter::class)
data class OttMessageEntity(
    @PrimaryKey
    val messageId: String,
    val title: String,
    val content: String,
    val cif: String,
    val phone: String,
    val status: Int = MESSAGE_STATUS_UNREAD,
    val sendTime: String, // Format: "dd/MM/yyyy HH:mm:ss"
    val messageType: String,
    val readTime: Date? = null,
    var data: String? = null, // JSON string với thông tin bổ sung
    var priority: String? = null,
    val timestamp: Long = 0L, // Added for proper date sorting
) {
    companion object {
        const val MESSAGE_STATUS_UNREAD = 0
        const val MESSAGE_STATUS_READ = 1
        const val MESSAGE_STATUS_DELETED = 2
    }
}
