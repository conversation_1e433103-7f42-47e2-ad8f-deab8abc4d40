package com.vietinbank.core_data.database.ott.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.vietinbank.core_data.database.ott.entity.OttMessageEntity
import kotlinx.coroutines.flow.Flow

/**
 * Created by vandz on 16/4/25.
 */
@Dao
interface OttMessageDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMessage(message: OttMessageEntity)

    // Bulk insert method
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMessages(messages: List<OttMessageEntity>)

    @Update
    suspend fun updateMessage(message: OttMessageEntity)

    @Query("DELETE FROM ott_messages WHERE cif = :cif AND phone = :phone")
    suspend fun deleteAllMessagesForUser(cif: String, phone: String)

    @Query("SELECT * FROM ott_messages WHERE cif = :cif AND phone = :phone ORDER BY timestamp DESC, sendTime DESC LIMIT 1")
    suspend fun getLatestMessage(cif: String, phone: String): OttMessageEntity?

    @Query("SELECT * FROM ott_messages WHERE cif = :cif AND phone = :phone ORDER BY timestamp DESC, sendTime DESC LIMIT :limit OFFSET :offset")
    suspend fun getMessagesWithPaging(cif: String, phone: String, limit: Int, offset: Int): List<OttMessageEntity>

    @Query("DELETE FROM ott_messages WHERE cif = :cif AND phone = :phone AND timestamp < :beforeTime")
    suspend fun deleteOldMessages(cif: String, phone: String, beforeTime: Long)

    @Query("SELECT * FROM ott_messages WHERE cif = :cif AND phone = :phone ORDER BY timestamp DESC, sendTime DESC")
    fun getMessagesForUser(cif: String, phone: String): Flow<List<OttMessageEntity>>

    @Query("SELECT * FROM ott_messages WHERE messageId = :messageId")
    suspend fun getMessageById(messageId: String): OttMessageEntity?

    @Query("UPDATE ott_messages SET status = :status, readTime = :readTime WHERE messageId = :messageId")
    suspend fun updateMessageStatus(
        messageId: String,
        status: Int,
        readTime: Long = System.currentTimeMillis(),
    )

    @Query("SELECT COUNT(*) FROM ott_messages WHERE cif = :cif AND phone = :phone AND status = 0")
    fun getUnreadMessageCount(cif: String, phone: String): Flow<Int>

    @Query("SELECT COUNT(*) > 0 FROM ott_messages WHERE cif != :cif LIMIT 1")
    suspend fun hasMessagesFromDifferentUser(cif: String): Boolean

    @Query("DELETE FROM ott_messages")
    suspend fun deleteAllMessages()

    @Query("SELECT messageId FROM ott_messages WHERE messageId IN (:messageIds)")
    suspend fun getExistingMessageIds(messageIds: List<String>): List<String>

    @Query("SELECT * FROM ott_messages WHERE cif = :cif AND phone = :phone AND timestamp > :cutoffTime")
    suspend fun getRecentMessagesForUser(cif: String, phone: String, cutoffTime: Long): List<OttMessageEntity>
}
