package com.vietinbank.core_data.database.ott.converter

import androidx.room.TypeConverter
import java.util.Date

/**
 * Created by vandz on 16/4/25.
 * Room chỉ lưu trữ các kiểu dữ liệu cơ bản (primitive types, String).
 * Type Converter giúp chuyển đổi giữa các kiểu dữ liệu phức tạp (như Date) và kiểu dữ liệu mà Room có thể lưu trữ (như Long).
 * Phải đăng ký converter với @TypeConverters trên Entity hoặc Database.
 */
class DateConverter {
    @TypeConverter
    fun fromTimestamp(value: Long?): Date? {
        return value?.let { Date(it) }
    }

    @TypeConverter
    fun dateToTimestamp(date: Date?): Long? {
        return date?.time
    }
}
