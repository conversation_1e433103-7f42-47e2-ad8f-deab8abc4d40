package com.vietinbank.core_data.network.api

import com.google.gson.JsonParser
import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.utils.GsonProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_data.encryption.EncryptionService
import com.vietinbank.core_data.models.base.BaseResponse
import com.vietinbank.core_data.models.base.Status
import com.vietinbank.core_data.models.request.CommonRequest
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import retrofit2.Response
import java.net.SocketException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import javax.net.ssl.SSLPeerUnverifiedException

/**
 * Created by vandz on 25/12/24.
 *
 * API client implementation for making API calls with encryption.
 *
 * @param encryptionService the encryption service for encrypting the request body and
 * decrypting the response body.
 * @param gsonProvider the gson provider for converting the request body to a JSON string.
 * @param parseType the type token for parsing the response body to a specific type.
 */
interface IApiClient {
    suspend fun <Req, Res> makeApiCall(
        request: Req,
        apiCall: suspend (RequestBody) -> Response<BaseResponse<Any>>,
        parseType: TypeToken<Res>,
    ): Resource<BaseResponse<Any>>
}

class ApiClient(
    private val encryptionService: EncryptionService,
    private val gsonProvider: GsonProvider,
    private val sessionManager: ISessionManager,
) : IApiClient {

    private fun <T> createErrorResponse(
        requestId: String,
        code: String,
        message: String,
    ): BaseResponse<Any> {
        return BaseResponse(
            requestId = requestId,
            sessionId = "",
            status = Status(code = code, message = message, subCode = ""),
            message = "",
        )
    }

    /**
     * Makes an API call with encryption.
     *
     * Encrypts the request body, makes the API call and decrypts the response body.
     *
     * @param request the request body to encrypt and send to the server.
     * @param apiCall the API call to make.
     * @param parseType the type token for parsing the response body to a specific type.
     * @return a Resource containing the decrypted response body.
     */
    override suspend fun <Req, Res> makeApiCall(
        request: Req,
        apiCall: suspend (RequestBody) -> Response<BaseResponse<Any>>,
        parseType: TypeToken<Res>,
    ): Resource<BaseResponse<Any>> {
        val commonRequest = request as CommonRequest
        val currentRequestId = commonRequest.requestId ?: ""
        return try {
            printLog("Original Request: $request")

            // Update sessionId if the request is a CommonRequest
            val sessionId = sessionManager.getSessionId() ?: ""
            commonRequest.sessionId = sessionId

            // Lưu differentiateKey được tạo khi encrypt
            val differentiateKey =
                encryptionService.generateDifferentiateKey(currentRequestId)

            // 1. Mã hóa request
            val encryptedRequest = encryptionService.encryptRequest(request, differentiateKey)

            val requestBody = gsonProvider.provideGson()
                .toJson(encryptedRequest)
                .toRequestBody("application/json".toMediaTypeOrNull())

            // 2. Gọi API
            printLog("Calling ${commonRequest.requestPath} with requestID: $currentRequestId...")
            val response = apiCall(requestBody)
            printLog("Request with requestID: $currentRequestId call completed!")

            // 3. Xử lý phản hồi
            if (response.isSuccessful) {
                val baseResponse = response.body()
                    ?: return Resource.Error(
                        "Empty response body",
                        "123",
                        AppException.HttpException(
                            httpCode = response.code(),
                            errorBody = response.body().toString(),
                            message = "API call failed: ${response.message()}",
                        ),
                    )

                // Giải mã message từ BaseResponse
                val decryptedResponse =
                    decryptResponse(baseResponse.message ?: "", differentiateKey)
                printLog("Decrypted Response in JSON: $decryptedResponse")

                // Chỉ lấy status từ JSON đã giải mã để kiểm tra
                val decryptedStatus = extractStatusFromJson(decryptedResponse)

                // Parse dữ liệu đã giải mã thành đối tượng Res
                val result = parseDecryptedData(decryptedResponse, parseType)
                val finalResult = baseResponse.copy(data = result)
                printLog("Parsed Response to Object: ${finalResult.data}")

                when (decryptedStatus.code) {
                    "1" -> {
                        Resource.Success(finalResult)
                    }

                    else -> {
                        Resource.Error(
                            decryptedStatus.message ?: "",
                            decryptedStatus.code ?: "",
                            AppException.ApiException(
                                code = decryptedStatus.code,
                                subCode = decryptedStatus.subCode,
                                requestPath = commonRequest.requestPath,
                                rawResponseJson = decryptedResponse,
                                message = decryptedStatus.message ?: "",
                                requestId = currentRequestId,
                            ),
                        )
                    }
                }
            } else {
                // Log error body nếu có
                val errorBody = response.errorBody()?.string()
                printLog("errorBody: $errorBody ")
                // Xử lý lỗi từ server
                Resource.Error(
                    response.message(),
                    response.code().toString(),
                    AppException.HttpException(
                        httpCode = response.code(),
                        errorBody = errorBody,
                        message = "API call failed: ${response.message()}",
                    ),
                )
            }
        } catch (e: Exception) {
            // Map các exception thành AppException
            val requestId = (request as? CommonRequest)?.requestId ?: ""
            printLog("API CLIENT e: $e")
            val errorResponse = when (e) {
                is UnknownHostException -> createErrorResponse<Any>(
                    requestId = requestId,
                    code = "998",
                    message = "Kiểm tra kết nối mạng",
                )

                is SocketTimeoutException -> createErrorResponse<Any>(
                    requestId = requestId,
                    code = "996",
                    message = "Connection timeout",
                )

                is SSLPeerUnverifiedException -> createErrorResponse<Any>(
                    requestId = requestId,
                    code = "995",
                    message = "SSL certificate not verified",
                )

                is AppException.EncryptionException -> createErrorResponse<Any>(
                    requestId = requestId,
                    code = "997",
                    message = "Lỗi trong quá trình mã hóa",
                )

                else -> createErrorResponse<Any>(
                    requestId = requestId,
                    code = "999",
                    message = e.message ?: "Lỗi không xác định",
                )
            }
            val appException = when (e) {
                is UnknownHostException -> AppException.NetworkException(
                    "Network error: Unable to resolve host.",
                    e,
                    requestId = requestId,
                    code = "998",
                )

                is SocketException -> AppException.NetworkException(
                    "Network error: Socket Exception.",
                    e,
                    requestId = requestId,
                    code = "994",
                )

                is SocketTimeoutException -> AppException.NetworkException(
                    "Network error: Connection timed out.",
                    e,
                    requestId = requestId,
                    code = "996",
                )

                is SSLPeerUnverifiedException -> AppException.NetworkException(
                    "Network error: SSL certificate not verified.",
                    e,
                    requestId = requestId,
                    code = "995",
                )

                is AppException.EncryptionException -> AppException.EncryptionException(
                    "Failed to encrypt/decrypt data.",
                    e,
                    requestId = requestId,
                    code = "997",
                )

                else -> AppException.UnknownException("An unexpected error occurred.", e, requestId = requestId, code = "999")
            }
            Resource.Error(
                errorResponse.status?.message ?: "",
                errorResponse.status?.code ?: "",
                appException,
            )
        }
    }

    /**
     * Handles an exception that occurred while making an API call.
     * Maps the exception to an AppException and returns a Resource.Error containing the AppException.
     * The AppException is created based on the type of exception that occurred.
     * If the exception is not one of the handled types, it creates an AppException.UnknownException with a generic error message.
     */
    private fun decryptResponse(encryptedValue: String, differentiateKey: String): String {
        try {
            // 5. Giải mã "message" sử dụng differentiateKey
            val decryptedMessage = encryptionService.decrypt(encryptedValue, differentiateKey)
            val unescapedString = decryptedMessage.replace("\\\"", "\"")
//            printLog("Decrypted Message: $unescapedString")

            // 6. Trả về JSON đã được giải mã dưới dạng chuỗi
            return unescapedString
        } catch (e: Exception) {
            printLog("Error decrypting response: ${e.message}")
            throw e
        }
    }

    // Catch all exceptions that occurred while decrypting the response.
    // Log the error with the decrypted message and rethrow the exception.
    private fun <T> parseDecryptedData(json: String, parseType: TypeToken<T>): T {
        return gsonProvider.provideGson().fromJson(json, parseType.type)
    }

    /**
     * Parses the decrypted response JSON string into an object of type T.
     * Uses the provided Gson instance to parse the JSON string.
     * @param json The decrypted response JSON string.
     * @return An object of type T parsed from the JSON string.
     */
    private fun extractStatusFromJson(json: String): Status {
        // Sử dụng JsonParser để chỉ lấy phần status từ JSON string
        val jsonObject = JsonParser.parseString(json).asJsonObject
        val statusObject = jsonObject.getAsJsonObject("status")
        return gsonProvider.provideGson().fromJson(statusObject, Status::class.java)
    }
}
