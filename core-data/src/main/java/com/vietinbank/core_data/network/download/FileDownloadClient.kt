package com.vietinbank.core_data.network.download

import android.content.Context
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.utils.GsonProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_data.network.api.ApiService
import dagger.hilt.android.qualifiers.ApplicationContext
import okhttp3.ResponseBody
import retrofit2.Response
import java.io.File
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import java.util.UUID
import javax.inject.Inject
import javax.net.ssl.SSLPeerUnverifiedException

/**
 * Interface for handling file downloads
 */
interface IFileDownloadClient {
    /**
     * Download file and return as ByteArray
     * @param url The URL to download from
     * @return Resource containing ByteArray or error
     */
    suspend fun downloadFile(url: String): Resource<ByteArray>

    /**
     * Download file and save to cache directory
     * @param url The URL to download from
     * @param fileName Optional file name, will generate timestamp-based name if null
     * @return Resource containing File or error
     */
    suspend fun downloadFileToCache(url: String, fileName: String? = null): Resource<File>
}

/**
 * Implementation of file download client with consistent error handling
 */
class FileDownloadClient @Inject constructor(
    private val apiService: ApiService,
    private val gsonProvider: GsonProvider,
    @ApplicationContext private val context: Context,
) : IFileDownloadClient {

    override suspend fun downloadFile(url: String): Resource<ByteArray> {
        val requestId = UUID.randomUUID().toString()
        printLog("FileDownloadClient: Downloading from $url with requestId: $requestId")

        return try {
            val response = apiService.downloadFile(url)
            handleResponse(response, requestId)
        } catch (e: Exception) {
            printLog("FileDownloadClient: Download failed - ${e.message}")
            handleException(e, requestId)
        }
    }

    override suspend fun downloadFileToCache(url: String, fileName: String?): Resource<File> {
        return when (val result = downloadFile(url)) {
            is Resource.Success -> {
                try {
                    val file = saveToCache(result.data, fileName)
                    printLog("FileDownloadClient: File saved to ${file.absolutePath}")
                    Resource.Success(file)
                } catch (e: Exception) {
                    printLog("FileDownloadClient: Failed to save file - ${e.message}")
                    Resource.Error(
                        "Failed to save file",
                        "999",
                        AppException.UnknownException(
                            message = "File save failed",
                            cause = e,
                            requestId = UUID.randomUUID().toString(),
                        ),
                    )
                }
            }
            is Resource.Error -> result
        }
    }

    private suspend fun handleResponse(
        response: Response<ResponseBody>,
        requestId: String,
    ): Resource<ByteArray> {
        return if (response.isSuccessful) {
            response.body()?.let { body ->
                try {
                    val bytes = body.bytes()
                    printLog("FileDownloadClient: Downloaded ${bytes.size} bytes")
                    Resource.Success(bytes)
                } catch (e: Exception) {
                    Resource.Error(
                        "Failed to read response body",
                        "999",
                        AppException.UnknownException(
                            message = "Failed to read response",
                            cause = e,
                            requestId = requestId,
                        ),
                    )
                }
            } ?: Resource.Error(
                "Empty response body",
                "999",
                AppException.UnknownException(
                    message = "Empty response",
                    requestId = requestId,
                ),
            )
        } else {
            val errorBody = try {
                response.errorBody()?.string()
            } catch (e: Exception) {
                null
            }

            Resource.Error(
                "Download failed: ${response.message()}",
                response.code().toString(),
                AppException.HttpException(
                    httpCode = response.code(),
                    errorBody = errorBody,
                    message = "Download failed: ${response.message()}",
                    requestId = requestId,
                ),
            )
        }
    }

    private fun handleException(e: Exception, requestId: String): Resource<ByteArray> {
        val appException = when (e) {
            is UnknownHostException -> AppException.NetworkException(
                message = "Network error: Unable to resolve host",
                cause = e,
                requestId = requestId,
                code = "998",
            )
            is SocketTimeoutException -> AppException.NetworkException(
                message = "Network error: Connection timeout",
                cause = e,
                requestId = requestId,
                code = "996",
            )
            is SSLPeerUnverifiedException -> AppException.NetworkException(
                message = "Network error: SSL certificate not verified",
                cause = e,
                requestId = requestId,
                code = "995",
            )
            else -> AppException.UnknownException(
                message = "Download failed: ${e.message}",
                cause = e,
                requestId = requestId,
                code = "999",
            )
        }

        return Resource.Error(
            appException.message ?: "Download failed",
            when (appException) {
                is AppException.NetworkException -> appException.code ?: "999"
                is AppException.UnknownException -> appException.code ?: "999"
                else -> "999"
            },
            appException,
        )
    }

    private fun saveToCache(data: ByteArray, fileName: String?): File {
        val name = fileName ?: "download_${System.currentTimeMillis()}"
        val file = File(context.cacheDir, name)
        file.writeBytes(data)
        return file
    }
}