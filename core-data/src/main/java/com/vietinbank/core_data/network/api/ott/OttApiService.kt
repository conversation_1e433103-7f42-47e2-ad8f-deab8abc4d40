package com.vietinbank.core_data.network.api.ott

import com.vietinbank.core_data.models.base.BaseResponse
import okhttp3.RequestBody
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Created by vandz on 15/4/25.
 */
interface OttApiService {
    @POST("/mobwebviewservice/efast/mb/decode/forwarder/")
    suspend fun callSecureApi(@Body requestBody: RequestBody): Response<BaseResponse<Any>>
}
