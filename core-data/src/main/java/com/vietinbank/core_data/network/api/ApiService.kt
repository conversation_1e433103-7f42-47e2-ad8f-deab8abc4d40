package com.vietinbank.core_data.network.api

import com.vietinbank.core_data.models.base.BaseResponse
import okhttp3.RequestBody
import okhttp3.ResponseBody
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Streaming
import retrofit2.http.Url

/**
 * Created by vandz on 18/12/24.
 */

/**
 * Interface defining the API endpoints for network communication.
 * Uses Retrofit annotations to define HTTP requests and their configurations.
 *
 * Features:
 * - Suspend functions for coroutine support
 * - Retrofit Response wrapper for handling HTTP responses
 * - Generic BaseResponse for type-safe API responses
 */
interface ApiService {
    /**
     * Makes a POST request to the SIT environment forwarder endpoint.
     * This endpoint handles encrypted request forwarding to appropriate services.
     *
     * @param requestBody The encrypted request payload
     * @return Response wrapped BaseResponse containing the decrypted API response
     * @throws retrofit2.HttpException if the request fails
     * @throws IOException if there's a network error
     */
    @POST("/mobwebviewservice/efast/mb/decode/forwarder/")
    suspend fun callSITEnv(@Body requestBody: RequestBody): Response<BaseResponse<Any>>

    @Streaming
    @GET
    suspend fun downloadFile(@Url fileUrl: String): Response<ResponseBody>
}
