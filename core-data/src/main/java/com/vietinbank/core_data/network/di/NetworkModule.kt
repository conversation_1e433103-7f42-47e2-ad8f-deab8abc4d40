package com.vietinbank.core_data.network.di

import android.content.Context
import com.vietinbank.core_common.constants.DataSourceProperties
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.utils.GsonProvider
import com.vietinbank.core_data.encryption.EncryptionService
import com.vietinbank.core_data.network.api.ApiClient
import com.vietinbank.core_data.network.api.ApiService
import com.vietinbank.core_data.network.api.IApiClient
import com.vietinbank.core_data.network.download.FileDownloadClient
import com.vietinbank.core_data.network.download.IFileDownloadClient
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import okhttp3.CertificatePinner
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit
import javax.inject.Singleton

/**
 * Module quản lý các dependency liên quan đến networking trong ứng dụng.
 *
 * Module này được tổ chức theo kiến trúc phân tầng, từ low-level networking
 * đến high-level API clients. Mỗi tầng được thiết kế để hoạt động độc lập
 * và có thể được thay thế hoặc mở rộng dễ dàng.
 *
 * Cấu trúc phân tầng:
 * 1. Network Layer: Cấu hình cơ bản cho HTTP client
 *    - Logging
 *    - Certificate Pinning
 *    - OkHttpClient
 *    - Retrofit setup
 *
 * 2. API Service Layer: Interface cho các API endpoints
 *    - Retrofit service definitions
 *
 * 3. API Client Layer: Wrapper cho API calls
 *    - Default client (với mã hóa)
 *    - NoEncryption client (không mã hóa)
 *
 * Tất cả các dependency được cung cấp là Singleton để đảm bảo
 * hiệu suất và tính nhất quán của network calls.
 *
 * @see ApiService
 * @see IApiClient
 * @see Default
 * @see NoEncryption
 */
@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {

    //region 1. Network Layer
    /**
     * Cung cấp HTTP logging interceptor cho debug.
     */
    @Singleton
    @Provides
    fun provideLoggingInterceptor(): HttpLoggingInterceptor {
        return HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }
    }

    /**
     * Cung cấp certificate pinner cho HTTPS security.
     *
     * @param dataSourceProperties Properties chứa thông tin certificates
     */
    @Singleton
    @Provides
    fun provideCertificatePinner(dataSourceProperties: DataSourceProperties): CertificatePinner {
        return CertificatePinner.Builder()
            .apply {
                repeat(dataSourceProperties.getCerts().size) {
                    add(
                        dataSourceProperties.getDomain(),
                        "sha256/${dataSourceProperties.getCerts()[it]}",
                    )
                }
            }
            .build()
    }

    /**
     * Cấu hình và cung cấp OkHttpClient với logging và certificate pinning.
     *
     * @param loggingInterceptor Interceptor cho logging
     * @param certificatePinner Certificate pinner cho security
     */
    @Singleton
    @Provides
    fun provideOkHttpClient(
        loggingInterceptor: HttpLoggingInterceptor,
        certificatePinner: CertificatePinner,
    ): OkHttpClient {
        return OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .certificatePinner(certificatePinner)
            .connectTimeout(150, TimeUnit.SECONDS)
            .writeTimeout(195, TimeUnit.SECONDS)
            .readTimeout(195, TimeUnit.SECONDS)
            .retryOnConnectionFailure(true)
            .build()
    }

    /**
     * Cấu hình và cung cấp Retrofit instance.
     *
     * @param okHttpClient Configured OkHttpClient
     * @param dataSourceProperties Properties chứa base URL
     */
    @Singleton
    @Provides
    fun provideRetrofit(
        okHttpClient: OkHttpClient,
        dataSourceProperties: DataSourceProperties,
    ): Retrofit {
        return Retrofit.Builder()
            .client(okHttpClient)
            .baseUrl(dataSourceProperties.getUrl())
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }
    //endregion

    //region 2. API Service Layer
    /**
     * Cung cấp implementation của API service interface.
     */
    @Singleton
    @Provides
    fun provideApiService(retrofit: Retrofit): ApiService {
        return retrofit.create(ApiService::class.java)
    }
    //endregion

    //region 3. API Client Layer - Default (With Encryption)

    /**
     * Cung cấp API client không mã hóa.
     *
     * @param encryptionService Service xử lý mã hóa (NoEncryption)
     * @param gsonProvider Provider cho JSON processing
     */
    @Singleton
    @Provides
    @NoEncryption
    fun provideNoEncryptionApiClient(
        @NoEncryption encryptionService: EncryptionService,
        gsonProvider: GsonProvider,
        sessionManager: ISessionManager, // Inject SessionManager from CommonModule
    ): IApiClient {
        return ApiClient(encryptionService, gsonProvider, sessionManager)
    }
    //endregion

    /**
     * Cung cấp API client với mã hóa mặc định.
     *
     * @param encryptionService Service xử lý mã hóa
     * @param gsonProvider Provider cho JSON processing
     */
    @Singleton
    @Provides
    @Default
    fun provideApiClient(
        @Default encryptionService: EncryptionService,
        gsonProvider: GsonProvider,
        sessionManager: ISessionManager, // Inject SessionManager from CommonModule
    ): IApiClient {
        return ApiClient(encryptionService, gsonProvider, sessionManager)
    }
    //endregion

    //region File Download
    /**
     * Provide FileDownloadClient for download file.
     *
     * @param apiService Service API để thực hiện download
     * @param gsonProvider Provider cho JSON processing
     * @param context Application context để lưu file vào cache
     */
    @Singleton
    @Provides
    fun provideFileDownloadClient(
        apiService: ApiService,
        gsonProvider: GsonProvider,
        @ApplicationContext context: Context,
    ): IFileDownloadClient {
        return FileDownloadClient(apiService, gsonProvider, context)
    }
    //endregion
}
