package com.vietinbank.core_data.smartca

import android.content.Intent
import androidx.fragment.app.FragmentActivity
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_domain.smartca.ISignResult
import com.vietinbank.core_domain.smartca.ISmartCAManager
import com.vietinbank.smartcadeeplink.SmartCAHandler
import com.vietinbank.smartcadeeplink.SmartCAResult
import com.vietinbank.smartcadeeplink.SmartCAResultCallback
import javax.inject.Inject

class SmartCAManagerImpl @Inject constructor(
    private var configManager: IAppConfigManager,
) : ISmartCAManager {
    private var smartCAHandler: SmartCAHandler? = null
    private var signListenerRef: ISignResult? = null

    override fun signTransaction(
        activity: FragmentActivity,
        clientId: String?,
        tranId: String?,
    ) {
        smartCAHandler?.startTransaction(activity, clientId ?: "", tranId ?: "")
    }

    override fun handleSignResult(
        requestCode: Int,
        resultCode: Int,
        data: Intent?,
    ) {
        smartCAHandler?.handleActivityResult(
            requestCode,
            resultCode,
            data,
            object : SmartCAResultCallback {
                override fun onSuccess(result: SmartCAResult) {
                    signListenerRef?.onResult(result.status, "")
                }

                override fun onError(result: SmartCAResult) {
                    // trương hop ky so tren app khac
                    signListenerRef?.onResult(result.status, result.statusDesc)
                }
            },
        )
    }

    override fun initSmartCA() {
        smartCAHandler = SmartCAHandler(
            if (configManager.getLanguage() == "vi") {
                SmartCAHandler.Language.VIETNAM
            } else {
                SmartCAHandler.Language.ENGLISH
            },
        )
        smartCAHandler?.initializeSDK()
    }

    override fun setSignListener(callback: ISignResult) {
        signListenerRef = callback
    }

    override fun clearManager() {
        signListenerRef = null
        smartCAHandler = null
    }
}