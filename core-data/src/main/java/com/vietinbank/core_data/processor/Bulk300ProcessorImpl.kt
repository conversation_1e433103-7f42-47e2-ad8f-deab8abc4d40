package com.vietinbank.core_data.processor

import com.vietinbank.core_common.extensions.getAmountServer
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.util.TransactionProcessor
import javax.inject.Inject

/**
 * Created by vandz on 4/4/25.
 */
class Bulk300ProcessorImpl @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : TransactionProcessor {
    override suspend fun processTransaction(transaction: TransactionListDomain): TransactionDomain {
        return TransactionDomain().apply {
            tranTypeName = transaction.tranTypeName
            tranType = transaction.tranType
            serviceId = transaction.serviceId
            serviceType = transaction.serviceType
            groupType = transaction.groupType
            createdDate = transaction.createdDate
            status = transaction.status
            tranType = transaction.tranType
            mtId = transaction.mtId
            bulkID = transaction.bulkID
            totalTrxNo = transaction.totalTrxNo
            fileName = transaction.fileName
            statusName = transaction.statusName ?: transaction.status
            currency = transaction.currency
            amount = Utils.g().getDotMoneyHasCcy(amount?.getAmountServer() ?: "", transaction.currency ?: "")
            feeAmount = Utils.g().getDotMoneyHasCcy(feeAmount?.getAmountServer() ?: "", transaction.currency ?: "")
            amountInWords = moneyHelper.convertAmountToWords(transaction.amount, transaction.currency)
            feePayMethodDesc = transaction.feePayMethodDesc
            process_time = transaction.process_time
        }
    }
}
