package com.vietinbank.core_data.processor

import com.vietinbank.core_common.extensions.getAmountServer
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.util.TransactionProcessor
import javax.inject.Inject

class GuaranteeProcessorImpl @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : TransactionProcessor {
    override suspend fun processTransaction(transaction: TransactionListDomain): TransactionDomain {
        // so tien de nghi phat hanh bao lanh
        val amountDisplayHasWord = StringBuilder()
        val amountDisplay = StringBuilder()
        if (!transaction.amount.isNullOrEmpty()) {
            amountDisplayHasWord.append(
                "${
                    Utils.g()
                        .getDotMoneyHasCcy(transaction.amount ?: "", transaction.currency ?: "")
                }\n${
                    moneyHelper.convertAmountToWords(
                        transaction.amount,
                        transaction.currency,
                    )
                }",
            )
            amountDisplay.append(
                Utils.g().getDotMoneyHasCcy(transaction.amount ?: "", transaction.currency ?: ""),
            )
        }
        if (!transaction.amount2.isNullOrEmpty() && "0" != transaction.amount2) {
            amountDisplayHasWord.append(
                "\n${
                    Utils.g().getDotMoneyHasCcy(
                        transaction.amount2 ?: "",
                        transaction.currency2 ?: "",
                    )
                }\n${
                    moneyHelper.convertAmountToWords(
                        transaction.amount2,
                        transaction.currency2,
                    )
                }",
            )
            amountDisplay.append(
                "\n${
                    Utils.g().getDotMoneyHasCcy(
                        transaction.amount2 ?: "",
                        transaction.currency2 ?: "",
                    )
                }",
            )
        }
        if (!transaction.amount3.isNullOrEmpty() && "0" != transaction.amount3) {
            amountDisplayHasWord.append(
                "\n${
                    Utils.g().getDotMoneyHasCcy(
                        transaction.amount3 ?: "",
                        transaction.currency3 ?: "",
                    )
                }\n${
                    moneyHelper.convertAmountToWords(
                        transaction.amount3,
                        transaction.currency3,
                    )
                }",
            )
            amountDisplay.append(
                "\n${
                    Utils.g().getDotMoneyHasCcy(
                        transaction.amount3 ?: "",
                        transaction.currency3 ?: "",
                    )
                }",
            )
        }

        val amountChangeDisplay = StringBuilder()
        val amountChangeInWordDisplay = StringBuilder()
        if (!transaction.amountChange.isNullOrEmpty()) {
            amountChangeDisplay.append(
                Utils.g()
                    .getDotMoneyHasCcy(transaction.amountChange ?: "", transaction.currency ?: ""),
            )
            amountChangeInWordDisplay.append(
                "${
                    Utils.g().getDotMoneyHasCcy(
                        transaction.amountChange ?: "",
                        transaction.currency ?: "",
                    )
                }\n${
                    moneyHelper.convertAmountToWords(
                        transaction.amountChange,
                        transaction.currency,
                    )
                }",
            )
        }
        if (!transaction.amountChange2.isNullOrEmpty() && "0" != transaction.amountChange2) {
            amountChangeDisplay.append(
                "\n${
                    Utils.g().getDotMoneyHasCcy(
                        transaction.amountChange2 ?: "",
                        transaction.currency2 ?: "",
                    )
                }",
            )
            amountChangeInWordDisplay.append(
                "\n${
                    Utils.g().getDotMoneyHasCcy(
                        transaction.amountChange2 ?: "",
                        transaction.currency2 ?: "",
                    )
                }\n${
                    moneyHelper.convertAmountToWords(
                        transaction.amountChange2,
                        transaction.currency2,
                    )
                }",
            )
        }
        if (!transaction.amountChange3.isNullOrEmpty() && "0" != transaction.amountChange2) {
            amountChangeDisplay.append(
                "\n${
                    Utils.g().getDotMoneyHasCcy(
                        transaction.amountChange3 ?: "",
                        transaction.currency3 ?: "",
                    )
                }",
            )

            amountChangeInWordDisplay.append(
                "\n${
                    Utils.g().getDotMoneyHasCcy(
                        transaction.amountChange3 ?: "",
                        transaction.currency3 ?: "",
                    )
                }\n${
                    moneyHelper.convertAmountToWords(
                        transaction.amountChange3,
                        transaction.currency3,
                    )
                }",
            )
        }
        return TransactionDomain().apply {
            tranTypeName = transaction.tranTypeName
            tranType = transaction.tranType
            serviceId = transaction.serviceId
            serviceType = transaction.serviceType
            groupType = transaction.groupType
            status = transaction.status
            creator = transaction.creator
            createdDate = transaction.createdDate
            mtId = transaction.mtId
            statusName = transaction.statusName ?: transaction.status
            representativeName = transaction.representativeName
            authority = transaction.authority
            contractNo = transaction.contractNo
            contractDate = transaction.contractDate
            activityLogs = transaction.activityLogs
            host_mtid = transaction.host_mtid
            releaseDate = transaction.releaseDate
            cifName = transaction.cifName
            dateRange = transaction.dateRange
            issuesBy = transaction.issuesBy
            reasonChange = transaction.reasonChange
            feeDesc = transaction.feeDesc
            beneficiaryName = transaction.beneficiaryName
            beneficiaryAddress = transaction.beneficiaryAddress
            beneficiaryCode = transaction.beneficiaryCode
            receiverIssuedBy = transaction.receiverIssuedBy
            receiverDateRange = transaction.receiverDateRange
            guaranteePurpose = transaction.guaranteePurpose
            effectiveStartDate = transaction.effectiveStartDate
            effectiveEndDate = transaction.effectiveEndDate
            messageType = transaction.messageType
            message = if (!transaction.message.isNullOrEmpty()) {
                transaction.message
            } else {
                when (transaction.messageType) {
                    "1" -> "Bản giấy"
                    "2" -> "Bản điện tử ký số"
                    "3" -> "SWIFT"
                    else -> transaction.messageType
                }
            }
            guaranteeCode = transaction.guaranteeCode
            branch = transaction.branch
            branchName = transaction.branchName
            issueTypeDesc = if (!transaction.issueTypeDesc.isNullOrEmpty()) {
                transaction.issueTypeDesc
            } else {
                when (transaction.issueType) {
                    "1" -> "Thư bảo lãnh"
                    "2" -> "Hợp đồng bảo lãnh"
                    "3" -> "Thư tín dụng dự phòng"
                    else -> "Khác"
                }
            }
            conditionPerform = transaction.conditionPerform
            conditionReduction = transaction.conditionReduction
            // cach thuc gui bao lanh
            sendTypeDesc = if (!transaction.sendTypeDesc.isNullOrEmpty()) {
                transaction.sendTypeDesc
            } else {
                when (transaction.sendType) {
                    "1" -> "Chúng tôi nhận và chuyển cho bên nhận bảo lãnh"
                    "2" -> "Ngân hàng gửi cho bên nhận bảo lãnh"
                    "3" -> "Chuyển thông qua mạng SWIFT"
                    else -> ""
                }
            }
            sendTypeCmnd = transaction.sendTypeCmnd
            sendTypeNo = transaction.sendTypeNo
            sendTypeDate = transaction.sendTypeDate
            sendTypeBy = transaction.sendTypeBy
            feeTimeDesc = if (!transaction.feeTimeDesc.isNullOrEmpty()) {
                transaction.feeTimeDesc
            } else {
                when (transaction.feeTime) {
                    "1" -> "Tại thời điểm phát hành"
                    "2" -> "Thỏa thuận"
                    else -> ""
                }
            }
            feeTime = transaction.feeTime
            measure = transaction.measure
            measureAcct = transaction.measureAcct
            measureAmount = Utils.g().getDotMoneyHasCcy(
                transaction.measureAmount ?: "",
                transaction.measureCurrency ?: "",
            )
            measureDesc = transaction.measureDesc
            language = when (transaction.language) {
                "vi" -> "Tiếng Việt"
                else -> ""
            }
            documentTypeName = transaction.documentTypeName
            biCodeInfo = transaction.biCodeInfo
            companyName = transaction.companyName
            listFiles = transaction.listFiles
            ibFile = transaction.ibFile
            amount = amountDisplay.toString()
            amountInWords = amountDisplayHasWord.toString()
            amountChange = amountChangeDisplay.toString()
            amountChangeInWords = amountChangeInWordDisplay.toString()
            headQuarters = transaction.headQuarters
            authorizationDate = transaction.authorizationDate
            typeChange = transaction.typeChange
            sendType = transaction.sendType
            effectiveEndDateChange = transaction.effectiveEndDateChange
            effectiveStartDateChange = transaction.effectiveStartDateChange
            guaranteeCommitContent = transaction.guaranteeCommitContent
            guaranteeCommitContentChange = transaction.guaranteeCommitContentChange
            createDateDesc = transaction.createDateDesc
            verifiedDateDesc = transaction.verifiedDateDesc
            messageTypeDes = transaction.messageTypeDes
            guaranteeBalances = Utils.g()
                .getDotMoneyHasCcy(transaction.guaranteeBalances ?: "", transaction.currency ?: "")
            trxTypeName = transaction.trxTypeName
            feeDescDisplay = when (transaction.feeDesc) {
                "10" -> "Theo biểu phí NHCT"
                else -> "Thỏa thuận"
            }
            measureDescDisplay = when {
                transaction.tranType == "goc" -> {
                    val measureDesc = StringBuilder()
                    if (true == transaction.measure?.contains("1")) {
                        measureDesc.append("\nKý quỹ tăng thêm")
                    }
                    if (true == transaction.measure?.contains("2")) {
                        measureDesc.append("\nTSĐB khác bổ sung")
                    }
                    measureDesc.toString().replaceFirst("\n".toRegex(), "")
                }

                transaction.tranType == "go" -> {
                    val measureDesc = StringBuilder()
                    if (true == transaction.measure?.contains("1")) {
                        measureDesc.append("\nKý quỹ")
                    }
                    if (true == transaction.measure?.contains("2")) {
                        measureDesc.append("\nCầm cố/thế chấp bằng tài sản khác của khách hàng bên thứ ba")
                    }
                    if (true == transaction.measure?.contains("3")) {
                        measureDesc.append("\nBiện pháp đảm bảo khác")
                    }
                    measureDesc.toString().replaceFirst("\n".toRegex(), "")
                }

                else -> {
                    ""
                }
            }
            typeChangeDisplay = when {
                transaction.tranType == "gor" && transaction.typeChange == "10" -> {
                    "Giảm trừ/Giải tỏa từng phần"
                }

                transaction.tranType == "gor" && transaction.typeChange == "11" -> {
                    "Giải tỏa toàn bộ"
                }

                transaction.tranType == "goc" -> {
                    // sua doi
                    val typeChangeDesc = StringBuilder()
                    if (true == transaction.typeChange?.contains("1")) {
                        typeChangeDesc.append("\nThời gian hiệu lực của bảo lãnh")
                    }
                    if (true == transaction.typeChange?.contains("2")) {
                        typeChangeDesc.append("\nSố tiền bảo lãnh")
                    }
                    if (true == transaction.typeChange?.contains("3")) {
                        typeChangeDesc.append("\nCác nội dung khác")
                    }
                    typeChangeDesc.toString().replaceFirst("\n".toRegex(), "")
                }

                else -> ""
            }
            depositDisplay = "${transaction.depositAccount ?: ""} - ${
                Utils.g().getDotMoney(transaction.depositAmount?.getAmountServer() ?: "")
            } - ${transaction.depositCurrency ?: ""}"
        }
    }
}