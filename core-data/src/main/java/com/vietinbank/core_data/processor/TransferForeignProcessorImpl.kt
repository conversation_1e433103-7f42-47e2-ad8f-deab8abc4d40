package com.vietinbank.core_data.processor

import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.util.TransactionProcessor
import javax.inject.Inject

class TransferForeignProcessorImpl @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : TransactionProcessor {
    override suspend fun processTransaction(transaction: TransactionListDomain): TransactionDomain {
        return TransactionDomain().apply {
            tranTypeName = transaction.tranTypeName
            tranType = transaction.tranType
            serviceId = transaction.serviceId
            serviceType = transaction.serviceType
            groupType = transaction.groupType
            createdDate = transaction.createdDate
            status = transaction.status
            mtId = transaction.mtId
            statusName = transaction.statusName ?: transaction.status
            fromAccountNo = transaction.fromAccountNo
            fromAccountName = transaction.fromAccountName
            currency = transaction.currency
            toAccountNo = transaction.toAccountNo
            remark = transaction.remark
            activityLogs = transaction.activityLogs
            introducer = transaction.introducer
            totalAmount = Utils.g().getDotMoneyHasCcy(
                transaction.totalAmount ?: "",
                transaction.currency ?: "",
            )
            amountInWords = moneyHelper.convertAmountToWords(
                transaction.totalAmount ?: "",
                transaction.currency,
            )
            note = transaction.note
            valueDate = transaction.valueDate
            purposeTransferName = transaction.purposeTransferName
            feeTypeName = transaction.feeTypeName
            debitRate1 = transaction.debitRate1
            debitRate2 = transaction.debitRate2
            debitAmtByRate1 = Utils.g().getDotMoneyHasCcy(
                transaction.debitAmtByRate1 ?: "",
                transaction.currency ?: "",
            )
            debitAmtByRate1AmountInWord = moneyHelper.convertAmountToWords(
                transaction.debitAmtByRate1 ?: "",
                transaction.currency,
            )
            debitAmtByRate2 = Utils.g().getDotMoneyHasCcy(
                transaction.debitAmtByRate2 ?: "",
                transaction.currency ?: "",
            )
            debitAmtByRate2AmountInWord = moneyHelper.convertAmountToWords(
                transaction.debitAmtByRate2 ?: "",
                transaction.currency,
            )
            fromAccount2 = transaction.fromAccount2
            fromAccountName2 = transaction.fromAccountName2
            benName = transaction.benName
            benCountry = transaction.benCountry
            benWard = transaction.benWard
            benStreet = transaction.benStreet
            benBankName = transaction.benBankName
            benBankCountry = transaction.benBankCountry
            benBankWard = transaction.benBankWard
            benBankStreet = transaction.benBankStreet
            receiveBank = transaction.receiveBank
            midBankName = transaction.midBankName
            midBankCode = transaction.midBankCode
            remitterName = transaction.remitterName
            remitterCountry = transaction.remitterCountry
            remitterDistrict = transaction.remitterDistrict
            remitterWard = transaction.remitterWard
            remitterStreet = transaction.remitterStreet
            benDistrict = transaction.benDistrict
            finalBenCountry = transaction.finalBenCountry
            finalBenDistrict = transaction.finalBenDistrict
            finalBenWard = transaction.finalBenWard
            finalBenStreet = transaction.finalBenStreet
            lcReturnDate = transaction.lcReturnDate
            feeType = transaction.feeType
            benBankDistrict = transaction.benBankDistrict
            midBankCountry = transaction.midBankCountry
            midBankDistrict = transaction.midBankDistrict
            midBankWard = transaction.midBankWard
            midBankStreet = transaction.midBankStreet
            conditionLink = transaction.conditionLink
            rateMsgWarning = transaction.rateMsgWarning
        }
    }
}
