package com.vietinbank.core_data.processor

import com.vietinbank.core_common.extensions.getAmountServer
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.util.TransactionProcessor
import javax.inject.Inject

/**
 * Created by vandz on 4/4/25.
 */
class SalaryProcessorImpl @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : TransactionProcessor {
    override suspend fun processTransaction(transaction: TransactionListDomain): TransactionDomain {
        val totalFee = (
            transaction.feeAmount?.getAmountServer()?.toDoubleOrNull()
                ?: 0.0
            ) + (transaction.feeVat?.getAmountServer()?.toDoubleOrNull() ?: 0.0)
        return TransactionDomain().apply {
            tranTypeName = transaction.tranTypeName?.ifEmpty {
                when (transaction.tranType) {
                    "slo" -> "Chi lương tự động"
                    "sx" -> "<PERSON> lương ngoại tệ"
                    "sl" -> "Chi lương qua ngân hàng"
                    else -> "Chi lương"
                }
            }
            tranType = transaction.tranType
            serviceId = transaction.serviceId
            serviceType = transaction.serviceType
            groupType = transaction.groupType
            createdDate = transaction.createdDate
            status = transaction.status
            mtId = transaction.mtId
            listFile2 = transaction.listFile2
            listFile = transaction.listFile
            statusName = transaction.statusName ?: transaction.status
            fromAccountNo = transaction.fromAccountNo
            currency = transaction.currency
            feePayMethodDesc1 = transaction.feePayMethodDesc1
            amount = Utils.g().getDotMoneyHasCcy(
                transaction.amount?.getAmountServer() ?: "", transaction.currency ?: "",
            )
            feeAmount = if (transaction.feePayMethod == "3") {
                transaction.feePayMethodDesc1 ?: ""
            } else {
                Utils.g().getDotMoneyHasCcy(totalFee.toString(), transaction.currency ?: "")
            }
            remark = transaction.remark
            amountInWords =
                moneyHelper.convertAmountToWords(transaction.amount, transaction.currency)
            process_time = transaction.process_time
            activityLogs = transaction.activityLogs
            exchangeRate = transaction.exchangeRate?.let {
                Utils.g().getDotMoneyHasCcy(it, transaction.debitCurrency ?: "")
            }
            debitCurrency = transaction.debitCurrency
            debitAmount = transaction.debitAmount?.let {
                Utils.g().getDotMoneyHasCcy(it, transaction.debitCurrency ?: "")
            }
            fromAccountName = transaction.fromAccountName
        }
    }
}
