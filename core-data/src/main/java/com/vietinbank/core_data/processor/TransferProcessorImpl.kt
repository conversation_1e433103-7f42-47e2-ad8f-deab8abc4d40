package com.vietinbank.core_data.processor

import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.util.TransactionProcessor
import javax.inject.Inject

/**
 * Created by vandz on 4/4/25.
 */
class TransferProcessorImpl @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : TransactionProcessor {
    override suspend fun processTransaction(transaction: TransactionListDomain): TransactionDomain {
        // Chuyển đổi số tiền sang chữ
        return TransactionDomain().apply {
            tranTypeName = transaction.tranTypeName
            tranType = transaction.tranType
            serviceId = transaction.serviceId
            serviceType = transaction.serviceType
            groupType = transaction.groupType
            createdDate = transaction.createdDate
            status = transaction.status
            mtId = transaction.mtId
            statusName = transaction.statusName ?: transaction.status
            fromAccountNo = transaction.fromAccountNo
            fromAccountName = transaction.fromAccountName
            feePayMethod = transaction.feePayMethod
            feePayMethodDesc1 = transaction.feePayMethodDesc1
            currency = transaction.currency
            toAccountNo = transaction.toAccountNo
            receiveBankName = transaction.receiveBankName
            amount =
                Utils.g().getDotMoneyHasCcy(transaction.amount ?: "", transaction.currency ?: "")
            feeAmount =
                Utils.g().getDotMoneyHasCcy(transaction.feeAmount ?: "", transaction.currency ?: "")
            remark = transaction.remark
            amountInWords =
                moneyHelper.convertAmountToWords(transaction.amount, transaction.currency)
            feePayMethodDesc = transaction.feePayMethodDesc
            receiveName = transaction.receiveName
            process_time = transaction.process_time
            activityLogs = transaction.activityLogs
        }
    }
}
