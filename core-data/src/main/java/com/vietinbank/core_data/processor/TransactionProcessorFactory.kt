package com.vietinbank.core_data.processor

import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_domain.models.checker.util.TransactionProcessor
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Created by vandz on 4/4/25.
 */
@Singleton
class TransactionProcessorFactory
@Inject
constructor(
    private val transferProcessor: TransferProcessorImpl,
    private val salaryProcessor: SalaryProcessorImpl,
    private val bulk5000ProcessorImpl: Bulk5000ProcessorImpl,
    private val bulk300ProcessorImpl: Bulk300ProcessorImpl,
    private val paymentProcessorImpl: PaymentProcessorImpl,
    private val traceProcessorImpl: TraceProcessorImpl,
    private val disbursementOnlineProcessorImpl: DisbursementOnlineProcessorImpl,
    private val guaranteeProcessorImpl: GuaranteeProcessorImpl,
    private val customsInlandProcessorImpl: CustomsInlandProcessorImpl,
    private val fileNSNNProcessorImpl: FileNSNNProcessorImpl,
    private val infrastructureProcessorImpl: InfrastructureProcessorImpl,
    private val transferForeignProcessorImpl: TransferForeignProcessorImpl,
    private val unLockUserProcessorImpl: UnLockUserProcessorImpl,
) {
    fun getProcessor(tranType: String): TransactionProcessor =
        when (tranType) {
            "in", "ou", "np" -> transferProcessor
            "sl", "slo", "sx" -> salaryProcessor
            "pm" -> paymentProcessorImpl
            "hu" -> bulk5000ProcessorImpl
            "ba" -> bulk300ProcessorImpl
            "tr" -> traceProcessorImpl
            "tx" -> customsInlandProcessorImpl
            "btx" -> fileNSNNProcessorImpl
            "if" -> infrastructureProcessorImpl
            "fx" -> transferForeignProcessorImpl
            Tags.TYPE_DISBURSEMENT_ONLINE.lowercase() -> disbursementOnlineProcessorImpl
            "go", "goc", "gor" -> guaranteeProcessorImpl
            Tags.TYPE_GROUP_RESET_PASSWORD.lowercase(), Tags.TYPE_GROUP_UNLOCK_USER.lowercase() -> unLockUserProcessorImpl
            else -> transferProcessor
        }
}
