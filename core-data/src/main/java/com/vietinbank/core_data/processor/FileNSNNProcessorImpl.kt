package com.vietinbank.core_data.processor

import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.util.TransactionProcessor
import javax.inject.Inject

class FileNSNNProcessorImpl @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : TransactionProcessor {
    override suspend fun processTransaction(transaction: TransactionListDomain): TransactionDomain {
        return TransactionDomain().apply {
            tranTypeName = transaction.tranTypeName
            tranType = transaction.tranType
            serviceId = transaction.serviceId
            serviceType = transaction.serviceType
            groupType = transaction.groupType
            createdDate = transaction.createdDate
            status = transaction.status
            mtId = transaction.mtId
            fileName = transaction.fileName
            statusName = transaction.statusName ?: transaction.status
            currency = transaction.currency
            createdDate = transaction.createdDate
            total =
                Utils.g().getDotMoneyHasCcy(transaction.total ?: "", transaction.currency ?: "")
            amountInWords = moneyHelper.convertAmountToWords(
                transaction.total ?: "",
                transaction.currency ?: "",
            )
            totalFee = Utils.g()
                .getDotMoneyHasCcy(transaction.totalFee ?: "", transaction.currency ?: "")
            creator = transaction.creator
            subTranItemList = transaction.subTranItemList
        }
    }
}
