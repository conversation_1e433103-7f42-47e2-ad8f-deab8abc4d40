package com.vietinbank.core_data.helper

import android.content.Context
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.environment.IEnvironmentProvider
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import javax.inject.Singleton

/**
 * Created by vandz on 13/3/25.
 */
@Singleton
object RequestManager {
    private lateinit var sessionManager: ISessionManager
    private lateinit var userProfData: IUserProf
    private lateinit var appConfig: IAppConfigManager
    private lateinit var envProvider: IEnvironmentProvider
    private lateinit var applicationContext: Context

    fun init(
        sessionManager: ISessionManager,
        userProfData: IUserProf,
        appConfigManager: IAppConfigManager,
        envProvider: IEnvironmentProvider,
        appContext: Context,
    ) {
        this.sessionManager = sessionManager
        this.userProfData = userProfData
        this.appConfig = appConfigManager
        this.envProvider = envProvider
        this.applicationContext = appContext
    }

    internal fun getSessionManager(): ISessionManager {
        if (!::sessionManager.isInitialized) {
            throw IllegalStateException("RequestManager must be initialized with sessionManager")
        }
        return sessionManager
    }

    internal fun getUserProf(): IUserProf {
        if (!::userProfData.isInitialized) {
            throw IllegalStateException("RequestManager must be initialized with userProf")
        }
        return userProfData
    }

    internal fun getAppConfig(): IAppConfigManager {
        if (!::appConfig.isInitialized) {
            throw IllegalStateException("RequestManager must be initialized with appConfig")
        }
        return appConfig
    }

    internal fun getEnvironmentProvider(): IEnvironmentProvider {
        if (!::envProvider.isInitialized) {
            throw IllegalStateException("RequestManager must be initialized with appConfig")
        }
        return envProvider
    }

    internal fun getContext(): Context {
        if (!::applicationContext.isInitialized) {
            throw IllegalStateException("RequestManager must be initialized with context")
        }
        return applicationContext
    }
}
