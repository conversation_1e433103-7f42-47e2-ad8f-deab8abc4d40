package com.vietinbank.feature_home.home_component

/**
 * Created by van<PERSON><PERSON> on 11/3/25.
 */
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_domain.models.home.ApprovalItem
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.core_ui.utils.safeClickable

/**
 * Màn hình danh sách yêu cầu chờ duyệt
 */
@Composable
fun ApprovalListView(
    approvalItems: List<ApprovalItem>,
    onItemClick: (ApprovalItem) -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
    ) {
        // Tiêu đề
        BaseText(
            text = "Loại yêu cầu chờ duyệt",
            color = AppColors.textPrimary,
            textSize = 16.sp,
            fontCus = 5, // Bold
            modifier = Modifier.padding(vertical = 12.dp),
        )

        // Danh sách yêu cầu
        LazyColumn(
            modifier = Modifier.fillMaxWidth(),
        ) {
            itemsIndexed(approvalItems) { index, item ->
                // Xác định xem item này là đầu tiên, cuối cùng hoặc giữa
                val isFirstItem = index == 0
                val isLastItem = index == approvalItems.size - 1

                // Bo góc dựa vào vị trí
                val cornerShape = when {
                    isFirstItem && isLastItem -> RoundedCornerShape(10.dp) // Nếu chỉ có 1 item
                    isFirstItem -> RoundedCornerShape(topStart = 10.dp, topEnd = 10.dp)
                    isLastItem -> RoundedCornerShape(bottomStart = 10.dp, bottomEnd = 10.dp)
                    else -> RoundedCornerShape(0.dp)
                }

                ApprovalItemView(
                    item = item,
                    onClick = { onItemClick(item) },
                    shape = cornerShape,
                    showTopBorder = !isFirstItem,
                    modifier = Modifier.fillMaxWidth(),
                )
            }
        }
    }
}

/**
 * Component hiển thị một mục yêu cầu chờ duyệt
 */
@Composable
fun ApprovalItemView(
    item: ApprovalItem,
    onClick: () -> Unit,
    shape: RoundedCornerShape,
    showTopBorder: Boolean = false,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .clip(shape)
            .background(Color.White)
            .safeClickable { onClick() },
    ) {
        // Viền phía trên để ngăn cách các item (trừ item đầu tiên)
        if (showTopBorder) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(0.5.dp)
                    .background(AppColors.borderColor)
                    .align(Alignment.TopCenter),
            )
        }

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // Icon bên trái
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .clip(RoundedCornerShape(100.dp))
                    .background(Color(0xFF0066B3)),
                contentAlignment = Alignment.Center,
            ) {
                Icon(
                    painter = painterResource(id = item.iconRes),
                    contentDescription = item.title,
                    tint = Color.White,
                    modifier = Modifier.size(24.dp),
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // Nội dung giữa
            Column(
                modifier = Modifier.weight(1f),
            ) {
                BaseText(
                    text = item.title,
                    color = AppColors.textPrimary,
                    textSize = 16.sp,
                    fontCus = 1, // Semi-bold
                )

                Spacer(modifier = Modifier.height(4.dp))

                BaseText(
                    text = "${item.count} yêu cầu chờ duyệt",
                    color = Color(0xFF707070), // Gray color
                    textSize = 14.sp,
                    fontCus = 0, // Regular
                )
            }

            // Icon mũi tên bên phải
            Icon(
                painter = painterResource(id = R.drawable.ic_right),
                contentDescription = "Chi tiết",
                tint = Color(0xFF707070),
                modifier = Modifier.size(24.dp),
            )
        }
    }
}

@Preview
@Composable
fun ApprovalListViewPreview() {
    val sampleItems = listOf(
        ApprovalItem(
            id = "1",
            title = "Chuyển tiền",
            count = 79,
            iconRes = R.drawable.ic_filter,
        ),
        ApprovalItem(
            id = "2",
            title = "Chuyển tiền theo file",
            count = 79,
            iconRes = R.drawable.ic_filter,
        ),
        ApprovalItem(
            id = "3",
            title = "Nộp ngân sách nhà nước",
            count = 79,
            iconRes = R.drawable.ic_filter,
        ),
        ApprovalItem(
            id = "4",
            title = "Nộp BHXH",
            count = 79,
            iconRes = R.drawable.ic_filter,
        ),
    )

    AppTheme {
        ApprovalListView(
            approvalItems = sampleItems,
            onItemClick = { },
        )
    }
}

@Preview
@Composable
fun ApprovalItemViewPreview() {
    val sampleItem = ApprovalItem(
        id = "1",
        title = "Chuyển tiền",
        count = 79,
        iconRes = R.drawable.ic_filter,
    )

    AppTheme {
        Column {
            // First item
            ApprovalItemView(
                item = sampleItem,
                onClick = { },
                shape = RoundedCornerShape(topStart = 10.dp, topEnd = 10.dp),
            )

            // Middle item
            ApprovalItemView(
                item = sampleItem.copy(title = "Chuyển tiền theo file"),
                onClick = { },
                shape = RoundedCornerShape(0.dp),
                showTopBorder = true,
            )

            // Last item
            ApprovalItemView(
                item = sampleItem.copy(title = "Nộp ngân sách nhà nước"),
                onClick = { },
                shape = RoundedCornerShape(bottomStart = 10.dp, bottomEnd = 10.dp),
                showTopBorder = true,
            )
        }
    }
}