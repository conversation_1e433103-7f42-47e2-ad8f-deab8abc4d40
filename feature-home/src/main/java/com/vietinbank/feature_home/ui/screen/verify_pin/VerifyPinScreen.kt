package com.vietinbank.feature_home.ui.screen.verify_pin

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ui.base.compose.BaseAppBar
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.base.compose.InputMode
import com.vietinbank.core_ui.base.compose.PinPasswordInput
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable

@Composable
fun VerifyPinScreen(
    state: VerifyPinState,
    onAction: (VerifyPinAction) -> Unit,
) {
    Column(
        modifier = Modifier
            .fillMaxSize(),
    ) {
        // App Bar
        BaseAppBar(
            title = "Nhập mã PIN",
            onBackClick = { onAction(VerifyPinAction.OnBackPressed) },
        )

        // Content
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 24.dp),
        ) {
            BaseText(
                text = "Quý khách vui lòng nhập mã PIN để xem thông tin biến động số dư",
                textColor = Color.White,
                textSize = 14.sp,
                textAlign = TextAlign.Start,
                modifier = Modifier.fillMaxWidth(),
            )

            Spacer(modifier = Modifier.height(32.dp))

            // PIN input
            PinPasswordInput(
                value = state.pin,
                onValueChange = { onAction(VerifyPinAction.OnPinChanged(it)) },
                inputMode = InputMode.PASSWORD,
                hint = "Nhập mã PIN",
                enabled = !state.isLoading && !state.isLockedOut,
                isError = state.pinError != null,
                errorMessage = state.pinError ?: state.lockoutMessage,
                textColor = Color.Gray,
                backgroundColor = Color.White,
                borderColor = Color.White,
                focusedBorderColor = AppColors.primary,
                errorBorderColor = Color.Red,
                hintColor = Color.Gray,
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Biometric option
            if (state.biometricAvailable && !state.isLockedOut) {
                TextButton(
                    onClick = { onAction(VerifyPinAction.OnUseBiometricClicked) },
                    modifier = Modifier.fillMaxWidth(),
                ) {
                    BaseText(
                        text = "Sử dụng vân tay/Face ID",
                        textColor = Color.White,
                        textSize = 14.sp,
                    )
                }
            }

            // Forgot PIN
            TextButton(
                onClick = { onAction(VerifyPinAction.OnForgotPinClicked) },
                modifier = Modifier.fillMaxWidth(),
            ) {
                BaseText(
                    text = "Quên mã PIN?",
                    textColor = Color.White,
                    textSize = 14.sp,
                )
            }
        }

        Spacer(modifier = Modifier.weight(1f))

        // Bottom button
        VerifyPinBottomButton(
            isLoading = state.isLoading,
            isEnabled = !state.isLockedOut,
            onContinueClick = { onAction(VerifyPinAction.OnContinueClicked) },
        )
    }
}

@Composable
private fun VerifyPinBottomButton(
    isLoading: Boolean,
    isEnabled: Boolean,
    onContinueClick: () -> Unit,
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                color = Color.White,
                shape = RoundedCornerShape(topStart = 4.dp, topEnd = 4.dp),
            )
            .imePadding(),
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp)
                .height(48.dp)
                .clip(RoundedCornerShape(6.dp))
                .background(
                    brush = if (isEnabled) {
                        Brush.horizontalGradient(
                            colors = listOf(
                                Color(0xFF81DEFE),
                                Color(0xFF6FC8F1),
                            ),
                        )
                    } else {
                        Brush.horizontalGradient(
                            colors = listOf(
                                Color.LightGray,
                                Color.LightGray,
                            ),
                        )
                    },
                )
                .padding(1.dp)
                .clip(RoundedCornerShape(5.dp))
                .background(
                    brush = if (isEnabled) {
                        Brush.horizontalGradient(
                            colors = listOf(
                                Color(0xFF2A8BCB),
                                Color(0xFF74CCF4),
                            ),
                        )
                    } else {
                        Brush.horizontalGradient(
                            colors = listOf(
                                Color.Gray,
                                Color.Gray,
                            ),
                        )
                    },
                )
                .safeClickable(enabled = isEnabled) { onContinueClick() },
            contentAlignment = Alignment.Center,
        ) {
            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(16.dp),
                    color = Color.White,
                    strokeWidth = 2.dp,
                )
            } else {
                BaseText(
                    text = "Xác nhận",
                    textColor = Color.White,
                    fontCus = 1,
                    textSize = 16.sp,
                )
            }
        }
    }
}

@Preview(showBackground = true, backgroundColor = 0xFF0066CC)
@Composable
fun VerifyPinScreenPreview() {
    VerifyPinScreen(
        state = VerifyPinState(),
        onAction = {},
    )
}

@Preview(showBackground = true, backgroundColor = 0xFF0066CC)
@Composable
fun VerifyPinScreenLockedPreview() {
    VerifyPinScreen(
        state = VerifyPinState(
            isLockedOut = true,
            lockoutMessage = "Tài khoản đã bị khóa. Vui lòng thử lại sau 25 phút",
        ),
        onAction = {},
    )
}