package com.vietinbank.feature_home.ui.navigation

import android.os.Bundle
import androidx.fragment.app.FragmentManager
import androidx.navigation.NavController
import androidx.navigation.NavOptions
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.feature_home.R

class AccountBalanceNavigatorImpl(
    private val navController: NavController,
    private val fragmentManager: FragmentManager,
) : AccountBalanceNavigator {

    private fun createSlideNavOptions(): NavOptions {
        val builder = NavOptions.Builder()
            .setEnterAnim(R.anim.slide_in_right)
            .setExitAnim(R.anim.slide_out_left)
            .setPopEnterAnim(R.anim.slide_in_left)
            .setPopExitAnim(R.anim.slide_out_right)

        return builder.build()
    }

    override fun goToOttPinSetup() {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.pinSetupFragment, null, navOptions)
    }

    override fun goToChangePinSetup() {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.changePinFragment, null, navOptions)
    }

    override fun goToNotificationSetting(accNumber: String, tag: Int) {
        val navOptions = createSlideNavOptions()
        val bundle = Bundle().apply {
            putString(Tags.ACC_NUMBER, accNumber)
            putInt(Tags.NOTIFICATION_ROUTE_TAG, tag)
        }
        navController.navigate(R.id.notificationSettingFragment, bundle, navOptions)
    }

    override fun goToChangeNotificationSms() {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.changeNotificationSmsFragment, null, navOptions)
    }

    override fun goToUnRegIdentityAccount() {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.unRegisIdentityAccountFragment, null, navOptions)
    }

    override fun popToNotificationSetting() {
        navController.popBackStack(R.id.notificationSettingFragment, false)
    }

    override fun popToAccountBalanceScreen() {
        navController.popBackStack(R.id.accountBalanceFragment, false)
    }

    override fun popToOttPinSetup() {
        navController.popBackStack(R.id.accountBalanceFragment, false)
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.pinSetupFragment, null, navOptions)
    }

    override fun popBackStack() {
        navController.popBackStack()
    }

    override fun popBackGraph() {
//        navController.popBackStack(R.id.update_ekyc_nav_graph, false)
    }
}