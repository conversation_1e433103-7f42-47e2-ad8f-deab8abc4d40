package com.vietinbank.feature_home.ui.screen.account_balance

sealed interface AccountBalanceAction {
    data class SelectedTabIndex(val tab: Int) : AccountBalanceAction
    data object NotificationAction : AccountBalanceAction
    data object PinSetupAction : AccountBalanceAction
    data object ChangePinAction : AccountBalanceAction
    data object ForgetPinAction : AccountBalanceAction
    data object NavigateBack : AccountBalanceAction
    data object GotoRegisterLoanAccountAction : AccountBalanceAction
    data object NotYetImplemented : AccountBalanceAction
}