package com.vietinbank.feature_home.ui.screen.change_notification_sms

import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.RadioButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.base.compose.BaseAppBar
import com.vietinbank.core_ui.base.compose.BaseButton
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_home.R
import kotlinx.coroutines.launch

@Composable
fun ChangeNotificationSmsScreen(
    uiState: ChangeNotificationUiState,
    onAction: (ChangeNotificationAction) -> Unit,
) {
    var showSheet by remember { mutableStateOf(false) }
    Column(
        modifier = Modifier.fillMaxSize(),
    ) {
        BaseAppBar(
            title = "Cài đặt thông báo",
            onBackClick = {
                onAction(ChangeNotificationAction.OnNavigateBack)
            },
        )

        Column(
            modifier = Modifier
                .weight(1f)
                .padding(horizontal = 16.dp, vertical = 20.dp),
            verticalArrangement = Arrangement.spacedBy(20.dp),
        ) {
            BaseText(
                text = "Quý khách vui lòng nhập/chọn thông tin để thực hiện chuyển đổi nhận thông báo BĐSD qua tin nhắn SMS.",
                color = Color(0xFFD6F4FF),
            )
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp),
            ) {
                CustomPhoneTextField(
                    value = uiState.phoneNum,
                    onValueChange = { onAction(ChangeNotificationAction.PhoneNumValueChange(it)) },
                )
                BaseText(
                    text = "Lưu ý: Số điện thoại phải là số đăng ký SMS",
                    color = Color(0xFFD6F4FF),
                )
            }

            Row(
                modifier = Modifier
                    .background(Color.White, RoundedCornerShape(4.dp))
                    .padding(horizontal = 24.dp, vertical = 20.dp)
                    .safeClickable {
                        showSheet = true
                    },
                horizontalArrangement = Arrangement.Center,
            ) {
                Column(
                    modifier = Modifier.weight(1f),
                    verticalArrangement = Arrangement.spacedBy(8.dp),
                ) {
                    BaseText(
                        text = "Loại thông báo nhận BĐSD",
                        color = Color(0xFF6A8094),
                        fontCus = 2,
                    )
                    if (!uiState.alertType.isNullOrEmpty()) {
                        BaseText(
                            text = uiState.alertType,
                            color = Color(0xFF0A5994),
                            fontCus = 2,
                        )
                    }
                }
                Image(
                    modifier = Modifier.size(24.dp),
                    painter = painterResource(R.drawable.ic_menu_down),
                    contentDescription = "",
                )
            }

            Spacer(
                modifier = Modifier.weight(1f),
            )

            if (!uiState.alertType.isNullOrEmpty()) {
                BaseButton(
                    modifier = Modifier
                        .fillMaxWidth()
                        .safeClickable {
                            onAction(ChangeNotificationAction.OnConfirmOtp)
                        },
                    text = "Tiếp tục",
                )
            }
        }
    }
    if (showSheet) {
        MenuDropDownTypeNotification(
            alertType = uiState.alertType ?: "",
            onDismiss = {
                onAction(ChangeNotificationAction.OnSelectAlertType(it))
                showSheet = false
            },
        )
    }
}

@Composable
fun CustomPhoneTextField(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    val focusRequester = remember { FocusRequester() }
    val focusState = remember { mutableStateOf(false) }

    // Animate padding top theo focus state
    val animatedTopPadding by animateDpAsState(
        targetValue = if (focusState.value || value.isNotBlank()) 20.dp else 0.dp,
        label = "TopPaddingAnimation",
    )
    Box(
        modifier = modifier
            .background(Color.White, RoundedCornerShape(4.dp))
            .padding(top = animatedTopPadding),
    ) {
        OutlinedTextField(
            value = value,
            onValueChange = { onValueChange(it) },
            label = {
                BaseText(
                    "Số điện thoại",
                    color = Color(0xFF6A8094),
                    fontCus = 2,
                )
            },
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Number,
            ),
            colors = OutlinedTextFieldDefaults.colors(
                focusedTextColor = Color(0xFF0A5994),
                unfocusedTextColor = Color(0xFF0A5994),
                cursorColor = Color(0xFF0A5994),
                focusedBorderColor = Color.Transparent,
                unfocusedBorderColor = Color.Transparent,
                disabledBorderColor = Color.Transparent,
                errorBorderColor = Color.Transparent,
            ),
            modifier = Modifier
                .fillMaxWidth()
                .onFocusChanged { focusState.value = it.isFocused }
                .focusRequester(focusRequester),
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun MenuDropDownTypeNotification(
    alertType: String,
    onDismiss: (String) -> Unit,
) {
    val scope = rememberCoroutineScope()
    val listItem = listOf(
        "Alert 189" to "Biến động số dư > 0đ hoặc 0 đơn vị ngoại tệ",
        "Alert 169" to "Báo có vào tài khoản",
        "Alert 89" to "Biến động số dư > 50.000đ hoặc 1 đơn vị ngoại tệ",
    )

    ModalBottomSheet(
        onDismissRequest = {},
        containerColor = Color.White,
        shape = RoundedCornerShape(topStart = 10.dp, topEnd = 10.dp),
        dragHandle = {},
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
        ) {
            Row(
                horizontalArrangement = Arrangement.Center,
            ) {
                Image(
                    painter = painterResource(R.drawable.ic_notification),
                    contentDescription = "",
                )
                BaseText(
                    text = "Loại thông báo nhận BĐSD",
                    color = Color(0xFF5C6674),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 10.dp)
                        .padding(start = 10.dp),
                )
            }
            listItem.forEach {
                Row(
                    modifier = Modifier
                        .padding(vertical = 12.dp)
                        .safeClickable {
                            scope.launch {
                                onDismiss(it.first)
                            }
                        },
                ) {
                    Column(
                        modifier = Modifier.weight(1f),
                        verticalArrangement = Arrangement.spacedBy(8.dp),
                    ) {
                        BaseText(
                            text = it.first,
                            color = Color(0xFF5C6674),
                        )
                        BaseText(
                            text = it.second,
                            color = Color(0xFF006594),
                        )
                    }
                    RadioButton(
                        selected = it.first == alertType,
                        onClick = { onDismiss(it.first) },
                    )
                }
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(1.dp)
                        .background(Color(0xFFF2F6F9)),
                )
            }
        }
    }
}