package com.vietinbank.feature_home.ui.screen.account_balance

import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AlertDialog
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.BiometricHelper
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.base.dialog.ListAccNumberDialog
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_home.ui.navigation.AccountBalanceNavigator
import com.vietinbank.feature_home.utils.AccountType
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class AccountBalanceFragment() : BaseFragment<AccountBalanceViewModel>() {
    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var accountBalanceNavigator: AccountBalanceNavigator

    override val viewModel: AccountBalanceViewModel by viewModels()
    override val useCompose: Boolean = true
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.isSetupPin()
        viewModel.getListAccNumber()
        viewModel.updateListRegisAccNumber()
        handleSingleEvent {
            when (it) {
                AccountBalanceEvent.NotificationEvent -> {
                    showListDialog(
                        list = viewModel.uiState.value.listAccNumber,
                        onItemSelected = { accNumber ->
                            accountBalanceNavigator.goToNotificationSetting(accNumber = accNumber, tag = viewModel.uiState.value.tag.value)
                        },
                    )
                }

                AccountBalanceEvent.PinSetupEvent -> {
                    accountBalanceNavigator.goToOttPinSetup()
                }

                AccountBalanceEvent.ChangePinEvent -> {
                    accountBalanceNavigator.goToChangePinSetup()
                }

                is AccountBalanceEvent.ShowBiometricPrompt -> {
                    showBiometricPrompt()
                }

                is AccountBalanceEvent.ShowBiometricSetupRequired -> {
                    showBiometricSetupRequiredDialog()
                }

                is AccountBalanceEvent.ShowError -> {
                    showErrorDialog(it.message)
                }

                AccountBalanceEvent.NavigateBackEvent -> accountBalanceNavigator.popBackStack()
                AccountBalanceEvent.GotoRegisterLoanAccountAction -> {
                    when (viewModel.uiState.value.tag) {
                        AccountType.UnDefine -> {}
                        AccountType.PaymentAccount -> {
                        }
                        AccountType.IdentityAccount -> {
                            appNavigator.goToRegisterOTT(tag = Tags.IDENTITY_ACCOUNT_TYPE_REG, flowSetting = "Y") // edit CIF
                        }
                        AccountType.LoanAccount -> {
                            appNavigator.goToRegisterOTT(tag = Tags.LOAN_ACCOUNT_TYPE, flowSetting = "Y") // edit CIF
                        }
                    }
                }

                AccountBalanceEvent.NotYetImplemented -> {
                    showNoticeDialog("Chức năng đang xây dựng")
                }
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.uiState.collectAsState()
        AppTheme {
            AccountBalanceScreen(
                uiState = uiState,
                onAction = viewModel::onAction,
            )
        }
    }

    private fun showListDialog(list: List<String>, onItemSelected: (String) -> Unit) {
        val dialog = ListAccNumberDialog.Companion.newInstance(
            title = "Số tài khoản nhận BĐSD",
            listAcc = list,
        )

        dialog.setOnItemClickListener {
            onItemSelected(it)
        }

        dialog.show(childFragmentManager, "ListAccNumberDialog")
    }

    private fun showBiometricPrompt() {
        BiometricHelper.showBiometricOrDeviceCredentialPrompt(
            fragment = this,
            title = "Xác thực",
            subtitle = "Vui lòng xác thực để hoàn tất cài đặt mã PIN",
            onSuccess = {
                viewModel.onBiometricSuccess()
            },
            onError = { errorMessage ->
                viewModel.onBiometricError(errorMessage)
            },
        )
    }

    private fun showBiometricSetupRequiredDialog() {
        AlertDialog.Builder(requireContext())
            .setTitle("Thông báo")
            .setMessage("Quý khách cần cài đặt khóa màn hình (PIN/Password/Pattern) hoặc vân tay để sử dụng tính năng này")
            .setPositiveButton("Cài đặt") { _, _ ->
                // Navigate to device security settings
                val intent =
                    android.content.Intent(android.provider.Settings.ACTION_SECURITY_SETTINGS)
                startActivity(intent)
            }
            .setNegativeButton("Hủy", null)
            .show()
    }

    private fun showErrorDialog(message: String) {
        showNoticeDialog(
            message = message,
            cancelable = false,
            positiveAction = {
                appNavigator.popBackStack()
            },
        )
    }
}
