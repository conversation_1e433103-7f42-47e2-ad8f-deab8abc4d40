package com.vietinbank.feature_home.ui.view.home_card

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.extensions.buildBoldText
import com.vietinbank.core_ui.components.ShimmerLoading
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.dismissRippleClickable
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_home.R
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

data class MyRequestUiModelState(
    val updatedTime: String = "",
    val list: List<FunctionItemData> = listOf(),
    val totalTransaction: String = "",
    val isLoading: Boolean = false,
    val isEnableLoading: Boolean = true,
)

sealed class MyRequestUiState {
    data object Loading : MyRequestUiState()

    data class Empty(
        val updatedTime: String = "",
        val isEnableLoading: Boolean,
    ) : MyRequestUiState()

    data class LoadingFail(
        val updatedTime: String = "",
        val isEnableLoading: Boolean,
    ) : MyRequestUiState()

    data class Success(
        val items: List<FunctionItemData>,
        val totalTransaction: String,
        val updatedTime: String,
        val isEnableLoading: Boolean,
    ) : MyRequestUiState()
}

@Composable
fun MyRequestCard(
    modifier: Modifier = Modifier,
    uiState: MyRequestUiState,
    clickLoading: () -> Unit,
    clickSeeAll: () -> Unit,
    onClickItem: (String) -> Unit,
) {
    when (uiState) {
        is MyRequestUiState.Empty -> {
            EmptyMyRequestCard(
                isEnableLoading = uiState.isEnableLoading,
                updatedTime = uiState.updatedTime,
                clickLoading = clickLoading,
            )
        }

        MyRequestUiState.Loading -> {
            Column(
                modifier = modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(FDS.Sizer.Padding.padding32))
                    .background(Color.White)
                    .padding(FDS.Sizer.Padding.padding24),
                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding16),
            ) {
                LoadingText(
                    title = stringResource(R.string.home_pending_transaction),
                    isLoading = true,
                    isEnableLoading = true,
                    onClickLoading = clickLoading,
                    updatedTime = "",
                )
                ShimmerLoading()
                ShimmerLoading()
            }
        }

        is MyRequestUiState.Success -> {
            val totalBoldText =
                stringResource(R.string.home_total_transaction_bold, uiState.totalTransaction)
            Column(
                modifier = modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(FDS.Sizer.Padding.padding32))
                    .background(Color.White)
                    .padding(FDS.Sizer.Padding.padding24),
            ) {
                LoadingText(
                    title = stringResource(R.string.home_pending_transaction),
                    isLoading = false,
                    isEnableLoading = uiState.isEnableLoading,
                    onClickLoading = clickLoading,
                    updatedTime = uiState.updatedTime,
                )

                uiState.items.forEachIndexed { index, item ->
                    MyRequestItem(
                        itemData = item,
                        showTopBorder = index != 0,
                        onClickItem = {
                            onClickItem(it)
                        },
                    )
                }

                FooterInsight(
                    annotatedText = stringResource(
                        R.string.home_total_transaction,
                        totalBoldText,
                    ).buildBoldText(
                        listOf(totalBoldText),
                    ),
                    buttonText = stringResource(R.string.home_see_all),
                    buttonAction = clickSeeAll,
                )
            }
        }

        is MyRequestUiState.LoadingFail -> {
            EmptyMyRequestCard(
                title = stringResource(R.string.home_error_balance),
                updatedTime = uiState.updatedTime,
                clickLoading = clickLoading,
                isEnableLoading = uiState.isEnableLoading,
            )
        }
    }
}

@Composable
fun FooterInsight(
    modifier: Modifier = Modifier,
    guideText: String = "",
    annotatedText: AnnotatedString? = null,
    buttonText: String,
    buttonAction: () -> Unit,
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding8, Alignment.CenterHorizontally),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        if (!annotatedText.isNullOrBlank()) {
            FoundationText(
                text = annotatedText,
                modifier = Modifier.weight(1f),
                color = FDS.Colors.characterPrimary,
                style = FDS.Typography.captionCaptionL,
            )
        } else {
            FoundationText(
                text = guideText,
                modifier = Modifier.weight(1f),
                color = FDS.Colors.characterPrimary,
                style = FDS.Typography.captionCaptionL,
            )
        }
        Box(
            modifier = Modifier
                .clip(RoundedCornerShape(FDS.Sizer.Padding.padding32))
                .border(
                    width = 1.dp,
                    shape = RoundedCornerShape(FDS.Sizer.Padding.padding32),
                    color = FDS.Colors.homeBorderButton,
                )
                .padding(
                    horizontal = FDS.Sizer.Padding.padding16,
                    vertical = FDS.Sizer.Padding.padding8,
                )
                .safeClickable {
                    buttonAction()
                },
        ) {
            FoundationText(
                text = buttonText,
                color = FDS.Colors.homeTextButton,
                style = FDS.Typography.interactionSmallButton,
            )
        }
    }
}

@Composable
fun EmptyMyRequestCard(
    modifier: Modifier = Modifier,
    updatedTime: String = "",
    isEnableLoading: Boolean = true,
    title: String = stringResource(R.string.home_empty_my_request),
    clickLoading: () -> Unit,
) {
    val bgColor = FDS.Colors.foundationDarkButtonPressedPrimary
    Column(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(FDS.Sizer.Padding.padding32))
            .drawBehind {
                drawRoundRect(
                    brush = Brush.linearGradient(
                        listOf(
                            bgColor,
                            Color.White,
                            Color.White,
                        ),
                        start = Offset(0.5f, 0f),
                        end = Offset(0.5f, size.height),
                    ),
                )
            }
            .padding(FDS.Sizer.Padding.padding24),
        verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding16),
    ) {
        Image(
            painter = painterResource(R.drawable.home_empty_icon),
            contentDescription = "",
            modifier = Modifier.size(FDS.Sizer.Icon.icon96),
        )
        LoadingText(
            title = title,
            isLoading = false,
            isEnableLoading = isEnableLoading,
            onClickLoading = clickLoading,
            updatedTime = updatedTime,
        )
    }
}

@Composable
fun StrokeLine(
    modifier: Modifier = Modifier,
    showBorder: Boolean = true,
) {
    if (showBorder) {
        Box(
            modifier = modifier
                .fillMaxWidth()
                .height(FDS.Sizer.Stroke.stroke1)
                .background(AppColors.borderColor),
        )
    }
}

@Composable
fun LoadingText(
    isLoading: Boolean,
    isEnableLoading: Boolean,
    title: String,
    updatedTime: String,
    onClickLoading: () -> Unit,
) {
    Row(
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding8),
        ) {
            FoundationText(
                text = title,
                color = FDS.Colors.tabTextActiveInline,
                style = FDS.Typography.headingH3,
            )
            if (isLoading) {
                FoundationText(
                    text = stringResource(R.string.home_update_loading_text),
                    style = FDS.Typography.captionCaptionL,
                    color = FDS.Colors.tabTextInactive,
                )
            } else {
                FoundationText(
                    text = stringResource(
                        R.string.home_update_guide,
                        updatedTime,
                    ).buildBoldText(
                        listOf(updatedTime),
                    ),
                    style = FDS.Typography.captionCaptionL,
                    color = FDS.Colors.tabTextInactive,
                )
            }
        }
        if (isEnableLoading) {
            Image(
                modifier = Modifier
                    .size(FDS.Sizer.Padding.padding24)
                    .safeClickable {
                        onClickLoading()
                    },
                painter = painterResource(R.drawable.ic_home_loading),
                contentDescription = "",
            )
        }
    }
}

@Composable
fun MyRequestItem(
    modifier: Modifier = Modifier,
    showTopBorder: Boolean = false,
    itemData: FunctionItemData,
    isHavePadding: Boolean = true,
    onClickItem: (String) -> Unit,
) {
    Column(
        modifier = modifier.fillMaxWidth(),
    ) {
        StrokeLine(showBorder = showTopBorder)
        Row(
            modifier = Modifier
                .then(
                    if (isHavePadding) {
                        Modifier.padding(vertical = FoundationDesignSystem.Sizer.Padding.padding16)
                    } else {
                        Modifier
                    },
                )
                .fillMaxWidth()
                .dismissRippleClickable {
                    onClickItem(itemData.functionId ?: "")
                },
            horizontalArrangement = Arrangement.spacedBy(
                FoundationDesignSystem.Sizer.Padding.padding8,
                Alignment.CenterHorizontally,
            ),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Image(
                painter = painterResource(itemData.iconRes),
                contentDescription = "",
            )
            FoundationText(
                modifier = Modifier.weight(1f),
                text = itemData.functionName,
                style = FoundationDesignSystem.Typography.interactionSmallButton,
                color = FoundationDesignSystem.Colors.backgroundBgScreen,
            )
            if (!itemData.valueRes.isNullOrBlank()) {
                Box(
                    modifier = Modifier
                        .size(FoundationDesignSystem.Sizer.Padding.padding24)
                        .clip(CircleShape)
                        .background(FoundationDesignSystem.Colors.stateBadgeCounting),
                    contentAlignment = Alignment.Center,
                ) {
                    FoundationText(
                        text = itemData.valueRes,
                        color = Color.White,
                        style = FoundationDesignSystem.Typography.material3Typography.labelSmall,
                    )
                }
            }
        }
    }
}