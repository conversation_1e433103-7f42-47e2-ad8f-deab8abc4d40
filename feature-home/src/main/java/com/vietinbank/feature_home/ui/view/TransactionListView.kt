package com.vietinbank.feature_home.ui.view

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_domain.models.checker.SubTranTypeListDomain
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_home.R
import com.vietinbank.feature_home.ui.screen.home.SectionTitle

/**
 * Created by vandz on 11/3/25.
 */
/**
 * Hiển thị danh sách loại yêu cầu chờ duyệt
 * @param transactions Danh sách giao dịch từ API
 * @param onItemClick Callback khi người dùng nhấn vào một item
 */
@Composable
fun TransactionListView(
    transactions: List<SubTranTypeListDomain>,
    onItemClick: (SubTranTypeListDomain) -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
    ) {
        // Tiêu đề section
        SectionTitle(
            title = "Loại yêu cầu chờ duyệt",
            modifier = Modifier.fillMaxWidth(),
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Khung chứa danh sách với bo góc 10.dp
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(10.dp))
                .background(AppColors.cardBackground)
                .border(
                    width = 0.5.dp,
                    color = AppColors.borderColor,
                    shape = RoundedCornerShape(10.dp),
                ),
        ) {
            transactions.forEachIndexed { index, transaction ->
                TransactionListItem(
                    transaction = transaction,
                    onItemClick = { onItemClick(transaction) },
                    isFirst = index == 0,
                    isLast = index == transactions.size - 1,
                )

                // Thêm divider nếu không phải item cuối cùng
                if (index < transactions.size - 1) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(0.5.dp)
                            .padding(start = 56.dp)
                            .background(AppColors.borderColor),
                    )
                }
            }
        }
    }
}

/**
 * Item trong danh sách loại yêu cầu chờ duyệt
 * @param transaction Dữ liệu giao dịch cần hiển thị
 * @param onItemClick Callback khi người dùng nhấn vào item
 * @param isFirst True nếu là item đầu tiên trong danh sách
 * @param isLast True nếu là item cuối cùng trong danh sách
 */
@Composable
fun TransactionListItem(
    transaction: SubTranTypeListDomain,
    onItemClick: () -> Unit,
    isFirst: Boolean = false,
    isLast: Boolean = false,
    modifier: Modifier = Modifier,
) {
    // Xác định hình dạng bo góc cho item đầu và cuối
    val shape = when {
        isFirst -> RoundedCornerShape(topStart = 10.dp, topEnd = 10.dp)
        isLast -> RoundedCornerShape(bottomStart = 10.dp, bottomEnd = 10.dp)
        else -> RoundedCornerShape(0.dp)
    }

    Row(
        modifier = modifier
            .fillMaxWidth()
            .clip(shape)
            .background(AppColors.cardBackground)
            .safeClickable { onItemClick() }
            .padding(horizontal = 16.dp, vertical = 12.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // Icon bên trái (center|start)
        Box(
            modifier = Modifier
                .size(32.dp)
                .clip(RoundedCornerShape(8.dp))
                .background(AppColors.inputBackground),
            contentAlignment = Alignment.Center,
        ) {
            // Chọn icon dựa vào loại giao dịch
            val iconRes = when (transaction.tranType) {
                "TRANSFER" -> R.drawable.ic_transfer
                "BHXH" -> R.drawable.ic_insurance
                "NSNN" -> R.drawable.ic_nsnn
                else -> R.drawable.ic_transaction
            }
            Image(
                painter = painterResource(id = iconRes),
                contentDescription = "Icon ${transaction.servicetypename}",
                modifier = Modifier.size(24.dp),
            )
        }

        Spacer(modifier = Modifier.width(12.dp))

        // Phần nội dung (layout dọc với title và content)
        Column(
            modifier = Modifier
                .weight(1f)
                .padding(vertical = 4.dp),
        ) {
            // Title - Tên dịch vụ
            BaseText(
                text = transaction.servicetypename ?: "",
                textSize = 14.sp,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                color = AppColors.textPrimary,
            )

            Spacer(modifier = Modifier.height(4.dp))

            // Content - Số lượng yêu cầu chờ duyệt
            BaseText(
                text = "${transaction.count_transaction ?: "0"} yêu cầu chờ duyệt",
                textSize = 12.sp,
                color = AppColors.textPrimary,
            )
        }

        // Icon ">" bên phải ngoài cùng
        Box(
            modifier = Modifier.size(24.dp),
            contentAlignment = Alignment.Center,
        ) {
            Image(
                painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_right),
                contentDescription = "Icon ${transaction.servicetypename}",
                modifier = Modifier.size(24.dp),
            )
        }
    }
}