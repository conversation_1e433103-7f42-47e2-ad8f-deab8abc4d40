package com.vietinbank.feature_home.ui.screen.verify_pin

import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.biometric.IBiometricManager
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.CustomEncryptedPrefs
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class VerifyPinViewModel @Inject constructor(
    private val biometricManager: IBiometricManager,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    private val prefs: CustomEncryptedPrefs,
    override val sessionManager: ISessionManager,
) : BaseViewModel() {

    companion object {
        private const val MAX_ATTEMPTS = 5
        private const val LOCKOUT_DURATION = 30 * 60 * 1000L // 30 minutes
    }

    private val _state = MutableStateFlow(VerifyPinState())
    val state: StateFlow<VerifyPinState> = _state.asStateFlow()

    private val _events = Channel<VerifyPinEvent>(Channel.BUFFERED)
    val events = _events.receiveAsFlow()

    init {
        checkInitialState()
    }

    private fun checkInitialState() {
        viewModelScope.launch {
            // Check if user has PIN setup
            if (!hasPinSetup()) {
                _events.send(VerifyPinEvent.NoPinSetup)
                return@launch
            }

            // Check lockout status
            if (isLockedOut()) {
                return@launch
            }

            // Check biometric availability
            val biometricAvailable = biometricManager.canAuthenticate()
            _state.update { it.copy(biometricAvailable = biometricAvailable) }
            if (biometricAvailable) {
                _events.send(VerifyPinEvent.ShowBiometricPrompt)
            }
        }
    }

    fun onAction(action: VerifyPinAction) {
        when (action) {
            is VerifyPinAction.OnBackPressed -> {
                viewModelScope.launch {
                    _events.send(VerifyPinEvent.NavigateBack)
                }
            }
            is VerifyPinAction.OnPinChanged -> {
                updatePin(action.pin)
            }
            is VerifyPinAction.OnContinueClicked -> {
                verifyPin()
            }
            is VerifyPinAction.OnUseBiometricClicked -> {
                viewModelScope.launch {
                    _events.send(VerifyPinEvent.ShowBiometricPrompt)
                }
            }
            is VerifyPinAction.OnForgotPinClicked -> {
                viewModelScope.launch {
                    _events.send(VerifyPinEvent.NavigateToForgotPin)
                }
            }
        }
    }

    private fun updatePin(pin: String) {
        if (pin.length <= 6 && pin.all { it.isDigit() }) {
            _state.update { currentState ->
                currentState.copy(
                    pin = pin,
                    pinError = null,
                )
            }
        }
    }

    private fun verifyPin() {
        val currentState = _state.value

        // Validate input
        if (currentState.pin.isEmpty()) {
            _state.update { it.copy(pinError = "Quý khách vui lòng nhập mã PIN") }
            return
        }

        if (currentState.pin.length < 6) {
            _state.update { it.copy(pinError = "Mã PIN phải có đủ 6 chữ số") }
            return
        }

        viewModelScope.launch {
            _state.update { it.copy(isLoading = true) }

            // Check lockout again
            if (isLockedOut()) {
                _state.update { it.copy(isLoading = false) }
                return@launch
            }

            // Verify PIN
            val isValid = checkPin(currentState.pin)

            if (isValid) {
                // Clear attempts
                clearAttempts()
                // Send success event
                _events.send(VerifyPinEvent.VerifySuccess)
            } else {
                // Handle failed attempt
                handleFailedAttempt()
            }

            _state.update { it.copy(isLoading = false) }
        }
    }

    private fun checkPin(inputPin: String): Boolean {
        val cifNo = userProf.getCifNo() ?: return false
        val key = "BĐSD_PIN_$cifNo"
        val savedPin = prefs.getString(key, encrypted = true)
        return inputPin == savedPin
    }

    private fun hasPinSetup(): Boolean {
        val cifNo = userProf.getCifNo() ?: return false
        val key = "BĐSD_PIN_$cifNo"
        val savedPin = prefs.getString(key, encrypted = true)
        return !savedPin.isNullOrEmpty()
    }

    private fun isLockedOut(): Boolean {
        val lockoutTime = prefs.getLong("PIN_LOCKOUT_TIME", 0, encrypted = false)
        val currentTime = System.currentTimeMillis()

        return if (lockoutTime > 0 && currentTime < lockoutTime) {
            val remainingTime = lockoutTime - currentTime
            val remainingMinutes = (remainingTime / 1000 / 60).toInt()
            _state.update {
                it.copy(
                    isLockedOut = true,
                    lockoutMessage = "Tài khoản đã bị khóa. Vui lòng thử lại sau $remainingMinutes phút",
                )
            }
            viewModelScope.launch {
                _events.send(VerifyPinEvent.Lockout(remainingTime))
            }
            true
        } else {
            _state.update { it.copy(isLockedOut = false, lockoutMessage = null) }
            false
        }
    }

    private fun handleFailedAttempt() {
        viewModelScope.launch {
            val attemptCount = prefs.getInt("PIN_ATTEMPT_COUNT", 0, encrypted = false) + 1

            if (attemptCount >= MAX_ATTEMPTS) {
                // Lock for 30 minutes
                val lockoutTime = System.currentTimeMillis() + LOCKOUT_DURATION
                prefs.setLong("PIN_LOCKOUT_TIME", lockoutTime, encrypted = false)
                prefs.setInt("PIN_ATTEMPT_COUNT", 0, encrypted = false)

                _state.update {
                    it.copy(
                        pin = "",
                        pinError = "Bạn đã nhập sai quá nhiều lần. Vui lòng thử lại sau 30 phút",
                    )
                }
                _events.send(VerifyPinEvent.MaxAttemptsReached)
            } else {
                prefs.setInt("PIN_ATTEMPT_COUNT", attemptCount, encrypted = false)
                val remainingAttempts = MAX_ATTEMPTS - attemptCount

                _state.update {
                    it.copy(
                        pin = "",
                        pinError = "Mã PIN không đúng. Còn $remainingAttempts lần thử",
                        remainingAttempts = remainingAttempts,
                    )
                }
                _events.send(VerifyPinEvent.WrongPin(remainingAttempts))
            }
        }
    }

    private fun clearAttempts() {
        prefs.setInt("PIN_ATTEMPT_COUNT", 0, encrypted = false)
        prefs.setLong("PIN_LOCKOUT_TIME", 0, encrypted = false)
    }

    fun onBiometricSuccess() {
        viewModelScope.launch {
            // Clear attempts
            clearAttempts()
            // Send success event
            _events.send(VerifyPinEvent.VerifySuccess)
        }
    }

    fun onBiometricError(errorMessage: String) {
        viewModelScope.launch {
            _events.send(VerifyPinEvent.ShowError(errorMessage))
        }
    }
}

data class VerifyPinState(
    val pin: String = "",
    val pinError: String? = null,
    val isLoading: Boolean = false,
    val biometricAvailable: Boolean = false,
    val remainingAttempts: Int = MAX_ATTEMPTS,
    val isLockedOut: Boolean = false,
    val lockoutMessage: String? = null,
) {
    companion object {
        private const val MAX_ATTEMPTS = 5
    }
}

sealed class VerifyPinEvent {
    data object NavigateBack : VerifyPinEvent()
    data object NoPinSetup : VerifyPinEvent()
    data object ShowBiometricPrompt : VerifyPinEvent()
    data object VerifySuccess : VerifyPinEvent()
    data object NavigateToForgotPin : VerifyPinEvent()
    data object MaxAttemptsReached : VerifyPinEvent()
    data class WrongPin(val remainingAttempts: Int) : VerifyPinEvent()
    data class Lockout(val remainingTime: Long) : VerifyPinEvent()
    data class ShowError(val message: String) : VerifyPinEvent()
}

sealed class VerifyPinAction {
    data object OnBackPressed : VerifyPinAction()
    data class OnPinChanged(val pin: String) : VerifyPinAction()
    data object OnContinueClicked : VerifyPinAction()
    data object OnUseBiometricClicked : VerifyPinAction()
    data object OnForgotPinClicked : VerifyPinAction()
}
