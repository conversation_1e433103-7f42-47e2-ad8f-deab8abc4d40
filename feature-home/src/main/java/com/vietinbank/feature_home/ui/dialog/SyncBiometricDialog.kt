package com.vietinbank.feature_home.ui.dialog

import android.os.Parcelable
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.fragment.app.FragmentManager
import com.vietinbank.core_common.utils.UserInfoDataHolder
import com.vietinbank.core_ui.base.dialog.BaseDialog
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.dialog.DialogLayout
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.feature_home.R
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.parcelize.Parcelize
import javax.inject.Inject
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Parcelize
data class SyncBiometricResult(
    val confirmed: Boolean,
    val timestamp: Long = System.currentTimeMillis(),
) : Parcelable

@AndroidEntryPoint
class SyncBiometricDialog : BaseDialog<SyncBiometricResult>() {

    companion object {
        private const val ARG_TITLE = "arg_title"

        const val KEY_SYNC_BIOMETRIC_RESULT = "KEY_SYNC_BIOMETRIC_RESULT"

        fun show(
            fragmentManager: FragmentManager,
            title: String = "",
        ) {
            val dialog = SyncBiometricDialog().apply {
                arguments = android.os.Bundle().apply {
                    putString(ARG_TITLE, title)
                }
            }
            dialog.show(fragmentManager, "SyncBiometricDialog")
        }
    }

    @Inject
    lateinit var imageLoader: CoilImageLoader

    // Dialog configuration
    override val layout: DialogLayout = DialogLayout.BottomSheet // Bottom sheet style
    override val maxWidthDp: Int = 600 // Max width for tablets
    override val resultKey = KEY_SYNC_BIOMETRIC_RESULT
    override val allowTouchDismiss = false // Force user to make a choice
    override val requiresSecureFlag = false

    @Composable
    override fun DialogContent(
        visible: Boolean,
        onDismissRequest: () -> Unit,
        onResult: (SyncBiometricResult) -> Unit,
    ) {
        val title = arguments?.getString(ARG_TITLE) ?: ""
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(FDS.Sizer.Padding.padding8),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Surface(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
                color = FDS.Colors.white,
                shadowElevation = FDS.Effects.elevationLg,
            ) {
                Column {
                    // Header - matching Figma exactly
                    FoundationText(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = FDS.Sizer.Padding.padding24),
                        text = stringResource(R.string.home_title_notice),
                        style = FDS.Typography.headingH3,
                        color = FDS.Colors.characterHighlighted,
                        textAlign = TextAlign.Center,
                    )

                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

                    HorizontalDivider(
                        color = FDS.Colors.strokeDivider,
                        thickness = FDS.Sizer.Stroke.stroke1,
                    )
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(FDS.Sizer.Padding.padding24),
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        imageLoader.LoadByteArray(
                            modifier = Modifier
                                .fillMaxWidth()
                                .aspectRatio(1f)
                                .clip(RoundedCornerShape(32.dp))
                                .background(Color.LightGray),
                            byteArray = UserInfoDataHolder.getBiometricImage(),
                            placeholderRes = R.drawable.white_background,
                            errorRes = R.drawable.white_background,
                            isCache = false,
                        )

                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

                        // Message
                        FoundationText(
                            text = stringResource(R.string.home_show_ekyc_dialog_guide),
                            style = FDS.Typography.bodyB2,
                            color = FDS.Colors.characterPrimary,
                            textAlign = TextAlign.Left,
                            modifier = Modifier.fillMaxWidth(),
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

            // Action buttons
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap12),
            ) {
                FoundationButton(
                    text = stringResource(R.string.home_button_negative_ekyc),
                    onClick = {
                        onDismissRequest()
                    },
                    isLightButton = false,
                    modifier = Modifier.weight(1f),
                )

                FoundationButton(
                    text = stringResource(R.string.home_button_positive_ekyc),
                    onClick = {
                        onResult(SyncBiometricResult(confirmed = true))
                    },
                    modifier = Modifier.weight(1f),
                )
            }
        }
    }
}
