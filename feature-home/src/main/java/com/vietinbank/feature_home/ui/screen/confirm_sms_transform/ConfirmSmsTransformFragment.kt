package com.vietinbank.feature_home.ui.screen.confirm_sms_transform

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_home.ui.navigation.AccountBalanceNavigator
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class ConfirmSmsTransformFragment : BaseFragment<ConfirmSmsTransformViewModel>() {
    override val viewModel: ConfirmSmsTransformViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var accountBalanceNavigator: AccountBalanceNavigator

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.updateAlertType(
            arguments?.getString(Tags.OTT_ALERT_TYPE) ?: "",
            arguments?.getString(Tags.OTT_ROUTE_TAG) ?: "",
            arguments?.getString(Tags.OTT_ACTION_TYPE) ?: "",
        )
        handleSingleEvent { event ->
            when (event) {
                ConfirmSmsEvent.NavigateBackEvent -> {
                    accountBalanceNavigator.popBackStack()
                }

                ConfirmSmsEvent.ConfirmTransferSMSEvent -> {
                    confirmTransferSmsDialog()
                }

                ConfirmSmsEvent.ConfirmRegisIdentityEvent -> {
                    confirmRegisIdentityDialog()
                }

                ConfirmSmsEvent.ConfirmSetupPinEvent -> {
                    confirmSetupPinDialog()
                }

                ConfirmSmsEvent.ShowEmptyListErrorDialog -> {
                    val message = when (viewModel.uiState.value.tag) {
                        Tags.IDENTITY_ACCOUNT_TYPE_REG -> " Quý khách vui lòng chọn tài khoản đăng ký nhận thông báo OTT Biến động số dư qua ứng dụng eFAST"
                        else -> "Quý khách vui lòng chọn tài khoản đăng ký nhận BĐSD qua tin nhắn SMS"
                    }
                    showNoticeDialog(
                        message,
                    )
                }

                ConfirmSmsEvent.ShowUnCheckValidateDialog -> {
                    showNoticeDialog(
                        "Quý khách vui lòng đồng ý với điều khoản và điều kiện sử dụng dịch vụ",
                    )
                }
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.uiState.collectAsState()

        ConfirmSmsTransformScreen(
            uiState = uiState,
            onAction = viewModel::onAction,
        )
    }

    private fun confirmTransferSmsDialog() {
        showNoticeDialog(
            "Qúy khách đã chuyển đổi nhận thông báo BĐSD của Số tài khoản ${
                viewModel.uiState.value.listReg.map { it.accountNumber }
                    .joinToString(", ")
            } thành công",
            positiveAction = {
                accountBalanceNavigator.popToNotificationSetting()
            },
        )
    }

    private fun confirmRegisIdentityDialog() {
        showNoticeDialog(
            "Quý khách đã kích hoạt dịch vụ nhận thông báo biến động số dư thành công.",
            positiveAction = {
                accountBalanceNavigator.popToAccountBalanceScreen()
            },
        )
    }

    private fun confirmSetupPinDialog() {
        showConfirmDialog(
            "Quý khách đã kích hoạt dịch vụ nhận thông báo BĐSD thành công. " +
                "\nĐể đảm bảo an toàn thông tin của khách hàng, quý khách có thể cài đặt mã PIN để xem tin BĐSD trước đăng nhập.",
            positiveButtonText = "Cài đặt PIN",
            negativeButtonText = "Bỏ qua",
            positiveAction = {
                accountBalanceNavigator.popToOttPinSetup()
            },
            negativeAction = {
                accountBalanceNavigator.popToAccountBalanceScreen()
            },
        )
    }
}