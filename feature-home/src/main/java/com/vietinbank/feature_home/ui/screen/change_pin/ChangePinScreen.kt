package com.vietinbank.feature_home.ui.screen.change_pin

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ui.base.compose.BaseAppBar
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.base.compose.InputMode
import com.vietinbank.core_ui.base.compose.PinPasswordInput
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.feature_home.ui.view.PinSetupBottomButtons

@Composable
fun ChangePinScreen(
    uiState: ChangePinState,
    onAction: (ChangePinAction) -> Unit,
) {
    Column(
        modifier = Modifier
            .fillMaxSize(),
    ) {
        // App Bar
        BaseAppBar(
            title = "Cài đặt PIN",
            onBackClick = {
                onAction(ChangePinAction.OnBackPressed)
            },
        )

        // Content
        Column(
            modifier = Modifier
                .padding(top = 20.dp)
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 24.dp),
            verticalArrangement = Arrangement.spacedBy(20.dp),
        ) {
            BaseText(
                modifier = Modifier.fillMaxWidth(),
                text = "Quý khách đang yêu cầu đổi mã PIN khi xem thông tin BĐSD. Vui lòng nhập mã PIN hiện tại để xác nhận và cài đặt PIN mới.",
                textColor = Color.White,
                textSize = 14.sp,
                textAlign = TextAlign.Start,
            )

            // Old PIN input
            ChangePinInputView(
                title = "Nhập mã PIN hiện tại",
                isLoading = uiState.isLoading,
                onValueChange = {
                    onAction(ChangePinAction.OnOldPinChanged(it))
                },
                pinValue = uiState.oldPin,
                pinError = uiState.oldPinError,
            )

            // New PIN input
            ChangePinInputView(
                title = "Nhập mã PIN mới",
                isLoading = uiState.isLoading,
                onValueChange = {
                    onAction(ChangePinAction.OnNewPinChanged(it))
                },
                pinValue = uiState.newPin,
                pinError = uiState.newPinError,
            )

            // Confirm PIN input
            ChangePinInputView(
                title = "Nhập lại mã pin mới",
                isLoading = uiState.isLoading,
                onValueChange = {
                    onAction(ChangePinAction.OnConfirmPinChanged(it))
                },
                pinValue = uiState.confirmPin,
                pinError = uiState.confirmPinError,
            )
        }

        Spacer(modifier = Modifier.weight(1f))

        // Bottom buttons
        PinSetupBottomButtons(
            isLoading = uiState.isLoading,
            onCancelClick = {
                onAction(ChangePinAction.OnBackPressed)
            },
            onContinueClick = {
                onAction(ChangePinAction.OnContinueClicked)
            },
        )
    }
}

@Composable
private fun ChangePinInputView(
    modifier: Modifier = Modifier,
    title: String,
    isLoading: Boolean,
    onValueChange: (String) -> Unit,
    pinValue: String,
    pinError: String?,
) {
    PinPasswordInput(
        modifier = modifier,
        value = pinValue,
        onValueChange = { onValueChange(it) },
        inputMode = InputMode.PASSWORD,
        hint = title,
        enabled = !isLoading,
        isError = pinError != null,
        errorMessage = pinError,
        textColor = Color.Gray,
        backgroundColor = Color.White,
        borderColor = Color.White,
        focusedBorderColor = AppColors.primary,
        errorBorderColor = Color.Red,
        hintColor = Color.Gray,
    )
}