package com.vietinbank.feature_home.ui.screen.account_balance

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ui.base.compose.BaseCustomSwitch
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_home.R
import com.vietinbank.feature_home.ui.view.AccountBalanceItem
import com.vietinbank.feature_home.utils.AccountType

@Composable
fun PaymentAccountTabView(
    uiState: AccountBalanceUiState,
    onAction: (AccountBalanceAction) -> Unit,
) {
    val titleContentPair by remember(uiState.tag) {
        derivedStateOf {
            when (uiState.tag) {
                AccountType.IdentityAccount -> {
                    "Thông báo BĐSD theo tài khoản định danh trên ứng dụng là gì?" to
                        "Là tin nhắn thông báo các khoản ghi có vào tài khoản định danh mà khách hàng được cấp bởi đơn vị chính. " +
                        "Tin nhắn được gửi qua ứng dụng VietinBank eFAST và được hiển thị ngay trên màn hình thiết bị di dộng đã " +
                        "cài ứng dụng eFAST. Khách hàng có thể mở ứng dụng eFAST để đọc toàn bộ các thông báo BĐSD đã được gửi."
                }

                AccountType.PaymentAccount, AccountType.LoanAccount -> {
                    "Thêm số tài khoản/công ty nhận BĐSD" to
                        "Là tin nhắn sẽ được gửi tới Khách hàng qua hệ thống thông báo của ứng dụng VietinBank eFAST và " +
                        "được hiển thị ngay trên màn hình thiết bị di động đã cài ứng dụng eFAST. Khách hàng có thể mở ứng dụng eFAST " +
                        "để đọc toàn bộ các thông báo BĐSD đã được gửi."
                }

                else -> {
                    "" to ""
                }
            }
        }
    }
    val title = titleContentPair.first
    val content = titleContentPair.second

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(15.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(20.dp),
    ) {
        ActiveServiceSession(
            title = title,
            content = content,
            action = onAction,
        )

        ListAccSession(
            uiState.listAccNumber,
        )

        NotificationSettingSession(
            action = onAction,
        )
        if (uiState.tag == AccountType.PaymentAccount) {
            PINSession(
                action = onAction,
                isChecked = uiState.setupPinState,
            )
        }
    }
}

@Composable
private fun ActiveServiceSession(
    title: String,
    content: String,
    action: (AccountBalanceAction) -> Unit,
) {
    Column() {
        BaseText(
            text = "Kích hoạt dịch vụ",
            color = Color(0xFFD6F4FF),
            modifier = Modifier.padding(bottom = 10.dp),
            textSize = 16.sp,
            fontCus = 2,
        )
        AccountBalanceItem(
            itemTitle = title,
            itemContent = content,
            shape = RoundedCornerShape(4.dp),
            trailingTitleAction = {
                Box(
                    modifier = Modifier
                        .border(
                            width = 1.dp,
                            color = Color(0xFFC4D7E5),
                            shape = RoundedCornerShape(4.dp),
                        )
                        .safeClickable {
                            action(AccountBalanceAction.GotoRegisterLoanAccountAction)
                        },
                    contentAlignment = Alignment.Center,
                ) {
                    BaseText(
                        modifier = Modifier.padding(horizontal = 12.dp, vertical = 4.dp),
                        text = "Cài đặt",
                        color = Color(0xFF1D4C9E),
                        fontWeight = FontWeight.Bold,
                        textSize = 12.sp,
                    )
                }
            },
        )
    }
}

@Composable
private fun ListAccSession(
    listAcc: List<String>,
) {
    Column() {
        BaseText(
            text = "Danh sách công ty nhận BĐSD",
            color = Color(0xFFD6F4FF),
            modifier = Modifier.padding(bottom = 10.dp),
            textSize = 16.sp,
            fontCus = 2,
        )
        listAcc.forEachIndexed { index, it ->
            val isSingle = listAcc.size == 1
            val isFirst = index == 0
            val isLast = index == listAcc.lastIndex

            val cornerShape = when {
                isSingle -> RoundedCornerShape(4.dp)
                isFirst -> RoundedCornerShape(topStart = 4.dp, topEnd = 4.dp)
                isLast -> RoundedCornerShape(bottomStart = 4.dp, bottomEnd = 4.dp)
                else -> RoundedCornerShape(0.dp)
            }

            AccountBalanceItem(
                itemTitle = it,
                shape = cornerShape,
                isShowDivideLine = !isLast && !isSingle,
                trailingAction = {
                    Box(
                        modifier = Modifier
                            .padding(start = 20.dp)
                            .background(
                                color = Color(0xffD6124C),
                                shape = RoundedCornerShape(4.dp),
                            ),
                        contentAlignment = Alignment.Center,
                    ) {
                        BaseText(
                            modifier = Modifier.padding(horizontal = 10.dp, vertical = 2.dp),
                            text = "0",
                            color = Color.White,
                            fontCus = 2,
                        )
                    }
                },
            )
        }
    }
}

@Composable
private fun NotificationSettingSession(
    action: (AccountBalanceAction) -> Unit,
) {
    Column() {
        BaseText(
            text = "Cài đặt thông báo",
            color = Color(0xFFD6F4FF),
            modifier = Modifier.padding(bottom = 10.dp),
            textSize = 16.sp,
            fontCus = 2,
        )
        AccountBalanceItem(
            itemTitle = "Cài đặt thông báo nhận BĐSD",
            shape = RoundedCornerShape(4.dp),
            trailingAction = {
                Image(
                    painter = painterResource(R.drawable.ic_right_arrow),
                    contentDescription = "",
                    modifier = Modifier.size(24.dp),
                )
            },
            onClickItem = {
                action(AccountBalanceAction.NotificationAction)
            },
        )
    }
}

@Composable
private fun PINSession(
    action: (AccountBalanceAction) -> Unit,
    isChecked: Boolean,
) {
    val shape = if (isChecked) {
        RoundedCornerShape(
            topStart = 4.dp,
            topEnd = 4.dp,
        )
    } else {
        RoundedCornerShape(4.dp)
    }
    Column() {
        BaseText(
            text = "Mã PIN",
            color = Color(0xFFD6F4FF),
            modifier = Modifier.padding(bottom = 10.dp),
            textSize = 16.sp,
            fontCus = 2,
        )
        AccountBalanceItem(
            itemTitle = "Cài đặt PIN",
            itemContent = "Xem tin BĐSD cần dùng mã PIN để tăng bảo mật thông tin",
            shape = shape,
            isShowDivideLine = true,
            trailingAction = {
                BaseCustomSwitch(
                    isChecked = isChecked,
                    onCheckedChange = {
                        action(AccountBalanceAction.PinSetupAction)
                    },
                )
            },
        )
        if (isChecked) {
            AccountBalanceItem(
                itemTitle = "Đổi PIN",
                itemContent = "Đổi mã PIN khác",
                shape = RoundedCornerShape(0.dp),
                isShowDivideLine = true,
                trailingAction = {
                    Image(
                        painter = painterResource(R.drawable.ic_right_arrow),
                        contentDescription = "",
                        modifier = Modifier.size(24.dp),
                    )
                },
                onClickItem = {
                    action(AccountBalanceAction.ChangePinAction)
                },
            )
            AccountBalanceItem(
                itemTitle = "Quên PIN",
                itemContent = "Đặt lại mã PIN mới",
                shape = RoundedCornerShape(bottomStart = 4.dp, bottomEnd = 4.dp),
                trailingAction = {
                    Image(
                        painter = painterResource(R.drawable.ic_right_arrow),
                        contentDescription = "",
                        modifier = Modifier.size(24.dp),
                    )
                },
                onClickItem = {
                    action(AccountBalanceAction.ForgetPinAction)
                },
            )
        }
    }
}