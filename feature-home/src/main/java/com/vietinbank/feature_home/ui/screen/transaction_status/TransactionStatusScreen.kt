package com.vietinbank.feature_home.ui.screen.transaction_status

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ui.base.compose.BaseAppBar
import com.vietinbank.core_ui.base.compose.BaseCustomSwitch
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.feature_home.ui.view.AccountBalanceItem

@Composable
fun TransactionStatusScreen(
    uiState: TransactionStatusUiState,
    onAction: (TransactionStatusAction) -> Unit,
) {
    val approveTitle = if (uiState.isChecker) "Chờ phê duyệt" else "Thành công"
    Column(
        modifier = Modifier.fillMaxSize(),
    ) {
        BaseAppBar(
            title = "Cài đặt thông báo",
            onBackClick = {
                onAction(TransactionStatusAction.OnNavigateBack)
            },
        )
        Column(
            modifier = Modifier.padding(horizontal = 15.dp),
            verticalArrangement = Arrangement.spacedBy(20.dp),
        ) {
            BaseText(
                text = "Quý khách vui lòng bật/tắt tùy chọn để nhận thông báo trạng thái giao dịch trên thiết bị",
                textSize = 16.sp,
                color = Color(0xFFD6F4FF),
            )
            AccountBalanceItem(
                itemTitle = "Nhận thông báo trạng thái giao dịch",
                itemContent = "Tắt/Bật hiển thị thông báo trạng thái “$approveTitle” của các giao dịch",
                shape = RoundedCornerShape(4.dp),
                trailingTitleAction = {
                    BaseCustomSwitch(
                        isChecked = uiState.approveState,
                        onCheckedChange = {
                            onAction(TransactionStatusAction.OnClickSwitch("approve"))
                        },
                    )
                },
            )
            if (!uiState.isChecker) {
                AccountBalanceItem(
                    itemTitle = "Nhận thông báo trạng thái giao dịch",
                    itemContent = "Tắt/Bật hiển thị thông báo trạng thái \"Từ chối” của các giao dịch",
                    shape = RoundedCornerShape(4.dp),
                    trailingTitleAction = {
                        BaseCustomSwitch(
                            isChecked = uiState.rejectState,
                            onCheckedChange = {
                                onAction(TransactionStatusAction.OnClickSwitch("reject"))
                            },
                        )
                    },
                )
            }
        }
    }
}