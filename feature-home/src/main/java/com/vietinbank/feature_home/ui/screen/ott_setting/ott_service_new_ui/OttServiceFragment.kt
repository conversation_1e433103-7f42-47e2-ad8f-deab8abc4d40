package com.vietinbank.feature_home.ui.screen.ott_setting.ott_service_new_ui

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.components.dialog.VerifyBiometricOtpDialog
import com.vietinbank.core_ui.components.dialog.VerifyBiometricOtpResult
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class OttServiceFragment : BaseFragment<OttServiceViewModel>() {
    override val viewModel: OttServiceViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        handleSingleEvent { event ->
            when (event) {
                OttServiceEvent.ShowOtpDialog -> {
                    showOtpDialog()
                }

                OttServiceEvent.VerifyOtpSuccess -> {
                    (
                        childFragmentManager.findFragmentByTag(
                            VerifyBiometricOtpDialog::class.java.name,
                        ) as? VerifyBiometricOtpDialog
                        )?.apply {
                        afterCallDone(true)
                        dismiss()
                    }
                }

                OttServiceEvent.VerifyOtpFail -> {
                    (
                        childFragmentManager.findFragmentByTag(
                            VerifyBiometricOtpDialog::class.java.name,
                        ) as? VerifyBiometricOtpDialog
                        )?.afterCallDone(false)
                }
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.uiState.collectAsState()

        OttServiceScreen(
            uiState = uiState,
            onAction = viewModel::onAction,
            onBackClick = { appNavigator.popBackStack() },
        )
    }

    private fun showOtpDialog() {
        val dialog = VerifyBiometricOtpDialog.newInstance(
            otpValiditySeconds = 270,
            phoneNum = "",
        )
        dialog.show(
            childFragmentManager,
            VerifyBiometricOtpDialog::class.java.name,
        )

        childFragmentManager.setFragmentResultListener(
            VerifyBiometricOtpDialog.BIOMETRIC_KEY,
            viewLifecycleOwner,
        ) { _, bundle ->
            val isCancelled = bundle.getBoolean("key_cancelled", false)
            if (!isCancelled) {
                val result = bundle.getParcelable<VerifyBiometricOtpResult>("key_result")
                result?.let {
                    viewModel.smsOtpVerify(it.otpValue)
                }
            } else {
                // user cancel
            }
        }
    }
}