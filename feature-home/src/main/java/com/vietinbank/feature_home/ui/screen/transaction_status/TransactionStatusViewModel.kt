package com.vietinbank.feature_home.ui.screen.transaction_status

import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_domain.models.ott_feature.OttStatusUpdateParams
import com.vietinbank.core_domain.usecase.ott_feature.OttFeatureUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.OneTimeEvent
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class TransactionStatusViewModel @Inject constructor(
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val sessionManager: ISessionManager,
    override val ottSetupService: IOttSetupService,
    private val ottFeatureUseCase: OttFeatureUseCase,
) : BaseViewModel() {
    private val _uiState = MutableStateFlow(TransactionStatusUiState())
    val uiState: StateFlow<TransactionStatusUiState> = _uiState.asStateFlow()

    init {
        _uiState.update {
            it.copy(
                userName = userProf.getUserName().toString(),
                isChecker = userProf.isChecker(),
            )
        }
        listStatus()
    }

    fun onAction(action: TransactionStatusAction) {
        when (action) {
            TransactionStatusAction.OnNavigateBack -> sendEvent(TransactionStatusEvent.NavigateBackEvent)
            is TransactionStatusAction.OnClickSwitch -> {
                _uiState.update {
                    if (action.key == "approve") {
                        it.copy(approveState = !it.approveState)
                    } else {
                        it.copy(rejectState = !it.rejectState)
                    }
                }
                val stateText = when (action.key) {
                    "approve" -> if (_uiState.value.approveState) "bật" else "tắt"
                    "reject" -> if (_uiState.value.rejectState) "bật" else "tắt"
                    else -> ""
                }

                updateStatus(stateText)
            }
        }
    }

    private fun listStatus() {
        launchJob {
            val res = ottFeatureUseCase.ottStatusList(userProf.getUserName() ?: "")
            handleResource(res) { data ->
                _uiState.update {
                    it.copy(
                        approveState = data.statusRegis?.contains("approve") ?: true,
                        rejectState = data.statusRegis?.contains("reject") ?: true,
                    )
                }
            }
        }
    }

    private fun updateStatus(state: String) {
        launchJob {
            val listState = buildList {
                _uiState.value.apply {
                    add("create")
                    if (approveState) add("approve")
                    if (rejectState) add("reject")
                }
            }
            val res = ottFeatureUseCase.ottStatusUpdate(
                OttStatusUpdateParams(
                    username = userProf.getUserName().toString(),
                    status = listState,
                ),
            )
            handleResource(res) {
                sendEvent(TransactionStatusEvent.UpdateStateSuccess(state))
            }
        }
    }
}

data class TransactionStatusUiState(
    val userName: String = "",
    val isChecker: Boolean = false,
    val approveState: Boolean = true,
    val rejectState: Boolean = true,
)

sealed class TransactionStatusEvent : OneTimeEvent {
    data object NavigateBackEvent : TransactionStatusEvent()
    data class UpdateStateSuccess(val state: String) : TransactionStatusEvent()
}

sealed interface TransactionStatusAction {
    data object OnNavigateBack : TransactionStatusAction
    data class OnClickSwitch(val key: String) : TransactionStatusAction
}
