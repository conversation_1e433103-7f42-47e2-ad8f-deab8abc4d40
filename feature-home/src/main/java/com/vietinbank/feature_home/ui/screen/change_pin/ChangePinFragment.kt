package com.vietinbank.feature_home.ui.screen.change_pin

import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AlertDialog
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.BiometricHelper
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class ChangePinFragment : BaseFragment<ChangePinViewModel>() {
    override val viewModel: ChangePinViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        handleSingleEvent { event ->
            when (event) {
                is ChangePinEvent.NavigateBack -> {
                    appNavigator.popBackStack()
                }

                is ChangePinEvent.ShowBiometricPrompt -> {
                    showBiometricPrompt()
                }

                is ChangePinEvent.ShowBiometricSetupRequired -> {
                    showBiometricSetupRequiredDialog()
                }

                is ChangePinEvent.ShowSuccessDialog -> {
                    showSuccessDialog(event.message)
                }

                is ChangePinEvent.ShowError -> {
                    showErrorDialog(event.message)
                }
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.uiState.collectAsState()
        AppTheme {
            ChangePinScreen(
                uiState = uiState,
                onAction = viewModel::onAction,
            )
        }
    }
    private fun showBiometricPrompt() {
        BiometricHelper.showBiometricOrDeviceCredentialPrompt(
            fragment = this,
            title = "Xác thực",
            subtitle = "Vui lòng xác thực để hoàn tất cài đặt mã PIN",
            onSuccess = {
                viewModel.onBiometricSuccess()
            },
            onError = { errorMessage ->
                viewModel.onBiometricError(errorMessage)
            },
        )
    }

    private fun showBiometricSetupRequiredDialog() {
        AlertDialog.Builder(requireContext())
            .setTitle("Thông báo")
            .setMessage("Quý khách cần cài đặt khóa màn hình (PIN/Password/Pattern) hoặc vân tay để sử dụng tính năng này")
            .setPositiveButton("Cài đặt") { _, _ ->
                // Navigate to device security settings
                val intent =
                    android.content.Intent(android.provider.Settings.ACTION_SECURITY_SETTINGS)
                startActivity(intent)
            }
            .setNegativeButton("Hủy", null)
            .show()
    }

    private fun showSuccessDialog(message: String) {
        showNoticeDialog(
            message = message,
            cancelable = false,
            positiveAction = {
                appNavigator.popBackStack()
            },
        )
    }

    private fun showErrorDialog(message: String) {
        showNoticeDialog(
            message = message,
            cancelable = false,
            positiveAction = {
                appNavigator.popBackStack()
            },
        )
    }
}