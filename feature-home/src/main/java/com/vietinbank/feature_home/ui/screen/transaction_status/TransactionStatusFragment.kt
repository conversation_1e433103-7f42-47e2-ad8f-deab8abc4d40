package com.vietinbank.feature_home.ui.screen.transaction_status

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.base.BaseFragment
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class TransactionStatusFragment() : BaseFragment<TransactionStatusViewModel>() {
    override val viewModel: TransactionStatusViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        handleSingleEvent {
            when (it) {
                TransactionStatusEvent.NavigateBackEvent -> appNavigator.popBackStack()
                is TransactionStatusEvent.UpdateStateSuccess -> {
                    showNoticeDialog(
                        message = "Quý khách đã ${it.state} chức năng nhận thông báo trạng thái giao dịch của ${viewModel.uiState.value.userName} trên thiết bị thành công",
                    )
                }
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.uiState.collectAsState()
        TransactionStatusScreen(
            uiState = uiState,
            onAction = viewModel::onAction,
        )
    }
}