package com.vietinbank.feature_home.ui.screen.un_regis_identity_account

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_home.ui.navigation.AccountBalanceNavigator
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class UnRegisIdentityAccountFragment : BaseFragment<UnRegisIdentityAccountViewModel>() {
    override val viewModel: UnRegisIdentityAccountViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var accountBalanceNavigator: AccountBalanceNavigator
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        handleSingleEvent { event ->
            when (event) {
                UnRegIdentityAccountEvent.NavigateBackEvent -> {}
                UnRegIdentityAccountEvent.ConfirmEvent -> {
                    showNoticeDialog(
                        "Quý khách đã huỷ kích hoạt dịch vụ nhận thông báo biến động số dư theo các tài khoản định danh ${
                            viewModel.uiState.value.listUnReg.map { it.accountNumber }
                                .joinToString(", ")
                        }",
                        positiveAction = {
                            accountBalanceNavigator.popToNotificationSetting()
                        },
                    )
                }
                UnRegIdentityAccountEvent.ShowEmptyListErrorDialog -> {
                    showNoticeDialog(
                        "Quý khách vui lòng chọn tài khoản huỷ đăng ký nhận BĐSD",
                    )
                }
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.uiState.collectAsState()

        UnRegisIdentityAccountScreen(
            uiState = uiState,
            onAction = viewModel::onAction,
        )
    }
}