package com.vietinbank.feature_home.ui.screen.ott_setting.list_account

import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.CustomEncryptedPrefs
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_domain.models.ott_feature.ResultItemDomains
import com.vietinbank.core_domain.repository.cache.IOttRegistrationRepository
import com.vietinbank.core_domain.usecase.ott_feature.OttFeatureUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.OneTimeEvent
import com.vietinbank.feature_home.ui.screen.ott_setting.ott_service_new_ui.OttServiceType
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

@HiltViewModel
class ListAccountViewModel @Inject constructor(
    override val sessionManager: ISessionManager,
    private val prefs: CustomEncryptedPrefs,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    val ottRegistrationRepository: IOttRegistrationRepository,
    private val ottUseCase: OttFeatureUseCase,
) : BaseViewModel() {
    private val _uiState = MutableStateFlow(ListAccountUiState())
    val uiState: StateFlow<ListAccountUiState> = _uiState.asStateFlow()

    fun setAccounts(data: List<ResultItemDomains>) {
        _uiState.value = _uiState.value.copy(
            listRegister = data.map { AccountUiModel(it) },
        )
    }

    fun toggleSelected(index: Int) {
        val current = _uiState.value.listRegister.toMutableList()
        val item = current[index]
        current[index] = item.copy(isSelected = !item.isSelected)
        _uiState.value = _uiState.value.copy(listRegister = current)
    }
}

// UI Model
data class AccountUiModel(
    val domain: ResultItemDomains,
    val isSelected: Boolean = false,
)

data class ListAccountUiState(
    val serviceType: OttServiceType = OttServiceType.SIGN_UP_SERVICE,
    val title: String = "",
    val listRegister: List<AccountUiModel> = emptyList(),
    val search: String = "",
)

sealed class ListAccountEvent : OneTimeEvent {
    data object ShowOtpDialog : OneTimeEvent
    data object VerifyOtpSuccess : OneTimeEvent
    data object VerifyOtpFail : OneTimeEvent
}

sealed interface ListAccountAction {
    data object OnConfirmClick : ListAccountAction
}