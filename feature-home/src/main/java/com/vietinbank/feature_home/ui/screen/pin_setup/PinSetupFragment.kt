package com.vietinbank.feature_home.ui.screen.pin_setup

import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AlertDialog
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.BiometricHelper
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class PinSetupFragment : BaseFragment<PinSetupViewModel>() {
    override val viewModel: PinSetupViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        observeEvents()
    }

    private fun observeEvents() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.events.collect { event ->
                when (event) {
                    is PinSetupEvent.NavigateBack -> {
                        appNavigator.popBackStack()
                    }

                    is PinSetupEvent.ShowBiometricPrompt -> {
                        showBiometricPrompt()
                    }

                    is PinSetupEvent.ShowBiometricSetupRequired -> {
                        showBiometricSetupRequiredDialog()
                    }

                    is PinSetupEvent.ShowSuccessDialog -> {
                        showSuccessDialog(event.message)
                    }

                    is PinSetupEvent.ShowError -> {
                        showErrorDialog(event.message)
                    }
                }
            }
        }
    }

    private fun showBiometricPrompt() {
        BiometricHelper.showBiometricOrDeviceCredentialPrompt(
            fragment = this,
            title = "Xác thực",
            subtitle = "Vui lòng xác thực để hoàn tất cài đặt mã PIN",
            onSuccess = {
                viewModel.onBiometricSuccess()
            },
            onError = { errorMessage ->
                viewModel.onBiometricError(errorMessage)
            },
        )
    }

    private fun showBiometricSetupRequiredDialog() {
        AlertDialog.Builder(requireContext())
            .setTitle("Thông báo")
            .setMessage("Quý khách cần cài đặt khóa màn hình (PIN/Password/Pattern) hoặc vân tay để sử dụng tính năng này")
            .setPositiveButton("Cài đặt") { _, _ ->
                // Navigate to device security settings
                val intent =
                    android.content.Intent(android.provider.Settings.ACTION_SECURITY_SETTINGS)
                startActivity(intent)
            }
            .setNegativeButton("Hủy", null)
            .show()
    }

    private fun showSuccessDialog(message: String) {
        showNoticeDialog(
            message = message,
            cancelable = false,
            positiveAction = {
                appNavigator.popBackStack()
            },
        )
    }

    private fun showErrorDialog(message: String) {
        showNoticeDialog(
            message = message,
            cancelable = false,
            positiveAction = {
                appNavigator.popBackStack()
            },
        )
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.state.collectAsState()
        AppTheme {
            PinSetupScreen(
                state = uiState,
                onAction = viewModel::onAction,
            )
        }
    }
}