package com.vietinbank.feature_home.ui.screen.ott_setting.ott_service_new_ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalTextStyle
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.vietinbank.core_ui.base.views.StrokeLine
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.dismissRippleClickable
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.ellipticalGradientBackground
import com.vietinbank.core_ui.utils.gradientBorder
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_home.R
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun OttServiceScreen(
    uiState: OttServiceUiState,
    onAction: (OttServiceAction) -> Unit,
    onBackClick: () -> Unit = {},
) {
    var showAccountTypeSheet by remember { mutableStateOf(false) }
    var showNotificationTypeSheet by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding()
            .padding(horizontal = FDS.Sizer.Padding.padding8),
    ) {
        FoundationAppBar(
            modifier = Modifier.padding(vertical = FDS.Sizer.Padding.padding8),
            title = stringResource(uiState.serviceType.title),
            titleStyle = FDS.Typography.headingH2,
            isLightIcon = false,
            onNavigationClick = {
                onBackClick()
            },
            isCustomActionUseSafeClick = false,
        )
        Row(
            Modifier.padding(horizontal = FDS.Sizer.Padding.padding16),
        ) {
            FoundationText(
                text = stringResource(uiState.serviceType.description),
                textAlign = TextAlign.Left,
                style = FDS.Typography.captionCaptionL,
                color = FDS.Colors.characterTertiary,
            )
        }

        Column(
            Modifier
                .padding(top = FDS.Sizer.Padding.padding16)
                .clip(RoundedCornerShape(FDS.Sizer.Padding.padding32))
                .background(Color.White)
                .padding(vertical = FDS.Sizer.Padding.padding24),
        ) {
            Row(
                modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
            ) {
                Column(
                    modifier = Modifier.weight(1f),
                ) {
                    FoundationText(
                        text = stringResource(R.string.ott_service_account_type_title),
                    )
                    val accountName =
                        if (uiState.accountType.title != null) stringResource(uiState.accountType.title) else ""
                    FoundationText(
                        text = accountName,
                    )
                }
                Icon(
                    painter = painterResource(R.drawable.ic_menu_down),
                    contentDescription = "",
                    tint = FDS.Colors.characterSecondary,
                    modifier = Modifier
                        .size(FDS.Sizer.Padding.padding32)
                        .dismissRippleClickable {
                            showAccountTypeSheet = true
                        },
                )
            }
            StrokeLine(modifier = Modifier.padding(vertical = FDS.Sizer.Padding.padding16))
            Column(
                modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
            ) {
                FoundationText(
                    text = stringResource(R.string.ott_service_cif_no_title),
                )
                BasicTextField(
                    value = uiState.cifNo,
                    onValueChange = {
                        onAction(OttServiceAction.OnCifNoChange(it))
                    },
                    textStyle = LocalTextStyle.current.copy(color = Color.Black),
                    modifier = Modifier
                        .fillMaxWidth(),
                )
            }
            StrokeLine(modifier = Modifier.padding(vertical = FDS.Sizer.Padding.padding16))

            Column(
                modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
            ) {
                FoundationText(
                    text = stringResource(R.string.ott_service_phone_title),
                )
                BasicTextField(
                    value = uiState.phoneNum,
                    onValueChange = {
                        onAction(OttServiceAction.OnPhoneNumChange(it))
                    },
                    textStyle = LocalTextStyle.current.copy(color = Color.Black),
                    modifier = Modifier
                        .fillMaxWidth(),
                )
            }
            if (uiState.accountType == ServiceAccountType.PAYMENT && uiState.serviceType != OttServiceType.SIGN_UP_SERVICE) {
                StrokeLine(modifier = Modifier.padding(vertical = FDS.Sizer.Padding.padding16))
                Row(
                    modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
                ) {
                    Column(
                        modifier = Modifier.weight(1f),
                    ) {
                        FoundationText(
                            text = stringResource(R.string.ott_service_notification_title),
                        )
                        FoundationText(
                            text = uiState.notificationType.alertType,
                        )
                    }
                    Icon(
                        painter = painterResource(R.drawable.ic_menu_down),
                        contentDescription = "",
                        tint = FDS.Colors.characterSecondary,
                        modifier = Modifier
                            .size(FDS.Sizer.Padding.padding32)
                            .dismissRippleClickable {
                                showNotificationTypeSheet = true
                            },
                    )
                }
            }
        }
        if (uiState.serviceType == OttServiceType.SIGN_UP_SERVICE) {
            Row(
                Modifier
                    .padding(top = FDS.Sizer.Padding.padding16)
                    .clip(RoundedCornerShape(FDS.Sizer.Padding.padding32))
                    .gradientBorder(
                        cornerRadius = FDS.Sizer.Padding.padding32,
                    )
                    .ellipticalGradientBackground(
                        colors = listOf(
                            FDS.Colors.buttonGradientPrimaryPressed.copy(alpha = 0.6F),
                            Color.White.copy(alpha = 0F),
                        ),
                        centerFraction = Offset(0.5f, 1.75f),
                        scaleX = 3f,
                    )
                    .padding(horizontal = FDS.Sizer.Padding.padding24, vertical = FDS.Sizer.Padding.padding24),
            ) {
                Icon(
                    modifier = Modifier
                        .padding(end = FDS.Sizer.Padding.padding8)
                        .size(FDS.Sizer.Padding.padding24),
                    painter = painterResource(R.drawable.ic_ott_service_bulb),
                    contentDescription = "",
                )
                FoundationText(
                    text = "Bạn cần đăng ký dịch vụ thông báo BĐSD qua SMS trước khi đăng ký dịch vụ thông báo BĐSD trên ứng dụng eFAST",
                    color = FDS.Colors.white,
                    style = FDS.Typography.captionCaptionL,
                    textAlign = TextAlign.Left,
                )
            }
        }
        Spacer(Modifier.weight(1f))
        FoundationButton(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(R.string.home_button_positive_ekyc),
            onClick = { onAction(OttServiceAction.OnConfirmClick) },
            enabled = (uiState.cifNo.isNotEmpty() && uiState.phoneNum.isNotEmpty() && uiState.accountType != ServiceAccountType.NONE),
//                    && uiState.notificationType != NotificationType.NONE)
        )

        OttServiceBottomSheet(
            visible = showAccountTypeSheet,
            accounts = listOf(
                Triple(
                    ServiceAccountType.PAYMENT.codeType,
                    stringResource(ServiceAccountType.PAYMENT.title!!),
                    null,
                ),
                Triple(
                    ServiceAccountType.IDENTITY.codeType,
                    stringResource(ServiceAccountType.IDENTITY.title!!),
                    null,
                ),
                Triple(
                    ServiceAccountType.LOAN.codeType,
                    stringResource(ServiceAccountType.LOAN.title!!),
                    null,
                ),
            ),
            onDismiss = { showAccountTypeSheet = false },
            onPicked = {
                onAction(OttServiceAction.OnPickAccountType(it))
            },
        )

        OttServiceBottomSheet(
            visible = showNotificationTypeSheet,
            accounts = listOf(
                Triple(
                    NotificationType.Alert89.alertType,
                    NotificationType.Alert89.alertType,
                    NotificationType.Alert89.description,
                ),
                Triple(
                    NotificationType.Alert189.alertType,
                    NotificationType.Alert189.alertType,
                    NotificationType.Alert189.description,
                ),
                Triple(
                    NotificationType.Alert169.alertType,
                    NotificationType.Alert169.alertType,
                    NotificationType.Alert169.description,
                ),
            ),
            onDismiss = { showNotificationTypeSheet = false },
            onPicked = {
                onAction(OttServiceAction.OnPickNotificationType(it))
            },
        )
    }
}