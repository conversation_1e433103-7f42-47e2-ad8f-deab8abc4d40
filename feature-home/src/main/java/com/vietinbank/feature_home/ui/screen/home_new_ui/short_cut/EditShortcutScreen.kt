package com.vietinbank.feature_home.ui.screen.home_new_ui.short_cut

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.models.AppBarAction
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.foundation.textfield.FoundationFieldType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.dismissRippleClickable
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.feature_home.R
import com.vietinbank.feature_home.ui.view.home_card.FunctionItemData
import com.vietinbank.feature_home.ui.view.home_card.ItemAction
import com.vietinbank.feature_home.ui.view.home_card.StrokeLine
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun EditShortcutScreen(
    uiState: EditShortcutUiState,
    listFavoriteFunc: List<FunctionItemData>,
    listFunc: Map<String, List<FunctionItemData>>,
    listSearch: List<FunctionItemData>,
    onAction: (ShortcutAction) -> Unit,
) {
    val listState = rememberLazyListState()

    val dragDropState =
        rememberDragDropState(
            lazyListState = listState,
            draggableItemsNum = listFavoriteFunc.size,
            onMove = { fromIndex, toIndex ->
                onAction(ShortcutAction.MoveItem(fromIndex, toIndex))
            },
        )

    val configuration = LocalConfiguration.current
    val screenHeight = configuration.screenHeightDp.dp

    Column(
        modifier = Modifier.eFastBackground(),
    ) {
        Column(
            Modifier
                .clip(
                    RoundedCornerShape(
                        bottomEnd = FDS.Sizer.Padding.padding32,
                        bottomStart = FDS.Sizer.Padding.padding32,
                    ),
                )
                .background(Color.White)
                .padding(bottom = FDS.Sizer.Padding.padding24)
                .statusBarsPadding(),
        ) {
            FoundationAppBar(
                modifier = Modifier.padding(vertical = FDS.Sizer.Padding.padding8),
                title = stringResource(R.string.home_app_bar_title_edit_shortcut),
                titleStyle = FDS.Typography.headingH4,
                onNavigationClick = {
                    onAction(ShortcutAction.BackAndSave)
                },
                actions = listOf(
                    AppBarAction(
                        icon = if (uiState.isEditMode) R.drawable.ic_home_shortcut_check else R.drawable.ic_home_edit_shortcut,
                        contentDescription = "",
                        onClick = {
                            onAction(ShortcutAction.EditItem)
                        },
                    ),
                ),
                isSingleLineAppBar = true,
                isCustomActionUseSafeClick = false,
            )
            LazyColumn(
                modifier = Modifier
                    .then(
                        if (uiState.isEditMode) {
                            Modifier
                        } else {
                            Modifier.dragContainer(dragDropState)
                        },
                    )
                    .background(
                        Color.White,
                        RoundedCornerShape(
                            bottomEnd = FDS.Sizer.Padding.padding32,
                            bottomStart = FDS.Sizer.Padding.padding32,
                        ),
                    )
                    .fillMaxWidth()
                    .heightIn(max = screenHeight / 3)
                    .padding(
                        horizontal = FDS.Sizer.Padding.padding16,
                        vertical = FDS.Sizer.Padding.padding8,
                    ),
                state = listState,
            ) {
                draggableItems(
                    items = listFavoriteFunc,
                    dragDropState = dragDropState,
                ) { modifier, item, _ ->
                    FuncItem(
                        modifier = modifier,
                        itemData = item,
                        onClick = { onAction(ShortcutAction.RemoveItem(item.functionId)) },
                    )
                }
            }
        }
        FoundationText(
            modifier = Modifier
                .fillMaxWidth()
                .padding(FDS.Sizer.Padding.padding8),
            textAlign = TextAlign.Center,
            color = Color.White,
            text = stringResource(uiState.guideline),
        )
        Column(
            modifier = Modifier
                .clip(
                    RoundedCornerShape(
                        topEnd = FDS.Sizer.Padding.padding32,
                        topStart = FDS.Sizer.Padding.padding32,
                    ),
                )
                .background(Color.White)
                .fillMaxWidth()
                .weight(1f)
                .padding(horizontal = FDS.Sizer.Padding.padding16)
                .padding(top = FDS.Sizer.Padding.padding24)
                .navigationBarsPadding(),

        ) {
            Box(
                Modifier
                    .padding(bottom = FDS.Sizer.Padding.padding8)
                    .background(
                        FDS.Colors.homeBackgroundIcon,
                        RoundedCornerShape(FDS.Sizer.Padding.padding32),
                    )
                    .then(
                        if (uiState.search.isEmpty()) {
                            Modifier
                        } else {
                            Modifier
                                .border(
                                    1.dp,
                                    FDS.Colors.borderGradientStart,
                                    RoundedCornerShape(FDS.Sizer.Padding.padding32),
                                )
                        },
                    ),
                contentAlignment = Alignment.Center,
            ) {
                FoundationFieldType(
                    value = uiState.search,
                    onValueChange = {
                        onAction(ShortcutAction.SearchItem(it))
                    },
                    placeholder = stringResource(R.string.shortcut_hint_search),
                    isHaveClearIcon = true,
                    clearValue = {
                        onAction(ShortcutAction.ClearValue)
                    },
                    leadingIcon = {
                        Icon(
                            painter = painterResource(
                                id = R.drawable.ic_home_search,
                            ),
                            contentDescription = "",
                            tint = if (uiState.search.isEmpty()) {
                                FDS.Colors.characterTertiary
                            } else {
                                FDS.Colors.tabTextActiveInline
                            },
                            modifier = Modifier
                                .size(FDS.Sizer.Icon.icon16),
                        )
                    },
                )
            }
            LazyColumn() {
                if (uiState.search.isNotBlank()) {
                    item {
                        FoundationText(
                            text = "${listSearch.size} kết quả",
                            color = FDS.Colors.tabTextActiveInline,
                            modifier = Modifier.padding(FDS.Sizer.Padding.padding8),
                        )
                    }
                    itemsIndexed(items = listSearch) { _, item ->
                        FuncItem(
                            itemData = item,
                            onClick = {
                                onAction(ShortcutAction.AddItem(item.functionId))
                            },
                        )
                    }
                    return@LazyColumn
                }

                listFunc.entries.forEach { (section, list) ->
                    item {
                        FoundationText(
                            text = section,
                            color = FDS.Colors.tabTextActiveInline,
                            modifier = Modifier.padding(FDS.Sizer.Padding.padding8),
                        )
                    }
                    itemsIndexed(items = list) { _, item ->
                        FuncItem(
                            itemData = item,
                            onClick = {
                                onAction(ShortcutAction.AddItem(item.functionId))
                            },
                        )
                    }
                    item {
                        StrokeLine(
                            modifier = Modifier.padding(FDS.Sizer.Padding.padding8),
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun FuncItem(
    modifier: Modifier = Modifier,
    itemData: FunctionItemData,
    onClick: () -> Unit,
) {
    Column(
        modifier = modifier,

    ) {
        Row(
            modifier = Modifier
                .background(Color.White, RoundedCornerShape(FDS.Sizer.Padding.padding12))
                .fillMaxWidth()
                .padding(FDS.Sizer.Padding.padding8)
                .dismissRippleClickable {
                    onClick()
                },
            horizontalArrangement = Arrangement.spacedBy(
                FDS.Sizer.Padding.padding8,
                Alignment.CenterHorizontally,
            ),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Image(
                painter = painterResource(itemData.iconRes),
                contentDescription = "",
            )
            FoundationText(
                modifier = Modifier.weight(1f),
                text = itemData.functionName,
                style = FDS.Typography.interactionSmallButton,
                color = FDS.Colors.tabTextActiveInline,
            )
            when (itemData.itemAction) {
                ItemAction.Add -> Image(
                    painter = painterResource(R.drawable.ic_home_add_short_cut),
                    contentDescription = "",
                )

                ItemAction.Move -> Image(
                    painter = painterResource(R.drawable.ic_home_menu_order),
                    contentDescription = "",
                )

                ItemAction.Remove -> Image(
                    painter = painterResource(R.drawable.ic_home_minus),
                    contentDescription = "",
                )

                ItemAction.NoAction -> {}
            }
        }
    }
}