package com.vietinbank.feature_home.ui.screen.un_regis_identity_account

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.base.compose.BaseAppBar
import com.vietinbank.core_ui.base.compose.BaseButton
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_home.R
import com.vietinbank.feature_home.ui.screen.confirm_sms_transform.AccountItem

@Composable
fun UnRegisIdentityAccountScreen(
    uiState: UnRegIdentityAccountUiState,
    onAction: (UnRegIdentityAccountAction) -> Unit,
) {
    val selectedOptions = remember { mutableStateListOf<String>() }
    var unRegExpand by remember { mutableStateOf(true) }
    Column(
        modifier = Modifier.fillMaxSize(),
    ) {
        BaseAppBar(
            title = "Cài đặt thông báo",
            onBackClick = {
                onAction(UnRegIdentityAccountAction.OnClickNavigateBack)
            },
        )
        Column(
            modifier = Modifier
                .padding(top = 20.dp)
                .padding(horizontal = 16.dp)
                .weight(1f),
            verticalArrangement = Arrangement.spacedBy(20.dp),
        ) {
            BaseText(
                text = "Quý khách đang thực hiện chuyển hình thức đăng ký nhận tin BĐSD từ ứng dụng eFAST qua tin nhắn SMS",
                color = Color(0xFFD6F4FF),
            )

            BaseText(
                text = uiState.corpName,
                color = Color(0xFFD6F4FF),
            )
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(4.dp))
                    .background(Color.White)
                    .padding(horizontal = 12.dp, vertical = 16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp),
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                ) {
                    BaseText(
                        "Chọn tài khoản đăng ký nhận BĐSD qua tin nhắn",
                        modifier = Modifier.weight(1f),
                    )
                    Image(
                        painter = painterResource(R.drawable.ic_menu_down),
                        contentDescription = "",
                        modifier = Modifier.safeClickable {
                            unRegExpand = !unRegExpand
                        },
                    )
                }
                AnimatedVisibility(visible = unRegExpand) {
                    Column {
                        uiState.listUnReg.forEach { option ->
                            AccountItem(
                                title = option.accountNumber ?: "",
                                isShowRadioButton = true,
                                selected = selectedOptions.contains(option.accountNumber ?: ""),
                                onClick = {
                                    if (selectedOptions.contains(it)) {
                                        selectedOptions.remove(it)
                                    } else {
                                        selectedOptions.add(it)
                                    }
                                },
                            )
                        }
                    }
                }
            }
        }
        BaseButton(
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .padding(bottom = 20.dp)
                .safeClickable {
                    onAction(UnRegIdentityAccountAction.OnClickConfirm(selectedOptions))
                },
            text = "Tiếp tục",
        )
    }
}