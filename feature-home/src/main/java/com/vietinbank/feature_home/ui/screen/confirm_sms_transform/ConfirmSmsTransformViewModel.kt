package com.vietinbank.feature_home.ui.screen.confirm_sms_transform

import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.CustomEncryptedPrefs
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_domain.models.ott_feature.CancelRecord
import com.vietinbank.core_domain.models.ott_feature.ConfirmVacctParams
import com.vietinbank.core_domain.models.ott_feature.ListRegRequestParams
import com.vietinbank.core_domain.models.ott_feature.OttRegisterRequestParams
import com.vietinbank.core_domain.models.ott_feature.ResultItemDomains
import com.vietinbank.core_domain.repository.cache.IOttRegistrationRepository
import com.vietinbank.core_domain.usecase.ott_feature.OttFeatureUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.OneTimeEvent
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class ConfirmSmsTransformViewModel @Inject constructor(
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val sessionManager: ISessionManager,
    override val ottSetupService: IOttSetupService,
    private val ottUseCase: OttFeatureUseCase,
    private val prefs: CustomEncryptedPrefs,
    private val ottRegistrationRepository: IOttRegistrationRepository,
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(ConfirmSmsUiState())
    val uiState: StateFlow<ConfirmSmsUiState> = _uiState.asStateFlow()

    private var baseFeeAmount = 0

    init {
        _uiState.update {
            it.copy(
                phoneNum = userProf.getPhoneNo() ?: "",
                corpName = userProf.getCorpName() ?: "",
            )
        }
    }

    fun onAction(action: ConfirmSmsAction) {
        when (action) {
            ConfirmSmsAction.OnClickNavigateBack -> sendEvent(ConfirmSmsEvent.NavigateBackEvent)
            ConfirmSmsAction.OnClickValidate -> {
                _uiState.update {
                    it.copy(
                        selectedValidate = !_uiState.value.selectedValidate,
                    )
                }
            }

            is ConfirmSmsAction.OnClickConfirm -> {
                if (action.listSelected.isEmpty()) {
                    sendEvent(ConfirmSmsEvent.ShowEmptyListErrorDialog)
                } else if (!_uiState.value.selectedValidate) {
                    sendEvent(ConfirmSmsEvent.ShowUnCheckValidateDialog)
                } else {
                    val listSelectedSms = _uiState.value.listUnReg.filter {
                        action.listSelected.contains(it.accountNumber)
                    }
                    when (_uiState.value.tag) {
                        Tags.IDENTITY_ACCOUNT_TYPE_REG -> {
                            registerIdentity(listSelectedSms)
                        }

                        else -> {
                            registerSms(listSelectedSms)
                        }
                    }
                }
            }

            is ConfirmSmsAction.OnClickSelected -> {
                val sumFee = _uiState.value.listUnReg.filter {
                    action.listSelected.contains(it.accountNumber)
                }.sumOf {
                    it.feeAmount?.toInt() ?: 0
                }
                val amountFee = sumFee + baseFeeAmount
                _uiState.update {
                    it.copy(
                        totalFee = amountFee.toString(),
                    )
                }
            }
        }
    }

    private fun listReg() {
        val isVacct = if (_uiState.value.tag == Tags.IDENTITY_ACCOUNT_TYPE_REG) "Y" else ""
        launchJob(showLoading = false) {
            val params = ListRegRequestParams(
                alertMethod = "ALL",
                isVacct = isVacct,
                mobileNumber = userProf.getPhoneNo() ?: "",
                roleId = "",
                tranId = "",
                typeCheck = "Y",
                username = userProf.getUserName() ?: "",
            )
            val res = ottUseCase.listReg(
                params,
            )
            handleResourceSilent(
                resource = res,
                onSuccess = { data ->
                    filterListUnRegSms(data.results)
                },
            )
        }
    }

    fun updateAlertType(type: String, tag: String, actionType: String) {
        _uiState.update {
            it.copy(
                alertType = type,
                tag = tag,
                actionType = actionType,
            )
        }
        listReg()
    }

    private fun filterListUnRegSms(list: List<ResultItemDomains>) {
        val listUnReg: List<ResultItemDomains> = list.filter {
            when (_uiState.value.tag) {
                Tags.IDENTITY_ACCOUNT_TYPE_REG -> it.status == "WC"
                else -> it.alertType == "159"
            }
        }
        val listReg: List<ResultItemDomains> =
            list.filter {
                when (_uiState.value.tag) {
                    Tags.IDENTITY_ACCOUNT_TYPE_REG -> it.status == "A"
                    else -> it.alertType in setOf("189", "89", "169")
                }
            }
        baseFeeAmount = listReg.mapNotNull { it.feeAmount?.toIntOrNull() }.sum()
        _uiState.update {
            it.copy(
                totalFee = baseFeeAmount.toString(),
                listUnReg = listUnReg,
                listReg = listReg,
            )
        }
    }

    private fun registerIdentity(listSelected: List<ResultItemDomains>) {
        launchJob(showLoading = true) {
            val selectedItems = listSelected.map {
                CancelRecord(
                    username = it.username,
                    virAcctNo = it.accountNumber,
                    virAcctName = it.username,
                    virPhoneNo = it.phonenumber,
                )
            }
            val params = ConfirmVacctParams(
                phoneNo = userProf.getPhoneNo(),
                records = selectedItems,
                userAction = "",
                userRole = "",
                username = userProf.getUserName(),
            )
            val res = ottUseCase.confirmVacct(
                params,
            )
            handleResource(res) {
                isSetupPin()
            }
        }
    }
    private fun isSetupPin() {
        val cifNo = userProf.getCifNo()
        val key = "IS_ENABLE_PIN_$cifNo"
        val state = prefs.getString(key, encrypted = false)
        when (state) {
            "ENABLE" -> sendEvent(ConfirmSmsEvent.ConfirmRegisIdentityEvent)
            else -> sendEvent(ConfirmSmsEvent.ConfirmSetupPinEvent)
        }
    }

    private fun registerSms(listSelectedSms: List<ResultItemDomains>) {
        launchJob(showLoading = true) {
            val selectedItems = listSelectedSms.map { originalItem ->
                ResultItemDomains(
                    position = originalItem.position,
                    customerNumber = originalItem.customerNumber,
                    accountNumber = originalItem.accountNumber,
                    alertAddr = originalItem.alertAddr,
                    alertType = _uiState.value.alertType,
                    alertMethod = "SMS",
                    feeAccountNumber = originalItem.feeAccountNumber,
                    feeAmount = originalItem.feeAmount,
                    feeType = originalItem.feeType,
                    startDate = originalItem.startDate,
                    action = "REG",
                    accountName = originalItem.accountName,
                    username = originalItem.username,
                    isVacct = originalItem.isVacct,
                    phonenumber = originalItem.phonenumber,
                    status = originalItem.status,
                    accountType = originalItem.accountType,
                    branchId = originalItem.branchId,
                    isSelected = false,
                )
            }.toMutableList()

            val unregisterItems = selectedItems.map { originalItem ->
                ResultItemDomains(
                    position = originalItem.position,
                    customerNumber = originalItem.customerNumber,
                    accountNumber = originalItem.accountNumber,
                    alertAddr = originalItem.alertAddr,
                    alertType = "159",
                    alertMethod = "OTT",
                    feeAccountNumber = originalItem.feeAccountNumber,
                    feeAmount = originalItem.feeAmount,
                    feeType = originalItem.feeType,
                    startDate = originalItem.startDate,
                    action = "UNREG",
                    accountName = originalItem.accountName,
                    username = originalItem.username,
                    isVacct = originalItem.isVacct,
                    phonenumber = originalItem.phonenumber,
                    status = originalItem.status,
                    accountType = originalItem.accountType,
                    branchId = originalItem.branchId,
                    isSelected = false,
                )
            }

            selectedItems.addAll(unregisterItems)
            val params = OttRegisterRequestParams(
                isVacct = "",
                mobileNumber = userProf.getPhoneNo() ?: "",
                roleId = "",
                tranId = "",
                username = userProf.getUserName() ?: "",
                recordsParams = selectedItems,
            )
            val res = ottUseCase.registerOTT(
                params,
            )
            handleResource(res) {
                sendEvent(ConfirmSmsEvent.ConfirmTransferSMSEvent)
            }
        }
    }
}

data class ConfirmSmsUiState(
    val tag: String = "",
    val actionType: String = "",
    val phoneNum: String = "",
    val corpName: String = "",
    val alertType: String = "",
    val selectedValidate: Boolean = false,
    val totalFee: String = "",
    val listUnReg: List<ResultItemDomains> = listOf(),
    val listReg: List<ResultItemDomains> = listOf(),
)

sealed class ConfirmSmsEvent : OneTimeEvent {
    data object NavigateBackEvent : OneTimeEvent
    data object ConfirmTransferSMSEvent : OneTimeEvent
    data object ConfirmRegisIdentityEvent : OneTimeEvent
    data object ConfirmSetupPinEvent : OneTimeEvent
    data object ShowEmptyListErrorDialog : OneTimeEvent
    data object ShowUnCheckValidateDialog : OneTimeEvent
}

sealed interface ConfirmSmsAction {
    data object OnClickNavigateBack : ConfirmSmsAction
    data object OnClickValidate : ConfirmSmsAction
    data class OnClickConfirm(val listSelected: List<String>) : ConfirmSmsAction
    data class OnClickSelected(val listSelected: List<String>) : ConfirmSmsAction
}