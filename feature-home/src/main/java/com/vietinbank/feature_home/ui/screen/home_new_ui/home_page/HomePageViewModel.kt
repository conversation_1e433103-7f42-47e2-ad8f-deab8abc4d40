package com.vietinbank.feature_home.ui.screen.home_new_ui.home_page

import androidx.lifecycle.viewModelScope
import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.CustomEncryptedPrefs
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.checker.CountPendingParams
import com.vietinbank.core_domain.models.checker.GetTransactionListParams
import com.vietinbank.core_domain.models.checker.MobileConfigLastParams
import com.vietinbank.core_domain.models.checker.SubTranTypeListDomain
import com.vietinbank.core_domain.models.csat.CSatConfigParams
import com.vietinbank.core_domain.models.csat.CSatRateParams
import com.vietinbank.core_domain.models.home.CountTransGroupParams
import com.vietinbank.core_domain.models.home.HomeAccountListParams
import com.vietinbank.core_domain.models.home.ListFunctionParams
import com.vietinbank.core_domain.models.home.ListKeyParams
import com.vietinbank.core_domain.models.home.ListLatestParams
import com.vietinbank.core_domain.repository.cache.IOttRegistrationRepository
import com.vietinbank.core_domain.usecase.home.HomeUseCase
import com.vietinbank.core_domain.usecase.transfer.CSatUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.OneTimeEvent
import com.vietinbank.feature_home.R
import com.vietinbank.feature_home.ui.view.AssetUiModel
import com.vietinbank.feature_home.ui.view.CreditUiModel
import com.vietinbank.feature_home.ui.view.home_card.FunctionItemData
import com.vietinbank.feature_home.ui.view.home_card.MyRequestUiState
import com.vietinbank.feature_home.ui.view.home_card.RecentTransactionUiModel
import com.vietinbank.feature_home.ui.view.home_card.RecentTransactionUiState
import com.vietinbank.feature_home.ui.view.home_card.RecommendedUiModelState
import com.vietinbank.feature_home.ui.view.home_card.defaultFunc
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale
import javax.inject.Inject
import kotlin.math.round

@HiltViewModel
class HomePageViewModel @Inject constructor(
    private val cSatUseCase: CSatUseCase,
    private val homeUseCase: HomeUseCase,
    override val sessionManager: ISessionManager,
    private val prefs: CustomEncryptedPrefs,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    val ottRegistrationRepository: IOttRegistrationRepository,
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(HomePageUiState())
    val uiState: StateFlow<HomePageUiState> = _uiState.asStateFlow()

    private val _sectionsHome = MutableStateFlow<List<HomeCardItem>>(emptyList())
    val sections: StateFlow<List<HomeCardItem>> = _sectionsHome.asStateFlow()

    private val formatTimeString = SimpleDateFormat(Tags.HOME_DATE_PATTERN, Locale.getDefault())

    private var timerRcm: Job? = null
    private var timerRequest: Job? = null

    var assetState: AssetUiModel? = null
        private set

    var creditState: CreditUiModel? = null
        private set

    // Store full countPending response for all transaction types
    private var countTransactionList: List<SubTranTypeListDomain> = emptyList()

    // Current transaction being processed
    private var currentTransaction: SubTranTypeListDomain? = null

    // Store transaction list JSON for navigation
    private var transactionListJson: String = ""

    fun getTransactionListJson(): String = transactionListJson

    fun getCurrentTransaction(): SubTranTypeListDomain? = currentTransaction

    init {
        _uiState.update {
            it.copy(
                approveLever = userProf.getRoleLevel() ?: "",
                userName = userProf.getFullName() ?: "",
                companyName = (userProf.getCorpName() + " " + userProf.getCifNo()),
            )
        }

        setCardBaseOnRole()
    }

    fun updateVisibleBalance() {
        _uiState.update {
            it.copy(
                isVisibleBalance = false,
            )
        }
    }

    private fun setCardBaseOnRole() {
        // maker 0
        when (userProf.getRoleLevel()) {
            Tags.ROLE_MAKER -> {
                _sectionsHome.value = listOf(
                    HomeCardItem.RecommendedCard(
                        RecommendedUiModelState(),
                    ),
                    HomeCardItem.BannerCard,
                    HomeCardItem.RecentTransactionCard(RecentTransactionUiState.Loading),
                )
                countTransGroup()
                listLatest()
                listKey()
            }

            else -> {
                _sectionsHome.value = listOf(
                    HomeCardItem.MyRequestCard(
                        MyRequestUiState.Loading,
                    ),
                    HomeCardItem.BannerCard,
                )
                countPending()
            }
        }
    }

    fun onAction(action: HomePageAction) {
        when (action) {
            HomePageAction.OnClickExploreButton -> {}
            is HomePageAction.OnTabSelected -> {
                when (action.index) {
                    1 -> {
                        sendEvent(HomePageEvent.NavigateToAccount)
                    }

                    2 -> sendEvent(HomePageEvent.NavigateToExplore)
                    else -> {}
                }
            }

            HomePageAction.OnClickVisibleButton -> {
                if (_uiState.value.isVisibleBalance) {
                    _uiState.update {
                        it.copy(
                            isVisibleBalance = !it.isVisibleBalance,
                        )
                    }
                } else {
                    homeAccountList()
                }
            }

            HomePageAction.OnClickAvatar -> {
                sendEvent(HomePageEvent.NavigateToSetting)
            }

            HomePageAction.OnClickNotification -> {
                sendEvent(HomePageEvent.NavigateToOttScreen)
            }

            HomePageAction.OnClickSearch -> {
                sendEvent(HomePageEvent.NotYetImplement)
            }

            HomePageAction.OnClickLoadingMyRequest -> {
                countPending()
            }

            HomePageAction.TestTest -> {
                sendEvent(HomePageEvent.NotYetImplement)
            }

            HomePageAction.OnClickLoadingRcmCard -> {
                countTransGroup()
            }

            HomePageAction.ClickShortcut -> {
                if (_uiState.value.isShowShortcut) {
                    _uiState.update {
                        it.copy(
                            isShowShortcut = false,
                        )
                    }
                } else {
                    getFavouriteList()
                }
            }

            HomePageAction.NavigateCt -> sendEvent(HomePageEvent.NavigateToCt)
            HomePageAction.ClickEditShortcut -> {
                sendEvent(HomePageEvent.NavigateToEditShortcut)
                _uiState.update {
                    it.copy(
                        isShowShortcut = false,
                    )
                }
            }

            HomePageAction.OnClickAllTransaction -> {
                sendEvent(HomePageEvent.NavigateToManager)
            }

            is HomePageAction.OnCLickItem -> {
                // Find the corresponding transaction data by functionId
                val transaction = countTransactionList.firstOrNull { it.functionId == action.id }
                if (transaction != null) {
                    // Call getTransactionList for any item with valid transaction data
                    getTransactionListForItem(transaction)
                }
            }

            HomePageAction.ClickCsat -> {
                sendEvent(HomePageEvent.ShowFeedBack)
            }
        }
    }

    private fun getTransactionListForItem(transaction: SubTranTypeListDomain) {
        // Store current transaction for navigation
        currentTransaction = transaction

        launchJob(showLoading = true) {
            val res = homeUseCase.getTranList(
                GetTransactionListParams(
                    cifNo = userProf.getCifNo().toString(),
                    username = userProf.getUserName().toString(),
                    groupType = getSafeGroupType(transaction.tranType, transaction.groupType),
                    serviceType = transaction.servicetype,
                    tranType = getSafeTranType(transaction.tranType),
                    pageNum = "0",
                    pageSize = "15",
                    orderByAmount = "0",
                    orderByApproveDate = "-1",
                ),
            )
            handleResource(res) { data ->
                // Store transaction list as JSON for navigation
                transactionListJson = Utils.g().provideGson().toJson(data.transactions)
                // Navigate to approval list with transaction data
                sendEvent(HomePageEvent.NavigateToApprovalList)
            }
        }
    }

    private fun getSafeGroupType(tranType: String?, groupType: String?): String? {
        return if (tranType == "fx" || tranType == "fxt") {
            ""
        } else {
            groupType
        }
    }

    private fun getSafeTranType(tranType: String?): String? {
        return if (tranType == "tp") {
            ""
        } else {
            tranType
        }
    }

    fun clearSession() {
        sessionManager.clearSession()
//        transferCacheManager.clearTransferALL()
        ottRegistrationRepository.clearAllOttCache()
    }

    private fun listFavoriteFunction() {
        launchJob(showLoading = true) {
            val res = homeUseCase.listFavoriteFunction(
                ListFunctionParams(
                    username = userProf.getUserName().orEmpty(),
                    roleId = userProf.getRoleId().toString(),
                    role = userProf.getRoleId().toString(),
                ),
            )
            handleResource(res) { data ->
                val func = data.data ?: listOf()
                val newList = if (func.isEmpty()) {
                    defaultFunc(financialPackage = userProf.getFinancialPackage() ?: "", roleLevel = userProf.getRoleLevel() ?: "")
                } else {
                    func.map {
                        FunctionItemData(
                            iconRes = R.drawable.ic_home_transfer,
                            functionName = it?.functionName ?: "",
                            functionId = it?.functionId ?: "",
                            groupName = it?.groupName ?: "",
                            groupId = it?.groupId ?: "",
                            orderNumber = it?.orderNumber?.toInt() ?: 0,
                            valueRes = null,
                        )
                    }
                }.sortedBy {
                    it.orderNumber
                }
                prefs.setString(Tags.LIST_FAVOURITE, Utils.g().provideGson().toJson(newList))
                _uiState.update {
                    it.copy(
                        isShowShortcut = true,
                        listFuncShortcut = newList,
                    )
                }
            }
        }
    }

    private fun getFavouriteList() {
        val data = prefs.getString(Tags.LIST_FAVOURITE)
        val type = object : TypeToken<List<FunctionItemData>>() {}.type
        if (!data.isNullOrEmpty()) {
            _uiState.update {
                it.copy(
                    isShowShortcut = true,
                    listFuncShortcut = Utils.g().provideGson().fromJson(data, type),
                )
            }
        } else {
            listFavoriteFunction()
        }
    }

    // MyRequestCard
    private fun countPending() {
        launchJob(showLoading = false) {
            updateMyRequestCardState(MyRequestUiState.Loading)

            val res = homeUseCase.countPending(
                CountPendingParams(
                    cifNo = userProf.getCifNo().orEmpty(),
                    userName = userProf.getUserName().orEmpty(),
                    role = userProf.getRoleId() ?: "",
                ),
            )
            if (res is Resource.Error) {
                val currentTime = formatTimeString.format(Calendar.getInstance().time)
                updateMyRequestCardState(
                    MyRequestUiState.Empty(
                        currentTime,
                        isEnableLoading = false,
                    ),
                )
            }
            handleResourceSilent(
                res,
                onSuccess = { data ->
                    val currentTime = formatTimeString.format(Calendar.getInstance().time)

                    val countList = data.countTransactionList.orEmpty()

                    if (countList.isEmpty()) {
                        updateMyRequestCardState(
                            MyRequestUiState.Empty(
                                currentTime,
                                isEnableLoading = false,
                            ),
                        )
                        return@handleResourceSilent
                    }
                    // Store full countTransactionList for all transaction types
                    countTransactionList = data.countTransactionList ?: emptyList()

                    timerRequest?.cancel()
                    timerRequest = startCountdown(
                        action = {
                            updateMyRequestEnableLoading(true)
                        },
                    )
                    val newList = countList.take(3).map {
                        FunctionItemData(
                            iconRes = R.drawable.ic_home_transfer,
                            functionName = it.servicetypename.orEmpty(),
                            valueRes = it.count_transaction,
                            functionId = it.functionId,
                        )
                    }

                    updateMyRequestCardState(
                        MyRequestUiState.Success(
                            items = newList,
                            totalTransaction = data.total_count.orEmpty(),
                            isEnableLoading = false,
                            updatedTime = currentTime,
                        ),
                    )
                },
                onError = {
                    val currentTime = formatTimeString.format(Calendar.getInstance().time)
                    updateMyRequestCardState(
                        MyRequestUiState.Empty(
                            currentTime,
                            isEnableLoading = true,
                        ),
                    )
                },
            )
        }
    }

    private fun updateMyRequestCardState(newState: MyRequestUiState) {
        _sectionsHome.updateCard<HomeCardItem.MyRequestCard> { card ->
            card.copy(state = newState)
        }
    }

    private fun updateMyRequestEnableLoading(enable: Boolean) {
        _sectionsHome.updateCard<HomeCardItem.MyRequestCard> { card ->
            val newState = when (val state = card.state) {
                is MyRequestUiState.Empty -> state.copy(isEnableLoading = enable)
                is MyRequestUiState.Success -> state.copy(isEnableLoading = enable)
                is MyRequestUiState.Loading -> state
                is MyRequestUiState.LoadingFail -> state.copy(isEnableLoading = enable)
            }
            card.copy(state = newState)
        }
    }

    private inline fun <reified T : HomeCardItem> MutableStateFlow<List<HomeCardItem>>.updateCard(
        crossinline transform: (T) -> HomeCardItem,
    ) {
        this.update { list ->
            list.map { item ->
                if (item is T) transform(item) else item
            }
        }
    }

    private fun countTransGroup() {
        launchJob(showLoading = false) {
            // set loading true
            _sectionsHome.updateCard<HomeCardItem.RecommendedCard> { card ->
                card.copy(state = card.state.copy(isLoading = true))
            }

            val res = homeUseCase.countTransGroup(
                CountTransGroupParams(
                    username = userProf.getUserName() ?: "",
                    role = userProf.getRoleId() ?: "",
                    type = Tags.TYPE_COUNT_TRANS_GROUP,
                ),
            )

            handleResourceSilent(
                res,
                onSuccess = { data ->
                    val currentTime = formatTimeString.format(Calendar.getInstance().time)
                    delay(500)
                    _sectionsHome.updateCard<HomeCardItem.RecommendedCard> { card ->
                        // parse dữ liệu từ backend
                        val pending =
                            data.transGroup?.firstOrNull { it.code == Tags.HOME_PENDING }?.count ?: "0"
                        val approved =
                            data.transGroup?.firstOrNull { it.code == Tags.HOME_APPROVED }?.count ?: "0"
                        val rejected =
                            data.transGroup?.firstOrNull { it.code == Tags.HOME_REJECTED }?.count ?: "0"

                        card.copy(
                            state = card.state.copy(
                                isLoading = false,
                                isEnableLoading = false,
                                updatedTime = currentTime,
                                pendingRequest = pending,
                                approveRequest = approved,
                                rejectRequest = rejected,
                            ),
                        )
                    }

                    // setup timer
                    timerRcm?.cancel()
                    timerRcm = startCountdown(
                        action = {
                            _sectionsHome.updateCard<HomeCardItem.RecommendedCard> { card ->
                                card.copy(
                                    state = card.state.copy(isEnableLoading = true),
                                )
                            }
                        },
                    )
                },
                onError = {
                    _sectionsHome.updateCard<HomeCardItem.RecommendedCard> { card ->
                        card.copy(
                            state = card.state.copy(
                                isLoading = false,
                                isEnableLoading = true,
                            ),
                        )
                    }
                },
            )
        }
    }

    private fun homeAccountList() {
        launchJob(showLoading = false) {
            _uiState.update {
                it.copy(
                    isBalanceLoading = true,
                )
            }
            val res = homeUseCase.homeAccountList(
                HomeAccountListParams(
                    username = userProf.getUserName() ?: "",
                    isBalance = Tags.IS_BALANCE,
                ),
            )

            handleResourceSilent(
                res,
                onSuccess = { data ->
                    prefs.setBoolean(Tags.IS_RELOAD_ACCOUNT, true)
                    _uiState.update {
                        it.copy(
                            totalBalance = data.assetAmount?.formatAsMoney() ?: "",
                            isBalanceLoading = false,
                            isVisibleBalance = true,
                        )
                    }

                    assetState = AssetUiModel(
                        total = data.assetAmount?.formatAsMoney() ?: "",
                        paymentTotal = data.ddaSumAmount?.formatAsMoney() ?: "",
                        depositAmount = data.cdSumAmount?.formatAsMoney() ?: "",
                    )

                    creditState = if (data.lnAccounts.isNullOrEmpty()) {
                        null
                    } else {
                        CreditUiModel(
                            total = data.lnSumAmount?.formatAsMoney() ?: "",
                            paymentDate = data.lnAccounts?.get(0)?.nextPaymentDate ?: "",
                            paymentTotal = data.lnSumAmount?.formatAsMoney() ?: "",
                            paymentDetail = data.lnAccounts?.get(0)?.accountName ?: "",
                        )
                    }
                },
                onError = {
                    _uiState.update {
                        it.copy(
                            totalBalance = null,
                            isBalanceLoading = false,
                            isVisibleBalance = true,
                        )
                    }
                },
            )
        }
    }

    // RecentCard
    private fun listLatest() {
        launchJob(showLoading = false) {
            _sectionsHome.updateCard<HomeCardItem.RecentTransactionCard> { card ->
                card.copy(state = RecentTransactionUiState.Loading)
            }
            val res = homeUseCase.listLatest(
                ListLatestParams(
                    username = userProf.getUserName() ?: "",
                    role = userProf.getRoleId() ?: "",
                    status = Tags.HOME_LIST_LATEST_PARAMS_STATUS,
                    pageSize = Tags.HOME_LIST_LATEST_PARAMS_PAGE_SIZE,
                    pageNum = Tags.HOME_LIST_LATEST_PARAMS_PAGE_NUM,
                ),
            )
            handleResourceSilent(
                res,
                onSuccess = { data ->
                    delay(500)
                    val transactionInfoList = data.transactionInfoList.orEmpty()

                    if (transactionInfoList.isEmpty()) {
                        _sectionsHome.updateCard<HomeCardItem.RecentTransactionCard> { card ->
                            card.copy(state = RecentTransactionUiState.Empty)
                        }
                        return@handleResourceSilent
                    }

                    val newList = transactionInfoList.take(3).map {
                        RecentTransactionUiModel(
                            logoBank = it.iconUrl ?: "",
                            title = it.tranTypeName ?: "",
                            content = it.toAccountName ?: "",
                            secondaryContent = (
                                it.amount?.formatAsMoney()
                                    ?: ""
                                ) + " ${it.currency}",
                        )
                    }

                    _sectionsHome.updateCard<HomeCardItem.RecentTransactionCard> { card ->
                        card.copy(
                            state = RecentTransactionUiState.Success(newList),
                        )
                    }
                },
                onError = {
                    _sectionsHome.updateCard<HomeCardItem.RecentTransactionCard> { card ->
                        card.copy(state = RecentTransactionUiState.LoadingFail)
                    }
                },
            )
        }
    }

    private var listRcm: List<FunctionItemData>? = null

    // recommended
    private fun listKey() {
        launchJob(showLoading = false) {
            _sectionsHome.updateCard<HomeCardItem.RecommendedCard> { card ->
                card.copy(state = card.state.copy(isLoadingRecommended = true))
            }
            val res = homeUseCase.listKey(
                ListKeyParams(
                    username = userProf.getUserName() ?: "",
                    type = Tags.HOME_LIST_KEY_PARAM_R,
                    role = userProf.getRoleId() ?: "",
                ),
            )
            handleResourceSilent(
                res,
                onSuccess = { data ->
                    delay(500)
                    val list = data.data.orEmpty()
                    listRcm = list.take(3).map {
                        FunctionItemData(
                            iconRes = R.drawable.ic_home_transfer,
                            functionName = it?.key ?: "",
                            functionId = it?.functionId ?: "",
                            valueRes = null,
                        )
                    }
                    _sectionsHome.updateCard<HomeCardItem.RecommendedCard> { card ->
                        card.copy(
                            state = card.state.copy(
                                listSuggestion = listRcm,
                                isLoadingRecommended = false,
                            ),
                        )
                    }
                },
                onError = {
                    _sectionsHome.updateCard<HomeCardItem.RecommendedCard> { card ->
                        card.copy(
                            state = card.state.copy(
                                isLoadingRecommended = false,
                                listSuggestion = listRcm,
                            ),
                        )
                    }
                },
            )
        }
    }

    private fun startCountdown(action: () -> Unit, totalMillis: Long = 5 * 60 * 1000L): Job {
        return viewModelScope.launch {
            var remaining = totalMillis
            while (remaining > 0) {
                delay(1000L)
                remaining -= 1000L
            }
            // Khi hết giờ
            action()
        }
    }

    // Call API check hết phiên client
    fun getMobileConfigLast() {
        launchJobSilent {
            val res = homeUseCase.mobileConfigLast(
                MobileConfigLastParams(
                    username = userProf.getUserName() ?: "",
                    lastModified = appConfig.getTotalLastModified()
                        .toString(),
                ),
            )
            handleResourceSilent(
                resource = res,
                onSuccess = { data ->
                    // set lại giá trị để chỉ gọi 1 lần API khi login vào
                    appConfig.setTimerConfig(false)
                    // Nếu configList rỗng thì giữ nguyên như cũ không thay đổi
                    if (!data.configList.isNullOrEmpty()) {
                        appConfig.updateTotalLastModified(data.totalLastModified!!)
                        // Lưu lại list configList để sau dùng
                        prefs.setString(Tags.TIMECONFINGLIST, Utils.g().provideGson().toJson(data.configList))
                    }
                },
            )
        }
    }

    fun updateFeedback(point: String, comment: String) {
        launchJob {
            val params = CSatRateParams(
                comment = comment,
                functionId = "MobileNew",
                ratePoint = point,
                userName = userProf.getUserName(),
            )
            val res = cSatUseCase.rateCSAT(params)
            handleResource(res) { data ->
                sendEvent(HomePageEvent.CompleteFeedback)
                // complete xong thi set show icon = false
            }
        }
    }

    private fun configCSat() = launchJobSilent {
        val params = CSatConfigParams(
            functionId = "MobileNew",
            userName = userProf.getUserName(),
        )
        val res = cSatUseCase.configCSAT(params)
        handleResourceSilent(
            resource = res,
            onSuccess = { data ->
                // showButton = Y thi hien thi icon
            },
        )
    }

    override fun onCleared() {
        super.onCleared()
        timerRcm?.cancel()
        timerRequest?.cancel()
    }
}

fun String.formatAsMoney(): String {
    val decimalIndex = this.indexOf(".")
    val trimmed = if (decimalIndex != -1 && decimalIndex + 3 <= this.length) {
        this.substring(0, decimalIndex + 3) // lấy phần nguyên + . + 2 số
    } else {
        this
    }
    val number = trimmed.toDoubleOrNull() ?: return this
    val rounded = round(number * 100) / 100
    val formatter = DecimalFormat(Tags.HOME_MONEY_PATTERN, DecimalFormatSymbols(Locale.US))
    return formatter.format(rounded)
}

data class HomePageUiState(
    val approveLever: String = "",
    val userName: String = "",
    val companyName: String = "",
    val isShowShortcut: Boolean = false,
    val bottomNavIndex: Int = 0,
    val isVisibleBalance: Boolean = false,
    val totalBalance: String? = "",
    val isBalanceLoading: Boolean = false,
    val listFuncShortcut: List<FunctionItemData> = listOf(),
)

sealed class HomePageEvent : OneTimeEvent {
    data object NavigateToSetting : HomePageEvent()
    data object NavigateToOttScreen : HomePageEvent()
    data object NavigateToAccount : HomePageEvent()
    data object NavigateToExplore : HomePageEvent()
    data object NavigateToCt : HomePageEvent()
    data object NavigateToEditShortcut : HomePageEvent()
    data object NavigateToManager : HomePageEvent()
    data object NavigateToApprovalList : HomePageEvent()
    data object NotYetImplement : HomePageEvent()
    data object ShowFeedBack : HomePageEvent()
    data object CompleteFeedback : HomePageEvent()
}

sealed interface HomePageAction {
    data object OnClickAvatar : HomePageAction
    data object OnClickExploreButton : HomePageAction
    data object OnClickVisibleButton : HomePageAction
    data object OnClickNotification : HomePageAction
    data class OnTabSelected(val index: Int) : HomePageAction
    data object OnClickSearch : HomePageAction
    data object OnClickLoadingMyRequest : HomePageAction
    data object OnClickLoadingRcmCard : HomePageAction
    data object NavigateCt : HomePageAction
    data object ClickShortcut : HomePageAction
    data object ClickEditShortcut : HomePageAction
    data object OnClickAllTransaction : HomePageAction
    data class OnCLickItem(val id: String) : HomePageAction
    data object TestTest : HomePageAction
    data object ClickCsat : HomePageAction
}

sealed class HomeCardItem {
    data class RecommendedCard(val state: RecommendedUiModelState) : HomeCardItem()
    data class RecentTransactionCard(val state: RecentTransactionUiState) :
        HomeCardItem()

    data class MyRequestCard(val state: MyRequestUiState) : HomeCardItem()
    data object BannerCard : HomeCardItem()
}