package com.vietinbank.feature_home.ui.screen.ott_setting.list_account

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationSelector
import com.vietinbank.core_ui.components.SelectorType
import com.vietinbank.core_ui.components.foundation.textfield.FoundationFieldType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_home.R
import com.vietinbank.feature_home.ui.screen.ott_setting.ott_service_new_ui.OttServiceAction
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun ListAccountScreen(
    uiState: ListAccountUiState,
    onAction: (OttServiceAction) -> Unit,
    onBackClick: () -> Unit = {},
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding()
            .padding(horizontal = FDS.Sizer.Padding.padding8),
    ) {
        FoundationAppBar(
            modifier = Modifier.padding(vertical = FDS.Sizer.Padding.padding8),
            title = stringResource(uiState.serviceType.title),
            titleStyle = FDS.Typography.headingH2,
            isLightIcon = false,
            onNavigationClick = {
                onBackClick()
            },
            isCustomActionUseSafeClick = false,
        )
        Row(
            Modifier.padding(horizontal = FDS.Sizer.Padding.padding16),
        ) {
            FoundationText(
                text = stringResource(uiState.serviceType.description),
                textAlign = TextAlign.Left,
                style = FDS.Typography.captionCaptionL,
                color = FDS.Colors.characterTertiary,
            )
        }

        Column(
            Modifier
                .padding(top = FDS.Sizer.Padding.padding16)
                .clip(RoundedCornerShape(FDS.Sizer.Padding.padding32))
                .background(FDS.Colors.white)
                .padding(vertical = FDS.Sizer.Padding.padding24),
        ) {
            Column(
                modifier = Modifier.weight(1f),
            ) {
                Box(
                    Modifier
                        .padding(bottom = FDS.Sizer.Padding.padding8)
                        .background(
                            FDS.Colors.homeBackgroundIcon,
                            RoundedCornerShape(FDS.Sizer.Padding.padding32),
                        )
                        .then(
                            if (uiState.search.isEmpty()) {
                                Modifier
                            } else {
                                Modifier
                                    .border(
                                        1.dp,
                                        FDS.Colors.borderGradientStart,
                                        RoundedCornerShape(FDS.Sizer.Padding.padding32),
                                    )
                            },
                        ),
                    contentAlignment = Alignment.Center,
                ) {
                    FoundationFieldType(
                        value = uiState.search,
                        onValueChange = {
//                            onAction(ShortcutAction.SearchItem(it))
                        },
                        placeholder = stringResource(R.string.shortcut_hint_search),
                        isHaveClearIcon = true,
                        clearValue = {
//                            onAction(ShortcutAction.ClearValue)
                        },
                        leadingIcon = {
                            Icon(
                                painter = painterResource(
                                    id = R.drawable.ic_home_search,
                                ),
                                contentDescription = "",
                                tint = if (uiState.search.isEmpty()) {
                                    FDS.Colors.characterTertiary
                                } else {
                                    FDS.Colors.tabTextActiveInline
                                },
                                modifier = Modifier
                                    .size(FDS.Sizer.Icon.icon16),
                            )
                        },
                    )
                }

                LazyColumn {
                }
            }

            Column {
                Modifier
                    .padding(top = FDS.Sizer.Padding.padding16)
                    .clip(RoundedCornerShape(FDS.Sizer.Padding.padding32))
                    .background(FDS.Colors.backgroundBgScreen)
                    .padding(vertical = FDS.Sizer.Padding.padding24)
            }
        }
    }
}

@Composable
fun AccountItem(
    account: AccountUiModel,
    onClick: () -> Unit,
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(12.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        FoundationSelector(
            boxType = SelectorType.Checkbox,
        )
        Column(modifier = Modifier.padding(start = 8.dp)) {
            FoundationText(
                text = "${account.domain.accountNumber} • ${account.domain.accountType}",
                style = MaterialTheme.typography.bodyMedium,
            )
        }
    }
}