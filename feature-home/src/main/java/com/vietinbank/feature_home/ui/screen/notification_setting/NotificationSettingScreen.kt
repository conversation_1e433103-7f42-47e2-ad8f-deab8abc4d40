package com.vietinbank.feature_home.ui.screen.notification_setting

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.base.compose.BaseAppBar
import com.vietinbank.core_ui.base.compose.BaseCustomSwitch
import com.vietinbank.feature_home.R
import com.vietinbank.feature_home.ui.view.AccountBalanceItem
import com.vietinbank.feature_home.utils.AccountType

@Composable
fun NotificationSettingScreen(
    uiState: NotificationSettingUiState,
    onAction: (NotificationSettingAction) -> Unit,
) {
    val (title, content) = when (uiState.tag) {
        AccountType.UnDefine -> "" to ""
        AccountType.PaymentAccount ->
            "Chuyển đổi nhận thông báo qua tin nhắn SMS" to
                "Thực hiện chuyển sang phương thức nhận thông báo BĐSD qua SMS"
        AccountType.IdentityAccount ->
            "Huỷ đăng ký nhận thông báo BĐSD" to
                "Huỷ đăng ký nhận thông báo BĐSD theo tài khoản định danh đã đăng ký"
        AccountType.LoanAccount -> "" to ""
    }

    Column(
        modifier = Modifier.fillMaxSize(),
    ) {
        BaseAppBar(
            title = "Cài đặt thông báo",
            onBackClick = {
                onAction(NotificationSettingAction.NavigateBackAction)
            },
        )

        Column(
            modifier = Modifier
                .weight(1f)
                .padding(top = 15.dp)
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(15.dp),
        ) {
            AccountBalanceItem(
                itemTitle = "Nhận thông báo BĐSD trên thiết bị",
                itemContent = "Tắt/Bật hiển thị thông báo biến động số dư trên thiết bị",
                shape = RoundedCornerShape(4.dp),
                trailingTitleAction = {
                    BaseCustomSwitch(
                        isChecked = uiState.ottState,
                        onCheckedChange = {
                            onAction(NotificationSettingAction.UpdateStatusOttAction)
                        },
                    )
                },
            )
            AccountBalanceItem(
                itemTitle = title,
                itemContent = content,
                shape = RoundedCornerShape(4.dp),
                trailingTitleAction = {
                    Image(
                        painter = painterResource(R.drawable.ic_right_arrow),
                        contentDescription = "",
                        modifier = Modifier.size(24.dp),
                    )
                },
                onClickItem = {
                    onAction(NotificationSettingAction.ClickRegisButtonAction)
                },
            )
        }
    }
}