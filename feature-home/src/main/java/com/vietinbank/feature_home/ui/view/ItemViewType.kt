package com.vietinbank.feature_home.ui.view

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_home.R

@Composable
fun ItemViewType(
    itemTitle: String,
    itemContent: String? = null,
    itemIcon: Int? = null,
    isShowDivideLine: Boolean,
    switchAction: Boolean = false,
    onClickItem: () -> Unit = {},
    shape: RoundedCornerShape,
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .clip(shape)
            .background(Color.White)
            .padding(horizontal = 14.dp)
            .safeClickable { onClickItem() },
    ) {
        Row(
            modifier = Modifier
                .padding(vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Column(
                modifier = Modifier.weight(1f),
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(22.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(10.dp),
                ) {
                    if (itemIcon != null) {
                        Image(
                            modifier = Modifier.size(16.dp),
                            painter = painterResource(itemIcon),
                            contentDescription = "",
                        )
                    }
                    BaseText(
                        text = itemTitle,
                        textSize = 14.sp,
                        fontCus = 2,
                        color = Color(0xFF0A5994),
                    )
                }
                if (!itemContent.isNullOrEmpty()) {
                    BaseText(
                        modifier = Modifier.padding(top = 4.dp),
                        text = itemContent,
                        textSize = 14.sp,
                        color = Color(0xFF062A46),
                    )
                }
            }
            Image(
                modifier = Modifier.size(22.dp),
                painter = painterResource(R.drawable.ic_right_arrow),
                contentDescription = "",
            )
        }

        if (isShowDivideLine) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(0.5.dp)
                    .background(Color(0xFFC6E9FC))
                    .align(Alignment.BottomCenter),
            )
        }
    }
}