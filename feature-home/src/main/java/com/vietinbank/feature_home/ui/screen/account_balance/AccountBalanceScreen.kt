package com.vietinbank.feature_home.ui.screen.account_balance

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.vietinbank.core_ui.base.compose.BaseAppBar
import com.vietinbank.core_ui.base.views.BaseTabLayout

@Composable
fun AccountBalanceScreen(
    uiState: AccountBalanceUiState,
    onAction: (AccountBalanceAction) -> Unit,
) {
    val tabTitles = listOf(
        "Tài khoản thanh toán",
        "Tài khoản định danh",
        "Tài khoản vay",
    )
    Column(
        modifier = Modifier.fillMaxSize(),
    ) {
        BaseAppBar(
            title = "Biến động số dư",
            onBackClick = {
                onAction(AccountBalanceAction.NavigateBack)
            },
        )
        BaseTabLayout(
            tabs = tabTitles,
            selectedTabIndex = uiState.tag.value,
            onTabSelected = { onAction(AccountBalanceAction.SelectedTabIndex(it)) },
        )
        PaymentAccountTabView(
            uiState = uiState,
            onAction = onAction,
        )
    }
}