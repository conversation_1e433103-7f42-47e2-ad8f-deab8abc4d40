package com.vietinbank.feature_home.ui.screen.ott_setting.ott_service_new_ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.ListItem
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun OttServiceBottomSheet(
    visible: <PERSON>olean,
    accounts: List<Triple<String, String, Int?>>,
    onDismiss: () -> Unit,
    onPicked: (String) -> Unit,
) {
    BaseBottomSheet<String>(
        visible = visible,
        onDismissRequest = onDismiss,
        onResult = { picked ->
            onPicked(picked)
            // Auto-dismiss handled by BaseBottomSheet
        },
        secureFlag = false, // Set to true if showing sensitive account data
    ) { onResult ->
        Column(
            Modifier
                .padding(top = FDS.Sizer.Padding.padding16)
                .clip(RoundedCornerShape(FDS.Sizer.Padding.padding32))
                .background(Color.White)
                .padding(vertical = FDS.Sizer.Padding.padding24),
        ) {
            // Title
            FoundationText(
                text = "Loại tài khoản",
                style = FDS.Typography.headingH3,
                color = FDS.Colors.characterHighlighted,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FDS.Sizer.Padding.padding16),
            )

            HorizontalDivider(
                color = FDS.Colors.strokeDivider,
                thickness = FDS.Sizer.Stroke.stroke1,
            )

            // Account list
            accounts.forEach { account ->
                ListItem(
                    headlineContent = {
                        FoundationText(
                            text = account.second,
                            style = FDS.Typography.bodyB2,
                            color = FDS.Colors.characterPrimary,
                        )
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .safeClickable(
                            onSafeClick = {
                                onResult(account.first) // This will trigger onPicked and auto-dismiss
                            },
                        ),
                )
            }
        }
    }
}