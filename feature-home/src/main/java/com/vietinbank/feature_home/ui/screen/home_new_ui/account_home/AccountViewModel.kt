package com.vietinbank.feature_home.ui.screen.home_new_ui.account_home

import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.CustomEncryptedPrefs
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.home.HomeAccountListParams
import com.vietinbank.core_domain.models.home.ListFunctionParams
import com.vietinbank.core_domain.repository.cache.IOttRegistrationRepository
import com.vietinbank.core_domain.usecase.home.HomeUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.OneTimeEvent
import com.vietinbank.feature_home.R
import com.vietinbank.feature_home.ui.screen.home_new_ui.home_page.formatAsMoney
import com.vietinbank.feature_home.ui.view.AssetUiModel
import com.vietinbank.feature_home.ui.view.CreditUiModel
import com.vietinbank.feature_home.ui.view.home_card.FunctionItemData
import com.vietinbank.feature_home.ui.view.home_card.defaultFunc
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class AccountViewModel @Inject constructor(
    private val homeUseCase: HomeUseCase,
    override val sessionManager: ISessionManager,
    private val prefs: CustomEncryptedPrefs,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    val ottRegistrationRepository: IOttRegistrationRepository,
) : BaseViewModel() {
    private val _uiState = MutableStateFlow(AccountUiState())
    val uiState: StateFlow<AccountUiState> = _uiState.asStateFlow()

    init {
        _uiState.update {
            it.copy(
                userName = userProf.getFullName() ?: "",
                companyName = (userProf.getCorpName() + " " + userProf.getCifNo()),
            )
        }
    }

    fun isReloadingData() {
        val isReloading = prefs.getBoolean(Tags.IS_RELOAD_ACCOUNT)
        if (isReloading) {
            homeAccountList()
        } else {
            val dataAsset = prefs.getString(Tags.HOME_ACCOUNT_ASSET)
            val typeAsset = object : TypeToken<AssetUiModel>() {}.type

            val isHaveCredit = prefs.getBoolean(Tags.IS_HAVE_CREDIT_ACCOUNT)

            val dataCredit = prefs.getString(Tags.HOME_ACCOUNT_CREDIT)
            val typeCredit = object : TypeToken<CreditUiModel>() {}.type

            when {
                !dataAsset.isNullOrEmpty() -> {
                    _uiState.update {
                        it.copy(
                            assetState = Utils.g().provideGson().fromJson(dataAsset, typeAsset),
                        )
                    }
                }
                !dataCredit.isNullOrEmpty() && isHaveCredit -> {
                    _uiState.update {
                        it.copy(
                            creditState = Utils.g().provideGson().fromJson(dataCredit, typeCredit),
                        )
                    }
                }
                dataAsset.isNullOrEmpty() || (isHaveCredit && dataCredit.isNullOrEmpty()) -> {
                    homeAccountList()
                }
                else -> {
                }
            }
        }
    }

    fun onAction(action: AccountAction) {
        when (action) {
            is AccountAction.OnTabSelected -> {
                when (action.index) {
                    0 -> {
                        sendEvent(AccountEvent.NavigateToHome)
                    }

                    2 -> {
                        sendEvent(AccountEvent.NavigateToExplore)
                    }

                    else -> {}
                }
            }

            AccountAction.TestTest -> sendEvent(AccountEvent.NotYetImplement)
            AccountAction.OnClickAvatar -> {
                sendEvent(AccountEvent.NavigateToSetting)
            }

            AccountAction.OnClickNotification -> {
                sendEvent(AccountEvent.NavigateToOttScreen)
            }

            AccountAction.ClickShortcut -> {
                if (_uiState.value.isShowShortcut) {
                    _uiState.update {
                        it.copy(
                            isShowShortcut = false,
                        )
                    }
                } else {
                    getFavouriteList()
                }
            }

            AccountAction.ClickEditShortcut -> {
                sendEvent(AccountEvent.NavigateToEditShortcut)
                _uiState.update {
                    it.copy(
                        isShowShortcut = false,
                    )
                }
            }
        }
    }

    private fun homeAccountList() {
        launchJob(showLoading = false) {
            _uiState.update {
                it.copy(
                    assetState = AssetUiModel(
                        isLoading = true,
                    ),
                )
            }
            val res = homeUseCase.homeAccountList(
                HomeAccountListParams(
                    username = userProf.getUserName() ?: "",
                ),
            )
            handleResource(res) { data ->
                _uiState.update {
                    it.copy(
                        assetState = AssetUiModel(
                            total = data.assetAmount?.formatAsMoney() ?: "",
                            paymentTotal = data.ddaSumAmount?.formatAsMoney() ?: "",
                            depositAmount = data.cdSumAmount?.formatAsMoney() ?: "",
                            isLoading = false,
                        ),
                        creditState = if (data.lnAccounts.isNullOrEmpty()) {
                            prefs.setBoolean(Tags.IS_HAVE_CREDIT_ACCOUNT, false)
                            null
                        } else {
                            prefs.setBoolean(Tags.IS_HAVE_CREDIT_ACCOUNT, true)
                            CreditUiModel(
                                total = data.lnSumAmount?.formatAsMoney() ?: "",
                                paymentDate = data.lnAccounts?.get(0)?.nextPaymentDate ?: "",
                                paymentTotal = data.lnSumAmount?.formatAsMoney() ?: "",
                                paymentDetail = data.lnAccounts?.get(0)?.accountName ?: "",
                            )
                        },
                    )
                }
                prefs.setBoolean(Tags.IS_RELOAD_ACCOUNT, false)
                prefs.setString(Tags.HOME_ACCOUNT_ASSET, Utils.g().provideGson().toJson(_uiState.value.assetState))
                prefs.setString(Tags.HOME_ACCOUNT_CREDIT, Utils.g().provideGson().toJson(_uiState.value.creditState))
            }
        }
    }

    private fun listFavoriteFunction() {
        launchJob(showLoading = true) {
            val res = homeUseCase.listFavoriteFunction(
                ListFunctionParams(
                    username = userProf.getUserName().orEmpty(),
                    roleId = userProf.getRoleId().toString(),
                    role = userProf.getRoleId().toString(),
                ),
            )
            handleResource(res) { data ->
                val func = data.data ?: listOf()
                val newList = if (func.isEmpty()) {
                    defaultFunc(financialPackage = userProf.getFinancialPackage() ?: "", roleLevel = userProf.getRoleLevel() ?: "")
                } else {
                    func.map {
                        FunctionItemData(
                            iconRes = R.drawable.ic_home_transfer,
                            functionName = it?.functionName ?: "",
                            functionId = it?.functionId ?: "",
                            groupName = it?.groupName ?: "",
                            groupId = it?.groupId ?: "",
                            orderNumber = it?.orderNumber?.toInt() ?: 0,
                        )
                    }.sortedBy {
                        it.orderNumber
                    }
                }
                prefs.setString(Tags.LIST_FAVOURITE, Utils.g().provideGson().toJson(newList))
                _uiState.update {
                    it.copy(
                        isShowShortcut = true,
                        listFuncShortcut = newList,
                    )
                }
            }
        }
    }

    private fun getFavouriteList() {
        val data = prefs.getString(Tags.LIST_FAVOURITE)
        val type = object : TypeToken<List<FunctionItemData>>() {}.type
        if (!data.isNullOrEmpty()) {
            _uiState.update {
                it.copy(
                    isShowShortcut = true,
                    listFuncShortcut = Utils.g().provideGson().fromJson(data, type),
                )
            }
        } else {
            listFavoriteFunction()
        }
    }
}

data class AccountUiState(
    val userName: String = "",
    val companyName: String = "",
    val bottomNavIndex: Int = 1,
    val isShowShortcut: Boolean = false,
    val assetState: AssetUiModel = AssetUiModel(),
    val creditState: CreditUiModel? = null,
    val listFuncShortcut: List<FunctionItemData> = listOf(),
)

sealed class AccountEvent : OneTimeEvent {
    data object NotYetImplement : AccountEvent()
    data object NavigateToOttScreen : AccountEvent()
    data object NavigateToSetting : AccountEvent()
    data object NavigateToExplore : AccountEvent()
    data object NavigateToHome : AccountEvent()
    data object NavigateToEditShortcut : AccountEvent()
}

sealed interface AccountAction {
    data class OnTabSelected(val index: Int) : AccountAction
    data object OnClickAvatar : AccountAction
    data object OnClickNotification : AccountAction
    data object ClickShortcut : AccountAction
    data object ClickEditShortcut : AccountAction
    data object TestTest : AccountAction
}