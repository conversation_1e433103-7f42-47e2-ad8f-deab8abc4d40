package com.vietinbank.feature_home.ui.screen.change_notification_sms

import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.OneTimeEvent
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class ChangeNotificationSmsViewModel @Inject constructor(
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val sessionManager: ISessionManager,
    override val ottSetupService: IOttSetupService,

) : BaseViewModel() {

    private val _uiState = MutableStateFlow(ChangeNotificationUiState())
    val uiState: StateFlow<ChangeNotificationUiState> = _uiState.asStateFlow()

    fun onAction(action: ChangeNotificationAction) {
        when (action) {
            ChangeNotificationAction.OnConfirmOtp -> sendEvent(ChangeNotificationEvent.ConfirmOtpEvent)
            ChangeNotificationAction.OnNavigateBack -> sendEvent(ChangeNotificationEvent.NavigateBackEvent)
            is ChangeNotificationAction.OnSelectAlertType -> {
                _uiState.update {
                    it.copy(
                        alertType = action.type,
                    )
                }
            }

            is ChangeNotificationAction.PhoneNumValueChange -> {
                _uiState.update {
                    it.copy(
                        phoneNum = action.phoneNum,
                    )
                }
            }
        }
    }
}

data class ChangeNotificationUiState(
    val phoneNum: String = "",
    val alertType: String? = null,
)

sealed class ChangeNotificationEvent : OneTimeEvent {
    data object NavigateBackEvent : ChangeNotificationEvent()
    data object ConfirmOtpEvent : ChangeNotificationEvent()
}

sealed interface ChangeNotificationAction {
    data object OnNavigateBack : ChangeNotificationAction
    data object OnConfirmOtp : ChangeNotificationAction
    data class OnSelectAlertType(val type: String) : ChangeNotificationAction
    data class PhoneNumValueChange(val phoneNum: String) : ChangeNotificationAction
}