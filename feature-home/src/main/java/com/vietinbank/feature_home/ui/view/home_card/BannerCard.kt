package com.vietinbank.feature_home.ui.view.home_card

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_ui.components.ButtonSize
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.glassMorphismEffect
import com.vietinbank.core_ui.utils.gradientFadeBorder
import com.vietinbank.feature_home.R
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun BannerCard(
    onClickBanner: () -> Unit,
) {
    // Promotion card - Same as NewUserContent
    val promoGlassColors = listOf(
        FDS.Colors.blue300.copy(alpha = GlassMorphismConstants.GLASS_ALPHA_PRIMARY),
        FDS.Colors.blue600.copy(alpha = GlassMorphismConstants.GLASS_ALPHA_SECONDARY),
        FDS.Colors.white.copy(alpha = GlassMorphismConstants.GLASS_ALPHA_TERTIARY),
        Color.Transparent,
    )
    val promoBorderColor = FDS.Colors.blue800
    val promoCornerRadius = FDS.Sizer.Radius.radius32

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(promoCornerRadius))
            .background(FDS.Colors.backgroundDarkBlue)
            .glassMorphismEffect(
                glassColors = promoGlassColors,
                centerYOffset = GlassMorphismConstants.GRADIENT_CENTER_Y_OFFSET,
                radiusMultiplier = GlassMorphismConstants.GRADIENT_RADIUS_MULTIPLIER,
            )
            .gradientFadeBorder(
                borderColor = promoBorderColor,
                cornerRadius = promoCornerRadius,
                strokeWidth = FDS.Sizer.Stroke.stroke1,
                fadeStartAlpha = GlassMorphismConstants.BORDER_FADE_START_ALPHA,
                fadeEndAlpha = GlassMorphismConstants.BORDER_FADE_END_ALPHA,
                fadeEndPosition = GlassMorphismConstants.BORDER_FADE_END_POSITION,
            ),
    ) {
        // Content on the left with padding
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(FDS.Sizer.Padding.padding24),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
        ) {
            Column(
                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap4),
            ) {
                FoundationText(
                    text = stringResource(id = R.string.home_banner_primary),
                    style = FDS.Typography.captionCaptionL,
                    color = FDS.Colors.characterHighlightedLighter,
                )
                FoundationText(
                    text = stringResource(id = R.string.home_banner_secondary),
                    style = FDS.Typography.headingH4,
                    color = FDS.Colors.white,
                    modifier = Modifier.fillMaxWidth(0.65f), // Limit width to prevent overlap with illustration
                )
            }

            Box(
                modifier = Modifier.wrapContentWidth(Alignment.Start),
            ) {
                FoundationButton(
                    isLightButton = false,
                    text = stringResource(id = R.string.home_banner_button),
                    onClick = { onClickBanner() },
                    size = ButtonSize.Small,
                )
            }
        }

        // Illustration positioned at top-end, naturally clipped by card boundary
        // Following Figma design: image sits at right edge and gets cut off
        Image(
            painter = painterResource(id = R.drawable.ic_login_illustration),
            contentDescription = null,
            modifier = Modifier
                .size(FDS.Sizer.Padding.padding144) // Fixed container size like Figma (36 * 4 = 144px)
                .align(Alignment.TopEnd)
                .offset(y = FDS.Sizer.Padding.padding8), // Only slight vertical offset, no horizontal offset needed
            contentScale = ContentScale.Crop,
        )
    }
}

private object GlassMorphismConstants {
    // Alpha values for gradient layers
    const val GLASS_ALPHA_PRIMARY = 0.4f
    const val GLASS_ALPHA_SECONDARY = 0.2f
    const val GLASS_ALPHA_TERTIARY = 0.08f

    // Gradient positioning
    const val GRADIENT_CENTER_Y_OFFSET = -0.05f // Offset above component for top sheen
    const val GRADIENT_RADIUS_MULTIPLIER = 1.6f // Wider spread horizontally

    // Border gradient fade
    const val BORDER_FADE_START_ALPHA = 1.0f
    const val BORDER_FADE_END_ALPHA = 0.1f // Almost transparent at bottom left
    const val BORDER_FADE_END_POSITION = 0.3f // Fade covers 30% of width
}