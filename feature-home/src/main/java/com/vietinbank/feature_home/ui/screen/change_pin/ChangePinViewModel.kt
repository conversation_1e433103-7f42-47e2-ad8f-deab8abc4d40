package com.vietinbank.feature_home.ui.screen.change_pin

import com.vietinbank.core_common.biometric.IBiometricManager
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.CustomEncryptedPrefs
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.OneTimeEvent
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class ChangePinViewModel @Inject constructor(
    private val biometricManager: IBiometricManager,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    private val prefs: CustomEncryptedPrefs,
    override val sessionManager: ISessionManager,
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(ChangePinState())
    val uiState: StateFlow<ChangePinState> = _uiState.asStateFlow()

    fun onAction(action: ChangePinAction) {
        when (action) {
            is ChangePinAction.OnBackPressed -> {
                sendEvent(ChangePinEvent.NavigateBack)
            }

            is ChangePinAction.OnOldPinChanged -> {
                updateOldPin(action.pin)
            }

            is ChangePinAction.OnNewPinChanged -> {
                updateNewPin(action.pin)
            }

            is ChangePinAction.OnConfirmPinChanged -> {
                updateConfirmPin(action.pin)
            }

            is ChangePinAction.OnContinueClicked -> {
                validateAndSetupPin()
            }
        }
    }

    private fun checkPin(inputPin: String): Boolean {
        val cifNo = userProf.getCifNo() ?: return false
        val key = "BĐSD_PIN_$cifNo"
        val savedPin = prefs.getString(key, encrypted = true)
        return inputPin == savedPin
    }

    private fun savePinToSecureStorage(pin: String) {
        // Save PIN using CustomEncryptedPrefs
        val cifNo = userProf.getCifNo()
        val key = "BĐSD_PIN_$cifNo"
        prefs.setString(key, pin, encrypted = true)
    }

    fun onBiometricSuccess() {
        _uiState.update { it.copy(isLoading = true) }

        try {
            // Save PIN to secure storage
            savePinToSecureStorage(temporaryPin)

            // Clear temporary PIN
            temporaryPin = ""

            sendEvent(ChangePinEvent.ShowSuccessDialog("Quý khách đã cài đặt mã PIN thành công"))
            _uiState.update { it.copy(isLoading = false) }
        } catch (e: Exception) {
            _uiState.update { it.copy(isLoading = false) }
            sendEvent(ChangePinEvent.ShowError("Có lỗi xảy ra khi lưu mã PIN"))
        }
    }

    fun onBiometricError(errorMessage: String) {
        sendEvent(ChangePinEvent.ShowError(errorMessage))
    }

    private fun updateOldPin(pin: String) {
        if (pin.length <= 6 && pin.all { it.isDigit() }) {
            _uiState.update { currentState ->
                currentState.copy(
                    oldPin = pin,
                    oldPinError = null,
                )
            }
        }
    }

    private fun updateNewPin(pin: String) {
        if (pin.length <= 6 && pin.all { it.isDigit() }) {
            _uiState.update { currentState ->
                currentState.copy(
                    newPin = pin,
                    newPinError = null,
                )
            }
        }
    }

    private fun updateConfirmPin(pin: String) {
        if (pin.length <= 6 && pin.all { it.isDigit() }) {
            _uiState.update { currentState ->
                currentState.copy(
                    confirmPin = pin,
                    confirmPinError = null,
                )
            }
        }
    }

    // Store PIN temporarily during setup
    private var temporaryPin: String = ""

    private fun validateAndSetupPin() {
        val currentState = _uiState.value
        var hasError = false

        // Reset errors
        _uiState.update { it.copy(oldPinError = null, newPinError = null, confirmPinError = null) }

        // Validate old PIN
        when {
            currentState.oldPin.isEmpty() -> {
                _uiState.update { it.copy(oldPinError = "Quý khách vui lòng nhập mã PIN hiện tại") }
                hasError = true
            }

            currentState.oldPin.length < 6 -> {
                _uiState.update { it.copy(oldPinError = "Mã PIN hiện tại phải bao gồm 6 ký tự") }
                hasError = true
            }

            !checkPin(currentState.oldPin) -> {
                _uiState.update { it.copy(oldPinError = "Mã PIN hiện tại không chính xác. Quý khách vui lòng kiểm tra lại") }
                hasError = true
            }
        }

        // Validate new PIN
        when {
            currentState.newPin.isEmpty() -> {
                _uiState.update { it.copy(newPinError = " Quý khách vui lòng nhập mã PIN mới") }
                hasError = true
            }

            currentState.newPin.length < 6 -> {
                _uiState.update { it.copy(newPinError = "Mã PIN mới phải bao gồm 6 ký tự") }
                hasError = true
            }

            !isValidPin(currentState.newPin) -> {
                _uiState.update { it.copy(newPinError = "Mã PIN không hợp lệ. Vui lòng không sử dụng các số liên tiếp hoặc giống nhau") }
                hasError = true
            }

            currentState.newPin == currentState.oldPin -> {
                _uiState.update { it.copy(confirmPinError = " Mã PIN mới không được phép trùng mã PIN hiện tại") }
                hasError = true
            }
        }

        // Validate confirm PIN
        when {
            currentState.confirmPin.isEmpty() -> {
                _uiState.update { it.copy(confirmPinError = " Quý khách vui lòng nhập lại mã PIN") }
                hasError = true
            }

            currentState.confirmPin != currentState.newPin -> {
                _uiState.update { it.copy(confirmPinError = "Mã PIN mới không trùng nhau. Quý khách vui lòng kiểm tra lại") }
                hasError = true
            }
        }

        if (!hasError) {
            _uiState.update { it.copy(isLoading = true) }
            // Check biometric availability
            if (biometricManager.canAuthenticate()) {
                // Save PIN temporarily and request biometric authentication
                temporaryPin = currentState.newPin
                sendEvent(ChangePinEvent.ShowBiometricPrompt)
            } else {
                sendEvent(ChangePinEvent.ShowBiometricSetupRequired)
            }

            _uiState.update { it.copy(isLoading = false) }
        }
    }

    private fun isValidPin(pin: String): Boolean {
        // Check if all digits are the same
        if (pin.all { it == pin[0] }) return false

        // Check for sequential digits
        val sequentialPatterns = listOf(
            "012345", "123456", "234567", "345678", "456789",
            "543210", "654321", "765432", "876543", "987654",
        )
        return !sequentialPatterns.any { pin.contains(it) }
    }
}

data class ChangePinState(
    val oldPin: String = "",
    val newPin: String = "",
    val confirmPin: String = "",
    val oldPinError: String? = null,
    val newPinError: String? = null,
    val confirmPinError: String? = null,
    val isLoading: Boolean = false,
    val biometricAvailable: Boolean = false,
)

sealed class ChangePinEvent : OneTimeEvent {
    data object NavigateBack : ChangePinEvent()
    data object ShowBiometricPrompt : ChangePinEvent()
    data object ShowBiometricSetupRequired : ChangePinEvent()
    data class ShowSuccessDialog(val message: String) : ChangePinEvent()
    data class ShowError(val message: String) : ChangePinEvent()
}