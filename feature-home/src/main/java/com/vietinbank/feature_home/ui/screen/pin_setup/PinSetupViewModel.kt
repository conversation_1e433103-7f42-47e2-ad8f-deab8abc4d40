package com.vietinbank.feature_home.ui.screen.pin_setup

import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.biometric.IBiometricManager
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.CustomEncryptedPrefs
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class PinSetupViewModel @Inject constructor(
    private val biometricManager: IBiometricManager,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    private val prefs: CustomEncryptedPrefs,
    override val sessionManager: ISessionManager,
) : BaseViewModel() {

    private val _state = MutableStateFlow(PinSetupState())
    val state: StateFlow<PinSetupState> = _state.asStateFlow()

    private val _events = Channel<PinSetupEvent>(Channel.BUFFERED)
    val events = _events.receiveAsFlow()

    fun onAction(action: PinSetupAction) {
        when (action) {
            is PinSetupAction.OnBackPressed -> {
                viewModelScope.launch {
                    _events.send(PinSetupEvent.NavigateBack)
                }
            }
            is PinSetupAction.OnCancelClicked -> {
                viewModelScope.launch {
                    _events.send(PinSetupEvent.NavigateBack)
                }
            }
            is PinSetupAction.OnFirstPinChanged -> {
                updateFirstPin(action.pin)
            }
            is PinSetupAction.OnConfirmPinChanged -> {
                updateConfirmPin(action.pin)
            }
            is PinSetupAction.OnContinueClicked -> {
                validateAndSetupPin()
            }
        }
    }

    private fun updateFirstPin(pin: String) {
        if (pin.length <= 6 && pin.all { it.isDigit() }) {
            _state.update { currentState ->
                currentState.copy(
                    firstPin = pin,
                    firstPinError = null,
                )
            }
        }
    }

    private fun updateConfirmPin(pin: String) {
        if (pin.length <= 6 && pin.all { it.isDigit() }) {
            _state.update { currentState ->
                currentState.copy(
                    confirmPin = pin,
                    confirmPinError = null,
                )
            }
        }
    }

    private fun validateAndSetupPin() {
        val currentState = _state.value
        var hasError = false

        // Reset errors
        _state.update { it.copy(firstPinError = null, confirmPinError = null) }

        // Validate first PIN
        when {
            currentState.firstPin.isEmpty() -> {
                _state.update { it.copy(firstPinError = "Quý khách vui lòng nhập mã PIN") }
                hasError = true
            }
            currentState.firstPin.length < 6 -> {
                _state.update { it.copy(firstPinError = "Mã PIN phải có đủ 6 chữ số") }
                hasError = true
            }
            !isValidPin(currentState.firstPin) -> {
                _state.update { it.copy(firstPinError = "Mã PIN không hợp lệ. Vui lòng không sử dụng các số liên tiếp hoặc giống nhau") }
                hasError = true
            }
        }

        // Validate confirm PIN
        when {
            currentState.confirmPin.isEmpty() -> {
                _state.update { it.copy(confirmPinError = "Quý khách vui lòng nhập lại mã PIN") }
                hasError = true
            }
            currentState.confirmPin != currentState.firstPin -> {
                _state.update { it.copy(confirmPinError = "Mã PIN không khớp. Vui lòng thử lại") }
                hasError = true
            }
        }

        if (!hasError) {
            viewModelScope.launch {
                _state.update { it.copy(isLoading = true) }

                // Check biometric availability
                if (biometricManager.canAuthenticate()) {
                    // Save PIN temporarily and request biometric authentication
                    temporaryPin = currentState.firstPin
                    _events.send(PinSetupEvent.ShowBiometricPrompt)
                } else {
                    _events.send(PinSetupEvent.ShowBiometricSetupRequired)
                }

                _state.update { it.copy(isLoading = false) }
            }
        }
    }

    private fun isValidPin(pin: String): Boolean {
        // Check if all digits are the same
        if (pin.all { it == pin[0] }) return false

        // Check for sequential digits
        val sequentialPatterns = listOf(
            "012345", "123456", "234567", "345678", "456789",
            "543210", "654321", "765432", "876543", "987654",
        )

        return !sequentialPatterns.any { pin.contains(it) }
    }

    // Store PIN temporarily during setup
    private var temporaryPin: String = ""

    fun onBiometricSuccess() {
        viewModelScope.launch {
            _state.update { it.copy(isLoading = true) }

            try {
                // Save PIN to secure storage
                savePinToSecureStorage(temporaryPin)

                // Clear temporary PIN
                temporaryPin = ""
                savePinState()
                _events.send(PinSetupEvent.ShowSuccessDialog("Quý khách đã cài đặt mã PIN thành công"))
                _state.update { it.copy(isLoading = false) }
            } catch (e: Exception) {
                _state.update { it.copy(isLoading = false) }
                _events.send(PinSetupEvent.ShowError("Có lỗi xảy ra khi lưu mã PIN"))
            }
        }
    }
    private fun savePinState() {
        val cifNo = userProf.getCifNo()
        val key = "IS_ENABLE_PIN_$cifNo"
        prefs.setString(key, "ENABLE", encrypted = false)
    }

    fun onBiometricError(errorMessage: String) {
        viewModelScope.launch {
            _events.send(PinSetupEvent.ShowError(errorMessage))
        }
    }

    private suspend fun savePinToSecureStorage(pin: String) {
        // Save PIN using CustomEncryptedPrefs
        val cifNo = userProf.getCifNo()
        val key = "BĐSD_PIN_$cifNo"
        prefs.setString(key, pin, encrypted = true)
    }
}

sealed class PinSetupEvent {
    data object NavigateBack : PinSetupEvent()
    data object ShowBiometricPrompt : PinSetupEvent()
    data object ShowBiometricSetupRequired : PinSetupEvent()
    data class ShowSuccessDialog(val message: String) : PinSetupEvent()
    data class ShowError(val message: String) : PinSetupEvent()
}