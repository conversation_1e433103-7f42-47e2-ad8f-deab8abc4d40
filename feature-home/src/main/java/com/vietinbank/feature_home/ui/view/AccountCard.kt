package com.vietinbank.feature_home.ui.view

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_ui.components.ShimmerLoading
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.feature_home.R
import com.vietinbank.feature_home.ui.view.home_card.FooterInsight
import com.vietinbank.feature_home.ui.view.home_card.StrokeLine
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

data class AssetUiModel(
    val total: String = "",
    val paymentTotal: String = "",
    val depositAmount: String = "",
    val isLoading: Boolean = false,
)

@Composable
fun AssetCard(
    uiModel: AssetUiModel,
    buttonAction: () -> Unit,
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(FDS.Sizer.Padding.padding32))
            .background(Color.White)
            .padding(vertical = FDS.Sizer.Padding.padding24),
        verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding16),
    ) {
        Column(
            Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding16),

        ) {
            FoundationText(
                text = stringResource(R.string.home_asset_card_title),
                color = FDS.Colors.homeTextButton,
                style = FDS.Typography.headingH3,
            )
            if (uiModel.isLoading) {
                ShimmerLoading()
            } else {
                AccountDetailComponent(
                    title = stringResource(R.string.home_asset_card_total_title),
                    amount = stringResource(R.string.home_account_vnd, uiModel.total),
                    isHaveIcon = false,
                    isLargeSize = true,
                )
            }
        }
        StrokeLine()
        Column(
            Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding16),

        ) {
            if (uiModel.isLoading) {
                ShimmerLoading()
                ShimmerLoading()
            } else {
                AccountDetailComponent(
                    title = stringResource(R.string.home_asset_payment_title),
                    amount = stringResource(R.string.home_account_vnd, uiModel.paymentTotal),
                )

                AccountDetailComponent(
                    title = stringResource(R.string.home_asset_deposit_title),
                    amount = stringResource(R.string.home_account_vnd, uiModel.depositAmount),
                )
            }
        }
        StrokeLine()
        FooterInsight(
            modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
            guideText = stringResource(R.string.home_assest_insignt),
            buttonText = stringResource(R.string.home_button_text_insign),
            buttonAction = buttonAction,
        )
    }
}

data class CreditUiModel(
    val total: String = "",
    val paymentDate: String = "",
    val paymentTotal: String = "",
    val paymentDetail: String = "",
    val isLoading: Boolean = false,
)

@Composable
fun CreditCard(
    uiModel: CreditUiModel,
    buttonAction: () -> Unit,
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(FDS.Sizer.Padding.padding32))
            .background(Color.White)
            .padding(vertical = FDS.Sizer.Padding.padding24),
        verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding16),
    ) {
        Column(
            Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding16),

        ) {
            FoundationText(
                text = stringResource(R.string.home_credit_card_title),
                color = FDS.Colors.homeTextButton,
                style = FDS.Typography.headingH3,
            )
            if (uiModel.isLoading) {
                ShimmerLoading()
            } else {
                AccountDetailComponent(
                    title = stringResource(R.string.home_credit_card_tiltle_total),
                    amount = stringResource(R.string.home_account_vnd, uiModel.total),
                    isHaveIcon = false,
                    isLargeSize = true,
                )
            }
        }

        StrokeLine()
        Column(
            Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding16),

        ) {
            if (uiModel.isLoading) {
                ShimmerLoading()
                ShimmerLoading()
            } else {
                AccountDetailComponent(
                    title = stringResource(R.string.home_credit_card_payment_date),
                    amount = uiModel.paymentDetail,
                    isHaveIcon = false,
                )

                Row(
                    horizontalArrangement = Arrangement.SpaceBetween,
                ) {
                    AccountDetailComponent(
                        title = stringResource(R.string.home_credit_card_payment_date),
                        amount = uiModel.paymentDate,
                        isHaveIcon = false,
                    )
                    Spacer(Modifier.weight(1f))
                    AccountDetailComponent(
                        title = stringResource(R.string.home_account_card_loan_amount),
                        amount = stringResource(R.string.home_account_vnd, uiModel.paymentTotal),
                        isHaveIcon = false,
                    )
                }
            }
        }
        StrokeLine()
        FooterInsight(
            modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
            guideText = stringResource(R.string.home_credit_insignt),
            buttonText = stringResource(R.string.home_button_text_insign),
            buttonAction = buttonAction,
        )
    }
}

@Composable
fun AccountDetailComponent(
    modifier: Modifier = Modifier,
    title: String,
    amount: String,
    isLargeSize: Boolean = false,
    isHaveIcon: Boolean = true,
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center,
    ) {
        Column(
            modifier.then(if (isHaveIcon) Modifier.weight(1f) else Modifier),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding8),
        ) {
            FoundationText(
                text = title,
                color = FDS.Colors.tabTextInactive,
                style = FDS.Typography.captionCaptionL,
            )
            FoundationText(
                text = amount,
                color = FDS.Colors.homeTextButton,
                style = FDS.Typography.headingH3,
            )
        }
        if (isHaveIcon) {
            Image(
                painter = painterResource(R.drawable.ic_home_account_arrow_right),
                contentDescription = "",
            )
        }
    }
}
