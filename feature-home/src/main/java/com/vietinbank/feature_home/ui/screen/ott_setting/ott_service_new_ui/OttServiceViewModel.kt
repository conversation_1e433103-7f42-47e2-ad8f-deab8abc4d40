package com.vietinbank.feature_home.ui.screen.ott_setting.ott_service_new_ui

import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.CustomEncryptedPrefs
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_domain.models.ott_feature.ListRegRequestParams
import com.vietinbank.core_domain.models.ott_feature.SmsOtpCreateRequestParams
import com.vietinbank.core_domain.models.ott_feature.SmsOtpVerifyRequestParams
import com.vietinbank.core_domain.repository.cache.IOttRegistrationRepository
import com.vietinbank.core_domain.usecase.ott_feature.OttFeatureUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.OneTimeEvent
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class OttServiceViewModel @Inject constructor(
    override val sessionManager: ISessionManager,
    private val prefs: CustomEncryptedPrefs,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    val ottRegistrationRepository: IOttRegistrationRepository,
    private val ottUseCase: OttFeatureUseCase,
) : BaseViewModel() {
    private val _uiState = MutableStateFlow(OttServiceUiState())
    val uiState: StateFlow<OttServiceUiState> = _uiState.asStateFlow()

    private var tempData: UserDataOtp = UserDataOtp()

    fun onAction(action: OttServiceAction) {
        when (action) {
            is OttServiceAction.OnCifNoChange -> {
                _uiState.update {
                    it.copy(
                        cifNo = action.value,
                    )
                }
            }

            is OttServiceAction.OnPhoneNumChange -> {
                _uiState.update {
                    it.copy(
                        phoneNum = action.value,
                    )
                }
            }

            is OttServiceAction.OnPickAccountType -> {
                _uiState.update {
                    it.copy(
                        accountType = ServiceAccountType.fromCodeType(action.value),
                    )
                }
            }

            is OttServiceAction.OnPickNotificationType -> {
                _uiState.update {
                    it.copy(
                        notificationType = NotificationType.fromAlertType(action.value),
                    )
                }
            }

            OttServiceAction.OnConfirmClick -> {
                if (tempData.isSameAs(_uiState.value)) {
                    sendEvent(OttServiceEvent.ShowOtpDialog)
                } else {
                    smsOtpCreate()
                }
            }
        }
    }

    private fun smsOtpCreate() {
        tempData = UserDataOtp(
            phoneNum = _uiState.value.phoneNum,
            cifNo = _uiState.value.cifNo,
            accountType = _uiState.value.accountType,
            notificationType = _uiState.value.notificationType,
        )

        launchJob(showLoading = true) {
            val res = ottUseCase.smsOtpCreate(
                SmsOtpCreateRequestParams(
                    accountNumber = "",
                    isVacct = if (_uiState.value.accountType == ServiceAccountType.PAYMENT) {
                        "N"
                    } else {
                        "Y"
                    },
                    phoneNo = _uiState.value.phoneNum,
                    roleId = "",
                    type = "",
                    username = userProf.getUserName() ?: "",
                    cifno = _uiState.value.cifNo,
                ),
            )
            handleResource(
                res,
                onSuccess = {
                    sendEvent(OttServiceEvent.ShowOtpDialog)
                },

            )
        }
    }

    fun smsOtpVerify(otp: String) {
        launchJob(showLoading = true) {
            val res = ottUseCase.smsOtpVerify(
                SmsOtpVerifyRequestParams(
                    isVacct = "",
                    otpNumber = otp,
                    phoneNo = _uiState.value.phoneNum,
                    roleId = "",
                    typeCheck = "1",
                    username = userProf.getUserName() ?: "",
                    cifno = _uiState.value.cifNo,
                ),
            )
            handleResourceSilent(
                res,
                onSuccess = { data ->
                    _uiState.update {
                        it.copy(
                            otpFailCount = 0,
                        )
                    }
                    listReg()
//                    sendEvent(OttServiceEvent.VerifyOtpSuccess)
                },
                onError = {
                    _uiState.update {
                        it.copy(
                            otpFailCount = it.otpFailCount++,
                        )
                    }
                    sendEvent(OttServiceEvent.VerifyOtpFail)
                },
            )
        }
    }

    private fun listReg() {
//        val cachedRegistrations = ottRegistrationRepository.getCachedRegistrations()
//        if (cachedRegistrations?.results?.isNotEmpty() == true) {
//            updateListWithPositions(cachedRegistrations.results)
//            return
//        }

        launchJob(showLoading = false) {
            val res = ottUseCase.listReg(
                ListRegRequestParams(
                    alertMethod = "SMS",
                    isVacct = if (_uiState.value.accountType == ServiceAccountType.PAYMENT) {
                        "N"
                    } else {
                        "Y"
                    },
                    mobileNumber = _uiState.value.phoneNum,
                    roleId = "",
                    tranId = "",
                    typeCheck = "Y",
                    type = "1",
                    username = userProf.getUserName().orEmpty(),
                    cifno = _uiState.value.cifNo,
                ),
            )

            handleResourceSilent(
                resource = res,
                onSuccess = { data ->
//                    Log.e("mata", "listReg: ${data.results}", )
                },
            )
        }
    }
}

data class UserDataOtp(
    val phoneNum: String = "",
    val cifNo: String = "",
    val accountType: ServiceAccountType = ServiceAccountType.NONE,
    val notificationType: NotificationType = NotificationType.NONE,
)

fun UserDataOtp.isSameAs(state: OttServiceUiState): Boolean {
    return phoneNum == state.phoneNum &&
        cifNo == state.cifNo &&
        accountType == state.accountType &&
        notificationType == state.notificationType
}

data class OttServiceUiState(
    val serviceType: OttServiceType = OttServiceType.SIGN_UP_SERVICE,
    val phoneNum: String = "",
    val cifNo: String = "",
    val accountType: ServiceAccountType = ServiceAccountType.NONE,
    val notificationType: NotificationType = NotificationType.NONE,
    var otpFailCount: Int = 0,
)

sealed class OttServiceEvent : OneTimeEvent {
    data object ShowOtpDialog : OneTimeEvent
    data object VerifyOtpSuccess : OneTimeEvent
    data object VerifyOtpFail : OneTimeEvent
}

sealed interface OttServiceAction {
    data object OnConfirmClick : OttServiceAction
    data class OnPickAccountType(val value: String) : OttServiceAction
    data class OnPickNotificationType(val value: String) : OttServiceAction
    data class OnCifNoChange(val value: String) : OttServiceAction
    data class OnPhoneNumChange(val value: String) : OttServiceAction
}