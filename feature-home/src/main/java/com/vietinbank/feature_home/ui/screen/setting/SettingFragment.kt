package com.vietinbank.feature_home.ui.screen.setting

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.base.dialog.UpdateEkycDialog
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_home.ui.screen.home.CheckUserEkycEvent
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class SettingFragment() : BaseFragment<SettingViewModel>() {
    override val viewModel: SettingViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        handleSingleEvent { event ->
            when (event) {
                CheckUserEkycEvent.NewEkyc -> { showNoticeDialog("Khách hàng cần thu thập sinh trắc học mới") }
                CheckUserEkycEvent.ShowGuildToCounter -> showGuideToCounterDialog()
                CheckUserEkycEvent.UpdateEkyc -> appNavigator.gotoUpdateEkyc()
                CheckUserEkycEvent.Nothing -> { showNoticeDialog("Đã thu thập sinh trắc học") }
                SettingEvent.AccountBalanceEvent -> appNavigator.goToAccountBalanceSetting(accountType = Tags.ACCOUNT_BALANCE_TYPE_CHANGE_SMS)
                SettingEvent.LoanAccountEvent -> appNavigator.goToAccountBalanceSetting(accountType = Tags.LOAN_ACCOUNT_TYPE)
                SettingEvent.TransactionStatusEvent -> appNavigator.goToTransactionStatus()
                SettingEvent.NavigateBackEvent -> appNavigator.popBackStack()
                SettingEvent.LogOutEvent -> showLogOutDialog()
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.settingUiState.collectAsStateWithLifecycle()
        AppTheme {
            SettingScreen(
                uiState = uiState,
                onAction = viewModel::onAction,
            )
        }
    }

    private fun showGuideToCounterDialog() {
        val dialog = UpdateEkycDialog.Companion.newInstance(
            title = "Thông báo",
            message = "Quý khách vui lòng thực hiện thu thập và đối chiếu sinh trắc học tai các Chi nhánh/ Phòng giao dịch VietinBank gần nhất",
            buttonText = "Đóng",
            showImage = false,
        )
        dialog.setOnConfirmListener {
            dialog.dismiss()
        }
        dialog.show(childFragmentManager, "GuideToCounterDialog")
    }
    private fun showLogOutDialog() {
        showConfirmDialog(
            message = "Bạn có chắc muốn logout?",
            positiveAction = {
                // Stop InactivityManager monitoring khi logout
                inactivityManager.stopMonitoring()
                viewModel.clearSession()
                viewModel.transferCacheManager.clearTransferALL()
                viewModel.ottRegistrationRepository.clearAllOttCache()
                appNavigator.goToHomePreLoginAndPopAll()
            },
            negativeAction = {
                // user cancel => do nothing
            },
        )
    }
}