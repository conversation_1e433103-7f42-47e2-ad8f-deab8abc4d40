package com.vietinbank.feature_home.ui.screen.confirm_sms_transform

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.base.compose.BaseAppBar
import com.vietinbank.core_ui.base.compose.BaseButton
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_home.R

@Composable
fun ConfirmSmsTransformScreen(
    uiState: ConfirmSmsUiState,
    onAction: (ConfirmSmsAction) -> Unit,
) {
    val selectedOptions = remember { mutableStateListOf<String>() }
    var unRegExpand by remember { mutableStateOf(true) }
    var regExpand by remember { mutableStateOf(true) }
    Column(
        modifier = Modifier.fillMaxSize(),
    ) {
        BaseAppBar(
            title = "Cài đặt thông báo",
            onBackClick = {
                onAction(ConfirmSmsAction.OnClickNavigateBack)
            },
        )
        Column(
            modifier = Modifier
                .padding(top = 20.dp)
                .padding(horizontal = 16.dp)
                .weight(1f),
        ) {
            Column(
                modifier = Modifier
                    .verticalScroll(rememberScrollState())
                    .padding(bottom = 20.dp),
                verticalArrangement = Arrangement.spacedBy(20.dp),
            ) {
                BaseText(
                    text = "Quý khách đang thực hiện chuyển hình thức đăng ký nhận tin BĐSD từ ứng dụng eFAST qua tin nhắn SMS",
                    color = Color(0xFFD6F4FF),
                )

                BaseText(
                    text = uiState.corpName,
                    color = Color(0xFFD6F4FF),
                )
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(horizontal = 12.dp, vertical = 16.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp),
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                    ) {
                        BaseText(
                            "Chọn tài khoản đăng ký nhận BĐSD qua tin nhắn",
                            modifier = Modifier.weight(1f),
                        )
                        Image(
                            painter = painterResource(R.drawable.ic_menu_down),
                            contentDescription = "",
                            modifier = Modifier.safeClickable {
                                unRegExpand = !unRegExpand
                            },
                        )
                    }
                    AnimatedVisibility(visible = unRegExpand) {
                        Column {
                            AccountItem(
                                title = "Chọn tất cả",
                                isShowRadioButton = true,
                                selected = selectedOptions.contains("ALL"),
                                onClick = {
                                    if (selectedOptions.contains("ALL")) {
                                        selectedOptions.removeAll(selectedOptions)
                                    } else {
                                        selectedOptions.add("ALL")
                                        selectedOptions.addAll(
                                            uiState.listUnReg.map {
                                                it.accountNumber ?: ""
                                            },
                                        )
                                    }
                                    onAction(ConfirmSmsAction.OnClickSelected(selectedOptions))
                                },
                            )
                            uiState.listUnReg.forEach { option ->
                                AccountItem(
                                    title = option.accountNumber ?: "",
                                    content = option.feeAmount + " VND",
                                    isShowRadioButton = true,
                                    selected = selectedOptions.contains(option.accountNumber ?: ""),
                                    onClick = {
                                        if (selectedOptions.contains(it)) {
                                            selectedOptions.remove(it)
                                        } else {
                                            selectedOptions.add(it)
                                        }
                                        onAction(ConfirmSmsAction.OnClickSelected(selectedOptions))
                                    },
                                )
                            }
                        }
                    }
                }

                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(horizontal = 12.dp, vertical = 16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp),
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                    ) {
                        BaseText(
                            "Danh sách tài khoản đã đăng ký nhận BĐSD qua SMS",
                            modifier = Modifier.weight(1f),
                        )
                        Image(
                            painter = painterResource(R.drawable.ic_menu_down),
                            contentDescription = "",
                            modifier = Modifier.safeClickable {
                                regExpand = !regExpand
                            },
                        )
                    }
                    AnimatedVisibility(visible = regExpand) {
                        Column {
                            uiState.listReg.forEach {
                                AccountItem(
                                    title = it.accountNumber ?: "",
                                    content = it.feeAmount + " VND",
                                    isShowRadioButton = false,
                                    onClick = {},
                                )
                            }
                        }
                    }
                }

                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(horizontal = 12.dp, vertical = 16.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp),
                ) {
                    Row {
                        BaseText(
                            text = "Tổng phí dịch vụ/tháng",
                            modifier = Modifier.weight(1f),
                            fontCus = 2,
                            color = Color(0xFF0A5994),
                        )
                        BaseText(
                            text = uiState.totalFee + " VND",
                            fontCus = 2,
                            color = Color.Red,
                        )
                    }
                    BaseText(
                        text = "Số điện thoại nhận SMS BĐSD: ${uiState.phoneNum}",
                    )
                    BaseText(
                        text = "Phí dịch vụ được thu hàng tháng vào ngày đăng ký sử dụng, trừ trường hợp KH được hưởng chính sách miễn phí",
                    )
                }
            }
        }

        Column(
            modifier = Modifier
                .clip(RoundedCornerShape(topEnd = 4.dp, topStart = 4.dp))
                .background(Color.White)
                .padding(horizontal = 16.dp, vertical = 8.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp),
        ) {
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
            ) {
                CustomCheckBox(
                    checked = uiState.selectedValidate,
                    onCheckedChange = {
                        onAction(ConfirmSmsAction.OnClickValidate)
                    },
                )
                BaseText(
                    text = "Tôi đồng ý thanh toán phí dịch vụ và các Điều khoản điều kiện hiện hành của VietinBank",
                )
            }
            BaseButton(
                modifier = Modifier.safeClickable {
                    onAction(ConfirmSmsAction.OnClickConfirm(selectedOptions))
                },
                text = "Tiếp tục",
                isCancelButton = !uiState.selectedValidate,
            )
        }
    }
}

@Composable
fun AccountItem(
    title: String,
    content: String? = null,
    isShowRadioButton: Boolean = false,
    selected: Boolean = false,
    onClick: (String) -> Unit,
) {
    Row(
        modifier = Modifier
            .padding(vertical = 12.dp),
        horizontalArrangement = Arrangement.spacedBy(20.dp),
    ) {
        if (isShowRadioButton) {
            MultiRadioStyleCheckBoxList(
                selected = selected,
                onClick = {
                    onClick(title)
                },
            )
        }
        Column(
            modifier = Modifier
                .safeClickable {
                    onClick(title)
                },
            verticalArrangement = Arrangement.spacedBy(8.dp),
        ) {
            BaseText(
                text = title,
                color = Color(0xFF5C6674),
            )
            if (!content.isNullOrEmpty()) {
                BaseText(
                    text = content,
                    color = Color(0xFFA2AAB5),
                )
            }
        }
    }
}

@Composable
fun CustomCheckBox(
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
) {
    Box(
        modifier = Modifier
            .size(24.dp)
            .clip(RoundedCornerShape(4.dp))
            .background(if (checked) Color(0xFF245E9E) else Color.LightGray)
            .safeClickable { onCheckedChange(!checked) },
        contentAlignment = Alignment.Center,
    ) {
        if (checked) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = "Checked",
                tint = Color.White,
                modifier = Modifier.size(16.dp),
            )
        }
    }
}

@Composable
fun MultiRadioStyleCheckBoxList(
    selected: Boolean = false,
    onClick: () -> Unit,
) {
    Column {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .clip(CircleShape)
                .clickable {
                    onClick()
                },
        ) {
            // Custom circle (like radio)
            Box(
                modifier = Modifier
                    .size(20.dp)
                    .clip(CircleShape)
                    .background(
                        if (selected) {
                            AppColors.switchCheckedTrack
                        } else {
                            AppColors.switchUncheckedThumb
                        },
                    )
                    .border(
                        4.dp,
                        AppColors.switchUncheckedThumb,
                        CircleShape,
                    ),
            )
        }
    }
}
