package com.vietinbank.feature_home.ui.screen.home

import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.livedata.SingleLiveEvent
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.CustomEncryptedPrefs
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.UserInfoDataHolder
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.checker.CountPendingDomain
import com.vietinbank.core_domain.models.checker.CountPendingParams
import com.vietinbank.core_domain.models.checker.GetTransactionListDomain
import com.vietinbank.core_domain.models.checker.GetTransactionListParams
import com.vietinbank.core_domain.models.checker.MobileConfigLastParams
import com.vietinbank.core_domain.models.checker.SubTranTypeListDomain
import com.vietinbank.core_domain.models.checker.TransactionItemUiModel
import com.vietinbank.core_domain.models.home.CheckUserEkycParams
import com.vietinbank.core_domain.models.home.GetBiometricFaceParams
import com.vietinbank.core_domain.models.home.RegisterTouchIDDomain
import com.vietinbank.core_domain.models.home.RegisterTouchIDParams
import com.vietinbank.core_domain.models.ott_feature.ListRegDomains
import com.vietinbank.core_domain.models.ott_feature.ListRegRequestParams
import com.vietinbank.core_domain.ott.IOttManager
import com.vietinbank.core_domain.repository.cache.IOttRegistrationRepository
import com.vietinbank.core_domain.repository.cache.ITransferCacheManager
import com.vietinbank.core_domain.usecase.home.HomeUseCase
import com.vietinbank.core_domain.usecase.ott_feature.OttFeatureUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.OneTimeEvent
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import javax.inject.Inject
import kotlin.io.encoding.Base64
import kotlin.io.encoding.ExperimentalEncodingApi

@HiltViewModel
class HomeViewModel @Inject constructor(
    private val homeUseCase: HomeUseCase,
    override val sessionManager: ISessionManager,
    private val prefs: CustomEncryptedPrefs,
    val ottManager: IOttManager,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    val ottUseCase: OttFeatureUseCase,
    val ottRegistrationRepository: IOttRegistrationRepository,
    val transferCacheManager: ITransferCacheManager,
) : BaseViewModel() {
    fun clearSession() = sessionManager.clearSession()

    // load config soft
    private var isKeepLoadConfigSoft = false
    fun isKeepLoadConfigSoft() = isKeepLoadConfigSoft
    fun setKeepLoadConfigSoft(isLoad: Boolean) {
        isKeepLoadConfigSoft = isLoad
    }

    fun isChecker() = userProf.isChecker()
    fun getKeypassProfile() = userProf.getKeypassProfile()
    fun isTimerConfig() = appConfig.isTimerConfig()

    // Event để hiển thị thông báo lỗi
    val showErrorDialog = SingleLiveEvent<String>()

    // StateFlow để theo dõi giá trị isChecker
    val isChecker: StateFlow<Boolean> = userProf.userProfFlow.map { userProf.isChecker() }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = false,
    )

    // StateFlow cho UI state trong HomeViewModel
    private val _uiState = MutableStateFlow(HomeUiState())
    val uiState = _uiState.asStateFlow()

    // Cập nhật UI state khi có dữ liệu
    fun updateUiState(update: (HomeUiState) -> HomeUiState) {
        _uiState.value = update(_uiState.value)
    }

    private val _errorEvent = MutableSharedFlow<String>()
    val errorEvent = _errorEvent.asSharedFlow()

    private val _getTransactionList = MutableSharedFlow<Resource<GetTransactionListDomain>>()
    val getTransactionList = _getTransactionList.asSharedFlow()

    private val _registerTouchIDState = MutableSharedFlow<Resource<RegisterTouchIDDomain>>()
    val registerTouchIDState = _registerTouchIDState.asSharedFlow()

    private val _countPending = MutableSharedFlow<Resource<CountPendingDomain>>()
    val countPending = _countPending.asSharedFlow()

    private val _checkUserEkyc = MutableSharedFlow<Boolean>()
    val checkUserEkyc = _checkUserEkyc.asSharedFlow()

    private val _checkEkycEvent = Channel<CheckUserEkycEvent>(Channel.BUFFERED)
    val checkEkycEvent = _checkEkycEvent.receiveAsFlow()

    private val _listRegState = SingleLiveEvent<ListRegDomains>()
    val listRegState: SingleLiveEvent<ListRegDomains> get() = _listRegState

    private var retailData: String? = null
    private var nationData: String? = null

    var currentGroupType: String? = null
    var currentTransType: String? = null
    var currentServiceType: String? = null

    private var hasInitialSyncCompleted = false

    fun showError(message: String) {
        viewModelScope.launch {
            _errorEvent.emit(message)
        }
    }

    init {
        viewModelScope.launch {
            userProf.userProfFlow.collect {
                updateUiState { state ->
                    state.copy(
                        isChecker = userProf.isChecker(),
                        fullName = userProf.getFullName().toString(),
                    )
                }
            }
        }

        // Observe OTT state and sync messages when initialized
        viewModelScope.launch {
            ottManager.ottState.collect { state ->
                if (state == IOttManager.OttState.Initialized && !hasInitialSyncCompleted) {
                    hasInitialSyncCompleted = true

                    // Check if notification processing is in progress
                    if (ottManager.isProcessingNotification()) {
                        printLog("HomeViewModel: Notification processing in progress, delaying sync...")
                        // Wait for notification processing to complete
                        var retries = 0
                        while (ottManager.isProcessingNotification() && retries < 30) { // Max 3 seconds
                            delay(100)
                            retries++
                        }
                    }

                    // Sync messages from server if not processing notification
                    if (!ottManager.isProcessingNotification()) {
                        printLog("HomeViewModel: Starting OTT sync...")
                        syncOttMessages(true)
                    } else {
                        printLog("HomeViewModel: Skipping sync, notification still processing")
                    }
                }
            }
        }
    }

    fun checkUserEkyc() {
        prefs.setBoolean(Tags.CHECKEKYC, false)
        launchJobSilent {
            val res = homeUseCase.checkUserEkyc(
                CheckUserEkycParams(
                    userProf.getUserName().toString(),
                ),
            )
            printLog("ekyc checkUserEkyc: $res")
            handleResourceSilent(
                resource = res,
                onSuccess = { data ->
                    _checkUserEkyc.emit(data.bioStat == "0" && data.statusAccountHolder == "1")
                    getBiometricFace(data.biometricFace ?: "")
                    retailData = data.retail
                    nationData = data.national
                },
            )
        }
    }

    @OptIn(ExperimentalEncodingApi::class)
    private fun getBiometricFace(biometricFace: String) {
        launchJobSilent {
            val res = homeUseCase.getBiometricFace(
                GetBiometricFaceParams(
                    biometricId = biometricFace,
                    username = userProf.getUserName().toString(),
                    roleId = "",
                ),
            )
            printLog("ekyc getBiometricFace: $res")
            handleResourceSilent(
                resource = res,
                onSuccess = { data ->
                    viewModelScope.launch(Dispatchers.Default) {
                        val biometricImage = vmScope.async {
                            Base64.Default.decode(data.image ?: "")
                        }
                        UserInfoDataHolder.saveBiometricImage(
                            biometricImage.await(),
                        )
                    }
                },
            )
        }
    }

    // check ekyc cua user khi click "Dong y"
    fun checkEkycEvent() {
        val checkUserEkycEvent = when {
            retailData == "0" && nationData == "VN" -> CheckUserEkycEvent.NewEkyc
            retailData == "0" && nationData != "VN" -> CheckUserEkycEvent.ShowGuildToCounter
            retailData == "1" -> CheckUserEkycEvent.UpdateEkyc
            else -> CheckUserEkycEvent.Nothing
        }
        viewModelScope.launch {
            _checkEkycEvent.send(checkUserEkycEvent)
        }
        retailData = null
        nationData = null
    }

    fun checkShowPopupEkyc(): Boolean = prefs.getBoolean(Tags.CHECKEKYC)

    fun registerTouchID(password: String) {
        launchJob(showLoading = true) {
            val res = homeUseCase.registerTouchID(
                RegisterTouchIDParams(
                    userProf.getUserName().toString(),
                    password,
                    userProf.getCifNo().toString(),
                ),
            )
            handleResource(res) { data ->
                appConfig.updateFingerID(data.touchIDToken)
                _registerTouchIDState.emit(Resource.Success(data))
            }
        }
    }

    fun getTransactionList(trans: SubTranTypeListDomain) {
        currentTransType = trans.tranType
        currentGroupType = trans.groupType
        currentServiceType = trans.servicetype
        launchJob(showLoading = true) {
            val res = homeUseCase.getTranList(
                GetTransactionListParams(
                    cifNo = userProf.getCifNo().toString(),
                    username = userProf.getUserName().toString(),
                    groupType = getSafeGroupType(trans.tranType, trans.groupType),
                    serviceType = trans.servicetype,
                    tranType = getSafeTranType(trans.tranType),
                    pageNum = "0",
                    pageSize = "15",
                    orderByAmount = "0", // 1 tăng dần, -1 giảm dần, 0 là không sắp xếp
                    orderByApproveDate = "-1", // 1 tăng dần, -1 giảm dần, 0 là không sắp xếp
                ),
            )
            handleResource(res) { data ->
                printLog("transaction: ${data.transactions?.size}")
                _getTransactionList.emit(Resource.Success(data))
            }
        }
    }

    private fun getSafeGroupType(tranType: String?, groupType: String?): String {
        return if (tranType != "btax") groupType.orEmpty() else ""
    }

    private fun getSafeTranType(tranType: String?): String {
        return when (tranType) {
            "btax", "fx" -> ""
            else -> tranType.orEmpty()
        }
    }

    fun countPending() {
        launchJob(showLoading = true) {
            val res = homeUseCase.countPending(
                CountPendingParams(
                    cifNo = userProf.getCifNo().toString(),
                    userName = userProf.getUserName().toString(),
                    role = userProf.getRoleId() ?: "",
                ),
            )
            handleResource(res) { data ->
                // Cập nhật pending transactions trong state
                updateUiState { currentState ->
                    currentState.copy(
                        pendingTransactions = data.countTransactionList ?: emptyList(),
                    )
                }
                _countPending.emit(Resource.Success(data))
            }
        }
    }

    fun listReg() {
        if (ottRegistrationRepository.getCachedRegistrations() != null && ottRegistrationRepository.getCachedRegistrations()?.results?.isNotEmpty() == true) {
            return
        }
        launchJob(showLoading = false) {
            val params = ListRegRequestParams(
                alertMethod = "ALL",
                isVacct = "",
                mobileNumber = userProf.getPhoneNo() ?: "",
                roleId = "",
                tranId = "",
                typeCheck = "Y",
                username = userProf.getUserName() ?: "",
            )
            val res = ottUseCase.listReg(
                params,
            )
            handleResourceSilent(
                resource = res,
                onSuccess = { data ->
                    viewModelScope.launch(Dispatchers.Default) {
                        ottRegistrationRepository.saveRegistrations(data)
                        if (checkConditionShowAlertRegOTT(data) && ottRegistrationRepository.getCahceCountTimeShowRegisterOTT() <= 5) {
                            ottRegistrationRepository.incrementCountTimeShowRegisterOTT()
                            _listRegState.postValue(data)
                        }
                    }
                },
            )
        }
    }

    // Call API check hết phiên client
    fun getMobileConfigLast() {
        launchJobSilent {
            val res = homeUseCase.mobileConfigLast(
                MobileConfigLastParams(
                    username = userProf.getUserName() ?: "",
                    lastModified = if (appConfig.getTotalLastModified() == null) {
                        "0"
                    } else {
                        appConfig.getTotalLastModified()
                            .toString()
                    },
                ),
            )
            handleResourceSilent(
                resource = res,
                onSuccess = { data ->
                    // set lại giá trị để chỉ gọi 1 lần API khi login vào
                    appConfig.setTimerConfig(false)
                    // Nếu configList rỗng thì giữ nguyên như cũ không thay đổi
                    if (!data.configList.isNullOrEmpty()) {
                        appConfig.updateTotalLastModified(data.totalLastModified!!)
                        // Lưu lại list configList để sau dùng
                        prefs.setString(Tags.TIMECONFINGLIST, Utils.g().provideGson().toJson(data.configList))
                    }
                },
            )
        }
    }

    private fun checkConditionShowAlertRegOTT(data: ListRegDomains): Boolean {
        val results = data.results
        if (results.isEmpty()) return false

        // Check if already registered for OTT (159)
        if (results.any { it.alertType == "159" }) return false

        // Check if has SMS registration type
        return results.any { it.alertType in setOf("189", "89", "169") }
    }
}

sealed class CheckUserEkycEvent : OneTimeEvent {
    data object UpdateEkyc : CheckUserEkycEvent()
    data object NewEkyc : CheckUserEkycEvent()
    data object ShowGuildToCounter : CheckUserEkycEvent()
    data object Nothing : CheckUserEkycEvent()
}

data class HomeUiState(
    val isChecker: Boolean = false,
    val fullName: String = "",
    val pendingTransactions: List<SubTranTypeListDomain> = emptyList(),
    val isLoading: Boolean = false,
    val error: String? = null,
)

sealed class TransactionListUIState {
    object Loading : TransactionListUIState()
    data class Success(val transactions: List<TransactionItemUiModel>) : TransactionListUIState()
    data class Error(val message: String) : TransactionListUIState()
}