package com.vietinbank.feature_home.ui.screen.home

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.TileMode
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_domain.models.home.BottomNavigationActions
import com.vietinbank.core_domain.models.home.HomeFeatureActions
import com.vietinbank.core_domain.models.home.HomeOtherActions
import com.vietinbank.core_domain.models.home.TuNA5Actions
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_home.R
import com.vietinbank.feature_home.ui.view.TransactionListView

@Composable
fun HomeScreen(
    featureActions: HomeFeatureActions,
    bottomNavActions: BottomNavigationActions,
    otherActions: HomeOtherActions,
    uiState: HomeUiState,
    tuNA5Actions: TuNA5Actions? = null,
    onClickAvatar: () -> Unit,
) {
    // State cho tab được chọn
    var selectedTabIndex by remember { mutableIntStateOf(0) }

    // Scrollable content
    Box(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding()
            .imePadding(),
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .verticalScroll(rememberScrollState()),
        ) {
            // Top app bar với logo và nút đăng nhập
            HomeTopBar(otherActions.onNotifyClick)

            // Khoảng cách từ top
            Spacer(modifier = Modifier.height(16.dp))

            // User info card
            UserInfoCard(
                fullName = uiState.fullName,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                onClickAvatar = { onClickAvatar() },
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Tab navigation (Tài khoản / Giao dịch)
            AccountTransactionTabs(
                selectedTabIndex = selectedTabIndex,
                onTabSelected = { selectedTabIndex = it },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
            )

            Spacer(modifier = Modifier.height(24.dp))

            // Chức năng yêu thích section
            FavoriteFeatures(
                onBhxhClick = featureActions.onBhxhClick,
                onTransferClick = featureActions.onTransferClick,
                onTaxClick = featureActions.onTaxClick,
                onForexClick = featureActions.onForexClick,
                onInquiryClick = featureActions.onInquiryClick,
                onPaymentOrderClick = featureActions.onPaymentOrderClick,
                isChecker = uiState.isChecker,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
            )

            Spacer(modifier = Modifier.height(24.dp))

            // Giao dịch tạo mới nhất
            BannerSection(
                title = "Giao dịch tạo mới nhất",
                onClick = featureActions.onLatestTransactionClick,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
            )

            Spacer(modifier = Modifier.height(24.dp))

            // Ưu đãi
            BannerSection(
                title = "Ưu đãi",
                onClick = featureActions.onOfferClick,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
            )

            if (uiState.isChecker && uiState.pendingTransactions.isNotEmpty()) {
                Spacer(modifier = Modifier.height(24.dp))
                TransactionListView(
                    transactions = uiState.pendingTransactions,
                    onItemClick = { transaction ->
                        otherActions.onTransactionItemClick(transaction)
                    },
                )
            }

            // Fix cứng nút điều hướng đến các chức năng của sprint 2 - tuna5
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
            ) {
                Text(
                    text = "Soft OTP",
                    style = getComposeFont(2, 14.sp, Color.White),
                    modifier = Modifier
                        .weight(1f)
                        .safeClickable { tuNA5Actions?.onSoftClick?.invoke() },
                )
                Text(
                    text = "Keypass",
                    style = getComposeFont(2, 14.sp, Color.White),
                    modifier = Modifier
                        .weight(1f)
                        .safeClickable { tuNA5Actions?.onKeypassClick?.invoke() },
                )
                Text(
                    text = "Quản lý giao dịch eFAST",
                    style = getComposeFont(2, 14.sp, Color.White),
                    modifier = Modifier
                        .weight(1f)
                        .safeClickable { tuNA5Actions?.onManageTransaction?.invoke() },
                )

                Text(
                    text = "Truy vấn tài khoản",
                    style = getComposeFont(2, 14.sp, Color.White),
                    modifier = Modifier
                        .weight(1f)
                        .safeClickable { tuNA5Actions?.onInquiryClick?.invoke() },
                )
            }
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
            ) {
                Text(
                    text = "Đăng ký SmartCA",
                    style = getComposeFont(2, 14.sp, Color.White),
                    modifier = Modifier
                        .padding(vertical = 10.dp)
                        .safeClickable { tuNA5Actions?.onSmartCAClick?.invoke() },
                )
                Text(
                    text = "Đăng ký OTT",
                    style = getComposeFont(2, 14.sp, Color.White),
                    modifier = Modifier
                        .padding(vertical = 10.dp)
                        .safeClickable { tuNA5Actions?.onRegiterOttClick?.invoke() },
                )
                Text(
                    text = "Đăng ký STH",
                    style = getComposeFont(2, 14.sp, Color.White),
                    modifier = Modifier
                        .padding(vertical = 10.dp)
                        .safeClickable { tuNA5Actions?.onRegisterEKYC?.invoke() },
                )
            }

            // Spacer chiếm phần còn lại để đẩy bottom bar xuống dưới
            Spacer(modifier = Modifier.height(80.dp))
        }

        // Bottom Navigation Bar
        BottomNavigationBar(
            onHomeClick = bottomNavActions.onHomeClick,
            onNotificationClick = bottomNavActions.onNotificationClick,
            onReportClick = bottomNavActions.onReportClick,
            onMarketClick = bottomNavActions.onMarketClick,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth(),
        )
    }
}

@Composable
fun HomeTopBar(
    onNotifyClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // Logo VietinBank eFAST
        Row(
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_logofull),
                contentDescription = "VietinBank Logo",
                modifier = Modifier.width(140.dp),
            )
        }

//        // Login button
//        Box(
//            modifier = Modifier
//                .clip(RoundedCornerShape(20.dp))
//                .background(AppColors.primary.copy(alpha = 0.1f))
//                .padding(horizontal = 12.dp, vertical = 6.dp)
//                .safeClickable { onNotifyClick() },
//            contentAlignment = Alignment.Center
//        ) {
//            BaseText(
//                text = "Đăng nhập",
//                color = AppColors.textPrimary,
//                textSize = AppSizer.Text.text14,
//                fontCus = 0
//            )
//        }
    }
}

@Composable
fun UserInfoCard(
    fullName: String,
    modifier: Modifier = Modifier,
    onClickAvatar: () -> Unit = {},
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .border(
                width = 1.4.dp,
                brush = Brush.linearGradient(
                    colors = listOf(
                        Color(0xFF8CAEFF), // #8CAEFF ở 20.64%
                        Color(0xFFF43F72), // #F43F72 ở 56.17%
                        Color(0xFF7AA3D5), // #7AA3D5 ở 90.43%
                    ),
                    start = Offset(0f, 0f),
                    end = Offset(0.7f, 1.2f), // Điều chỉnh để tạo góc 230.68 độ
                    tileMode = TileMode.Clamp,
                ),
                shape = RoundedCornerShape(10.dp),
            )
            .clip(RoundedCornerShape(10.dp))
            .background(
                color = AppColors.cardBackground,
            )
            .padding(16.dp),
    ) {
        Row(
            modifier = Modifier.wrapContentSize(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween,
        ) {
            // Avatar circle
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .clip(CircleShape)
                    .background(AppColors.surface)
                    .safeClickable {
                        onClickAvatar()
                    },
                contentAlignment = Alignment.Center,
            ) {
                // Ảnh mask có background trong suốt
                Image(
                    painter = painterResource(R.drawable.ic_mask),
                    contentDescription = "Avatar mask",
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Crop,
                )

                // Icon person bên trong, cách viền 1dp
                Box(
                    modifier = Modifier
                        .size(46.dp)
                        .padding(1.dp)
                        .clip(CircleShape)
                        .background(AppColors.inputBackground)
                        .align(Alignment.Center),
                    contentAlignment = Alignment.Center,
                ) {
                    Image(
                        painter = painterResource(R.drawable.ic_person),
                        contentDescription = "Person icon",
                        modifier = Modifier.wrapContentSize(),
                    )
                }
            }

            Spacer(modifier = Modifier.width(12.dp))
            Column {
                BaseText(
                    text = "Xin chào",
                    color = AppColors.textSecondary,
                    fontCus = 0,
                    textSize = 14.sp,
                )
                Spacer(modifier = Modifier.height(2.dp))
                BaseText(
                    text = fullName,
                    color = AppColors.textPrimary,
                    fontCus = 5, // bold
                    textSize = 16.sp,
                )
            }
        }
    }
}

@Composable
fun AccountTransactionTabs(
    selectedTabIndex: Int,
    onTabSelected: (Int) -> Unit,
    modifier: Modifier = Modifier,
) {
    val tabs = listOf("Tài khoản", "Giao dịch")

    TabRow(
        selectedTabIndex = selectedTabIndex,
        modifier = modifier
            .clip(RoundedCornerShape(AppSizer.Radius.radius10))
            .background(AppColors.surface),
        indicator = { },
        divider = { },
    ) {
        tabs.forEachIndexed { index, title ->
            Tab(
                selected = selectedTabIndex == index,
                onClick = { onTabSelected(index) },
                modifier = Modifier
                    .padding(4.dp)
                    .clip(RoundedCornerShape(AppSizer.Radius.radius10))
                    .background(
                        if (selectedTabIndex == index) {
                            AppColors.primary
                        } else {
                            AppColors.surface
                        },
                    )
                    .padding(vertical = 8.dp),
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center,
                ) {
                    if (index == 0) {
                        Image(
                            painter = painterResource(id = R.drawable.ic_person),
                            contentDescription = "Account Icon",
                            modifier = Modifier.size(24.dp),
                        )
                    } else {
                        Image(
                            painter = painterResource(id = R.drawable.ic_transaction),
                            contentDescription = "Transaction Icon",
                            modifier = Modifier.size(24.dp),
                        )
                    }

                    Spacer(modifier = Modifier.width(4.dp))

                    BaseText(
                        text = title,
                        color = if (selectedTabIndex == index) {
                            Color.White
                        } else {
                            AppColors.textPrimary
                        },
                        textSize = 14.sp,
                        fontCus = 0,
                    )
                }
            }
        }
    }
}

@Composable
fun FavoriteFeatures(
    onBhxhClick: () -> Unit,
    onTransferClick: () -> Unit,
    onTaxClick: () -> Unit,
    onForexClick: () -> Unit,
    onInquiryClick: () -> Unit, // maker
    onPaymentOrderClick: () -> Unit, // maker
    modifier: Modifier = Modifier,
    isChecker: Boolean = false,
) {
    Column(modifier = modifier) {
        SectionTitle(title = "Chức năng yêu thích")

        Spacer(modifier = Modifier.height(8.dp))

        // Row 1: BHXH & Chuyển tiền
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
        ) {
            if (isChecker) {
            } else {
                FeatureItem(
                    icon = R.drawable.ic_insurance, // Đã điền ID icon BHXH
                    title = "Nộp BHXH",
                    onClick = onBhxhClick,
                    modifier = Modifier.weight(1f),
                )

                FeatureItem(
                    icon = R.drawable.ic_transfer, // Đã điền ID icon chuyển tiền
                    title = "Chuyển tiền",
                    onClick = onTransferClick,
                    modifier = Modifier.weight(1f),
                )
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // Row 2: Nộp ngân sách & Chuyển tiền ngoại tệ
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
        ) {
            if (isChecker) {
            } else {
                FeatureItem(
                    icon = R.drawable.ic_nsnn, // Đã điền ID icon nộp ngân sách
                    title = "Tra soát",
                    onClick = onInquiryClick,
                    modifier = Modifier.weight(1f),
                )

                FeatureItem(
                    icon = R.drawable.ic_ngoaite, // Đã điền ID icon chuyển tiền ngoại tệ
                    title = "Lệnh chi",
                    onClick = onPaymentOrderClick,
                    modifier = Modifier.weight(1f),
                )
            }
        }
    }
}

@Composable
fun FeatureItem(
    icon: Int,
    title: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .height(70.dp) // Tăng chiều cao để đảm bảo text 2 dòng không bị vỡ
            .clip(RoundedCornerShape(AppSizer.Radius.radius10))
            .background(AppColors.itemBackground)
            .border(
                width = AppSizer.Stroke.stroke05,
                color = AppColors.borderColor,
                shape = RoundedCornerShape(AppSizer.Radius.radius10),
            )
            .safeClickable { onClick() }
            .padding(8.dp),
        contentAlignment = Alignment.CenterStart,
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Image(
                painter = painterResource(id = icon),
                contentDescription = title,
                modifier = Modifier.size(24.dp),
            )

            Spacer(modifier = Modifier.width(8.dp))

            BaseText(
                text = title,
                textSize = 14.sp,
                color = AppColors.textPrimary,
                maxLines = 2,
                fontCus = 0,
            )
        }
    }
}

@Composable
fun SectionTitle(
    title: String,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Box(
            modifier = Modifier
                .width(3.dp)
                .height(16.dp)
                .background(AppColors.redButton),
        )

        Spacer(modifier = Modifier.width(8.dp))

        BaseText(
            text = title,
            textSize = 16.sp,
            fontCus = 5, // Bold font
            color = AppColors.textPrimary,
        )
    }
}

@Composable
fun BannerSection(
    title: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier) {
        SectionTitle(title = title)

        Spacer(modifier = Modifier.height(8.dp))

        // Banner placeholder
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(100.dp)
                .clip(RoundedCornerShape(AppSizer.Radius.radius10))
                .background(AppColors.surface)
                .safeClickable { onClick() },
            contentAlignment = Alignment.Center,
        ) {
            BaseText(
                text = "Banner",
                textSize = 16.sp,
                color = AppColors.textSecondary,
                fontCus = 0,
            )
        }
    }
}

@Composable
fun BottomNavigationBar(
    onHomeClick: () -> Unit,
    onNotificationClick: () -> Unit,
    onReportClick: () -> Unit,
    onMarketClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .wrapContentHeight()
            .background(Color.White)
            .border(
                width = 0.5.dp,
                color = AppColors.borderColor,
                shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
            )
            .padding(vertical = 8.dp),
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight(),
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            BottomNavItem(
                icon = R.drawable.ic_home,
                title = "Trang chủ",
                isSelected = true,
                onClick = onHomeClick,
            )

            BottomNavItem(
                icon = R.drawable.ic_mail,
                title = "Thông báo",
                isSelected = false,
                onClick = onNotificationClick,
            )

            BottomNavItem(
                icon = R.drawable.ic_report,
                title = "Báo cáo",
                isSelected = false,
                onClick = onReportClick,
            )

            BottomNavItem(
                icon = R.drawable.ic_market_bottom,
                title = "Thị trường",
                isSelected = false,
                onClick = onMarketClick,
            )
        }
    }
}

@Composable
fun BottomNavItem(
    icon: Int,
    title: String,
    isSelected: Boolean,
    onClick: () -> Unit,
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
        modifier = Modifier
            .wrapContentSize()
            .navigationBarsPadding()
            .safeClickable { onClick() }
            .padding(horizontal = 8.dp),
    ) {
        Image(
            painter = painterResource(id = icon),
            contentDescription = title,
            modifier = Modifier.size(24.dp),
        )

        Spacer(modifier = Modifier.height(4.dp))

        BaseText(
            text = title,
            textSize = 12.sp,
            color = if (isSelected) {
                AppColors.primary
            } else {
                AppColors.textSecondary
            },
            fontCus = 0,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
        )
    }
}