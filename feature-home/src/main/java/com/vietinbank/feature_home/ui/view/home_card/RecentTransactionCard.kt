package com.vietinbank.feature_home.ui.view.home_card

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.components.ShimmerLoading
import com.vietinbank.core_ui.components.TitleShimmerLoading
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.feature_home.R
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

data class RecentTransactionUiModel(
    val title: String = "",
    val content: String = "",
    val secondaryContent: String = "",
    val logoBank: String = "",
)
sealed class RecentTransactionUiState {
    data object Loading : RecentTransactionUiState()

    data object Empty : RecentTransactionUiState()

    data object LoadingFail : RecentTransactionUiState()

    data class Success(
        val items: List<RecentTransactionUiModel>,
    ) : RecentTransactionUiState()
}

@Composable
fun RecentTransactionCard(
    modifier: Modifier = Modifier,
    uiModel: RecentTransactionUiState,
    imageLoader: CoilImageLoader,
) {
    when (uiModel) {
        RecentTransactionUiState.Empty -> {
            EmptyRecentCard()
        }
        RecentTransactionUiState.LoadingFail -> {
            EmptyRecentCard(
                title = stringResource(R.string.home_loading_fail_recent_card_title),
                content = stringResource(R.string.home_loading_fail_recent_card_content),
            )
        }
        RecentTransactionUiState.Loading -> {
            Column(
                modifier = modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(FDS.Sizer.Padding.padding32))
                    .background(Color.White)
                    .padding(FDS.Sizer.Padding.padding24),
                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding16),
            ) {
                TitleShimmerLoading()
                ShimmerLoading()
                ShimmerLoading()
            }
        }
        is RecentTransactionUiState.Success -> {
            Column(
                modifier = modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(FDS.Sizer.Padding.padding32))
                    .background(Color.White)
                    .padding(FDS.Sizer.Padding.padding24),
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    FoundationText(
                        text = stringResource(R.string.home_recent_transaction),
                        style = FDS.Typography.headingH3,
                        color = FDS.Colors.characterHighlighted,
                        modifier = Modifier.weight(1f),
                    )
                    Image(
                        modifier = Modifier.size(FDS.Sizer.Padding.padding24),
                        painter = painterResource(R.drawable.ic_home_more),
                        contentDescription = "",
                    )
                }
                uiModel.items.forEachIndexed { index, data ->
                    Column(
                        modifier = modifier.fillMaxWidth(),
                    ) {
                        StrokeLine(showBorder = index != 0)
                        Row(
                            modifier = Modifier
                                .padding(vertical = FDS.Sizer.Padding.padding16)
                                .fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(
                                FDS.Sizer.Padding.padding8,
                            ),
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            Box(
                                modifier = Modifier
                                    .size(FDS.Sizer.Padding.padding40)
                                    .background(FDS.Colors.homeBackgroundIcon, CircleShape),
                                contentAlignment = Alignment.Center,
                            ) {
                                imageLoader.LoadUrl(
                                    modifier = Modifier.size(FDS.Sizer.Padding.padding24),
                                    url = data.logoBank,
                                    placeholderRes = R.drawable.ic_home_vtb_logo,
                                    errorRes = R.drawable.home_gray_background,
                                    isCache = false,
                                )
                            }
                            Column(
                                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding4),
                            ) {
                                FoundationText(
                                    text = data.title,
                                    style = FDS.Typography.bodyB2Emphasized,
                                    color = FDS.Colors.characterPrimary,
                                )
                                FoundationText(
                                    text = data.content,
                                    style = FDS.Typography.captionCaptionL,
                                    color = FDS.Colors.tabTextInactive,
                                )
                                FoundationText(
                                    text = data.secondaryContent,
                                    style = FDS.Typography.captionCaptionL,
                                    color = FDS.Colors.tabTextInactive,
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun EmptyRecentCard(
    modifier: Modifier = Modifier,
    title: String = stringResource(R.string.home_empty_transaction),
    content: String = stringResource(R.string.home_empty_transaction_guide),
) {
    val bgColor = FDS.Colors.foundationDarkButtonPressedPrimary
    Column(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(FDS.Sizer.Padding.padding32))
            .drawBehind {
                drawRoundRect(
                    brush = Brush.linearGradient(
                        listOf(
                            bgColor,
                            Color.White,
                            Color.White,
                        ),
                        start = Offset(0.5f, 0f),
                        end = Offset(0.5f, size.height),
                    ),
                )
            }
            .padding(FDS.Sizer.Padding.padding24),
        verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding16),
    ) {
        Image(
            painter = painterResource(R.drawable.ic_home_empty_transaction),
            contentDescription = "",
            modifier = Modifier.size(FDS.Sizer.Icon.icon96),
        )

        FoundationText(
            text = title,
            style = FDS.Typography.headingH3,
            color = FDS.Colors.characterHighlighted,
        )

        FoundationText(
            text = content,
            style = FDS.Typography.captionCaptionL,

        )
    }
}
