package com.vietinbank.feature_home.ui.screen.home_new_ui.short_cut

import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.mutableStateListOf
import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.CustomEncryptedPrefs
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.home.FunctionData
import com.vietinbank.core_domain.models.home.ListFunctionParams
import com.vietinbank.core_domain.models.home.UpdateFunctionParams
import com.vietinbank.core_domain.repository.cache.IOttRegistrationRepository
import com.vietinbank.core_domain.usecase.home.HomeUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.OneTimeEvent
import com.vietinbank.feature_home.R
import com.vietinbank.feature_home.ui.view.home_card.FunctionItemData
import com.vietinbank.feature_home.ui.view.home_card.ItemAction
import com.vietinbank.feature_home.ui.view.home_card.withAction
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import java.text.Normalizer
import javax.inject.Inject

@HiltViewModel
class EditShortcutViewModel @Inject constructor(
    private val homeUseCase: HomeUseCase,
    override val sessionManager: ISessionManager,
    private val prefs: CustomEncryptedPrefs,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    val ottRegistrationRepository: IOttRegistrationRepository,
) : BaseViewModel() {

    private var _favouriteItems = mutableStateListOf<FunctionItemData>()
    val favouriteItems = _favouriteItems

    private var _listFunc = mutableStateListOf<FunctionItemData>()
    val listFunc = derivedStateOf {
        _listFunc.groupBy {
            it.groupName ?: ""
        }
    }

    private var _listSearch = mutableStateListOf<FunctionItemData>()
    val listSearch = _listSearch

    private val _uiState = MutableStateFlow(EditShortcutUiState())
    val uiState: StateFlow<EditShortcutUiState> = _uiState.asStateFlow()

    init {
        getFavouriteList()
        listFunc()
    }

    fun onAction(action: ShortcutAction) {
        when (action) {
            is ShortcutAction.AddItem -> {
                if (_favouriteItems.size >= 10) {
                    _uiState.update {
                        it.copy(
                            guideline = R.string.home_edit_shortcut_error_max_favourite,
                        )
                    }
                } else {
                    _uiState.update {
                        it.copy(
                            guideline = R.string.home_app_bar_guide_line_edit_shortcut,
                        )
                    }
                    _listFunc.firstOrNull { it.functionId == action.id }?.let { item ->
                        _listFunc.remove(item)
                        _favouriteItems.add(
                            item.withAction(if (_uiState.value.isEditMode) ItemAction.Remove else ItemAction.Move),
                        )
                    }
                }
            }

            is ShortcutAction.MoveItem -> {
                _favouriteItems.add(action.to, _favouriteItems.removeAt(action.from))
            }

            is ShortcutAction.RemoveItem -> {
                if (_favouriteItems.size <= 2) {
                    _uiState.update {
                        it.copy(
                            guideline = R.string.home_edit_shortcut_error_min_favourite,
                        )
                    }
                } else {
                    _uiState.update {
                        it.copy(
                            guideline = R.string.home_app_bar_guide_line_edit_shortcut,
                        )
                    }
                    if (_uiState.value.isEditMode) {
                        _favouriteItems.firstOrNull { it.functionId == action.id }?.let { item ->
                            _favouriteItems.remove(item)
                            _listFunc.add(item.withAction(ItemAction.Add))
                        }
                    }
                }
            }

            ShortcutAction.EditItem -> {
                val newEditMode = !_uiState.value.isEditMode
                _uiState.update { it.copy(isEditMode = newEditMode) }

                _favouriteItems.replaceAll { item ->
                    item.withAction(if (newEditMode) ItemAction.Remove else ItemAction.Move)
                }
            }

            is ShortcutAction.SearchItem -> {
                _uiState.update {
                    it.copy(
                        search = action.search,
                    )
                }
                _listSearch.clear()
                if (action.search.isNotBlank()) {
                    _listSearch.addAll(
                        _listFunc.filter {
                            val name = it.functionName.normalize()
                            val query = action.search.normalize()
                            name.contains(query)
                        },
                    )
                }
            }

            ShortcutAction.ClearValue -> {
                _uiState.update {
                    it.copy(
                        search = "",
                    )
                }
            }

            ShortcutAction.BackAndSave -> {
                updateList()
            }
        }
    }

    private fun getFavouriteList() {
        val data = prefs.getString(Tags.LIST_FAVOURITE)
        val type = object : TypeToken<List<FunctionItemData>>() {}.type
        if (!data.isNullOrEmpty()) {
            val list: List<FunctionItemData> = Utils.g().provideGson().fromJson(data, type)
            _favouriteItems.addAll(
                list.map {
                    it.copy(
                        iconRes = R.drawable.ic_home_transfer,
                        functionName = it.functionName ?: "",
                        functionId = it.functionId ?: "",
                        groupName = it.groupName ?: "",
                        groupId = it.groupId ?: "",
                        orderNumber = it.orderNumber,
                        itemAction = ItemAction.Move,
                    )
                },
            )
        }
    }

    private fun listFunc() {
        launchJob(showLoading = true) {
            val res = homeUseCase.listFunction(
                ListFunctionParams(
                    username = userProf.getUserName().orEmpty(),
                    roleId = userProf.getRoleId().toString(),
                    role = userProf.getRoleId().toString(),
                ),
            )
            handleResource(res) { data ->
                val func = data.data ?: listOf()
                _listFunc.addAll(
                    func.map {
                        FunctionItemData(
                            iconRes = R.drawable.ic_home_transfer,
                            functionName = it?.functionName ?: "",
                            functionId = it?.functionId ?: "",
                            groupName = it?.groupName ?: "",
                            groupId = it?.groupId ?: "",
                            itemAction = ItemAction.Add,
                        )
                    },
                )
            }
        }
    }

    fun updateList() {
        launchJob(showLoading = true) {
            val funcData: List<FunctionData> = _favouriteItems.mapIndexed { index, functionItemData ->
                FunctionData(
                    functionName = functionItemData.functionName,
                    functionId = functionItemData.functionId,
                    groupId = functionItemData.groupId,
                    groupName = functionItemData.groupName,
                    orderNumber = index.toString(),
                    role = userProf.getRoleId().toString(),
                )
            }
            val res = homeUseCase.updateFavoriteFunction(
                UpdateFunctionParams(
                    username = userProf.getUserName().orEmpty(),
                    roleId = userProf.getRoleId().toString(),
                    role = userProf.getRoleId().toString(),
                    functions = funcData,
                ),
            )
            handleResource(res) {
                prefs.setString(Tags.LIST_FAVOURITE, Utils.g().provideGson().toJson(_favouriteItems))
                sendEvent(ShortcutEvent.PopBackStack)
            }
        }
    }
}

fun String.normalize(): String {
    return Normalizer.normalize(this, Normalizer.Form.NFD)
        .replace("\\p{Mn}+".toRegex(), "") // bỏ toàn bộ dấu
        .lowercase() // đồng bộ lowercase
}

data class EditShortcutUiState(
    val isEditMode: Boolean = false,
    val search: String = "",
    val guideline: Int = R.string.home_app_bar_guide_line_edit_shortcut,
)

sealed class ShortcutEvent : OneTimeEvent {
    data object PopBackStack : ShortcutEvent()
}

sealed interface ShortcutAction {
    data class MoveItem(val from: Int, val to: Int) : ShortcutAction
    data class RemoveItem(val id: String?) : ShortcutAction
    data class AddItem(val id: String?) : ShortcutAction
    data class SearchItem(val search: String) : ShortcutAction
    data object EditItem : ShortcutAction
    data object ClearValue : ShortcutAction
    data object BackAndSave : ShortcutAction
}