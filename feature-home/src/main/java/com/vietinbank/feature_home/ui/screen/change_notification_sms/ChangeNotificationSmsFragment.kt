package com.vietinbank.feature_home.ui.screen.change_notification_sms

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_home.ui.navigation.AccountBalanceNavigator
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class ChangeNotificationSmsFragment : BaseFragment<ChangeNotificationSmsViewModel>() {
    override val viewModel: ChangeNotificationSmsViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var accountBalanceNavigator: AccountBalanceNavigator

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        handleSingleEvent {
            when (it) {
                ChangeNotificationEvent.ConfirmOtpEvent -> appNavigator.goToValidateOTT(
                    tag = Tags.ACCOUNT_BALANCE_TYPE_CHANGE_SMS,
                    alertType = viewModel.uiState.value.alertType?.substringAfter(" "),
                )

                ChangeNotificationEvent.NavigateBackEvent -> accountBalanceNavigator.popBackStack()
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.uiState.collectAsState()

        ChangeNotificationSmsScreen(
            uiState = uiState,
            onAction = viewModel::onAction,
        )
    }
}