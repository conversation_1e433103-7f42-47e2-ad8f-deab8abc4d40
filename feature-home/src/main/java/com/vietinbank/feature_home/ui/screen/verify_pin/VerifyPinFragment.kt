package com.vietinbank.feature_home.ui.screen.verify_pin

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.BiometricHelper
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_home.ui.navigation.AccountBalanceNavigator
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class VerifyPinFragment : BaseFragment<VerifyPinViewModel>() {
    override val viewModel: VerifyPinViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var accountBalanceNavigator: AccountBalanceNavigator

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        observeEvents()
    }

    private fun observeEvents() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.events.collect { event ->
                when (event) {
                    is VerifyPinEvent.NavigateBack -> {
                        accountBalanceNavigator.popBackStack()
                    }

                    is VerifyPinEvent.NoPinSetup -> {
                        showNoticeDialog(
                            message = "Quý khách chưa cài đặt mã PIN. Vui lòng cài đặt để sử dụng tính năng này",
                            positiveAction = {
                                // Navigate to PIN setup
                                accountBalanceNavigator.goToOttPinSetup()
                            },
                        )
                    }

                    is VerifyPinEvent.ShowBiometricPrompt -> {
                        showBiometricPrompt()
                    }

                    is VerifyPinEvent.VerifySuccess -> {
                        // Navigate to account balance or pop back with result
                        appNavigator.popBackStack()
                    }

                    is VerifyPinEvent.NavigateToForgotPin -> {
                        // Navigate to forgot PIN flow
//                        appNavigator.navigateTo("forgot_pin")
                    }

                    is VerifyPinEvent.MaxAttemptsReached -> {
                        showNoticeDialog(
                            message = "Bạn đã nhập sai quá nhiều lần. Tài khoản đã bị khóa trong 30 phút",
                            cancelable = false,
                            positiveAction = {
                                appNavigator.popBackStack()
                            },
                        )
                    }

                    is VerifyPinEvent.WrongPin -> {
                        // Optionally show toast or inline error is enough
                    }

                    is VerifyPinEvent.Lockout -> {
                        val minutes = (event.remainingTime / 1000 / 60).toInt()
                        showNoticeDialog(
                            message = "Tài khoản đã bị khóa. Vui lòng thử lại sau $minutes phút",
                            cancelable = false,
                            positiveAction = {
                                appNavigator.popBackStack()
                            },
                        )
                    }

                    is VerifyPinEvent.ShowError -> {
                        showNoticeDialog(
                            message = event.message,
                            cancelable = true,
                        )
                    }
                }
            }
        }
    }

    private fun showBiometricPrompt() {
        BiometricHelper.showBiometricOrDeviceCredentialPrompt(
            fragment = this,
            title = "Xác thực",
            subtitle = "Vui lòng xác thực để xem thông tin biến động số dư",
            onSuccess = {
                viewModel.onBiometricSuccess()
            },
            onError = { errorMessage ->
                viewModel.onBiometricError(errorMessage)
            },
        )
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.state.collectAsState()
        AppTheme {
            VerifyPinScreen(
                state = uiState,
                onAction = viewModel::onAction,
            )
        }
    }
}