package com.vietinbank.feature_home.ui.view

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandHorizontally
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkHorizontally
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.CircularIconButton
import com.vietinbank.core_ui.components.CircularIconButtonSize
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.LocalAppConfigManager
import com.vietinbank.core_ui.utils.ellipticalGradientBackground
import com.vietinbank.core_ui.utils.gradientBorder
import com.vietinbank.core_ui.utils.innerShadow
import com.vietinbank.core_ui.utils.rememberAppNavBarDp
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
@Preview
fun NavigationBar(
    modifier: Modifier = Modifier,
    selectedIndex: Int = 0,
    onTabSelected: (Int) -> Unit = {},
    onClickShortcut: () -> Unit = {},
    onSizeChanged: (IntSize) -> Unit = {},
) {
    val bottomDp = rememberAppNavBarDp(LocalAppConfigManager.current) ?: FDS.Sizer.Padding.padding0
    val listTabs: List<Pair<Int, Int>> = listOf(
        R.drawable.ic_core_ui_home to R.string.nav_bar_home,
        R.drawable.ic_core_ui_admin to R.string.nav_bar_admin,
        R.drawable.ic_core_ui_explore to R.string.nav_bar_explore,
    )
    val bgScreen = FDS.Colors.backgroundBgScreen
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .drawBehind {
                drawRoundRect(
                    brush = Brush.linearGradient(
                        listOf(
                            bgScreen,
                            bgScreen,
                            bgScreen,
                            Color.White.copy(0f),
                        ),
                        start = Offset(0.5f, size.height),
                        end = Offset(0.5f, 0f),
                    ),
                )
            }
            .padding(horizontal = FDS.Sizer.Padding.padding24)
            .padding(top = FDS.Sizer.Padding.padding16)
            .padding(bottom = bottomDp)
            .onGloballyPositioned { coordinates ->
                onSizeChanged(coordinates.size)
            },

    ) {
        Row(
            modifier = Modifier.align(Alignment.CenterStart)
                .gradientBorder()
                .clip(RoundedCornerShape(50))
                .innerShadow(
                    shape = CircleShape,
                    color = Color.White.copy(0.56f),
                    offsetY = (-2).dp,
                )
                .innerShadow(
                    shape = CircleShape,
                    color = Color.White.copy(0.56f),
                    offsetY = (2).dp,
                )
                .ellipticalGradientBackground(
                    colors = listOf(
                        FDS.Colors.buttonGradientPrimaryPressed.copy(alpha = 0.6F),
                        Color.White.copy(alpha = 0F),
                    ),
                    centerFraction = Offset(0.5f, 1.75f),
                    scaleX = 3f,
                )
                .padding(FDS.Sizer.Padding.padding8),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            listTabs.forEachIndexed { index, item ->
                val isSelected = index == selectedIndex
                Row(
                    modifier = Modifier
                        .clip(
                            RoundedCornerShape(50),
                        )
                        .then(
                            if (isSelected) {
                                Modifier.background(
                                    Color.White.copy(alpha = 0.15f),
                                )
                            } else {
                                Modifier.background(Color.Transparent)
                            },
                        )
                        .safeClickable {
                            onTabSelected(index)
                        }
                        .padding(FDS.Sizer.Padding.padding8),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    AnimatedVisibility(
                        visible = isSelected,
                        enter = expandHorizontally(
                            expandFrom = Alignment.End, // từ phải sang trái
                            animationSpec = tween(300),
                        ) + fadeIn(),
                        exit = shrinkHorizontally(
                            shrinkTowards = Alignment.End,
                            animationSpec = tween(300),
                        ) + fadeOut(),
                    ) {
                        Image(
                            modifier = Modifier.padding(end = FDS.Sizer.Padding.padding4),
                            painter = painterResource(id = item.first),
                            contentDescription = "",
                        )
                    }
                    FoundationText(
                        text = stringResource(item.second),
                        color = Color.White,
                        style = FDS.Typography.material3Typography.labelMedium,
                    )
                }
            }
        }

        CircularIconButton(
            modifier = Modifier.align(Alignment.CenterEnd),
            icon = painterResource(R.drawable.ic_home_short_cut),
            isLightIcon = false,
            onClick = onClickShortcut,
            size = CircularIconButtonSize.Medium,
        )
    }
}