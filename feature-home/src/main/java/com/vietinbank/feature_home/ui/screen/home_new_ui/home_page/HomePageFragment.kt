package com.vietinbank.feature_home.ui.screen.home_new_ui.home_page

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.components.dialog.csat_dialog.CsatDialog
import com.vietinbank.core_ui.components.dialog.csat_dialog.CsatResult
import com.vietinbank.feature_home.R
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class HomePageFragment : BaseFragment<HomePageViewModel>() {
    override val viewModel: HomePageViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var imageLoader: CoilImageLoader

    override fun onResume() {
        super.onResume()
        viewModel.updateVisibleBalance()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        handleSingleEvent { event ->
            when (event) {
                HomePageEvent.NavigateToSetting -> {
                    appNavigator.goToSetting()
                }
                HomePageEvent.NavigateToOttScreen -> {
                    appNavigator.goToOttDashboard()
                }

                HomePageEvent.NavigateToAccount -> {
                    appNavigator.goToAccountNoAnm(
                        Utils.g().provideGson().toJson(viewModel.assetState),
                        Utils.g().provideGson().toJson(viewModel.creditState),
                    )
                }
                HomePageEvent.NavigateToExplore -> {
                    appNavigator.goToMakerTransfer(bundleOf())
                }
                HomePageEvent.NotYetImplement -> {
                    showNoticeDialog(
                        getString(R.string.home_update_text),
                    )
                }
                HomePageEvent.NavigateToCt -> {
                }
                HomePageEvent.NavigateToEditShortcut -> {
                    appNavigator.goToEditShortcut()
                }
                HomePageEvent.NavigateToManager -> {
                    appNavigator.gotoTransactionManager()
                }
                HomePageEvent.NavigateToApprovalList -> {
                    // Navigate to Checker Approval List with transaction data
                    // API getTransactionList already called in ViewModel
                    val transaction = viewModel.getCurrentTransaction()
                    if (transaction != null) {
                        appNavigator.goToApprovalList(
                            transaction = viewModel.getTransactionListJson(),
                            tranType = transaction.tranType ?: "",
                            groupType = transaction.groupType ?: "",
                            serviceType = transaction.servicetype ?: "",
                            isBatchFileMode = false,
                        )
                    }
                }
                HomePageEvent.ShowFeedBack -> {
                    showCsat()
                }
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.uiState.collectAsState()
        val sections by viewModel.sections.collectAsState()

        HomePageScreen(
            uiState = uiState,
            sections = sections,
            onAction = viewModel::onAction,
            imageLoader = imageLoader,
        )
    }
    override fun onBackPressed(): Boolean {
        showConfirmDialog(
            message = getString(R.string.home_log_out_guide),
            positiveAction = {
                inactivityManager.stopMonitoring()
                // user confirm => pop
                viewModel.clearSession()
                appNavigator.goToHomePreLoginAndPopAll()
            },
            negativeAction = {
                // user cancel => do nothing
            },
        )
        return true
    }

    private fun showCsat() {
        val dialog = CsatDialog.newInstance()
        dialog.show(childFragmentManager, "CsatDialog")
        childFragmentManager.setFragmentResultListener(
            "csat_dialog_result",
            viewLifecycleOwner,
        ) { _, bundle ->
            val isCancelled = bundle.getBoolean("key_cancelled", false)
            if (!isCancelled) {
                val result = bundle.getParcelable<CsatResult>("key_result")
                result?.let {
                    viewModel.updateFeedback(it.feedbackPoint, it.feedbackComment)
                }
            }
        }
    }
}
