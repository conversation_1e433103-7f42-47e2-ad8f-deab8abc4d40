package com.vietinbank.feature_home.ui.screen.home_new_ui.home_page

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.components.CircularIconButton
import com.vietinbank.core_ui.components.CircularIconButtonSize
import com.vietinbank.core_ui.components.UserProfileCard
import com.vietinbank.core_ui.components.dialog.csat_dialog.CsatView
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.dismissRippleClickable
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_home.R
import com.vietinbank.feature_home.ui.screen.home_new_ui.short_cut.ShortCutScreen
import com.vietinbank.feature_home.ui.view.NavigationBar
import com.vietinbank.feature_home.ui.view.home_card.BannerCard
import com.vietinbank.feature_home.ui.view.home_card.MyRequestCard
import com.vietinbank.feature_home.ui.view.home_card.RecentTransactionCard
import com.vietinbank.feature_home.ui.view.home_card.RecommendedCard
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun HomePageScreen(
    uiState: HomePageUiState,
    sections: List<HomeCardItem>,
    onAction: (HomePageAction) -> Unit,
    imageLoader: CoilImageLoader,
) {
    val listState = rememberLazyListState()
    var bottomSize by remember { mutableStateOf(IntSize.Zero) }

    Box {
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .eFastBackground()
                .systemBarsPadding()
                .padding(top = FDS.Sizer.Padding.padding8)
                .padding(horizontal = FDS.Sizer.Padding.padding8)
                .clip(
                    RoundedCornerShape(
                        bottomEnd = FDS.Sizer.Padding.padding32,
                        bottomStart = FDS.Sizer.Padding.padding32,
                    ),
                ),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding8),
            horizontalAlignment = Alignment.CenterHorizontally,
            state = listState,
        ) {
            item {
                UserProfileCard(
                    title = uiState.userName,
                    content = uiState.companyName,
                    onClickAvatar = {
                        onAction(HomePageAction.OnClickAvatar)
                    },
                    onClickIcon = {
                        onAction(HomePageAction.OnClickNotification)
                    },
                )
            }
            item {
                TotalBalance(
                    balance = uiState.totalBalance,
                    isVisible = uiState.isVisibleBalance,
                    onClick = {
                        onAction(HomePageAction.OnClickVisibleButton)
                    },
                    isLoading = uiState.isBalanceLoading,
                )
            }

            item {
                HomeSearch(
                    onClickSearch = { onAction(HomePageAction.TestTest) },
                )
            }

            items(
                items = sections,
                key = {
                    when (it) {
                        HomeCardItem.BannerCard -> Tags.HOME_BANNER_CARD
                        is HomeCardItem.RecommendedCard -> Tags.HOME_RECOMMENDED_CARD
                        is HomeCardItem.MyRequestCard -> Tags.HOME_MY_REQUEST_CARD
                        is HomeCardItem.RecentTransactionCard -> Tags.HOME_RECENT_TRANS_CARD
                    }
                },
            ) { item ->
                when (item) {
                    HomeCardItem.BannerCard -> {
                        BannerCard(
                            onClickBanner = { onAction(HomePageAction.TestTest) },
                        )
                    }

                    is HomeCardItem.RecommendedCard -> {
                        RecommendedCard(
                            uiModel = item.state,
                            onClickLoading = {
                                onAction(HomePageAction.OnClickLoadingRcmCard)
                            },
                            onClickSeeAll = {
                                onAction(HomePageAction.OnClickAllTransaction)
                            },
                            onClickItem = {
                                onAction(HomePageAction.OnCLickItem(it))
                            },
                        )
                    }

                    is HomeCardItem.MyRequestCard -> MyRequestCard(
                        uiState = item.state,
                        clickLoading = {
                            onAction(HomePageAction.OnClickLoadingMyRequest)
                        },
                        clickSeeAll = {
                            onAction(HomePageAction.OnClickAllTransaction)
                        },
                        onClickItem = {
                            onAction(HomePageAction.OnCLickItem(it))
                        },
                    )

                    is HomeCardItem.RecentTransactionCard -> {
                        RecentTransactionCard(
                            uiModel = item.state,
                            imageLoader = imageLoader,
                        )
                    }
                }
            }
            item {
                CsatView(
                    onClick = { onAction(HomePageAction.ClickCsat) },
                )
            }

            item {
                BottomGuideAction(
                    modifier = Modifier
                        .padding(bottom = (bottomSize.height / 2).dp),
                    onClickFindBankBranch = { onAction(HomePageAction.TestTest) },
                    onClickHelp = { onAction(HomePageAction.TestTest) },
                    onClickContact = { onAction(HomePageAction.TestTest) },
                )
            }
        }

        Box(
            modifier = Modifier.align(Alignment.BottomCenter),
        ) {
            NavigationBar(
                selectedIndex = uiState.bottomNavIndex,
                onTabSelected = {
                    onAction(HomePageAction.OnTabSelected(it))
                },
                onClickShortcut = {
                    onAction(HomePageAction.ClickShortcut)
                },
                onSizeChanged = {
                    bottomSize = it
                },
            )
        }

        if (uiState.isShowShortcut) {
            ShortCutScreen(
                onClickShortcut = { onAction(HomePageAction.ClickShortcut) },
                list = uiState.listFuncShortcut,
                onClickEdit = { onAction(HomePageAction.ClickEditShortcut) },
            )
        }
    }
}

@Composable
fun TotalBalance(
    modifier: Modifier = Modifier,
    balance: String?,
    isVisible: Boolean,
    onClick: () -> Unit,
    isLoading: Boolean = false,
) {
    val painter = if (isVisible) {
        painterResource(R.drawable.ic_home_page_eye_close)
    } else {
        painterResource(R.drawable.ic_home_page_eye_open)
    }

    val balanceText = if (isVisible) {
        if (balance == null) {
            stringResource(R.string.home_error_balance)
        } else {
            stringResource(
                R.string.home_balance,
                balance,
            )
        }
    } else {
        stringResource(R.string.home_marked_balance)
    }
    Column(
        modifier = modifier
            .padding(vertical = FDS.Sizer.Padding.padding16)
            .fillMaxWidth()
            .padding(FDS.Sizer.Padding.padding16),
        verticalArrangement = Arrangement.spacedBy(
            FDS.Sizer.Padding.padding8,
            Alignment.CenterVertically,
        ),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        FoundationText(
            text = stringResource(R.string.home_total_balance_text),
            color = Color.White,
            style = FDS.Typography.captionCaptionLBold,
        )
        Row(
            horizontalArrangement = Arrangement.spacedBy(
                FDS.Sizer.Padding.padding8,
                Alignment.CenterHorizontally,
            ),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(FDS.Sizer.Padding.padding32),
                    color = FDS.Colors.textSelected,
                    strokeWidth = FDS.Sizer.Padding.padding6,
                )
            } else {
                FoundationText(
                    text = balanceText,
                    color = Color.White,
                    style = FDS.Typography.material3Typography.headlineMedium,
                )
                Image(
                    painter = painter,
                    contentDescription = "",
                    modifier = Modifier
                        .size(FDS.Sizer.Padding.padding24)
                        .dismissRippleClickable {
                            onClick()
                        },
                )
            }
        }
    }
}

@Composable
fun HomeSearch(
    modifier: Modifier = Modifier,
    onClickSearch: () -> Unit,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(50))
            .background(Color.White)
            .padding(FDS.Sizer.Padding.padding16)
            .safeClickable { onClickSearch() },
        horizontalArrangement = Arrangement.spacedBy(
            FDS.Sizer.Padding.padding8,
            Alignment.CenterHorizontally,
        ),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Image(
            painter = painterResource(R.drawable.ic_home_vtb_logo),
            contentDescription = "",
            modifier = Modifier
                .clip(CircleShape)
                .background(Color.White)
                .size(FDS.Sizer.Padding.padding32),
            contentScale = ContentScale.Crop,
        )
        FoundationText(
            text = stringResource(R.string.search_hint_text),
            color = FDS.Colors.textSelected,
            style = FDS.Typography.bodyB2Emphasized,
            modifier = Modifier.weight(1f),
        )

        Box(
            modifier = Modifier
                .width(FDS.Sizer.Padding.padding2)
                .height(FDS.Sizer.Padding.padding24)
                .background(Color.Black.copy(0.1f)),
        )

        Image(
            painter = painterResource(R.drawable.ic_home_micro),
            contentDescription = "",
            modifier = Modifier
                .padding(end = FDS.Sizer.Padding.padding8)
                .size(FDS.Sizer.Padding.padding24),
        )
    }
}

@Composable
fun BottomGuideAction(
    modifier: Modifier = Modifier,
    onClickFindBankBranch: () -> Unit,
    onClickHelp: () -> Unit,
    onClickContact: () -> Unit,
) {
    Column(
        modifier = modifier.padding(top = FDS.Sizer.Padding.padding24),
        verticalArrangement = Arrangement.spacedBy(
            FDS.Sizer.Padding.padding24,
            Alignment.CenterVertically,
        ),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        FoundationText(
            text = stringResource(R.string.home_guide_line),
            color = Color.White,
        )
        Row(
            horizontalArrangement = Arrangement.spacedBy(
                FDS.Sizer.Padding.padding16,
                Alignment.CenterHorizontally,
            ),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding8, Alignment.CenterVertically),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                CircularIconButton(
                    icon = painterResource(R.drawable.ic_home_guide_line_building_bank),
                    isLightIcon = false,
                    onClick = onClickFindBankBranch,
                    size = CircularIconButtonSize.Large,
                )
                FoundationText(
                    text = stringResource(R.string.home_guide_line_find_cn),
                    color = Color.White,
                )
            }

            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding8, Alignment.CenterVertically),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                CircularIconButton(
                    icon = painterResource(R.drawable.ic_home_guide_line_help),
                    isLightIcon = false,
                    onClick = onClickHelp,
                    size = CircularIconButtonSize.Large,
                )
                FoundationText(
                    text = stringResource(R.string.home_guide_line_manual),
                    color = Color.White,
                )
            }

            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding8, Alignment.CenterVertically),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                CircularIconButton(
                    icon = painterResource(R.drawable.ic_home_guide_line_contact),
                    isLightIcon = false,
                    onClick = onClickContact,
                    size = CircularIconButtonSize.Large,
                )
                FoundationText(
                    text = stringResource(R.string.home_guide_line_contact),
                    color = Color.White,
                )
            }
        }
    }
}
