package com.vietinbank.feature_home.ui.screen.notification_setting

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.base.dialog.VerifyPasswordDialog
import com.vietinbank.feature_home.ui.navigation.AccountBalanceNavigator
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class NotificationSettingFragment() : BaseFragment<NotificationSettingViewModel>() {
    override val viewModel: NotificationSettingViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var accountBalanceNavigator: AccountBalanceNavigator

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.getState(accNumber = arguments?.getString(Tags.ACC_NUMBER) ?: "", tag = arguments?.getInt(Tags.NOTIFICATION_ROUTE_TAG) ?: -1)
        handleSingleEvent {
            when (it) {
                NotificationSettingEvent.RegisToSmsEvent -> {
                    accountBalanceNavigator.goToChangeNotificationSms()
                }
                NotificationSettingEvent.UnRegIdentityAccountEvent -> {
                    accountBalanceNavigator.goToUnRegIdentityAccount()
                }
                NotificationSettingEvent.TurnOnOttEvent -> {
                    showVerifyPasswordDialog()
                }
                NotificationSettingEvent.TurnOffOttEvent -> {
                    showNoticeDialog(
                        "Quý khách sẽ không nhận được thông báo biến động số dư của số tài khoản " +
                            "${viewModel.uiState.value.accNumber} trên thiết bị. Quý khách có " +
                            "chắc chắn tắt chức năng này?",
                        positiveButtonText = "Đồng ý",
                        positiveAction = {
                            viewModel.updateOttState(false)
                        },
                    )
                }
                NotificationSettingEvent.NavigateBackEvent -> {
                    appNavigator.popBackStack()
                }
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.uiState.collectAsState()
        NotificationSettingScreen(
            uiState = uiState,
            onAction = viewModel::onAction,
        )
    }

    private fun showVerifyPasswordDialog() {
        val dialog = VerifyPasswordDialog.newInstance(
            title = "Thông báo",
            message = "Quý khách vui lòng nhập mật khẩu đăng nhập để bật chức năng nhận thông báo BĐSD " +
                "của của Số tài khoản ${viewModel.uiState.value.accNumber} trên thiết bị",
        )
        dialog.setOnConfirmListener { password ->
            viewModel.checkPassword(
                password = password,
            )
        }
        dialog.setOnDismissClickListener {
            // user bấm (X)
        }
        dialog.show(childFragmentManager, "VerifyPasswordDialog")
    }
}