package com.vietinbank.feature_home.ui.screen.pin_setup

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ui.base.compose.BaseAppBar
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.base.compose.InputMode
import com.vietinbank.core_ui.base.compose.PinPasswordInput
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.feature_home.ui.view.PinSetupBottomButtons

sealed class PinSetupAction {
    data object OnBackPressed : PinSetupAction()
    data object OnCancelClicked : PinSetupAction()
    data class OnFirstPinChanged(val pin: String) : PinSetupAction()
    data class OnConfirmPinChanged(val pin: String) : PinSetupAction()
    data object OnContinueClicked : PinSetupAction()
}

@Composable
fun PinSetupScreen(
    state: PinSetupState,
    onAction: (PinSetupAction) -> Unit,
) {
    Column(
        modifier = Modifier
            .fillMaxSize(),
    ) {
        // App Bar
        BaseAppBar(
            title = "Cài đặt PIN",
            onBackClick = {
                onAction(PinSetupAction.OnBackPressed)
            },
        )

        // Content
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 24.dp),
        ) {
            BaseText(
                text = "Quý khách vui lòng cài đặt mã PIN để tăng tính bảo mật quản lý thông tin biến động số dư",
                textColor = Color.White,
                textSize = 14.sp,
                textAlign = TextAlign.Start,
                modifier = Modifier.fillMaxWidth(),
            )

            Spacer(modifier = Modifier.height(32.dp))

            // First PIN input
            PinPasswordInput(
                value = state.firstPin,
                onValueChange = { onAction(PinSetupAction.OnFirstPinChanged(it)) },
                inputMode = InputMode.PASSWORD,
                hint = "Nhập mã PIN",
                enabled = !state.isLoading,
                isError = state.firstPinError != null,
                errorMessage = state.firstPinError,
                textColor = Color.Gray,
                backgroundColor = Color.White,
                borderColor = Color.White,
                focusedBorderColor = AppColors.primary,
                errorBorderColor = Color.Red,
                hintColor = Color.Gray,
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Confirm PIN input
            PinPasswordInput(
                value = state.confirmPin,
                onValueChange = { onAction(PinSetupAction.OnConfirmPinChanged(it)) },
                inputMode = InputMode.PASSWORD,
                hint = "Nhập lại mã PIN",
                enabled = !state.isLoading,
                isError = state.confirmPinError != null,
                errorMessage = state.confirmPinError,
                textColor = Color.Gray,
                backgroundColor = Color.White,
                borderColor = Color.White,
                focusedBorderColor = AppColors.primary,
                errorBorderColor = Color.Red,
                hintColor = Color.Gray,
            )
        }

        Spacer(modifier = Modifier.weight(1f))

        // Bottom buttons
        PinSetupBottomButtons(
            isLoading = state.isLoading,
            onCancelClick = { onAction(PinSetupAction.OnCancelClicked) },
            onContinueClick = { onAction(PinSetupAction.OnContinueClicked) },
        )
    }
}

data class PinSetupState(
    val firstPin: String = "",
    val confirmPin: String = "",
    val firstPinError: String? = null,
    val confirmPinError: String? = null,
    val isLoading: Boolean = false,
)

@Preview(showBackground = true, backgroundColor = 0xFF0066CC)
@Composable
fun PinSetupScreenPreview() {
    PinSetupScreen(
        state = PinSetupState(),
        onAction = {},
    )
}