package com.vietinbank.feature_home.ui.screen.notification_setting

import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.CustomEncryptedPrefs
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.home.CheckPasswordParams
import com.vietinbank.core_domain.usecase.home.SettingUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.OneTimeEvent
import com.vietinbank.feature_home.utils.AccountType
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class NotificationSettingViewModel @Inject constructor(
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    private val prefs: CustomEncryptedPrefs,
    private val settingUseCase: SettingUseCase,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
) : BaseViewModel() {
    private val _uiState = MutableStateFlow(NotificationSettingUiState())
    val uiState: StateFlow<NotificationSettingUiState> = _uiState.asStateFlow()

    private var mutableMapOttState: MutableMap<String, Boolean> = mutableMapOf()

    fun getState(accNumber: String, tag: Int) {
        _uiState.update {
            it.copy(
                tag = AccountType.fromValue(tag),
                accNumber = accNumber,
            )
        }
        val data = prefs.getString(Tags.LIST_PAYMENT_ACC_NUMBER + userProf.getCifNo())
        val type = object : TypeToken<Map<String, Boolean>>() {}.type
        if (!data.isNullOrEmpty()) {
            mutableMapOttState = Utils.g().provideGson().fromJson(data, type)
            _uiState.update {
                it.copy(
                    ottState = mutableMapOttState[accNumber] ?: true,
                )
            }
        }
    }

    fun checkPassword(password: String) {
        launchJob {
            val res = settingUseCase.checkPassword(
                CheckPasswordParams(
                    userName = userProf.getUserName().toString(),
                    password = password,
                ),
            )
            handleResource(
                resource = res,
                onSuccess = {
                    updateOttState(true)
                },
            )
        }
    }

    fun onAction(action: NotificationSettingAction) {
        when (action) {
            NotificationSettingAction.UpdateStatusOttAction -> {
                if (_uiState.value.ottState) {
                    sendEvent(NotificationSettingEvent.TurnOffOttEvent)
                } else {
                    sendEvent(NotificationSettingEvent.TurnOnOttEvent)
                }
            }

            NotificationSettingAction.NavigateBackAction -> {
                sendEvent(NotificationSettingEvent.NavigateBackEvent)
            }

            NotificationSettingAction.ClickRegisButtonAction -> {
                if (_uiState.value.tag == AccountType.IdentityAccount) {
                    sendEvent(NotificationSettingEvent.UnRegIdentityAccountEvent)
                } else {
                    sendEvent(NotificationSettingEvent.RegisToSmsEvent)
                }
            }
        }
    }

    fun updateOttState(state: Boolean) {
        _uiState.update {
            it.copy(
                ottState = state,
            )
        }
        mutableMapOttState[_uiState.value.accNumber] = state
        val updatedJson = Utils.g().provideGson().toJson(mutableMapOttState)
        prefs.setString(Tags.LIST_PAYMENT_ACC_NUMBER + userProf.getCifNo(), updatedJson)
    }
}

data class NotificationSettingUiState(
    val tag: AccountType = AccountType.UnDefine,
    val accNumber: String = "",
    val ottState: Boolean = true,
)

sealed interface NotificationSettingEvent : OneTimeEvent {
    data object TurnOnOttEvent : NotificationSettingEvent
    data object TurnOffOttEvent : NotificationSettingEvent
    data object RegisToSmsEvent : NotificationSettingEvent
    data object UnRegIdentityAccountEvent : NotificationSettingEvent
    data object NavigateBackEvent : NotificationSettingEvent
}

interface NotificationSettingAction {
    data object UpdateStatusOttAction : NotificationSettingAction
    data object ClickRegisButtonAction : NotificationSettingAction
    data object NavigateBackAction : NotificationSettingAction
}