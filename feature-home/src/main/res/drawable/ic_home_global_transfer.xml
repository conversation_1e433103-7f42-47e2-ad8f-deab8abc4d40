<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="32dp"
    android:height="32dp"
    android:viewportWidth="32"
    android:viewportHeight="32">
  <path
      android:pathData="M16,0.465C24.58,0.465 31.535,7.42 31.535,16C31.535,24.58 24.58,31.535 16,31.535C7.42,31.535 0.465,24.58 0.465,16C0.465,7.42 7.42,0.465 16,0.465Z"
      android:fillAlpha="0.5">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="0"
          android:startY="0"
          android:endX="32"
          android:endY="32"
          android:type="linear">
        <item android:offset="0" android:color="#FF72CEFF"/>
        <item android:offset="1" android:color="#FFF5FCFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M16,0.465C24.58,0.465 31.535,7.42 31.535,16C31.535,24.58 24.58,31.535 16,31.535C7.42,31.535 0.465,24.58 0.465,16C0.465,7.42 7.42,0.465 16,0.465Z"
      android:strokeWidth="0.929412"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="0"
          android:startY="0"
          android:endX="32"
          android:endY="32"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#FF8FD9FF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M21.917,15.007C21.723,13.849 21.193,12.774 20.394,11.915C19.595,11.055 18.561,10.449 17.421,10.171C16.281,9.893 15.084,9.955 13.979,10.351C12.874,10.746 11.909,11.457 11.204,12.395C10.498,13.333 10.084,14.458 10.012,15.629C9.939,16.801 10.212,17.968 10.796,18.986C11.38,20.004 12.25,20.828 13.297,21.357C14.345,21.885 15.525,22.095 16.691,21.96M10.4,14H21.6M10.4,18H17M15.667,10C14.543,11.8 13.948,13.879 13.948,16C13.948,18.121 14.543,20.2 15.667,22M16.333,10C17.459,11.803 18.055,13.887 18.052,16.013M22,18H20.333C20.068,18 19.814,18.105 19.626,18.293C19.439,18.48 19.333,18.735 19.333,19C19.333,19.265 19.439,19.52 19.626,19.707C19.814,19.895 20.068,20 20.333,20H21C21.265,20 21.519,20.105 21.707,20.293C21.895,20.48 22,20.735 22,21C22,21.265 21.895,21.52 21.707,21.707C21.519,21.895 21.265,22 21,22H19.333M20.667,22V22.667M20.667,17.333V18"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#0F4C7A"
      android:strokeLineCap="round"/>
</vector>
