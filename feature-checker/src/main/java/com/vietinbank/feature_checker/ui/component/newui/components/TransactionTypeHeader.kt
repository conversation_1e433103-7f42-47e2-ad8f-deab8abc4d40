package com.vietinbank.feature_checker.ui.component.newui.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Component for displaying transaction type header with count badge
 * Shows the transaction type title and total count in a badge
 *
 * @param headerTitle The title to display
 * @param totalTransactionsCount Total number of transactions
 * @param onHeaderClick Callback when header is clicked to show transaction type selector
 */
@Composable
fun TransactionTypeHeader(
    modifier: Modifier = Modifier,
    headerTitle: String,
    totalTransactionsCount: Int,
    onHeaderClick: () -> Unit = {},
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .safeClickable { onHeaderClick() }
            .padding(horizontal = FDS.Sizer.Padding.padding24),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Row(
            horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            FoundationText(
                text = headerTitle,
                style = FDS.Typography.headingH3,
                color = FDS.Colors.characterHighlighted,
            )
        }

        Icon(
            painter = painterResource(R.drawable.ic_drop_down),
            contentDescription = stringResource(R.string.feature_checker_content_desc_dropdown),
            tint = FDS.Colors.characterSecondary,
            modifier = Modifier.size(FDS.Sizer.Icon.icon24),
        )
    }
}
