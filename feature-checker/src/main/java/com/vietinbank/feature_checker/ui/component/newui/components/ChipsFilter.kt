package com.vietinbank.feature_checker.ui.component.newui.components

import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.key
import androidx.compose.ui.Modifier
import com.vietinbank.core_ui.components.FoundationChips
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Component for displaying transaction type filter chips
 * Handles horizontal scrolling and selection state
 *
 * @param transactionTypes List of transaction type pairs (displayName to code)
 * @param selectedTransactionTypeCode Currently selected transaction type code
 * @param onTransactionTypeSelected Callback when a chip is selected
 */
@Composable
fun ChipsFilter(
    transactionTypes: List<Pair<String, String?>>,
    selectedTransactionTypeCode: String?,
    onTransactionTypeSelected: (String?) -> Unit,
    modifier: Modifier = Modifier,
) {
    // Use remember for scroll state to avoid resetting on recomposition
    val scrollState = rememberScrollState()

    Row(
        modifier = modifier
            .fillMaxWidth()
            .horizontalScroll(scrollState)
            .padding(horizontal = FDS.Sizer.Padding.padding24),
        horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap4),
    ) {
        transactionTypes.forEach { (displayName, tranTypeCode) ->
            val isSelected = selectedTransactionTypeCode == tranTypeCode

            // Use key to help with recomposition
            key(tranTypeCode ?: "all") {
                FoundationChips(
                    text = displayName,
                    isSelector = isSelected,
                    onClick = {
                        // Only update if actually changing
                        if (selectedTransactionTypeCode != tranTypeCode) {
                            onTransactionTypeSelected(tranTypeCode)
                        }
                    },
                )
            }
        }
    }
}
