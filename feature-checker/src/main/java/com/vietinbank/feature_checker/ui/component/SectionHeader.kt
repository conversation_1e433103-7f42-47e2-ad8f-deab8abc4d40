package com.vietinbank.feature_checker.ui.component

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.utils.safeClickable

/**
 * Created by vandz on 4/4/25.
 */
@Composable
fun SectionHeader(
    title: String,
    hasMoreInfo: Boolean = false,
    isCollapse: Boolean = false,
    onInfoClick: (() -> Unit)? = null,
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        BaseText(
            text = title,
            color = Color.White,
            textSize = 16.sp,
            fontWeight = FontWeight.Medium,
        )

        Spacer(modifier = Modifier.weight(1f))

        if (hasMoreInfo) {
            BaseText(
                text = if (isCollapse) "Xem thêm" else "Thu gọn",
                color = Color.White,
                textSize = 12.sp,
                fontWeight = FontWeight.Medium,
                textDecoration = TextDecoration.Underline,
                modifier = Modifier.safeClickable {
                    onInfoClick?.invoke()
                },
            )
        }
    }
}