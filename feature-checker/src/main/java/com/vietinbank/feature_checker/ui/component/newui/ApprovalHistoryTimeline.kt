package com.vietinbank.feature_checker.ui.component.newui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_domain.models.trace_payment.ActivityLogsDomains
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

private enum class ApprovalStatus { Pending, Completed, Rejected, Error }

private data class ApprovalHistoryItem(
    val name: String,
    val username: String,
    val processDate: String,
    val status: ApprovalStatus,
)

@Composable
fun ApprovalHistoryTimeline(
    activityLogs: ActivityLogsDomains?,
) {
    // Parse createdBy from activityLogs
    val createdBy = remember(activityLogs) {
        activityLogs?.createdBy?.let { creator ->
            ApprovalHistoryItem(
                name = creator.userfullname ?: "",
                username = creator.username ?: "",
                processDate = creator.processDate ?: "",
                status = ApprovalStatus.Completed,
            )
        }
    }

    // Parse verifiedBy from activityLogs
    val verifiedBy = remember(activityLogs) {
        activityLogs?.verifiedBy?.map { verifier ->
            ApprovalHistoryItem(
                name = verifier.userfullname ?: "",
                username = verifier.username ?: "",
                processDate = verifier.processDate ?: "",
                status = ApprovalStatus.Completed,
            )
        } ?: emptyList()
    }

    // Parse nextApprovers from activityLogs (nextApproverBy)
    val waitingApprovalText = stringResource(com.vietinbank.feature_checker.R.string.feature_checker_waiting_approval)
    val nextApprovers = remember(activityLogs, waitingApprovalText) {
        activityLogs?.nextApproverBy?.map { approver ->
            ApprovalHistoryItem(
                name = approver.userfullname ?: "",
                username = approver.username ?: "",
                processDate = approver.processDate ?: waitingApprovalText,
                status = ApprovalStatus.Pending,
            )
        } ?: emptyList()
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(FDS.Sizer.Padding.padding24),
    ) {
        createdBy?.let { item ->
            TimelineItemWithIcon(
                item = item,
                showBottomConnector = verifiedBy.isNotEmpty() || nextApprovers.isNotEmpty(),
            )
        }

        if (verifiedBy.isNotEmpty()) {
            verifiedBy.forEachIndexed { index, item ->
                TimelineItemWithIcon(
                    item = item,
                    showBottomConnector = index < verifiedBy.lastIndex || nextApprovers.isNotEmpty(),
                    isTransitionToPending = index == verifiedBy.lastIndex && nextApprovers.isNotEmpty(),
                )
            }
        }

        if (nextApprovers.isNotEmpty()) {
            val density = LocalDensity.current
            var pendingListHeightPx by remember { mutableStateOf(0) }

            Box {
                Column {
                    TimelineItemWithIcon(
                        item = nextApprovers[0],
                        showBottomConnector = false,
                        isTransitionToPending = false,
                    )

                    Column(
                        modifier = Modifier.onGloballyPositioned { coords ->
                            pendingListHeightPx = coords.size.height
                        },
                    ) {
                        nextApprovers.drop(1).forEachIndexed { index, item ->
                            if (index == 0) Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
                            TimelineItemWithoutIcon(
                                item = item,
                                isLast = index == nextApprovers.drop(1).lastIndex,
                            )
                            if (index < nextApprovers.drop(1).lastIndex) {
                                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
                            }
                        }
                    }
                }

                if (nextApprovers.size > 1) {
                    Box(
                        modifier = Modifier
                            .padding(start = (FDS.Sizer.Icon.icon24.value / 2 - Tags.TIMELINE_ICON_HALF_OFFSET).dp)
                            .padding(top = FDS.Sizer.Padding.padding32),
                    ) {
                        val calculatedHeight = with(density) { pendingListHeightPx.toDp() }
                        VerticalDashedLine(
                            height = calculatedHeight,
                            color = FDS.Colors.stateWarningLighter,
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun TimelineItemWithIcon(
    item: ApprovalHistoryItem,
    showBottomConnector: Boolean = false,
    isTransitionToPending: Boolean = false,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
    ) {
        Box(
            modifier = Modifier.width(FDS.Sizer.Icon.icon24),
            contentAlignment = Alignment.TopCenter,
        ) {
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                StatusIconBadge(status = item.status)

                if (showBottomConnector) {
                    Spacer(modifier = Modifier.height(FDS.Sizer.Padding.padding4))
                    if (isTransitionToPending) {
                        VerticalGradientDashedLine(height = FDS.Sizer.Padding.padding24)
                    } else {
                        VerticalDashedLine(
                            height = FDS.Sizer.Padding.padding24,
                            color = FDS.Colors.stateSuccessLighter,
                        )
                    }
                    Spacer(modifier = Modifier.height(FDS.Sizer.Padding.padding4))
                }
            }
        }

        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap2),
        ) {
            FoundationText(
                text = item.name,
                style = FDS.Typography.bodyB1Emphasized,
                color = FDS.Colors.characterPrimary,
            )
            FoundationText(
                text = item.username,
                style = FDS.Typography.captionCaptionL,
                color = FDS.Colors.characterSecondary,
            )
            FoundationText(
                text = item.processDate,
                style = FDS.Typography.captionCaptionL,
                color = FDS.Colors.characterSecondary,
            )
        }
    }
}

@Composable
private fun TimelineItemWithoutIcon(
    item: ApprovalHistoryItem,
    isLast: Boolean,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
    ) {
        Box(
            modifier = Modifier.width(FDS.Sizer.Icon.icon24),
            contentAlignment = Alignment.TopCenter,
        ) {
            if (!isLast) {
                VerticalDashedLine(
                    height = FDS.Sizer.Padding.padding24,
                    color = FDS.Colors.stateWarningLighter,
                )
            }
        }
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap2),
        ) {
            FoundationText(
                text = item.name,
                style = FDS.Typography.bodyB1Emphasized,
                color = FDS.Colors.characterPrimary,
            )
            FoundationText(
                text = item.username,
                style = FDS.Typography.captionCaptionL,
                color = FDS.Colors.characterSecondary,
            )
            FoundationText(
                text = item.processDate,
                style = FDS.Typography.captionCaptionL,
                color = FDS.Colors.characterSecondary,
            )
        }
    }
}

@Composable
private fun StatusIconBadge(
    status: ApprovalStatus,
    modifier: Modifier = Modifier,
) {
    val backgroundColor = when (status) {
        ApprovalStatus.Completed -> FDS.Colors.stateSuccessLighter
        ApprovalStatus.Pending -> FDS.Colors.stateWarningLighter
        ApprovalStatus.Rejected -> FDS.Colors.stateErrorLighter
        ApprovalStatus.Error -> FDS.Colors.stateErrorLighter
    }
    val iconRes = when (status) {
        ApprovalStatus.Completed -> R.drawable.ic_commom_success_24
        ApprovalStatus.Pending -> R.drawable.ic_commom_pending_24
        ApprovalStatus.Rejected -> R.drawable.ic_common_banned_24
        ApprovalStatus.Error -> R.drawable.ic_commom_fail_24
    }
    Box(
        modifier = modifier
            .size(FDS.Sizer.Icon.icon24)
            .clip(CircleShape)
            .background(backgroundColor),
        contentAlignment = Alignment.Center,
    ) {
        androidx.compose.foundation.Image(
            painter = painterResource(id = iconRes),
            contentDescription = null,
            modifier = Modifier.size(FDS.Sizer.Icon.icon16),
        )
    }
}

@Composable
private fun VerticalDashedLine(
    height: Dp,
    color: Color,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .width(Tags.TIMELINE_LINE_WIDTH.dp)
            .height(height),
    ) {
        androidx.compose.foundation.Canvas(
            modifier = Modifier.matchParentSize(),
        ) {
            val strokeWidth = Tags.TIMELINE_LINE_WIDTH.dp.toPx()
            val dashOn = Tags.TIMELINE_DASH_ON.dp.toPx()
            val dashOff = Tags.TIMELINE_DASH_OFF.dp.toPx()
            val path = Path().apply {
                moveTo(size.width / 2f, 0f)
                lineTo(size.width / 2f, size.height)
            }
            drawPath(
                path = path,
                color = color,
                style = Stroke(
                    width = strokeWidth,
                    pathEffect = PathEffect.dashPathEffect(floatArrayOf(dashOn, dashOff)),
                ),
            )
        }
    }
}

@Composable
private fun VerticalGradientDashedLine(
    height: Dp,
    modifier: Modifier = Modifier,
) {
    // Resolve colors in Composable scope BEFORE Canvas/DrawScope
    val greenColor = FDS.Colors.timelineGradientGreen
    val orangeColor = FDS.Colors.timelineGradientOrange

    Box(
        modifier = modifier
            .width(Tags.TIMELINE_LINE_WIDTH.dp)
            .height(height),
    ) {
        androidx.compose.foundation.Canvas(
            modifier = Modifier.matchParentSize(),
        ) {
            val strokeWidth = Tags.TIMELINE_LINE_WIDTH.dp.toPx()
            val dashOn = Tags.TIMELINE_DASH_ON.dp.toPx()
            val dashOff = Tags.TIMELINE_DASH_OFF.dp.toPx()
            val path = Path().apply {
                moveTo(size.width / 2f, 0f)
                lineTo(size.width / 2f, size.height)
            }
            drawPath(
                path = path,
                brush = Brush.verticalGradient(
                    0f to greenColor,
                    1f to orangeColor,
                ),
                style = Stroke(
                    width = strokeWidth,
                    pathEffect = PathEffect.dashPathEffect(floatArrayOf(dashOn, dashOff)),
                ),
            )
        }
    }
}
