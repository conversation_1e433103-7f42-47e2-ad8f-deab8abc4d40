package com.vietinbank.feature_checker.ui.component.confirmchecker

import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.getAmountServer
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyCurrency
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItem
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItemType
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmUiModel
import javax.inject.Inject

class TransferForeignConfirmUIGenerator @Inject constructor() : ITransactionConfirmUIGenerator {
    override fun generateConfirmUI(
        transaction: TransactionDomain,
        confirmType: String,
        onFieldEvent: ((TransactionFieldEvent) -> Unit)?,
    ): ConfirmUiModel {
        val items = mutableListOf<ConfirmItem>()

        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin chung",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Loại giao dịch",
                value = transaction.tranTypeName ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Số giao dịch",
                value = transaction.mtId ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Trạng thái",
                value = transaction.statusName ?: "",
            ),
        )

        transaction.activityLogs?.let {
            it.createdBy?.let { user ->
                items.add(
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        label = "Người tạo",
                        value = "${transaction.activityLogs?.createdBy?.username ?: ""} - ${transaction.activityLogs?.createdBy?.processDate ?: ""}",
                    ),
                )
            }

            var verifyUser = ""
            it.verifiedBy?.forEachIndexed { index, user ->
                verifyUser += "${user.username ?: ""} - ${user.processDate}"
                if (index < (it.verifiedBy?.size ?: 0) - 1) {
                    verifyUser += "\n"
                }
            }
            if (!verifyUser.isEmpty()) {
                items.add(
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        label = "Người phê duyệt",
                        value = verifyUser,
                    ),
                )
            }
        }

        items.add(
            ConfirmItem(
                type = ConfirmItemType.SPACER,
            ),
        )

        // Thêm header
        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin người chuyển",
            ),
        )

        // Thêm thông tin tài khoản thụ hưởng
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tên đơn vị mua/chuyển tiền",
                value = transaction.fromAccountName?.toString() ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Hình thức",
                value = if (transaction.remitterName?.isEmpty() == false) {
                    "Nguời mua, chuyển tiền là người chuyển thực tế"
                } else {
                    "Nguời mua, chuyển tiền Không là Người chuyển thực tế"
                },
            ),
        )
        if (transaction.remitterName?.isEmpty() == false) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Tên người chuyển tiền thực tế",
                    value = transaction.fromAccountName ?: "",
                ),
            )

            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Quốc gia",
                    value = transaction.remitterCountry ?: "",
                ),
            )

            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Tỉnh/Thành phố",
                    value = transaction.remitterDistrict ?: "",
                ),
            )
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Phường/Xã",
                    value = transaction.remitterWard ?: "",
                ),
            )
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Đường",
                    value = transaction.remitterStreet ?: "",
                ),
            )
        }

        // Thêm header
        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin chi tiết",
            ),
        )

        // Thêm thông tin tài khoản thụ hưởng
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Cán bộ giới thiệu",
                value = transaction.introducer?.toString() ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tên chi nhánh xử lý",
                value = transaction.branchName ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Số tiền chuyển",
                value = Utils.g().getDotMoneyHasCcy(
                    transaction.totalAmount?.getAmountServer() ?: "",
                    transaction.currency ?: "VND",
                ),
//                isHighlighted = true,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Nội dung giao dịch",
                value = transaction.remark ?: "",
            ),
        )
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Ghi chú",
                value = transaction.note ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Ngày giá trị",
                value = transaction.valueDate ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Mục đích sử dụng ngoại tệ",
                value = transaction.purposeTransferName ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Ngày hoàn chứng từ",
                value = if (transaction.lcReturnDate?.isEmpty() == false) {
                    "Thanh toán sau khi nhận hàng"
                } else {
                    "Thanh toán trước khi nhận hàng"
                },
            ),
        )
        if (!transaction.lcReturnDate.isNullOrEmpty()) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Ngày",
                    value = transaction.lcReturnDate ?: "",
                ),
            )
        }

        val formatedFeeAmount = Utils.g().getDotMoneyHasCcy(
            transaction.totalTrxNo ?: "0",
            transaction.currency ?: MoneyCurrency.VND.value,
        )
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng số tiền phí",
                value = formatedFeeAmount,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Loại phí",
                value = transaction.feeType ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tài khoản trích nợ phí",
                value = transaction.feeAccountNo ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Tài khoản trích nợ 1",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Số tài khoản",
                value = transaction.fromAccountNo ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tỷ giá",
                value = transaction.debitRate1 ?: "",
            ),
        )
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Số tiền trích nợ tạm tính",
                value = Utils.g().getDotMoneyHasCcy(
                    transaction.debitAmtByRate1 ?: "",
                    transaction.currency ?: MoneyCurrency.VND.value,
                ),
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Tài khoản trích nợ 2",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Số tài khoản",
                value = transaction.fromAccount2 ?: "",
                subValue = transaction.fromAccountName2 ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tỷ giá",
                value = transaction.debitRate2 ?: "",
            ),
        )
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Số tiền trích nợ tạm tính",
                value = Utils.g().getDotMoneyHasCcy(
                    transaction.debitAmtByRate2 ?: "",
                    transaction.currency ?: "",
                ),
            ),
        )
        // Thêm header
        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin người hưởng",
            ),
        )

        // Thêm thông tin tài khoản thụ hưởng
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Số tài khoản/IBAN người hưởng",
                value = transaction.beneficiaryAccount?.toString() ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tên",
                value = transaction.benName ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Quốc gia",
                value = transaction.benCountry ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tỉnh/Thành phố",
                value = transaction.benDistrict ?: "",
            ),
        )
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Phường/Xã",
                value = transaction.benWard ?: "",
            ),
        )
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Đường",
                value = transaction.benStreet ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Nguời hưởng là người hưởng cuối cùng",
                value = if (transaction.remitterName?.isEmpty() == false) "Không" else "Có",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tên người hưởng cuối cùng",
                value = transaction.finalBenName ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Quốc gia",
                value = transaction.finalBenCountry ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tỉnh/Thành phố",
                value = transaction.finalBenDistrict ?: "",
            ),
        )
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Phường/Xã",
                value = transaction.finalBenWard ?: "",
            ),
        )
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Đường",
                value = transaction.finalBenStreet ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Ngân hàng hưởng",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Bic code",
                value = transaction.receiveBank ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tên/Địa chỉ",
                value = transaction.benBankName ?: "",
            ),
        )
        if (!transaction.benBankCountry.isNullOrEmpty()) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Quốc gia",
                    value = transaction.benBankCountry ?: "",
                ),
            )
        }
        if (!transaction.benBankDistrict.isNullOrEmpty()) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Tỉnh/Thành phố",
                    value = transaction.benBankDistrict ?: "",
                ),
            )
        }
        if (!transaction.benBankWard.isNullOrEmpty()) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Phường/Xã",
                    value = transaction.benBankWard ?: "",
                ),
            )
        }
        if (!transaction.benBankStreet.isNullOrEmpty()) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Đường",
                    value = transaction.benBankStreet ?: "",
                ),
            )
        }

        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Ngân hàng trung gian",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Bic code",
                value = transaction.midBankCode ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tên/Địa chỉ",
                value = transaction.midBankName ?: "",
            ),
        )
        if (!transaction.midBankCountry.isNullOrEmpty()) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Quốc gia",
                    value = transaction.midBankCountry ?: "",
                ),
            )
        }
        if (!transaction.midBankDistrict.isNullOrEmpty()) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Tỉnh/Thành phố",
                    value = transaction.midBankDistrict ?: "",
                ),
            )
        }
        if (!transaction.midBankWard.isNullOrEmpty()) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Phường/Xã",
                    value = transaction.midBankWard ?: "",
                ),
            )
        }
        if (!transaction.midBankStreet.isNullOrEmpty()) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Đường",
                    value = transaction.midBankStreet ?: "",
                ),
            )
        }

        if (!transaction.listFile.isNullOrEmpty()) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.HEADER,
                    title = "File đính kèm",
                ),
            )
            transaction.listFile?.firstOrNull()?.let { file ->
                items.add(
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        label = "Hồ sơ CMNĐSDNT",
                        value = file.fileName ?: "Chưa có file",
//                    isHighlighted = file != null,
                        isClickable = file != null,
                        clickEvent = file?.let {
                            TransactionFieldEvent.FileAttachmentClick(it)
                        },
                    ),
                )
            }
        }

        return ConfirmUiModel(
            screenTitle = if (confirmType == Tags.APPROVE) "Duyệt" else "Từ chối",
            items = items,
            transactionIds = listOfNotNull(transaction.mtId),
            serviceType = transaction.serviceType ?: "CT",
            tranType = transaction.tranType ?: "",
            groupType = transaction.groupType ?: "",
        )
    }

    override fun generateBatchConfirmUI(
        transactions: List<TransactionListDomain>,
        totalAmount: String,
        totalFee: String,
        confirmType: String,
        createdDate: String,
    ): ConfirmUiModel {
        val items = mutableListOf<ConfirmItem>()

        // Header info giao dịch
        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin giao dịch chuyển khoản",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng số giao dịch",
                value = transactions.size.toString(),
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng số tiền chuyển",
                value = totalAmount,
//                isHighlighted = true,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng phí giao dịch",
                value = totalFee,
            ),
        )

        // Lấy danh sách ID giao dịch
        val transactionIds = transactions.mapNotNull { it.mtId }

        // Lấy service type và tran type
        val serviceType = transactions.firstOrNull()?.serviceType ?: ""
        val tranType = transactions.firstOrNull()?.tranType ?: ""
        val groupType = transactions.firstOrNull()?.groupType ?: ""

        return ConfirmUiModel(
            screenTitle = if (confirmType == Tags.APPROVE) "Duyệt" else "Từ chối",
            items = items,
            transactionIds = transactionIds,
            serviceType = serviceType,
            tranType = tranType,
            groupType = groupType,
        )
    }
}