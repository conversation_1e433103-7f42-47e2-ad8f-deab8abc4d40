package com.vietinbank.feature_checker.ui.component.approvallist

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import com.vietinbank.core_common.extensions.getAmountServer
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.FoundationSelector
import com.vietinbank.core_ui.components.SelectorType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.getColorStatus
import javax.inject.Inject
import com.vietinbank.core_ui.R as CoreR
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS
import com.vietinbank.feature_checker.R as FR

/**
 * New Transfer Transaction Renderer using Foundation Design System
 * Handles: "in", "ou", "np", "pm", "sn" transaction types
 */
class NewTransferTransactionRenderer @Inject constructor() : ITransactionContentRenderer {

    @Composable
    override fun RenderContent(
        transaction: TransactionListDomain,
        isSelectionMode: Boolean,
        isSelected: Boolean,
        onChangeClick: () -> Unit,
    ) {
        Column(
            modifier = Modifier.fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
        ) {
            // 1. Transaction number with inline value and badge
            RenderTransactionNumber(transaction, isSelectionMode, isSelected, onChangeClick)

            // 2. Transaction type with icon and amount
            RenderTransactionTypeAndAmount(transaction)

            // 3. Beneficiary section ("Chuyển tới")
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
            ) {
                FoundationText(
                    modifier = Modifier.weight(0.3f),
                    text = stringResource(R.string.manager_detail_transfer_to),
                    style = FDS.Typography.captionL,
                    color = FDS.Colors.characterSecondary,
                )

                FoundationText(
                    modifier = Modifier.weight(0.7f),
                    text = "${transaction.receiveName ?: ""}\n${transaction.receiveBankName ?: ""} - ${transaction.toAccountNo ?: ""}",
                    style = FDS.Typography.captionLSemibold,
                    color = FDS.Colors.characterPrimary,
                    textAlign = TextAlign.End,
                )
            }

            // 4. Content/Remark
            FoundationInfoHorizontal(
                title = stringResource(R.string.transaction_label_content),
                value = transaction.remark ?: "",
                titleStyle = FDS.Typography.captionL,
                valueStyle = FDS.Typography.captionLSemibold,
            )

            // 5. Creation time
            FoundationInfoHorizontal(
                title = stringResource(R.string.manager_detail_create_time),
                value = transaction.createdDate ?: "",
                titleStyle = FDS.Typography.captionL,
                valueStyle = FDS.Typography.captionLSemibold,
            )

            // 6. Transaction status
            FoundationInfoHorizontal(
                title = stringResource(R.string.manager_detail_status),
                value = transaction.statusName ?: "",
                titleStyle = FDS.Typography.captionL,
                valueStyle = FDS.Typography.captionLSemibold,
                valueColor = transaction.status?.getColorStatus(),
            )
        }
    }

    @Composable
    private fun RenderTransactionNumber(
        transaction: TransactionListDomain,
        isSelectionMode: Boolean,
        isSelected: Boolean,
        onChangeClick: (() -> Unit) = {},
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            if (isSelectionMode) {
                FoundationSelector(
                    modifier = Modifier.padding(end = FDS.Sizer.Gap.gap4),
                    boxType = SelectorType.Checkbox,
                    isSelected = isSelected,
                    onClick = onChangeClick,
                )
            }
            // Left side: label + value with mixed styles
            FoundationText(
                text = buildAnnotatedString {
                    withStyle(style = SpanStyle(color = FDS.Colors.characterSecondary)) {
                        append(stringResource(CoreR.string.transaction_number_label))
                    }
                    append(" ")
                    withStyle(
                        style = SpanStyle(
                            color = when {
                                isSelected -> FDS.Colors.characterHighlighted
                                isSelectionMode -> FDS.Colors.characterSecondary
                                else -> FDS.Colors.characterPrimary
                            },
                            fontWeight = FontWeight.SemiBold,
                        ),
                    ) {
                        append(transaction.mtId)
                    }
                },
                style = FDS.Typography.captionL,
                modifier = Modifier.weight(1f),
            )

            // Right side: "Tách lệnh" badge if applicable
            if (transaction.tranType == "sn") {
                FoundationText(
                    modifier = Modifier
                        .clip(RoundedCornerShape(FDS.Sizer.Radius.radius32))
                        .border(
                            FDS.Sizer.Stroke.stroke1,
                            FDS.Colors.backgroundBgHighlight,
                            RoundedCornerShape(FDS.Sizer.Radius.radius32),
                        )
                        .background(FDS.Colors.blue50)
                        .padding(
                            horizontal = FDS.Sizer.Gap.gap8,
                            vertical = FDS.Sizer.Gap.gap4,
                        ),
                    text = stringResource(R.string.manager_detail_separation),
                    color = FDS.Colors.characterHighlighted,
                    style = FDS.Typography.captionCaptionMBold,
                )
            }

            if (!transaction.processTime.isNullOrEmpty()) {
                FoundationText(
                    modifier = Modifier
                        .clip(RoundedCornerShape(FDS.Sizer.Radius.radius32))
                        .border(
                            FDS.Sizer.Stroke.stroke1,
                            FDS.Colors.backgroundBgHighlight,
                            RoundedCornerShape(FDS.Sizer.Radius.radius32),
                        )
                        .background(FDS.Colors.blue50)
                        .padding(
                            horizontal = FDS.Sizer.Gap.gap8,
                            vertical = FDS.Sizer.Gap.gap4,
                        ),
                    text = stringResource(R.string.transaction_transfer_scheduled),
                    color = FDS.Colors.characterHighlighted,
                    style = FDS.Typography.captionMBold,
                )
            }
        }
    }

    @Composable
    private fun RenderTransactionTypeAndAmount(transaction: TransactionListDomain) {
        // Cache formatted amount
        val formattedAmount = remember(transaction.amount, transaction.currency) {
            try {
                val cleanAmount = transaction.amount?.getAmountServer()
                Utils.g().getDotMoneyHasCcy(cleanAmount ?: "", transaction.currency ?: "")
            } catch (_: Exception) {
                "${transaction.amount} ${transaction.currency}"
            }
        }

        // Localized short labels
        val typeInternal = stringResource(CoreR.string.feature_checker_transaction_type_internal)
        val typeExternal = stringResource(CoreR.string.feature_checker_transaction_type_external)
        val typeNapas = stringResource(CoreR.string.feature_checker_transaction_type_napas)
        val typeNormal = stringResource(CoreR.string.maker_transfer_dashboard_normal)

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // Left side: Icon + Transaction type name
            Row(
                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap4),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Icon(
                    painter = painterResource(getTransactionIcon(transaction.tranType)),
                    contentDescription = null,
                    tint = FDS.Colors.characterPrimary,
                    modifier = Modifier.size(FDS.Sizer.Icon.icon16),
                )

                // Shorten the transaction type name for better display
                val shortTypeName = transaction.tranTypeName?.let {
                    when {
                        it.contains(
                            "trong VietinBank",
                            ignoreCase = true,
                        ) || it.contains(
                            "Chuyển tiền trong VietinBank", ignoreCase = true,
                        ) -> typeInternal

                        it.contains(
                            "Liên ngân hàng",
                            ignoreCase = true,
                        ) && it.contains(
                            "24/7", ignoreCase = true,
                        ) -> typeNapas

                        it.contains(
                            "Liên ngân hàng",
                            ignoreCase = true,
                        ) -> typeNormal

                        it.contains(
                            "Ngoài VietinBank",
                            ignoreCase = true,
                        ) || it.contains(
                            "Chuyển tiền ngoài VietinBank", ignoreCase = true,
                        ) -> typeExternal

                        it.contains("Nhanh 24/7", ignoreCase = true) -> typeNapas
                        else -> it
                    }
                } ?: ""

                FoundationText(
                    text = shortTypeName,
                    style = FDS.Typography.captionLSemibold,
                    color = FDS.Colors.characterPrimary,
                )
            }

            // Right side: Amount with currency (right aligned)
            FoundationText(
                text = formattedAmount,
                style = FDS.Typography.captionLSemibold,
                color = FDS.Colors.characterPrimary,
                textAlign = TextAlign.End,
            )
        }
    }

    private fun getTransactionIcon(type: String?): Int {
        return when (type?.lowercase()) {
            "in", "ou", "np", "pm", "sn" -> FR.drawable.ic_checker_sort_arrow_16 // Transfer icon
            else -> FR.drawable.ic_checker_sort_arrow_16
        }
    }
}
