package com.vietinbank.feature_checker.ui.component.confirmchecker

import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItem
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItemType
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmUiModel
import javax.inject.Inject

class CustomsInlandConfirmUIGenerator @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : ITransactionConfirmUIGenerator {
    override fun generateConfirmUI(
        transaction: TransactionDomain,
        confirmType: String,
        onFieldEvent: ((TransactionFieldEvent) -> Unit)?,
    ): ConfirmUiModel {
        val items = mutableListOf<ConfirmItem>()

        // Thêm header Thông tin nguời nộp thuế
        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin nguời nộp thuế",
            ),
        )

        if ("0" == transaction.payMethod) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Hình thức",
                    value = "Nộp cho chính đơn vị",
                ),
            )
        } else if ("1" == transaction.payMethod) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Hình thức",
                    value = "Nộp thay cho đơn vị khác",
                ),
            )
        }

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tên đơn vị nộp",
                value = transaction.payname ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Mã số thuế",
                value = transaction.payCode ?: "",
            ),
        )

        if (!transaction.idNumber.isNullOrEmpty()) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Số CMND/Hộ chiếu/CCCD",
                    value = transaction.idNumber ?: "",
                ),
            )
        }

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Địa chỉ",
                value = transaction.payAddress ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Từ tài khoản",
                value = transaction.fromAccountNo ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Ngân hàng",
                value = transaction.branchName ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.SPACER,
            ),
        )

        // Thêm header Thông tin chi tiết nộp NSNN
        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin chi tiết nộp NSNN",
            ),
        )

        transaction.items?.forEach { file ->
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Tổng số tiền thực nộp",
                    value = Utils.g().getDotMoneyHasCcy(
                        file.amount ?: "",
                        file.currency ?: "",
                    ),
                    subValue = moneyHelper.convertAmountToWords(
                        file.amount ?: "",
                        file.currency ?: "",
                    ),
                ),
            )
        }

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Phí giao dịch",
                value = transaction.feeAmount ?: "",
            ),
        )

        transaction.items?.forEach { file ->
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Nội dung nộp thuế",
                    value = file.content ?: "",
                ),
            )

            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Mã NDKT (TM)",
                    value = file.businessCode ?: "",
                ),
            )

            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Kỳ thuế",
                    value = file.taxPeriod ?: "",
                ),
            )
        }

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Số tiền",
                value = transaction.amount ?: "",
                isHighlighted = true,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.SPACER,
            ),
        )

        return ConfirmUiModel(
            screenTitle = if (confirmType == Tags.APPROVE) "Duyệt" else "Từ chối",
            items = items,
            transactionIds = listOfNotNull(transaction.mtId),
            serviceType = transaction.serviceType ?: "CT",
            tranType = transaction.tranType ?: "",
            groupType = transaction.groupType ?: "",
        )
    }

    override fun generateBatchConfirmUI(
        transactions: List<TransactionListDomain>,
        totalAmount: String,
        totalFee: String,
        confirmType: String,
        createdDate: String,
    ): ConfirmUiModel {
        val items = mutableListOf<ConfirmItem>()

        // Header info giao dịch
        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin giao dịch chuyển khoản",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng số giao dịch",
                value = transactions.size.toString(),
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng số tiền chuyển",
                value = totalAmount,
                isHighlighted = true,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng phí giao dịch",
                value = totalFee,
            ),
        )

        // Lấy danh sách ID giao dịch
        val transactionIds = transactions.mapNotNull { it.mtId }

        // Lấy service type và tran type
        val serviceType = transactions.firstOrNull()?.serviceType ?: ""
        val tranType = transactions.firstOrNull()?.tranType ?: ""
        val groupType = transactions.firstOrNull()?.groupType ?: ""

        return ConfirmUiModel(
            screenTitle = if (confirmType == Tags.APPROVE) "Duyệt" else "Từ chối",
            items = items,
            transactionIds = transactionIds,
            serviceType = serviceType,
            tranType = tranType,
            groupType = groupType,
        )
    }
}