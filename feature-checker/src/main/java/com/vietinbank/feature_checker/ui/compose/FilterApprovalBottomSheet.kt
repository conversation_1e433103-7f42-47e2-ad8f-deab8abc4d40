package com.vietinbank.feature_checker.ui.compose

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_domain.models.checker.FilterApproveDomain
import com.vietinbank.core_domain.models.manage.TSortDomain
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationCategorySection
import com.vietinbank.core_ui.components.FoundationChips
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.foundation.textfield.FoundationEditText
import com.vietinbank.core_ui.components.foundation.textfield.InputType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.dismissKeyboardOnClickOutside
import com.vietinbank.core_ui.utils.glassBottomGradient
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_checker.ui.fragment.ApprovalListViewModel.Companion.INPUT_END_AMOUNT
import com.vietinbank.feature_checker.ui.fragment.ApprovalListViewModel.Companion.INPUT_START_AMOUNT
import com.vietinbank.feature_checker.ui.fragment.ApprovalListViewModel.Companion.SELECTOR_DATE_RANGE
import com.vietinbank.feature_checker.ui.fragment.ApprovalListViewModel.Companion.SELECTOR_DATE_TRANSFER
import com.vietinbank.feature_checker.ui.fragment.FilterApprovalAction
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun FilterApprovalBottomSheet(
    visible: Boolean,
    filterModel: FilterApproveDomain?,
    onAction: ((FilterApprovalAction) -> Unit),
) {
    val dateItems = remember {
        listOf(
            R.string.account_detail_transaction_all to "",
            R.string.transaction_transfer_scheduled to "Y",
            R.string.transaction_transfer_immediate to "N",
        )
    }

    val dateRangeItems = remember {
        listOf(
            R.string.account_detail_7_days to Tags.DAY_7,
            R.string.account_detail_15_days to Tags.DAY_15,
            R.string.account_detail_30_days to Tags.DAY_30,
        )
    }

    BaseBottomSheet<String>(
        visible = visible,
        onDismissRequest = {
            onAction(FilterApprovalAction.TapShowFilter(false))
        },
        onResult = {},
        allowTouchDismiss = true,
        secureFlag = true,
        containerPadding = PaddingValues(0.dp),
        extraBottomSpacing = 0.dp,
    ) { action ->

        val focusManager = LocalFocusManager.current

        Column(modifier = Modifier.dismissKeyboardOnClickOutside()) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f, false)
                    .clip(RoundedCornerShape(FDS.Sizer.Radius.radius32))
                    .background(FDS.Colors.backgroundBgContainer)
                    .padding(vertical = FDS.Sizer.Gap.gap24),
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = FDS.Sizer.Gap.gap16)
                        .padding(horizontal = FDS.Sizer.Gap.gap24),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween,
                ) {
                    FoundationText(
                        text = stringResource(R.string.manager_filter_option),
                        style = FDS.Typography.headingH3,
                        color = FDS.Colors.characterHighlighted,
                    )

                    Image(
                        modifier = Modifier.safeClickable {
                            onAction(FilterApprovalAction.TapReset)
                        },
                        painter = painterResource(R.drawable.ic_common_filter_reset_24),
                        contentDescription = "",
                    )
                }

                FoundationDivider()

                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = FDS.Sizer.Gap.gap16)
                        .padding(horizontal = FDS.Sizer.Gap.gap24),
                ) {
                    item {
                        FoundationCategorySection(
                            title = stringResource(R.string.manager_sort),
                            value = filterModel?.sortField?.name,
                            isShowLine = true,
                            icon = R.drawable.ic_right,
                        ) { onAction(FilterApprovalAction.TapShowSort(true)) }

                        FoundationText(
                            modifier = Modifier.padding(top = FDS.Sizer.Gap.gap24),
                            text = stringResource(R.string.transaction_label_transfer_time),
                            style = FDS.Typography.bodyB2Emphasized,
                            color = FDS.Colors.characterSecondary,
                        )

                        LazyRow(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = FDS.Sizer.Gap.gap8),
                            horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
                        ) {
                            items(dateItems) { (dateId, dateType) ->
                                FoundationChips(
                                    text = stringResource(dateId),
                                    isSelector = filterModel?.dateTransfer == dateType,
                                ) {
                                    onAction(
                                        FilterApprovalAction.TapChip(
                                            SELECTOR_DATE_TRANSFER,
                                            dateType,
                                        ),
                                    )
                                }
                            }
                        }

                        FoundationText(
                            modifier = Modifier.padding(top = FDS.Sizer.Gap.gap24),
                            text = stringResource(R.string.checker_filter_date_init),
                            style = FDS.Typography.bodyB2Emphasized,
                            color = FDS.Colors.characterSecondary,
                        )

                        LazyRow(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = FDS.Sizer.Gap.gap8),
                            horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
                        ) {
                            items(dateRangeItems) { (rangeId, rangeType) ->
                                FoundationChips(
                                    text = stringResource(rangeId),
                                    isSelector = filterModel?.dateRange == rangeType.toString(),
                                ) {
                                    onAction(
                                        FilterApprovalAction.TapChip(
                                            SELECTOR_DATE_RANGE,
                                            rangeType.toString(),
                                        ),
                                    )
                                }
                            }
                        }

                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = FDS.Sizer.Gap.gap16),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
                        ) {
                            FoundationCategorySection(
                                modifier = Modifier.weight(1f),
                                title = stringResource(R.string.account_detail_start_date),
                                value = filterModel?.startDate,
                                isShowLine = true,
                            ) { onAction(FilterApprovalAction.TapShowPicker(true)) }

                            FoundationCategorySection(
                                modifier = Modifier.weight(1f),
                                title = stringResource(R.string.account_detail_end_date),
                                value = filterModel?.endDate,
                                isShowLine = true,
                            ) { onAction(FilterApprovalAction.TapShowPicker(true)) }
                        }

                        FoundationText(
                            modifier = Modifier.padding(top = FDS.Sizer.Gap.gap24),
                            text = stringResource(R.string.maker_transfer_dashboard_amount),
                            style = FDS.Typography.bodyB2Emphasized,
                            color = FDS.Colors.characterSecondary,
                        )

                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = FDS.Sizer.Gap.gap8),
                            horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
                        ) {
                            FoundationEditText(
                                modifier = Modifier.weight(1f),
                                value = filterModel?.startAmount ?: "",
                                onValueChange = {
                                    onAction(
                                        FilterApprovalAction.TapInput(
                                            INPUT_START_AMOUNT,
                                            it,
                                        ),
                                    )
                                },
                                inputType = InputType.AMOUNT,
                                placeholder = stringResource(R.string.account_filter_min_amount),
                                hintText = stringResource(R.string.maker_transfer_dashboard_amount),
                                showBottomBorder = true,
                                showCharacterCounter = false,
                                maxLength = 19,
                                imeAction = ImeAction.Done,
                                keyboardActions = KeyboardActions(
                                    onDone = { focusManager.clearFocus() },
                                ),
                            )

                            FoundationEditText(
                                modifier = Modifier.weight(1f),
                                value = filterModel?.endAmount ?: "",
                                onValueChange = {
                                    onAction(
                                        FilterApprovalAction.TapInput(
                                            INPUT_END_AMOUNT,
                                            it,
                                        ),
                                    )
                                },
                                inputType = InputType.AMOUNT,
                                placeholder = stringResource(R.string.account_filter_max_amount),
                                hintText = stringResource(R.string.maker_transfer_dashboard_amount),
                                showBottomBorder = true,
                                showCharacterCounter = false,
                                maxLength = 19,
                                imeAction = ImeAction.Done,
                                keyboardActions = KeyboardActions(
                                    onDone = { focusManager.clearFocus() },
                                ),
                            )
                        }
                    }
                }
            }

            // Bottom buttons section with gradient background (aligned with NextApproverBottomSheet)
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .glassBottomGradient(
                        endColor = FDS.Colors.blue900,
                        alpha = 0.675f,
                    )
                    .padding(
                        top = FDS.Sizer.Padding.padding16,
                        bottom = FDS.Sizer.Padding.padding16,
                        start = FDS.Sizer.Padding.padding16,
                        end = FDS.Sizer.Padding.padding16,
                    ),
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
                ) {
                    FoundationButton(
                        modifier = Modifier.weight(1f),
                        text = stringResource(R.string.common_back),
                        isLightButton = false,
                        onClick = { onAction.invoke(FilterApprovalAction.TapShowFilter(false)) },
                    )

                    FoundationButton(
                        modifier = Modifier.weight(1f),
                        text = stringResource(R.string.common_apply),
                        onClick = { onAction.invoke(FilterApprovalAction.TapApply) },
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SortTransactionSheet(
    visible: Boolean = false,
    sortLst: List<TSortDomain>? = null,
    selectedItem: TSortDomain? = null,
    onAction: ((FilterApprovalAction) -> Unit),

) {
    BaseBottomSheet<TSortDomain>(
        visible = visible,
        onDismissRequest = {
            onAction(FilterApprovalAction.TapShowSort(false))
        },
        onResult = { },
        allowTouchDismiss = true,
        secureFlag = true,
    ) { onSelect ->
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(FDS.Sizer.Radius.radius32))
                .background(FDS.Colors.backgroundBgContainer)
                .padding(vertical = FDS.Sizer.Gap.gap20),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            FoundationText(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(R.string.manager_sort),
                style = FDS.Typography.headingH3,
                color = FDS.Colors.characterHighlighted,
                textAlign = TextAlign.Center,
            )

            FoundationDivider(modifier = Modifier.padding(top = FDS.Sizer.Gap.gap24))

            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f, fill = false)
                    .heightIn(max = FDS.Sizer.Dialog.maxListHeight),
                contentPadding = PaddingValues(horizontal = FDS.Sizer.Padding.padding24),
            ) {
                itemsIndexed(sortLst ?: emptyList()) { index, data ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = FDS.Sizer.Gap.gap8)
                            .safeClickable {
                                onAction(FilterApprovalAction.TapSelectSort(data))
                            },
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        FoundationText(
                            text = data.name ?: "",
                            color = FDS.Colors.characterPrimary,
                            style = FDS.Typography.bodyB1Emphasized,
                        )

                        Image(
                            painter = painterResource(
                                when {
                                    selectedItem?.name == data.name -> R.drawable.ic_common_radio_check_24
                                    else -> R.drawable.ic_common_radio_uncheck_24
                                },
                            ),
                            contentDescription = null,
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun FilterLockApprovalBottomSheet(
    visible: Boolean,
    filterModel: FilterApproveDomain?,
    onAction: ((FilterApprovalAction) -> Unit),
    sortLst: List<TSortDomain>? = null,
    selectedItem: TSortDomain? = null,
) {
    val dateRangeItems = remember {
        listOf(
            R.string.account_detail_7_days to Tags.DAY_7,
            R.string.account_detail_15_days to Tags.DAY_15,
            R.string.account_detail_30_days to Tags.DAY_30,
        )
    }

    BaseBottomSheet<String>(
        visible = visible,
        onDismissRequest = {
            onAction(FilterApprovalAction.TapShowFilter(false))
        },
        onResult = {},
        allowTouchDismiss = true,
        secureFlag = true,
    ) { action ->

        Column(modifier = Modifier.dismissKeyboardOnClickOutside()) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f, false)
                    .clip(RoundedCornerShape(FDS.Sizer.Radius.radius32))
                    .background(FDS.Colors.backgroundBgContainer)
                    .padding(vertical = FDS.Sizer.Gap.gap24),
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = FDS.Sizer.Gap.gap16)
                        .padding(horizontal = FDS.Sizer.Gap.gap24),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween,
                ) {
                    FoundationText(
                        text = stringResource(R.string.manager_filter_option),
                        style = FDS.Typography.headingH3,
                        color = FDS.Colors.characterHighlighted,
                    )

                    Image(
                        modifier = Modifier.safeClickable {
                            onAction(FilterApprovalAction.TapReset)
                        },
                        painter = painterResource(R.drawable.ic_common_filter_reset_24),
                        contentDescription = "",
                    )
                }

                FoundationDivider()

                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = FDS.Sizer.Gap.gap16)
                        .padding(horizontal = FDS.Sizer.Gap.gap24),
                ) {
                    item {
                        FoundationText(
                            modifier = Modifier.fillMaxWidth(),
                            text = stringResource(R.string.manager_sort),
                            style = FDS.Typography.bodyB2Emphasized,
                            color = FDS.Colors.characterSecondary,
                        )

                        LazyColumn(
                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f, fill = false)
                                .heightIn(max = FDS.Sizer.Dialog.maxListHeight),
                        ) {
                            itemsIndexed(sortLst ?: emptyList()) { index, data ->
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = FDS.Sizer.Gap.gap8)
                                        .safeClickable {
                                            onAction(FilterApprovalAction.TapSelectSort(data))
                                        },
                                    horizontalArrangement = Arrangement.Start,
                                    verticalAlignment = Alignment.CenterVertically,
                                ) {
                                    Image(
                                        painter = painterResource(
                                            when {
                                                selectedItem?.name == data.name -> R.drawable.ic_common_radio_check_24
                                                else -> R.drawable.ic_common_radio_uncheck_24
                                            },
                                        ),
                                        modifier = Modifier
                                            .padding(end = FDS.Sizer.Gap.gap8),
                                        contentDescription = null,
                                    )
                                    FoundationText(
                                        text = data.name ?: "",
                                        color = FDS.Colors.characterPrimary,
                                        style = FDS.Typography.bodyB2,
                                    )
                                }
                            }
                        }

                        FoundationText(
                            modifier = Modifier.padding(top = FDS.Sizer.Gap.gap24),
                            text = stringResource(R.string.checker_filter_date_init),
                            style = FDS.Typography.bodyB2Emphasized,
                            color = FDS.Colors.characterSecondary,
                        )

                        LazyRow(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = FDS.Sizer.Gap.gap8),
                            horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
                        ) {
                            items(dateRangeItems) { (rangeId, rangeType) ->
                                FoundationChips(
                                    text = stringResource(rangeId),
                                    isSelector = filterModel?.dateRange == rangeType.toString(),
                                ) {
                                    onAction(
                                        FilterApprovalAction.TapChip(
                                            SELECTOR_DATE_RANGE,
                                            rangeType.toString(),
                                        ),
                                    )
                                }
                            }
                        }

                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = FDS.Sizer.Gap.gap16),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
                        ) {
                            FoundationCategorySection(
                                modifier = Modifier.weight(1f),
                                title = stringResource(R.string.account_detail_start_date),
                                value = filterModel?.startDate,
                                isShowLine = true,
                            ) { onAction(FilterApprovalAction.TapShowPicker(true)) }

                            FoundationCategorySection(
                                modifier = Modifier.weight(1f),
                                title = stringResource(R.string.account_detail_end_date),
                                value = filterModel?.endDate,
                                isShowLine = true,
                            ) { onAction(FilterApprovalAction.TapShowPicker(true)) }
                        }
                    }
                }
            }

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = FDS.Sizer.Gap.gap16)
                    .padding(horizontal = FDS.Sizer.Gap.gap24),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
            ) {
                FoundationButton(
                    modifier = Modifier.weight(1f),
                    text = stringResource(R.string.common_back),
                    isLightButton = false,
                    onClick = { onAction.invoke(FilterApprovalAction.TapShowFilter(false)) },
                )

                FoundationButton(
                    modifier = Modifier.weight(1f),
                    text = stringResource(R.string.common_apply),
                    onClick = { onAction.invoke(FilterApprovalAction.TapApply) },
                )
            }
        }
    }
}
