package com.vietinbank.feature_checker.ui.component.approvallist

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_domain.models.checker.TransactionItemUiModel
import com.vietinbank.core_ui.components.FoundationSelector
import com.vietinbank.core_ui.components.SelectorType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.feature_checker.R
import javax.inject.Inject

class NewTraceTransactionRenderer @Inject constructor() : ITransactionContentRenderer {
    @Composable
    override fun RenderContent(
        transaction: TransactionItemUiModel,
        isSelectionMode: Boolean,
        isSelected: Boolean,
        onChangeClick: () -> Unit,
    ) {
        Column(modifier = Modifier.fillMaxWidth()) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                if (isSelectionMode) {
                    FoundationSelector(
                        modifier = Modifier.padding(end = FoundationDesignSystem.Sizer.Gap.gap4),
                        boxType = SelectorType.Checkbox,
                        isSelected = isSelected,
                        onClick = onChangeClick,
                    )
                }
                // Left side: label + value with mixed styles
                val transactionId = transaction.referenceNo.ifEmpty { transaction.mtID }
                val label = stringResource(id = com.vietinbank.core_ui.R.string.transaction_number_label) + ": "
                FoundationText(
                    text = buildAnnotatedString {
                        withStyle(style = SpanStyle(color = FoundationDesignSystem.Colors.characterSecondary)) {
                            append(label)
                        }
                        withStyle(
                            style = SpanStyle(
                                color = FoundationDesignSystem.Colors.characterPrimary,
                                fontWeight = FontWeight.Bold,
                            ),
                        ) {
                            append(transactionId)
                        }
                    },
                    style = FoundationDesignSystem.Typography.captionCaptionL,
                    modifier = Modifier.weight(1f),
                )
            }

            Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))

            Row(
                horizontalArrangement = Arrangement.spacedBy(FoundationDesignSystem.Sizer.Gap.gap4),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Icon(
                    painter = painterResource(R.drawable.ic_checker_sort_arrow_16),
                    contentDescription = null,
                    tint = FoundationDesignSystem.Colors.characterPrimary,
                    modifier = Modifier.size(FoundationDesignSystem.Sizer.Icon.icon16),
                )

                FoundationText(
                    text = transaction.tranTypeName,
                    style = FoundationDesignSystem.Typography.bodyB2Emphasized,
                    color = FoundationDesignSystem.Colors.characterPrimary,
                )
            }

            Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))

            transaction.details.forEach { detail ->
                if (detail.label.isNotEmpty()) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                    ) {
                        FoundationText(
                            text = detail.label,
                            style = FoundationDesignSystem.Typography.captionCaptionL,
                            color = FoundationDesignSystem.Colors.characterSecondary,
                            modifier = Modifier.width(FoundationDesignSystem.Sizer.Padding.padding100),
                        )
                        FoundationText(
                            text = detail.value,
                            style = FoundationDesignSystem.Typography.captionCaptionL,
                            color = FoundationDesignSystem.Colors.characterPrimary,
                            textAlign = TextAlign.End,
                            modifier = Modifier.weight(1f),
                        )
                    }
                    Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))
                } else {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                    ) {
                        FoundationText(
                            text = detail.value,
                            style = FoundationDesignSystem.Typography.captionCaptionL,
                            color = FoundationDesignSystem.Colors.characterPrimary,
                            textAlign = TextAlign.End,
                            modifier = Modifier.weight(1f),
                        )
                    }
                    Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))
                }
            }

            if (transaction.status.isNotEmpty()) {
                val statusText = when (transaction.status) {
                    "1.1", Tags.STATUS_AWAITING_APPROVAL -> stringResource(R.string.feature_checker_status_awaiting_approval)
                    "05", Tags.STATUS_SUCCESS -> stringResource(R.string.feature_checker_status_success)
                    "01", Tags.STATUS_USER_REJECT -> stringResource(R.string.feature_checker_status_reject)
                    else -> stringResource(R.string.feature_checker_status_processing)
                }

                val statusColor = when (transaction.status) {
                    "1.1", Tags.STATUS_AWAITING_APPROVAL -> FoundationDesignSystem.Colors.warning
                    "05", Tags.STATUS_SUCCESS -> FoundationDesignSystem.Colors.success
                    "01", Tags.STATUS_USER_REJECT -> FoundationDesignSystem.Colors.error
                    else -> FoundationDesignSystem.Colors.characterSecondary
                }

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                ) {
                    FoundationText(
                        text = stringResource(R.string.feature_checker_status),
                        style = FoundationDesignSystem.Typography.captionCaptionL,
                        color = FoundationDesignSystem.Colors.characterSecondary,
                    )
                    FoundationText(
                        text = statusText,
                        style = FoundationDesignSystem.Typography.captionCaptionLBold,
                        color = statusColor,
                    )
                }
            }
        }
    }
}