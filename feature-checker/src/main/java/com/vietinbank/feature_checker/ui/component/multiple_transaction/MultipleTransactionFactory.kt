package com.vietinbank.feature_checker.ui.component.multiple_transaction

import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class MultipleTransactionFactory @Inject constructor(
    private val defaultRenderer: MultipleDefaultRender,
    private val transferRenderer: MultipleTransferRender,
) {
    fun getRender(tranType: String): IMultipleTransactionRender {
        return when (tranType) {
            "in", "ou", "np", "sn" -> transferRenderer
            else -> defaultRenderer
        }
    }
}