package com.vietinbank.feature_checker.ui.fragment

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.livedata.SingleLiveEvent
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.ApproveDomain
import com.vietinbank.core_domain.models.checker.ApproveParams
import com.vietinbank.core_domain.models.checker.ApproveTransactionDomain
import com.vietinbank.core_domain.models.checker.ConfirmTransactionDomain
import com.vietinbank.core_domain.models.checker.SuccessItemDomain
import com.vietinbank.core_domain.models.checker.SuccessTitleDomain
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItemType
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmUiModel
import com.vietinbank.core_domain.models.home.UpdateEkycParams
import com.vietinbank.core_domain.models.maker.ApproverDomains
import com.vietinbank.core_domain.usecase.checker.CheckerUserCase
import com.vietinbank.core_domain.usecase.checker.validate.ValidateKeypassUseCase
import com.vietinbank.core_domain.usecase.home.HomeUseCase
import com.vietinbank.core_domain.validator.cfkeypass.KeypassValidator
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.feature_checker.ui.component.success.SuccessHelper
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ConfirmKeypassViewModel @Inject constructor(
    val useCase: CheckerUserCase,
    private val homeUseCase: HomeUseCase,
    private val moneyHelper: MoneyHelper,
    override val resourceProvider: IResourceProvider,
    private val validateKeypassUseCase: ValidateKeypassUseCase,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
) : BaseViewModel() {

    var challengeCode by mutableStateOf("")
    var confirmType by mutableStateOf("")
    var registerEkycID = ""

    // Lưu trữ transaction từ Bundle
    var originalTransaction: TransactionListDomain? = null
        private set

    // Trạng thái đang xử lý
    private val _isProcessing = MutableLiveData<Boolean>(false)
    val isProcessing: LiveData<Boolean> = _isProcessing

    // Events - sử dụng SingleLiveEvent để tránh trigger lặp lại
    private val _errorEvent = SingleLiveEvent<String>()
    val errorEvent: LiveData<String> = _errorEvent

    private val _doApproveResponse = SingleLiveEvent<Resource<ApproveDomain>>()
    val doApproveResponse: SingleLiveEvent<Resource<ApproveDomain>> get() = _doApproveResponse

    // LiveData để báo lỗi Approve
    private val _approveErrorEvent = SingleLiveEvent<AppException>()
    val approveErrorEvent: LiveData<AppException> = _approveErrorEvent

    // Dữ liệu giao dịch để hiển thị
    var transaction: ConfirmTransactionDomain = ConfirmTransactionDomain()
        private set

    private var confirmUiModel: ConfirmUiModel? = null

    // Selected approver
    var selectedNextApprover: List<ApproverDomains>? = emptyList()

    // Thêm state validation
    private val _keypassValidationState = MutableLiveData<KeypassValidator.ValidationResult>()
    val keypassValidationState: LiveData<KeypassValidator.ValidationResult> = _keypassValidationState

    // Thêm state để lưu nội dung Keypass
    private val _keypassValue = MutableStateFlow("")
    val keypassValue = _keypassValue.asStateFlow()

    private val _apiEvent = Channel<ApiEvent>(Channel.UNLIMITED)
    val apiEvent = _apiEvent.receiveAsFlow()

    var transactionsMtID = ""

    // Cập nhật giá trị Keypass
    fun updateKeypassValue(value: String) {
        _keypassValue.value = value
    }

    // Validate Keypass trước khi submit
    fun validateKeypass(): Boolean {
        val result = validateKeypassUseCase(_keypassValue.value)
        _keypassValidationState.postValue(result)
        return result is KeypassValidator.ValidationResult.Success
    }

    fun onConfirmClick() {
        if (validateKeypass()) {
            when (confirmType) {
                Tags.APPROVE -> doApprove(_keypassValue.value)
                Tags.UPDATE, Tags.REGISTER_EKYC -> updateEkyc(_keypassValue.value)
            }
        }
    }

    /**
     * Xử lý dữ liệu ConfirmUiModel
     */
    fun processConfirmUiModel(uiModel: ConfirmUiModel) {
        confirmUiModel = uiModel
        printLog("Đã xử lý ConfirmUiModel với ${uiModel.items.size} items")
    }

    /**
     * Xử lý dữ liệu giao dịch
     */
    fun processTransaction(transDomain: TransactionListDomain) {
        originalTransaction = transDomain
        try {
            printLog("Xử lý giao dịch: ${transDomain.mtId}")

            // Chuyển đổi sang model hiển thị
            transaction = ConfirmTransactionDomain(
                fromAccountNo = transDomain.fromAccountNo,
                currency = transDomain.currency,
                toAccountNo = transDomain.toAccountNo,
                receiveName = transDomain.receiveName,
                receiveBankName = transDomain.receiveBankName,
                amount = transDomain.amount,
                feeAmount = transDomain.feeAmount,
                feePayMethodDesc = transDomain.feePayMethodDesc,
                remark = transDomain.remark,
                createdDate = transDomain.createdDate,
            )

            printLog("Đã chuyển đổi thành công giao dịch: ${transDomain.mtId}")
        } catch (e: Exception) {
            printLog("Lỗi xử lý giao dịch: ${e.message}")
            e.printStackTrace()
            _errorEvent.postValue("Không thể xử lý dữ liệu giao dịch: ${e.message}")
        }
    }

    /**
     * doApprove
     */
    fun doApprove(otpValue: String) {
        _isProcessing.postValue(true)

        confirmUiModel?.let { uiModel ->
            launchJob(showLoading = true) {
                val nextApproversList = ArrayList<ApproverDomains>()
                selectedNextApprover?.forEach { approver ->
                    val updatedApprover = approver.copy()
                    updatedApprover.isSelected = true
                    nextApproversList.add(updatedApprover)
                }
                val res = useCase.doApprove(
                    ApproveParams(
                        username = userProf.getUserName().toString(),
                        token = otpValue,
                        softOtpTransId = "",
                        authenType = "K",
                        requestType = "0",
                        serviceType = "CT",
                        tranType = uiModel.tranType,
                        transactions = ArrayList(uiModel.transactionIds),
                        transactionsTp = arrayListOf(),
                        nextApprovers = nextApproversList,
                    ),
                )
                handleResource(res) { data ->
                    // Capture tranDate from API response
                    transactionDate = data.tranDate
                    _doApproveResponse.postValue(Resource.Success(data))
                }
            }
        } ?: run {
            _isProcessing.postValue(false)
            showError("Không có thông tin giao dịch để xác nhận OTP")
        }
    }

    private fun updateEkyc(otpValue: String) {
        launchJob {
            val params = createEkycParams(otpValue)
            val res = homeUseCase.updateEkyc(params)

            printLog("update ekyc keypass $res")
            handleResource(res) {
                vmScope.launch {
                    _apiEvent.send(ApiEvent.Success)
                }
            }
        }
    }

    private fun createEkycParams(otpValue: String): UpdateEkycParams {
        val isUpdate = confirmType == Tags.UPDATE
        return UpdateEkycParams(
            username = userProf.getUserName().toString(),
            authenType = "K",
            softOtpTransId = "",
            token = otpValue,
            tranType = if (isUpdate) "UTH" else "NEW_STH",
            serviceType = if (isUpdate) "UTH" else "NEW_STH",
            transactions = listOf(if (isUpdate) transactionsMtID else registerEkycID),
        )
    }

    // Store the transaction date from API response
    private var transactionDate: String? = null

    /**
     * Lấy danh sách mục thông tin cho màn hình thành công
     * Tùy vào loại giao dịch mà tạo danh sách khác nhau
     */
    fun getSuccessList(): List<SuccessItemDomain> {
        // Nếu không có confirmUiModel, trả về danh sách rỗng
        confirmUiModel?.let { uiModel ->
            // Lấy mtID từ danh sách transactionIds
            val mtId = uiModel.transactionIds.firstOrNull() ?: ""

            // Batch transfer
            val isBatch = uiModel.transactionIds.size > 1

            return SuccessHelper
                .createSuccessList(uiModel.tranType, mtId, uiModel.items, isBatch, transactionDate)
        } ?: return emptyList()
    }

    fun getSuccessTitle(trans: ApproveTransactionDomain?, message: String): SuccessTitleDomain {
        // Tìm item số tiền được highlight từ ConfirmUiModel
        val highlightedItem = confirmUiModel?.items?.find {
            it.type == ConfirmItemType.KEY_VALUE && it.isHighlighted
        }

        // Lấy giá trị amount, đảm bảo không null
        val amount = highlightedItem?.value ?: ""

        // Lấy giá trị subValue (số tiền bằng chữ) đã được tạo trước đó
        var amountText = highlightedItem?.subValue ?: ""

        // Xử lý trường hợp truyền số tiền bằng chữ không thành công
        if (amountText.isEmpty() && amount.isNotEmpty()) {
            // Thử tạo lại amountText từ amount
            try {
                // Nếu là nhiều dòng (nhiều loại tiền), xử lý riêng từng loại tiền
                if (amount.contains("\n")) {
                    val lines = amount.split("\n")
                    val resultBuilder = StringBuilder()

                    lines.forEachIndexed { index, line ->
                        // Tìm loại tiền tệ từ dòng
                        val currency = extractCurrency(line)

                        // Lấy số tiền sạch
                        val cleanAmount = cleanAmountString(line, currency)

                        // Chuyển đổi thành chữ
                        val lineInWords = moneyHelper.convertAmountToWords(cleanAmount, currency)
                        resultBuilder.append(lineInWords)

                        // Thêm "và" giữa các dòng (nếu không phải dòng cuối)
                        if (index < lines.size - 1) {
                            resultBuilder.append(" và ")
                        }
                    }
                    amountText = resultBuilder.toString()
                } else {
                    // Trường hợp đơn tiền tệ
                    val currency = extractCurrency(amount)
                    val cleanAmount = cleanAmountString(amount, currency)
                    amountText = moneyHelper.convertAmountToWords(cleanAmount, currency)
                }
            } catch (e: Exception) {
                printLog("Lỗi khi convert amount: ${e.message}")
            }
        }

        return SuccessTitleDomain(
            title = message,
            status = trans?.status,
            statusTrans = trans?.statusTrans,
            amountText = amountText,
            amountTitle = amount,
        )
    }

    /**
     * Trích xuất loại tiền tệ từ chuỗi số tiền
     */
    private fun extractCurrency(amountString: String): String {
        return when {
            amountString.contains(" VND") -> "VND"
            amountString.contains(" USD") -> "USD"
            amountString.contains(" EUR") -> "EUR"
            amountString.contains(" GBP") -> "GBP"
            amountString.contains(" JPY") -> "JPY"
            amountString.contains(" CAD") -> "CAD"
            amountString.contains(" AUD") -> "AUD"
            amountString.contains(" SGD") -> "SGD"
            amountString.contains(" HKD") -> "HKD"
            amountString.contains(" CNY") -> "CNY"
            else -> "VND" // Mặc định là VND
        }
    }

    /**
     * Làm sạch chuỗi số tiền, loại bỏ định dạng và đơn vị tiền tệ
     */
    private fun cleanAmountString(amountString: String, currency: String): String {
        return amountString.replace(" $currency", "").replace(",", "").trim()
    }

    /**
     * Hiển thị lỗi
     */
    fun showError(message: String) {
        _errorEvent.postValue(message)
    }
}