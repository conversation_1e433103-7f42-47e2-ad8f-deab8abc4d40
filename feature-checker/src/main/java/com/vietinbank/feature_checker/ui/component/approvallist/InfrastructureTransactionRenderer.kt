package com.vietinbank.feature_checker.ui.component.approvallist

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_domain.models.checker.TransactionItemUiModel
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.feature_checker.R
import com.vietinbank.feature_checker.ui.component.KeyValueRow
import javax.inject.Inject

class InfrastructureTransactionRenderer @Inject constructor() : ITransactionContentRenderer {
    @Composable
    override fun RenderContent(
        transaction: TransactionItemUiModel,
        isSelectionMode: Boolean,
    ) {
        Column(modifier = Modifier.fillMaxWidth()) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                BaseText(
                    text = transaction.tranTypeName.trim(),
                    color = AppColors.blue02,
                    textSize = 14.sp,
                    fontCus = 5,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f),
                )

                BaseText(
                    modifier = Modifier.padding(start = 16.dp),
                    text = transaction.amount + " " + transaction.ccy,
                    fontCus = 5,
                    color = AppColors.primaryRed,
                    textSize = 14.sp,
                    rightDrawable = R.drawable.ic_more,
                    rightDrawableSize = 24.dp,
                    textAlign = TextAlign.End,
                )
            }

            Spacer(modifier = Modifier.height(10.dp))

            // Chi tiết giao dịch - không bị ảnh hưởng bởi icon ở trên
            transaction.details.forEach { detail ->
                if (detail.label.isNotEmpty()) {
                    KeyValueRow(
                        label = detail.label,
                        value = detail.value,
                        subValue = detail.subValue ?: "",
                        iconResource = detail.iconResource,
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                } else {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.End,
                    ) {
                        BaseText(
                            text = detail.value,
                            color = if (detail.isWarning) Color.Red else AppColors.textPrimary,
                            textSize = 14.sp,
                            textAlign = TextAlign.End,
                            maxLines = 3,
                            overflow = TextOverflow.Ellipsis,
                        )
                    }
                    Spacer(modifier = Modifier.height(4.dp))
                }
            }
        }
    }
}