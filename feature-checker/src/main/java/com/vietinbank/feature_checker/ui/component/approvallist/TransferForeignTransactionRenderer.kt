package com.vietinbank.feature_checker.ui.component.approvallist

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_common.extensions.getAmountServer
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.checker.TransactionItemUiModel
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.feature_checker.R
import com.vietinbank.feature_checker.ui.component.KeyValueRow
import javax.inject.Inject

class TransferForeignTransactionRenderer @Inject constructor() : ITransactionContentRenderer {
    @Composable
    override fun RenderContent(transaction: TransactionItemUiModel, isSelectionMode: Boolean) {
        Column(modifier = Modifier.fillMaxWidth()) {
            // Transaction type and amount row
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                // Tên giao dịch (căn trái)
                Column(modifier = Modifier.weight(1f)) {
                    BaseText(
                        text = transaction.tranTypeName,
                        color = Color(0xFF0066B3),
                        textSize = 16.sp,
                        fontCus = 5,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                    )
                }

                Spacer(modifier = Modifier.width(4.dp))

                BaseText(
                    text = Utils.g().getDotMoneyHasCcy(
                        transaction.amount.getAmountServer(),
                        transaction.ccy,
                    ),
                    color = Color.Red,
                    textSize = 16.sp,
                    fontCus = 1,
                )

                Icon(
                    painter = painterResource(id = R.drawable.ic_more),
                    contentDescription = "Menu",
                    tint = Color.Unspecified,
                    modifier = Modifier
                        .size(24.dp)
                        .padding(start = 4.dp),
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Chi tiết giao dịch - không bị ảnh hưởng bởi icon ở trên
            transaction.details.forEach { detail ->
                if (detail.label.isNotEmpty()) {
                    KeyValueRow(
                        label = detail.label,
                        value = detail.value,
                        subValue = detail.subValue ?: "",
                        iconResource = detail.iconResource,
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                } else {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.End,
                    ) {
                        BaseText(
                            text = detail.value,
                            color = if (detail.isWarning) Color.Red else AppColors.textPrimary,
                            textSize = 14.sp,
                            textAlign = TextAlign.End,
                            maxLines = 3,
                            overflow = TextOverflow.Ellipsis,
                        )
                    }
                    Spacer(modifier = Modifier.height(4.dp))
                }
            }
        }
    }
}