package com.vietinbank.feature_checker.ui.fragment

import android.os.Bundle
import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.ApproveDomain
import com.vietinbank.core_domain.models.checker.ApproveParams
import com.vietinbank.core_domain.models.checker.GenKeyPassChallengeCodeDomain
import com.vietinbank.core_domain.models.checker.GenKeyPassChallengeCodeParams
import com.vietinbank.core_domain.models.checker.GenSOTPTransCodeDomain
import com.vietinbank.core_domain.models.checker.GenSOTPTransCodeParams
import com.vietinbank.core_domain.models.checker.MultipleConfirmUIState
import com.vietinbank.core_domain.models.checker.MultipleResultUIState
import com.vietinbank.core_domain.models.checker.MultipleStatusDomain
import com.vietinbank.core_domain.models.checker.RejectParams
import com.vietinbank.core_domain.models.checker.maping.confirm.MultipleConfirmUIModel
import com.vietinbank.core_domain.softotp.IPinResult
import com.vietinbank.core_domain.softotp.ISoftManager
import com.vietinbank.core_domain.softotp.ISoftResult
import com.vietinbank.core_domain.usecase.checker.CheckerUserCase
import com.vietinbank.core_domain.usecase.home.HomeUseCase
import com.vietinbank.core_domain.usecase.transfer.CSatUseCase
import com.vietinbank.feature_checker.ui.component.approvallist.TransactionRendererFactory
import com.vietinbank.feature_checker.ui.component.multiple_transaction.MultipleTransactionFactory
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class MultipleApprovalConfirmViewModel
@Inject constructor(
    val useCase: CheckerUserCase,
    val homeUseCase: HomeUseCase,
    val factory: TransactionRendererFactory,
    val renderMultipleFactory: MultipleTransactionFactory,
    override val cSatUseCase: CSatUseCase,
    private val softManager: ISoftManager, // Added for PIN verification and OTP generation
    private val moneyHelper: MoneyHelper,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
) : CSatViewModel(
    cSatUseCase,
    sessionManager,
    resourceProvider,
    appConfig,
    userProf,
    ottSetupService,
) {
    // new ui
    private val _uiConfirmState = MutableStateFlow<MultipleConfirmUIState>(MultipleConfirmUIState())
    val uiConfirmState = _uiConfirmState.asStateFlow()

    // lay type man confirm
    fun getTranType() = _uiConfirmState.value.confirmModel?.tranType ?: ""
    fun getServiceType() = _uiConfirmState.value.confirmModel?.serviceType ?: ""
    fun getGroupType() = _uiConfirmState.value.confirmModel?.groupType ?: ""

    fun updateConfirmState(uiModel: MultipleConfirmUIModel?) {
        _uiConfirmState.value = _uiConfirmState.value.copy(
            confirmModel = uiModel,
            renderUI = renderMultipleFactory.getRender(uiModel?.tranType),
        )
    }

    fun onChangeRejectSheet(isShow: Boolean) {
        _uiConfirmState.value = _uiConfirmState.value.copy(isShowRejectReason = isShow)
    }

    /**
     * Tạo Keypass Challenge Code
     */
    private val _genKeyPassChallengeCode = MutableSharedFlow<GenKeyPassChallengeCodeDomain>()
    val genKeyPassChallengeCode = _genKeyPassChallengeCode.asSharedFlow()

    fun genKeyPassChallengeCode() {
        launchJob(showLoading = true) {
            val res = useCase.genKeyPassChallengeCode(
                GenKeyPassChallengeCodeParams(
                    username = userProf.getUserName(),
                ),
            )
            handleResource(res) { data ->
                _genKeyPassChallengeCode.emit(data)
            }
        }
    }

    private val _genSOTPTransCode = MutableSharedFlow<GenSOTPTransCodeDomain>()
    val genSOTPTransCode = _genSOTPTransCode.asSharedFlow()

    /**
     * Tạo OTP Transaction Code
     */
    fun genSOTPTransCode() {
        val transactionIds = _uiConfirmState.value.confirmModel?.transactionIds
        if (transactionIds.isNullOrEmpty()) {
//            showError("Không có ID giao dịch để xác nhận")
            return
        }
        launchJob(showLoading = true) {
            val res = useCase.genSOTPTransCode(
                GenSOTPTransCodeParams(
                    cifno = userProf.getCifNo(),
                    userName = userProf.getUserName().toString(),
                    actionId = "approve",
                    groupType = _uiConfirmState.value.confirmModel?.groupType,
                    mtIds = transactionIds.joinToString(","),
                    reason = "",
                    transactionData = "1",
                    trxType = _uiConfirmState.value.confirmModel?.tranType,
                ),
            )
            handleResource(res) { responseData ->
                _uiConfirmState.value = _uiConfirmState.value.copy(
                    transactionIdSoft = responseData.transactionId,
                )
                _genSOTPTransCode.emit(responseData)
            }
        }
    }

    // ===== SOFT OTP PIN VERIFICATION & OTP GENERATION =====
    // Following Clean Architecture - business logic in ViewModel, not in Dialog
    private val _pinVerificationResult = MutableSharedFlow<Resource<Boolean>>()
    val pinVerificationResult = _pinVerificationResult.asSharedFlow()

    /**
     * Verify PIN for Soft OTP
     * Called when user enters PIN in SimplePinInputDialog
     */
    fun verifySoftOtpPin(pin: String) {
        printLog("ConfirmCheckerViewModel: Verifying PIN")
//        printLog("ConfirmCheckerViewModel: Current transactionID before PIN verify = $transactionID")
//        printLog("ConfirmCheckerViewModel: Current uiModel.transactionIds = ${_uiModel.value.transactionIds}")
        // Clear previous result

        // Set up PIN result listener
        softManager.setPinResultListener(object : IPinResult {
            override fun onSuccess() {
                printLog("ConfirmCheckerViewModel: PIN verified successfully")
                generateSoftOtp()
            }

            override fun onError(message: String?, code: Int?) {
                printLog("ConfirmCheckerViewModel: PIN verification failed - $message")
                viewModelScope.launch {
                    _pinVerificationResult.emit(
                        Resource.Error(
                            message = message ?: "Mã PIN không đúng",
                            code = code?.toString() ?: "PIN_ERROR",
                        ),
                    )
                }
            }
        })

        // Verify PIN
        softManager.verifyPin(pin)
    }

    /**
     * Generate Soft OTP after PIN verification
     * Called automatically after successful PIN verification
     */
    private val _otpGenerationResult = MutableSharedFlow<Resource<String>>()
    val otpGenerationResult = _otpGenerationResult.asSharedFlow()
    private fun generateSoftOtp() {
        printLog("ConfirmCheckerViewModel: Generating Soft OTP")
        // Set up OTP result listener BEFORE calling genSoftOtp
        softManager.setSoftResultListener(object : ISoftResult {
            override fun onSuccess(otpCode: String?, timeCount: Int?) {
                printLog("ConfirmCheckerViewModel: ISoftResult.onSuccess called - otpCode=$otpCode, timeCount=$timeCount")
                viewModelScope.launch {
                    if (!otpCode.isNullOrEmpty()) {
                        printLog("ConfirmCheckerViewModel: Emitting OTP success to Fragment")
                        _otpGenerationResult.emit(Resource.Success(otpCode))
                    } else {
                        printLog("ConfirmCheckerViewModel: OTP is empty or null")
                        _otpGenerationResult.emit(
                            Resource.Error(
                                message = "Không thể tạo mã OTP",
                                code = "OTP_EMPTY",
                            ),
                        )
                    }
                }
            }

            override fun isActive(isActive: Boolean?) {
                printLog("ConfirmCheckerViewModel: ISoftResult.isActive called - isActive=$isActive")
            }

            override fun onError(message: String?, code: Int?) {
                printLog("ConfirmCheckerViewModel: ISoftResult.onError called - message=$message, code=$code")
                viewModelScope.launch {
                    _otpGenerationResult.emit(
                        Resource.Error(
                            message = message ?: "Không thể tạo mã OTP",
                            code = code?.toString() ?: "OTP_ERROR",
                        ),
                    )
                }
            }
        })

        // Generate OTP with transaction data
        // Using same params as old ConfirmCheckerFragment
        val userId = userProf.getKeypassProfile() ?: userProf.getUserName()
        val transactionId =
            _uiConfirmState.value.transactionIdSoft // Already set from genSOTPTransCode
        val messageId = "" // Empty string like in old flow

        printLog("ConfirmCheckerViewModel: Calling softManager.genSoftOtp with params:")
        printLog("  - userId=$userId")
        printLog("  - transactionId=$transactionId")
        printLog("  - messageId=$messageId (empty like old flow)")

        // Call genSoftOtp with same params as old flow
        softManager.genSoftOtp(
            userId = userId,
            transactionId = transactionId,
            messageId = messageId,
        )

        printLog("ConfirmCheckerViewModel: softManager.genSoftOtp() called, waiting for callback...")
    }

    // Approve with Soft OTP
    // duyet theo lo khong can chon nguoi phe duyet tiep theo
    private val _doApproveResponse = MutableSharedFlow<ApproveDomain>()
    val doApproveResponse: SharedFlow<ApproveDomain> = _doApproveResponse.asSharedFlow()
    fun doApprove(otpCode: String) {
        val transactionIds = _uiConfirmState.value.confirmModel?.transactionIds
        // client show thong bao khong co giao dich
        if (transactionIds.isNullOrEmpty()) {
            onDisplayErrorMessage(
                AppException.ApiException(
                    requestPath = null,
                    rawResponseJson = null,
                    code = null,
                    subCode = null,
                    message = resourceProvider.getString(com.vietinbank.core_ui.R.string.checker_multiple_approve_not_found_id),
                ),
            )
            return
        }
        launchJob {
            val res = useCase.doApprove(
                ApproveParams(
                    username = userProf.getUserName().toString(),
                    token = otpCode,
                    softOtpTransId = _uiConfirmState.value.transactionIdSoft ?: "",
                    authenType = "S", // S for Soft OTP
                    requestType = null,
                    serviceType = _uiConfirmState.value.confirmModel?.serviceType,
                    tranType = _uiConfirmState.value.confirmModel?.tranType,
                    transactions = ArrayList(transactionIds),
                    transactionsTp = arrayListOf(),
                    nextApprovers = arrayListOf(),
                ),
            )
            handleResource(res) { data ->
                data.isApproval = true
                _doApproveResponse.emit(data)
            }
        }
    }

    /**
     * Xử lý từ chối giao dịch
     */
    fun doReject(reason: String? = null) {
        val transactionIds = _uiConfirmState.value.confirmModel?.transactionIds
        // client show thong bao khong co giao dich
        if (transactionIds.isNullOrEmpty()) {
            onDisplayErrorMessage(
                AppException.ApiException(
                    requestPath = null,
                    rawResponseJson = null,
                    code = null,
                    subCode = null,
                    message = resourceProvider.getString(com.vietinbank.core_ui.R.string.checker_multiple_reject_not_found_id),
                ),
            )
            return
        }

        launchJob(showLoading = true) {
            val res = useCase.doReject(
                RejectParams(
                    username = userProf.getUserName().toString(),
                    token = "",
                    softOtpTransId = null,
                    authenType = "S",
                    requestType = "0",
                    serviceType = _uiConfirmState.value.confirmModel?.serviceType,
                    tranType = _uiConfirmState.value.confirmModel?.tranType,
                    transactions = ArrayList(transactionIds),
                    transactionsTp = arrayListOf(),
                    serviceId = "",
                    reason = reason,
                ),
            )
            handleResource(res) { data ->
                data.isApproval = false
                _doApproveResponse.emit(data)
            }
        }
    }

    // man hinh ket qua
    private val _uiResultState = MutableStateFlow<MultipleResultUIState>(MultipleResultUIState())
    val uiResultState = _uiResultState.asStateFlow()
    fun initResultState(bundle: Bundle?) {
        try {
            val data = Utils.g().provideGson().fromJson(
                bundle?.getString(Tags.TRANSACTION_BUNDLE),
                ApproveDomain::class.java,
            )
            _uiResultState.value = _uiResultState.value.copy(
                resultDomain = data,
                tranType = bundle?.getString(Tags.TRAN_TYPE_BUNDLE),
                serviceType = bundle?.getString(Tags.SERVICE_TYPE_BUNDLE),
                groupType = bundle?.getString(Tags.GROUP_TYPE_BUNDLE),
                messageLst = if (data.isApproval) {
                    resourceProvider.getString(com.vietinbank.core_ui.R.string.feature_checker_need_approved) to
                        resourceProvider.getString(com.vietinbank.core_ui.R.string.checker_multiple_status_fail)
                } else {
                    resourceProvider.getString(com.vietinbank.core_ui.R.string.common_reject_success_txt) to
                        resourceProvider.getString(com.vietinbank.core_ui.R.string.common_reject_fail_txt)
                },
            )

            if (!isKeepCallInit()) {
                // Configure CSAT if needed
                configCSat(Tags.CSAT_FUNC_ID_KEY)
                setKeepCallInit()
            }
        } catch (_: Exception) {
        }
    }

    // trang thai hien thi dialog
    fun getStatus(isSuccess: Boolean = false): MultipleStatusDomain? {
        return _uiResultState.value.resultDomain?.let {
            val (title, statusMessage) = when {
                // phe duyet dien thanh cong
                isSuccess && it.isApproval -> resourceProvider.getString(com.vietinbank.core_ui.R.string.checker_multiple_success_dialog) to
                    resourceProvider.getString(com.vietinbank.core_ui.R.string.feature_checker_need_approved)
                // phe duyet dien that bai
                it.isApproval -> resourceProvider.getString(com.vietinbank.core_ui.R.string.checker_multiple_fail_dialog) to
                    resourceProvider.getString(com.vietinbank.core_ui.R.string.checker_multiple_status_fail)
                // tu choi dien thanh cong
                isSuccess && !it.isApproval -> resourceProvider.getString(com.vietinbank.core_ui.R.string.checker_multiple_reject_success_dialog) to
                    resourceProvider.getString(com.vietinbank.core_ui.R.string.common_reject_success_txt)
                // tu choi dien that bai
                else -> resourceProvider.getString(com.vietinbank.core_ui.R.string.checker_multiple_reject_fail_dialog) to
                    resourceProvider.getString(com.vietinbank.core_ui.R.string.common_reject_fail_txt)
            }
            return MultipleStatusDomain(
                title = title,
                statusMessage = statusMessage,
                tranType = _uiResultState.value.tranType,
                tranDate = it.tranDate,
                isSuccess = isSuccess,
            )
        }
    }
}

sealed class MultipleConfirmActions {
    data object BackClick : MultipleConfirmActions()
    data object ApproveClick : MultipleConfirmActions()
    data class RejectReason(val isShow: Boolean) : MultipleConfirmActions()
    data class RejectClick(val reason: String) : MultipleConfirmActions()
}
