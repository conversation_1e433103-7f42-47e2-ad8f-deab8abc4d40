package com.vietinbank.feature_checker.ui.component.confirmchecker

import com.vietinbank.core_common.constants.Tags
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Created by vandz on 9/4/25.
 */
@Singleton
class TransactionConfirmUIFactory @Inject constructor(
    private val transferConfirmUIGenerator: TransferConfirmUIGenerator,
    private val salaryConfirmUIGenerator: SalaryConfirmUIGenerator,
    private val paymentConfirmUIGenerator: PaymentConfirmUIGenerator,
    private val bulk5000ConfirmUIGenerator: Bulk5000ConfirmUIGenerator,
    private val disbursementConfirmUIGenerator: DisbursementConfirmUIGenerator,
    private val guaranteeConfirmUIGenerator: GuaranteeConfirmUIGenerator,
    private val traceConfirmUIGenerator: TraceConfirmUIGenerator,
    private val customsInlandConfirmUIGenerator: CustomsInlandConfirmUIGenerator,
    private val infrastructureConfirmUIGenerator: InfrastructureConfirmUIGenerator,
    private val transferForeignConfirmUIGenerator: TransferForeignConfirmUIGenerator,
    private val unLockUserConfirmUIGenerator: UnLockUserConfirmUIGenerator,
) {
    fun getGenerator(tranType: String): ITransactionConfirmUIGenerator {
        return when (tranType) {
            "in", "ou", "np", "sn" -> transferConfirmUIGenerator
            "sl", "slo", "sx" -> salaryConfirmUIGenerator
            "pm" -> paymentConfirmUIGenerator
            "hu" -> bulk5000ConfirmUIGenerator
            "tr" -> traceConfirmUIGenerator
            "tx" -> customsInlandConfirmUIGenerator
            "if" -> infrastructureConfirmUIGenerator
            "fx" -> transferForeignConfirmUIGenerator
            Tags.TYPE_DISBURSEMENT_ONLINE.lowercase() -> disbursementConfirmUIGenerator
            "go", "goc", "gor" -> guaranteeConfirmUIGenerator
            Tags.TYPE_GROUP_RESET_PASSWORD.lowercase(), Tags.TYPE_GROUP_UNLOCK_USER.lowercase() -> unLockUserConfirmUIGenerator
            else -> transferConfirmUIGenerator
        }
    }
}