package com.vietinbank.feature_checker.ui.component.multiple_transaction

import androidx.compose.runtime.Composable
import com.vietinbank.core_domain.models.checker.TransactionItemUiModel

interface IMultipleTransactionRender {
    @Composable
    fun RenderConfirmContent(transaction: TransactionItemUiModel)

    @Composable
    fun RenderSuccessContent(transaction: TransactionItemUiModel)

    @Composable
    fun RenderFailContent(transaction: TransactionItemUiModel)
}