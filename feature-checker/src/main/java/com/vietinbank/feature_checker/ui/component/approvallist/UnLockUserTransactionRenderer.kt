package com.vietinbank.feature_checker.ui.component.approvallist

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_domain.models.checker.TransactionItemUiModel
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.feature_checker.R
import com.vietinbank.feature_checker.ui.component.KeyValueRow
import javax.inject.Inject

class UnLockUserTransactionRenderer @Inject constructor() : ITransactionContentRenderer {
    @Composable
    override fun RenderContent(
        transaction: TransactionItemUiModel,
        isSelectionMode: Boolean,
    ) {
        Column(modifier = Modifier.fillMaxWidth()) {
            Row(modifier = Modifier.fillMaxWidth()) {
                Text(
                    modifier = Modifier.weight(1f),
                    text = transaction.tranTypeName.trim(),
                    style = getComposeFont(5, 14.sp, AppColors.blue02),
                )

                Image(
                    modifier = Modifier.size(24.dp),
                    painter = painterResource(R.drawable.ic_more),
                    contentDescription = null,
                )
            }

            Spacer(modifier = Modifier.height(10.dp))

            transaction.details.forEach { detail ->
                if (detail.label.isNotEmpty()) {
                    KeyValueRow(label = detail.label, value = detail.value)
                    Spacer(modifier = Modifier.height(4.dp))
                } else {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.End,
                    ) {
                        BaseText(
                            text = detail.value,
                            color = AppColors.textPrimary,
                            textSize = 14.sp,
                            textAlign = TextAlign.End,
                            maxLines = 3,
                            overflow = TextOverflow.Ellipsis,
                        )
                    }
                    Spacer(modifier = Modifier.height(4.dp))
                }
            }
        }
    }
}