package com.vietinbank.feature_checker.ui.component.confirmchecker

import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItem
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItemType
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmUiModel
import javax.inject.Inject

/**
 * Created by vandz on 9/4/25.
 */

class SalaryConfirmUIGenerator @Inject constructor() : ITransactionConfirmUIGenerator {
    override fun generateConfirmUI(
        transaction: TransactionDomain,
        confirmType: String,
        onFieldEvent: ((TransactionFieldEvent) -> Unit)?,
    ): ConfirmUiModel {
        val items = mutableListOf<ConfirmItem>()

        // Thêm header Tài khoản chuyển
        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin tài khoản chuyển",
            ),
        )

        // Thêm thông tin tài khoản chuyển
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Từ tài khoản",
                value = "${transaction.fromAccountNo ?: ""} - ${transaction.currency ?: "VND"} - ${transaction.fromAccountName ?: ""}",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.SPACER,
            ),
        )
        // Thêm header TT chi tiết
        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin chi tiết",
            ),
        )

        if (!transaction.amount.isNullOrEmpty() && !transaction.currency.isNullOrEmpty()) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Số tiền",
                    value = transaction.amount ?: "",
                    isHighlighted = true,
                ),
            )
        }

        // chi luong ngoai te
        if (transaction.tranType == "sx") {
            transaction.exchangeRate?.let {
                items.add(
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        label = "Tỷ giá (quy đổi tham khảo)",
                        value = it,
                    ),
                )
            }
            transaction.debitAmount?.let {
                items.add(
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        label = "Số tiền trích nợ (tham khảo)",
                        value = it,
                    ),
                )
            }
        }

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Phí giao dịch",
                value = transaction.feeAmount ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Nội dung",
                value = transaction.remark ?: "",
            ),
        )

        val file = transaction.listFile?.firstOrNull()
        val canClickFile = shouldEnableClickForField(transaction.tranType)

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "File đính kèm",
                value = file?.fileName ?: "Chưa có file lương",
                isHighlighted = file != null && canClickFile,
                isClickable = file != null && canClickFile,
                clickEvent = file?.let {
                    if (canClickFile) {
                        TransactionFieldEvent.FileAttachmentClick(it)
                    } else {
                        null
                    }
                },
            ),
        )

        transaction.listFile2?.firstOrNull()?.let {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Hồ sơ đính kèm",
                    value = it.fileName ?: "",
                ),
            )
        }

        if (transaction.process_time.isNullOrEmpty()) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Thời gian chuyển",
                    value = "Chuyển ngay",
                ),
            )
        } else {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Thời gian chuyển",
                    value = "Đặt lịch",
                ),
            )

            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Ngày đặt lịch",
                    value = transaction.process_time ?: "",
                ),
            )
        }

        return ConfirmUiModel(
            screenTitle = if (confirmType == Tags.APPROVE) "Duyệt" else "Từ chối",
            items = items,
            transactionIds = listOfNotNull(transaction.mtId),
            serviceType = transaction.serviceType ?: "CT",
            tranType = transaction.tranType ?: "",
            groupType = transaction.groupType ?: "",
        )
    }

    override fun generateBatchConfirmUI(
        transactions: List<TransactionListDomain>,
        totalAmount: String,
        totalFee: String,
        confirmType: String,
        createdDate: String,
    ): ConfirmUiModel {
        val items = mutableListOf<ConfirmItem>()

        // Header info giao dịch
        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin giao dịch chi lương",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng số giao dịch",
                value = transactions.size.toString(),
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng phí giao dịch",
                value = totalFee,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Thời gian giao dịch",
                value = "",
            ),
        )

        // Lấy danh sách ID giao dịch
        val transactionIds = transactions.mapNotNull { it.mtId }

        // Lấy service type và tran type
        val serviceType = transactions.firstOrNull()?.serviceType ?: ""
        val tranType = transactions.firstOrNull()?.tranType ?: ""
        val groupType = transactions.firstOrNull()?.groupType ?: ""

        return ConfirmUiModel(
            screenTitle = if (confirmType == Tags.APPROVE) "Duyệt" else "Từ chối",
            items = items,
            transactionIds = transactionIds,
            serviceType = serviceType,
            tranType = tranType,
            groupType = groupType,
        )
    }

    private fun shouldEnableClickForField(tranType: String?): Boolean {
        return when (tranType) {
            "sl" -> true
            "slo" -> true
            "sx" -> true
            else -> false
        }
    }
}