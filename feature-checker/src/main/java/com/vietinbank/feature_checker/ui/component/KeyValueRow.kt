package com.vietinbank.feature_checker.ui.component

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.theme.AppColors

/**
 * Created by vandz on 4/4/25.
 */
@Composable
fun KeyValueRow(
    label: String,
    value: String,
    isHighlighted: Boolean = false,
    isHyperlink: Boolean = false,
    subValue: String? = null,
    iconResource: Int? = null, // Thêm tham số icon
    isWarning: Boolean = false, // Thêm tham số cảnh báo
    maxLine: Int = 6,
    onClick: (() -> Unit)? = null,
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            if (iconResource != null) {
                Icon(
                    painter = painterResource(id = iconResource),
                    contentDescription = null,
                    tint = if (isWarning) Color.Red else Color.Unspecified,
                    modifier = Modifier.size(24.dp),
                )
                Spacer(modifier = Modifier.width(8.dp))
            }

            BaseText(
                text = label,
                color = Color.Gray,
                textSize = 14.sp,
                modifier = Modifier.weight(0.4f),
            )

            Spacer(modifier = Modifier.width(4.dp))

            Column(
                modifier = Modifier.weight(0.6f),
                horizontalAlignment = Alignment.End,
            ) {
                BaseText(
                    text = value,
                    fontWeight = if (isHighlighted) FontWeight.Bold else FontWeight.Normal,
                    color = when {
                        isHyperlink -> AppColors.primary
                        isHighlighted -> AppColors.primary
                        isWarning -> Color.Red
                        else -> Color.Gray
                    },
                    textSize = if (isHighlighted) 16.sp else 14.sp,
                    textDecoration = if (isHyperlink) TextDecoration.Underline else null,
                    fontCus = 1,
                    maxLines = maxLine,
                    overflow = TextOverflow.Ellipsis,
                    textAlign = TextAlign.End,
                    onClick = onClick,
                )

                if (!subValue.isNullOrEmpty()) {
                    BaseText(
                        text = subValue,
                        color = Color.Gray,
                        textSize = 12.sp,
                        fontCus = 1,
                        maxLines = 3,
                        overflow = TextOverflow.Ellipsis,
                        textAlign = TextAlign.End,
                    )
                }
            }
        }
    }
}