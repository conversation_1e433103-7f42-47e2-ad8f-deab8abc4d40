package com.vietinbank.feature_checker.ui.navigation

import com.google.gson.Gson
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyCurrency
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.MethodEntityDomain
import com.vietinbank.core_domain.models.checker.PreApproveDomain
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItem
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItemType
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmUiModel
import com.vietinbank.core_domain.navigation.IConfirmCheckerLauncher
import javax.inject.Inject

/**
 * Created by vandz on 24/4/25.
 */
class ConfirmCheckerLauncherImpl
@Inject
constructor(
    private val appNavigator: IAppNavigator,
    private val gson: Gson,
    private val moneyHelper: MoneyHelper,
) : IConfirmCheckerLauncher {
    override fun launchWithSingleTransaction(
        transaction: TransactionDomain,
        confirmType: String,
        paymentMethod: ArrayList<MethodEntityDomain>?,
        onError: ((message: String) -> Unit)?,
    ): Boolean {
        if (transaction.mtId == null) {
            onError?.invoke("Transaction ID cannot be null")
            return false
        }

        val transactionJson = gson.toJson(transaction)
        val paymentMethodJson = paymentMethod?.let { gson.toJson(it) } ?: "{}"

        appNavigator.goToConfirmChecker(
            transaction = transactionJson,
            paymentMethod = paymentMethodJson,
            confirmType = confirmType,
            dataType = Tags.UI_MODEL,
        )
        return true
    }

    override fun launchWithBatchConfirmModel(
        confirmUiModel: ConfirmUiModel,
        confirmType: String,
        paymentMethod: ArrayList<MethodEntityDomain>?,
        onError: ((message: String) -> Unit)?,
    ): Boolean {
        if (confirmUiModel.transactionIds.isEmpty()) {
            onError?.invoke("TransactionIds cannot be empty")
            return false
        }

        val confirmUiModelJson = gson.toJson(confirmUiModel)
        val paymentMethodJson = paymentMethod?.let { gson.toJson(it) } ?: "{}"

        appNavigator.goToConfirmChecker(
            transaction = confirmUiModelJson,
            paymentMethod = paymentMethodJson,
            confirmType = confirmType,
            dataType = Tags.UI_BATCH,
        )
        return true
    }

    override fun launchWithBatchTransactions(
        transaction: TransactionDomain,
        confirmType: String,
        paymentMethod: ArrayList<MethodEntityDomain>?,
        selectedMtIds: ArrayList<String>,
        onError: ((message: String) -> Unit)?,
    ): Boolean {
        if (selectedMtIds.isEmpty()) {
            onError?.invoke("Vui lòng chọn ít nhất một giao dịch để phê duyệt")
            return false
        }

        // Create a list of ConfirmItems for the batch transaction
        val confirmItems = mutableListOf<ConfirmItem>()

        // Add header
        confirmItems.add(ConfirmItem(type = ConfirmItemType.HEADER, title = "Thông tin giao dịch"))

        // Add transaction details
        confirmItems.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng số giao dịch",
                value = selectedMtIds.size.toString(),
            ),
        )
        // Add amount with highlighting - ưu tiên sử dụng transaction.amount từ kết quả API
        // transaction.amount chứa tổng số tiền thực tế của các giao dịch được chọn
        val totalAmount = Utils.g().getDotMoneyHasCcy(
            transaction.amount ?: transaction.totalAmount?.toString() ?: "0",
            transaction.currency ?: MoneyCurrency.VND.value,
        )
        confirmItems.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng số tiền",
                value = totalAmount,
                subValue = moneyHelper.convertAmountToWords(
                    transaction.amount ?: transaction.totalAmount ?: transaction.total ?: "0",
                    transaction.currency ?: MoneyCurrency.VND.value,
                ),
                isHighlighted = if ("btx" == transaction.tranType) false else true,
            ),
        )

        val feeAmount = Utils.g().getDotMoneyHasCcy(
            transaction.feeAmount ?: transaction.totalFee ?: "0",
            transaction.currency ?: MoneyCurrency.VND.value,
        )
        confirmItems.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng số phí giao dịch",
                value = feeAmount,
            ),
        )

        // Add spacer
        confirmItems.add(ConfirmItem(type = ConfirmItemType.SPACER))

        // Create a ConfirmUiModel for batch transactions
        val confirmUiModel = ConfirmUiModel(
            screenTitle = "Xác nhận giao dịch",
            items = confirmItems,
            transactionIds = selectedMtIds,
            serviceType = transaction.serviceType ?: "",
            tranType = transaction.tranType ?: "",
            groupType = transaction.groupType ?: "",
        )

        return launchWithBatchConfirmModel(confirmUiModel, confirmType, paymentMethod, onError)
    }

    override fun getPaymentMethod(success: PreApproveDomain): ArrayList<MethodEntityDomain>? {
        return success.methodList
    }
}