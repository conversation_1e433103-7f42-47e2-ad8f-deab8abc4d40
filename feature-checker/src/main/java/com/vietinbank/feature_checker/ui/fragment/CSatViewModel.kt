package com.vietinbank.feature_checker.ui.fragment

import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.csat.CSatConfigDomain
import com.vietinbank.core_domain.models.csat.CSatConfigParams
import com.vietinbank.core_domain.models.csat.CSatRateDomain
import com.vietinbank.core_domain.models.csat.CSatRateParams
import com.vietinbank.core_domain.usecase.transfer.CSatUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

open class CSatViewModel @Inject constructor(
    open val cSatUseCase: CSatUseCase,
    override val sessionManager: ISessionManager,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
) : BaseViewModel() {

    // danh gia cl dich vu
    private val _rateCSATStatus = MutableSharedFlow<Resource<CSatRateDomain>?>()
    val rateCSATStatus = _rateCSATStatus.asSharedFlow()
    fun rateCSat(functionId: String? = null, point: String? = null, comment: String? = null) {
        launchJob {
            val params = CSatRateParams(
                comment = comment,
                functionId = functionId,
                ratePoint = point,
                userName = userProf.getUserName(),
            )
            val res = cSatUseCase.rateCSAT(params)
            handleResource(res) { data ->
                _rateCSATStatus.emit(Resource.Success(data))
            }
        }
    }

    // cau hinh danh gia cl dich vu

    private val _isShowCSatState = MutableStateFlow<Boolean>(false)
    val isShowCSatState = _isShowCSatState.asStateFlow()

    private val _configCSATStatus = MutableStateFlow<CSatConfigDomain?>(null)
    val configCSatStatus: StateFlow<CSatConfigDomain?> = _configCSATStatus.asStateFlow()
    fun configCSat(functionId: String? = null) = launchJobSilent {
        val params = CSatConfigParams(
            functionId = functionId,
            userName = userProf.getUserName(),
        )
        val res = cSatUseCase.configCSAT(params)
        handleResourceSilent(
            resource = res,
            onSuccess = { data ->
                _configCSATStatus.value = data
                _isShowCSatState.value = data.showButton == "Y"
            },
        )
    }
}