package com.vietinbank.feature_checker.ui.component.multiple_transaction

import androidx.compose.runtime.Composable
import com.vietinbank.core_domain.models.checker.TransactionItemUiModel
import javax.inject.Inject

class MultipleTransferRender @Inject constructor() : IMultipleTransactionRender {
    @Composable
    override fun RenderConfirmContent(transaction: TransactionItemUiModel) {
    }

    @Composable
    override fun RenderSuccessContent(transaction: TransactionItemUiModel) {
    }

    @Composable
    override fun RenderFailContent(transaction: TransactionItemUiModel) {
    }
}