package com.vietinbank.feature_checker.ui.sheet

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_common.extensions.containsNotVietNam
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.FoundationSelector
import com.vietinbank.core_ui.components.SelectorType
import com.vietinbank.core_ui.components.foundation.textfield.FieldVariant
import com.vietinbank.core_ui.components.foundation.textfield.FoundationFieldType
import com.vietinbank.core_ui.components.foundation.textfield.InputType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.R as CoreR
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Data model cho loại giao dịch
 */
data class TransactionType(
    val id: String,
    val code: String,
    val name: String,
    val icon: Int? = null,
)

/**
 * Bottom sheet để chọn loại giao dịch với search và radio list
 * Dựa trên design của NextApproverBottomSheet nhưng dùng radio button và có thể dismiss
 */
@Composable
fun TransactionTypeBottomSheet(
    modifier: Modifier = Modifier,
    isVisible: Boolean,
    transactionTypes: List<TransactionType>,
    selectedTypeId: String? = null,
    onItemSelected: (TransactionType) -> Unit,
    onDismiss: () -> Unit,
) {
    if (!isVisible) return

    // Local search state
    var searchQuery by remember { mutableStateOf("") }

    // Filter transaction types based on search
    val filteredTypes = remember(searchQuery, transactionTypes) {
        val q = searchQuery.trim().lowercase()
        if (q.isEmpty()) {
            transactionTypes
        } else {
            transactionTypes.filter { type ->
                type.name.containsNotVietNam(q) ||
                    type.code.containsNotVietNam(q)
            }
        }
    }

    BaseBottomSheet(
        visible = isVisible,
        onDismissRequest = onDismiss,
        allowTouchDismiss = true, // Cho phép dismiss khi touch outside
    ) { close ->
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .navigationBarsPadding()
                .imePadding(),
        ) {
            // White card area with rounded corners and shadow
            Surface(
                shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
                color = FDS.Colors.white,
                shadowElevation = FDS.Sizer.Gap.gap4,
                modifier = Modifier.fillMaxWidth(),
            ) {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                ) {
                    FoundationText(
                        text = stringResource(com.vietinbank.feature_checker.R.string.feature_checker_transaction_type_title),
                        style = FDS.Typography.headingH3,
                        color = FDS.Colors.characterHighlighted,
                        modifier = Modifier
                            .padding(
                                top = FDS.Sizer.Padding.padding24,
                                bottom = FDS.Sizer.Padding.padding16,
                            )
                            .padding(horizontal = FDS.Sizer.Padding.padding24)
                            .align(Alignment.CenterHorizontally),
                    )

                    // Divider
                    FoundationDivider()

                    // Search area - background #EDF4F8
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = FDS.Sizer.Padding.padding24)
                            .padding(top = FDS.Sizer.Gap.gap12, bottom = FDS.Sizer.Gap.gap8),
                    ) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(
                                    color = FDS.Colors.homeBackgroundIcon, // #EDF4F8
                                    shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
                                )
                                .padding(
                                    horizontal = FDS.Sizer.Padding.padding16,
                                    vertical = FDS.Sizer.Padding.padding8,
                                ),
                        ) {
                            FoundationFieldType(
                                value = searchQuery,
                                onValueChange = { searchQuery = it },
                                variant = FieldVariant.TRANSPARENT,
                                inputType = InputType.TEXT,
                                placeholder = stringResource(com.vietinbank.feature_checker.R.string.feature_checker_search_transaction_type),
                                singleLine = true,
                                showCharacterCounter = false,
                                isHaveClearIcon = searchQuery.isNotEmpty(),
                                clearValue = { searchQuery = "" },
                                leadingIcon = {
                                    Image(
                                        painter = painterResource(id = CoreR.drawable.ic_search),
                                        contentDescription = null,
                                    )
                                },
                            )
                        }
                    }

                    // Transaction types list
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = FDS.Sizer.Padding.padding24),
                    ) {
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))

                        // List
                        LazyColumn(
                            modifier = Modifier
                                .fillMaxWidth()
                                .heightIn(max = FDS.Sizer.Dialog.maxListHeight),
                        ) {
                            items(filteredTypes) { type ->
                                TransactionTypeItem(
                                    transactionType = type,
                                    isSelected = type.id == selectedTypeId,
                                    onClick = {
                                        onItemSelected(type)
                                        close() // Auto close after selection
                                    },
                                )

                                if (type != filteredTypes.lastOrNull()) {
                                    FoundationDivider()
                                }
                            }

                            // Bottom padding
                            item {
                                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * Item hiển thị một loại giao dịch với radio button
 */
@Composable
private fun TransactionTypeItem(
    transactionType: TransactionType,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .safeClickable { onClick() }
            .padding(vertical = FDS.Sizer.Padding.padding16),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // Icon (if available)
        transactionType.icon?.let { iconRes ->
            Image(
                painter = painterResource(id = iconRes),
                contentDescription = transactionType.name,
                modifier = Modifier.size(FDS.Sizer.Icon.icon32),
            )
            Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap8))
        }

        // Text content
        Column(modifier = Modifier.weight(1f)) {
            FoundationText(
                text = transactionType.name,
                style = FDS.Typography.bodyB1,
                color = FDS.Colors.characterPrimary,
            )
        }

        // Radio button
        FoundationSelector(
            boxType = SelectorType.Radio,
            isSelected = isSelected,
            isClickable = false,
        )
    }
}
