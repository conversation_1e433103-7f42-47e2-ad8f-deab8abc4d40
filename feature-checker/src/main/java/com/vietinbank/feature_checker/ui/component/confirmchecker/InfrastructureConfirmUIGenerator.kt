package com.vietinbank.feature_checker.ui.component.confirmchecker

import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItem
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItemType
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmUiModel
import javax.inject.Inject

class InfrastructureConfirmUIGenerator @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : ITransactionConfirmUIGenerator {
    override fun generateConfirmUI(
        transaction: TransactionDomain,
        confirmType: String,
        onFieldEvent: ((TransactionFieldEvent) -> Unit)?,
    ): ConfirmUiModel {
        val items = mutableListOf<ConfirmItem>()

        // Điện HCM
        if ("975" == transaction.providerCode) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.HEADER,
                    title = "Thông tin chung",
                ),
            )

            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Loại giao dịch",
                    value = transaction.tranTypeName ?: "",
                ),
            )

            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Số giao dịch",
                    value = transaction.mtId ?: "",
                ),
            )

            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Trạng thái",
                    value = transaction.statusName ?: "",
                ),
            )

            items.add(
                ConfirmItem(
                    type = ConfirmItemType.SPACER,
                ),
            )

            items.add(
                ConfirmItem(
                    type = ConfirmItemType.HEADER,
                    title = "Thông tin đơn vị thu phí",
                ),
            )

            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Mã đơn vị thu phí",
                    value = (transaction.providerCode ?: "") + (transaction.providerName ?: ""),
                ),
            )

            items.add(
                ConfirmItem(
                    type = ConfirmItemType.SPACER,
                ),
            )

            // Thêm header Thông tin nguời nộp thuế
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.HEADER,
                    title = "Thông tin đơn vị nộp thuế",
                ),
            )
        } else if ("31" == transaction.providerCode) { // Điện hải phòng
            // Thêm header Thông tin nguời nộp thuế
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.HEADER,
                    title = "Thông tin nguời nộp phí hạ tầng",
                ),
            )
        }

        if ("0" == transaction.payMethod) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Hình thức",
                    value = "Nộp cho chính đơn vị",
                ),
            )
        } else if ("1" == transaction.payMethod) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Hình thức",
                    value = "Nộp thay cho đơn vị khác",
                ),
            )
        }

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tên đơn vị nộp",
                value = transaction.payname ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Mã số thuế",
                value = transaction.payCode ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Địa chỉ",
                value = transaction.payAddress ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Từ tài khoản",
                value = if (!transaction.fromAccountNo.isNullOrEmpty()) {
                    "${transaction.fromAccountNo ?: ""} - ${transaction.currency}"
                } else {
                    ""
                },
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Ngân hàng",
                value = transaction.branchName ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.SPACER,
            ),
        )

        // Điện HCM

        // Thêm header Thông tin nguời nộp thuế
        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin chi tiết khoản nộp",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "ID chứng từ",
                value = if ("975" == transaction.providerCode) {
                    transaction.invoiceId ?: ""
                } else {
                    transaction.docId ?: ""
                },
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Số chứng từ",
                value = if ("975" == transaction.providerCode) {
                    transaction.voucherNumber ?: ""
                } else {
                    transaction.docNum ?: ""
                },
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Ký hiệu chứng từ",
                value = if ("975" == transaction.providerCode) {
                    transaction.voucherSymbol ?: ""
                } else {
                    transaction.docSign ?: ""
                },
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Ngày chứng từ",
                value = if ("975" == transaction.providerCode) {
                    transaction.voucherDate ?: ""
                } else {
                    transaction.docDate ?: ""
                },
            ),
        )

        if ("975" != transaction.providerCode) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Mã chương",
                    value = transaction.chapterCode ?: "",
                ),
            )

            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Mã tiểu mục",
                    value = transaction.subsect ?: "",
                ),
            )
        }

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Số tiền giao dịch",
                value = Utils.g().getDotMoneyHasCcy(
                    transaction.amount ?: "",
                    transaction.currency ?: "",
                ),
                subValue = moneyHelper.convertAmountToWords(
                    transaction.amount ?: "",
                    transaction.currency ?: "",
                ),
                isHighlighted = true,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Phí giao dịch",
                value = Utils.g().getDotMoneyHasCcy(
                    transaction.feeAmount ?: "",
                    transaction.currency ?: "",
                ),
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.SPACER,
            ),
        )

        return ConfirmUiModel(
            screenTitle = if (confirmType == Tags.APPROVE) "Duyệt" else "Từ chối",
            items = items,
            transactionIds = listOfNotNull(transaction.mtId),
            serviceType = transaction.serviceType ?: "CT",
            tranType = transaction.tranType ?: "",
            groupType = transaction.groupType ?: "",
        )
    }

    override fun generateBatchConfirmUI(
        transactions: List<TransactionListDomain>,
        totalAmount: String,
        totalFee: String,
        confirmType: String,
        createdDate: String,
    ): ConfirmUiModel {
        val items = mutableListOf<ConfirmItem>()

        // Header info giao dịch
        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin giao dịch chuyển khoản",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng số giao dịch",
                value = transactions.size.toString(),
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng số tiền chuyển",
                value = totalAmount,
                isHighlighted = true,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng phí giao dịch",
                value = totalFee,
            ),
        )

        // Lấy danh sách ID giao dịch
        val transactionIds = transactions.mapNotNull { it.mtId }

        // Lấy service type và tran type
        val serviceType = transactions.firstOrNull()?.serviceType ?: ""
        val tranType = transactions.firstOrNull()?.tranType ?: ""
        val groupType = transactions.firstOrNull()?.groupType ?: ""

        return ConfirmUiModel(
            screenTitle = if (confirmType == Tags.APPROVE) "Duyệt" else "Từ chối",
            items = items,
            transactionIds = transactionIds,
            serviceType = serviceType,
            tranType = tranType,
            groupType = groupType,
        )
    }
}