package com.vietinbank.feature_checker.ui.component.confirmchecker

import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.getAmountServer
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyCurrency
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItem
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItemType
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmUiModel
import javax.inject.Inject

/**
 * Created by vandz on 9/4/25.
 */

class PaymentConfirmUIGenerator @Inject constructor() : ITransactionConfirmUIGenerator {
    override fun generateConfirmUI(
        transaction: TransactionDomain,
        confirmType: String,
        onFieldEvent: ((TransactionFieldEvent) -> Unit)?,
    ): ConfirmUiModel {
        val items = mutableListOf<ConfirmItem>()

        // Thêm header Tài khoản chuyển
        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin tài khoản chuyển",
            ),
        )

        // Thêm thông tin tài khoản chuyển
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Từ tài khoản",
                value = "${transaction.fromAccountNo ?: ""} - ${transaction.currency ?: "VND"} - ${transaction.fromAccountName ?: "VND"}",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.SPACER,
            ),
        )

        // Thêm header Tài khoản thụ hưởng
        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin tài khoản thụ hưởng",
            ),
        )

        // Thêm thông tin tài khoản thụ hưởng
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tới tài khoản",
                value = transaction.toAccountNo?.toString() ?: "",
                subValue = transaction.receiveName,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Ngân hàng",
                value = transaction.receiveBankName ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Số tiền",
                value = Utils.g().getDotMoneyHasCcy(
                    transaction.amount?.getAmountServer() ?: "",
                    transaction.currency ?: "VND",
                ),
                isHighlighted = true,
            ),
        )

        val formatedFeeAmount = Utils.g().getDotMoneyHasCcy(
            transaction.feeAmount ?: "0",
            transaction.currency ?: MoneyCurrency.VND.value,
        )

        val fee = if (transaction.feePayMethod == "3") {
            transaction.feePayMethodDesc1 ?: ""
        } else {
            "${transaction.feeAmount ?: "0"} ${transaction.currency ?: ""}"
        }
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Phí giao dịch",
                value = fee,
            ),
        )

        val feeMethod = if (transaction.feePayMethod == "0") {
            "Phí ngoài"
        } else {
            "Phí trong"
        }
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Hình thức thu phí",
                value = feeMethod,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Nội dung",
                value = transaction.remark ?: "",
            ),
        )

        if (!transaction.fileName.isNullOrEmpty()) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "File đính kèm",
                    value = transaction.fileName ?: "",
                    isHighlighted = true,
                ),
            )
        }

        if (transaction.process_time.isNullOrEmpty()) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Thời gian chuyển",
                    value = "Chuyển ngay",
                ),
            )
        } else {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Thời gian chuyển",
                    value = "Đặt lịch",
                ),
            )

            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Ngày đặt lịch",
                    value = transaction.process_time ?: "",
                ),
            )
        }

        return ConfirmUiModel(
            screenTitle = if (confirmType == Tags.APPROVE) "Duyệt" else "Từ chối",
            items = items,
            transactionIds = listOfNotNull(transaction.mtId),
            serviceType = transaction.serviceType ?: "CT",
            tranType = transaction.tranType ?: "",
            groupType = transaction.groupType ?: "",
        )
    }

    override fun generateBatchConfirmUI(
        transactions: List<TransactionListDomain>,
        totalAmount: String,
        totalFee: String,
        confirmType: String,
        createdDate: String,
    ): ConfirmUiModel {
        val items = mutableListOf<ConfirmItem>()

        // Header info giao dịch
        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin giao dịch chuyển khoản",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng số giao dịch",
                value = transactions.size.toString(),
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng số tiền chuyển",
                value = totalAmount,
                isHighlighted = true,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng phí giao dịch",
                value = totalFee,
            ),
        )

        // Lấy danh sách ID giao dịch
        val transactionIds = transactions.mapNotNull { it.mtId }

        // Lấy service type và tran type
        val serviceType = transactions.firstOrNull()?.serviceType ?: ""
        val tranType = transactions.firstOrNull()?.tranType ?: ""
        val groupType = transactions.firstOrNull()?.groupType ?: ""

        return ConfirmUiModel(
            screenTitle = if (confirmType == Tags.APPROVE) "Duyệt" else "Từ chối",
            items = items,
            transactionIds = transactionIds,
            serviceType = serviceType,
            tranType = tranType,
            groupType = groupType,
        )
    }
}