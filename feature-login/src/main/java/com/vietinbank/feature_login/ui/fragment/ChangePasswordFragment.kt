package com.vietinbank.feature_login.ui.fragment

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_login.ui.screen.ActiveScreen
import com.vietinbank.feature_login.ui.viewmodel.LoginViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

/**
 * <PERSON><PERSON>n hình <PERSON>ổi mật khẩu
 * Created by vandz on 3/3/25.
 */

@AndroidEntryPoint
class ChangePasswordFragment : BaseFragment<LoginViewModel>() {
    override val viewModel: LoginViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Composable
    override fun ComposeScreen() {
        AppTheme {
            ActiveScreen(
                currentUsername = viewModel.userName.value ?: "",
                onBackClick = { appNavigator.popBackStack() },
                onNotifyClick = { /* TODO: Xử lý thông báo */ },
                onShareClick = { /* TODO: Xử lý chia sẻ */ },
                onChangePasswordClick = { oldPassword, newPassword, confirmPassword ->
                    // Kiểm tra và xác thực trước khi gọi API
                    if (viewModel.validatePasswordChange(
                            oldPassword,
                            newPassword,
                            confirmPassword,
                        )
                    ) {
//                        viewModel.changePassword(
//                            viewModel.userName.value.toString(),
//                            oldPassword,
//                            newPassword,
//                        )
                    }
                },
            )
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Thiết lập các observers cho ViewModel
        setupObservers()
        viewModel.updateLoginStatus()

        // Ẩn bàn phím khi nhấn ra ngoài
        hideKeyboardWhenTouchOutside()
    }

    private fun hideKeyboardWhenTouchOutside() {
        // Sử dụng cơ chế ẩn bàn phím từ BaseActivity
        hideKeyboard()
    }

    private fun setupObservers() {
        // TODO: Migrate to Channel events pattern
        // changePasswordResult has been migrated to Channel events in ViewModel
        // This fragment needs to collect events from viewModel.events Flow
        /*
        // Observer kết quả đổi mật khẩu
        viewModel.changePasswordResult.observe(viewLifecycleOwner) { result ->
            when (result) {
                is Resource.Success -> {
                    // Hiển thị thông báo thành công và quay lại màn hình trước
                    showNoticeDialog(result.data.status.message.toString()) {
                        appNavigator.popBackStack()
                    }
                }

                is Resource.Error -> {
                    // Lỗi đã được xử lý tự động bởi BaseViewModel
                }

                else -> {}
            }
        }
         */

        // Observer các lỗi liên quan đến mật khẩu
        viewModel.oldPasswordError.observe(viewLifecycleOwner) { error ->
            error?.let { showNoticeDialog(it) {} }
        }

        viewModel.newPasswordError.observe(viewLifecycleOwner) { error ->
            error?.let { showNoticeDialog(it) {} }
        }

        viewModel.confirmPasswordError.observe(viewLifecycleOwner) { error ->
            error?.let { showNoticeDialog(it) {} }
        }
    }
}