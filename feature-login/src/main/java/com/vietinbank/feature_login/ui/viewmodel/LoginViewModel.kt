package com.vietinbank.feature_login.ui.viewmodel

import android.text.TextUtils
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.analytics.CrashlyticsManager
import com.vietinbank.core_common.biometric.BiometricStatus
import com.vietinbank.core_common.biometric.IBiometricManager
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_common.constants.ListFunctionId
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.environment.IEnvironmentProvider
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.livedata.SingleLiveEvent
import com.vietinbank.core_common.models.ForceUpdateDomain
import com.vietinbank.core_common.models.UserProfData
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.CustomEncryptedPrefs
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.login.ChangePasswordParams
import com.vietinbank.core_domain.models.login.CifSharedDomain
import com.vietinbank.core_domain.models.login.ForceUpdateParams
import com.vietinbank.core_domain.models.login.LoginDomain
import com.vietinbank.core_domain.models.login.LoginParams
import com.vietinbank.core_domain.ott.IOttManager
import com.vietinbank.core_domain.repository.cache.IOttRegistrationRepository
import com.vietinbank.core_domain.repository.cache.ITransferCacheManager
import com.vietinbank.core_domain.usecase.login.LoginUseCase
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.feature_login.ui.events.ChangePassEvent
import com.vietinbank.feature_login.ui.events.LoginEvent
import com.vietinbank.feature_login.ui.state.ChangePassUiState
import com.vietinbank.feature_login.ui.state.LoginUiState
import com.vietinbank.feature_login.ui.viewmodel.ValidationConstants.CHANGE_PASSWORD_CONDITION
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

// Validation Constants - Made internal for UI access
internal object ValidationConstants {
    const val USERNAME_MAX_LENGTH = 40
    const val PASSWORD_MIN_LENGTH = 8
    const val PASSWORD_MAX_LENGTH = 40
    const val CHANGE_PASSWORD_MAX_LENGTH = 50
    const val CHANGE_PASSWORD_CONDITION = "@#\$%"
}

// Response Code Constants
private object ResponseCode {
    const val PASSWORD_FORGOT = "882"
    const val FIRST_LOGIN = "10"
    const val TWO_FA_REQUIRED = "44"
    const val PASSWORD_EXPIRY_WARNING = "93"
    const val PASSWORD_EXPIRED_881 = Tags.PASSWORD_EXPIRE // "881"
    const val PASSWORD_EXPIRED_NEW = Tags.NEW_PASSWORD_EXPIRE
    const val LOGIN_AUTHENTICATION = Tags.LOGIN_AUTHENTICATION
    const val USER_MULTI_CIF = Tags.USER_MULTI_CIF
    const val FORCE_UPDATE = Tags.FORCE_UPDATE
}

// Authentication Constants
private object AuthConstants {
    const val AUTH_TYPE_TOUCH_ID = "T"
    const val REG_TOUCH_ID_ENABLED = "1"
    const val DEBUG_PASSWORD = "12121212"
}

// Dialog State Management
sealed interface DialogState {
    data object None : DialogState
    data class CompanySelection(
        val companies: List<CifSharedDomain>,
    ) : DialogState
    data class Notice(
        val message: String,
        val positiveAction: (() -> Unit)? = null,
    ) : DialogState
    data class Confirm(
        val message: String,
        val positiveText: String? = null,
        val negativeText: String? = null,
        val onPositive: () -> Unit,
        val onNegative: () -> Unit,
    ) : DialogState
}

/**
 * Created by vandz on 18/12/24.
 */
@HiltViewModel
class LoginViewModel @Inject constructor(
    private val loginUseCase: LoginUseCase,
    override val sessionManager: ISessionManager,
    private val prefs: CustomEncryptedPrefs,
    override val resourceProvider: IResourceProvider,
    private val environmentProvider: IEnvironmentProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    private val crashlyticsManager: CrashlyticsManager,
    private val ottManager: IOttManager,
    val transferCacheManager: ITransferCacheManager,
    val ottRegistrationRepository: IOttRegistrationRepository,
    private val biometricManager: IBiometricManager,
) : BaseViewModel() {
    fun saveSession(sessionId: String) = sessionManager.saveSession(sessionId)
    fun clearSession() = sessionManager.clearSession()
    fun isChecker(): Boolean = userProf.isChecker()
    fun getKeypassProfile(): String? = userProf.getKeypassProfile()
    fun getUserName(): String? = userProf.getUserName()
    fun getFullName(): String? = userProf.getFullName()
    fun isActive(): Boolean = userProf.isActive()
    fun saveProfile(userProfData: UserProfData) = userProf.saveProfile(userProfData)
    fun clearProfile() = userProf.clearProfile()
    fun updateFingerID(fingerID: String) = appConfig.updateFingerID(fingerID)
    fun clearAllConfig() = appConfig.clearAllConfig()
    fun setTimerConfig(timerConfig: Boolean) = appConfig.setTimerConfig(timerConfig)
    fun isNonFinancial(): Boolean = userProf.getFinancialPackage() == Tags.ROLE_ACCOUNT_INQUIRY

    // ===== CHANNEL-BASED EVENT SYSTEM FOR NEWUI FRAGMENTS =====
    // WARNING: This runs PARALLEL with LiveData for backward compatibility
    // When sending events, MUST call BOTH Channel.send() AND LiveData.postValue()

    // One-time events using Channel (NewUI fragments only)
    private val _events = Channel<LoginEvent>(Channel.BUFFERED)
    val events = _events.receiveAsFlow()

    // Helper function to send events to Channel
    private fun sendEvent(event: LoginEvent) {
        viewModelScope.launch {
            _events.send(event)
        }
    }
    // ===== END CHANNEL EVENT SYSTEM =====

    private val _uiChangePassState = MutableStateFlow(ChangePassUiState())
    val uiChangePassState: StateFlow<ChangePassUiState> = _uiChangePassState.asStateFlow()

    private fun updateChangePassUiState(update: (ChangePassUiState) -> ChangePassUiState) {
        _uiChangePassState.value = update(_uiChangePassState.value)
    }

    // This StateFlow will eventually replace individual state variables
    // For now, it runs PARALLEL with old state for backward compatibility
    private val _uiState = MutableStateFlow(LoginUiState())
    val uiState: StateFlow<LoginUiState> = _uiState.asStateFlow()

    // Password retention for biometric registration multi-CIF flow
    // This temporarily stores the password when biometric registration is initiated
    // It's cleared after successful login or if the flow is cancelled
    private var biometricRegistrationPassword: String? = null

    // Flag to remember if the current login flow is using fingerprint authentication
    // This is needed for multi-CIF retry after company selection
    private var isUsingFingerprintAuth: Boolean = false

    // Company selection dialog state - REMOVED: Using Channel events instead
    // Dialog showing is now a one-time event sent via Channel to prevent auto-show on back navigation

    // Helper function to update UI state
    private fun updateUiState(update: (LoginUiState) -> LoginUiState) {
        _uiState.value = update(_uiState.value)
    }
    // ===== END UNIFIED STATE =====

    // các state từ Composable (KEEP for backward compatibility)
    private val _isActive = MutableStateFlow<Boolean>(false)
    val isActive = _isActive.asStateFlow()

    // Biến chứa username
    private val _username = MutableStateFlow("")
    val username = _username.asStateFlow()

    // Password state
    private val _password = MutableStateFlow("")
    val password = _password.asStateFlow()

    // Password visibility state
    private val _passwordVisible = MutableStateFlow(false)
    val passwordVisible = _passwordVisible.asStateFlow()

    // Username tooltip display state
    private val _showUsernameTooltip = MutableStateFlow(false)
    val showUsernameTooltip: StateFlow<Boolean> = _showUsernameTooltip.asStateFlow()

    // Toggle username tooltip visibility
    fun toggleUsernameTooltip() {
        _showUsernameTooltip.value = !_showUsernameTooltip.value
        // Also update the uiState to ensure UI observes the change
        updateUiState { it.copy(showUsernameTooltip = _showUsernameTooltip.value) }
    }

    // Show username tooltip
    fun showUsernameTooltip() {
        _showUsernameTooltip.value = true
        // Also update the uiState to ensure UI observes the change
        updateUiState { it.copy(showUsernameTooltip = true) }
    }

    // Hide username tooltip
    fun hideUsernameTooltip() {
        _showUsernameTooltip.value = false
        // Also update the uiState to ensure UI observes the change
        updateUiState { it.copy(showUsernameTooltip = false) }
    }

    // Migrated to Channel events - see LoginEvent.LoginResult
    // private val _loginState = SingleLiveEvent<Resource<LoginDomain>>()
    // val loginState: LiveData<Resource<LoginDomain>> get() = _loginState

    var currentUsername = MutableLiveData<String>("")

    // LiveData for tracking username
    private val _userName = MutableLiveData<String>()
    val userName: LiveData<String> get() = _userName

    // LiveData for full name
    private val _fullName = MutableLiveData<String>()
    val fullName: LiveData<String> get() = _fullName

    // LiveData validation state
    private val _usernameError = MutableLiveData<String?>(null)
    val usernameError: LiveData<String?> get() = _usernameError

    private val _passwordError = MutableLiveData<String?>(null)
    val passwordError: LiveData<String?> get() = _passwordError

    private val _multiCifError = MutableLiveData<String?>(null)
    val multiCifError: LiveData<String?> get() = _multiCifError

    // Function to validate if company is selected
    private fun validateCompanySelection(): Boolean {
        return if (multiCifResponse.value != null && cifSharedSelected == null) {
            _multiCifError.postValue(resourceProvider.getString(R.string.validation_company_not_selected))
            false
        } else {
            _multiCifError.postValue(null)
            true
        }
    }

    // Event to show error dialog - moved to Channel events

    // LiveData for password change
    private val _oldPasswordError = SingleLiveEvent<String?>()
    val oldPasswordError: SingleLiveEvent<String?> get() = _oldPasswordError

    private val _newPasswordError = SingleLiveEvent<String?>()
    val newPasswordError: SingleLiveEvent<String?> get() = _newPasswordError

    private val _confirmPasswordError = SingleLiveEvent<String?>()
    val confirmPasswordError: SingleLiveEvent<String?> get() = _confirmPasswordError

    // Password change result
    // Migrated to Channel events - see LoginEvent.ChangePasswordResult
    // private val _changePasswordResult = SingleLiveEvent<Resource<ChangePasswordDomain>>()
    // val changePasswordResult: SingleLiveEvent<Resource<ChangePasswordDomain>> get() = _changePasswordResult

    // Migrated to Channel events - see LoginEvent.ForceUpdateResult
    // private val _forceUpdateResult = SingleLiveEvent<Resource<ForceUpdateDomain>>()
    // val forceUpdateResult: SingleLiveEvent<Resource<ForceUpdateDomain>> get() = _forceUpdateResult

    val loginResponse = SingleLiveEvent<LoginDomain>()
    val accountFirstActive = SingleLiveEvent<LoginDomain>()
    val updatableResponse = SingleLiveEvent<LoginDomain>()
    val showDialogUpdatableApp = SingleLiveEvent<LoginDomain>()
    val isForceUpdate = SingleLiveEvent<LoginDomain>()
    val showForgetPwdDialog = SingleLiveEvent<LoginDomain>()
    val isLoginAuthentication = SingleLiveEvent<LoginDomain>()
    val expirePassLoginResponse = SingleLiveEvent<LoginDomain>()
    val warningPwdAboutExpireLoginResponse = SingleLiveEvent<LoginDomain>()

    // phat sinh 1 usserr nhieu cif
    val pwdExpire881Response = SingleLiveEvent<LoginDomain>()

    // 1 user nhieu cif - Keep public for backward compatibility with LoginFragment
    // TODO: Make private after migrating LoginFragment to new architecture
    val multiCifResponse = MutableLiveData<LoginDomain?>()

    // Biometric availability check result
    // Migrated to Channel events - see LoginEvent.ShowBiometricNotAvailable
    // val biometricNotAvailable = SingleLiveEvent<String>()

    // Company list - Keep public for backward compatibility with LoginFragment
    // TODO: Make private after migrating LoginFragment to new architecture
    var cifSharedList: ArrayList<CifSharedDomain>? = null
    private val _selectedCompany = MutableStateFlow<CifSharedDomain?>(null)
    val selectedCompany = _selectedCompany.asStateFlow()
    var cifSharedSelected: CifSharedDomain?
        get() = _selectedCompany.value
        set(value) {
            _selectedCompany.value = value
        }

    // Handle error cases - Calls BOTH LiveData (backward compatibility) AND Channel (NewUI)
    // WARNING: When adding new handlers, MUST call both systems!
    private val codeHandlers = mapOf(
        ResponseCode.PASSWORD_FORGOT to { res: LoginDomain ->
            // LiveData for backward compatibility
            showForgetPwdDialog.postValue(res)
            // Channel for NewUI fragments
            sendEvent(LoginEvent.ShowForgotPasswordDialog(res.status?.message ?: ""))
        },
        ResponseCode.FORCE_UPDATE to { res: LoginDomain ->
            // LiveData for backward compatibility
            isForceUpdate.postValue(res)
            // Channel for NewUI fragments
            sendEvent(LoginEvent.ShowForceUpdateDialog(res.status?.message ?: ""))
        },
        ResponseCode.FIRST_LOGIN to { res: LoginDomain ->
            // LiveData for backward compatibility
            accountFirstActive.postValue(res)
            // Channel for NewUI fragments
            sendEvent(LoginEvent.ShowFirstLoginDialog(res))
        },
        ResponseCode.TWO_FA_REQUIRED to { res: LoginDomain ->
            // LiveData only - required by Active2FAStep2Fragment
            loginResponse.postValue(res)
        },
        ResponseCode.PASSWORD_EXPIRY_WARNING to { res: LoginDomain ->
            // LiveData for backward compatibility
            warningPwdAboutExpireLoginResponse.postValue(res)
            // Channel for NewUI fragments
            sendEvent(LoginEvent.ShowPasswordExpiryWarning(res))
        },
        ResponseCode.PASSWORD_EXPIRED_NEW to { res: LoginDomain ->
            // LiveData for backward compatibility
            warningPwdAboutExpireLoginResponse.postValue(res)
            // Channel for NewUI fragments
            sendEvent(LoginEvent.ShowPasswordExpiryNew(res))
        },
        ResponseCode.PASSWORD_EXPIRED_881 to { res: LoginDomain ->
            // LiveData for backward compatibility
            pwdExpire881Response.postValue(res)
            // Channel for NewUI fragments - send specific event for password expired 881
            sendEvent(LoginEvent.ShowPasswordExpired881Dialog(res))
        },
        ResponseCode.LOGIN_AUTHENTICATION to { res: LoginDomain ->
            // LiveData for backward compatibility
            isLoginAuthentication.postValue(res)
            // Channel for NewUI fragments
            sendEvent(
                LoginEvent.NavigateTo2FA(
                    addField1 = res.addField1,
                    username = currentUsername.value,
                    cifNo = cifSharedSelected?.enterpriseid,
                ),
            )
        },
        ResponseCode.USER_MULTI_CIF to { res: LoginDomain ->
            // Store companies in ViewModel for later use
            cifSharedList = res.listCifShared?.let { ArrayList(it) }

            // Reset selected company and show company selection sheet
            res.listCifShared?.let { companies ->
                updateUiState { state ->
                    state.copy(
                        selectedCompany = null,
                        dialog = DialogState.CompanySelection(companies = companies),
                    )
                }
            }
        },
    )

    init {
        if (environmentProvider.isBuildConfigDebug()) {
            _password.value = AuthConstants.DEBUG_PASSWORD
            // Sync with unified state
            updateUiState { state ->
                state.copy(password = AuthConstants.DEBUG_PASSWORD)
            }
        }

        // Initialize unified state with current user profile
        updateUiState { state ->
            state.copy(
                isActive = userProf.isActive(),
                fullName = userProf.getFullName() ?: "",
                savedUsername = userProf.getUserName() ?: "",
            )
        }
    }

    /**
     * Cập nhật trạng thái đăng nhập từ sessionManager
     */
    fun updateLoginStatus() {
        // Old system (backward compatibility)
        _isActive.value = userProf.isActive()
        _userName.postValue(userProf.getUserName() ?: "")
        _fullName.postValue(userProf.getFullName() ?: "")

        // NEW: Sync with unified state
        updateUiState { state ->
            state.copy(
                isActive = userProf.isActive(),
                fullName = userProf.getFullName() ?: "",
                savedUsername = userProf.getUserName() ?: "",
            )
        }
    }

    /**
     * Đăng xuất khỏi tài khoản hiện tại
     */
    fun logout() {
        crashlyticsManager.clearUserIdentifier()
        crashlyticsManager.log("User logged out")

        userProf.clearProfile()
        appConfig.updateFingerID("")

        // reset state (old system)
        multiCifResponse.postValue(null)
        cifSharedList = null
        cifSharedSelected = null

        // NEW: Reset unified state
        _uiState.value = LoginUiState() // Reset to initial state

        // Clear OTT data completely (including database)
        ottManager.cleanup()
        transferCacheManager.clearTransferALL()
        ottRegistrationRepository.clearAllOttCache(needClearCahceCountTimeShowRegisterOTT = true)
        updateLoginStatus()
    }

    /**
     * Kiểm tra tính hợp lệ của username
     * @return true nếu username hợp lệ
     */
    private fun validateUsername(username: String): Boolean {
        return when {
            username.isEmpty() -> {
                val errorMsg = resourceProvider.getString(R.string.validation_username_empty)
                _usernameError.postValue(errorMsg) // Keep for collecting errors
                // Remove inline error display
                updateUiState { state ->
                    state.copy(usernameError = null) // Don't show inline
                }
                false
            }

            username.length > ValidationConstants.USERNAME_MAX_LENGTH -> {
                val errorMsg = resourceProvider.getString(R.string.validation_username_max_length)
                _usernameError.postValue(errorMsg) // Keep for collecting errors
                updateUiState { state ->
                    state.copy(usernameError = null) // Don't show inline
                }
                false
            }

            else -> {
                _usernameError.postValue(null)
                updateUiState { state ->
                    state.copy(usernameError = null)
                }
                true
            }
        }
    }

    /**
     * Kiểm tra tính hợp lệ của password
     * @return true nếu password hợp lệ
     */
    private fun validatePassword(password: String): Boolean {
        return when {
            password.isEmpty() -> {
                val errorMsg = resourceProvider.getString(R.string.validation_password_empty)
                _passwordError.postValue(errorMsg) // Keep for collecting errors
                // Remove inline error display
                updateUiState { state ->
                    state.copy(passwordError = null) // Don't show inline
                }
                false
            }

            password.length < ValidationConstants.PASSWORD_MIN_LENGTH -> {
                val errorMsg = resourceProvider.getString(R.string.validation_password_min_length)
                _passwordError.postValue(errorMsg) // Keep for collecting errors
                // Remove inline error display
                updateUiState { state ->
                    state.copy(passwordError = null) // Don't show inline
                }
                false
            }

            password.length > ValidationConstants.PASSWORD_MAX_LENGTH -> {
                val errorMsg = resourceProvider.getString(R.string.validation_password_max_length)
                _passwordError.postValue(errorMsg) // Keep for collecting errors
                updateUiState { state ->
                    state.copy(passwordError = null) // Don't show inline
                }
                false
            }

            // Check if password contains only spaces
            password.trim().isEmpty() -> {
                val errorMsg = resourceProvider.getString(R.string.validation_password_only_spaces)
                _passwordError.postValue(errorMsg) // Keep for collecting errors
                updateUiState { state ->
                    state.copy(passwordError = null) // Don't show inline
                }
                false
            }

            else -> {
                _passwordError.postValue(null)
                updateUiState { state ->
                    state.copy(passwordError = null)
                }
                true
            }
        }
    }

    /**
     * Thực hiện đăng nhập với validation
     */
    fun login(
        username: String,
        password: String,
        isActiveFinger: Boolean = false,
        regTouchId: String = "",
    ) {
        // Store fingerprint auth state for potential multi-CIF retry
        isUsingFingerprintAuth = isActiveFinger

        // Reset error states
        _usernameError.postValue(null) // sample => will remove
        _passwordError.postValue(null) // sample => will remove
        _multiCifError.postValue(null)

        // Validate input
        val isUsernameValid = if (!isActive.value) validateUsername(username) else true
        val isPasswordValid = if (isActiveFinger) {
            true
        } else {
            validatePassword(password)
        }

        // Check if company is selected when multiCifResponse exists
        val isCompanyValid = validateCompanySelection()

        // Check if all validations pass
        if (isUsernameValid && isPasswordValid && isCompanyValid) {
            launchJob(showLoading = true) {
                var touchID = ""
                var authenType = ""
                if (isActiveFinger) {
                    touchID = appConfig.getFingerID()
                    authenType = AuthConstants.AUTH_TYPE_TOUCH_ID
                }
                val res = loginUseCase.login(
                    LoginParams(
                        username = username,
                        password = password,
                        cifno = cifSharedSelected?.enterpriseid ?: "",
                        authenType = authenType,
                        regTouchId = regTouchId,
                        touchId = touchID,
                    ),
                )
                handleResource(res) { data ->
                    // Clear any stored biometric password after successful login
                    clearBiometricPassword()

                    // Send LoginResult event for fragments to handle navigation
                    sendEvent(LoginEvent.LoginResult(data))

                    // Update crash reporting
                    crashlyticsManager.setUserIdentifier()
                    crashlyticsManager.log("User logged in successfully")
                }
            }
        } else {
            // Show error dialog with the first error message
            val errorMessage = _usernameError.value ?: _passwordError.value ?: _multiCifError.value
            errorMessage?.let {
                sendEvent(LoginEvent.ShowErrorDialog(it))
            }
        }
    }

    // processLoginSuccess removed - fragments handle this directly via LoginResult event

    override fun onDisplayErrorMessage(exception: AppException) {
        if (exception is AppException.ApiException && exception.requestPath == Constants.MB_LOGIN) {
            printLog("ErrorCode with JsonData: ${exception.rawResponseJson}")
            val res =
                Utils.g().provideGson().fromJson(exception.rawResponseJson, LoginDomain::class.java)
            if (Tags.FIRST_LOGIN_SIGNAL == res.corpUser?.status) {
                if (Tags.NEW_PASSWORD_EXPIRE == res.status?.code || ResponseCode.FIRST_LOGIN == res.status?.code) {
                    codeHandlers[res.status?.code]?.invoke(res)
                        ?: super.onDisplayErrorMessage(exception)
                } else {
                    accountFirstActive.postValue(res)
                }
            } else {
                codeHandlers[res.status?.code]?.invoke(res)
                    ?: super.onDisplayErrorMessage(exception)
            }
        } else {
            super.onDisplayErrorMessage(exception)
        }
    }

    /**
     * Hàm xác thực input đổi mật khẩu
     * @return true nếu tất cả input hợp lệ, false nếu có lỗi
     */
    fun validatePasswordChange(
        oldPassword: String,
        newPassword: String,
        confirmPassword: String,
    ): Boolean {
        // Reset errors
        _oldPasswordError.postValue(null)
        _newPasswordError.postValue(null)
        _confirmPasswordError.postValue(null)

        // Validate old password - first priority
        if (oldPassword.isEmpty()) {
            _oldPasswordError.postValue(
                resourceProvider.getString(R.string.validation_old_password_empty),
            )
            sendEvent(LoginEvent.ShowErrorDialog(_oldPasswordError.value!!))
            return false
        }

        if (oldPassword.length < ValidationConstants.PASSWORD_MIN_LENGTH) {
            _oldPasswordError.postValue(
                resourceProvider.getString(R.string.validation_old_password_min_length),
            )
            sendEvent(LoginEvent.ShowErrorDialog(_oldPasswordError.value!!))
            return false
        }

        // Validate new password - second priority
        if (newPassword.isEmpty()) {
            _newPasswordError.postValue(
                resourceProvider.getString(R.string.validation_new_password_empty),
            )
            sendEvent(LoginEvent.ShowErrorDialog(_newPasswordError.value!!))
            return false
        }

        if (newPassword.length < ValidationConstants.PASSWORD_MIN_LENGTH) {
            _newPasswordError.postValue(
                resourceProvider.getString(R.string.validation_new_password_min_length),
            )
            sendEvent(LoginEvent.ShowErrorDialog(_newPasswordError.value!!))
            return false
        }

        // Check new password doesn't match old password
        if (newPassword == oldPassword) {
            _newPasswordError.postValue(resourceProvider.getString(R.string.validation_password_same_as_old))
            sendEvent(LoginEvent.ShowErrorDialog(_newPasswordError.value!!))
            return false
        }

        // Check new password doesn't match username
        val username = _userName.value
        if (username != null && newPassword == username) {
            _newPasswordError.postValue(
                resourceProvider.getString(R.string.validation_password_same_as_username),
            )
            sendEvent(LoginEvent.ShowErrorDialog(_newPasswordError.value!!))
            return false
        }

        // Validate confirm password - third priority
        if (confirmPassword.isEmpty()) {
            _confirmPasswordError.postValue(
                resourceProvider.getString(R.string.validation_confirm_password_empty),
            )
            sendEvent(LoginEvent.ShowErrorDialog(_confirmPasswordError.value!!))
            return false
        }

        if (confirmPassword.length < ValidationConstants.PASSWORD_MIN_LENGTH) {
            _confirmPasswordError.postValue(
                resourceProvider.getString(R.string.validation_confirm_password_min_length),
            )
            sendEvent(LoginEvent.ShowErrorDialog(_confirmPasswordError.value!!))
            return false
        }

        // Check new password and confirm password match
        if (newPassword != confirmPassword) {
            _confirmPasswordError.postValue(
                resourceProvider.getString(R.string.validation_password_mismatch),
            )
            sendEvent(LoginEvent.ShowErrorDialog(_confirmPasswordError.value!!))
            return false
        }

        return true
    }

    private val _eventsChangePass = Channel<ChangePassEvent>(Channel.BUFFERED)
    val eventsChangePass = _eventsChangePass.receiveAsFlow()

    private fun sendEventChangePass(event: ChangePassEvent) {
        viewModelScope.launch {
            _eventsChangePass.send(event)
        }
    }

    /**
     * Hàm đổi mật khẩu gọi API
     * @param oldPassword mật khẩu cũ
     * @param newPassword mật khẩu mới
     */
    fun changePassword() {
        launchJob(showLoading = true) {
            try {
                val res = loginUseCase.changePassword(
                    ChangePasswordParams(
                        cifno = userProf.getCifNo().toString(),
                        newPass = _uiChangePassState.value.passwordNew,
                        oldPass = _uiChangePassState.value.passwordOld,
                        username = userName.value.toString(),
                        status = userProf.getCorpUserStatus().toString(),
                    ),
                )
                handleResource(res) { data ->
                    // Send event NewUIChangePassWordFragment
                    sendEventChangePass(ChangePassEvent.NewUIChangePasswordResult(data))
                    // Send via Channel for one-time delivery
                    sendEvent(LoginEvent.ChangePasswordResult(data))
                } // viewmodel emmit ui event success , fragmetn collect va hiển thị dialog
            } catch (e: Exception) {
                printLog(e.printStackTrace())
            }
        }
    }

    /**
     * Helper function để dispatch force update events cho cả 2 systems
     * Centralized dual-dispatch during migration period
     */
    private fun sendForceUpdateEvent(data: ForceUpdateDomain) {
        viewModelScope.launch {
            // 1. Channel event for NewUI (primary)
            _events.send(LoginEvent.HandleForceUpdate(ListFunctionId.FORCE_UPDATE_ALL))

            // 2. Send ForceUpdateResult event
            sendEvent(LoginEvent.ForceUpdateResult(data))
        }
    }

    /**
     * Hàm forceupdate gọi API
     */
    fun forceUpdate() {
        launchJobSilent {
            try {
                val res = loginUseCase.forceUpdate(
                    ForceUpdateParams(
                        roleId = userProf.getRoleId() ?: "",
//                        username = userProf.getUserName() ?: "",
                    ),
                )
                handleResourceSilent(
                    resource = res,
                    onSuccess = { data ->
                        // Use helper instead of direct postValue
                        sendForceUpdateEvent(data)
                        // Luu lai dung cac man hinh sau neu yeu cau
                        data.versionApp?.let { appConfig.saveConfigList(it) }
                    },
                )
            } catch (e: Exception) {
                printLog(e.printStackTrace())
            }
        }
    }

    fun enableTouchID(): Boolean {
        return !TextUtils.isEmpty(appConfig.getFingerID())
    }

    fun checkBiometricForLogin() {
        val status = biometricManager.getStatus()

        when (status) {
            BiometricStatus.NotSupportedOS -> {
                val message = resourceProvider.getString(R.string.biometric_os_not_supported)
                // LiveData for backward compatibility
                sendEvent(LoginEvent.ShowBiometricNotAvailable(message))
                // Channel for NewUI fragments - specific event for OS not supported
                sendEvent(LoginEvent.ShowBiometricOsNotSupported(message))
            }
            BiometricStatus.NotConfiguredDevice -> {
                val message = resourceProvider.getString(R.string.biometric_device_not_enrolled)
                // LiveData for backward compatibility
                sendEvent(LoginEvent.ShowBiometricNotAvailable(message))
                // Channel for NewUI fragments - specific event for device not enrolled
                sendEvent(LoginEvent.ShowBiometricDeviceNotEnrolled(message))
            }
            BiometricStatus.NotConfiguredApp -> {
                // App not configured - need to verify password to enable biometric
                sendEvent(LoginEvent.ShowVerifyPasswordForBiometric)
            }
            BiometricStatus.Ready -> {
                // Biometric is ready - show prompt
                sendEvent(LoginEvent.ShowBiometricPrompt)
            }
        }
    }

    // State update functions - SYNCED WITH UI STATE
    fun updateUsername(newUsername: String) {
        // Old system (backward compatibility)
        _username.value = newUsername
        // Reset CompanyDropdown related values when username changes
        if (null != multiCifResponse.value) {
            cifSharedList = null
            cifSharedSelected = null
            multiCifResponse.postValue(null)
        }

        // Validate username if not empty (real-time validation)
        val errorMsg = if (newUsername.isNotEmpty()) {
            when {
                newUsername.length > ValidationConstants.USERNAME_MAX_LENGTH -> resourceProvider.getString(R.string.validation_username_max_length)
                else -> null
            }
        } else {
            null // Don't show error for empty field until submit
        }

        _usernameError.postValue(errorMsg)

        // NEW: Sync with unified state - Don't show inline errors
        updateUiState { state ->
            state.copy(
                username = newUsername,
                usernameError = null, // Don't show inline validation
                selectedCompany = null,
            )
        }
    }

    /**
     * Request focus to password field
     * Used when showing dialog after session expired
     */
    fun requestPasswordFocus() {
        updateUiState { state ->
            state.copy(passwordFocusTrigger = state.passwordFocusTrigger + 1)
        }
    }

    fun updatePassword(newPassword: String) {
        // Old system (backward compatibility)
        _password.value = newPassword

        // Validate password if not empty (real-time validation)
        val errorMsg = if (newPassword.isNotEmpty()) {
            when {
                newPassword.length < ValidationConstants.PASSWORD_MIN_LENGTH -> resourceProvider.getString(R.string.validation_password_min_length)
                newPassword.length > ValidationConstants.PASSWORD_MAX_LENGTH -> resourceProvider.getString(R.string.validation_password_max_length)
                newPassword.trim().isEmpty() -> resourceProvider.getString(R.string.validation_password_only_spaces_short)
                else -> null
            }
        } else {
            null // Don't show error for empty field until submit
        }

        _passwordError.postValue(errorMsg)

        // NEW: Sync with unified state - Don't show inline errors
        updateUiState { state ->
            state.copy(
                password = newPassword,
                passwordError = null, // Don't show inline validation
            )
        }
    }

    // NewUIChangePassWord
    private fun validateOldPassword(oldPassword: String): String? {
        if (oldPassword.isEmpty()) return null
        return if (oldPassword.length < ValidationConstants.PASSWORD_MIN_LENGTH) {
            resourceProvider.getString(com.vietinbank.feature_login.R.string.change_password_condition_old_pass)
        } else {
            null
        }
    }

    private fun validateNewPassword(
        newPassword: String,
        oldPassword: String,
        userName: String?,
    ): String? {
        if (newPassword.isEmpty()) return null
        return when {
            newPassword.length < ValidationConstants.PASSWORD_MIN_LENGTH ->
                resourceProvider.getString(com.vietinbank.feature_login.R.string.change_password_condition)

            newPassword.length > ValidationConstants.CHANGE_PASSWORD_MAX_LENGTH ->
                resourceProvider.getString(com.vietinbank.feature_login.R.string.change_password_condition)

            !isAllowedPasswordChars(newPassword) ->
                resourceProvider.getString(com.vietinbank.feature_login.R.string.change_password_condition_old)

            !isPasswordComplex(newPassword) ->
                resourceProvider.getString(com.vietinbank.feature_login.R.string.change_password_condition_new)

            newPassword == oldPassword ->
                resourceProvider.getString(com.vietinbank.feature_login.R.string.change_password_condition_again)

            userName != null && newPassword == userName ->
                resourceProvider.getString(com.vietinbank.feature_login.R.string.change_password_condition_again)

            else -> null
        }
    }

    /**
     * Khi oldPassword thay đổi, chỉ xử lý lại errorNew theo rule "new == old".
     * - Nếu new trống -> giữ nguyên errorNew cũ.
     * - Nếu new == old -> set lỗi trùng (sameAsOldMsg).
     * - Nếu trước đó errorNew chính là sameAsOldMsg nhưng hiện tại không còn trùng nữa -> re-validate full cho new.
     * - Còn lại: giữ nguyên errorNew cũ (tránh đè lỗi khác).
     */
    private fun computeErrorNewWhenOldChanges(
        state: ChangePassUiState,
        oldPassword: String,
        sameAsOldMsg: String,
    ): String? {
        val newPwd = state.passwordNew
        if (newPwd.isEmpty()) return state.errorNew

        val userName = _userName.value
        return if (newPwd == oldPassword) {
            sameAsOldMsg
        } else {
            if (state.errorNew == sameAsOldMsg) {
                // Hết trùng old -> tính lại full rule cho new
                validateNewPassword(newPwd, oldPassword, userName)
            } else {
                // Đang có lỗi khác hoặc không lỗi -> giữ nguyên
                state.errorNew
            }
        }
    }

    // ===== Public APIs bạn đang dùng (KHÔNG đổi tên) =====
    fun updatePasswordOld(oldPassword: String) {
        println(
            "Phongdc: cifno " + userProf.getCifNo().toString() + " username " +
                userName.value.toString() + " status " + userProf.getCorpUserStatus().toString() +
                " getUserName" + userProf.getUserName(),
        )
        val sameAsOldMsg = resourceProvider.getString(
            com.vietinbank.feature_login.R.string.change_password_condition_again,
        )

        updateChangePassUiState { state ->
            // 1) Lỗi cho oldPassword
            val errorOld = validateOldPassword(oldPassword)

            // 2) Tính lại lỗi cho "new" chỉ cho rule: new == old (và clear nếu trước đó là rule này)
            val errorNew = computeErrorNewWhenOldChanges(state, oldPassword, sameAsOldMsg)

            // 3) Tạo state mới trước khi tính enable
            val next = state.copy(
                passwordOld = oldPassword,
                errorOld = errorOld,
                errorNew = errorNew,
            )

            // 4) Derived: có cho phép submit không?
            next.copy(isSubmitEnabled = next.canSubmit())
        }
    }

    fun updatePasswordNew(newPassword: String) {
        updateChangePassUiState { state ->
            val sameAsAgainMsg = resourceProvider.getString(R.string.validation_password_mismatch)

            // 1) Lỗi cho newPassword (full rule, có xét old + username)
            val userName = _userName.value
            val errorMsg = validateNewPassword(
                newPassword = newPassword,
                oldPassword = _uiChangePassState.value.passwordOld,
                userName = userName,
            )

            // 2) Tính lại lỗi cho "again" chỉ cho rule: new != again
            val errorMsgAgain = state.passwordAgain.isNotEmpty() && state.passwordAgain != newPassword
            val errorAgain = if (errorMsgAgain) {
                sameAsAgainMsg
            } else {
                if (state.errorAgain == sameAsAgainMsg) null else state.errorAgain
            }

            // 3) Tạo state mới trước khi tính enable
            val next = state.copy(
                passwordNew = newPassword,
                errorNew = errorMsg,
                errorAgain = errorAgain,
            )

            // 4) Derived: có cho phép submit không?
            next.copy(isSubmitEnabled = next.canSubmit())
        }
    }

    fun updatePasswordAgain(againPassword: String) {
        // Validate confirm password - rule: đủ min length + phải khớp new
        val errorMsg = if (againPassword.isNotEmpty()) {
            when {
                againPassword.length < ValidationConstants.PASSWORD_MIN_LENGTH ->
                    resourceProvider.getString(com.vietinbank.feature_login.R.string.change_password_condition)

                againPassword != _uiChangePassState.value.passwordNew ->
                    resourceProvider.getString(R.string.validation_password_mismatch)

                else -> null
            }
        } else {
            null
        }

        updateChangePassUiState { state ->
            val next = state.copy(
                passwordAgain = againPassword,
                errorAgain = errorMsg,
            )
            next.copy(isSubmitEnabled = next.canSubmit())
        }
    }

    // Giữ logic enable ở 1 chỗ, dễ test & tái sử dụng (KHÔNG đổi)
    private fun ChangePassUiState.canSubmit(): Boolean =
        passwordOld.isNotEmpty() &&
            passwordNew.isNotEmpty() &&
            passwordAgain.isNotEmpty() &&
            errorOld.isNullOrEmpty() &&
            errorNew.isNullOrEmpty() &&
            errorAgain.isNullOrEmpty()

    fun togglePasswordVisibility() {
        // Old system (backward compatibility)
        _passwordVisible.value = !_passwordVisible.value

        // NEW: Sync with unified state
        updateUiState { state ->
            state.copy(passwordVisible = !state.passwordVisible)
        }
    }

    fun updateActiveStatus(status: Boolean) {
        // Old system (backward compatibility)
        _isActive.value = status

        // NEW: Sync with unified state
        updateUiState { state ->
            state.copy(isActive = status)
        }
    }

    fun updateSelectedCompany(company: CifSharedDomain?) {
        // Old system (backward compatibility)
        _selectedCompany.value = company

        // NEW: Sync with unified state
        updateUiState { state ->
            state.copy(selectedCompany = company)
        }
    }

    fun setEnableCheckEkyc() {
        prefs.setBoolean(Tags.CHECKEKYC, true)
    }

    // Public API for Fragment - Proper Encapsulation
    /**
     * Get companies for selection dialog
     * Encapsulates internal field access
     */
    fun getCompaniesForSelection(): List<CifSharedDomain>? {
        return cifSharedList ?: multiCifResponse.value?.listCifShared
    }

    /**
     * Store password temporarily for biometric registration with multi-CIF flow
     * This is needed when login from biometric dialog might trigger multi-CIF selection
     * The password is cleared after successful login or cancellation
     */
    fun storeBiometricPassword(password: String) {
        biometricRegistrationPassword = password
    }

    /**
     * Retrieve the stored password for multi-CIF retry during biometric registration
     * Returns null if no password is stored
     */
    fun getLastLoginPassword(): String? {
        return biometricRegistrationPassword
    }

    /**
     * Check if the current login flow is using fingerprint authentication
     * This is needed for multi-CIF retry logic
     */
    fun isUsingFingerprint(): Boolean {
        return isUsingFingerprintAuth
    }

    /**
     * Clear the stored biometric password and fingerprint state
     * Should be called after successful login or when cancelling the flow
     */
    fun clearBiometricPassword() {
        biometricRegistrationPassword = null
        isUsingFingerprintAuth = false
    }

    /**
     * Check if multi-CIF is active
     */
    fun isMultiCifActive(): Boolean {
        return multiCifResponse.value != null
    }

    /**
     * Get active user info for display
     */
    fun getActiveUserFullName(): String? {
        return if (isActive()) getFullName() else null
    }

    // Company Dialog Management Functions
    fun clearMultiCifState() {
        // Clear multi-cif state to allow re-triggering dialog
        multiCifResponse.postValue(null)
        cifSharedList = null
        cifSharedSelected = null
        _selectedCompany.value = null
        updateUiState { state ->
            state.copy(
                selectedCompany = null,
            )
        }
    }

    fun clearCompanySelection() {
        // Clear all company selection state and dialog
        cifSharedSelected = null
        _selectedCompany.value = null
        updateUiState { state ->
            state.copy(
                selectedCompany = null,
                dialog = DialogState.None,
            )
        }
    }

    fun selectCompany(company: CifSharedDomain) {
        // Update old system
        cifSharedSelected = company

        // Update new unified state
        updateUiState { state ->
            state.copy(selectedCompany = company)
        }
        _selectedCompany.value = company

        // Clear any previous error
        _multiCifError.postValue(null)
    }

    fun confirmCompanySelection() {
        // The Fragment will handle login after company selection
        // Dialog dismissal is handled by the Fragment via dialog callbacks
    }

    // Dialog Management
    private fun setDialog(dialogState: DialogState) {
        updateUiState { state ->
            state.copy(dialog = dialogState)
        }
    }

    fun dismissDialog() {
        updateUiState { state ->
            state.copy(dialog = DialogState.None)
        }
    }

    // Centralized Login Logic (moved from Fragment)
    fun onLoginClicked() {
        val u = username.value.trim()
        val p = password.value

        // Validation for active users (returning)
        if (isActive()) {
            if (p.isBlank()) {
                setDialog(
                    DialogState.Notice(
                        message = resourceProvider.getString(R.string.login_password_required),
                    ),
                )
                return
            }
        } else {
            // Validation for new users
            if (u.isBlank() || p.isBlank()) {
                setDialog(
                    DialogState.Notice(
                        message = resourceProvider.getString(R.string.login_full_info_required),
                    ),
                )
                return
            }
        }

        // Check multi-CIF selection if needed
        if (multiCifResponse.value != null && cifSharedSelected == null) {
            // Don't show dialog here - it's shown via Channel event
            return
        }

        // Perform login
        if (isActive()) {
            login(getUserName().toString(), p)
        } else {
            login(u, p)
        }
    }

    /**
     * 1: Chỉ cho phép ký tự a-z, A-Z, 0-9 và các ký tự đặc biệt trong allowedSet.
     * Trả về true nếu toàn bộ chuỗi hợp lệ (có thể gọi trong onValueChange để chặn nhập).
     *
     * Mặc định cho phép: @ # $ %
     */
    private fun isAllowedPasswordChars(text: String): Boolean {
        if (text.isEmpty()) return true // cho phép rỗng trong lúc gõ
        val allowedSet = CHANGE_PASSWORD_CONDITION.toSet()
        return text.all { ch ->
            (ch in 'a'..'z') || (ch in 'A'..'Z') || ch.isDigit() || (ch in allowedSet)
        }
    }

    /**
     * 2: Kiểm tra độ mạnh theo rule:
     * - Ít nhất 1 chữ số
     * - Ít nhất 1 chữ thường
     * - Ít nhất 1 chữ hoa
     * - Ít nhất 1 ký tự thuộc reqSet
     *
     * Mặc định yêu cầu một trong: @ # $ %
     */
    private fun isPasswordComplex(text: String): Boolean {
        if (text.isEmpty()) return false
        val reqSet = CHANGE_PASSWORD_CONDITION.toSet()

        val hasDigit = text.any { it.isDigit() }
        val hasLower = text.any { it in 'a'..'z' }
        val hasUpper = text.any { it in 'A'..'Z' }
        val hasReqSpecial = text.any { it in reqSet }

        return hasDigit && hasLower && hasUpper && hasReqSpecial
    }
}