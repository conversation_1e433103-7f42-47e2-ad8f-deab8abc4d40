package com.vietinbank.feature_login.ui.dialog

import android.os.Bundle
import android.os.Parcelable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.ViewModelProvider
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_ui.base.dialog.BaseDialog
import com.vietinbank.core_ui.components.ButtonSize
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.dialog.DialogLayout
import com.vietinbank.core_ui.components.foundation.pin.PinShowComponent
import com.vietinbank.core_ui.components.foundation.pin.PinShowState
import com.vietinbank.core_ui.components.foundation.pin.StealthOtpInputField
import com.vietinbank.core_ui.components.foundation.pin.toPinValues
import com.vietinbank.core_ui.components.foundation.pin.toTimerFormat
import com.vietinbank.core_ui.components.text.foundation.FoundationClickableText
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.feature_login.R
import com.vietinbank.feature_login.ui.viewmodel.LoginViewModel
import kotlinx.coroutines.delay
import kotlinx.parcelize.Parcelize

/**
 * Result data for user selection
 */
@Parcelize
data class Active2FAStep2Result(
    val action: Active2FAStep2Result.Action,
    val otpInput: String? = null,
) : Parcelable {
    @Parcelize
    enum class Action : Parcelable {
        BACK,
        CONTINUE,
        RESEND,
    }
}

class NewUIActive2FAStep2Dialog : BaseDialog<Active2FAStep2Result>() {
    private val loginViewModel: LoginViewModel by lazy {
        ViewModelProvider(requireParentFragment())[LoginViewModel::class.java]
    }

    companion object {
        private const val TAG = "NewUIActive2FAStep2Dialog"
        private const val ARG_OTP_INPUT_PHONE = "arg_otp_input_phone"
        private const val ARG_OTP_INPUT_EMAIL = "arg_otp_input_email"

        private var pendingBack = false

        fun show(
            fragmentManager: FragmentManager,
            phoneNo: String? = null,
            emailNo: String? = null,
        ) {
            (fragmentManager.findFragmentByTag(TAG) as? DialogFragment)
                ?.dismissAllowingStateLoss()
            fragmentManager.executePendingTransactions()

            val dialog = NewUIActive2FAStep2Dialog().apply {
                arguments = Bundle().apply {
                    putString(ARG_OTP_INPUT_PHONE, phoneNo)
                    putString(ARG_OTP_INPUT_EMAIL, emailNo)
                }
            }

            dialog.show(fragmentManager, TAG)
        }
    }

    private fun sendResultToParent(action: Active2FAStep2Result.Action, otp: String? = null) {
        parentFragmentManager.setFragmentResult(
            resultKey,
            Bundle().apply {
                putBoolean("key_cancelled", false)
                putParcelable(
                    "key_result",
                    Active2FAStep2Result(action = action, otpInput = otp),
                )
            },
        )
    }

    // Dialog configuration
    override val resultKey: String = "newui_active2fa_step2_result"
    override val layout: DialogLayout = DialogLayout.BottomSheet // Bottom sheet style
    override val requiresSecureFlag: Boolean = true // Banking security requirement

    @Composable
    override fun DialogContent(
        visible: Boolean,
        onDismissRequest: () -> Unit,
        onResult: (Active2FAStep2Result) -> Unit,
    ) {
        val otpUi by loginViewModel.otpUi.collectAsState()

        Active2FAStep2Content(
            onConfirm = { otpInput ->
                sendResultToParent(Active2FAStep2Result.Action.CONTINUE, otpInput)
            },
            onDismiss = {
                pendingBack = true
                onDismissRequest()
            },
            phoneNo = arguments?.getString(ARG_OTP_INPUT_PHONE).toString(),
            emailNo = arguments?.getString(ARG_OTP_INPUT_EMAIL).toString(),

            // DÙNG state từ VM (không tự đếm nữa)
            validity = otpUi.remainingValidity,
            resendTime = otpUi.resendRemaining,
            // truyền tín hiệu xuống
            otpStartedSignal = otpUi.otpStartedSignal,

            onResend = { sendResultToParent(Active2FAStep2Result.Action.RESEND) },
        )
    }

    override fun onDismiss(dialog: android.content.DialogInterface) {
        super.onDismiss(dialog)
        if (pendingBack) {
            pendingBack = false
            sendResultToParent(Active2FAStep2Result.Action.BACK) // xử lý sau khi đã đóng
        }
    }
}

@Composable
fun Active2FAStep2Content(
    onConfirm: (String) -> Unit,
    onDismiss: () -> Unit,
    phoneNo: String = "",
    emailNo: String = "",
    validity: Int = Tags.TIME_DIALOG_ACTIVE_STEP2_VALIDITY,
    resendTime: Int = Tags.TIME_DIALOG_ACTIVE_STEP2_RESEND,
    onResend: () -> Unit = {},
    otpStartedSignal: Int = 0, // NEW: tín hiệu từ DialogContent
) {
    var phoneNo by remember { mutableStateOf(phoneNo) }
    var emailNo by remember { mutableStateOf(emailNo) }

    var otpInput by remember { mutableStateOf("") }
    val focusRequester = remember { androidx.compose.ui.focus.FocusRequester() }
    val keyboardController = androidx.compose.ui.platform.LocalSoftwareKeyboardController.current
    val focusManager = LocalFocusManager.current
    val view = androidx.compose.ui.platform.LocalView.current

    // không remember vì VM “đếm” và đẩy số mới xuống; Compose recompose là đủ.
    val remainingValidity = validity
    val resendRemaining = resendTime
    val isCounting = remainingValidity > 0

    // Khi chưa nhận tín hiệu bắt đầu (otpStartedSignal == 0) thì vẫn hiển thị TYPING
    val showTypingBeforeStarted = otpStartedSignal == 0

    LaunchedEffect(otpInput) {
        if (otpInput.length == 6) {
            // 1) Ẩn IME theo Compose
            keyboardController?.hide()
            // 2) Clear focus để IME không bật lại
            focusManager.clearFocus(force = true)

            // 3) Fallback tuyệt đối: dùng InputMethodManager (một số ROM/IME cần bước này)
            val imm = view.context.getSystemService(android.content.Context.INPUT_METHOD_SERVICE)
                as android.view.inputmethod.InputMethodManager
            kotlinx.coroutines.delay(50)
            imm.hideSoftInputFromWindow(view.windowToken, 0)
        } else {
            focusRequester.requestFocus()
            keyboardController?.show()
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = FoundationDesignSystem.Sizer.Padding.padding2),
    ) {
        Surface(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(FoundationDesignSystem.Sizer.Radius.radius32),
            color = FoundationDesignSystem.Colors.white,
            shadowElevation = FoundationDesignSystem.Effects.elevationLg,
        ) {
            Column {
                // Header - matching Figma exactly
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding24),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    // Title: "Thông báo" - Heading H3 (20sp SemiBold) per Figma
                    FoundationText(
                        text = stringResource(R.string.active2fa_step1_title),
                        style = FoundationDesignSystem.Typography.headingH3,
                        color = FoundationDesignSystem.Colors.characterHighlighted,
                        textAlign = TextAlign.Center,
                    )

                    Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap24))

                    HorizontalDivider(
                        color = FoundationDesignSystem.Colors.strokeDivider,
                        thickness = FoundationDesignSystem.Sizer.Stroke.stroke1,
                    )
                }

                // Content
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = FoundationDesignSystem.Sizer.Padding.padding24),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))

                    FoundationText(
                        text = stringResource(R.string.active2fa_step2_content, phoneNo, emailNo),
                        style = FoundationDesignSystem.Typography.captionCaptionLBold,
                        color = FoundationDesignSystem.Colors.characterSecondary,
                        textAlign = TextAlign.Center,
                    )

                    Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap24))

                    // OTP Component with stealth input overlay
                    Box(
                        modifier = Modifier.fillMaxWidth(),
                        contentAlignment = Alignment.Center,
                    ) {
                        PinShowComponent(
                            state = when {
                                otpInput.length == 6 && isCounting -> PinShowState.TYPED
                                isCounting || showTypingBeforeStarted -> PinShowState.TYPING
                                else -> PinShowState.EXPIRED
                            },
                            pinValues = otpInput.toPinValues(6),
                            onRowTap = {
                                if (isCounting) { // chỉ cho focus/bàn phím khi còn hiệu lực
                                    focusRequester.requestFocus()
                                    keyboardController?.show()
                                }
                            },
                            modifier = Modifier.fillMaxWidth(),
                        )

                        // Overlay input ẩn — đặt ngay bên dưới PinShowComponent
                        StealthOtpInputField(
                            value = otpInput,
                            length = 6,
                            onValueChange = { v ->
                                otpInput = v
                                if (v.length == 6) {
                                    // Ẩn IME ngay khi vừa đủ 6 số
                                    keyboardController?.hide()
                                    focusManager.clearFocus(force = true)

                                    // Fallback hệ thống
                                    val imm = view.context.getSystemService(android.content.Context.INPUT_METHOD_SERVICE)
                                        as android.view.inputmethod.InputMethodManager
                                    imm.hideSoftInputFromWindow(view.windowToken, 0)
                                }
                            },
                            focusRequester = focusRequester,
                            enabled = isCounting, // <-- chỉ cho nhập khi còn hiệu lực
                            modifier = Modifier.fillMaxWidth(),
                            onDone = {
                                keyboardController?.hide()
                                focusManager.clearFocus(force = true)

                                // Fallback hệ thống
                                val imm = view.context.getSystemService(android.content.Context.INPUT_METHOD_SERVICE)
                                    as android.view.inputmethod.InputMethodManager
                                imm.hideSoftInputFromWindow(view.windowToken, 0)
                            },
                        )
                    }

                    Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))

                    FoundationText(
                        text = if (isCounting) {
                            stringResource(com.vietinbank.core_ui.R.string.otp_valid_for, remainingValidity.toTimerFormat())
                        } else {
                            stringResource(com.vietinbank.core_ui.R.string.otp_expired_message)
                        },
                        style = FoundationDesignSystem.Typography.captionCaptionLBold,
                        color = if (isCounting) {
                            FoundationDesignSystem.Colors.characterTertiary
                        } else {
                            FoundationDesignSystem.Colors.stateWarning
                        },
                        textAlign = TextAlign.Center,
                        modifier = Modifier.padding(horizontal = FoundationDesignSystem.Sizer.Padding.padding24),
                    )

                    Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap16))

                    // Resend section
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = FoundationDesignSystem.Sizer.Padding.padding20),
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        FoundationText(
                            text = stringResource(com.vietinbank.core_ui.R.string.otp_not_received_question),
                            style = FoundationDesignSystem.Typography.captionCaptionLBold,
                            color = FoundationDesignSystem.Colors.characterSecondary,
                            textAlign = TextAlign.Center,
                        )

                        Spacer(modifier = Modifier.width(FoundationDesignSystem.Sizer.Gap.gap4))

                        if (resendRemaining == 0) {
                            // Clickable resend text
                            FoundationClickableText(
                                text = stringResource(com.vietinbank.core_ui.R.string.resend_otp),
                                onClick = { onResend() }, // giữ nguyên
                                style = FoundationDesignSystem.Typography.captionCaptionLBold,
                                color = FoundationDesignSystem.Colors.characterHighlighted,
                            )
                        } else {
                            // Countdown text (đếm cooldown Gửi lại)
                            FoundationText(
                                text = stringResource(R.string.active2fa_step2_resend, resendRemaining),
                                style = FoundationDesignSystem.Typography.captionCaptionLBold,
                                color = FoundationDesignSystem.Colors.characterSecondary,
                                textAlign = TextAlign.Center,
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap24))
                }
            }
        }

        Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap16))

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = FoundationDesignSystem.Sizer.Padding.padding16),
            horizontalArrangement = Arrangement.spacedBy(FoundationDesignSystem.Sizer.Gap.gap8),
        ) {
            FoundationButton(
                text = stringResource(com.vietinbank.core_ui.R.string.biometric_verify_button_back),
                onClick = {
                    onDismiss()
                },
                modifier = Modifier.weight(1f),
                isLightButton = false,
                size = ButtonSize.Large,
            )

            FoundationButton(
                text = stringResource(com.vietinbank.core_ui.R.string.btn_continue),
                onClick = {
                    onConfirm(otpInput)
                },
                modifier = Modifier.weight(1f),
                isLightButton = otpInput.length == 6 && isCounting,
                enabled = otpInput.length == 6 && isCounting,
                size = ButtonSize.Large,
            )
        }

        Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap24))
    }
}

@Preview(showBackground = true)
@Composable
fun Active2FAStep2Content() {
    AppTheme {
        Active2FAStep2Content(
            onConfirm = {},
            onDismiss = {},
            onResend = {},
            phoneNo = "",
            emailNo = "",
            validity = 270,
            resendTime = 60,
            otpStartedSignal = 0,
        )
    }
}