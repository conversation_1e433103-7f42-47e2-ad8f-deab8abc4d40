package com.vietinbank.feature_login.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.TileMode
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_domain.models.login.CifSharedDomain
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_login.R

/**
 * Các component dùng chung trong module login
 * Created by vandz on 02/03/25.
 */

// Component Card hiển thị thông tin user với avatar và lời chào
@Composable
fun UserInfoCard(
    fullName: String,
    onRefreshClick: () -> Unit,
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .border(
                width = 1.4.dp,
                brush = Brush.linearGradient(
                    colors = listOf(
                        Color(0xFF8CAEFF), // #8CAEFF ở 20.64%
                        Color(0xFFF43F72), // #F43F72 ở 56.17%
                        Color(0xFF7AA3D5), // #7AA3D5 ở 90.43%
                    ),
                    start = Offset(0f, 0f),
                    end = Offset(0.7f, 1.2f), // Điều chỉnh để tạo góc 230.68 độ
                    tileMode = TileMode.Clamp,
                ),
                shape = RoundedCornerShape(10.dp),
            )
            .clip(RoundedCornerShape(10.dp))
            .background(
                color = AppColors.cardBackground,
            )
            .padding(16.dp),
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween,
        ) {
            // left: avatar + text
            Row(verticalAlignment = Alignment.CenterVertically) {
                // avatar - thiết kế hai lớp (mask bên ngoài, person bên trong)
                Box(
                    modifier = Modifier
                        .size(48.dp)
                        .clip(CircleShape),
                ) {
                    // Ảnh mask có background trong suốt
                    Image(
                        painter = painterResource(R.drawable.ic_mask),
                        contentDescription = "Avatar mask",
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.Crop,
                    )

                    // Icon person bên trong, cách viền 1dp
                    Box(
                        modifier = Modifier
                            .size(46.dp)
                            .padding(1.dp)
                            .clip(CircleShape)
                            .background(AppColors.inputBackground)
                            .align(Alignment.Center),
                        contentAlignment = Alignment.Center,
                    ) {
                        Image(
                            painter = painterResource(R.drawable.ic_person),
                            contentDescription = "Person icon",
                            modifier = Modifier.wrapContentSize(),
                        )
                    }
                }
                Spacer(modifier = Modifier.width(12.dp))
                Column {
                    BaseText(
                        text = "Xin chào",
                        color = AppColors.textSecondary,
                        fontCus = 0,
                        textSize = 14.sp,
                    )
                    Spacer(modifier = Modifier.height(2.dp))
                    BaseText(
                        text = fullName,
                        color = AppColors.textPrimary,
                        fontCus = 5, // bold
                        textSize = 16.sp,
                    )
                }
            }

            // right: refresh icon
            Image(
                painter = painterResource(R.drawable.ic_refresh),
                contentDescription = "Refresh",
                modifier = Modifier
                    .size(24.dp)
                    .safeClickable { onRefreshClick() },
            )
        }
    }
}

// TopBar component dùng chung cho các màn hình
@Composable
fun TopBarRow(
    onNotifyClick: () -> Unit,
    onShareClick: () -> Unit,
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // Logo
        Image(
            painter = painterResource(R.drawable.ic_logofull),
            contentDescription = "VietinBank eFAST logo",
            modifier = Modifier.width(140.dp),
        )

        // Row of action icons
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // Vietnam flag with dropdown
            Row(
                modifier = Modifier
                    .border(
                        width = 0.5.dp,
                        color = AppColors.borderColor,
                        shape = RoundedCornerShape(size = 72.dp),
                    )
                    .padding(start = 8.dp, top = 5.dp, end = 8.dp, bottom = 5.dp),
                horizontalArrangement = Arrangement.spacedBy(4.dp, Alignment.Start),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Image(
                    painter = painterResource(R.drawable.ic_flagvn),
                    contentDescription = "Lang Flag",
                    modifier = Modifier.size(24.dp),
                )

                // Text "VI"
                BaseText(
                    text = "VI",
                    color = AppColors.textPrimary,
                    fontCus = 0,
                )

                Image(
                    painter = painterResource(R.drawable.ic_dropdownsmall),
                    contentDescription = "Drop down icon",
                    modifier = Modifier.size(24.dp),
                )
            }

            // Notification icon
            Box(
                modifier = Modifier
                    .clip(CircleShape)
                    .clickable { onNotifyClick() }
                    .padding(4.dp),
                contentAlignment = Alignment.Center,
            ) {
                Image(
                    painter = painterResource(R.drawable.ic_bell),
                    contentDescription = "Bell",
                    modifier = Modifier.size(24.dp),
                )
            }

            // Share icon
            Box(
                modifier = Modifier
                    .clip(CircleShape)
                    .clickable { onShareClick() }
                    .padding(4.dp),
                contentAlignment = Alignment.Center,
            ) {
                Image(
                    painter = painterResource(R.drawable.ic_share),
                    contentDescription = "Share",
                    modifier = Modifier.size(24.dp),
                )
            }
        }
    }
}

// Action box dùng chung cho các màn hình
@Composable
fun HomeActionBox(
    text: String,
    icon: Int,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .heightIn(min = 56.dp)
            .border(
                width = 0.5.dp,
                color = AppColors.borderColor,
                shape = RoundedCornerShape(10.dp),
            )
            .clip(RoundedCornerShape(10.dp))
            .background(AppColors.itemBackground)
            .padding(start = 12.dp, top = 12.dp, end = 12.dp, bottom = 12.dp)
            .safeClickable { onClick() },
        contentAlignment = Alignment.TopStart,
    ) {
        Column(
            horizontalAlignment = Alignment.Start,
        ) {
            // Icon
            if (icon != 0) {
                Image(
                    painter = painterResource(icon),
                    contentDescription = null,
                    modifier = Modifier.size(24.dp),
                )
            }

            // Gap giữa icon và text
            Spacer(modifier = Modifier.height(8.dp))

            // Text
            BaseText(
                text = text,
                color = AppColors.textPrimary,
                fontCus = 4, // Medium font style
                textSize = 14.sp,
                modifier = Modifier.fillMaxWidth(),
            )
        }
    }
}

// Component button ở dưới cùng màn hình
@Composable
fun BottomButton(
    text: String,
    icon: Int,
    onClick: () -> Unit,
) {
    Box(
        modifier = Modifier
            .widthIn(min = 100.dp)
            .heightIn(min = 56.dp)
            .border(
                width = 0.5.dp,
                color = AppColors.borderColor,
                shape = RoundedCornerShape(10.dp),
            )
            .clip(RoundedCornerShape(10.dp))
            .background(AppColors.itemBackground)
            .padding(horizontal = 12.dp, vertical = 8.dp)
            .safeClickable { onClick() },
        contentAlignment = Alignment.Center,
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier.padding(vertical = 4.dp),
        ) {
            // Icon
            Image(
                painter = painterResource(icon),
                contentDescription = null,
                modifier = Modifier.size(24.dp),
            )

            // Khoảng cách giữa icon và text
            Spacer(modifier = Modifier.height(4.dp))

            // Text - cho phép xuống dòng khi cần
            BaseText(
                text = text,
                color = AppColors.textPrimary,
                fontCus = 0,
                textSize = 14.sp,
                modifier = Modifier.wrapContentWidth(),
            )
        }
    }
}

// Component Row với các nút ở dưới cùng màn hình
@Composable
fun BottomRowHome(
    onMarketClick: () -> Unit,
    onSupportClick: () -> Unit,
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // "Thị trường" tab
        BottomButton(
            text = "Thị trường",
            icon = R.drawable.ic_market,
            onClick = { onMarketClick() },
        )

        // Gap giữa 2 nút
        Spacer(modifier = Modifier.width(8.dp))

        // "Hỗ trợ" tab
        BottomButton(
            text = "Hỗ trợ",
            icon = R.drawable.ic_support,
            onClick = { onSupportClick() },
        )
    }
}

@Composable
fun CompanyDropdown(
    companies: List<CifSharedDomain>?,
    selectedCompany: CifSharedDomain?,
    onCompanySelected: (CifSharedDomain) -> Unit,
) {
    var expanded by remember { mutableStateOf(false) }
    val displayText = if (selectedCompany == null) {
        "Chọn công ty"
    } else {
        selectedCompany.enterpriseid + " - " + selectedCompany.enterprisename
    }

    Box(
        modifier = Modifier
            .fillMaxWidth(),
    ) {
        // Dropdown field
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(min = 56.dp)
                .clip(RoundedCornerShape(10.dp))
                .background(AppColors.inputBackground)
                .safeClickable { expanded = true }
                .padding(start = 16.dp, end = 16.dp),
            contentAlignment = Alignment.CenterStart,
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                // Company icon và text
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_creditcard), // Icon hiện có phù hợp nhất
                        contentDescription = "Company Icon",
                        modifier = Modifier.size(24.dp),
                    )

                    Spacer(modifier = Modifier.width(16.dp))

                    BaseText(
                        text = displayText,
                        color = AppColors.textPrimary,
                        fontCus = 0,
                        textSize = 16.sp,
                    )
                }

                // Dropdown arrow
                Image(
                    painter = painterResource(id = R.drawable.ic_dropdownsmall),
                    contentDescription = "Dropdown Arrow",
                    modifier = Modifier.size(24.dp),
                )
            }
        }

        // Dropdown menu
        DropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false },
            modifier = Modifier
                .fillMaxWidth(0.94f)
                .background(AppColors.itemBackground),
        ) {
            companies?.forEach { company ->
                DropdownMenuItem(
                    onClick = {
                        onCompanySelected(company)
                        expanded = false
                    },
                    text = {
                        BaseText(
                            text = (company.enterpriseid + " - " + company.enterprisename),
                            color = Color.Black,
                            fontCus = 0,
                            textSize = 16.sp,
                        )
                    },
                )
            }
        }
    }
}
