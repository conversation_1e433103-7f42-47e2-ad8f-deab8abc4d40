package com.vietinbank.feature_login.ui.fragment

import androidx.lifecycle.LiveData
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.livedata.SingleLiveEvent
import com.vietinbank.core_common.models.UserProfData
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.login.AuthenticationMethod
import com.vietinbank.core_domain.models.login.GenOTPDomain
import com.vietinbank.core_domain.models.login.GenOTPParams
import com.vietinbank.core_domain.models.login.VerifyOTPDomain
import com.vietinbank.core_domain.models.login.VerifyOTPParams
import com.vietinbank.core_domain.usecase.login.LoginUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import java.security.MessageDigest
import javax.inject.Inject

@HiltViewModel
class Active2FAViewModel @Inject constructor(
    private val useCase: LoginUseCase,
    override val sessionManager: ISessionManager,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
) : BaseViewModel() {
    fun saveSession(sessionId: String) = sessionManager.saveSession(sessionId)
    fun saveProfile(userProfData: UserProfData) = userProf.saveProfile(userProfData)
    fun updateFingerID(fingerID: String) = appConfig.updateFingerID(fingerID)
    fun setTimerConfig(timerConfig: Boolean) = appConfig.setTimerConfig(timerConfig)

    // Events - sử dụng SingleLiveEvent để tránh trigger lặp lại
    private val _errorEvent = SingleLiveEvent<String>()
    val errorEvent: LiveData<String> = _errorEvent

    private val _genOTPResponse = SingleLiveEvent<Resource<GenOTPDomain>>()
    val genOTPResponse: SingleLiveEvent<Resource<GenOTPDomain>> get() = _genOTPResponse

    private val _verifyOTPResponse = SingleLiveEvent<Resource<VerifyOTPDomain>>()
    val verifyOTPResponse: SingleLiveEvent<Resource<VerifyOTPDomain>> get() = _verifyOTPResponse

    var addField1: String? = null
    var eFastID: String? = null
    var currentUsername: String? = null
    var currentCifNo: String? = null
    var methodSelected: AuthenticationMethod? = null

    fun genOTP(method: String) {
        if (true) {
            launchJob(showLoading = true) {
                val res = useCase.genOTP(
                    GenOTPParams(
                        username = currentUsername.toString(),
                        type = method,
                        addition1 = addField1.toString(),
                        cifno = currentCifNo,
                    ),
                )
                handleResource(res) { data ->
                    _genOTPResponse.postValue(Resource.Success(data))
                }
            }
        }
    }

    fun verifyOTP(otp: String) {
        if (true) {
            launchJob(showLoading = true) {
                val res = useCase.verifyOTP(
                    VerifyOTPParams(
                        username = currentUsername.toString(),
                        type = methodSelected?.value.toString(),
                        addition1 = addField1.toString(),
                        cifno = currentCifNo,
                        efastId = eFastID.toString(),
                        otp = hashMD5(otp),
                    ),
                )
                handleResource(res) { data ->
                    _verifyOTPResponse.postValue(Resource.Success(data))
                }
            }
        }
    }

    /**
     * Hiển thị lỗi
     */
    fun showError(message: String) {
        _errorEvent.postValue(message)
    }

    /**
     * Hàm băm MD5 cho OTP
     * @param value Chuỗi cần băm
     * @return Chuỗi đã được băm MD5 dưới dạng hex
     */
    private fun hashMD5(value: String): String {
        val digest = MessageDigest.getInstance("MD5")
        val hash = digest.digest(value.toByteArray())
        return hash.joinToString("") { "%02x".format(it) }
    }
}