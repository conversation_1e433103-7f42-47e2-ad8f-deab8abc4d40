package com.vietinbank.feature_login.ui.fragment

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_login.ui.events.ChangePassEvent
import com.vietinbank.feature_login.ui.screen.NewUIActiveScreen
import com.vietinbank.feature_login.ui.screen.NewUIChangePassAction
import com.vietinbank.feature_login.ui.viewmodel.LoginViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * <PERSON><PERSON><PERSON> h<PERSON>nh <PERSON> mật khẩu
 */

@AndroidEntryPoint
class NewUIChangePasswordFragment : BaseFragment<LoginViewModel>() {
    override val viewModel: LoginViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Composable
    override fun ComposeScreen() {
        AppTheme {
            val uiState by viewModel.uiChangePassState.collectAsState()
            NewUIActiveScreen(
                uiState,
                onAction = { action ->
                    handleScreenAction(action)
                },
            )
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Thiết lập các event
        setupEventCollector()
        viewModel.updateLoginStatus()
    }

    private fun setupEventCollector() {
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.eventsChangePass.collect { event ->
                    handleChangePassEvent(event)
                }
            }
        }
    }

    private fun handleChangePassEvent(event: ChangePassEvent) {
        when (event) {
            is ChangePassEvent.NewUIChangePasswordResult -> {
                // Hiển thị thông báo thành công và quay lại màn hình trước
                showNoticeDialog(
                    message = event.data.status.message.toString(),
                    positiveAction = { appNavigator.popBackStack() },
                    positiveButtonText = getString(com.vietinbank.feature_login.R.string.login_action),
                )
            }
        }
    }

    private fun handleScreenAction(action: NewUIChangePassAction) {
        when (action) {
            is NewUIChangePassAction.OnBackClick -> {
                appNavigator.popBackStack()
            }

            is NewUIChangePassAction.OnPasswordChangeOld -> {
                viewModel.updatePasswordOld(action.passwordOld)
            }

            is NewUIChangePassAction.OnPasswordChangeNew -> {
                viewModel.updatePasswordNew(action.passwordNew)
            }

            is NewUIChangePassAction.OnPasswordChangeAgain -> {
                viewModel.updatePasswordAgain(action.passwordAgain)
            }

            is NewUIChangePassAction.OnChangePasswordClick -> {
                viewModel.changePassword()
            }
            is NewUIChangePassAction.OnForgotPasswordClick -> {
                appNavigator.goToLockAccount(
                    bundleOf().apply {
                        putString(Tags.DATA_TYPE, Tags.TYPE_GROUP_RESET_PASSWORD)
                    },
                )
            }
        }
    }
}