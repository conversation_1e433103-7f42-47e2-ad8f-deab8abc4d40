package com.vietinbank.feature_login.ui.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_domain.models.login.AuthenticationMethod
import com.vietinbank.core_ui.base.compose.BaseAppBar
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.theme.AppColors

/**
 * Created by vandz on 17/3/25.
 */

@Preview
@Composable
fun Active2FAStep1(
    onContinueClick: () -> Unit = {},
    onMethodSelected: (AuthenticationMethod) -> Unit = {},
    selectedOption: MutableState<String> = remember { mutableStateOf("Email") },
) {
    val primaryColor = AppColors.gradientStart

// Định nghĩa các phương thức xác thực
    val authMethods = remember {
        listOf(
            AuthenticationMethod("Email", Tags.EMAIL_OTP),
            AuthenticationMethod("Số điện thoại", Tags.SMS_OTP),
        )
    }

    // Tìm phương thức hiện tại từ selectedOption
    val currentMethod = remember(selectedOption.value) {
        authMethods.find { it.name == selectedOption.value } ?: authMethods[0]
    }

    LaunchedEffect(Unit) {
        onMethodSelected(currentMethod)
    }

    Scaffold(
        topBar = {
            // AppBar
            BaseAppBar(
                title = "Kích hoạt",
                onBackClick = {},
            )
        },
        bottomBar = {
            Button(
                onClick = onContinueClick,
                modifier = Modifier
                    .fillMaxWidth()
                    .navigationBarsPadding()
                    .padding(horizontal = 24.dp, vertical = 16.dp)
                    .height(56.dp),
                shape = RoundedCornerShape(10.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color.Transparent,
                ),
                contentPadding = PaddingValues(0.dp),
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            brush = Brush.horizontalGradient(
                                colors = listOf(
                                    AppColors.gradientStart,
                                    AppColors.gradientEnd,
                                ),
                            ),
                            shape = RoundedCornerShape(10.dp),
                        ),
                    contentAlignment = Alignment.Center,
                ) {
                    BaseText(
                        text = "Tiếp tục",
                        color = Color.White,
                        textSize = 16.sp,
                        fontCus = 0,
                    )
                }
            }
        },
        containerColor = Color.Transparent,
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(
                    top = paddingValues.calculateTopPadding(),
                    bottom = paddingValues.calculateBottomPadding(),
                ),
        ) {
            BaseText(
                text = "Quý khách đang yêu cầu kích hoạt lại tài khoản VietinBank eFAST trên thiết bị mới",
                color = Color.White,
                textSize = 14.sp,
                fontCus = 0,
                lineHeight = 28.sp,
                modifier = Modifier.padding(horizontal = 24.dp, vertical = 20.dp),
            )

            // Options Card
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                shape = RoundedCornerShape(10.dp),
                colors = CardDefaults.cardColors(containerColor = Color.White),
            ) {
                Column(
                    modifier = Modifier.padding(24.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp),
                ) {
                    // Selection label
                    BaseText(
                        text = "Chọn phương thức xác thực",
                        color = Color.Gray,
                        textSize = 14.sp,
                        fontCus = 0,
                    )

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp),
                    ) {
                        // Option 1: Email
                        Row(
                            modifier = Modifier
                                .weight(1f)
                                .clip(RoundedCornerShape(8.dp))
                                .border(
                                    width = 1.dp,
                                    color = if (selectedOption.value == authMethods[0].name) primaryColor else Color.LightGray,
                                    shape = RoundedCornerShape(8.dp),
                                )
                                .background(
                                    if (selectedOption.value == authMethods[0].name) {
                                        Color(0xFFEEF4FF)
                                    } else {
                                        Color.White
                                    },
                                )
                                .clickable {
                                    selectedOption.value = authMethods[0].name
                                    onMethodSelected(authMethods[0]) // Gọi callback với phương thức đã chọn
                                }
                                .padding(horizontal = 12.dp, vertical = 12.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.Start,
                        ) {
                            RadioButton(
                                selected = selectedOption.value == authMethods[0].name,
                                onClick = {
                                    selectedOption.value = authMethods[0].name
                                    onMethodSelected(authMethods[0]) // Gọi callback với phương thức đã chọn
                                },
                                modifier = Modifier.size(20.dp),
                                // Giữ nguyên các thuộc tính khác
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            BaseText(
                                text = authMethods[0].name,
                                color = Color.Gray,
                                textSize = 14.sp,
                                fontCus = 0,
                            )
                        }

                        // Option 2: Phone
                        Row(
                            modifier = Modifier
                                .weight(1f)
                                .clip(RoundedCornerShape(8.dp))
                                .border(
                                    width = 1.dp,
                                    color = if (selectedOption.value == authMethods[1].name) primaryColor else Color.LightGray,
                                    shape = RoundedCornerShape(8.dp),
                                )
                                .background(
                                    if (selectedOption.value == authMethods[1].name) {
                                        Color(0xFFEEF4FF)
                                    } else {
                                        Color.White
                                    },
                                )
                                .clickable {
                                    selectedOption.value = authMethods[1].name
                                    onMethodSelected(authMethods[1]) // Gọi callback với phương thức đã chọn
                                }
                                .padding(horizontal = 12.dp, vertical = 12.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.Start,
                        ) {
                            RadioButton(
                                selected = selectedOption.value == authMethods[1].name,
                                onClick = {
                                    selectedOption.value = authMethods[1].name
                                    onMethodSelected(authMethods[1]) // Gọi callback với phương thức đã chọn
                                },
                                modifier = Modifier.size(20.dp),
                                // Giữ nguyên các thuộc tính khác
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            BaseText(
                                text = authMethods[1].name,
                                color = Color.Gray,
                                textSize = 14.sp,
                                fontCus = 0,
                            )
                        }
                    }
                }
            }
        }
    }
}