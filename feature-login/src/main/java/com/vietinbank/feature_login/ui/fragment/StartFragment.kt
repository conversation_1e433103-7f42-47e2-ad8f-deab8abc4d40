package com.vietinbank.feature_login.ui.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.viewbinding.ViewBinding
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_login.databinding.FragmentStartBinding
import com.vietinbank.feature_login.ui.viewmodel.LoginViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class StartFragment : BaseFragment<LoginViewModel>() {
    override val viewModel: LoginViewModel by viewModels()
    override val useCompose: Boolean = false

    override fun inflateViewBinding(inflater: LayoutInflater, container: ViewGroup?): ViewBinding? {
        return FragmentStartBinding.inflate(inflater, container, false)
    }

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var userProf: IUserProf

    /**
     * Override to prevent session timeout handling in start screen
     * This is the initial screen before any authentication
     */
    override fun shouldObserveSessionExpiry(): Boolean = false

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Get app active state from SavedStateHandle (set by MainActivity)
        // or directly check from userProf if not available
        val isAppActive = findNavController()
            .currentBackStackEntry
            ?.savedStateHandle
            ?.get<Boolean>("isAppActive") ?: userProf.isActive()

        // Điều hướng dựa trên trạng thái
        if (true) {
            // Use new UI flow - NewUILoginFragment handles both states
            appNavigator.goToLoginFromSplash()
        } else {
            // Use existing flow
            if (isAppActive) {
                appNavigator.goToHomePreLoginFromSplash()
            } else {
                appNavigator.goToLoginFromSplash()
            }
        }
    }
}