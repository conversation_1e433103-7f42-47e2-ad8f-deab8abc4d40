package com.vietinbank.feature_login.ui.fragment

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.vietinbank.core_common.constants.ListFunctionId
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.openUrl
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.models.UserProfData
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.recent.IRecentUsersStore
import com.vietinbank.core_common.recent.RecentUserEntry
import com.vietinbank.core_common.utils.BiometricHelper
import com.vietinbank.core_domain.models.login.CifSharedDomain
import com.vietinbank.core_domain.models.login.LoginDomain
import com.vietinbank.core_domain.softotp.ISoftManager
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_login.ui.dialog.BiometricVerifyPasswordDialog
import com.vietinbank.feature_login.ui.dialog.BiometricVerifyPasswordResult
import com.vietinbank.feature_login.ui.dialog.Company
import com.vietinbank.feature_login.ui.dialog.CompanySelectionDialog
import com.vietinbank.feature_login.ui.dialog.CompanySelectionResult
import com.vietinbank.feature_login.ui.dialog.SwitchUserDialog
import com.vietinbank.feature_login.ui.dialog.SwitchUserResult
import com.vietinbank.feature_login.ui.events.LoginEvent
import com.vietinbank.feature_login.ui.screen.newui.NewUIPreLoginAction
import com.vietinbank.feature_login.ui.screen.newui.NewUIPreLoginScreen
import com.vietinbank.feature_login.ui.viewmodel.LoginViewModel
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Pre-login fragment for NewUI
 * Handles both new users and returning users based on isActive state
 */
@AndroidEntryPoint
class NewUIPreLoginFragment : BaseFragment<LoginViewModel>() {

    // region Properties
    override val viewModel: LoginViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softManager: ISoftManager

    @Inject
    lateinit var recentUsersStore: IRecentUsersStore

    /**
     * Override to prevent session timeout handling in pre-login screen
     * This prevents the infinite loop when navigating back from login after timeout
     */
    override fun shouldObserveSessionExpiry(): Boolean = false
    // endregion

    // region Lifecycle
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        requestNotificationPermissionIfNeeded()
        setupCompanySelectionListener() // Setup Fragment Result listener for company selection
        setupSwitchUserResultListener() // Setup Fragment Result listener for switch user dialog
        setupEventCollector() // Collect Channel events
        setupAllObservers() // Keep for backward compatibility
        viewModel.updateLoginStatus()
        viewModel.forceUpdate()

        // Load Soft OTP status for checker role users
        if (viewModel.isChecker()) {
            softManager.loadSoftStatus()
        }
    }
    // endregion

    // region Compose UI
    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.uiState.collectAsState()

        AppTheme {
            NewUIPreLoginScreen(
                isActive = uiState.isActive,
                userName = uiState.savedUsername, // Use savedUsername for returning users
                fullName = uiState.fullName,
                onAction = { action -> handleScreenAction(action) },
            )
        }
    }
    // endregion

    // region User Actions
    private fun handleScreenAction(action: NewUIPreLoginAction) {
        when (action) {
            is NewUIPreLoginAction.OnLoginClick -> {
                // Navigate to login screen
                appNavigator.goToLogin()
            }
            is NewUIPreLoginAction.OnRegisterClick -> {
                showNoticeDialog(getString(R.string.prelogin_feature_under_construction))
            }
            is NewUIPreLoginAction.OnSyncTokenClick -> {
                activity?.openUrl(VSoftConstants.URL_SYNC)
            }
            is NewUIPreLoginAction.OnUnlockUserClick -> {
                appNavigator.goToLockAccount(
                    bundleOf().apply {
                        putString(Tags.DATA_TYPE, Tags.TYPE_GROUP_UNLOCK_USER)
                    },
                )
            }
            is NewUIPreLoginAction.OnBranchLocationClick -> {
                showNoticeDialog(getString(R.string.prelogin_feature_under_construction))
            }
            is NewUIPreLoginAction.OnGuideClick -> {
                showNoticeDialog(getString(R.string.prelogin_feature_under_construction))
            }
            is NewUIPreLoginAction.OnContactClick -> {
                showNoticeDialog(getString(R.string.prelogin_feature_under_construction))
            }
            is NewUIPreLoginAction.OnSwitchUserClick -> {
                showSwitchUserDialog()
            }
            is NewUIPreLoginAction.OnFingerprintClick -> {
                handleBiometricLogin()
            }
            is NewUIPreLoginAction.OnSearchClick -> {
                showNoticeDialog(getString(R.string.prelogin_search_under_construction))
            }
            is NewUIPreLoginAction.OnVoiceClick -> {
                showNoticeDialog(getString(R.string.prelogin_voice_search_under_construction))
            }
            is NewUIPreLoginAction.OnTransferClick -> {
                showNoticeDialog(getString(R.string.prelogin_login_required_for_transfer))
            }
            is NewUIPreLoginAction.OnInternationalTransferClick -> {
                showNoticeDialog(getString(R.string.prelogin_login_required_for_international))
            }
            is NewUIPreLoginAction.OnReissuePasswordClick -> {
                // Cấp lại mật khẩu for new users
                appNavigator.goToLockAccount(
                    bundleOf().apply {
                        putString(Tags.DATA_TYPE, Tags.TYPE_GROUP_RESET_PASSWORD)
                    },
                )
            }
            is NewUIPreLoginAction.OnRestorePasswordClick -> {
                // Khôi phục mật khẩu for active users
                appNavigator.goToLockAccount(
                    bundleOf().apply {
                        putString(Tags.DATA_TYPE, Tags.TYPE_GROUP_RESET_PASSWORD)
                    },
                )
            }
            is NewUIPreLoginAction.OnSoftOtpClick -> {
                // Handle Soft OTP quick access
                if (softManager.isAppActive) {
                    // Navigate to PIN entry for OTP retrieval
                    appNavigator.goToEnterPIN(VSoftConstants.VtpOTPFlowType.GET_OTP.name)
                } else {
                    // Show dialog that Soft OTP is not activated
                    showNoticeDialog(getString(R.string.prelogin_soft_otp_not_activated))
                }
            }

            // Header notification/language actions
            is NewUIPreLoginAction.OnLanguageClick -> {
                // TODO: Implement language/region selector
                showNoticeDialog(getString(R.string.prelogin_feature_under_construction))
            }

            is NewUIPreLoginAction.OnNotificationClick -> {
                // TODO: Navigate to notifications
                showNoticeDialog(getString(R.string.prelogin_feature_under_construction))
            }

            is NewUIPreLoginAction.OnOttNotificationClick -> {
                // TODO: Navigate to OTT notification settings
                showNoticeDialog(getString(R.string.prelogin_feature_under_construction))
            }
        }
    }

    /**
     * Setup listener for company selection dialog results
     * This is needed when login from biometric registration returns multi-CIF response
     */
    private fun setupCompanySelectionListener() {
        childFragmentManager.setFragmentResultListener(
            "company_selection_result",
            viewLifecycleOwner,
        ) { requestKey, bundle ->
            val isCancelled = bundle.getBoolean("key_cancelled", false)

            if (!isCancelled) {
                val result = bundle.getParcelable<CompanySelectionResult>("key_result")

                result?.let { selectionResult ->
                    // Get companies from ViewModel using proper encapsulation
                    val companies = viewModel.getCompaniesForSelection()

                    // Find the selected CifSharedDomain from result
                    val selectedCif = companies?.find { it.enterpriseid == selectionResult.companyId }

                    selectedCif?.let {
                        viewModel.selectCompany(it)
                        // Dialog auto-dismisses on result

                        // Now call login with the selected company
                        val username = viewModel.getUserName().toString()

                        // Check if we're coming from biometric password dialog or fingerprint login
                        val password = viewModel.getLastLoginPassword()

                        when {
                            !password.isNullOrEmpty() -> {
                                // Coming from biometric password dialog - need to register TouchID
                                viewModel.login(
                                    username = username,
                                    password = password,
                                    regTouchId = "1", // Register TouchID flag
                                )
                            }
                            viewModel.isUsingFingerprint() -> {
                                // Coming from fingerprint login - no password, use fingerprint auth
                                viewModel.login(
                                    username = username,
                                    password = "",
                                    isActiveFinger = true, // Use fingerprint authentication
                                )
                            }
                            else -> {
                                // Shouldn't happen, but handle gracefully
                                // Clear state and don't retry
                                viewModel.clearMultiCifState()
                                viewModel.clearBiometricPassword()
                            }
                        }
                    }
                }
            } else {
                // User dismissed dialog without selecting
                // Clear multi-cif to allow re-triggering
                viewModel.clearMultiCifState()
            }
        }
    }

    /**
     * Show Company Selection Dialog using the new BaseDialog architecture
     * Maps CifSharedDomain to Company and handles result
     */
    private fun showCompanySelectionDialog(
        companies: List<CifSharedDomain>,
        selectedId: String?,
    ) {
        // Map CifSharedDomain to Company for dialog
        val companyList = companies.map { cif ->
            Company(
                id = cif.enterpriseid ?: "",
                name = cif.enterprisename ?: "",
                taxCode = cif.enterpriseid ?: "", // Use enterpriseid as tax code display
            )
        }

        // Show the dialog
        CompanySelectionDialog.show(
            fragmentManager = childFragmentManager,
            companies = companyList,
            selectedId = selectedId,
        )
    }

    /**
     * Show Switch User Dialog
     * Shows recent users and allows switching between accounts
     */
    private fun showSwitchUserDialog() {
        val recentUsers = recentUsersStore.getRecentUsers().toMutableList()
        val currentUsername = viewModel.getUserName()
        val currentFullName = viewModel.getFullName()

        // If user is logged in, add current user to the top of the list
        if (viewModel.isActive() && !currentUsername.isNullOrEmpty()) {
            // Check if current user already exists in recent users
            val existingCurrentUser = recentUsers.find { it.username == currentUsername }

            if (existingCurrentUser == null) {
                // Create current user entry and add to top of list
                val currentUser = RecentUserEntry(
                    username = currentUsername,
                    fullName = currentFullName,
                    corpName = null, // TODO: Get from session if available
                    roleId = null,
                    biometricToken = null, // Will be checked from biometric store
                )
                recentUsers.add(0, currentUser)
            } else {
                // Move existing user to top if not already there
                recentUsers.remove(existingCurrentUser)
                recentUsers.add(0, existingCurrentUser)
            }
        }

        SwitchUserDialog.show(
            fragmentManager = childFragmentManager,
            recentUsers = recentUsers,
            currentUsername = currentUsername,
            onRemoveUser = { username ->
                // Remove user from recent list
                recentUsersStore.removeUser(username)
            },
        )
    }

    /**
     * Setup listener for switch user dialog results
     */
    private fun setupSwitchUserResultListener() {
        childFragmentManager.setFragmentResultListener(
            "switch_user_result",
            viewLifecycleOwner,
        ) { _, bundle ->

            val isCancelled = bundle.getBoolean("key_cancelled", false)

            if (!isCancelled) {
                val result = bundle.getParcelable<SwitchUserResult>("key_result")

                result?.let { switchResult ->
                    when {
                        switchResult.selectedUsername.isEmpty() -> {
                            // Add new account - clear data and go to login
                            clearAllData()
                            viewModel.updateActiveStatus(false)
                            appNavigator.goToLogin()
                        }
                        switchResult.hasBiometric -> {
                            // Switch to user with biometric
                            viewModel.updateUsername(switchResult.selectedUsername)
                            handleBiometricLogin()
                        }
                        else -> {
                            // Switch to user without biometric - go to login with pre-filled username
                            viewModel.updateUsername(switchResult.selectedUsername)
                            appNavigator.goToLogin()
                        }
                    }
                }
            }
        }
    }
    // endregion

    // region Event Collector (NEW - Channel-based one-time events)
    private fun setupEventCollector() {
        // Collect Channel events for backward compatibility
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.events.collect { event ->
                    handleLoginEvent(event)
                }
            }
        }
    }

    private fun handleLoginEvent(event: LoginEvent) {
        when (event) {
            is LoginEvent.NavigateTo2FA -> {
                val bundle = Bundle().apply {
                    putString(Tags.ADDFIELD1_BUNDLE, event.addField1)
                    putString(Tags.USERNAME_BUNDLE, event.username)
                    putString(Tags.CIFNO_BUNDLE, event.cifNo)
                }
                appNavigator.goToActive2FAStep1Fragment(bundle)
            }
            is LoginEvent.NavigateToActive -> {
                viewModel.saveSession(sessionId = event.sessionId)
                appNavigator.goToActive()
            }
            is LoginEvent.NavigateToHome -> {
                navigateToHome()
            }
            is LoginEvent.LoginSuccessWithSetup -> {
                // DEPRECATED - Using LoginResult instead to avoid duplicate processing
                // This event is kept for backward compatibility but should not be used
            }
            is LoginEvent.NavigateToLockAccount -> {
                appNavigator.goToLockAccount(
                    bundleOf().apply {
                        putString(Tags.DATA_TYPE, event.dataType)
                    },
                )
            }
            is LoginEvent.ShowErrorDialog -> {
                showNoticeDialog(event.message)
            }
            is LoginEvent.ShowNoticeDialog -> {
                showNoticeDialog(
                    message = event.message,
                    positiveAction = event.positiveAction ?: {},
                    positiveButtonText = event.positiveButtonText ?: getString(R.string.dialog_button_confirm),
                )
            }
            is LoginEvent.ShowConfirmDialog -> {
                showConfirmDialog(
                    message = event.message,
                    positiveButtonText = event.positiveButtonText,
                    negativeButtonText = event.negativeButtonText,
                    positiveAction = event.positiveAction,
                    negativeAction = event.negativeAction ?: {},
                    dismissAction = {},
                )
            }
            is LoginEvent.ShowPasswordExpiryWarning -> {
                handlePasswordExpiryWarning(event.data)
            }
            is LoginEvent.ShowPasswordExpiryNew -> {
                handlePasswordExpiryNew(event.data)
            }
            is LoginEvent.ShowPasswordExpiredDialog -> {
                // Handle generic password expired - navigate to Active to change password
                handlePasswordExpired(event.data)
            }
            is LoginEvent.ShowPasswordExpired881Dialog -> {
                // Handle password expired with code 881
                handlePasswordExpired881(event.data)
            }
            is LoginEvent.ShowFirstLoginDialog -> {
                handleFirstLoginResponse(event.data)
            }
            is LoginEvent.ShowForgotPasswordDialog -> {
                showNoticeDialog(
                    message = event.message,
                    positiveAction = {},
                    positiveButtonText = getString(R.string.login_forgot_password_button),
                )
            }
            is LoginEvent.ShowBiometricNotAvailable -> {
                // Backward compatibility - generic message
                showNoticeDialog(event.message)
            }
            is LoginEvent.ShowBiometricOsNotSupported -> {
                // OS does not support biometric (Android < 6.0)
                showNoticeDialog(event.message)
            }
            is LoginEvent.ShowBiometricDeviceNotEnrolled -> {
                // Device has no fingerprint/PIN/pattern enrolled
                // Show dialog with option to go to Settings
                showConfirmDialog(
                    message = event.message,
                    positiveButtonText = getString(R.string.button_settings),
                    negativeButtonText = getString(R.string.dialog_button_close),
                    positiveAction = {
                        // Open Security Settings
                        try {
                            val intent = Intent(android.provider.Settings.ACTION_SECURITY_SETTINGS)
                            startActivity(intent)
                        } catch (e: Exception) {
                            showNoticeDialog(getString(R.string.error_cannot_open_settings))
                        }
                    },
                    negativeAction = {},
                )
            }
            is LoginEvent.ShowVerifyPasswordForBiometric -> {
                showVerifyPasswordDialog()
            }
            is LoginEvent.ShowBiometricPrompt -> {
                showFingerPrintPopup()
            }
            is LoginEvent.ShowForceUpdateDialog -> {
                showNoticeDialog(event.message)
            }
            is LoginEvent.ShowUpdatableDialog -> {
                showNoticeDialog(event.message)
            }
            is LoginEvent.HandleForceUpdate -> {
                viewModel.handleForceUpdateOrNavigate(event.functionId) {}
            }
            is LoginEvent.ShowCompanySelectionDialog -> {
                // Show company selection dialog when multi-CIF response is received
                // This can happen when login is triggered from biometric registration
                showCompanySelectionDialog(
                    companies = event.companies,
                    selectedId = event.selectedId,
                )
            }
            is LoginEvent.LoginResult -> {
                // Handle login result using existing logic
                handleLoginSuccess(event.data)
            }
            is LoginEvent.ChangePasswordResult -> {
                // Handle change password result
                showNoticeDialog(
                    message = event.data.status.message ?: "Đổi mật khẩu thành công",
                    positiveAction = { appNavigator.popBackStack() },
                )
            }
            is LoginEvent.ForceUpdateResult -> {
                // Handle force update result
                viewModel.handleForceUpdateOrNavigate(ListFunctionId.FORCE_UPDATE_ALL) {}
            }
        }
    }
    // endregion

    // region Observers Setup
    private fun setupAllObservers() {
        setupLoginObservers()
        setupErrorObservers()
        setupUpdateObservers()
    }

    private fun setupLoginObservers() {
        // Migrated to Channel events - see LoginEvent.LoginResult
        // Now handled in handleLoginEvent()
        /*
        // Login success/error handling
        viewModel.loginState.observe(viewLifecycleOwner) { resource ->
            when (resource) {
                is Resource.Success -> {
                    handleLoginSuccess(resource.data)
                    viewModel.updateLoginStatus()
                }
                is Resource.Error -> {
                    // Error handled in BaseFragment
                }
            }
        }
         */
    }

    private fun setupErrorObservers() {
        // Multi-CIF handling
        viewModel.multiCifResponse.observe(viewLifecycleOwner) { response ->
            // Multi-CIF logic handled in login screen - this is UI state, not event
        }
    }

    private fun setupUpdateObservers() {
        // Migrated to Channel events - see LoginEvent.ForceUpdateResult
        // Now handled in handleLoginEvent()
        /*
        // Force update handling
        viewModel.forceUpdateResult.observe(viewLifecycleOwner) { result ->
            when (result) {
                is Resource.Success -> {
                    viewModel.handleForceUpdateOrNavigate(ListFunctionId.FORCE_UPDATE_ALL) {}
                }
                is Resource.Error -> {
                    // Error handled by BaseViewModel
                }
            }
        }
         */

        // Update dialogs now handled via Channel events
    }
    // endregion

    // region Helper Methods
    private fun handleFirstLoginResponse(data: LoginDomain) {
        printLog("data.status?.code: ${data.status?.code}")
        if (!TextUtils.isEmpty(data.corpUser?.status) && Tags.FIRST_LOGIN_SIGNAL == data.corpUser?.status) {
            if (viewModel.isActive()) {
                clearAllData()
            }
            showConfirmDialog(
                message = data.status?.message.toString(),
                positiveButtonText = getString(R.string.login_change_password_button),
                negativeButtonText = getString(R.string.login_close_button),
                positiveAction = {
                    viewModel.saveSession(sessionId = data.sessionId.toString())
                    saveProfileAfterLoginSuccess(data)
                    viewModel.updateLoginStatus()
                    appNavigator.goToActive()
                },
                negativeAction = {},
                dismissAction = {},
            )
        } else {
            showNoticeDialog(data.status?.message.toString())
        }
    }

    private fun handlePasswordExpiryWarning(data: LoginDomain) {
        if (viewModel.isActive()) {
            clearAllData()
        }
        showConfirmDialog(
            message = data.status?.message.toString(),
            positiveButtonText = getString(com.vietinbank.feature_login.R.string.change_password_title),
            negativeButtonText = getString(com.vietinbank.feature_login.R.string.company_dialog_cancel_close),
            positiveAction = {
                viewModel.saveSession(sessionId = data.sessionId.toString())
                viewModel.saveProfile(
                    UserProfData(
                        userName = viewModel.userName.toString(),
                    ),
                )
                appNavigator.goToActive()
            },
            negativeAction = {},
            dismissAction = {},
        )
    }

    private fun handlePasswordExpiryNew(data: LoginDomain) {
        if (viewModel.isActive()) {
            clearAllData()
        }
        showConfirmDialog(
            message = data.status?.message.toString(),
            positiveButtonText = getString(com.vietinbank.feature_login.R.string.change_password_title),
            negativeButtonText = getString(R.string.company_dialog_cancel),
            positiveAction = {
                viewModel.saveSession(sessionId = data.sessionId.toString())
                viewModel.saveProfile(
                    UserProfData(
                        userName = viewModel.userName.toString(),
                    ),
                )
                appNavigator.goToActive()
            },
            negativeAction = {},
            dismissAction = {},
        )
    }

    private fun handlePasswordExpired(data: LoginDomain) {
        // Generic password expired - navigate to Active screen to change password
        showNoticeDialog(
            message = data.status?.message ?: getString(R.string.login_password_expired),
            positiveAction = {
                viewModel.saveSession(sessionId = data.sessionId.toString())
                saveProfileAfterLoginSuccess(data)
                viewModel.updateLoginStatus()
                appNavigator.goToActive()
            },
        )
    }

    private fun handlePasswordExpired881(data: LoginDomain) {
        // Password expired with code 881 - specific handling
        showNoticeDialog(
            message = data.status?.message ?: getString(R.string.login_password_expired_881),
            positiveAction = {
                viewModel.saveSession(sessionId = data.sessionId.toString())
                saveProfileAfterLoginSuccess(data)
                viewModel.updateLoginStatus()
                appNavigator.goToActive()
            },
        )
    }
    // endregion

    // region Biometric Handling
    private fun handleBiometricLogin() {
        if (viewModel.isActive()) {
            if (viewModel.enableTouchID()) {
                // Call checkBiometricForLogin which will send appropriate event
                // The event handler will then call showFingerPrintPopup() if ready
                viewModel.checkBiometricForLogin()
            } else {
                showVerifyPasswordDialog()
            }
        } else {
            showNoticeDialog(getString(R.string.login_biometric_activation_message))
        }
    }

    /**
     * Shows the new BiometricVerifyPasswordDialog
     * Replaces the old XML-based VerifyPasswordDialog
     */
    private fun showVerifyPasswordDialog() {
        // Create and show dialog using BaseDialog pattern
        val dialog = BiometricVerifyPasswordDialog.newInstance(
            title = getString(R.string.login_verify_password_title),
            message = getString(R.string.login_verify_password_message),
        )
        dialog.show(childFragmentManager, "BiometricVerifyPasswordDialog")

        // Listen for dialog result using Fragment Result API
        childFragmentManager.setFragmentResultListener(
            "biometric_verify_password_result",
            viewLifecycleOwner,
        ) { _, bundle ->
            val isCancelled = bundle.getBoolean("key_cancelled", false)
            if (!isCancelled) {
                val result = bundle.getParcelable<BiometricVerifyPasswordResult>("key_result")
                result?.let {
                    // Store password in ViewModel for potential multi-CIF retry
                    // This is needed in case server returns multi-CIF response
                    viewModel.storeBiometricPassword(it.password)

                    // Perform login with TouchID registration
                    viewModel.login(
                        username = viewModel.getUserName().toString(),
                        password = it.password,
                        regTouchId = "1", // Register TouchID flag - critical for biometric registration
                    )
                }
            }
        }
    }

    private fun showFingerPrintPopup() {
        // Don't call checkBiometricForLogin() here - it creates infinite loop!
        // This function is called FROM the event handler after check is done
        BiometricHelper.showBiometricOrDeviceCredentialPrompt(
            fragment = this,
            title = getString(R.string.login_biometric_title),
            subtitle = getString(R.string.login_biometric_subtitle),
            onSuccess = {
                printLog("verify success")
                viewModel.login(
                    username = viewModel.getUserName().toString(),
                    password = "",
                    isActiveFinger = true,
                )
            },
            onError = { errorMsg ->
                showNoticeDialog(errorMsg)
            },
        )
    }
    // endregion

    // region Permission Handling
    private fun requestNotificationPermissionIfNeeded() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(
                    requireContext(),
                    Manifest.permission.POST_NOTIFICATIONS,
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
                    if (isGranted) {
                        printLog(getString(R.string.login_notification_permission_granted))
                    } else {
                        printLog(getString(R.string.login_notification_permission_denied))
                    }
                }.launch(Manifest.permission.POST_NOTIFICATIONS)
            }
        }
    }
    // endregion

    private fun clearAllData() {
        viewModel.clearSession()
        viewModel.clearProfile()
        viewModel.clearAllConfig()
        viewModel.transferCacheManager.clearTransferALL()
        viewModel.ottRegistrationRepository.clearAllOttCache()
    }

    private fun saveProfileAfterLoginSuccess(data: LoginDomain) {
        viewModel.saveProfile(
            UserProfData(
                userName = data.corpUser?.username.toString(),
                phone = data.corpUser?.phoneNo.toString(),
                fullName = data.corpUser?.fullname.toString(),
                cifNo = data.cifNo.toString(),
                roleId = data.corpUser?.roleId,
                keypassprofile = data.corpUser?.keypassprofile ?: "",
                keypasssoftotp = data.corpUser?.keypasssoftotp ?: "",
                email = data.corpUser!!.email,
                corpName = data.corpUser?.corpName ?: "",
                // keypass
                keypassCode = data.corpUser?.keypassid,
                keypassToken = data.corpUser?.keypassid,
                // next approver
                roleLevel = data.corpUser?.roleLevel,
                groupType = data.corpUser?.groupType,
                sessionID = data.sessionId,
                idNumber = data.corpUser?.idNumber,
                userId = data.userId,
                addField4 = data.addField4,
            ),
        )

        viewModel.setTimerConfig(true)
    }

    private fun handleLoginSuccess(data: LoginDomain) {
        // case dang nhap lan sau (normal case)
        // saveDataLogin
        // dieu huong sang man hinh Home
        viewModel.saveSession(
            sessionId = data.sessionId.toString(),
        )
        printLog("Save session success: ${data.sessionId}")
        saveProfileAfterLoginSuccess(data)

        if (!TextUtils.isEmpty(data.addField3)) {
            printLog("AddField3: ${data.addField3}")
            viewModel.updateFingerID(data.addField3!!)
        }
        navigateToHome()
        viewModel.setEnableCheckEkyc()
    }

    private fun navigateToHome() {
        if (viewModel.isNonFinancial()) {
            appNavigator.goToAccountFromLogin(true)
        } else {
            appNavigator.goToHome()
        }
    }
}