package com.vietinbank.feature_login.ui.fragment.account_lock

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.login.LockResultActions
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_login.ui.screen.account_lock.LockResultScreen
import com.vietinbank.feature_login.ui.viewmodel.AccountLockViewModel
import com.vietinbank.feature_login.ui.viewmodel.LockUIState
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import kotlin.getValue

@AndroidEntryPoint
class AccountLockResultFragment : BaseFragment<AccountLockViewModel>() {
    override val viewModel: AccountLockViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        getExtrasData()
        initObserver()
    }

    private fun getExtrasData() {
        try {
            viewModel.setLockUIState(
                Utils.g().provideGson().fromJson(
                    arguments?.getString(Tags.TRANSACTION_BUNDLE),
                    LockUIState.ResultState::class.java,
                ),
            )
        } catch (_: Exception) {
        }
    }

    private fun initObserver() {
    }

    @Composable
    override fun ComposeScreen() {
        val actions = LockResultActions(
            onLoginClick = {
                appNavigator.popToLogin(true)
            },
            onDownloadClick = {
                showNoticeDialog(it)
            },
        )

        AppTheme {
            LockResultScreen(viewModel, actions)
        }
    }
}