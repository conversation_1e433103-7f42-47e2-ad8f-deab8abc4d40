package com.vietinbank.feature_login.ui.sheet

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.extensions.getInitials
import com.vietinbank.core_common.recent.RecentUserEntry
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.glassBottomGradient
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Constants for DeleteUserConfirmationBottomSheet styling
 */
private object DeleteUserConfirmationConstants {
    const val WARNING_ICON_SIZE_DP = 96
    const val AVATAR_SIZE_DP = 48
    const val GRADIENT_BOTTOM_ALPHA = 0.675f
}

/**
 * Actions for delete user confirmation bottom sheet
 */
sealed interface DeleteUserAction {
    object OnDismiss : DeleteUserAction
    object OnConfirmDelete : DeleteUserAction
}

/**
 * Delete User Confirmation Bottom Sheet
 * Shows confirmation dialog before deleting selected users
 *
 * @param visible Whether the bottom sheet is visible
 * @param selectedUsers List of users to be deleted
 * @param onAction Callback for user actions
 */
@Composable
fun DeleteUserConfirmationBottomSheet(
    visible: Boolean,
    selectedUsers: List<RecentUserEntry>,
    onAction: (DeleteUserAction) -> Unit,
) {
    BaseBottomSheet<DeleteUserAction>(
        visible = visible,
        onDismissRequest = {
            onAction(DeleteUserAction.OnDismiss)
        },
        onResult = { action ->
            onAction(action)
        },
        allowTouchDismiss = false,
        secureFlag = true,
    ) { onSheetAction ->
        DeleteUserConfirmationContent(
            selectedUsers = selectedUsers,
            onAction = onSheetAction,
        )
    }
}

/**
 * Content for delete user confirmation bottom sheet
 */
@Composable
private fun DeleteUserConfirmationContent(
    selectedUsers: List<RecentUserEntry>,
    onAction: (DeleteUserAction) -> Unit,
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .systemBarsPadding(),
    ) {
        // White container with rounded corners on all 4 sides
        Surface(
            shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
            color = FDS.Colors.white,
            modifier = Modifier.fillMaxWidth(),
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FDS.Sizer.Padding.padding24),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                // Header: "Thông báo"
                FoundationText(
                    text = stringResource(R.string.delete_user_confirmation_title),
                    style = FDS.Typography.headingH3,
                    color = FDS.Colors.characterHighlighted,
                    textAlign = TextAlign.Center,
                )

                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

                // Divider
                FoundationDivider()

                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

                // Warning icon (96dp with warning triangle)
                WarningIcon()

                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

                // Confirmation message
                FoundationText(
                    text = stringResource(R.string.delete_user_confirmation_message),
                    style = FDS.Typography.bodyB2Emphasized,
                    color = FDS.Colors.characterSecondary,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
                )

                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

                // User list to be deleted
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = FDS.Sizer.Padding.padding24),
                    verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
                ) {
                    selectedUsers.forEach { user ->
                        UserItemToDelete(user = user)
                    }
                }

                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
            }
        }

        // Space between container and buttons
        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))

        // Bottom buttons with glass gradient background
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .glassBottomGradient(
                    endColor = FDS.Colors.blue900,
                    alpha = DeleteUserConfirmationConstants.GRADIENT_BOTTOM_ALPHA,
                )
                .padding(
                    top = FDS.Sizer.Padding.padding12,
                    start = FDS.Sizer.Padding.padding16,
                    end = FDS.Sizer.Padding.padding16,
                ),
        ) {
            // Button group: Back + Confirm
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
            ) {
                // Quay lại (Back) - dark button
                FoundationButton(
                    isLightButton = false,
                    text = stringResource(R.string.common_back),
                    onClick = { onAction(DeleteUserAction.OnDismiss) },
                    modifier = Modifier.weight(1f),
                )

                // Xác nhận (Confirm) - light button
                FoundationButton(
                    isLightButton = true,
                    text = stringResource(R.string.common_confirm),
                    onClick = { onAction(DeleteUserAction.OnConfirmDelete) },
                    modifier = Modifier.weight(1f),
                )
            }
        }
    }
}

/**
 * Warning icon component (96dp with blue triangle)
 */
@Composable
private fun WarningIcon() {
    Box(
        modifier = Modifier
            .size(DeleteUserConfirmationConstants.WARNING_ICON_SIZE_DP.dp)
            .clip(CircleShape),
        contentAlignment = Alignment.Center,
    ) {
        // Warning triangle icon (72dp in 96dp container)
        Image(
            painter = painterResource(id = R.drawable.ic_common_warning_72),
            contentDescription = null,
            modifier = Modifier.size(FDS.Sizer.Icon.icon72),
        )
    }
}

/**
 * Individual user item in the deletion list
 */
@Composable
private fun UserItemToDelete(
    user: RecentUserEntry,
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = FDS.Sizer.Padding.padding8),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // Avatar with initials
        Box(
            modifier = Modifier
                .size(DeleteUserConfirmationConstants.AVATAR_SIZE_DP.dp)
                .clip(CircleShape)
                .background(FDS.Colors.blue100),
            contentAlignment = Alignment.Center,
        ) {
            val initials = user.fullName?.getInitials()
                ?: user.username.take(2).uppercase()

            FoundationText(
                text = initials,
                style = FDS.Typography.bodyB2.copy(fontWeight = FontWeight.SemiBold),
                color = FDS.Colors.characterHighlighted,
            )
        }

        Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap8))

        // User info
        Column(
            modifier = Modifier.weight(1f),
        ) {
            // Full name or username
            FoundationText(
                text = user.fullName ?: user.username,
                style = FDS.Typography.bodyB2.copy(fontWeight = FontWeight.Bold),
                color = FDS.Colors.characterHighlighted,
            )

            // Company name (if available)
            user.corpName?.let { corpName ->
                FoundationText(
                    text = corpName,
                    style = FDS.Typography.captionM,
                    color = FDS.Colors.characterSecondary,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )
            }
        }
    }
}