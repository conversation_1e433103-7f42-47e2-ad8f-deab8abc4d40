package com.vietinbank.feature_login.ui.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusManager
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_ui.components.ButtonSize
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.foundation.textfield.FoundationEditText
import com.vietinbank.core_ui.components.foundation.textfield.InputType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_login.R
import com.vietinbank.feature_login.ui.state.ChangePassUiState
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun NewUIActiveScreen(
    uiState: ChangePassUiState,
    onAction: (NewUIChangePassAction) -> Unit,
) {
    val focusManager = LocalFocusManager.current
    val scrollState = rememberScrollState()

    val passwordOld = uiState.passwordOld
    val passwordNew = uiState.passwordNew
    val passwordAgain = uiState.passwordAgain
    val passwordErrorOld = uiState.errorOld
    val passwordErrorNew = uiState.errorNew
    val passwordErrorAgain = uiState.errorAgain

    // Focus requesters for input fields
    val passwordFocusRequester = remember { FocusRequester() }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding()
            .imePadding(), // IME padding at root level for proper keyboard handling
    ) {
        Column(
            modifier = Modifier.fillMaxSize(),
        ) {
            // Scrollable content area - takes remaining space with weight(1f)
            Box(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth()
                    .padding(horizontal = FDS.Sizer.Padding.padding8), // Main horizontal padding here
                contentAlignment = Alignment.TopCenter,
            ) {
                Column(
                    modifier = Modifier
                        .widthIn(max = FDS.Sizer.ScreenBreakpoint.tabletMinWidth) // Max width for tablets
                        .verticalScroll(scrollState),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    // FoundationAppBar with title
                    FoundationAppBar(
                        isLightIcon = false,
                        title = stringResource(id = com.vietinbank.feature_login.R.string.change_password_title),
                        onNavigationClick = { onAction(NewUIChangePassAction.OnBackClick) },
                        modifier = Modifier.padding(top = FDS.Sizer.Padding.padding16),
                    )

                    Spacer(modifier = Modifier.height(FDS.Sizer.Padding.padding16))

                    NewActiveScreenCard(
                        passwordOld = passwordOld,
                        passwordNew = passwordNew,
                        passwordAgain = passwordAgain,
                        passwordErrorOld = passwordErrorOld,
                        passwordErrorNew = passwordErrorNew,
                        passwordErrorAgain = passwordErrorAgain,
                        onAction = onAction,
                        focusManager = focusManager,
                        passwordFocusRequester = passwordFocusRequester,
                    )

                    // Forgot password section - OUTSIDE the card
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(
                                vertical = FDS.Sizer.Gap.gap8,
                                horizontal = FDS.Sizer.Gap.gap16,
                            ),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16), // min 8dp
                    ) {
                        FoundationText(
                            text = stringResource(com.vietinbank.feature_login.R.string.login_forgot_password_prompt),
                            style = FDS.Typography.captionCaptionL,
                            color = FDS.Colors.white,
                            modifier = Modifier.weight(1f),
                        )

                        FoundationButton(
                            text = stringResource(com.vietinbank.feature_login.R.string.login_restore_password),
                            size = ButtonSize.Small,
                            isLightButton = false,
                            onClick = { onAction(NewUIChangePassAction.OnForgotPasswordClick) },
                        )
                    }

                    // Add bottom padding for scroll content
                    Spacer(modifier = Modifier.height(FDS.Sizer.Padding.padding16))
                }
            }

            // Login button anchored at bottom
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = FDS.Sizer.Padding.padding24)
                    .padding(bottom = FDS.Sizer.Gap.gap8), // 36dp bottom spacing as per design
                contentAlignment = Alignment.Center,
            ) {
                // Center button on tablets
                FoundationButton(
                    isLightButton = uiState.isSubmitEnabled,
                    text = stringResource(id = com.vietinbank.feature_login.R.string.change_password_title),
                    onClick = { onAction(NewUIChangePassAction.OnChangePasswordClick) },
                    modifier = Modifier
                        .widthIn(max = FDS.Sizer.ScreenBreakpoint.tabletMinWidth)
                        .fillMaxWidth(),
                    size = ButtonSize.Large,
                    enabled = uiState.isSubmitEnabled,
                )
            }
        }
    }
}

@Composable
private fun NewActiveScreenCard(
    passwordOld: String,
    passwordNew: String,
    passwordAgain: String,
    passwordErrorOld: String? = null,
    passwordErrorNew: String? = null,
    passwordErrorAgain: String? = null,
    onAction: (NewUIChangePassAction) -> Unit,
    focusManager: FocusManager,
    passwordFocusRequester: FocusRequester,
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
        colors = CardDefaults.cardColors(
            containerColor = FDS.Colors.white,
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = FDS.Effects.elevationSm,
        ),
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FDS.Sizer.Padding.padding16),
        ) {
            FoundationEditText(
                value = passwordOld,
                onValueChange = { onAction(NewUIChangePassAction.OnPasswordChangeOld(it)) },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(FDS.Sizer.Padding.padding16)
                    .focusRequester(passwordFocusRequester),
                placeholder = stringResource(id = com.vietinbank.feature_login.R.string.change_password_old),
                inputType = InputType.PASSWORD,
                isError = passwordErrorOld != null,
                errorMessage = passwordErrorOld,
                showCharacterCounter = false,
                keyboardActions = KeyboardActions(
                    onDone = {
                        focusManager.clearFocus()
                    },
                ),
            )

            // Divider between fields - FULL WIDTH without padding
            HorizontalDivider(
                modifier = Modifier.fillMaxWidth(), // No horizontal padding - full width
                color = FDS.Colors.strokeDivider,
                thickness = FDS.Sizer.Stroke.stroke1,
            )

            FoundationEditText(
                value = passwordNew,
                onValueChange = { onAction(NewUIChangePassAction.OnPasswordChangeNew(it)) },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(FDS.Sizer.Padding.padding16)
                    .focusRequester(passwordFocusRequester),
                placeholder = stringResource(id = com.vietinbank.feature_login.R.string.change_password_new),
                inputType = InputType.PASSWORD,
                isError = passwordErrorNew != null,
                errorMessage = passwordErrorNew,
                showCharacterCounter = false,
                keyboardActions = KeyboardActions(
                    onDone = {
                        focusManager.clearFocus()
                    },
                ),
            )

            // Divider between fields - FULL WIDTH without padding
            HorizontalDivider(
                modifier = Modifier.fillMaxWidth(), // No horizontal padding - full width
                color = FDS.Colors.strokeDivider,
                thickness = FDS.Sizer.Stroke.stroke1,
            )

            FoundationEditText(
                value = passwordAgain,
                onValueChange = { onAction(NewUIChangePassAction.OnPasswordChangeAgain(it)) },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(FDS.Sizer.Padding.padding16)
                    .focusRequester(passwordFocusRequester),
                placeholder = stringResource(id = com.vietinbank.feature_login.R.string.change_password_again_new),
                inputType = InputType.PASSWORD,
                isError = passwordErrorAgain != null,
                errorMessage = passwordErrorAgain,
                showCharacterCounter = false,
                keyboardActions = KeyboardActions(
                    onDone = {
                        focusManager.clearFocus()
                    },
                ),
            )

            // Liệt kê các điều kiện
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        start = FDS.Sizer.Gap.gap24,
                        top = FDS.Sizer.Gap.gap12,
                        end = FDS.Sizer.Gap.gap12,
                        bottom = FDS.Sizer.Gap.gap24,
                    ),
                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap12),
            ) {
                PasswordConditionItem(stringResource(R.string.change_password_condition))
                PasswordConditionItem(stringResource(R.string.change_password_condition_old))
                PasswordConditionItem(stringResource(R.string.change_password_condition_new))
                PasswordConditionItem(stringResource(R.string.change_password_condition_again))
            }
        }
    }
}

// Component hiển thị điều kiện mật khẩu với dấu chấm tròn
@Composable
private fun PasswordConditionItem(condition: String) {
    Row(
        verticalAlignment = Alignment.Top,
        modifier = Modifier.fillMaxWidth(),
    ) {
        // Dấu chấm tròn
        Box(
            modifier = Modifier
                .padding(top = FDS.Sizer.Gap.gap6)
                .size(FDS.Sizer.Icon.icon4)
                .background(FDS.Colors.characterSecondary, shape = RoundedCornerShape(FDS.Sizer.Radius.radius4)),
        )
        Spacer(modifier = Modifier.padding(horizontal = FDS.Sizer.Gap.gap8))

        FoundationText(
            text = condition,
            style = FDS.Typography.captionCaptionL,
            color = FDS.Colors.characterSecondary,
        )
    }
}

/**
 * Actions for screen
 */
sealed class NewUIChangePassAction {
    object OnBackClick : NewUIChangePassAction()
    data class OnPasswordChangeOld(val passwordOld: String) : NewUIChangePassAction()
    data class OnPasswordChangeNew(val passwordNew: String) : NewUIChangePassAction()
    data class OnPasswordChangeAgain(val passwordAgain: String) : NewUIChangePassAction()
    object OnChangePasswordClick : NewUIChangePassAction()
    object OnForgotPasswordClick : NewUIChangePassAction()
}