package com.vietinbank.feature_login.ui.screen

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_domain.models.login.CifUiState
import com.vietinbank.core_domain.models.login.LoginActions
import com.vietinbank.core_domain.models.login.UserUiState
import com.vietinbank.core_ui.base.compose.ArrowPosition
import com.vietinbank.core_ui.base.compose.BaseEditText
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.base.compose.BaseTooltip
import com.vietinbank.core_ui.components.ButtonSize
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.core_ui.utils.InputFilters
import com.vietinbank.core_ui.utils.dismissKeyboardOnClickOutside
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_login.R
import com.vietinbank.feature_login.ui.components.BottomRowHome
import com.vietinbank.feature_login.ui.components.CompanyDropdown
import com.vietinbank.feature_login.ui.components.HomeActionBox
import com.vietinbank.feature_login.ui.components.TopBarRow
import com.vietinbank.feature_login.ui.components.UserInfoCard
import com.vietinbank.feature_login.ui.viewmodel.LoginViewModel

@Composable
fun LoginScreen(
    viewModel: LoginViewModel,
    action: LoginActions,
    userState: UserUiState,
    cifState: CifUiState? = null,
) {
    // Sử dụng ViewModel để quản lý state
    val username by viewModel.username.collectAsState()
    val password by viewModel.password.collectAsState()
    val passwordVisible by viewModel.passwordVisible.collectAsState()
    val showUsernameTooltip by viewModel.showUsernameTooltip.collectAsState()

    // Màn hình background -> gradient or image
    Box(
        modifier = Modifier
            .fillMaxSize()
            .dismissKeyboardOnClickOutside(),
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize(),
        )

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(0.dp)
                .verticalScroll(rememberScrollState()), // scroll if needed
        ) {
            // ~~~ row icon app, cờ VI, bell, share ~~~
            TopBarRow({ action.onNotifyClick() }, { action.onShareClick() })

            // Thêm khoảng cách rộng phía trên để logo ở vị trí thích hợp
            Spacer(modifier = Modifier.height(80.dp))

            // ~~~ big container ~~~
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 30.dp),
            ) {
                // Hiển thị form đăng nhập hoặc thông tin user dựa vào trạng thái isActive
                if (userState.isActive) {
                    // ~~~ Card user info ~~~
                    UserInfoCard(
                        fullName = userState.fullName,
                        onRefreshClick = { action.onRefreshClick() },
                    )
                } else {
                    // ~~~ "Tên đăng nhập" -> BaseEditText
                    Spacer(modifier = Modifier.height(50.dp))

                    // Box để chứa BaseEditText và tooltip
                    Box(modifier = Modifier.fillMaxWidth()) {
                        val infoIcon: @Composable () -> Unit = {
                            IconButton(
                                onClick = { viewModel.toggleUsernameTooltip() },
                                modifier = Modifier.size(40.dp),
                            ) {
                                Icon(
                                    painter = painterResource(id = R.drawable.ic_info),
                                    contentDescription = "Thông tin đăng nhập",
                                    tint = AppColors.textSecondary,
                                    modifier = Modifier.size(24.dp),
                                )
                            }
                        }

                        BaseEditText(
                            value = username,
                            pattern = InputFilters.USERNAME_PATTERN,
                            onValueChange = { viewModel.updateUsername(it) },
                            hint = "Tên đăng nhập",
                            leftDrawable = R.drawable.ic_user,
                            rightComposable = infoIcon,
                            showClearButton = true,
                            fontCus = 0,
                            hintColor = AppColors.textPrimary,
                            textColor = AppColors.textPrimary,
                            backgroundColor = AppColors.inputBackground,
                            borderColor = AppColors.borderColor,
                            borderWidth = 0.5.dp,
                            borderRadius = 10.dp,
                            modifier = Modifier
                                .fillMaxWidth()
                                .heightIn(min = 56.dp)
                                .padding(start = 12.dp, end = 12.dp, top = 0.dp, bottom = 0.dp),
                        )

                        // Thêm BaseTooltip cho username field
                        if (showUsernameTooltip) {
                            BaseTooltip(
                                title = "Tên đăng nhập có thể là:",
                                bulletPoints = listOf(
                                    "Thông tin tên đăng nhập của Quý khách đã được VietinBank gửi đến Email trước đó",
                                    "Số điện thoại của Quý khách",
                                ),
                                onDismissRequest = { viewModel.hideUsernameTooltip() },
                                // Quay lại BottomEnd như bạn đề xuất vị trí chuẩn hơn
                                alignment = Alignment.BottomEnd,
                                // Giữ nguyên offset như bản trước (đưa tooltip lên trên)
                                offset = IntOffset(-40, -80),
                                // Loại bỏ modifier không cần thiết
                                modifier = Modifier,
                                // Giữ BOTTOM_END để mũi tên hướng xuống
                                arrowPosition = ArrowPosition.BOTTOM_END,
                                // Tăng kích thước mũi tên để dễ nhìn hơn
                                arrowSize = 12.dp,
                                // Giữ maximum width để hiển thị nội dung đầy đủ
                                maxWidth = 320.dp,
                                // Giữ nguyên các tham số padding mặc định cho tooltip
                                padding = PaddingValues(
                                    horizontal = AppSizer.Gap.gap24,
                                    vertical = AppSizer.Gap.gap16,
                                ),
                            )
                        }
                    }
                }

                // ~~~ "Mật khẩu" -> BaseEditText
                Spacer(modifier = Modifier.height(8.dp))

                val passwordEyeIcon: @Composable () -> Unit = {
                    IconButton(
                        onClick = { viewModel.togglePasswordVisibility() },
                        modifier = Modifier.size(40.dp),
                    ) {
                        Icon(
                            painter = painterResource(id = if (passwordVisible) R.drawable.ic_eye_open else R.drawable.ic_eye_close),
                            contentDescription = "Hiện/ẩn mật khẩu",
                            tint = AppColors.textSecondary,
                            modifier = Modifier.size(24.dp),
                        )
                    }
                }

                BaseEditText(
                    value = password,
                    pattern = InputFilters.NONE_SPACE,
                    onValueChange = { viewModel.updatePassword(it) },
                    hint = "Mật khẩu",
                    leftDrawable = R.drawable.ic_lock,
                    rightComposable = passwordEyeIcon,
                    isPassword = !passwordVisible,
                    showClearButton = true,
                    fontCus = 0,
                    hintColor = AppColors.textPrimary,
                    textColor = AppColors.textPrimary,
                    backgroundColor = AppColors.inputBackground,
                    borderColor = AppColors.borderColor,
                    borderWidth = 0.5.dp,
                    borderRadius = 10.dp,
                    onRightDrawableClick = { viewModel.togglePasswordVisibility() },
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(min = 56.dp)
                        .padding(start = 12.dp, end = 12.dp, top = 0.dp, bottom = 0.dp),
                )

                if (cifState?.showCompanyDropdown == true) {
                    Spacer(modifier = Modifier.height(8.dp))
                    CompanyDropdown(
                        companies = cifState.multiCifList,
                        selectedCompany = cifState.selectedCompany,
                        onCompanySelected = cifState.onCompanySelected,
                    )
                }

                // ~~~ Cấp lại mật khẩu
                Spacer(modifier = Modifier.height(16.dp))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End,
                ) {
                    BaseText(
                        textSize = 14.sp,
                        text = "Cấp lại mật khẩu",
                        color = AppColors.textPrimary,
                        fontCus = 0,
                        onClick = { action.onForgotPasswordClick() },
                    )
                }

                // ~~~ "Đăng nhập" + fingerprint button
                Spacer(modifier = Modifier.height(24.dp))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    // Login button với FoundationButtonLight
                    FoundationButton(
                        text = "Đăng nhập",
                        onClick = {
                            if (userState.isActive) {
                                action.onLoginClick(username.trim(), password.trim())
                                action.onUserChange(username.trim())
                            } else {
                                action.onLoginClick(username.trim(), password.trim())
                                action.onUserChange(username.trim())
                            }
                        },
                        size = ButtonSize.Large,
                        modifier = Modifier.weight(1f),
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    // Fingerprint button
                    Box(
                        modifier = Modifier
                            .size(56.dp)
                            .clip(RoundedCornerShape(10.dp))
                            .background(AppColors.redButton)
                            .safeClickable { action.onTouchFingerClick() },
                        contentAlignment = Alignment.Center,
                    ) {
                        Image(
                            painter = painterResource(R.drawable.ic_fingerprint),
                            contentDescription = "Fingerprint",
                            modifier = Modifier.size(24.dp),
                        )
                    }
                }

                // ~~~ Row "Đồng bộ eToken" & "Mở khoá người dùng"
                Spacer(modifier = Modifier.height(24.dp))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                ) {
                    HomeActionBox(
                        text = "Đồng bộ eToken",
                        icon = R.drawable.ic_key,
                        onClick = { action.onSyncTokenClick() },
                        modifier = Modifier.weight(1f),
                    )

                    HomeActionBox(
                        text = "Mã khoá người dùng",
                        icon = R.drawable.ic_lock,
                        onClick = { action.onUnlockUserClick() },
                        modifier = Modifier.weight(1f),
                    )
                }

                // ~~~ "Mở tài khoản thanh toán Online"
                Spacer(modifier = Modifier.height(8.dp))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                ) {
                    HomeActionBox(
                        text = "Mở tài khoản thanh toán Online",
                        icon = R.drawable.ic_creditcard,
                        onClick = { action.onOpenAccountClick() },
                        modifier = Modifier.weight(1f),
                    )

                    // trường hợp là checker
                    if (viewModel.isChecker()) {
                        Spacer(modifier = Modifier.width(8.dp))

                        HomeActionBox(
                            text = "Lấy mã Soft OTP",
                            icon = R.drawable.ic_key,
                            onClick = { action.onSoftOtpClick() },
                            modifier = Modifier.weight(1f),
                        )
                    }
                }

                // Spacer để căn chỉnh phần bottom
                Spacer(modifier = Modifier.weight(1f))
            }

            // Spacer để đẩy BottomRow xuống dưới
            Spacer(modifier = Modifier.height(50.dp))

            // ~~~ Bottom row "Thị trường" | "Hỗ trợ"
            BottomRowHome(action.onMarketClick, action.onSupportClick)
        }
    }
}