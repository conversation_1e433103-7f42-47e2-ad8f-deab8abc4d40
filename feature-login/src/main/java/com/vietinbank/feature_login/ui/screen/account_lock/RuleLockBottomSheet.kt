package com.vietinbank.feature_login.ui.screen.account_lock

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_domain.models.login.MethodDomain
import com.vietinbank.core_domain.models.maker.ApproverDomains
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_login.ui.viewmodel.RuleBottomEvent

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RuleLockBottomSheet(
    ruleEvent: RuleBottomEvent? = null,
    onToggleRule: ((Any) -> Unit)? = null,
    onDismiss: () -> Unit = {},
) {
    if (ruleEvent != RuleBottomEvent.None) {
        val lstRule = mutableListOf<Any>()
        var selectorRule: Any? = null
        when (ruleEvent) {
            is RuleBottomEvent.Method -> {
                lstRule.addAll(ruleEvent.lstMethod ?: emptyList())
                selectorRule = ruleEvent.selector
            }

            is RuleBottomEvent.Approved -> {
                lstRule.addAll(ruleEvent.lstApprover ?: emptyList())
                selectorRule = ruleEvent.approved
            }

            else -> {}
        }
        ModalBottomSheet(
            onDismissRequest = onDismiss,
            shape = RoundedCornerShape(topStart = 8.dp, topEnd = 8.dp),
            containerColor = Color.White,
        ) {
            Column(
                modifier = Modifier.fillMaxWidth(),
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Icon(
                        // R.drawable.ic_user
                        painter = painterResource(
                            id = when (ruleEvent) {
                                is RuleBottomEvent.Method -> com.vietinbank.feature_login.R.drawable.ic_lock
                                else -> com.vietinbank.feature_login.R.drawable.ic_user
                            },
                        ),
                        contentDescription = null,
                        tint = AppColors.blue02,
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    // Title
                    Text(
                        text = when (ruleEvent) {
                            is RuleBottomEvent.Method -> "Phương thức cấp lại mật khẩu"
                            else -> "Danh sách người phê duyệt"
                        },
                        style = getComposeFont(4, 14.sp, AppColors.blue02),
                    )
                }

                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                        .heightIn(max = 400.dp), // Giới hạn chiều cao tối đa
                ) {
                    items(lstRule) { rule ->
                        LockRuleItem(
                            modifier = Modifier
                                .fillMaxWidth()
                                .safeClickable {
                                    onToggleRule?.invoke(rule)
                                },
                            item = rule,
                            isSelected = rule == selectorRule,
                        )
                    }
                }
                Spacer(modifier = Modifier.height(24.dp))
            }
        }
    }
}

@Composable
fun LockRuleItem(
    modifier: Modifier,
    item: Any? = null,
    isSelected: Boolean = false,
) {
    Column(modifier = modifier) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 12.dp),
        ) {
            Text(
                modifier = Modifier.weight(1f),
                text = when (item) {
                    is MethodDomain -> item.value ?: ""
                    is ApproverDomains -> item.username ?: ""
                    else -> ""
                },
                style = getComposeFont(
                    4,
                    14.sp,
                    if (isSelected) {
                        AppColors.blue02
                    } else {
                        AppColors.blue07
                    },
                ),
            )
            Spacer(modifier = Modifier.width(16.dp))
            Image(
                painter = painterResource(
                    if (isSelected) {
                        R.drawable.ic_radio_check
                    } else {
                        R.drawable.ic_radio_uncheck
                    },
                ),
                contentDescription = null,
            )
        }

        Spacer(
            modifier = Modifier
                .fillMaxWidth()
                .height(1.dp)
                .background(AppColors.blue09),
        )
    }
}