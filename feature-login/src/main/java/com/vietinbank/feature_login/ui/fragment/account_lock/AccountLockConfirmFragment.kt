package com.vietinbank.feature_login.ui.fragment.account_lock

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.FlowUtils
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.login.LockActions
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_login.ui.screen.account_lock.LockConfirmScreen
import com.vietinbank.feature_login.ui.viewmodel.AccountLockViewModel
import com.vietinbank.feature_login.ui.viewmodel.LockUIState
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import kotlin.getValue

@AndroidEntryPoint
class AccountLockConfirmFragment : BaseFragment<AccountLockViewModel>() {
    override val viewModel: AccountLockViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        getExtrasData()
        initObserver()
    }

    private fun initObserver() {
        FlowUtils.collectFlow(this@AccountLockConfirmFragment, viewModel.otpLockDomain) { model ->
            viewModel.validateOTPLockAccount()?.let { desc ->
                showNoticeDialog(desc) {
                    appNavigator.popToLogin(clearBackStack = true)
                }
                return@collectFlow
            }
            viewModel.getResultLockAccount(model)?.let { result ->
                appNavigator.goToLockResultAccount(
                    bundleOf().apply {
                        putString(
                            Tags.TRANSACTION_BUNDLE,
                            Utils.g().provideGson().toJson(
                                LockUIState.ResultState(result),
                            ),
                        )
                    },
                )
            }
        }
    }

    private fun getExtrasData() {
        try {
            viewModel.setLockUIState(
                Utils.g().provideGson().fromJson(
                    arguments?.getString(Tags.TRANSACTION_BUNDLE),
                    LockUIState.InitState::class.java,
                ),
            )
        } catch (_: Exception) {
        }
    }

    @Composable
    override fun ComposeScreen() {
        val actions = LockActions(onBackClick = { appNavigator.popBackStack() }, onNextClick = {
            viewModel.validateInputField(false)?.let {
                showNoticeDialog(it)
                return@LockActions
            }
            viewModel.sendOTPLockAccount()
        }, onRuleClick = {
            viewModel.validateSelectorField(it)?.let { msg ->
                showNoticeDialog(msg)
                return@LockActions
            }
            viewModel.setRuleBottomEvent(it)
        }, onToggleRuleClick = {
            viewModel.setRuleSelector(it)
        })

        AppTheme {
            LockConfirmScreen(viewModel, actions)
        }
    }
}