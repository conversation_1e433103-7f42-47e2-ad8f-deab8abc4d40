package com.vietinbank.feature_login.ui.fragment

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_domain.softotp.ISoftManager
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_login.ui.screen.HomePreLoginScreen
import com.vietinbank.feature_login.ui.viewmodel.LoginViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class HomePreLoginFragment : BaseFragment<LoginViewModel>() {
    override val viewModel: LoginViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softManager: ISoftManager

    @Composable
    override fun ComposeScreen() {
        AppTheme {
            HomePreLoginScreen(
                userName = viewModel.getFullName().toString(),
                onLoginClick = {
                    appNavigator.goToLogin()
                },
            )
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (viewModel.isChecker()) {
            softManager.loadSoftStatus()
        }
    }
}