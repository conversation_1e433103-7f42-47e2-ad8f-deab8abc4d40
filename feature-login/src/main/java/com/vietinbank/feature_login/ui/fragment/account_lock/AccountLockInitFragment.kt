package com.vietinbank.feature_login.ui.fragment.account_lock

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import com.fis.ekyc.liveness.activity.LivenessActivity
import com.fis.ekyc.liveness.preference.ErrorCode
import com.fis.ekyc.liveness.preference.LivenessConfigManager
import com.fis.ekyc.liveness.preference.LivenessListener
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.FlowUtils
import com.vietinbank.core_domain.models.login.LockActions
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_login.ui.screen.account_lock.LockInitScreen
import com.vietinbank.feature_login.ui.viewmodel.AccountLockViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import kotlin.getValue

@AndroidEntryPoint
class AccountLockInitFragment : BaseFragment<AccountLockViewModel>(), LivenessListener {
    override val viewModel: AccountLockViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        getExtrasData()
        initObserver()
    }

    private fun getExtrasData() {
        viewModel.setTitleType(arguments?.getString(Tags.DATA_TYPE))
    }

    private fun initObserver() {
        FlowUtils.collectFlow(this@AccountLockInitFragment, viewModel.lockDomainState) { data ->
            //     maker - k admin -> khong face gi ca
//     checker - k admin - k dai dien -> khong face gi ca
//                - dai dien + da thu thap sinh tra hoc -> face
//                  - dai dien + chua thu thạp -> "Yêu cầu Mở khóa người dùng không thành công, Quý khách vui lòng liên hệ với CN/PGD VietinBank gần nhất để được hỗ trợ.” + [Mã lỗi]."
//     checker - admin- k dai dien -> tb "“Yêu cầu Mở khóa người dùng không thành công do Quý khách được cấp quyền người dùng quản trị. Vui lòng liên hệ với Chi nhánh/Phòng giao dịch VietinBank gần nhất để được hỗ trợ.” + [Mã lỗi]."
//                    - dai dien + da thu thap -> face
//                    - dai dien + chua thu thap -> tb "Yêu cầu Mở khóa người dùng không thành công do chưa thu thập Sinh trắc học, Quý khách vui lòng liên hệ với CN/PGD VietinBank gần nhất để được hỗ trợ.” + [Mã lỗi].

//            if ("y" == data?.isPresent && "1" == data?.ekycFlag) {
//                // di case face id
//            } else if ("y" == data?.isPresent) {
//                // chua thu thap
//                val tb = if ("checker" != "admin") {
//                    // k admin
//                    "Yêu cầu Mở khóa người dùng không thành công, Quý khách vui lòng liên hệ với CN/PGD VietinBank gần nhất để được hỗ trợ.” + [Mã lỗi]."
//                } else {
//                    "Yêu cầu Mở khóa người dùng không thành công do chưa thu thập Sinh trắc học, Quý khách vui lòng liên hệ với CN/PGD VietinBank gần nhất để được hỗ trợ.” + [Mã lỗi]."
//                }
//            } else if ("admin" == "") {
//                // k dai dien
//                val tb =
//                    "Yêu cầu Mở khóa người dùng không thành công do Quý khách được cấp quyền người dùng quản trị. Vui lòng liên hệ với Chi nhánh/Phòng giao dịch VietinBank gần nhất để được hỗ trợ.” + [Mã lỗi]."
//            } else {
//                // di luong cap mk binh thuong
//            }

            // message lỗi ==> anh Quang trả -> chỉ còn 3 case
            // đại diện + đã thu thâp -> faceId
            // admin -> di luồng bình thường
            if (Tags.TYPE_SAY_YES == data.present && Tags.TYPE_FLAG_YES == data.ekycFlag) {
                // faceId
                printLog("TuNA5 -- FaceId")
                context?.let { ct ->
                    LivenessConfigManager.startLiveness(ct, 30L, this)
                }
            } else {
                // di luong binh thuong
                viewModel.getBundleLockAccount()?.let { dataBundle ->
                    appNavigator.goToLockConfirmAccount(
                        bundleOf().apply { putString(Tags.TRANSACTION_BUNDLE, dataBundle) },
                    )
                }
            }
        }

        FlowUtils.collectFlow(this@AccountLockInitFragment, viewModel.faceLockAccount) { data ->
            if (Tags.TYPE_SAY_YES == data.match) {
                // chuyen sang mann xac nhan
                viewModel.getBundleLockAccount()?.let { dataBundle ->
                    appNavigator.goToLockConfirmAccount(
                        bundleOf().apply { putString(Tags.TRANSACTION_BUNDLE, dataBundle) },
                    )
                }
            } else {
                // show thong bao khong trung khop
                showNoticeDialog("Yêu cầu ${viewModel.typeScreen.value} không thành công do không thể xác minh FaceID, Quý khách vui lòng liên hệ với CN/PGD VietinBank gần nhất để được hỗ trợ")
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val actions = LockActions(onBackClick = { appNavigator.popBackStack() }, onNextClick = {
            viewModel.validateInputField()?.let { fieldMsg ->
                showNoticeDialog(fieldMsg)
                return@LockActions
            }
            viewModel.checkInformation()
        })

        AppTheme {
            LockInitScreen(viewModel, actions)
        }
    }

    override fun onError(
        error: ErrorCode,
        faceList: ArrayList<String>,
        activity: LivenessActivity,
    ) {
        when (error) {
            ErrorCode.LIVENESS_TIMEOUT -> {
            }

            ErrorCode.USER_REFUSE_PERMISSION -> {
            }

            ErrorCode.DEVICE_NOT_SATISFIED -> {
            }

            ErrorCode.OUTPUT_NOT_MEET_REQUIREMENT -> {
            }

            else -> {
            }
        }
    }

    override fun onSuccess(
        faceListBase64: ArrayList<String>,
        activity: LivenessActivity,
    ) {
        faceListBase64.forEach {
            println("Tuna5 - $it\n")
        }
        activity.finish()
        viewModel.compareFaceLockAccount(faceListBase64.firstOrNull())
    }
}