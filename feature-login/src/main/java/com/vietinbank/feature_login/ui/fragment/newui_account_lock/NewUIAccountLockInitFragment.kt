package com.vietinbank.feature_login.ui.fragment.newui_account_lock

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import com.fis.ekyc.liveness.activity.LivenessActivity
import com.fis.ekyc.liveness.preference.ErrorCode
import com.fis.ekyc.liveness.preference.LivenessConfigManager
import com.fis.ekyc.liveness.preference.LivenessListener
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.FlowUtils
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_login.ui.screen.account_lock.LockInitScreen
import com.vietinbank.feature_login.ui.viewmodel.AccountLockViewModel
import com.vietinbank.feature_login.ui.viewmodel.FaceLockInitEvent
import com.vietinbank.feature_login.ui.viewmodel.LockActionEvent
import com.vietinbank.feature_login.ui.viewmodel.LockInitEvent
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import kotlin.getValue

@AndroidEntryPoint
class NewUIAccountLockInitFragment : BaseFragment<AccountLockViewModel>(), LivenessListener {
    override val viewModel: AccountLockViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        getExtrasData()
        initObserver()
        handleSingleEvent { event ->
            when (event) {
                is LockActionEvent.NavigateBack -> {
                    appNavigator.popBackStack()
                }

                is LockActionEvent.NextClick -> {
                    viewModel.validateInputField()
                }

                is LockActionEvent.ShowValidationError -> {
                    showNoticeDialog(event.message)
                }
            }
        }
    }

    private fun getExtrasData() {
        viewModel.setTitleType(arguments?.getString(Tags.DATA_TYPE))
    }

    private fun initObserver() {
        FlowUtils.collectFlow(this@NewUIAccountLockInitFragment, viewModel.eventsLockDomain) { e ->
            when (e) {
                is LockInitEvent.StartLiveness -> {
                    // faceId
                    context?.let { ct ->
                        LivenessConfigManager.startLiveness(ct, 30L, this)
                    }
                }

                is LockInitEvent.GoToConfirm -> {
                    // di luong binh thuong
                    viewModel.getBundleLockAccount()?.let { dataBundle ->
                        appNavigator.goToLockConfirmAccount(dataBundle)
                    }
                }

                else -> {}
            }
        }

        FlowUtils.collectFlow(this@NewUIAccountLockInitFragment, viewModel.eventsFaceLockAccount) { e ->
            when (e) {
                is FaceLockInitEvent.GoToConfirmFace -> {
                    // chuyen sang mann xac nhan
                    viewModel.getBundleLockAccount()?.let { dataBundle ->
                        appNavigator.goToLockConfirmAccount(dataBundle)
                    }
                }

                is FaceLockInitEvent.ShowNotice -> {
                    // show thong bao khong trung khop
                    showNoticeDialog(e.message)
                }

                else -> {}
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.initUiState.collectAsState()
        AppTheme {
            LockInitScreen(uiState, viewModel::onAction)
        }
    }

    override fun onError(
        error: ErrorCode,
        faceList: ArrayList<String>,
        activity: LivenessActivity,
    ) {
        when (error) {
            ErrorCode.LIVENESS_TIMEOUT -> {
            }

            ErrorCode.USER_REFUSE_PERMISSION -> {
            }

            ErrorCode.DEVICE_NOT_SATISFIED -> {
            }

            ErrorCode.OUTPUT_NOT_MEET_REQUIREMENT -> {
            }

            else -> {
            }
        }
    }

    override fun onSuccess(
        faceListBase64: ArrayList<String>,
        activity: LivenessActivity,
    ) {
        activity.finish()
        viewModel.compareFaceLockAccount(faceListBase64.firstOrNull())
    }
}