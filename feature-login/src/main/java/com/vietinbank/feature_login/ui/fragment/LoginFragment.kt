package com.vietinbank.feature_login.ui.fragment

import android.Manifest
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.openUrl
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.models.UserProfData
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.BiometricHelper
import com.vietinbank.core_domain.models.login.CifUiState
import com.vietinbank.core_domain.models.login.LoginActions
import com.vietinbank.core_domain.models.login.LoginDomain
import com.vietinbank.core_domain.models.login.UserUiState
import com.vietinbank.core_domain.softotp.ISoftManager
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.base.dialog.VerifyPasswordDialog
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_login.ui.screen.LoginScreen
import com.vietinbank.feature_login.ui.viewmodel.LoginViewModel
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

/**
 * Created by vandz on 19/12/24.
 */
@AndroidEntryPoint
class LoginFragment : BaseFragment<LoginViewModel>() {
    override val viewModel: LoginViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softManager: ISoftManager

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        requestNotificationPermissionIfNeeded()
        setupObservers()
        setupErrorHandling()
        viewModel.updateLoginStatus()

        viewModel.forceUpdate()
    }

    private fun setupObservers() {
        // TODO: Migrate to Channel events pattern (see NewUILoginFragment for reference)
        // loginState and forceUpdateResult have been migrated to Channel events in ViewModel
        /*
        viewModel.loginState.observe(viewLifecycleOwner) { resource ->
            when (resource) {
                is Resource.Success -> {
                    moveWhenLoginSuccess(resource.data)
                    viewModel.updateLoginStatus()
                }

                is Resource.Error -> {
                    // Error đã được xử lý trong BaseFragment
                }
            }
        }

        viewModel.forceUpdateResult.observe(viewLifecycleOwner) { result ->
            when (result) {
                is Resource.Success -> {
                    viewModel.handleForceUpdateOrNavigate(ListFunctionId.FORCE_UPDATE_ALL) {}
                }

                is Resource.Error -> {
                    // Lỗi đã được xử lý tự động bởi BaseViewModel
                }
            }
        }
         */

        viewModel.isLoginAuthentication.observe(viewLifecycleOwner) { data ->
            printLog(" viewModel.isLoginAuthentication trigger: ${viewModel.currentUsername.value}")
            val bundle = Bundle().apply {
                putString(Tags.ADDFIELD1_BUNDLE, data.addField1)
                putString(Tags.USERNAME_BUNDLE, viewModel.currentUsername.value)
                putString(Tags.CIFNO_BUNDLE, viewModel.cifSharedSelected?.enterpriseid)
            }
            appNavigator.goToActive2FAStep1Fragment(bundle)
        }

        viewModel.accountFirstActive.observe(viewLifecycleOwner) { data ->
            printLog("data.status?.code: ${data.status?.code}")
            if (!TextUtils.isEmpty(data.corpUser?.status) && Tags.FIRST_LOGIN_SIGNAL == data.corpUser?.status) {
                // case dang nhap lan dau => dieu huong doi mat khau
                if (viewModel.isActive()) {
                    // xoa old data
                    onClearData()
                }
                showConfirmDialog(
                    message = data.status?.message.toString(),
                    positiveButtonText = "Đổi mật khẩu",
                    negativeButtonText = "Đóng",
                    positiveAction = {
                        viewModel.saveSession(
                            sessionId = data.sessionId.toString(),
                        )
                        saveProfileAfterLoginSuccess(data)

                        viewModel.updateLoginStatus()
                        appNavigator.goToActive()
                    },
                    negativeAction = {
                    },
                    dismissAction = {
                    },
                )
            } else {
                showNoticeDialog(data.status?.message.toString())
            }
        }

        viewModel.updatableResponse.observe(viewLifecycleOwner) { state ->
            showNoticeDialog(state.status?.message ?: "UpdatableResponse")
        }

        viewModel.isForceUpdate.observe(viewLifecycleOwner) { state ->
            showNoticeDialog(state.status?.message ?: "ForceUpdateResponse")
        }

        viewModel.showForgetPwdDialog.observe(viewLifecycleOwner) { state ->
            showNoticeDialog(
                message = state.status?.message ?: "Forgot Password Dialog",
                positiveAction = {},
                positiveButtonText = "Quên mật khẩu",
            )
        }

        viewModel.pwdExpire881Response.observe(viewLifecycleOwner) { data ->
            showNoticeDialog(
                message = data.status?.message ?: "PwdExpire881Response",
                positiveAction = {
                    viewModel.saveSession(
                        sessionId = data.sessionId.toString(),
                    )
                    saveProfileAfterLoginSuccess(data)
                    viewModel.updateLoginStatus()
                    appNavigator.goToActive()
                },
            )
        }

        viewModel.warningPwdAboutExpireLoginResponse.observe(viewLifecycleOwner) { data ->
            if (viewModel.isActive()) {
                // xoa old data
                onClearData()
            }
            showConfirmDialog(
                message = data.status?.message.toString(),
                positiveButtonText = "Đổi mật khẩu",
                negativeButtonText = "Đóng",
                positiveAction = {
                    viewModel.saveSession(
                        sessionId = data.sessionId.toString(),
                    )
                    viewModel.saveProfile(
                        UserProfData(
                            userName = viewModel.userName.toString(),
                        ),
                    )
                    appNavigator.goToActive()
                },
                negativeAction = {
                },
                dismissAction = {
                },
            )
        }

        viewModel.expirePassLoginResponse.observe(viewLifecycleOwner) { data ->
            showNoticeDialog(
                message = data.status?.message ?: "expirePassLoginResponse",
                positiveAction = {
                    viewModel.saveSession(
                        sessionId = data.sessionId.toString(),
                    )
                    saveProfileAfterLoginSuccess(data)
                    viewModel.updateLoginStatus()
                    appNavigator.goToActive()
                },
            )
        }

//        lifecycleScope.launch {
//            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
//                viewModel.isActive.collect { isActive ->
//
//                }
//            }
//        }

        viewModel.isLoginAuthentication.observe(viewLifecycleOwner) { state ->
            showNoticeDialog(state.status?.message ?: "LoginAuthencationResponse")
        }
    }

    private fun onClearData() {
        viewModel.clearSession()
        viewModel.clearProfile()
        viewModel.clearAllConfig()
        viewModel.transferCacheManager.clearTransferALL()
        viewModel.ottRegistrationRepository.clearAllOttCache()
    }

    /**
     * Cài đặt hoạt động xử lý các lỗi đặc biệt từ API
     */
    private fun setupErrorHandling() {
        // TODO: Migrate to Channel events pattern (see NewUILoginFragment for reference)
        // showErrorDialog has been migrated to Channel events in ViewModel
        // This fragment needs to be updated to collect events from viewModel.events Flow
        // For now, validation errors won't show dialogs in this old login screen

        // Migrated to Channel events - see LoginEvent.ShowBiometricNotAvailable
        /*
        viewModel.biometricNotAvailable.observe(viewLifecycleOwner) { message ->
            showNoticeDialog(message)
        }
         */
    }

    @Composable
    override fun ComposeScreen() {
        // Các state từ LiveData
        val isActive by viewModel.isActive.collectAsState()
        val userName by viewModel.userName.observeAsState(initial = "")
        val fullName by viewModel.fullName.observeAsState(initial = "")
        val multiCifResponse by viewModel.multiCifResponse.observeAsState()
        // Collect selectedCompany from StateFlow
        val selectedCompany by viewModel.selectedCompany.collectAsState()

        // Tạo các object cho LoginScreen
        val userState = UserUiState(
            isActive = isActive,
            userName = userName,
            fullName = fullName,
        )

        val actions = LoginActions(
            onLoginClick = { user, pass ->
                if (viewModel.isActive()) {
                    viewModel.login(viewModel.getUserName().toString(), pass)
                } else {
                    viewModel.login(user.trim(), pass)
                }
            },
            onUserChange = {
                if (viewModel.isActive()) {
                    viewModel.currentUsername.value = viewModel.getUserName()
                } else {
                    viewModel.currentUsername.value = it
                }
            },
            onTouchFingerClick = {
                if (viewModel.isActive()) {
                    if (viewModel.enableTouchID()) {
                        showFingerPrintPopup()
                    } else {
                        showVerifyPasswordDialog()
                    }
                } else {
                    showNoticeDialog("Quý khách vui lòng đăng nhập ứng dụng và sử dụng chức năng \"Cài đặt vân tay\" để kích hoạt tính năng này")
                }
            },
            onRefreshClick = {
                showConfirmDialog(
                    message = "Quý khách sẽ thoát ra khỏi tài khoản hiện tại. Quý khách có thực sự muốn đăng nhập tài khoản khác?",
                    positiveButtonText = "Hủy",
                    negativeButtonText = "Đồng ý",
                    positiveAction = {},
                    negativeAction = {
                        viewModel.logout()
                    },
                )
            },
            onForgotPasswordClick = {
                appNavigator.goToLockAccount(
                    bundleOf().apply {
                        putString(Tags.DATA_TYPE, Tags.TYPE_GROUP_RESET_PASSWORD)
                    },
                )
            },
            onNotifyClick = { showNoticeDialog("Chức năng đang xây dựng") },
            onMarketClick = { showNoticeDialog("Chức năng đang xây dựng") },
            onSupportClick = { showNoticeDialog("Chức năng đang xây dựng") },
            onShareClick = { showNoticeDialog("Chức năng đang xây dựng") },
            onSyncTokenClick = { activity?.openUrl(VSoftConstants.URL_SYNC) },
            onUnlockUserClick = {
                appNavigator.goToLockAccount(
                    bundleOf().apply {
                        putString(Tags.DATA_TYPE, Tags.TYPE_GROUP_UNLOCK_USER)
                    },
                )
            },
            onOpenAccountClick = { showNoticeDialog("Chức năng đang xây dựng") },
            onSoftOtpClick = {
                if (softManager.isAppActive) {
                    // device da kich hoat soft nhung user thi chua
                    appNavigator.goToEnterPIN(VSoftConstants.VtpOTPFlowType.GET_OTP.name)
                } else {
                    showNoticeDialog("Quý khách chưa kích hoạt Smart OTP trên ứng dụng.")
                }
            },
        )

        val cifState = if (multiCifResponse != null) {
            CifUiState(
                multiCifList = viewModel.cifSharedList,
                selectedCompany = selectedCompany,
                showCompanyDropdown = true,
                onCompanySelected = { company ->
                    viewModel.updateSelectedCompany(company)
                },
            )
        } else {
            null
        }

        AppTheme {
            LoginScreen(
                viewModel = viewModel,
                action = actions,
                userState = userState,
                cifState = cifState,
            )
        }
    }

    private fun showFingerPrintPopup() {
        viewModel.checkBiometricForLogin()
        BiometricHelper.showBiometricOrDeviceCredentialPrompt(
            fragment = this,
            title = "Đăng nhập",
            subtitle = "Dùng vân tay hoặc mã khóa",
            onSuccess = {
                // => user quét vân tay thành công HOẶC nhập PIN
                printLog("verify success")
                viewModel.login(
                    username = viewModel.getUserName().toString(),
                    password = "",
                    isActiveFinger = true,
                )
            },
            onError = { errorMsg ->
                showNoticeDialog(errorMsg)
            },
        )
    }

    private fun saveProfileAfterLoginSuccess(data: LoginDomain) {
        viewModel.saveProfile(
            UserProfData(
                userName = data.corpUser?.username.toString(),
                phone = data.corpUser?.phoneNo.toString(),
                fullName = data.corpUser?.fullname.toString(),
                cifNo = data.cifNo.toString(),
                roleId = data.corpUser?.roleId,
                keypassprofile = data.corpUser?.keypassprofile ?: "",
                keypasssoftotp = data.corpUser?.keypasssoftotp ?: "",
                email = data.corpUser!!.email,
                corpName = data.corpUser?.corpName ?: "",
                // keypass
                keypassCode = data.corpUser?.keypassid,
                keypassToken = data.corpUser?.keypassid,
                // next approver
                roleLevel = data.corpUser?.roleLevel,
                groupType = data.corpUser?.groupType,
                sessionID = data.sessionId,
                idNumber = data.corpUser?.idNumber,
                userId = data.userId,
                addField4 = data.addField4,
            ),
        )

        viewModel.setTimerConfig(true)

//        viewModel.updateTimeout(data.timeout ?: "300")
    }

    private fun moveWhenLoginSuccess(data: LoginDomain) {
        // case dang nhap lan sau (normal case)
        // saveDataLogin
        // dieu huong sang man hinh Home
        viewModel.saveSession(
            sessionId = data.sessionId.toString(),
        )
        printLog("Save session success: ${data.sessionId}")
        saveProfileAfterLoginSuccess(data)

        if (!TextUtils.isEmpty(data.addField3)) {
            printLog("AddField3: ${data.addField3}")
            viewModel.updateFingerID(data.addField3!!)
        }
        appNavigator.goToHome()
        viewModel.setEnableCheckEkyc()
    }

    private fun showVerifyPasswordDialog() {
        val dialog = VerifyPasswordDialog.newInstance(
            title = "Thông báo",
            message = "Quý khách vui lòng nhập mật khẩu để kích hoạt đăng nhập bằng vân tay.",
        )
        dialog.setOnConfirmListener { password ->
            viewModel.login(
                username = viewModel.getUserName().toString(),
                password = password,
                regTouchId = "1",
            )
        }
        dialog.setOnDismissClickListener {
            // user bấm (X)
        }
        dialog.show(childFragmentManager, "VerifyPasswordDialog")
    }

    private val notificationPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission(),
    ) { isGranted ->
        printLog("Notification permission granted: $isGranted")
    }

    private fun requestNotificationPermissionIfNeeded() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(
                    requireContext(),
                    Manifest.permission.POST_NOTIFICATIONS,
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                // Xin permission
                notificationPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
            }
        }
    }
}