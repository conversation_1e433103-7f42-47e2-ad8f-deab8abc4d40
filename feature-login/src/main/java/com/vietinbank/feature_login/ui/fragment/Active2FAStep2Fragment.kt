package com.vietinbank.feature_login.ui.fragment

import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.compose.runtime.Composable
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.models.UserProfData
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.login.AuthenticationMethod
import com.vietinbank.core_domain.models.login.LoginDomain
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_login.ui.screen.Active2FAStep2
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class Active2FAStep2Fragment : BaseFragment<Active2FAViewModel>() {
    override val viewModel: Active2FAViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        getBundleData()
        initObserver()
    }

    private fun initObserver() {
        viewModel.apply {
            verifyOTPResponse.observe(viewLifecycleOwner) { data ->
                when (data) {
                    is Resource.Success -> {
                        printLog("verify otp res: ${Utils.g().provideGson().toJson(data.data)}")
                        moveWhenLoginSuccess(data.data.loginResponse)
                    }

                    else -> {}
                }
            }
        }
    }

    private fun getBundleData() {
        arguments?.getString(Tags.ADDFIELD1_BUNDLE)?.let { data ->
            viewModel.addField1 = data
        }
        arguments?.getString(Tags.USERNAME_BUNDLE)?.let { data ->
            viewModel.currentUsername = data
        }
        arguments?.getString(Tags.TYPE_BUNDLE)?.let { data ->
            viewModel.methodSelected = AuthenticationMethod("", data)
        }
        arguments?.getString(Tags.EFAST_ID_BUNDLE)?.let { data ->
            viewModel.eFastID = data
        }
        arguments?.getString(Tags.CIFNO_BUNDLE)?.let { data ->
            viewModel.currentCifNo = data
        }
    }

    private fun saveProfileAfterLoginSuccess(data: LoginDomain) {
        viewModel.saveProfile(
            UserProfData(
                userName = data.corpUser?.username.toString(),
                phone = data.corpUser?.phoneNo.toString(),
                fullName = data.corpUser?.fullname.toString(),
                cifNo = data.cifNo.toString(),
                roleId = data.corpUser?.roleId,
                keypassprofile = data.corpUser?.keypassprofile ?: "",
                keypasssoftotp = data.corpUser?.keypasssoftotp ?: "",
                email = data.corpUser!!.email,
                corpName = data.corpUser?.corpName ?: "",
                // keypass
                keypassCode = data.corpUser?.keypassid,
                keypassToken = data.corpUser?.keypassid,
                // next approver
                roleLevel = data.corpUser?.roleLevel,
                groupType = data.corpUser?.groupType,
                sessionID = data.sessionId,
                idNumber = data.corpUser?.idNumber,
                userId = data.userId,
                addField4 = data.addField4,
            ),
        )
//        viewModel.updateTimeout(data.timeout ?: "300")
        viewModel.setTimerConfig(true)
    }

    private fun moveWhenLoginSuccess(data: LoginDomain) {
        // case dang nhap lan sau (normal case)
        // dieu huong sang man hinh Home
        viewModel.saveSession(
            sessionId = data.sessionId.toString(),
        )
        saveProfileAfterLoginSuccess(data)
        if (!TextUtils.isEmpty(data.addField3)) {
            printLog("AddField3: ${data.addField3}")
            viewModel.updateFingerID(data.addField3!!)
        }
        appNavigator.goToHomeFrom2FA()
    }

    @Composable
    override fun ComposeScreen() {
        AppTheme {
            Active2FAStep2(
                onContinueClick = { otp ->
                    viewModel.verifyOTP(otp)
                },
            )
        }
    }
}