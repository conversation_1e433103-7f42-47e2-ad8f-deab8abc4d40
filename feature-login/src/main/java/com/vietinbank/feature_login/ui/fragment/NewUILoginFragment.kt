package com.vietinbank.feature_login.ui.fragment

// Dialog imports no longer needed - using CompanySelectionSheet in Compose
// CompanySelectionDialog and CompanySelectionResult are replaced by the Sheet
// import com.vietinbank.feature_login.ui.dialog.CompanySelectionDialog
// import com.vietinbank.feature_login.ui.dialog.CompanySelectionResult
import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.vietinbank.core_common.constants.ListFunctionId
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.models.UserProfData
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.BiometricHelper
import com.vietinbank.core_domain.models.login.LoginDomain
import com.vietinbank.core_domain.softotp.ISoftManager
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.base.dialog.VerifyPasswordDialog
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_login.ui.events.LoginEvent
import com.vietinbank.feature_login.ui.screen.newui.NewUILoginAction
import com.vietinbank.feature_login.ui.screen.newui.NewUILoginScreen
import com.vietinbank.feature_login.ui.viewmodel.LoginViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject

// Bundle key constants for fragment arguments
private object BundleKeys {
    const val SESSION_EXPIRED_FLAG = "session_expired_flag"
}

// Animation timing constants
private object AnimationConstants {
    const val SESSION_EXPIRED_DIALOG_DELAY = 450L // milliseconds (wait for initial focus)
}

/**
 * NewUI Login Fragment - Handles all login business logic for NewUI
 * This fragment can display either Welcome screen (not active) or Login screen (active)
 */
@AndroidEntryPoint
class NewUILoginFragment : BaseFragment<LoginViewModel>() {

    // region Properties
    override val viewModel: LoginViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softManager: ISoftManager

    // Permission launcher - declared once as property
    private val notificationPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission(),
    ) { isGranted ->
        printLog(
            if (isGranted) {
                getString(R.string.login_notification_permission_granted)
            } else {
                getString(R.string.login_notification_permission_denied)
            },
        )
    }
    // endregion

    // region Lifecycle
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        requestNotificationPermissionIfNeeded()
        setupEventCollector() // NEW: Collect Channel events
        setupAllObservers() // Keep for backward compatibility
        viewModel.updateLoginStatus()
        viewModel.forceUpdate()

        // Check if navigated here due to session expiry
        checkAndShowSessionExpiredDialog()
    }
    // endregion

    // region Compose UI
    @Composable
    override fun ComposeScreen() {
        AppTheme {
            LoginScreenContent()
        }
    }

    @Composable
    private fun LoginScreenContent() {
        // Single source of truth - unified state
        val uiState by viewModel.uiState.collectAsState()

        // Note: Company dialog is now handled via Channel events in handleLoginEvent()
        // This prevents the dialog from auto-showing on back navigation

        NewUILoginScreen(
            isActive = uiState.isActive,
            username = uiState.username,
            password = uiState.password,
            passwordVisible = uiState.passwordVisible,
            showUsernameTooltip = uiState.showUsernameTooltip,
            selectedCompany = uiState.selectedCompany,
            fullName = uiState.fullName,
            usernameError = uiState.usernameError,
            passwordError = uiState.passwordError,
            showCompanyDialog = false, // Dialog is shown via one-time events
            dialogState = uiState.dialog,
            passwordFocusTrigger = uiState.passwordFocusTrigger,
            onAction = { action ->
                handleScreenAction(action)
            },
            onDismissDialog = {
                viewModel.dismissDialog()
            },
        )
    }

    /**
     * Show Company Selection Dialog using the new BaseDialog architecture
     * Maps CifSharedDomain to Company and handles result
     */
    // No longer needed - CompanySelectionSheet is shown directly in Compose
    // The dialog state is managed in LoginViewModel and rendered in NewUILoginScreen
    /*
    private fun showCompanySelectionDialog(
        companies: List<CifSharedDomain>,
        selectedId: String?,
    ) {
        // Map CifSharedDomain to Company for dialog
        val companyList = companies.map { cif ->
            Company(
                id = cif.enterpriseid ?: "",
                name = cif.enterprisename ?: "",
                taxCode = cif.enterpriseid ?: "", // Use enterpriseid as tax code display
            )
        }

        // Show the dialog
        CompanySelectionDialog.show(
            fragmentManager = childFragmentManager,
            companies = companyList,
            selectedId = selectedId,
        )
    }
     */

    // No longer needed - CompanySelectionSheet handles everything in Compose
    // Results are handled via direct callbacks in NewUILoginScreen
    /*
    // Setup persistent listener for company selection dialog results
    private fun setupCompanySelectionListener() {
        childFragmentManager.setFragmentResultListener(
            "company_selection_result",
            viewLifecycleOwner,
        ) { requestKey, bundle ->
            val isCancelled = bundle.getBoolean("key_cancelled", false)

            if (!isCancelled) {
                val result = bundle.getParcelable<CompanySelectionResult>("key_result")

                result?.let { selectionResult ->
                    // Get companies from ViewModel using proper encapsulation
                    val companies = viewModel.getCompaniesForSelection()

                    // Find the selected CifSharedDomain from result
                    val selectedCif = companies?.find { it.enterpriseid == selectionResult.companyId }

                    selectedCif?.let {
                        viewModel.selectCompany(it)
                        // Dialog auto-dismisses on result
                        // Now call login with the selected company
                        val username = viewModel.username.value.trim()
                        val password = viewModel.password.value

                        // Call login based on user status
                        if (viewModel.isActive()) {
                            viewModel.login(viewModel.getUserName().toString(), password)
                        } else {
                            viewModel.login(username, password)
                        }
                    }
                }
            } else {
                // User dismissed dialog without selecting
                // Clear multi-cif to allow re-triggering
                viewModel.clearMultiCifState()
            }
        }
    }
     */
    // endregion

    // region Event Collector (NEW - Channel-based one-time events)
    private fun setupEventCollector() {
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.events.collect { event ->
                    handleLoginEvent(event)
                }
            }
        }
    }

    private fun handleLoginEvent(event: LoginEvent) {
        when (event) {
            is LoginEvent.NavigateTo2FA -> {
                val bundle = Bundle().apply {
                    putString(Tags.ADDFIELD1_BUNDLE, event.addField1)
                    putString(Tags.USERNAME_BUNDLE, event.username)
                    putString(Tags.CIFNO_BUNDLE, event.cifNo)
                }
                appNavigator.goToActive2FAStep1Fragment(bundle)
            }
            is LoginEvent.NavigateToActive -> {
                viewModel.saveSession(sessionId = event.sessionId)
                appNavigator.goToActive()
            }
            is LoginEvent.NavigateToHome -> {
                navigateToHome()
            }
            is LoginEvent.NavigateToLockAccount -> {
                appNavigator.goToLockAccount(
                    bundleOf().apply {
                        putString(Tags.DATA_TYPE, event.dataType)
                    },
                )
            }
            is LoginEvent.ShowErrorDialog -> {
                showNoticeDialog(event.message)
            }
            is LoginEvent.ShowNoticeDialog -> {
                showNoticeDialog(
                    message = event.message,
                    positiveAction = event.positiveAction ?: {},
                    positiveButtonText = event.positiveButtonText ?: getString(R.string.dialog_button_confirm),
                )
            }
            is LoginEvent.ShowConfirmDialog -> {
                showConfirmDialog(
                    message = event.message,
                    positiveButtonText = event.positiveButtonText,
                    negativeButtonText = event.negativeButtonText,
                    positiveAction = event.positiveAction,
                    negativeAction = event.negativeAction ?: {},
                    dismissAction = {},
                )
            }
            is LoginEvent.ShowPasswordExpiryWarning -> {
                handlePasswordExpiryWarning(event.data)
            }
            is LoginEvent.ShowPasswordExpiryNew -> {
                handlePasswordExpiryNew(event.data)
            }
            is LoginEvent.ShowPasswordExpiredDialog -> {
                handlePasswordExpired(event.data)
            }
            is LoginEvent.ShowPasswordExpired881Dialog -> {
                handlePasswordExpired881(event.data)
            }
            is LoginEvent.ShowFirstLoginDialog -> {
                handleFirstLoginResponse(event.data)
            }
            is LoginEvent.ShowForgotPasswordDialog -> {
                showNoticeDialog(
                    message = event.message,
                    positiveAction = {},
                    positiveButtonText = getString(R.string.login_forgot_password_button),
                )
            }
            is LoginEvent.ShowBiometricNotAvailable -> {
                // Backward compatibility - generic message
                showNoticeDialog(event.message)
            }
            is LoginEvent.ShowBiometricOsNotSupported -> {
                // OS does not support biometric (Android < 6.0)
                showNoticeDialog(event.message)
            }
            is LoginEvent.ShowBiometricDeviceNotEnrolled -> {
                // Device has no fingerprint/PIN/pattern enrolled
                // Show dialog with option to go to Settings
                showConfirmDialog(
                    message = event.message,
                    positiveButtonText = getString(R.string.button_settings),
                    negativeButtonText = getString(R.string.dialog_button_close),
                    positiveAction = {
                        // Open Security Settings
                        try {
                            val intent = Intent(android.provider.Settings.ACTION_SECURITY_SETTINGS)
                            startActivity(intent)
                        } catch (e: Exception) {
                            showNoticeDialog(getString(R.string.error_cannot_open_settings))
                        }
                    },
                    negativeAction = {},
                )
            }
            is LoginEvent.ShowVerifyPasswordForBiometric -> {
                showVerifyPasswordDialog()
            }
            is LoginEvent.ShowBiometricPrompt -> {
                showFingerPrintPopup()
            }
            is LoginEvent.ShowForceUpdateDialog -> {
                showNoticeDialog(event.message)
            }
            is LoginEvent.ShowUpdatableDialog -> {
                showNoticeDialog(event.message)
            }
            is LoginEvent.HandleForceUpdate -> {
                viewModel.handleForceUpdateOrNavigate(event.functionId) {}
            }
            is LoginEvent.ShowCompanySelectionDialog -> {
                // Now handled directly in Compose via DialogState.CompanySelection
                // The CompanySelectionSheet is shown when dialogState changes
                // No Fragment-level handling needed anymore
            }
            is LoginEvent.LoginSuccessWithSetup -> {
                // DEPRECATED - Using LoginResult instead to avoid duplicate processing
                // This event is kept for backward compatibility but should not be used
            }
            is LoginEvent.LoginResult -> {
                // Handle login result - save data and navigate
                val data = event.data

                // Save session
                viewModel.saveSession(sessionId = data.sessionId.toString())

                // Save user profile
                saveProfileAfterLoginSuccess(data)

                // Set timer config
                viewModel.setTimerConfig(true)

                // Update fingerID if present
                if (!TextUtils.isEmpty(data.addField3)) {
                    viewModel.updateFingerID(data.addField3!!)
                }

                // Navigate to home
                navigateToHome()

                // Enable eKYC check and update status
                viewModel.setEnableCheckEkyc()
                viewModel.updateLoginStatus()
            }
            is LoginEvent.ChangePasswordResult -> {
                // Handle change password result
                showNoticeDialog(
                    message = event.data.status.message ?: "Đổi mật khẩu thành công",
                    positiveAction = { /* Navigate to login or appropriate screen */ },
                )
            }
            is LoginEvent.ForceUpdateResult -> {
                // Handle force update result
                viewModel.handleForceUpdateOrNavigate(ListFunctionId.FORCE_UPDATE_ALL) {}
            }
        }
    }
    // endregion

    // region Observers Setup
    private fun setupAllObservers() {
        setupUpdateObservers()
        setupErrorHandling() // Add error handling observers
    }

    private fun setupUpdateObservers() {
        // Force update handling - Migrated to Channel events
        // Now handled in handleLoginEvent() -> LoginEvent.ForceUpdateResult
        /*
        viewModel.forceUpdateResult.observe(viewLifecycleOwner) { result ->
            when (result) {
                is Resource.Success -> {
                    viewModel.handleForceUpdateOrNavigate(ListFunctionId.FORCE_UPDATE_ALL) {}
                }
                is Resource.Error -> {
                    // Error handled by BaseViewModel
                }
            }
        }
         */

        // Update dialogs now handled via Channel events
    }

    /**
     * Setup error handling observers for validation and biometric errors
     */
    private fun setupErrorHandling() {
        // All error handling migrated to Channel events
        // See handleLoginEvent() for:
        // - LoginEvent.ShowErrorDialog (validation errors)
        // - LoginEvent.ShowBiometricNotAvailable (biometric errors)
        /*
        viewModel.biometricNotAvailable.observe(viewLifecycleOwner) { message ->
            showNoticeDialog(message)
        }
         */
    }
    // endregion

    // region User Actions
    private fun handleScreenAction(action: NewUILoginAction) {
        when (action) {
            is NewUILoginAction.OnBackClick -> {
                appNavigator.navigateUp()
            }
            is NewUILoginAction.OnUsernameChange -> {
                viewModel.updateUsername(action.username)
                viewModel.currentUsername.value = action.username
            }
            is NewUILoginAction.OnPasswordChange -> {
                viewModel.updatePassword(action.password)
            }
            is NewUILoginAction.OnPasswordVisibilityToggle -> {
                viewModel.togglePasswordVisibility()
            }
            is NewUILoginAction.OnUsernameTooltipToggle -> {
                viewModel.toggleUsernameTooltip()
            }
            is NewUILoginAction.OnCompanySelect -> {
                viewModel.selectCompany(action.company)
            }
            is NewUILoginAction.OnConfirmCompanySelection -> {
                // Close dialog and perform login with selected company
                val username = viewModel.username.value
                val password = viewModel.password.value
                if (viewModel.isActive()) {
                    viewModel.login(viewModel.getUserName().toString(), password)
                } else {
                    viewModel.login(username.trim(), password)
                }
            }
            is NewUILoginAction.OnDismissCompanyDialog -> {
                // Back - Clear selection and dismiss
                viewModel.clearCompanySelection()
            }
            is NewUILoginAction.OnLoginClick -> {
                viewModel.onLoginClicked()
            }
            is NewUILoginAction.OnBiometricClick -> {
                handleBiometricLogin()
            }
            is NewUILoginAction.OnForgotPasswordClick -> {
                appNavigator.goToLockAccount(
                    bundleOf().apply {
                        putString(Tags.DATA_TYPE, Tags.TYPE_GROUP_RESET_PASSWORD)
                    },
                )
            }
            is NewUILoginAction.OnUnlockUserClick -> {
                appNavigator.goToLockAccount(
                    bundleOf().apply {
                        putString(Tags.DATA_TYPE, Tags.TYPE_GROUP_UNLOCK_USER)
                    },
                )
            }
            is NewUILoginAction.OnHotlineClick -> {
                showNoticeDialog(getString(com.vietinbank.feature_login.R.string.login_hotline))
            }
            is NewUILoginAction.OnGuideClick -> {
                showNoticeDialog(getString(R.string.feature_guide_under_construction))
            }
        }
    }
    // endregion

    // region Biometric Handling
    private fun handleBiometricLogin() {
        if (viewModel.isActive()) {
            if (viewModel.enableTouchID()) {
                // Call checkBiometricForLogin which will send appropriate event
                // The event handler will then call showFingerPrintPopup() if ready
                viewModel.checkBiometricForLogin()
            } else {
                showVerifyPasswordDialog()
            }
        } else {
            showNoticeDialog(getString(R.string.login_biometric_activation_message))
        }
    }

    private fun showVerifyPasswordDialog() {
        val dialog = VerifyPasswordDialog.newInstance(
            title = getString(R.string.login_verify_password_title),
            message = getString(R.string.login_verify_password_message),
        )
        dialog.setOnConfirmListener { password ->
            viewModel.login(
                username = viewModel.getUserName().toString(),
                password = password,
                regTouchId = "1",
            )
        }
        dialog.setOnDismissClickListener {
            // user dismissed
        }
        dialog.show(childFragmentManager, "VerifyPasswordDialog")
    }

    private fun showFingerPrintPopup() {
        // Don't call checkBiometricForLogin() here - it creates infinite loop!
        // This function is called FROM the event handler after check is done
        BiometricHelper.showBiometricOrDeviceCredentialPrompt(
            fragment = this,
            title = getString(R.string.login_biometric_title),
            subtitle = getString(R.string.login_biometric_subtitle),
            onSuccess = {
                printLog("verify success")
                viewModel.login(
                    username = viewModel.getUserName().toString(),
                    password = "",
                    isActiveFinger = true,
                )
            },
            onError = { errorMsg ->
                showNoticeDialog(errorMsg)
            },
        )
    }
    // endregion

    // region Helper Methods
    private fun handleFirstLoginResponse(data: LoginDomain) {
        printLog("data.status?.code: ${data.status?.code}")
        if (!TextUtils.isEmpty(data.corpUser?.status) && Tags.FIRST_LOGIN_SIGNAL == data.corpUser?.status) {
            if (viewModel.isActive()) {
                clearAllData()
            }
            showConfirmDialog(
                message = data.status?.message.toString() + "\n (" + data.status?.subCode.toString() + ")",
                positiveButtonText = getString(com.vietinbank.feature_login.R.string.change_password_title),
                negativeButtonText = getString(com.vietinbank.feature_login.R.string.company_dialog_cancel_close),
                positiveAction = {
                    viewModel.saveSession(sessionId = data.sessionId.toString())
                    saveProfileAfterLoginSuccess(data)
                    viewModel.updateLoginStatus()
                    appNavigator.goToActive()
                },
                negativeAction = {},
                dismissAction = {},
            )
        } else {
            showNoticeDialog(data.status?.message.toString())
        }
    }

    private fun handlePasswordExpiryWarning(data: LoginDomain) {
        if (viewModel.isActive()) {
            clearAllData()
        }
        showConfirmDialog(
            message = data.status?.message.toString() + "\n (" + data.status?.subCode.toString() + ")",
            positiveButtonText = getString(com.vietinbank.feature_login.R.string.change_password_title),
            negativeButtonText = getString(com.vietinbank.feature_login.R.string.company_dialog_cancel_close),
            positiveAction = {
                viewModel.saveSession(sessionId = data.sessionId.toString())
                viewModel.saveProfile(
                    UserProfData(
                        userName = data.corpUser?.username ?: viewModel.userName.toString(),
                        cifNo = data.cifNo,
                        status = data.corpUser?.status,
                    ),
                )
                appNavigator.goToActive()
            },
            negativeAction = {},
            dismissAction = {},
        )
    }

    private fun handlePasswordExpiryNew(data: LoginDomain) {
        if (viewModel.isActive()) {
            clearAllData()
        }
        showConfirmDialog(
            message = data.status?.message.toString() + "\n (" + data.status?.subCode.toString() + ")",
            positiveButtonText = getString(com.vietinbank.feature_login.R.string.change_password_title),
            negativeButtonText = getString(R.string.company_dialog_cancel),
            positiveAction = {
                viewModel.saveSession(sessionId = data.sessionId.toString())
                viewModel.saveProfile(
                    UserProfData(
                        userName = data.corpUser?.username ?: viewModel.userName.toString(),
                        cifNo = data.cifNo,
                        status = data.corpUser?.status,
                    ),
                )
                appNavigator.goToActive()
            },
            negativeAction = {},
            dismissAction = {},
        )
    }

    private fun handlePasswordExpired(data: LoginDomain) {
        // Generic password expired - navigate to Active screen to change password
        showNoticeDialog(
            message = data.status?.message ?: getString(R.string.login_password_expired),
            positiveAction = {
                viewModel.saveSession(sessionId = data.sessionId.toString())
                saveProfileAfterLoginSuccess(data)
                viewModel.updateLoginStatus()
                appNavigator.goToActive()
            },
        )
    }

    private fun handlePasswordExpired881(data: LoginDomain) {
        // Password expired with code 881 - specific handling
        showNoticeDialog(
            message = data.status?.message ?: getString(R.string.login_password_expired_881),
            positiveAction = {
                viewModel.saveSession(sessionId = data.sessionId.toString())
                saveProfileAfterLoginSuccess(data)
                viewModel.updateLoginStatus()
                appNavigator.goToActive()
            },
        )
    }

    private fun requestNotificationPermissionIfNeeded() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            val granted = ContextCompat.checkSelfPermission(
                requireContext(),
                Manifest.permission.POST_NOTIFICATIONS,
            ) == PackageManager.PERMISSION_GRANTED

            if (!granted) {
                notificationPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
            }
        }
    }

    private fun clearAllData() {
        viewModel.clearSession()
        viewModel.clearProfile()
        viewModel.clearAllConfig()
        viewModel.transferCacheManager.clearTransferALL()
        viewModel.ottRegistrationRepository.clearAllOttCache()
    }

    private fun saveProfileAfterLoginSuccess(data: LoginDomain) {
        viewModel.saveProfile(
            UserProfData(
                userName = data.corpUser?.username.toString(),
                phone = data.corpUser?.phoneNo.toString(),
                fullName = data.corpUser?.fullname.toString(),
                cifNo = data.cifNo.toString(),
                roleId = data.corpUser?.roleId,
                keypassprofile = data.corpUser?.keypassprofile ?: "",
                keypasssoftotp = data.corpUser?.keypasssoftotp ?: "",
                email = data.corpUser!!.email,
                corpName = data.corpUser?.corpName ?: "",
                // keypass
                keypassCode = data.corpUser?.keypassid,
                keypassToken = data.corpUser?.keypassid,
                // next approver
                roleLevel = data.corpUser?.roleLevel,
                groupType = data.corpUser?.groupType,
                sessionID = data.sessionId,
                idNumber = data.corpUser?.idNumber,
                userId = data.userId,
                addField4 = data.addField4,
                status = data.corpUser?.status.toString(),
            ),
        )

        viewModel.setTimerConfig(true)
    }

    // region Session Expired Handling
    /**
     * Check if navigated here due to session expiry and show dialog
     * Security-first approach: Navigate immediately, then show dialog
     */
    private fun checkAndShowSessionExpiredDialog() {
        // Check if Bundle contains session expired flag
        val hasSessionExpired = arguments?.getBoolean(BundleKeys.SESSION_EXPIRED_FLAG, false) ?: false

        if (hasSessionExpired) {
            // Clear the flag to prevent showing dialog on rotation
            arguments?.remove(BundleKeys.SESSION_EXPIRED_FLAG)

            // Get message from string resource (auto handles locale)
            val sessionExpiredMessage = getString(R.string.server_expired_session)

            // Delay dialog to let initial focus complete first (avoid conflict)
            lifecycleScope.launch {
                // Wait for initial focus animation to complete
                delay(AnimationConstants.SESSION_EXPIRED_DIALOG_DELAY)

                // Show dialog with the session expired message
                // Dialog is shown AFTER navigation to Login, ensuring security
                showNoticeDialog(
                    message = sessionExpiredMessage,
                    positiveButtonText = getString(R.string.dialog_button_relogin),
                    cancelable = false,
                    positiveAction = {
                        // Request focus to password field when dialog is dismissed
                        viewModel.requestPasswordFocus()
                    },
                )
            }
        }
    }
    // endregion

    private fun navigateToHome() {
        if (viewModel.isNonFinancial()) {
            appNavigator.goToAccountFromLogin(true)
        } else {
            appNavigator.goToHome()
        }
    }
}