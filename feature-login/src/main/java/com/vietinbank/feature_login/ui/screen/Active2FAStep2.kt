package com.vietinbank.feature_login.ui.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ui.base.compose.BaseAppBar
import com.vietinbank.core_ui.base.compose.BaseOtpBoxInput
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable

/**
 * Created by vandz on 17/3/25.
 */

@Composable
fun Active2FAStep2(
    onContinueClick: (String) -> Unit = {},
    onResendClick: () -> Unit = {},
    contact: String = "098****123", // Số điện thoại hoặc email người dùng
    requestId: String = "ID yêu cầu", // ID của yêu cầu
    remainingTime: String = "04:30", // Thời gian còn lại
    otpInput: MutableState<String> = remember { mutableStateOf("") },
) {
    val primaryColor = AppColors.gradientStart

    Column(
        modifier = Modifier.fillMaxSize(),
    ) {
        // AppBar
        BaseAppBar(
            title = "Kích hoạt",
            onBackClick = {},
        )

        // Heading text
        BaseText(
            text = "Nhập mã OTP để hoàn tất kích hoạt tài khoản",
            color = Color.White,
            textSize = 18.sp,
            fontCus = 1, // Semi-bold
            lineHeight = 28.sp,
            modifier = Modifier.padding(horizontal = 24.dp, vertical = 20.dp),
        )

        // OTP Card
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 24.dp),
            shape = RoundedCornerShape(12.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White),
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                verticalArrangement = Arrangement.spacedBy(24.dp),
            ) {
                // Instructions text
                BaseText(
                    text = "Vui lòng nhập mã xác thực đăng nhập tương ứng với yêu cầu số [$requestId] đã gửi đến số điện thoại $contact của Quý khách.",
                    color = Color.Gray,
                    textSize = 14.sp,
                    fontCus = 0,
                    lineHeight = 22.sp,
                )
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    BaseOtpBoxInput(
                        value = otpInput.value,
                        onValueChange = {
                            otpInput.value = it
                        },
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Timer row
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        BaseText(
                            text = "Thời gian hiệu lực OTP còn ",
                            color = Color.Gray,
                            textSize = 14.sp,
                            fontCus = 0,
                        )
                        BaseText(
                            text = remainingTime,
                            color = Color.Blue,
                            textSize = 14.sp,
                            fontCus = 1, // Semi-bold
                        )
                    }
                }
            }
        }

        // "Chưa nhận được mã OTP?" row
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 24.dp, vertical = 16.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            BaseText(
                text = "Chưa nhận được mã OTP? ",
                color = Color.White,
                textSize = 14.sp,
                fontCus = 0,
            )
            BaseText(
                text = "Gửi lại",
                color = Color.White,
                textSize = 14.sp,
                fontCus = 1, // Semi-bold
                modifier = Modifier.safeClickable { onResendClick() },
            )
        }

        // Spacer để đẩy button xuống dưới cùng
        Spacer(modifier = Modifier.weight(1f))

        // Continue button
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 24.dp, vertical = 16.dp)
                .height(56.dp)
                .clip(RoundedCornerShape(10.dp))
                .background(
                    brush = Brush.horizontalGradient(
                        colors = listOf(
                            AppColors.gradientStart,
                            AppColors.gradientEnd,
                        ),
                    ),
                )
                .safeClickable(
                    timeWindow = 1000L,
                    onSafeClick = { onContinueClick(otpInput.value) },
                ),
            contentAlignment = Alignment.Center,
        ) {
            BaseText(
                text = "Tiếp tục",
                color = Color.White,
                textSize = 16.sp,
                fontCus = 1, // Semi-bold
            )
        }
    }
}
