package com.vietinbank.feature_login.ui.dialog

import android.os.Bundle
import android.os.Parcelable
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_ui.base.dialog.BaseDialog
import com.vietinbank.core_ui.components.ButtonSize
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationSelector
import com.vietinbank.core_ui.components.SelectorType
import com.vietinbank.core_ui.components.dialog.DialogLayout
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.feature_login.R
import kotlinx.parcelize.Parcelize

/**
 * Result data for user selection
 */
@Parcelize
data class Active2FAStep1Result(
    val typeOTP2FA: String,
) : Parcelable

class NewUIActive2FAStep1Dialog : BaseDialog<Active2FAStep1Result>() {
    companion object {
        private const val TAG = "NewUIActive2FAStep1Dialog"
        private const val ARG_TYPE_OTP2FA = "arg_type_OTP2FA"

        fun show(
            fragmentManager: FragmentManager,
            typeOTP2FA: String? = null,
        ) {
            (fragmentManager.findFragmentByTag(TAG) as? DialogFragment)
                ?.dismissAllowingStateLoss()
            fragmentManager.executePendingTransactions()

            val dialog = NewUIActive2FAStep1Dialog().apply {
                arguments = Bundle().apply {
                    putString(ARG_TYPE_OTP2FA, typeOTP2FA)
                }
            }

            dialog.show(fragmentManager, TAG)
        }
    }

    // Dialog configuration
    override val resultKey: String = "newui_active2fa_step1_result"
    override val layout: DialogLayout = DialogLayout.BottomSheet // Bottom sheet style
    override val requiresSecureFlag: Boolean = true // Banking security requirement

    @Composable
    override fun DialogContent(
        visible: Boolean,
        onDismissRequest: () -> Unit,
        onResult: (Active2FAStep1Result) -> Unit,
    ) {
        Active2FAStep1Content(
            onConfirm = { typeOTP2FA ->
                onResult(Active2FAStep1Result(typeOTP2FA))
            },
            onDismiss = onDismissRequest,
            selectedIndexOTP = arguments?.getString(ARG_TYPE_OTP2FA).toString(),
        )
    }
}

@Composable
fun Active2FAStep1Content(
    onConfirm: (String) -> Unit,
    onDismiss: () -> Unit,
    selectedIndexOTP: String = Tags.SMS_OTP,
) {
    var selectedIndex by remember { mutableStateOf(selectedIndexOTP) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = FoundationDesignSystem.Sizer.Padding.padding2),
    ) {
        Surface(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(FoundationDesignSystem.Sizer.Radius.radius32),
            color = FoundationDesignSystem.Colors.white,
            shadowElevation = FoundationDesignSystem.Effects.elevationLg,
        ) {
            Column {
                // Header - matching Figma exactly
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding24),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    // Title: "Thông báo" - Heading H3 (20sp SemiBold) per Figma
                    FoundationText(
                        text = stringResource(R.string.active2fa_step1_title),
                        style = FoundationDesignSystem.Typography.headingH3,
                        color = FoundationDesignSystem.Colors.characterHighlighted,
                        textAlign = TextAlign.Center,
                    )

                    Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap24))

                    HorizontalDivider(
                        color = FoundationDesignSystem.Colors.strokeDivider,
                        thickness = FoundationDesignSystem.Sizer.Stroke.stroke1,
                    )
                }

                // Content
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = FoundationDesignSystem.Sizer.Padding.padding24),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_active_2fa),
                        contentDescription = null,
                        modifier = Modifier.size(FoundationDesignSystem.Sizer.Icon.icon96),
                    )

                    Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap24))

                    // Message - Body B2
                    FoundationText(
                        text = stringResource(R.string.active2fa_step1_content),
                        style = FoundationDesignSystem.Typography.bodyB2,
                        color = FoundationDesignSystem.Colors.textGuideLine,
                        textAlign = TextAlign.Center,
                    )

                    Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap24))
                }

                HorizontalDivider(
                    color = FoundationDesignSystem.Colors.strokeDivider,
                    thickness = FoundationDesignSystem.Sizer.Stroke.stroke1,
                )

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            horizontal = FoundationDesignSystem.Sizer.Padding.padding24,
                            vertical = FoundationDesignSystem.Sizer.Padding.padding24,
                        ),
                    horizontalArrangement = Arrangement.spacedBy(FoundationDesignSystem.Sizer.Gap.gap12),
                ) {
                    FoundationSelector(
                        boxType = SelectorType.Radio,
                        title = stringResource(R.string.active2fa_step1_content_sms),
                        isSelected = selectedIndex == Tags.SMS_OTP,
                        onClick = { selectedIndex = Tags.SMS_OTP },
                        modifier = Modifier.weight(1f)
                            .padding(start = FoundationDesignSystem.Sizer.Padding.padding32),
                    )

                    FoundationSelector(
                        boxType = SelectorType.Radio,
                        title = stringResource(R.string.active2fa_step1_content_email),
                        isSelected = selectedIndex == Tags.EMAIL_OTP,
                        onClick = { selectedIndex = Tags.EMAIL_OTP },
                        modifier = Modifier.weight(1f)
                            .padding(start = FoundationDesignSystem.Sizer.Padding.padding32),
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap16))

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = FoundationDesignSystem.Sizer.Padding.padding16),
            horizontalArrangement = Arrangement.spacedBy(FoundationDesignSystem.Sizer.Gap.gap8),
        ) {
            FoundationButton(
                text = stringResource(com.vietinbank.core_ui.R.string.biometric_verify_button_back),
                onClick = {
                    onDismiss()
                },
                modifier = Modifier.weight(1f),
                isLightButton = false,
                size = ButtonSize.Large,
            )

            FoundationButton(
                text = stringResource(com.vietinbank.core_ui.R.string.btn_continue),
                onClick = {
                    onConfirm(selectedIndex)
                },
                modifier = Modifier.weight(1f),
                isLightButton = true,
                enabled = true,
                size = ButtonSize.Large,
            )
        }

        Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap24))
    }
}

@Preview(showBackground = true)
@Composable
fun Active2FAStep1Content() {
    AppTheme {
        Active2FAStep1Content(
            onConfirm = {},
            onDismiss = {},
        )
    }
}