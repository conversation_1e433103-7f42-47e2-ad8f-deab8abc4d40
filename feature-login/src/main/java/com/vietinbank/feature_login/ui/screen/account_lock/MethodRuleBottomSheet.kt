package com.vietinbank.feature_login.ui.screen.account_lock

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.vietinbank.core_domain.models.login.MethodDomain
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.text.foundation.FoundationIconText
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.models.IconConfig
import com.vietinbank.core_ui.models.IconPosition
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_login.R
import com.vietinbank.feature_login.ui.viewmodel.AccountLockViewModel.Companion.SMS

@Composable
fun MethodRuleBottomSheet(
    visible: Boolean,
    lstMethod: List<MethodDomain>,
    selector: MethodDomain?,
    onSelect: (MethodDomain) -> Unit,
    onDismiss: () -> Unit,
) {
    BaseBottomSheet<MethodDomain>(
        visible = visible,
        onDismissRequest = onDismiss,
        onResult = { chosen -> onSelect(chosen) },
        allowTouchDismiss = true,
        containerColor = FoundationDesignSystem.Colors.white,
        shape = RoundedCornerShape(FoundationDesignSystem.Sizer.Radius.radius32),
    ) { onResult ->
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding12),
        ) {
            FoundationText(
                text = stringResource(com.vietinbank.core_ui.R.string.lock_confirm_method_pass),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding8),
                style = FoundationDesignSystem.Typography.headingH3,
                textAlign = TextAlign.Center,
                color = FoundationDesignSystem.Colors.characterHighlighted,
            )

            FoundationDivider(modifier = Modifier.padding(vertical = FoundationDesignSystem.Sizer.Gap.gap8))

            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = FoundationDesignSystem.Sizer.Padding.padding16),
            ) {
                itemsIndexed(lstMethod) { index, method ->
                    MethodLockRuleItem(
                        modifier = Modifier
                            .fillMaxWidth()
                            .safeClickable { onResult(method) },
                        item = method,
                        isSelected = method == selector,
                    )
                    if (index < lstMethod.lastIndex) {
                        FoundationDivider()
                    }
                }
            }

            Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap24))
        }
    }
}

@Composable
fun MethodLockRuleItem(
    modifier: Modifier,
    item: MethodDomain? = null,
    isSelected: Boolean = false,
) {
    Column(modifier = modifier) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Gap.gap12),
        ) {
            FoundationIconText(
                text = item?.code ?: "",
                icons = if (item?.code == SMS) {
                    mapOf(
                        IconPosition.LEFT to IconConfig(
                            icon = R.drawable.ic_lock_sms,
                            size = FoundationDesignSystem.Sizer.Icon.icon40,
                        ),
                    )
                } else {
                    mapOf(
                        IconPosition.LEFT to IconConfig(
                            icon = R.drawable.ic_lock_email,
                            size = FoundationDesignSystem.Sizer.Icon.icon40,
                        ),
                    )
                },
            )
            Spacer(modifier = Modifier.width(FoundationDesignSystem.Sizer.Gap.gap16))
        }
    }
}
