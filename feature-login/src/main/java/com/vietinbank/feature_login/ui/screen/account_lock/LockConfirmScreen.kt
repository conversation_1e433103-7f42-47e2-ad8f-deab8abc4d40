package com.vietinbank.feature_login.ui.screen.account_lock

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_domain.models.login.LockActions
import com.vietinbank.core_ui.base.compose.BaseAppBar
import com.vietinbank.core_ui.base.compose.BaseButton
import com.vietinbank.core_ui.base.compose.InfoHorizontalView
import com.vietinbank.core_ui.base.compose.InfoVerticalView
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_login.ui.viewmodel.AccountLockViewModel
import com.vietinbank.feature_login.ui.viewmodel.LockUIState

@Composable
fun LockConfirmScreen(
    viewModel: AccountLockViewModel,
    actions: LockActions,
) {
    val uiState by viewModel.lockUIState.collectAsState()
    val methodResetPassword by viewModel.methodResetPassword.collectAsState()
    val approveUser by viewModel.approveUser.collectAsState()
    val ruleBottomEvent by viewModel.ruleBottomEvent.collectAsState()

    RuleLockBottomSheet(
        ruleEvent = ruleBottomEvent,
        onToggleRule = { rule ->
            actions.onToggleRuleClick.invoke(rule)
        },
        onDismiss = {
            actions.onRuleClick.invoke(AccountLockViewModel.NONE)
        },
    )

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(AppColors.itemBackground),
    ) {
        BaseAppBar(
            "Xác nhận",
            onBackClick = actions.onBackClick,
        )

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp, 12.dp)
                .weight(1f)
                .verticalScroll(rememberScrollState()),
        ) {
            when {
                uiState is LockUIState.InitState -> {
                    Text(
                        text = "Thông tin khách hàng",
                        style = getComposeFont(4, 16.sp, Color.White),
                    )

                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 12.dp)
                            .background(Color.White, RoundedCornerShape(5.dp))
                            .padding(horizontal = 16.dp, vertical = 12.dp),
                    ) {
                        InfoHorizontalView(
                            title = "Mã số doanh nghiệp",
                            value = (uiState as LockUIState.InitState).accountDomain.cifNo ?: "",
                        )

                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Tên truy cập",
                            value = (uiState as LockUIState.InitState).accountDomain.username ?: "",
                        )

                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Số CMND/ Hộ chiếu/ CCCD",
                            value = (uiState as LockUIState.InitState).accountDomain.idCard ?: "",
                        )

                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Số tài khoản thanh toán của DN",
                            value = (uiState as LockUIState.InitState).accountDomain.accountPayment
                                ?: "",
                        )

                        InfoVerticalView(
                            modifier = Modifier.padding(top = 32.dp),
                            title = "Phương thức cấp lại mật khẩu",
                            value = methodResetPassword?.value ?: "",
                        ) {
                            actions.onRuleClick.invoke(AccountLockViewModel.RULE_METHOD)
                        }

                        methodResetPassword?.let {
                            Text(
                                modifier = Modifier.padding(top = 4.dp),
                                text = it.description ?: "",
                                style = getComposeFont(
                                    4,
                                    12.sp,
                                    AppColors.contentSecondary,
                                ),
                            )
                        }

                        if (true != (uiState as LockUIState.InitState).accountDomain.isFace) {
                            InfoVerticalView(
                                title = "Danh sách người phê duyệt",
                                value = approveUser?.username ?: "",
                                modifier = Modifier.padding(top = 12.dp),
                            ) {
                                actions.onRuleClick.invoke(AccountLockViewModel.RULE_APPROVE)
                            }
                        }
                    }
                }

                else -> {
                }
            }
        }

        BaseButton(
            Modifier
                .padding(16.dp)
                .safeClickable {
                    actions.onNextClick.invoke()
                },
            "Chuyển kiểm soát",
        )
    }
}