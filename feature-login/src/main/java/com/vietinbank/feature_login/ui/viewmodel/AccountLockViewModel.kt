package com.vietinbank.feature_login.ui.viewmodel

import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItem
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItemType
import com.vietinbank.core_domain.models.login.AccountLockDomain
import com.vietinbank.core_domain.models.login.AccountLockFaceParams
import com.vietinbank.core_domain.models.login.AccountLockOTPDomain
import com.vietinbank.core_domain.models.login.AccountLockOTPParams
import com.vietinbank.core_domain.models.login.AccountLockParams
import com.vietinbank.core_domain.models.login.MethodDomain
import com.vietinbank.core_domain.models.maker.ApproverDomains
import com.vietinbank.core_domain.usecase.login.LoginUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

@HiltViewModel
class AccountLockViewModel @Inject constructor(
    private val loginUseCase: LoginUseCase,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val sessionManager: ISessionManager,
    override val ottSetupService: IOttSetupService,
) : BaseViewModel() {
    companion object {
        const val NONE = ""
        const val RULE_APPROVE = "RULE_APPROVE"
        const val RULE_METHOD = "RULE_METHOD"
        const val SMS = "SMS"
        const val EMAIL = "EMAIL"
    }

    private val _typeReset = MutableStateFlow<String?>(null)
    private val _typeScreen = MutableStateFlow<String?>(null)
    val typeScreen = _typeScreen.asStateFlow()
    fun setTitleType(cif: String?) {
        _typeReset.value = cif
        _typeScreen.value = when (_typeReset.value) {
            Tags.TYPE_GROUP_RESET_PASSWORD -> "Cấp lại mật khẩu"
            Tags.TYPE_GROUP_UNLOCK_USER -> "Mở khóa người dùng"
            else -> ""
        }
    }

    private val _companyCif = MutableStateFlow<String?>(null)
    val companyCif = _companyCif.asStateFlow()
    fun setCompanyCif(cif: String?) {
        _companyCif.value = cif?.trim()
    }

    private val _companyUser = MutableStateFlow<String?>(null)
    val companyUser = _companyUser.asStateFlow()
    fun setCompanyUser(user: String?) {
        _companyUser.value = user?.trim()
    }

    private val _companyId = MutableStateFlow<String?>(null)
    val companyId = _companyId.asStateFlow()
    fun setCompanyId(id: String?) {
        _companyId.value = id?.trim()
    }

    private val _companyAccount = MutableStateFlow<String?>(null)
    val companyAccount = _companyAccount.asStateFlow()
    fun setCompanyAccount(accountNumber: String?) {
        _companyAccount.value = accountNumber?.trim()
    }

    private val _ruleBottomEvent = MutableStateFlow<RuleBottomEvent>(RuleBottomEvent.None)
    val ruleBottomEvent = _ruleBottomEvent.asStateFlow()
    fun setRuleBottomEvent(ruleType: String) {
        when {
            _lockUIState.value is LockUIState.InitState -> {
                _ruleBottomEvent.value = when (ruleType) {
                    RULE_APPROVE -> {
                        RuleBottomEvent.Approved(lstChecker, _approveUser.value)
                    }

                    RULE_METHOD -> {
                        RuleBottomEvent.Method(lstMethod, _methodResetPassword.value)
                    }

                    else -> {
                        RuleBottomEvent.None
                    }
                }
            }

            else -> {
            }
        }
    }

    // phuong thuc cap lai mk
    private val _methodResetPassword = MutableStateFlow<MethodDomain?>(null)
    val methodResetPassword = _methodResetPassword.asStateFlow()
    fun setMethodProvide(rule: MethodDomain?) {
        _methodResetPassword.value = rule
    }

    // nguoi phe duyet
    private val _approveUser = MutableStateFlow<ApproverDomains?>(null)
    val approveUser = _approveUser.asStateFlow()
    fun setApproveUser(rule: ApproverDomains?) {
        _approveUser.value = rule
    }

    fun setRuleSelector(rule: Any?) {
        when (rule) {
            is ApproverDomains -> {
                setApproveUser(rule)
            }

            is MethodDomain -> {
                setMethodProvide(rule)
            }

            else -> {
            }
        }
        // an bottom sheet
        setRuleBottomEvent(NONE)
    }

    fun validateInputField(initScreen: Boolean = true): String? {
        val isFace = _lockUIState.value is LockUIState.InitState &&
            (_lockUIState.value as LockUIState.InitState).accountDomain.isFace == true
        return if (initScreen && (
                _companyCif.value.isNullOrEmpty() ||
                    _companyUser.value.isNullOrEmpty() ||
                    _companyId.value.isNullOrEmpty() ||
                    _companyAccount.value.isNullOrEmpty()
                )
        ) {
            "Thông tin khách hàng không được để trống. Vui lòng kiểm tra lại."
        } else if (!initScreen && (
                null == _methodResetPassword.value ||
                    (null == _approveUser.value && !isFace)
                )
        ) {
            "Thông tin khách hàng không được để trống. Vui lòng kiểm tra lại."
        } else {
            null
        }
    }

    fun validateSelectorField(ruleType: String?): String? = when {
        ruleType == RULE_METHOD && lstMethod.isEmpty() -> "Không có thông tin số điện thoại và email được đăng ký với ngân hàng. Quý khách vui lòng liên hệ với CN/PGD VietinBank để được hỗ trợ"
        ruleType == RULE_APPROVE && lstChecker.isEmpty() -> "Chưa có người dùng phê duyệt được gán quyền người dùng quản trị. Quý khách vui lòng liên hệ với CN/PGD VietinBank gần nhất để được hỗ trợ"
        else -> null
    }

    private var lockDomain: AccountLockDomain? = null
    private val _lockDomainState = MutableSharedFlow<AccountLockDomain>()
    val lockDomainState = _lockDomainState.asSharedFlow()
    fun checkInformation() = launchJob {
        val params = AccountLockParams(
            accountPayment = _companyAccount.value,
            cifNo = _companyCif.value,
            idCard = _companyId.value,
            type = _typeReset.value?.lowercase(),
            username = _companyUser.value,
        )
        val result = loginUseCase.checkInformation(params)
        handleResource(result) {
            it.apply {
                username = _companyUser.value
                cifNo = _companyCif.value
                idCard = _companyId.value
                accountPayment = _companyAccount.value
                typeReset = _typeReset.value?.lowercase()
            }
            lockDomain = it
            _lockDomainState.emit(it)
        }
    }

    private val _otpLockDomain = MutableSharedFlow<AccountLockOTPDomain>()
    val otpLockDomain = _otpLockDomain.asSharedFlow()
    fun sendOTPLockAccount() = launchJob {
        when {
            _lockUIState.value is LockUIState.InitState -> {
                val params = AccountLockOTPParams(
                    type = when (_methodResetPassword.value?.code) {
                        SMS -> Tags.TYPE_FLAG_NO
                        EMAIL -> Tags.TYPE_FLAG_YES
                        else -> ""
                    },
                    typeReset = (_lockUIState.value as LockUIState.InitState).accountDomain.typeReset,
                    cifNo = (_lockUIState.value as LockUIState.InitState).accountDomain.cifNo,
                    username = (_lockUIState.value as LockUIState.InitState).accountDomain.username,
                    accountPayment = (_lockUIState.value as LockUIState.InitState).accountDomain.accountPayment,
                    idCard = (_lockUIState.value as LockUIState.InitState).accountDomain.idCard,
                    approver = _approveUser.value?.username,
                    roleChecker = _approveUser.value?.approverlevel,
                    token = when (_methodResetPassword.value?.code) {
                        SMS -> (_lockUIState.value as LockUIState.InitState).accountDomain.phoneNumber
                        EMAIL -> (_lockUIState.value as LockUIState.InitState).accountDomain.email
                        else -> ""
                    },
                )
                val result = loginUseCase.sendOTPLockAccount(params)
                handleResource(result) {
                    _otpLockDomain.emit(it)
                }
            }

            else -> {
            }
        }
    }

    private val lstChecker = mutableListOf<ApproverDomains>()
    private val lstMethod = mutableListOf<MethodDomain>()
    private val _lockUIState = MutableStateFlow<LockUIState>(LockUIState.NONE)
    val lockUIState = _lockUIState.asStateFlow()
    fun setLockUIState(state: LockUIState) {
        _lockUIState.value = state
        // man confirm -> state Init
        if (state is LockUIState.InitState) {
            lstChecker.clear()
            lstMethod.clear()
            // checker
            lstChecker.addAll(state.accountDomain.listChecker ?: emptyList())
            // them phuong thuc thu cong
            if (!state.accountDomain.phoneNumber.isNullOrEmpty()) {
                lstMethod.add(
                    MethodDomain(
                        SMS,
                        "Cấp qua SMS",
                        "Quý khách vui lòng kiểm tra số điện thoại đã đăng ký với ngân hàng ${state.accountDomain.phoneNumber}",
                    ),
                )
            }
            if (!state.accountDomain.email.isNullOrEmpty()) {
                lstMethod.add(
                    MethodDomain(
                        EMAIL,
                        "Cấp qua Email",
                        "Quý khách vui lòng kiểm tra Email đã đăng ký với ngân hàng ${state.accountDomain.email}",
                    ),
                )
            }
        }
    }

    fun getResultLockAccount(domain: AccountLockOTPDomain): Pair<String, List<ConfirmItem>>? {
        if (_lockUIState.value is LockUIState.InitState) {
            (_lockUIState.value as LockUIState.InitState).accountDomain.let {
                val description = when (it.typeReset) {
                    Tags.TYPE_GROUP_UNLOCK_USER.lowercase() -> {
                        "Yêu cầu mở khóa người dùng được chuyển kiểm soát thành công"
                    }

                    else -> {
                        "Yêu cầu cấp lại mật khẩu đã được chuyển kiểm soát thành công"
                    }
                }

                val lstDisplay = mutableListOf<ConfirmItem>(
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        title = "Mã số doanh nghiệp",
                        value = it.cifNo ?: "",
                    ),
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        title = "Tên truy cập",
                        value = it.username ?: "",
                    ),
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        title = "Số CMND/ Hộ chiếu/ CCCD",
                        value = it.idCard ?: "",
                    ),
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        title = "Số tài khoảng thanh toán của DN",
                        value = it.accountPayment ?: "",
                    ),
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        title = "Người phê duyệt",
                        value = _approveUser.value?.username ?: "",
                    ),
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        title = "Phương thức cấp lại mật khẩu",
                        value = _methodResetPassword.value?.value ?: "",
                    ),
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        title = "Thời gian giao dịch",
                        value = domain.createdDate ?: "",
                    ),
                )
                return Pair(description, lstDisplay)
            }
        }
        return null
    }

    private val _faceLockAccount = MutableSharedFlow<AccountLockOTPDomain>()
    val faceLockAccount = _faceLockAccount.asSharedFlow()
    fun compareFaceLockAccount(image: String?) = launchJob {
        val params = AccountLockFaceParams(
            cifNo = lockDomain?.cifNo,
            username = lockDomain?.username,
            image = image,
        )
        val result = loginUseCase.compareFaceLockAccount(params)
        handleResource(result) {
            lockDomain?.isFace = true
            _faceLockAccount.emit(it)
        }
    }

    fun getBundleLockAccount() =
        lockDomain?.let { Utils.g().provideGson().toJson(LockUIState.InitState(it)) }

    fun validateOTPLockAccount(): String? {
        return if (_lockUIState.value is LockUIState.InitState && (_lockUIState.value as LockUIState.InitState).accountDomain.isFace == true) {
            when ((_lockUIState.value as LockUIState.InitState).accountDomain.typeReset) {
                Tags.TYPE_GROUP_UNLOCK_USER.lowercase() -> {
                    "Quý khách đã được Mở khoá thành công. Vui lòng kiểm tra thông tin mật khẩu mới tại Email/SMS đã đăng ký với Ngân hàng."
                }

                else -> {
                    "Quý khách đã được Cấp lại mật khẩu thành công. Vui lòng kiểm tra thông tin mật khẩu mới tại Email/SMS đã đăng ký với Ngân hàng."
                }
            }
        } else {
            null
        }
    }
}

sealed class RuleBottomEvent {
    data object None : RuleBottomEvent()
    data class Method(val lstMethod: List<MethodDomain>?, val selector: MethodDomain?) :
        RuleBottomEvent()

    data class Approved(val lstApprover: List<ApproverDomains>?, val approved: ApproverDomains?) :
        RuleBottomEvent()
}

sealed class LockUIState {
    data object NONE : LockUIState()
    data class ResultState(val result: Pair<String, List<ConfirmItem>>) : LockUIState()
    data class InitState(val accountDomain: AccountLockDomain) : LockUIState()
}
