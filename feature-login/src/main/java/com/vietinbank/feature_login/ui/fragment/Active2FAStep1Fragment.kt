package com.vietinbank.feature_login.ui.fragment

import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.compose.runtime.Composable
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_login.ui.screen.Active2FAStep1
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class Active2FAStep1Fragment : BaseFragment<Active2FAViewModel>() {
    override val viewModel: Active2FAViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        getExtrasData()
        initObserver()
    }

    private fun initObserver() {
        viewModel.apply {
            genOTPResponse.observe(viewLifecycleOwner) { data ->
                when (data) {
                    is Resource.Success -> {
                        Toast.makeText(
                            requireContext(),
                            data.data.status?.message.toString(),
                            Toast.LENGTH_LONG,
                        ).show()
                        printLog("viewModel.methodSelected?.value : ${viewModel.methodSelected?.value}") // null
                        val bundle = Bundle().apply {
                            putString(Tags.ADDFIELD1_BUNDLE, viewModel.addField1)
                            putString(Tags.USERNAME_BUNDLE, viewModel.currentUsername)
                            putString(Tags.CIFNO_BUNDLE, viewModel.currentCifNo)
                            putString(Tags.TYPE_BUNDLE, viewModel.methodSelected?.value)
                            putString(Tags.EFAST_ID_BUNDLE, data.data.efastId)
                        }
                        appNavigator.goToActive2FAStep2Fragment(bundle)
                    }

                    else -> {}
                }
            }
        }
    }

    private fun getExtrasData() {
        arguments?.getString(Tags.ADDFIELD1_BUNDLE)?.let { data ->
            viewModel.addField1 = data
        }
        arguments?.getString(Tags.USERNAME_BUNDLE)?.let { data ->
            viewModel.currentUsername = data
        }
        arguments?.getString(Tags.CIFNO_BUNDLE)?.let { data ->
            viewModel.currentCifNo = data
        }
    }

    @Composable
    override fun ComposeScreen() {
        AppTheme {
            Active2FAStep1(
                onMethodSelected = { method ->
                    // Xử lý khi người dùng chọn phương thức
                    viewModel.methodSelected = method
                    printLog("Selected method: ${viewModel.methodSelected?.name}, value: ${viewModel.methodSelected?.value}")
                },
                onContinueClick = {
                    // Xử lý khi người dùng ấn nút Tiếp tục
                    viewModel.genOTP(viewModel.methodSelected?.value.toString())
                },
            )
        }
    }
}