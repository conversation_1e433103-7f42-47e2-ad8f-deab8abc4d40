package com.vietinbank.feature_login.ui.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ui.base.compose.BaseEditText
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_login.R
import com.vietinbank.feature_login.ui.components.TopBarRow

/**
 * Màn hình Đổi mật khẩu
 * Created by vandz on 3/3/25.
 */

@Preview
@Composable
fun ActiveScreen(
    currentUsername: String = "",
    onBackClick: () -> Unit = {},
    onNotifyClick: () -> Unit = {},
    onShareClick: () -> Unit = {},
    onChangePasswordClick: (String, String, String) -> Unit = { _, _, _ -> },
) {
    // States cho các trường nhập liệu mật khẩu
    var oldPassword by remember { mutableStateOf("") }
    var newPassword by remember { mutableStateOf("") }
    var confirmNewPassword by remember { mutableStateOf("") }

    // States cho việc hiển thị/ẩn mật khẩu
    var oldPasswordVisible by remember { mutableStateOf(false) }
    var newPasswordVisible by remember { mutableStateOf(false) }
    var confirmNewPasswordVisible by remember { mutableStateOf(false) }

    // Lấy focus manager và keyboard controller để xử lý ẩn bàn phím
    val focusManager = LocalFocusManager.current
    val keyboardController = LocalSoftwareKeyboardController.current
    val interactionSource = remember { MutableInteractionSource() }

    // Hàm ẩn bàn phím khi click ra ngoài
    fun hideKeyboard() {
        focusManager.clearFocus()
        keyboardController?.hide()
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .imePadding()
            // Bắt sự kiện click ra ngoài để ẩn bàn phím
            .clickable(
                interactionSource = interactionSource,
                indication = null, // Không hiển thị hiệu ứng ripple
            ) {
                hideKeyboard()
            },
    ) {
        // Scroll column cho nội dung chính
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(horizontal = 16.dp),
        ) {
            // Top bar với logo và các icon
            TopBarRow(
                onNotifyClick = onNotifyClick,
                onShareClick = onShareClick,
            )

            Spacer(modifier = Modifier.height(80.dp))

            // Tiêu đề màn hình với nút back
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth(),
            ) {
                // Nút back
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .clip(RoundedCornerShape(10.dp))
                        .background(AppColors.itemBackground.copy(alpha = 0.1f))
                        .safeClickable { onBackClick() },
                    contentAlignment = Alignment.Center,
                ) {
                    Icon(
                        painter = painterResource(R.drawable.ic_back),
                        contentDescription = "Back",
                        tint = Color.White,
                        modifier = Modifier.size(24.dp),
                    )
                }

                Spacer(modifier = Modifier.padding(horizontal = 12.dp))

                // Tiêu đề "Đổi mật khẩu"
                BaseText(
                    text = "Đổi mật khẩu",
                    color = Color.White,
                    fontCus = 5, // Bold
                    textSize = 18.sp,
                )
            }

            Spacer(modifier = Modifier.height(32.dp))

            // Trường nhập mật khẩu cũ
            BaseEditText(
                value = oldPassword,
                onValueChange = { oldPassword = it },
                hint = "Mật khẩu cũ",
                leftDrawable = R.drawable.ic_lock,
                rightDrawable = if (oldPasswordVisible) R.drawable.ic_eye_open else R.drawable.ic_eye_close,
                onRightDrawableClick = { oldPasswordVisible = !oldPasswordVisible },
                isPassword = !oldPasswordVisible,
                keyboardType = KeyboardType.Password,
                backgroundColor = AppColors.inputBackground,
                borderColor = AppColors.borderColor,
                textColor = AppColors.textPrimary,
                hintColor = AppColors.textHint,
                borderRadius = 10.dp,
                borderWidth = 0.5.dp,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp),
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Trường nhập mật khẩu mới
            BaseEditText(
                value = newPassword,
                onValueChange = { newPassword = it },
                hint = "Mật khẩu mới",
                leftDrawable = R.drawable.ic_lock,
                rightDrawable = if (newPasswordVisible) R.drawable.ic_eye_open else R.drawable.ic_eye_close,
                onRightDrawableClick = { newPasswordVisible = !newPasswordVisible },
                isPassword = !newPasswordVisible,
                keyboardType = KeyboardType.Password,
                backgroundColor = AppColors.inputBackground,
                borderColor = AppColors.borderColor,
                textColor = AppColors.textPrimary,
                hintColor = AppColors.textHint,
                borderRadius = 10.dp,
                borderWidth = 0.5.dp,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp),
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Trường nhập lại mật khẩu mới
            BaseEditText(
                value = confirmNewPassword,
                onValueChange = { confirmNewPassword = it },
                hint = "Nhập lại mật khẩu mới",
                leftDrawable = R.drawable.ic_lock,
                rightDrawable = if (confirmNewPasswordVisible) R.drawable.ic_eye_open else R.drawable.ic_eye_close,
                onRightDrawableClick = { confirmNewPasswordVisible = !confirmNewPasswordVisible },
                isPassword = !confirmNewPasswordVisible,
                keyboardType = KeyboardType.Password,
                backgroundColor = AppColors.inputBackground,
                borderColor = AppColors.borderColor,
                textColor = AppColors.textPrimary,
                hintColor = AppColors.textHint,
                borderRadius = 10.dp,
                borderWidth = 0.5.dp,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp),
            )

            Spacer(modifier = Modifier.height(24.dp))

            // Nút Xác nhận
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp)
                    .clip(RoundedCornerShape(10.dp))
                    .background(Color(0xFF1A73E8)) // Màu xanh dương theo hình
                    .safeClickable {
                        onChangePasswordClick(oldPassword, newPassword, confirmNewPassword)
                    },
                contentAlignment = Alignment.Center,
            ) {
                BaseText(
                    text = "Xác nhận",
                    color = Color.White,
                    fontCus = 5, // Bold
                    textSize = 16.sp,
                )
            }

            Spacer(modifier = Modifier.height(32.dp))

            // Phần điều kiện mật khẩu
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .border(
                        width = 3.dp,
                        color = Color(0x33FFFFFF),
                        shape = RoundedCornerShape(size = 10.dp),
                    )
                    .background(
                        color = Color(0x40000000),
                        shape = RoundedCornerShape(size = 10.dp),
                    )
                    .padding(start = 12.dp, top = 12.dp, end = 12.dp, bottom = 12.dp),
            ) {
                Column {
                    BaseText(
                        text = "Mật khẩu mới phải thỏa mãn các điều kiện sau:",
                        color = Color.White,
                        fontCus = 0,
                        textSize = 14.sp,
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Liệt kê các điều kiện
                    Column(
                        verticalArrangement = Arrangement.spacedBy(12.dp),
                    ) {
                        PasswordConditionItem("Độ dài mật khẩu tối thiểu 8 ký tự và tối đa 50 ký tự")
                        PasswordConditionItem("Chỉ nhập các ký tự a-z, A-Z, 0-9, @#$%")
                        PasswordConditionItem("Mật khẩu phải chứa ít nhất một chữ số, một chữ thường, một chữ hoa và một ký tự đặc biệt")
                        PasswordConditionItem("Mật khẩu mới không được trùng với mật khẩu cũ")
                        PasswordConditionItem("Mật khẩu không trùng với tên đăng nhập hiện tại")
                    }
                }
            }

            Spacer(modifier = Modifier.height(32.dp))
        }
    }
}

// Component hiển thị điều kiện mật khẩu với dấu chấm tròn
@Composable
private fun PasswordConditionItem(condition: String) {
    Row(
        verticalAlignment = Alignment.Top,
        modifier = Modifier.fillMaxWidth(),
    ) {
        // Dấu chấm tròn
        Box(
            modifier = Modifier
                .padding(top = 6.dp)
                .size(6.dp)
                .background(Color.White, shape = RoundedCornerShape(3.dp)),
        )
        Spacer(modifier = Modifier.padding(horizontal = 8.dp))
        BaseText(
            text = condition,
            color = Color.White,
            fontCus = 0,
            textSize = 14.sp,
        )
    }
}