<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="40dp"
    android:height="40dp"
    android:viewportWidth="40"
    android:viewportHeight="40">
  <path
      android:pathData="M20,0.5C30.77,0.5 39.5,9.23 39.5,20C39.5,30.77 30.77,39.5 20,39.5C9.23,39.5 0.5,30.77 0.5,20C0.5,9.23 9.23,0.5 20,0.5Z"
      android:fillColor="#EDF4F8"/>
  <path
      android:strokeWidth="1"
      android:pathData="M20,0.5C30.77,0.5 39.5,9.23 39.5,20C39.5,30.77 30.77,39.5 20,39.5C9.23,39.5 0.5,30.77 0.5,20C0.5,9.23 9.23,0.5 20,0.5Z"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="0"
          android:startY="0"
          android:endX="42.629"
          android:endY="3.031"
          android:type="linear">
        <item android:offset="0" android:color="#00FFFFFF"/>
        <item android:offset="0.5" android:color="#7FFFFFFF"/>
        <item android:offset="1" android:color="#33FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M11,15C11,14.47 11.211,13.961 11.586,13.586C11.961,13.211 12.47,13 13,13H27C27.53,13 28.039,13.211 28.414,13.586C28.789,13.961 29,14.47 29,15M11,15V25C11,25.53 11.211,26.039 11.586,26.414C11.961,26.789 12.47,27 13,27H27C27.53,27 28.039,26.789 28.414,26.414C28.789,26.039 29,25.53 29,25V15M11,15L20,21L29,15"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#036091"
      android:strokeLineCap="round"/>
</vector>
