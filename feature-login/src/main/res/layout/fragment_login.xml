<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">


        <View
            android:id="@+id/header_toolbar_fixed_height"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp15" />

        <LinearLayout
            android:id="@+id/header_toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp40"
            android:contentInsetStart="0dp"
            android:contentInsetLeft="0dp"
            android:contentInsetEnd="0dp"
            android:contentInsetRight="0dp"
            android:elevation="@dimen/dp4"
            android:gravity="center"
            android:minHeight="?attr/actionBarSize"
            android:orientation="horizontal"
            android:translationZ="10dp"
            app:contentInsetEnd="0dp"
            app:contentInsetLeft="0dp"
            app:contentInsetRight="0dp"
            app:contentInsetStart="0dp"
            app:layout_collapseMode="pin"
            app:showAsAction="always">

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/title_left"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center|start"
                android:padding="@dimen/dp14" />

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/title_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="0.1"
                android:gravity="center|start"
                android:textColor="@android:color/white"
                android:textSize="@dimen/sp22"
                android:textStyle="normal"
                android:visibility="visible" />

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/title_right"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center|start"
                android:padding="@dimen/dp14"
                android:visibility="visible" />

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/imv_ott"
                android:layout_width="@dimen/dp24"
                android:layout_height="@dimen/dp24"
                android:layout_gravity="center|start"
                android:layout_marginEnd="@dimen/dp16"
                android:visibility="visible" />
        </LinearLayout>

        <com.vietinbank.core_ui.base.views.BaseTextView
            android:id="@+id/logo"
            android:text="VietinBank eFast"
            android:textSize="@dimen/sp30"
            android:textColor="@color/white"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp30" />

        <LinearLayout
            android:id="@+id/linLoginForm"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp41"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/linWelcome"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="@dimen/dp24"
                android:layout_marginRight="@dimen/dp24"
                android:orientation="horizontal"
                android:visibility="gone">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/imgAvatar"
                    android:layout_width="@dimen/dp47"
                    android:layout_height="@dimen/dp47"
                    app:srcCompat="@drawable/ricardo" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="@dimen/dp15"
                    android:layout_marginRight="@dimen/dp15"
                    android:orientation="vertical">

                    <com.vietinbank.core_ui.base.views.BaseTextView
                        android:id="@+id/tvHelloText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Xin chào"
                        android:textColor="#c7e7f1"
                        android:textSize="@dimen/sp15"
                        android:textStyle="normal"
                        app:fontCus="medium" />

                    <com.vietinbank.core_ui.base.views.BaseTextView
                        android:id="@+id/tvFullName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp2"
                        android:textColor="#ffffff"
                        android:textSize="@dimen/sp16"
                        app:fontCus="regular_bold" />

                </LinearLayout>


            </LinearLayout>

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tvLogout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp24"
                android:layout_marginRight="@dimen/dp24"
                android:drawablePadding="@dimen/dp12"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/dp5"
                android:paddingBottom="@dimen/dp3"
                android:text="@string/login_on_another_account"
                android:textColor="#81defe"
                android:textSize="@dimen/sp12"
                android:textStyle="normal"
                android:visibility="gone"
                app:fontCus="medium" />


            <com.vietinbank.core_ui.base.views.BaseEditText
                android:id="@+id/etUsername"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp24"
                android:layout_marginRight="@dimen/dp24"
                android:background="@drawable/border_box_radius"
                android:drawablePadding="@dimen/dp12"
                android:gravity="center_vertical"
                android:hint="@string/username"
                android:longClickable="false"
                android:padding="@dimen/dp14"
                android:textColor="@color/color_text_default"
                android:textColorHint="#4a4a4a"
                android:textSize="@dimen/sp16"
                app:left="@drawable/ic_dangnhap_user" />

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp24"
                android:layout_marginTop="@dimen/dp16"
                android:layout_marginRight="@dimen/dp24">

                <com.vietinbank.core_ui.base.views.BaseEditText
                    android:id="@+id/etPass"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/border_box_radius"
                    android:drawablePadding="@dimen/dp12"
                    android:gravity="center_vertical"
                    android:hint="@string/password"
                    android:inputType="textPassword"
                    android:longClickable="false"
                    android:padding="@dimen/dp14"
                    android:paddingEnd="@dimen/dp48"
                    android:textColor="@color/color_text_default"
                    android:textColorHint="#4a4a4a"
                    android:textSize="@dimen/sp16"
                    android:textStyle="normal"
                    app:fontCus="regular"
                    app:left="@drawable/ic_dangnhap_khoa" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/imgEye"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end|center_vertical"
                    android:padding="@dimen/dp12"
                    app:srcCompat="@drawable/eye_selector" />
            </FrameLayout>


            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp24"
                android:layout_marginTop="@dimen/dp24"
                android:layout_marginRight="@dimen/dp24">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <com.vietinbank.core_ui.base.views.BaseTextView
                        android:id="@+id/submit"
                        style="@style/submit"
                        android:text="@string/login" />
                </LinearLayout>

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/imgFinger"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center|end"
                    android:adjustViewBounds="true"
                    android:paddingRight="@dimen/dp1"
                    android:visibility="gone"
                    app:srcCompat="@drawable/ic_van_tay" />

            </FrameLayout>

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/etSyncKeyPass"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp24"
                android:layout_marginTop="@dimen/dp30"
                android:layout_marginRight="@dimen/dp24"
                android:gravity="center_vertical"
                android:paddingBottom="@dimen/dp3"
                android:text="Đồng bộ Keypass"
                android:textColor="#81defe"
                android:textSize="@dimen/sp14"
                android:textStyle="normal"
                app:fontCus="medium" />

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/etGetSoftOtpCode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp24"
                android:layout_marginTop="@dimen/dp17"
                android:layout_marginRight="@dimen/dp24"
                android:gravity="center_vertical"
                android:paddingBottom="@dimen/dp3"
                android:text="Lấy mã Soft OTP"
                android:textColor="#81defe"
                android:textSize="@dimen/sp14"
                android:textStyle="normal"
                android:visibility="visible"
                app:fontCus="medium" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_gravity="bottom"
            android:layout_weight="1"
            android:gravity="bottom|center_horizontal"
            android:orientation="vertical"
            android:padding="@dimen/dp10">

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tvBottomText1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/dp20"
                android:letterSpacing="0.03"
                android:text="KHÁCH HÀNG"
                android:textAllCaps="true"
                android:textColor="#8fd1e9"
                android:textSize="@dimen/sp15"
                app:fontCus="heavy_italic" />

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tvBottomText2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp20"
                android:letterSpacing="0.03"
                android:text="DOANH NGHIỆP"
                android:textAllCaps="true"
                android:textColor="#8fd1e9"
                android:textSize="@dimen/sp15"
                app:fontCus="heavy_italic" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/layoutMarketSupport"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:layout_marginBottom="@dimen/dp40"
            android:gravity="bottom|center_horizontal"
            android:orientation="horizontal"
            android:padding="@dimen/dp10"
            android:visibility="visible">

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tvMarket"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/dp14"
                android:drawablePadding="@dimen/dp5"
                android:text="Thị trường"
                android:textColor="@color/white"
                android:textSize="@dimen/sp14"
                app:fontCus="medium" />

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tvSupport"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp14"
                android:drawablePadding="@dimen/dp5"
                android:text="Hỗ trợ"
                android:textColor="@color/white"
                android:textSize="@dimen/sp14"
                app:fontCus="medium" />
        </LinearLayout>

    </LinearLayout>


</RelativeLayout>