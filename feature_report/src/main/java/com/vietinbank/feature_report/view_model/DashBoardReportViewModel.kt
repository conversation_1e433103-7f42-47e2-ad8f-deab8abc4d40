package com.vietinbank.feature_report.view_model

import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.livedata.SingleLiveEvent
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_domain.models.checker.CountPendingDomain
import com.vietinbank.core_domain.models.checker.CountPendingParams
import com.vietinbank.core_domain.models.maker.NapasTransferParams
import com.vietinbank.core_domain.models.trace_payment.TransactionDomains
import com.vietinbank.core_domain.models.transfer_report.TransactionListDomains
import com.vietinbank.core_domain.models.transfer_report.TransactionListParams
import com.vietinbank.core_domain.usecase.account.ReportTransferUseCase
import com.vietinbank.core_domain.usecase.home.HomeUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.feature_report.adapter.DashBoardReportAdapter
import com.vietinbank.feature_report.adapter.ListRejectTransferDetailAdapter
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class DashBoardReportViewModel @Inject constructor(
    val homeUseCase: HomeUseCase,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    val reportTransferUseCase: ReportTransferUseCase,
    override val sessionManager: ISessionManager,
) : BaseViewModel() {
    val dashBoardAdapter = DashBoardReportAdapter()
    val listRejectTransferDetailAdapter = ListRejectTransferDetailAdapter()
    val transactionsList: MutableList<TransactionDomains> = mutableListOf<TransactionDomains>()

    private val _transactionListState = SingleLiveEvent<TransactionListDomains>()
    val transactionListState: SingleLiveEvent<TransactionListDomains> get() = _transactionListState

    private val _countPending = SingleLiveEvent<CountPendingDomain>()
    val countPending: SingleLiveEvent<CountPendingDomain> get() = _countPending

    fun getTransactionList(servicetype: String?) {
        launchJob(showLoading = true) {
            val params = TransactionListParams(
                orderByAmount = "-1",
                content = "",
                username = userProf.getUserName() ?: "",
                serviceType = servicetype ?: "",
                all = "N",
                pageNum = "0",
                orderByApproveDate = "-1",
                pageSize = "30",
                tranType = "",
                mtId = "",
            )
            val res = reportTransferUseCase.transactionList(params)
            handleResource(res) { data ->
                data.transactions?.toMutableList()?.let {
                    transactionsList.addAll(it)
                    listRejectTransferDetailAdapter.setData(it)
                }

                _transactionListState.postValue(data)
            }
        }
    }

    fun getTransactionItem(pos: Int): NapasTransferParams? {
        return transactionsList.getOrNull(pos)?.let {
            NapasTransferParams().apply {
                receiveBank = it.receiveBank
                receiveAcct = it.toAccountNo
                amount = it.amount
                currency = it.currency
                remark = it.tranDesc
                tranType = it.tranType
                remark = it.remark
            }
        }
    }

    fun countPending() {
        launchJob(showLoading = true) {
            val res = homeUseCase.countPending(
                CountPendingParams(
                    cifNo = userProf.getCifNo().toString(),
                    userName = userProf.getUserName().toString(),
                    role = userProf.getRoleId() ?: "",
                ),
            )
            handleResource(res) { data ->
                dashBoardAdapter.setData(
                    data.countTransactionList?.toMutableList() ?: mutableListOf(),
                )
                _countPending.postValue(data)
            }
        }
    }
}