package com.vietinbank.feature_report.fragment

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.RecyclerView
import androidx.viewbinding.ViewBinding
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.SwipeHelperLeft
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.checker.SubTranTypeListDomain
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_report.R
import com.vietinbank.feature_report.databinding.FragmentListRejectTransferDetailBinding
import com.vietinbank.feature_report.view_model.DashBoardReportViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class ListRejectTransferDetailFragment : BaseFragment<DashBoardReportViewModel>() {
    override val viewModel: DashBoardReportViewModel by viewModels()
    override val useCompose: Boolean = false

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override fun inflateViewBinding(inflater: LayoutInflater, container: ViewGroup?): ViewBinding {
        return FragmentListRejectTransferDetailBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initData()
        initListener()
    }

    private fun initView() {
        (binding as FragmentListRejectTransferDetailBinding).apply {
            arguments?.getString(Tags.TRANSACTION_BUNDLE)?.let { jsonData ->
                if (jsonData.isNotEmpty()) {
                    val originalTrans = Utils.g().provideGson()
                        .fromJson(jsonData, SubTranTypeListDomain::class.java)
                    openCustomToolbar.tvTitleToolbar.text = originalTrans.servicetypename
                    viewModel.getTransactionList(servicetype = originalTrans.servicetype)
                }
            }
            rcvListRejectDetail.adapter = viewModel.listRejectTransferDetailAdapter
        }
    }

    private fun initData() {
        (binding as FragmentListRejectTransferDetailBinding).apply {
        }
    }

    private fun initListener() {
        (binding as FragmentListRejectTransferDetailBinding).apply {
            openCustomToolbar.btnBack.setThrottleClickListener {
                onBackPressed()
            }
            requireContext().let {
                val swipeHelperLeft = object : SwipeHelperLeft(
                    it,
                    rcvListRejectDetail,
                ) {
                    override fun instantiateUnderlayButton(
                        viewHolder: RecyclerView.ViewHolder,
                        underlayButtons: MutableList<UnderlayButton>,
                    ) {
                        // Add your buttons here
                        underlayButtons.add(
                            UnderlayButton(
                                it,
                                "Sao chép",
                                R.drawable.ic_more,
                                Color.parseColor("#BBBBC3"),
                                object : UnderlayButtonClickListener {
                                    override fun onClick(pos: Int) {
                                        val bundle = Bundle()
                                        viewModel.getTransactionItem(pos)?.let { napasParam ->
                                            bundle.putString(
                                                Tags.COPY_TRANSFER_OBJECT_BUNDLE,
                                                Utils.g().provideGson().toJson(napasParam),
                                            )
                                        }
                                        appNavigator.goToViewPagerTransferFragment(bundle)
                                    }
                                },
                            ),
                        )
                    }
                }
            }
        }
    }
}