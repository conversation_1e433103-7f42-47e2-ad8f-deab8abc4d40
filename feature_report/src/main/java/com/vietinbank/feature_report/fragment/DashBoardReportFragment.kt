package com.vietinbank.feature_report.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.viewbinding.ViewBinding
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_report.databinding.FragmentDashboardBinding
import com.vietinbank.feature_report.view_model.DashBoardReportViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class DashBoardReportFragment : BaseFragment<DashBoardReportViewModel>() {
    override val viewModel: DashBoardReportViewModel by viewModels()
    override val useCompose: Boolean = false

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override fun inflateViewBinding(inflater: LayoutInflater, container: ViewGroup?): ViewBinding {
        return FragmentDashboardBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initData()
        initListener()
        viewModel.countPending()
    }

    private fun initView() {
        (binding as FragmentDashboardBinding).apply {
            rcvDashBoard.adapter = viewModel.dashBoardAdapter
        }
    }

    private fun initData() {
        (binding as FragmentDashboardBinding).apply {
            openCustomToolbar.tvTitleToolbar.text = "Quản lý giao dịch từ chối"
        }
    }

    private fun initListener() {
        (binding as FragmentDashboardBinding).apply {
            openCustomToolbar.btnBack.setThrottleClickListener {
                onBackPressed()
            }
            viewModel.dashBoardAdapter.onClickItem = {
                val bundle = Bundle()
                bundle.putString(
                    Tags.TRANSACTION_BUNDLE,
                    Utils.g().provideGson().toJson(it),
                )
                appNavigator.gotoListRejectTransferDetailFragment(bundle)
            }
        }
    }
}