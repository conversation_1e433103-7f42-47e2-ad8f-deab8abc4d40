package com.vietinbank.feature_report.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.trace_payment.TransactionDomains
import com.vietinbank.feature_report.databinding.ItemRejectTransferBinding

class ListRejectTransferDetailAdapter :
    RecyclerView.Adapter<ListRejectTransferDetailAdapter.Holder>() {
    private var dataSet: MutableList<TransactionDomains> = mutableListOf()
    var context: Context? = null

    @SuppressLint("NotifyDataSetChanged")
    var onClickItem: ((TransactionDomains) -> Unit)? = null

    @SuppressLint("NotifyDataSetChanged")
    fun setData(data: MutableList<TransactionDomains>) {
        dataSet.clear()
        dataSet.addAll(data)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): Holder {
        val binding = ItemRejectTransferBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false,
        )
        return Holder(binding)
    }

    override fun onBindViewHolder(holder: Holder, position: Int) {
        val item = dataSet[position]
        with(holder.binding) {
            tvTransferTypeTitle.text = item.tranTypeName.orEmpty()
            tvAmount.text = Utils.g().getDotMoneyHasCcy(item.amount.orEmpty(), item.currency.orEmpty())
            contentTransferType.text = item.tranTypeName.orEmpty()
            contentRecieveAccount.text = "${item.toAccountNo.orEmpty()}\n${item.receiveName.orEmpty()}"
            contentMtId.text = item.mtId.orEmpty()
            contentStatus.text = item.statusName.orEmpty()
            contentReject.text = item.rejectContent.orEmpty()

            item.activityLogs?.createdBy?.let { creator ->
                contentCreatedBy.text = "${creator.username.orEmpty()} - ${creator.processDate.orEmpty()}"
            } ?: run {
                contentCreatedBy.text = ""
            }

            val lastVerification = item.activityLogs?.verifiedBy?.lastOrNull()
            if (lastVerification != null) {
                contentRejectBy.text = "${lastVerification.username.orEmpty()} - ${lastVerification.processDate.orEmpty()}"
                clRejectBy.visibility = View.VISIBLE
            } else {
                clRejectBy.visibility = View.GONE
            }

            root.setThrottleClickListener { onClickItem?.invoke(item) }
        }
    }

    inner class Holder(var binding: ItemRejectTransferBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun getItemCount(): Int = dataSet.size
}