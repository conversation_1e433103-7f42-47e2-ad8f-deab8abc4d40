package com.vietinbank.feature_report.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_domain.models.checker.SubTranTypeListDomain
import com.vietinbank.feature_report.databinding.ItemDashboardBinding

class DashBoardReportAdapter : RecyclerView.Adapter<DashBoardReportAdapter.Holder>() {
    private var dataSet: MutableList<SubTranTypeListDomain> = mutableListOf()
    var context: Context? = null

    @SuppressLint("NotifyDataSetChanged")
    var onClickItem: ((SubTranTypeListDomain) -> Unit)? = null

    @SuppressLint("NotifyDataSetChanged")
    fun setData(data: MutableList<SubTranTypeListDomain>) {
        dataSet.clear()
        dataSet.addAll(data)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): Holder {
        val binding = ItemDashboardBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false,
        )
        return Holder(binding)
    }

    override fun onBindViewHolder(holder: Holder, position: Int) {
        val item = dataSet[position]
        with(holder.binding) {
            baseTextView.text = ("${(item.servicetypename ?: "")} (${(item.count_transaction ?: "")})")
            root.setThrottleClickListener {
                onClickItem?.invoke(item)
            }
        }
    }

    inner class Holder(var binding: ItemDashboardBinding) : RecyclerView.ViewHolder(binding.root)

    override fun getItemCount(): Int = dataSet.size
}