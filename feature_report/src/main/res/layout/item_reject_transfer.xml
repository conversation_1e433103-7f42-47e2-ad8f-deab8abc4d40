<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/item_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_marginVertical="@dimen/dp10"
        android:paddingBottom="@dimen/dp10"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_white_radius_2dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/dp12"
            android:paddingVertical="@dimen/dp10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tvTransferTypeTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/dp5"
                android:text="Quản lý giao dịch eFAST"
                android:textColor="@color/text_blue_02"
                android:textSize="@dimen/sp16"
                app:fontCus="semi_bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/tvAmount"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tvAmount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/dp5"
                android:text="11111"
                android:textColor="@color/red_button"
                android:textSize="14sp"
                app:fontCus="regular_bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/appCompatImageView"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/appCompatImageView"
                android:layout_width="@dimen/dp24"
                android:layout_height="@dimen/dp24"
                android:background="@drawable/ic_more"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/view"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginHorizontal="@dimen/dp12"
            android:background="@color/color_line"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/clTitle" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clTransferType"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp10"
            android:layout_marginTop="@dimen/dp5"
            app:layout_constraintTop_toBottomOf="@+id/view">

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tvTransferType"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/dp5"
                android:text="Loại giao dịch"
                android:textColor="@color/text_gray_08"
                android:textSize="14sp"
                app:fontCus="medium"
                app:layout_constraintEnd_toStartOf="@+id/contentTransferType"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/contentTransferType"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:text="Text 2"
                android:textSize="14sp"
                app:fontCus="medium"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toEndOf="@id/tvTransferType"
                app:layout_constraintTop_toTopOf="@id/tvTransferType" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clRecieveAccount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp10"
            android:layout_marginTop="@dimen/dp5"
            app:layout_constraintTop_toBottomOf="@+id/clTransferType">

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tvRecieveAccount"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/dp5"
                android:text="Tài khoản thụ hưởng"
                android:textColor="@color/text_gray_08"
                android:textSize="14sp"
                app:fontCus="medium"
                app:layout_constraintEnd_toStartOf="@+id/contentRecieveAccount"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/contentRecieveAccount"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:text="Text 2"
                android:textSize="14sp"
                app:fontCus="medium"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toEndOf="@id/tvRecieveAccount"
                app:layout_constraintTop_toTopOf="@id/tvRecieveAccount" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clMtId"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp10"
            android:layout_marginTop="@dimen/dp5"
            app:layout_constraintTop_toBottomOf="@+id/clRecieveAccount">

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tvMtId"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/dp5"
                android:text="Số giao dịch"
                android:textColor="@color/text_gray_08"
                android:textSize="14sp"
                app:fontCus="medium"
                app:layout_constraintEnd_toStartOf="@+id/contentMtId"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/contentMtId"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:text="Text 2"
                android:textSize="14sp"
                app:fontCus="medium"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toEndOf="@id/tvMtId"
                app:layout_constraintTop_toTopOf="@id/tvMtId" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clStatus"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp10"
            android:layout_marginTop="@dimen/dp5"
            app:layout_constraintTop_toBottomOf="@+id/clMtId">

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tvStatus"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/dp5"
                android:text="Trạng thái"
                android:textColor="@color/text_gray_08"
                android:textSize="14sp"
                app:fontCus="medium"
                app:layout_constraintEnd_toStartOf="@+id/lnStatus"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:id="@+id/lnStatus"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="end"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toEndOf="@id/tvStatus"
                app:layout_constraintTop_toTopOf="@id/tvStatus">

                <com.vietinbank.core_ui.base.views.BaseTextView
                    android:id="@+id/contentStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:background="@drawable/bg_blue09_radius_8dp"
                    android:gravity="end"
                    android:padding="3dp"
                    android:text="KTT/CTK từ chối"
                    android:textColor="@color/text_blue_02"
                    android:textSize="14sp"
                    app:fontCus="semi_bold" />
            </LinearLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clCreatedBy"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp10"
            android:layout_marginTop="@dimen/dp5"
            app:layout_constraintTop_toBottomOf="@+id/clStatus">

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tvCreatedBy"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/dp5"
                android:text="Người tạo điện"
                android:textColor="@color/text_gray_08"
                android:textSize="14sp"
                app:fontCus="medium"
                app:layout_constraintEnd_toStartOf="@+id/contentCreatedBy"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/contentCreatedBy"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:text="Text 2"
                android:textSize="14sp"
                app:fontCus="medium"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toEndOf="@id/tvCreatedBy"
                app:layout_constraintTop_toTopOf="@id/tvCreatedBy" />

        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clRejectBy"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:layout_marginHorizontal="@dimen/dp10"
            android:layout_marginTop="@dimen/dp5"
            app:layout_constraintTop_toBottomOf="@+id/clCreatedBy">

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tvRejectBy"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/dp5"
                android:text="Người từ chối"
                android:textColor="@color/text_gray_08"
                android:textSize="14sp"
                app:fontCus="medium"
                app:layout_constraintEnd_toStartOf="@+id/contentRejectBy"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/contentRejectBy"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:text="Text 2"
                android:textSize="14sp"
                app:fontCus="medium"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toEndOf="@id/tvRejectBy"
                app:layout_constraintTop_toTopOf="@id/tvRejectBy" />

        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clRejectContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp10"
            android:layout_marginTop="@dimen/dp5"
            app:layout_constraintTop_toBottomOf="@+id/clRejectBy">

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tvRejectContent"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/dp5"
                android:text="Nội dung từ chối"
                android:textColor="@color/text_gray_08"
                android:textSize="14sp"
                app:fontCus="medium"
                app:layout_constraintEnd_toStartOf="@+id/contentReject"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/contentReject"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:text="Text 2"
                android:textSize="14sp"
                app:fontCus="medium"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toEndOf="@id/tvRejectContent"
                app:layout_constraintTop_toTopOf="@id/tvRejectContent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>