<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/openCustomToolbar"
        layout="@layout/custom_toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/totalTransfer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingVertical="@dimen/dp10"
        android:paddingLeft="@dimen/dp10"
        android:text="Tổng số giao dịch bị từ chối"
        android:textColor="@color/white"
        android:textSize="@dimen/sp16"
        app:fontCus="semi_bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/openCustomToolbar" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rcvListRejectDetail"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:paddingBottom="@dimen/dp10"
        android:layout_marginHorizontal="@dimen/dp10"
        android:orientation="vertical"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/totalTransfer"
        tools:itemCount="5"
        tools:listitem="@layout/item_reject_transfer" />

</androidx.constraintlayout.widget.ConstraintLayout>