package com.vietinbank.newcore.di

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.nav.ISoftNavigator
import com.vietinbank.core_ekyc.update_ekyc.navigation.EkycNavigator
import com.vietinbank.core_ekyc.update_ekyc.navigation.EkycNavigatorImpl
import com.vietinbank.feature_home.ui.navigation.AccountBalanceNavigator
import com.vietinbank.feature_home.ui.navigation.AccountBalanceNavigatorImpl
import com.vietinbank.feature_soft.common.di.SoftNavigatorImpl
import com.vietinbank.newcore.nav.AppNavigatorImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.FragmentComponent

/**
 * Created by vandz on 18/12/24.
 */

@Module
@InstallIn(FragmentComponent::class)
object NavModule {

    @Provides
    fun provideNavController(fragment: Fragment): NavController {
        // Lấy NavController từ fragment
        return fragment.findNavController()
    }

    @Provides
    fun provideFragmentManager(fragment: Fragment): FragmentManager {
        return fragment.parentFragmentManager
    }

    @Provides
    fun provideAppNavigator(
        navController: NavController,
        fragmentManager: FragmentManager,
    ): IAppNavigator {
        return AppNavigatorImpl(navController, fragmentManager)
    }

    @Provides
    fun provideSoftNavigator(
        navController: NavController,
        fragmentManager: FragmentManager,
    ): ISoftNavigator {
        return SoftNavigatorImpl(navController, fragmentManager)
    }

    @Provides
    fun provideEkycNavigator(
        navController: NavController,
        fragmentManager: FragmentManager,
    ): EkycNavigator {
        return EkycNavigatorImpl(navController, fragmentManager)
    }

    @Provides
    fun provideAccountBalanceNavigator(
        navController: NavController,
        fragmentManager: FragmentManager,
    ): AccountBalanceNavigator {
        return AccountBalanceNavigatorImpl(navController, fragmentManager)
    }
}
