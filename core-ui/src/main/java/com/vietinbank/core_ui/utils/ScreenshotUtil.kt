package com.vietinbank.core_ui.utils

import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.view.View
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.LayoutCoordinates
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.core.content.FileProvider
import androidx.core.view.drawToBitmap
import com.vietinbank.core_common.extensions.printLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Utility class to handle screenshot capture and sharing in Jetpack Compose
 */
object ScreenshotUtil {
    private const val TAG = "ScreenshotUtil"
    private const val FILE_PREFIX = "VietinBank_efast_transaction_"
    private const val FILE_EXTENSION = ".jpg"
    private const val MIME_TYPE = "image/jpeg"
    private const val QUALITY = 100

    /**
     * Modifier extension to track the coordinates of a composable element
     */
    fun Modifier.trackBounds(
        onBoundsChange: (LayoutCoordinates) -> Unit,
    ): Modifier = this.then(
        Modifier.onGloballyPositioned { coordinates ->
            onBoundsChange(coordinates)
        },
    )

    /**
     * Captures a screenshot of a specific area in the view hierarchy
     */
    suspend fun captureScreenshot(
        rootView: View,
        contentBounds: androidx.compose.ui.geometry.Rect,
        context: Context,
    ): Bitmap? = withContext(Dispatchers.Main) {
        try {
            val bitmap = rootView.drawToBitmap()

            // Crop the bitmap to include only the content area
            val left = contentBounds.left.toInt().coerceAtLeast(0)
            val top = contentBounds.top.toInt().coerceAtLeast(0)
            val width = contentBounds.width.toInt().coerceAtMost(bitmap.width - left)
            val height = contentBounds.height.toInt().coerceAtMost(bitmap.height - top)

            if (width <= 0 || height <= 0) {
                printLog(TAG, "Invalid cropping dimensions: width=$width, height=$height")
                return@withContext null
            }

            return@withContext Bitmap.createBitmap(
                bitmap,
                left,
                top,
                width,
                height,
            )
        } catch (e: Exception) {
            printLog(e, "Error capturing screenshot: ${e.message}")
            return@withContext null
        }
    }

    /**
     * Saves a bitmap to the device storage
     */
    suspend fun saveBitmapToStorage(
        context: Context,
        bitmap: Bitmap,
    ): Uri? = withContext(Dispatchers.IO) {
        try {
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val fileName = "$FILE_PREFIX$timestamp$FILE_EXTENSION"

            val uri = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // For Android 10 and above, use MediaStore
                val contentValues = ContentValues().apply {
                    put(MediaStore.MediaColumns.DISPLAY_NAME, fileName)
                    put(MediaStore.MediaColumns.MIME_TYPE, MIME_TYPE)
                    put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_PICTURES)
                }

                val resolver = context.contentResolver
                val uri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)
                uri?.let {
                    resolver.openOutputStream(it)?.use { outputStream ->
                        bitmap.compress(Bitmap.CompressFormat.JPEG, QUALITY, outputStream)
                    }
                }
                uri
            } else {
                // For older Android versions, use FileProvider
                val imagesDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
                val imageFile = File(imagesDir, fileName)

                FileOutputStream(imageFile).use { outputStream ->
                    bitmap.compress(Bitmap.CompressFormat.JPEG, QUALITY, outputStream)
                }

                FileProvider.getUriForFile(
                    context,
                    "${context.packageName}.provider",
                    imageFile,
                )
            }

            printLog(TAG, "Screenshot saved: $uri")
            return@withContext uri
        } catch (e: IOException) {
            printLog(e, "Error saving screenshot: ${e.message}")
            return@withContext null
        }
    }

    /**
     * Creates a temporary file for sharing
     */
    private suspend fun createTempFileForSharing(
        context: Context,
        bitmap: Bitmap,
    ): Uri? = withContext(Dispatchers.IO) {
        try {
            val cachePath = File(context.cacheDir, "images")
            cachePath.mkdirs()

            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val fileName = "$FILE_PREFIX$timestamp$FILE_EXTENSION"
            val file = File(cachePath, fileName)

            FileOutputStream(file).use { outputStream ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, QUALITY, outputStream)
                outputStream.flush()
            }

            FileProvider.getUriForFile(
                context,
                "${context.packageName}.provider",
                file,
            )
        } catch (e: IOException) {
            printLog("Error creating temp file: ${e.message}")
            null
        }
    }

    /**
     * Shares a bitmap via the system share intent
     */
    suspend fun shareBitmap(
        context: Context,
        bitmap: Bitmap,
    ) = withContext(Dispatchers.Main) {
        try {
            val uri = createTempFileForSharing(context, bitmap) ?: return@withContext

            val shareIntent = Intent().apply {
                action = Intent.ACTION_SEND
                putExtra(Intent.EXTRA_STREAM, uri)
                type = MIME_TYPE
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }

            context.startActivity(Intent.createChooser(shareIntent, "Chia sẻ kết quả giao dịch"))
        } catch (e: Exception) {
            printLog("Error sharing screenshot: ${e.message}")
        }
    }
}