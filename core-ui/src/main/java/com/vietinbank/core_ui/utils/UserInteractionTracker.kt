package com.vietinbank.core_ui.utils

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.PointerEventType
import androidx.compose.ui.input.pointer.changedToDownIgnoreConsumed
import androidx.compose.ui.input.pointer.pointerInput

/**
 * Wraps content and invokes [onInteract] whenever the user touches the screen.
 * This is a lightweight root-level tracker to keep session-alive signals flowing
 * in Compose screens where View-level touch listeners may not observe all events.
 */
@Composable
fun TrackUserInteraction(
    onInteract: () -> Unit,
    modifier: Modifier = Modifier,
    content: @Composable BoxScope.() -> Unit,
) {
    Box(
        modifier = modifier.pointerInput(Unit) {
            awaitPointerEventScope {
                while (true) {
                    val event = awaitPointerEvent()
                    // Trigger only on down to avoid spamming on move
                    if (
                        event.type == PointerEventType.Press ||
                        event.changes.any { it.changedToDownIgnoreConsumed() }
                    ) {
                        onInteract()
                    }
                }
            }
        },
        content = content,
    )
}
