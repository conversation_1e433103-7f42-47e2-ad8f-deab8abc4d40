package com.vietinbank.core_ui.utils

import androidx.compose.runtime.Immutable

/**
 * App-specific window width classification
 * Abstraction over Material3's WindowWidthSizeClass to avoid API leakage
 *
 * @see <a href="https://m3.material.io/foundations/layout/applying-layout/window-size-classes">Material Design Window Size Classes</a>
 */
@Immutable
sealed interface AppWindowWidthClass {
    /**
     * Compact width: typically phones in portrait (width < 600dp)
     */
    data object Compact : AppWindowWidthClass

    /**
     * Medium width: typically tablets in portrait or phones in landscape (600dp ≤ width < 840dp)
     */
    data object Medium : AppWindowWidthClass

    /**
     * Expanded width: typically tablets in landscape or desktop (width ≥ 840dp)
     */
    data object Expanded : AppWindowWidthClass
}

/**
 * App-specific window height classification
 * Abstraction over Material3's WindowHeightSizeClass to avoid API leakage
 *
 * @see <a href="https://m3.material.io/foundations/layout/applying-layout/window-size-classes">Material Design Window Size Classes</a>
 */
@Immutable
sealed interface AppWindowHeightClass {
    /**
     * Compact height: typically phones in landscape (height < 480dp)
     */
    data object Compact : AppWindowHeightClass

    /**
     * Medium height: typically phones in portrait or tablets (480dp ≤ height < 900dp)
     */
    data object Medium : AppWindowHeightClass

    /**
     * Expanded height: typically tablets or desktop with plenty of vertical space (height ≥ 900dp)
     */
    data object Expanded : AppWindowHeightClass
}

/**
 * Represents the window size class for both width and height dimensions
 * This abstraction allows responsive layouts without direct dependency on Material3
 *
 * @property width The width classification of the current window
 * @property height The height classification of the current window
 *
 * Usage:
 * ```kotlin
 * val windowSizeClass = rememberAppWindowSizeClass()
 * val spacing = when (windowSizeClass.width) {
 *     AppWindowWidthClass.Compact -> 16.dp
 *     AppWindowWidthClass.Medium -> 24.dp
 *     AppWindowWidthClass.Expanded -> 32.dp
 * }
 * ```
 */
@Immutable
data class AppWindowSizeClass(
    val width: AppWindowWidthClass,
    val height: AppWindowHeightClass,
) {
    /**
     * Helper to check if the device is in landscape orientation
     * Note: This is a simplified check based on typical breakpoints
     */
    val isLandscape: Boolean
        get() = height == AppWindowHeightClass.Compact

    /**
     * Helper to check if this is likely a phone form factor
     */
    val isPhone: Boolean
        get() = width == AppWindowWidthClass.Compact

    /**
     * Helper to check if this is likely a tablet or desktop
     */
    val isTabletOrDesktop: Boolean
        get() = width == AppWindowWidthClass.Expanded ||
            (width == AppWindowWidthClass.Medium && height != AppWindowHeightClass.Compact)
}