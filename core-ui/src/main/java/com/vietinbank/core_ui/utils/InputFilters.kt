package com.vietinbank.core_ui.utils

import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.runtime.Composable
import androidx.compose.ui.text.input.KeyboardType
import java.text.Normalizer

/**
 * Created by vandz on 5/5/25.
 */
object InputFilters {
    // Pattern chỉ cho phép chữ cái, số, dấu gạch dưới, dấu chấm và dấu gạch ngang
    val USERNAME_PATTERN = "[A-Za-z0-9._\\-]*".toRegex()
    val NONE_SPACE = "^[A-Za-z0-9!@#$%^&*().,<>?\\-=+/;:'\"|`~_\\[\\]{}]+$".toRegex()

    val NUMBER_ONLY_PATTERN = "[0-9]*".toRegex()
    val EMAIL_PATTERN = "[A-Za-z0-9._%+\\-@]*".toRegex()
    val PHONE_PATTERN = "[0-9+]*".toRegex()
    val NO_SPECIAL_CHARS_PATTERN = "[A-Za-z0-9]*".toRegex()
    val ALPHANUMERIC_WITH_SPACE = "[A-Za-z0-9\\s]*".toRegex()
    val REJECT_PATTERN = "[^@!#$%^&*{}]".toRegex()

    /**
     * Lọc các ký tự không hợp lệ khỏi chuỗi đầu vào
     *
     * @param text Chuỗi đầu vào cần lọc
     * @param pattern Mẫu regex cho các ký tự hợp lệ
     * @return Chuỗi đã lọc chỉ chứa ký tự hợp lệ
     */
    fun filterText(text: String, pattern: Regex): String {
        // Chuyển ký tự có dấu thành không dấu
        val normalized = Normalizer.normalize(text, Normalizer.Form.NFD)
            .replace("\\p{InCombiningDiacriticalMarks}+".toRegex(), "")

        // Chỉ giữ lại các ký tự khớp với pattern
        return normalized.filter { it.toString().matches(pattern) || it.toString() == "" }
    }

    /**
     * Kiểm tra xem chuỗi có khớp hoàn toàn với pattern không
     *
     * @param text Chuỗi cần kiểm tra
     * @param pattern Mẫu regex
     * @return true nếu chuỗi khớp hoàn toàn với pattern
     */
    fun matchesPattern(text: String, pattern: Regex): Boolean {
        return text.matches(pattern)
    }

    /**
     * Trả về KeyboardOptions phù hợp với loại pattern
     *
     * @param pattern Mẫu regex đang sử dụng
     * @return KeyboardOptions phù hợp
     */
    @Composable
    fun getKeyboardOptions(pattern: Regex): KeyboardOptions {
        return when (pattern) {
            NUMBER_ONLY_PATTERN, PHONE_PATTERN -> KeyboardOptions(keyboardType = KeyboardType.Number)
            EMAIL_PATTERN -> KeyboardOptions(keyboardType = KeyboardType.Email)
            USERNAME_PATTERN, REJECT_PATTERN -> KeyboardOptions(keyboardType = KeyboardType.Password)
            else -> KeyboardOptions(keyboardType = KeyboardType.Ascii)
        }
    }
}
