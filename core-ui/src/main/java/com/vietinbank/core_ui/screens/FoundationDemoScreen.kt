package com.vietinbank.core_ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.sp
import com.vietinbank.core_common.models.AppBarAction
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.ButtonSize
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.core_ui.theme.SvnGilroyFamily

/**
 * Demo screen to showcase Foundation Design System components
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FoundationDemoScreen() {
    Scaffold(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black), // Dark background like Figma
        topBar = {
            FoundationAppBar(
                title = "Foundation Demo",
                onNavigationClick = { },
                isBackHome = true,
                actions = listOf(
                    AppBarAction(
                        icon = R.drawable.ic_common_back_24dp,
                        contentDescription = "Notifications",
                        onClick = { },
                    ),
                    AppBarAction(
                        icon = R.drawable.ic_common_back_24dp,
                        contentDescription = "Settings",
                        onClick = { },
                    ),
                ),
            )
        },
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(Color(0xFFF5F5F5)) // Light gray background to see shadows
                .verticalScroll(rememberScrollState())
                .padding(innerPadding)
                .padding(dimensionResource(R.dimen.foundation_spacing_md)),
            verticalArrangement = Arrangement.spacedBy(dimensionResource(R.dimen.foundation_spacing_lg)),
        ) {
            // Title
            Text(
                text = "Foundation Design System",
                fontSize = 24.sp,
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.Bold,
                color = AppColors.secondary,
                modifier = Modifier.padding(bottom = dimensionResource(R.dimen.foundation_spacing_md)),
            )

            // Button Sizes Section
            SectionTitle("Button Sizes")

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(dimensionResource(R.dimen.foundation_spacing_md)),
            ) {
                FoundationButton(
                    text = "Small",
                    onClick = {},
                    size = ButtonSize.Small,
                    modifier = Modifier.weight(1f),
                )

                FoundationButton(
                    text = "Medium",
                    onClick = {},
                    size = ButtonSize.Medium,
                    modifier = Modifier.weight(1f),
                )

                FoundationButton(
                    text = "Large",
                    onClick = {},
                    size = ButtonSize.Large,
                    modifier = Modifier.weight(1f),
                )
            }

            // Button States Section
            SectionTitle("Button States")

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(dimensionResource(R.dimen.foundation_spacing_md)),
            ) {
                FoundationButton(
                    text = "Enabled",
                    onClick = {},
                    enabled = true,
                    modifier = Modifier.weight(1f),
                )

                FoundationButton(
                    text = "Disabled",
                    onClick = {},
                    enabled = false,
                    modifier = Modifier.weight(1f),
                )
            }

            // Colors Demo Section
            SectionTitle("Foundation Colors")

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(dimensionResource(R.dimen.foundation_spacing_sm)),
            ) {
                ColorBox(
                    color = AppColors.primary,
                    label = "Primary",
                )
                ColorBox(
                    color = AppColors.secondary,
                    label = "Secondary",
                )
                ColorBox(
                    color = AppColors.successGreen,
                    label = "Success",
                )
                ColorBox(
                    color = AppColors.textWarningYellow,
                    label = "Warning",
                )
            }

            // Spacing Demo Section
            SectionTitle("Foundation Spacing")

            Column(
                verticalArrangement = Arrangement.spacedBy(dimensionResource(R.dimen.foundation_spacing_xs)),
            ) {
                SpacingDemo("XS", R.dimen.foundation_spacing_xs)
                SpacingDemo("SM", R.dimen.foundation_spacing_sm)
                SpacingDemo("MD", R.dimen.foundation_spacing_md)
                SpacingDemo("LG", R.dimen.foundation_spacing_lg)
                SpacingDemo("XL", R.dimen.foundation_spacing_xl)
            }

            // AppBar Demo Section
            SectionTitle("Foundation AppBar Variants")

            Text(
                text = "AppBar examples are shown above and in the following preview functions.",
                fontSize = 14.sp,
                fontFamily = SvnGilroyFamily,
                color = AppColors.textSecondary,
                modifier = Modifier.padding(bottom = dimensionResource(R.dimen.foundation_spacing_md)),
            )
        }
    }
}

@Composable
private fun SectionTitle(title: String) {
    Text(
        text = title,
        fontSize = 18.sp,
        fontFamily = SvnGilroyFamily,
        fontWeight = FontWeight.SemiBold,
        color = AppColors.textPrimary,
        modifier = Modifier.padding(vertical = dimensionResource(R.dimen.foundation_spacing_sm)),
    )
}

@Composable
private fun ColorBox(
    color: Color,
    label: String,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Box(
            modifier = Modifier
                .size(dimensionResource(R.dimen.foundation_icon_xl))
                .background(
                    color,
                    RoundedCornerShape(dimensionResource(R.dimen.foundation_radius_sm)),
                ),
        )
        Text(
            text = label,
            fontSize = 12.sp,
            fontFamily = SvnGilroyFamily,
            color = AppColors.grey07,
            modifier = Modifier.padding(top = dimensionResource(R.dimen.foundation_spacing_xs)),
        )
    }
}

@Composable
private fun SpacingDemo(
    label: String,
    spacing: Int,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
            text = label,
            fontSize = 14.sp,
            fontFamily = SvnGilroyFamily,
            color = AppColors.grey07,
            modifier = Modifier.width(dimensionResource(R.dimen.foundation_icon_xl)),
        )
        Box(
            modifier = Modifier
                .width(dimensionResource(spacing))
                .height(dimensionResource(R.dimen.foundation_spacing_lg))
                .background(AppColors.blue04),
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Preview(showBackground = true)
@Composable
fun FoundationDemoScreenPreview() {
    AppTheme {
        FoundationDemoScreen()
    }
}

/**
 * Additional demo screens for AppBar variants
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppBarDemoScreen() {
    Scaffold(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black), // Dark background
        topBar = {
            Column {
                // Simple back navigation
                FoundationAppBar(
                    title = "Simple Back Navigation",
                    onNavigationClick = { },
                )

                // With single action
                FoundationAppBar(
                    title = "Home",
                    onNavigationClick = { },
                    actions = listOf(
                        AppBarAction(
                            icon = R.drawable.ic_common_back_24dp,
                            contentDescription = "Notifications",
                            onClick = { },
                        ),
                    ),
                )
            }
        },
        content = { innerPadding ->
            LazyColumn(
                contentPadding = innerPadding,
                modifier = Modifier.fillMaxSize(),
            ) {
                items(50) {
                    Text(
                        text = "Item $it",
                        color = Color.White,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(dimensionResource(R.dimen.foundation_spacing_md)),
                    )
                }
            }
        },
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Preview(name = "AppBar Demo Screen")
@Composable
private fun AppBarDemoScreenPreview() {
    AppTheme {
        AppBarDemoScreen()
    }
}