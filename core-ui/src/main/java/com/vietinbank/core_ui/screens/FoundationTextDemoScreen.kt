package com.vietinbank.core_ui.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment.Companion.CenterHorizontally
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.text.foundation.FoundationClickableText
import com.vietinbank.core_ui.components.text.foundation.FoundationExpandableText
import com.vietinbank.core_ui.components.text.foundation.FoundationHtmlText
import com.vietinbank.core_ui.components.text.foundation.FoundationIconText
import com.vietinbank.core_ui.components.text.foundation.FoundationRichText
import com.vietinbank.core_ui.components.text.foundation.FoundationSelectableText
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.components.text.foundation.SelectionIndicator
import com.vietinbank.core_ui.components.text.foundation.appendBold
import com.vietinbank.core_ui.components.text.foundation.appendColored
import com.vietinbank.core_ui.components.text.foundation.appendLink
import com.vietinbank.core_ui.models.IconConfig
import com.vietinbank.core_ui.models.IconPosition
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Demo screen showcasing all Foundation Text components
 */
@Composable
fun FoundationTextDemoScreen(
    onBackPress: () -> Unit = {},
) {
    Scaffold(
        topBar = {
            FoundationAppBar(
                title = "Foundation Text Demo",
                onNavigationClick = onBackPress,
            )
        },
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
                .padding(FDS.Sizer.Padding.padding16),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
        ) {
            // FoundationText Demo
            DemoSection(title = "FoundationText") {
                FoundationText(
                    text = "Basic text with default style",
                )

                FoundationText(
                    text = "Heading style text",
                    style = FDS.Typography.headingH2,
                    color = FDS.Colors.characterHighlighted,
                )

                FoundationText(
                    text = "Error text with custom style",
                    style = FDS.Typography.bodyB1Emphasized,
                    color = FDS.Colors.error,
                )
            }

            // FoundationIconText Demo
            DemoSection(title = "FoundationIconText") {
                FoundationIconText(
                    text = "Text with left icon",
                    icons = mapOf(
                        IconPosition.LEFT to IconConfig(
                            icon = R.drawable.ic_commom_success_24,
                        ),
                    ),
                )

                FoundationIconText(
                    text = "Text with right icon",
                    icons = mapOf(
                        IconPosition.RIGHT to IconConfig(
                            icon = R.drawable.ic_commom_pending_24,
                        ),
                    ),
                )

                FoundationIconText(
                    text = "Premium Account",
                    icons = mapOf(
                        IconPosition.LEFT to IconConfig(
                            icon = R.drawable.ic_account_company_24,
                            size = FDS.Sizer.Icon.icon24,
                        ),
                        IconPosition.TOP_RIGHT to IconConfig(
                            icon = R.drawable.ic_approved,
                            size = FDS.Sizer.Icon.icon24,
                        ),
                    ),
                    style = FDS.Typography.bodyB1Emphasized,
                )

                FoundationIconText(
                    text = "Upload Document",
                    icons = mapOf(
                        IconPosition.TOP to IconConfig(
                            icon = R.drawable.ic_camera,
                            size = FDS.Sizer.Icon.icon32,
                        ),
                    ),
                    horizontalAlignment = CenterHorizontally,
                )

                // Multi-directional icons example
                FoundationIconText(
                    text = "Transaction Details",
                    icons = mapOf(
                        IconPosition.LEFT to IconConfig(
                            icon = R.drawable.ic_commom_pending_24,
                            size = FDS.Sizer.Icon.icon20,
                        ),
                        IconPosition.TOP_RIGHT to IconConfig(
                            icon = R.drawable.ic_approved,
                            size = FDS.Sizer.Icon.icon16,
                            tint = FDS.Colors.success,
                        ),
                        IconPosition.BOTTOM_RIGHT to IconConfig(
                            icon = R.drawable.ic_lock,
                            size = FDS.Sizer.Icon.icon16,
                        ),
                    ),
                    style = FDS.Typography.bodyB1,
                )
            }

            // FoundationClickableText Demo
            DemoSection(title = "FoundationClickableText") {
                var clickCount by remember { mutableStateOf(0) }

                FoundationClickableText(
                    text = "Click me! (Count: $clickCount)",
                    onClick = { clickCount++ },
                )

                FoundationClickableText(
                    text = "Xem điều khoản sử dụng",
                    onClick = { /* Handle click */ },
                    underline = true,
                )

                FoundationClickableText(
                    text = "Disabled clickable text",
                    onClick = { /* Won't be called */ },
                    enabled = false,
                )

                FoundationClickableText(
                    text = "Filter Options",
                    onClick = { /* Handle click */ },
                    icons = mapOf(
                        IconPosition.LEFT to IconConfig(
                            icon = R.drawable.ic_filter,
                        ),
                    ),
                )
            }

            // FoundationRichText Demo
            DemoSection(title = "FoundationRichText") {
                val successColor = FDS.Colors.success
                val linkColor = FDS.Colors.textLink
                val errorColor = FDS.Colors.error
                val warningColor = FDS.Colors.warning

                FoundationRichText {
                    append("Total: ")
                    appendBold("1,234,567 VND", color = successColor)
                }

                FoundationRichText(
                    onTextPartClick = { annotation ->
                        // Handle click on annotated parts
                    },
                ) {
                    append("Bằng việc tiếp tục, bạn đồng ý với ")
                    appendLink(
                        text = "Điều khoản sử dụng",
                        tag = "terms",
                        color = linkColor,
                    )
                    append(" và ")
                    appendLink(
                        text = "Chính sách bảo mật",
                        tag = "privacy",
                        color = linkColor,
                    )
                }

                FoundationRichText {
                    append("Status: ")
                    appendColored("Thành công", color = successColor, fontWeight = FontWeight.Bold)
                    append(" | ")
                    appendColored("3 lỗi", color = errorColor)
                    append(" | ")
                    appendColored("2 cảnh báo", color = warningColor)
                }
            }

            // FoundationHtmlText Demo
            DemoSection(title = "FoundationHtmlText") {
                FoundationHtmlText(
                    html = "<b>Chú ý:</b> Vui lòng <u>kiểm tra</u> thông tin trước khi <i>xác nhận</i>",
                )

                FoundationHtmlText(
                    html = "Visit <a href='https://vietinbank.vn'>VietinBank website</a> for more info",
                    onLinkClick = { url ->
                        // Handle link click
                    },
                )

                FoundationHtmlText(
                    html = "<font color='#FF0000'>Red text</font> and <font color='#00FF00'>Green text</font>",
                )
            }

            // FoundationSelectableText Demo
            DemoSection(title = "FoundationSelectableText") {
                var selected1 by remember { mutableStateOf(false) }
                var selected2 by remember { mutableStateOf(true) }
                var selected3 by remember { mutableStateOf(false) }
                var selected4 by remember { mutableStateOf(true) }

                FoundationSelectableText(
                    text = "Tap to select (Background)",
                    selected = selected1,
                    onSelectionChange = { selected1 = it },
                    selectionIndicator = SelectionIndicator.Background,
                )

                FoundationSelectableText(
                    text = "Tap to select (Border)",
                    selected = selected2,
                    onSelectionChange = { selected2 = it },
                    selectionIndicator = SelectionIndicator.Border,
                )

                FoundationSelectableText(
                    text = "Tap to select (Underline)",
                    selected = selected3,
                    onSelectionChange = { selected3 = it },
                    selectionIndicator = SelectionIndicator.Underline,
                )

                FoundationSelectableText(
                    text = "Tap to select (CheckMark)",
                    selected = selected4,
                    onSelectionChange = { selected4 = it },
                    selectionIndicator = SelectionIndicator.CheckMark,
                )
            }

            // FoundationExpandableText Demo
            DemoSection(title = "FoundationExpandableText") {
                FoundationExpandableText(
                    text = "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.",
                    collapsedMaxLines = 2,
                )

                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))

                FoundationExpandableText(
                    text = "Thông báo quan trọng: Ngân hàng TMCP Công Thương Việt Nam thông báo về việc nâng cấp hệ thống Core Banking trong thời gian từ 22h00 ngày 15/12/2024 đến 6h00 ngày 16/12/2024. Trong thời gian này, một số dịch vụ có thể bị gián đoạn.",
                    collapsedMaxLines = 3,
                    showExpandTextInline = false,
                    style = FDS.Typography.bodyB2,
                    color = FDS.Colors.warning,
                )
            }
        }
    }
}

/**
 * Demo section wrapper
 */
@Composable
private fun DemoSection(
    title: String,
    content: @Composable () -> Unit,
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(
            defaultElevation = FDS.Effects.elevationSm,
        ),
        colors = CardDefaults.cardColors(
            containerColor = FDS.Colors.surface,
        ),
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(FDS.Sizer.Padding.padding16),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
        ) {
            Text(
                text = title,
                style = FDS.Typography.headingH4,
                color = FDS.Colors.primary,
            )

            HorizontalDivider(
                color = FDS.Colors.divider,
                modifier = Modifier.padding(vertical = FDS.Sizer.Padding.padding4),
            )

            content()
        }
    }
}

// Preview 1: Basic Text Components (FoundationText + FoundationIconText)
@Preview(showBackground = true, backgroundColor = 0xFF000000, name = "Basic Text Components")
@Composable
private fun BasicTextComponentsPreview() {
    Scaffold(
        containerColor = Color.Black,
        topBar = {
            FoundationAppBar(
                title = "Basic Text Components",
                onNavigationClick = {},
            )
        },
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
                .padding(FDS.Sizer.Padding.padding16),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
        ) {
            // FoundationText Demo
            DemoSection(title = "FoundationText") {
                FoundationText(
                    text = "Basic text with default style",
                )

                FoundationText(
                    text = "Heading style text",
                    style = FDS.Typography.headingH2,
                    color = FDS.Colors.characterHighlighted,
                )

                FoundationText(
                    text = "Error text with custom style",
                    style = FDS.Typography.bodyB1Emphasized,
                    color = FDS.Colors.error,
                )
            }

            // FoundationIconText Demo
            DemoSection(title = "FoundationIconText") {
                FoundationIconText(
                    text = "Text with left icon",
                    icons = mapOf(
                        IconPosition.LEFT to IconConfig(
                            icon = R.drawable.ic_commom_success_24,
                        ),
                    ),
                )

                FoundationIconText(
                    text = "Text with right icon",
                    icons = mapOf(
                        IconPosition.RIGHT to IconConfig(
                            icon = R.drawable.ic_commom_pending_24,
                        ),
                    ),
                )

                FoundationIconText(
                    text = "Premium Account",
                    icons = mapOf(
                        IconPosition.LEFT to IconConfig(
                            icon = R.drawable.ic_account_company_24,
                            size = FDS.Sizer.Icon.icon24,
                        ),
                        IconPosition.TOP_RIGHT to IconConfig(
                            icon = R.drawable.ic_approved,
                            size = FDS.Sizer.Icon.icon24,
                        ),
                    ),
                    style = FDS.Typography.bodyB1Emphasized,
                )

                FoundationIconText(
                    text = "Transaction Details",
                    icons = mapOf(
                        IconPosition.LEFT to IconConfig(
                            icon = R.drawable.ic_commom_pending_24,
                            size = FDS.Sizer.Icon.icon20,
                        ),
                        IconPosition.TOP_RIGHT to IconConfig(
                            icon = R.drawable.ic_approved,
                            size = FDS.Sizer.Icon.icon16,
                            tint = FDS.Colors.success,
                        ),
                        IconPosition.BOTTOM_RIGHT to IconConfig(
                            icon = R.drawable.ic_lock,
                            size = FDS.Sizer.Icon.icon16,
                        ),
                    ),
                    style = FDS.Typography.bodyB1,
                )
            }
        }
    }
}

// Preview 2: Interactive Text Components (FoundationClickableText + FoundationRichText)
@Preview(showBackground = true, backgroundColor = 0xFF000000, name = "Interactive Text Components")
@Composable
private fun InteractiveTextComponentsPreview() {
    var clickCount by remember { mutableStateOf(0) }

    Scaffold(
        containerColor = Color.Black,
        topBar = {
            FoundationAppBar(
                title = "Interactive Text Components",
                onNavigationClick = {},
            )
        },
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
                .padding(FDS.Sizer.Padding.padding16),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
        ) {
            // FoundationClickableText Demo
            DemoSection(title = "FoundationClickableText") {
                FoundationClickableText(
                    text = "Click me! (Count: $clickCount)",
                    onClick = { clickCount++ },
                )

                FoundationClickableText(
                    text = "Xem điều khoản sử dụng",
                    onClick = { /* Handle click */ },
                    underline = true,
                )

                FoundationClickableText(
                    text = "Disabled clickable text",
                    onClick = { /* Won't be called */ },
                    enabled = false,
                )

                FoundationClickableText(
                    text = "Filter Options",
                    onClick = { /* Handle click */ },
                    icons = mapOf(
                        IconPosition.LEFT to IconConfig(
                            icon = R.drawable.ic_filter,
                        ),
                    ),
                )
            }

            // FoundationRichText Demo
            DemoSection(title = "FoundationRichText") {
                val successColor = FDS.Colors.success
                val linkColor = FDS.Colors.textLink
                val errorColor = FDS.Colors.error
                val warningColor = FDS.Colors.warning

                FoundationRichText {
                    append("Total: ")
                    appendBold("1,234,567 VND", color = successColor)
                }

                FoundationRichText(
                    onTextPartClick = { annotation ->
                        // Handle click on annotated parts
                    },
                ) {
                    append("Bằng việc tiếp tục, bạn đồng ý với ")
                    appendLink(
                        text = "Điều khoản sử dụng",
                        tag = "terms",
                        color = linkColor,
                    )
                    append(" và ")
                    appendLink(
                        text = "Chính sách bảo mật",
                        tag = "privacy",
                        color = linkColor,
                    )
                }

                FoundationRichText {
                    append("Status: ")
                    appendColored("Thành công", color = successColor, fontWeight = FontWeight.Bold)
                    append(" | ")
                    appendColored("3 lỗi", color = errorColor)
                    append(" | ")
                    appendColored("2 cảnh báo", color = warningColor)
                }
            }
        }
    }
}

// Preview 3: Special Text Components (FoundationHtmlText + FoundationSelectableText + FoundationExpandableText)
@Preview(showBackground = true, backgroundColor = 0xFF000000, name = "Special Text Components")
@Composable
private fun SpecialTextComponentsPreview() {
    var selected1 by remember { mutableStateOf(false) }
    var selected2 by remember { mutableStateOf(true) }
    var selected3 by remember { mutableStateOf(false) }
    var selected4 by remember { mutableStateOf(true) }

    Scaffold(
        containerColor = Color.Black,
        topBar = {
            FoundationAppBar(
                title = "Special Text Components",
                onNavigationClick = {},
            )
        },
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
                .padding(FDS.Sizer.Padding.padding16),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
        ) {
            // FoundationHtmlText Demo
            DemoSection(title = "FoundationHtmlText") {
                FoundationHtmlText(
                    html = "<b>Chú ý:</b> Vui lòng <u>kiểm tra</u> thông tin trước khi <i>xác nhận</i>",
                )

                FoundationHtmlText(
                    html = "Visit <a href='https://vietinbank.vn'>VietinBank website</a> for more info",
                    onLinkClick = { url ->
                        // Handle link click
                    },
                )

                FoundationHtmlText(
                    html = "<font color='#FF0000'>Red text</font> and <font color='#00FF00'>Green text</font>",
                )
            }

            // FoundationSelectableText Demo
            DemoSection(title = "FoundationSelectableText") {
                FoundationSelectableText(
                    text = "Tap to select (Background)",
                    selected = selected1,
                    onSelectionChange = { selected1 = it },
                    selectionIndicator = SelectionIndicator.Background,
                )

                FoundationSelectableText(
                    text = "Tap to select (Border)",
                    selected = selected2,
                    onSelectionChange = { selected2 = it },
                    selectionIndicator = SelectionIndicator.Border,
                )

                FoundationSelectableText(
                    text = "Tap to select (Underline)",
                    selected = selected3,
                    onSelectionChange = { selected3 = it },
                    selectionIndicator = SelectionIndicator.Underline,
                )

                FoundationSelectableText(
                    text = "Tap to select (CheckMark)",
                    selected = selected4,
                    onSelectionChange = { selected4 = it },
                    selectionIndicator = SelectionIndicator.CheckMark,
                )
            }

            // FoundationExpandableText Demo
            DemoSection(title = "FoundationExpandableText") {
                FoundationExpandableText(
                    text = "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.",
                    collapsedMaxLines = 2,
                )

                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))

                FoundationExpandableText(
                    text = "Thông báo quan trọng: Ngân hàng TMCP Công Thương Việt Nam thông báo về việc nâng cấp hệ thống Core Banking trong thời gian từ 22h00 ngày 15/12/2024 đến 6h00 ngày 16/12/2024. Trong thời gian này, một số dịch vụ có thể bị gián đoạn.",
                    collapsedMaxLines = 3,
                    showExpandTextInline = false,
                    style = FDS.Typography.bodyB2,
                    color = FDS.Colors.warning,
                )
            }
        }
    }
}