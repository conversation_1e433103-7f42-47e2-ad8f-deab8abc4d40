package com.vietinbank.core_ui.validators

import com.vietinbank.core_ui.components.foundation.textfield.validators.Validator
import java.util.regex.Pattern

/**
 * Common validators for Foundation EditText components
 * Provides pre-built validators for common use cases in banking applications
 */
object FoundationValidators {
    /**
     * Required field validator
     */
    val required = Validator { input ->
        if (input.isBlank()) "This field is required" else null
    }

    /**
     * Email validator
     */
    val email = Validator { input ->
        val emailPattern = Pattern.compile(
            "[a-zA-Z0-9+._%-+]{1,256}" +
                "@" +
                "[a-zA-Z0-9][a-zA-Z0-9-]{0,64}" +
                "(" +
                "." +
                "[a-zA-Z0-9][a-zA-Z0-9-]{0,25}" +
                ")+",
        )
        if (input.isNotBlank() && !emailPattern.matcher(input).matches()) {
            "Invalid email format"
        } else {
            null
        }
    }

    /**
     * Phone number validator (Vietnam)
     */
    val phone = Validator { input ->
        val phonePattern = Pattern.compile("^(0|\\+84)[3-9][0-9]{8}$")
        val cleanInput = input.replace(" ", "").replace("-", "")
        if (input.isNotBlank() && !phonePattern.matcher(cleanInput).matches()) {
            "Invalid phone number"
        } else {
            null
        }
    }

    /**
     * Minimum length validator
     */
    fun minLength(length: Int) = Validator { input ->
        if (input.length < length) "Minimum $length characters required" else null
    }

    /**
     * Maximum length validator
     */
    fun maxLength(length: Int) = Validator { input ->
        if (input.length > length) "Maximum $length characters allowed" else null
    }

    /**
     * Combine multiple validators
     * Returns the first error message encountered, or null if all validators pass
     */
    fun combine(vararg validators: Validator) = Validator { input ->
        validators.asSequence()
            .map { it.validate(input) }
            .firstOrNull { it != null }
    }

    /**
     * Number range validator
     */
    fun numberRange(min: Long, max: Long) = Validator { input ->
        val number = input.toLongOrNull()
        when {
            number == null -> "Invalid number"
            number < min -> "Value must be at least $min"
            number > max -> "Value must be at most $max"
            else -> null
        }
    }

    /**
     * Pattern validator
     * Validates input against a regex pattern
     */
    fun pattern(regex: Regex, errorMessage: String = "Invalid format") = Validator { input ->
        if (input.isNotBlank() && !input.matches(regex)) errorMessage else null
    }
}