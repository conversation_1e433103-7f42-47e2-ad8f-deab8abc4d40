package com.vietinbank.core_ui.di

import com.vietinbank.core_ui.base.imageloader.GlideImageLoader
import com.vietinbank.core_ui.base.imageloader.IImageLoader
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton

/**
 * Created by vandz on 10/4/25.
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class GlideLoaderModule {

    @Binds
    @Singleton
    @Named("glide")
    abstract fun bindImageLoader(
        glideImageLoader: GlideImageLoader,
    ): IImageLoader
}