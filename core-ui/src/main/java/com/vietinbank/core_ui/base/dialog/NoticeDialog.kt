package com.vietinbank.core_ui.base.dialog

import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.DialogFragment
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_ui.R

/**
 * Dialog thông báo
 * Sử dụng AlertDialog truyền thống để đảm bảo tương thích với toàn bộ hệ thống
 */
class NoticeDialog : DialogFragment() {
    private var btnPositive: Button? = null
    private var btnNegative: Button? = null
    private var btnClose: View? = null
    private var contentView: TextView? = null

    companion object {
        const val ARG_TITLE = "arg_title"
        const val ARG_MESSAGE = "arg_message"
        const val ARG_POSITIVE_BUTTON = "arg_positive_button"
        const val ARG_NEGATIVE_BUTTON = "arg_negative_button"
        const val ARG_SHOW_NEGATIVE = "arg_show_negative"
        const val ARG_CANCELABLE = "arg_cancelable"

        fun newInstance(
            message: String,
            title: String = "Thông báo",
            positiveButtonText: String = "Xác nhận",
            negativeButtonText: String = "Từ chối",
            showNegativeButton: Boolean = false,
            cancelable: Boolean = true,
        ): NoticeDialog {
            val fragment = NoticeDialog()
            val bundle = Bundle()
            bundle.putString(ARG_TITLE, title)
            bundle.putString(ARG_MESSAGE, message)
            bundle.putString(ARG_POSITIVE_BUTTON, positiveButtonText)
            bundle.putString(ARG_NEGATIVE_BUTTON, negativeButtonText)
            bundle.putBoolean(ARG_SHOW_NEGATIVE, showNegativeButton)
            bundle.putBoolean(ARG_CANCELABLE, cancelable)
            fragment.arguments = bundle
            return fragment
        }
    }

    private var onPositiveClick: (() -> Unit)? = null
    private var onNegativeClick: (() -> Unit)? = null
    private var onDismissClick: (() -> Unit)? = null

    fun setOnPositiveClickListener(listener: () -> Unit) {
        onPositiveClick = listener
    }

    fun setOnNegativeClickListener(listener: () -> Unit) {
        onNegativeClick = listener
    }

    fun setOnDismissClickListener(listener: () -> Unit) {
        onDismissClick = listener
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)

        // Clear InputMethodManager references using the activity's current focus
        val activity = activity
        if (activity != null && !activity.isDestroyed) {
            val imm = activity.getSystemService(Context.INPUT_METHOD_SERVICE) as? InputMethodManager
            activity.currentFocus?.let { view ->
                imm?.hideSoftInputFromWindow(view.windowToken, 0)
            }
            // Force clear focus from any view that might have it
            imm?.hideSoftInputFromWindow(activity.window.decorView.windowToken, 0)
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        // Tạo dialog custom view
        val inflater = LayoutInflater.from(context)
        val view: View = inflater.inflate(R.layout.dialog_notice, null)

        // Store references
        contentView = view.findViewById(R.id.tvDialogContent)
        btnPositive = view.findViewById(R.id.btnPositive)
        btnNegative = view.findViewById(R.id.btnNegative)
        btnClose = view.findViewById(R.id.btnClose)

        // Lấy nội dung truyền vào qua Bundle
        val title = arguments?.getString(ARG_TITLE) ?: "Thông báo"
        val message = arguments?.getString(ARG_MESSAGE) ?: ""
//        printLog("NoticeDialog onCreateDialog message: $message")
        val positiveButtonText = arguments?.getString(ARG_POSITIVE_BUTTON) ?: "Xác nhận"
        val negativeButtonText = arguments?.getString(ARG_NEGATIVE_BUTTON) ?: "Từ chối"
        val showNegativeButton = arguments?.getBoolean(ARG_SHOW_NEGATIVE) ?: false

//        printLog("NoticeDialog onCreateDialog showNegativeButton: $showNegativeButton")

        // Thiết lập view
        view.findViewById<TextView>(R.id.tvDialogTitle).text = title
        val contentView = view.findViewById<TextView>(R.id.tvDialogContent)
        contentView.text = message
        printLog("ContentView text set to: ${contentView.text}")

        // Button xác nhận
        val btnPositive = view.findViewById<Button>(R.id.btnPositive)
        btnPositive.text = positiveButtonText
        btnPositive.setOnClickListener {
            onPositiveClick?.invoke()
            dismiss()
        }

        // Button từ chối
        val btnNegative = view.findViewById<Button>(R.id.btnNegative)
        btnNegative.text = negativeButtonText
        btnNegative.visibility = if (showNegativeButton) View.VISIBLE else View.GONE
        btnNegative.setOnClickListener {
            onNegativeClick?.invoke()
            dismiss()
        }

        // Button đóng (X)
        view.findViewById<View>(R.id.btnClose)?.setOnClickListener {
            onDismissClick?.invoke()
            dismiss()
        }

        // Nếu hiển thị 2 button (có button từ chối), thiết lập lại layout constraint
        if (showNegativeButton) {
            // Thay đổi constraint cho btnPositive để chỉ chiếm nửa bên phải
            val positiveParams =
                btnPositive.layoutParams as androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
            positiveParams.startToStart =
                androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET
            positiveParams.startToEnd = R.id.btnNegative
            positiveParams.horizontalBias = 1.0f
            positiveParams.leftMargin = 8
            btnPositive.layoutParams = positiveParams

            // Đảm bảo btnNegative hiển thị và chiếm nửa bên trái
            val negativeParams =
                btnNegative.layoutParams as androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
            negativeParams.width = 0
            negativeParams.endToEnd =
                androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET
            negativeParams.endToStart = R.id.btnPositive
            negativeParams.horizontalBias = 0.0f
            negativeParams.rightMargin = 8
            btnNegative.layoutParams = negativeParams

            btnNegative.visibility = View.VISIBLE
        } else {
            // Nếu chỉ hiển thị một nút, thiết lập lại layout constraint
            val positiveParams =
                btnPositive.layoutParams as androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
            positiveParams.startToStart = R.id.dialogNoticeLayout
            positiveParams.startToEnd =
                androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET
            positiveParams.horizontalBias = 0.5f
            btnPositive.layoutParams = positiveParams

            btnNegative.visibility = View.GONE
        }

        // Tạo dialog
        val dialog = AlertDialog.Builder(requireContext())
            .setView(view)
            .setCancelable(isCancelable)
            .create()

        // Dialog không hiển thị title của AlertDialog
        dialog.requestWindowFeature(android.view.Window.FEATURE_NO_TITLE)

        // Set background transparent để sử dụng custom background
        dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)

        return dialog
    }

    override fun onDestroyView() {
        // Clear all click listeners
        btnPositive?.setOnClickListener(null)
        btnNegative?.setOnClickListener(null)
        btnClose?.setOnClickListener(null)

        // Clear view references
        btnPositive = null
        btnNegative = null
        btnClose = null
        contentView = null

        // Clear callbacks
        onPositiveClick = null
        onNegativeClick = null
        onDismissClick = null
        super.onDestroyView()
    }
}
