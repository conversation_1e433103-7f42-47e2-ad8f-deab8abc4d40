package com.vietinbank.core_ui.base.imageloader

import android.graphics.Bitmap
import android.net.Uri
import android.widget.ImageView
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Created by vand<PERSON> on 10/4/25.
 */
@Singleton
class GlideImageLoader @Inject constructor() : IImageLoader {
    override fun loadUrl(
        imageView: ImageView,
        url: String?,
        isCache: Boolean,
        placeholderRes: Int?,
        errorRes: Int?,
    ) {
        Glide.with(imageView.context)
            .load(url)
            .diskCacheStrategy(if (isCache) DiskCacheStrategy.ALL else DiskCacheStrategy.NONE)
            .apply {
                placeholderRes?.let { placeholder(it) }
                errorRes?.let { error(it) }
            }
            .into(imageView)
    }

    override fun loadBitmap(
        imageView: ImageView,
        bitmap: Bitmap?,
        isCache: Boolean,
        placeholderRes: Int?,
        errorRes: Int?,
    ) {
        Glide.with(imageView.context)
            .load(bitmap)
            .diskCacheStrategy(if (isCache) DiskCacheStrategy.ALL else DiskCacheStrategy.NONE)
            .apply {
                placeholderRes?.let { placeholder(it) }
                errorRes?.let { error(it) }
            }
            .into(imageView)
    }

    override fun loadUri(
        imageView: ImageView,
        uri: Uri?,
        isCache: Boolean,
        placeholderRes: Int?,
        errorRes: Int?,
    ) {
        Glide.with(imageView.context)
            .load(uri)
            .diskCacheStrategy(if (isCache) DiskCacheStrategy.ALL else DiskCacheStrategy.NONE)
            .apply {
                placeholderRes?.let { placeholder(it) }
                errorRes?.let { error(it) }
            }
            .into(imageView)
    }

    override fun loadDrawable(
        imageView: ImageView,
        drawableRes: Int,
        isCache: Boolean,
        placeholderRes: Int?,
        errorRes: Int?,
    ) {
        Glide.with(imageView.context)
            .load(drawableRes)
            .diskCacheStrategy(if (isCache) DiskCacheStrategy.ALL else DiskCacheStrategy.NONE)
            .apply {
                placeholderRes?.let { placeholder(it) }
                errorRes?.let { error(it) }
            }
            .into(imageView)
    }

    @Composable
    override fun LoadUrl(
        url: String?,
        isCache: Boolean,
        placeholderRes: Int?,
        errorRes: Int?,
        modifier: Modifier,
    ) {
        // Dùng AndroidView để wrap ImageView, load Glide.
        AndroidView(
            factory = { context ->
                ImageView(context)
            },
            modifier = modifier,
            update = { imageView ->
                Glide.with(imageView.context)
                    .load(url)
                    .diskCacheStrategy(
                        if (isCache) DiskCacheStrategy.ALL else DiskCacheStrategy.NONE,
                    )
                    .apply {
                        placeholderRes?.let { placeholder(it) }
                        errorRes?.let { error(it) }
                    }
                    .into(imageView)
            },
        )
    }

    @Composable
    override fun LoadByteArray(
        modifier: Modifier,
        byteArray: ByteArray?,
        isCache: Boolean,
        placeholderRes: Int?,
        errorRes: Int?,
    ) {
        AndroidView(
            factory = { context ->
                ImageView(context).apply {
                    scaleType = ImageView.ScaleType.FIT_CENTER
                }
            },
            modifier = modifier,
            update = { imageView ->
                Glide.with(imageView.context)
                    .load(byteArray) // Glide hỗ trợ load từ ByteArray
                    .diskCacheStrategy(
                        if (isCache) DiskCacheStrategy.ALL else DiskCacheStrategy.NONE,
                    )
                    .skipMemoryCache(!isCache)
                    .apply {
                        placeholderRes?.let { placeholder(it) }
                        errorRes?.let { error(it) }
                    }
                    .into(imageView)
            },
        )
    }
}