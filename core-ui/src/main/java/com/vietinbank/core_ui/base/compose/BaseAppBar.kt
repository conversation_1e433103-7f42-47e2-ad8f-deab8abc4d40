package com.vietinbank.core_ui.base.compose

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_common.models.AppBarAction
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.utils.safeClickable

/**
 * Base AppBar component for the application
 *
 * @param title The title to display in the center
 * @param onBackClick Callback when back button is clicked
 * @param actions List of AppBarAction to display on the right side
 * @param customActions Optional custom composable for right side actions.
 * Ex for customActions: customActions = {
 *         Icon(/* sort icon */)
 *         Spacer(modifier = Modifier.width(16.dp))
 *         BaseText(text = "Chọn", /* custom style */)
 *     }
 */

@Composable
fun BaseAppBar(
    title: String,
    onBackClick: () -> Unit,
    actions: List<AppBarAction> = emptyList(),
    customActions: @Composable (() -> Unit)? = null,
    showSelectText: Boolean = false,
    selectText: String = "Chọn",
    isBackHome: Boolean = false,
    onSelectClick: () -> Unit = {},
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(56.dp),
    ) {
        // Left side: Back icon and title
        Row(
            modifier = Modifier.align(Alignment.CenterStart),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Icon(
                painter = painterResource(
                    id = if (isBackHome) {
                        R.drawable.ic_home
                    } else {
                        R.drawable.ic_back
                    },
                ),
                contentDescription = "Back",
                tint = Color.White,
                modifier = Modifier
                    .padding(start = 16.dp)
                    .size(24.dp)
                    .safeClickable { onBackClick() },
            )
            BaseText(
                text = title,
                color = Color.White,
                textSize = 18.sp,
                fontCus = 2,
                textAlign = TextAlign.Start,
                modifier = Modifier.padding(start = 12.dp),
            )
        }

        // Right side: Selection mode, custom actions, or list of AppBarAction
        Row(
            modifier = Modifier
                .align(Alignment.CenterEnd)
                .padding(end = 16.dp),
            horizontalArrangement = Arrangement.End,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            if (showSelectText) {
                Box(
                    modifier = Modifier
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.Transparent)
                        .border(0.5.dp, Color.White, RoundedCornerShape(4.dp))
                        .padding(horizontal = 8.dp, vertical = 4.dp)
                        .safeClickable { onSelectClick() },
                    contentAlignment = Alignment.Center,
                ) {
                    BaseText(
                        text = selectText,
                        color = Color.White,
                        textSize = 12.sp,
                        fontCus = 0,
                    )
                }
                Spacer(modifier = Modifier.width(16.dp))
            }

            if (customActions != null) {
                customActions()
            } else {
                actions.forEach { action ->
                    Icon(
                        painter = painterResource(id = action.icon),
                        contentDescription = action.contentDescription,
                        tint = action.tint,
                        modifier = Modifier
                            .size(24.dp)
                            .safeClickable { action.onClick() },
                    )
                    Spacer(modifier = Modifier.width(16.dp))
                }
            }
        }
    }
}