package com.vietinbank.core_ui.base.dialog

import android.os.Bundle
import android.os.Parcelable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.setFragmentResult
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.dialog.KeypassAuthDialog.Companion.KEYPASS_CODE_LENGTH
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.dialog.DialogLayout
import com.vietinbank.core_ui.components.foundation.pin.OTPPinInput
import com.vietinbank.core_ui.components.foundation.pin.PinState
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.core_ui.utils.commonRoundedCornerCard
import com.vietinbank.core_ui.utils.safeClickable
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay
import javax.inject.Inject
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@AndroidEntryPoint
class KeypassAuthDialog : BaseDialog<Parcelable>() {
    @Inject
    lateinit var appNavigator: IAppNavigator

    companion object {
        const val KEYPASS_CODE_LENGTH = 6
        const val KEYPASS_AUTH_DIALOG_RESULT_KEY = "KEYPASS_AUTH_DIALOG_RESULT_KEY"
        const val RESULT_KEYPASS_VALUE = "RESULT_KEYPASS_VALUE"
        const val CHALLENGE_CODE_KEY = "CHALLENGE_CODE_KEY"

        fun newInstance(
            challengeCode: String,
        ): KeypassAuthDialog {
            return KeypassAuthDialog().apply {
                arguments = Bundle().apply {
                    putString(CHALLENGE_CODE_KEY, challengeCode)
                }
            }
        }
    }

    override val resultKey: String = KEYPASS_AUTH_DIALOG_RESULT_KEY

    override val layout: DialogLayout = DialogLayout.BottomSheet

    @Composable
    override fun DialogContent(
        visible: Boolean,
        onDismissRequest: () -> Unit,
        onResult: (Parcelable) -> Unit,
    ) {
        val keypassCode = arguments?.getString(CHALLENGE_CODE_KEY) ?: ""

        var keypassPinState by remember {
            mutableStateOf(PinState.INPUT)
        }
        var keypassValue by remember {
            mutableStateOf("")
        }

        var isTimeout by remember {
            mutableStateOf(false)
        }

        LaunchedEffect(Unit) {
            delay(60 * 1000L)
            isTimeout = true
        }

        KeypassAuthDialogContent(
            keypassPinState = keypassPinState,
            keypassValue = keypassValue,
            keypassCode = keypassCode,
            onKeypassValueChange = {
                keypassPinState = PinState.INPUT
                keypassValue = it
            },
            onNext = {
                if (isTimeout) {
                    keypassPinState = PinState.ERROR
                } else {
                    setFragmentResult(
                        KEYPASS_AUTH_DIALOG_RESULT_KEY,
                        Bundle().apply {
                            putString(RESULT_KEYPASS_VALUE, keypassValue)
                        },
                    )
                }
            },
            onBack = {
                dismiss()
            },
            onForgot = {
                onDismissRequest.invoke()
                appNavigator.goToForgotKeypass()
            },
        )
    }
}

@Composable
private fun KeypassAuthDialogContent(
    modifier: Modifier = Modifier,
    keypassPinState: PinState,
    keypassValue: String,
    keypassCode: String,
    onKeypassValueChange: (String) -> Unit,
    onBack: () -> Unit,
    onNext: () -> Unit,
    onForgot: () -> Unit,
) {
    var keypassInstructionVisible by remember {
        mutableStateOf(false)
    }

    val nextButtonEnabled by remember(keypassValue.length) {
        derivedStateOf {
            keypassValue.length >= KEYPASS_CODE_LENGTH
        }
    }

    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center,
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Column(
                modifier = Modifier
                    .padding(horizontal = FDS.Sizer.Padding.padding8)
                    .commonRoundedCornerCard(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                FoundationText(
                    stringResource(R.string.keypass_auth_title),
                    style = FDS.Typography.headingH3,
                    color = FDS.Colors.characterHighlighted,
                )
                HorizontalDivider(
                    modifier = Modifier.padding(vertical = (FDS.Sizer.Gap.gap16)),
                    thickness = FDS.Sizer.Stroke.stroke05,
                    color = FDS.Colors.strokeDivider,
                )

                FoundationText(
                    modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
                    text = buildAnnotatedString {
                        append(stringResource(R.string.keypass_auth_description))
                        append(" ")
                        withStyle(
                            style = SpanStyle(
                                color = FDS.Colors.characterHighlightedLighter,
                                fontWeight = FDS.Typography.bodyB1.fontWeight,
                            ),
                        ) {
                            append(keypassCode)
                        }
                    },
                    style = FDS.Typography.bodyB1,
                    color = FDS.Colors.characterPrimary,
                    textAlign = TextAlign.Center,
                )

                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap12))

                FoundationText(
                    stringResource(R.string.keypass_auth_sub_description),
                    style = FDS.Typography.bodyB2,
                    color = FDS.Colors.characterSecondary,
                )

                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

                OTPPinInput(
                    modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
                    pinState = keypassPinState,
                    pinValue = keypassValue,
                    showNumber = true,
                    onPinValueChange = onKeypassValueChange,
                )

                if (keypassPinState == PinState.ERROR) {
                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
                    FoundationText(
                        modifier = Modifier.safeClickable {
                            keypassInstructionVisible = true
                        },
                        text = stringResource(R.string.keypass_auth_fail_message),
                        style = FDS.Typography.captionLSemibold,
                        color = FDS.Colors.stateError,
                    )
                }

                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

                FoundationText(
                    modifier = Modifier.safeClickable {
                        keypassInstructionVisible = true
                    },
                    text = stringResource(R.string.keypass_auth_instruction_button),
                    style = FDS.Typography.captionCaptionL,
                    color = FDS.Colors.characterHighlightedLighter,
                )

                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))

                FoundationText(
                    modifier = Modifier.safeClickable {
                        onForgot.invoke()
                    },
                    text = stringResource(R.string.keypass_auth_forgot_button),
                    style = FDS.Typography.captionCaptionL,
                    color = FDS.Colors.characterHighlightedLighter,
                )
            }

            Row(
                modifier = Modifier.padding(
                    top = FDS.Sizer.Padding.padding16,
                    start = FDS.Sizer.Padding.padding24,
                    end = FDS.Sizer.Padding.padding24,
                    bottom = FDS.Sizer.Padding.padding16,
                ),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center,
            ) {
                FoundationButton(
                    modifier = Modifier.weight(1f),
                    text = stringResource(R.string.common_back),
                    onClick = onBack,
                    isLightButton = false,
                )

                Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap8))

                FoundationButton(
                    modifier = Modifier.weight(1f),
                    text = stringResource(R.string.btn_continue),
                    enabled = nextButtonEnabled,
                    onClick = onNext,
                )
            }
        }
    }

    GetKeypassInstruction(
        visible = keypassInstructionVisible,
        onBack = {
            keypassInstructionVisible = !keypassInstructionVisible
        },
    )
}

@Preview
@Composable
private fun PreviewKeypassAuthDialog() {
    AppTheme {
        KeypassAuthDialogContent(
            keypassPinState = PinState.INPUT,
            keypassValue = "",
            keypassCode = "123456",
            onKeypassValueChange = {},
            onBack = {},
            onNext = {},
            onForgot = {},
        )
    }
}