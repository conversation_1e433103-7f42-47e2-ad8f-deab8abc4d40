package com.vietinbank.core_ui.base.imageloader

import androidx.compose.ui.Modifier
import coil3.ImageLoader

/**
 * Created by vand<PERSON> on 10/4/25.
 *
 * Cung cấp extension function có default params để tiện lợi cho người dùng gọi,
 * thay vì gọi hàm interface gốc không có default.
 */
fun ImageLoader.LoadUrl(
    url: String?,
    modifier: Modifier = Modifier,
    isCache: Boolean = true,
    placeholderRes: Int? = null,
    errorRes: Int? = null,
) {
    LoadUrl(
        url = url,
        isCache = isCache,
        placeholderRes = placeholderRes,
        errorRes = errorRes,
        modifier = modifier,
    )
}