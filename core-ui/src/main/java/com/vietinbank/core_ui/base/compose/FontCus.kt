package com.vietinbank.core_ui.base.compose

/**
 * Created by van<PERSON><PERSON> on 28/2/25.
 */
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ui.R

@Composable
fun getComposeFont(
    fontCus: Int?,
    fontSize: TextUnit = 16.sp,
    fontColor: Color = Color.Unspecified,
): TextStyle {
    val context = LocalContext.current

    // Lấy font từ resource giống như trong XML truyền thống
    val fontFamily = when (fontCus) {
        2 -> FontFamily(Font(R.font.semibold))
        3 -> FontFamily(Font(R.font.heavyitalic))
        4 -> FontFamily(Font(R.font.medium))
        5 -> FontFamily(Font(R.font.bold))
        6 -> FontFamily(Font(R.font.xbold))
        7 -> FontFamily(Font(R.font.semibolditalic))
        8 -> FontFamily(Font(R.font.heavy))
        else -> FontFamily(Font(R.font.regular))
    }

    // Tạo TextStyle với fontFamily, fontSize và color được chỉ định
    return TextStyle(
        fontFamily = fontFamily,
        fontSize = fontSize,
        color = fontColor,
    )
}

@Composable
fun getFontFamily(fontCus: Int?): FontFamily {
    // Lấy font từ resource giống như trong XML truyền thống
    return when (fontCus) {
        2 -> FontFamily(Font(R.font.semibold))
        3 -> FontFamily(Font(R.font.heavyitalic))
        4 -> FontFamily(Font(R.font.medium))
        5 -> FontFamily(Font(R.font.bold))
        6 -> FontFamily(Font(R.font.xbold))
        7 -> FontFamily(Font(R.font.semibolditalic))
        8 -> FontFamily(Font(R.font.heavy))
        else -> FontFamily(Font(R.font.regular))
    }
}
