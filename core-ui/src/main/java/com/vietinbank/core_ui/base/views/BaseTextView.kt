package com.vietinbank.core_ui.base.views

import android.content.Context
import android.graphics.drawable.Drawable
import android.os.Build
import android.text.Html
import android.util.AttributeSet
import androidx.appcompat.content.res.AppCompatResources
import androidx.appcompat.widget.AppCompatTextView
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_ui.R

/**
 * Custom TextView với khả năng nhận các attr tuỳ chỉnh (drawable, font, v.v.)
 * Ví dụ:
 *  <com.vietinbank.core_ui.base.views.BaseTextView
 *      android:layout_width="wrap_content"
 *      android:layout_height="wrap_content"
 *      app:left="@drawable/ic_left"
 *      app:fontCus="1"
 *      ... />
 */
class BaseTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = android.R.attr.textViewStyle,
) : AppCompatTextView(context, attrs, defStyleAttr) {

    init {
        initAttrs(attrs)
    }

    private fun initAttrs(attrs: AttributeSet?) {
        if (attrs == null) {
            return
        }

        try {
            val a = context.obtainStyledAttributes(attrs, R.styleable.BaseTextView)

            // === 1) Set background nếu có ===
            if (a.hasValue(R.styleable.BaseTextView_background)) {
                val bgRes = a.getResourceId(R.styleable.BaseTextView_background, 0)
                if (bgRes != 0) {
                    // Hoặc dùng trực tiếp:
                    background = AppCompatResources.getDrawable(context, bgRes)
                }
            }

            // === 2) Drawable left / top / right / bottom ===
            var left: Drawable? = null
            var top: Drawable? = null
            var right: Drawable? = null
            var bottom: Drawable? = null

            var foundDrawable = false

            val leftResId = a.getResourceId(R.styleable.BaseTextView_left, 0)
            if (leftResId != 0) {
                foundDrawable = true
                left = AppCompatResources.getDrawable(context, leftResId)
            }

            val topResId = a.getResourceId(R.styleable.BaseTextView_top, 0)
            if (topResId != 0) {
                foundDrawable = true
                top = AppCompatResources.getDrawable(context, topResId)
            }

            val rightResId = a.getResourceId(R.styleable.BaseTextView_right, 0)
            if (rightResId != 0) {
                foundDrawable = true
                right = AppCompatResources.getDrawable(context, rightResId)
            }

            val bottomResId = a.getResourceId(R.styleable.BaseTextView_bottom, 0)
            if (bottomResId != 0) {
                foundDrawable = true
                bottom = AppCompatResources.getDrawable(context, bottomResId)
            }

            if (foundDrawable) {
                setCompoundDrawablesWithIntrinsicBounds(left, top, right, bottom)
            }

            // === 3) Font tùy chọn ===
            if (a.hasValue(R.styleable.BaseTextView_fontCus)) {
                val fontType = a.getInt(R.styleable.BaseTextView_fontCus, 0)

                typeface = Utils.g().getFont(fontType, context)
            } else {
                typeface = Utils.g().getFont(1, context)
            }

            // === 4) Check property (selected)
            if (a.hasValue(R.styleable.BaseTextView_check)) {
                val isCheck = a.getBoolean(R.styleable.BaseTextView_check, false)
                isSelected = isCheck
            }

            // === 5) HTML text
            if (a.hasValue(R.styleable.BaseTextView_html)) {
                val htmlStr = a.getString(R.styleable.BaseTextView_html)

                htmlStr?.let {
                    text = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        Html.fromHtml(it, Html.FROM_HTML_MODE_COMPACT)
                    } else {
                        Html.fromHtml(it)
                    }
                }
            }

            a.recycle()
        } catch (e: Exception) {
            printLog("BaseTextView Lỗi trong initAttrs: ${e.message}")
        } finally {
//            printLog("BaseTextView ==== initAttrs END ====")
        }

        includeFontPadding = true
    }
}