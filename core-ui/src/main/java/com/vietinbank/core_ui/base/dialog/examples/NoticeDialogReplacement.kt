package com.vietinbank.core_ui.base.dialog.examples

import android.os.Parcelable
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.fragment.app.FragmentManager
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.dialog.BaseDialog
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.dialog.CancellableResult
import com.vietinbank.core_ui.components.dialog.DialogLayout
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.safeClickable
import kotlinx.parcelize.Parcelize
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Result for notice dialog replacement
 */
@Parcelize
data class NoticeResult(
    val action: String, // "positive", "negative", or "dismissed"
    val timestamp: Long = System.currentTimeMillis(),
) : Parcelable

/**
 * Modern replacement for NoticeDialog using BaseDialog architecture
 *
 * This dialog provides all the functionality of the old NoticeDialog
 * but with improved architecture, animations, and state management.
 *
 * Migration example:
 *
 * OLD WAY (NoticeDialog):
 * ```kotlin
 * val dialog = NoticeDialog.newInstance(
 *     message = "Transaction completed successfully",
 *     title = "Success",
 *     positiveButtonText = "OK",
 *     negativeButtonText = "View Details",
 *     showNegativeButton = true,
 *     cancelable = true
 * )
 * dialog.setOnPositiveClickListener {
 *     // Handle positive
 * }
 * dialog.setOnNegativeClickListener {
 *     // Handle negative
 * }
 * dialog.show(supportFragmentManager, "notice")
 * ```
 *
 * NEW WAY (NoticeDialogReplacement):
 * ```kotlin
 * NoticeDialogReplacement.show(
 *     fragmentManager = supportFragmentManager,
 *     title = "Success",
 *     message = "Transaction completed successfully",
 *     positiveButtonText = "OK",
 *     negativeButtonText = "View Details",
 *     showNegativeButton = true,
 *     cancelable = true,
 *     onResult = { result ->
 *         when (result.action) {
 *             "positive" -> // Handle positive
 *             "negative" -> // Handle negative
 *             "dismissed" -> // Handle dismissal
 *         }
 *     }
 * )
 * ```
 */
class NoticeDialogReplacement : BaseDialog<NoticeResult>(), CancellableResult {

    companion object {
        private const val ARG_TITLE = "arg_title"
        private const val ARG_MESSAGE = "arg_message"
        private const val ARG_POSITIVE_BUTTON = "arg_positive_button"
        private const val ARG_NEGATIVE_BUTTON = "arg_negative_button"
        private const val ARG_SHOW_NEGATIVE = "arg_show_negative"
        private const val ARG_CANCELABLE = "arg_cancelable"
        private const val ARG_SHOW_CLOSE_BUTTON = "arg_show_close_button"

        fun show(
            fragmentManager: FragmentManager,
            message: String,
            title: String = "Thông báo",
            positiveButtonText: String = "Xác nhận",
            negativeButtonText: String = "Từ chối",
            showNegativeButton: Boolean = false,
            cancelable: Boolean = true,
            showCloseButton: Boolean = true,
            onResult: ((NoticeResult) -> Unit)? = null,
        ) {
            val dialog = NoticeDialogReplacement().apply {
                arguments = android.os.Bundle().apply {
                    putString(ARG_TITLE, title)
                    putString(ARG_MESSAGE, message)
                    putString(ARG_POSITIVE_BUTTON, positiveButtonText)
                    putString(ARG_NEGATIVE_BUTTON, negativeButtonText)
                    putBoolean(ARG_SHOW_NEGATIVE, showNegativeButton)
                    putBoolean(ARG_CANCELABLE, cancelable)
                    putBoolean(ARG_SHOW_CLOSE_BUTTON, showCloseButton)
                }

                this.onResultCallback = onResult
            }

            dialog.show(fragmentManager, "NoticeDialogReplacement")
        }

        /**
         * Convenience method for simple OK dialog
         */
        fun showSimple(
            fragmentManager: FragmentManager,
            message: String,
            title: String = "Thông báo",
            buttonText: String = "Đồng ý",
            onDismiss: (() -> Unit)? = null,
        ) {
            show(
                fragmentManager = fragmentManager,
                message = message,
                title = title,
                positiveButtonText = buttonText,
                showNegativeButton = false,
                cancelable = true,
                onResult = { onDismiss?.invoke() },
            )
        }

        /**
         * Convenience method for error dialog
         */
        fun showError(
            fragmentManager: FragmentManager,
            message: String,
            title: String = "Lỗi",
            onDismiss: (() -> Unit)? = null,
        ) {
            show(
                fragmentManager = fragmentManager,
                message = message,
                title = title,
                positiveButtonText = "Đóng",
                showNegativeButton = false,
                cancelable = true,
                onResult = { onDismiss?.invoke() },
            )
        }
    }

    private var onResultCallback: ((NoticeResult) -> Unit)? = null

    // Dialog configuration
    override val resultKey = "notice_result"
    override val layout = DialogLayout.Center
    override val allowTouchDismiss: Boolean
        get() = arguments?.getBoolean(ARG_CANCELABLE, true) ?: true
    override val requiresSecureFlag = false
    override val maxWidthDp = 400

    @Composable
    override fun DialogContent(
        visible: Boolean,
        onDismissRequest: () -> Unit,
        onResult: (NoticeResult) -> Unit,
    ) {
        val title = arguments?.getString(ARG_TITLE) ?: "Thông báo"
        val message = arguments?.getString(ARG_MESSAGE) ?: ""
        val positiveButtonText = arguments?.getString(ARG_POSITIVE_BUTTON) ?: "Xác nhận"
        val negativeButtonText = arguments?.getString(ARG_NEGATIVE_BUTTON) ?: "Từ chối"
        val showNegativeButton = arguments?.getBoolean(ARG_SHOW_NEGATIVE, false) ?: false
        val showCloseButton = arguments?.getBoolean(ARG_SHOW_CLOSE_BUTTON, true) ?: true

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(FDS.Sizer.Padding.padding24),
        ) {
            // Header with title and close button
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                FoundationText(
                    text = title,
                    style = FDS.Typography.headingH3,
                    color = FDS.Colors.characterPrimary,
                    modifier = Modifier.weight(1f),
                )

                if (showCloseButton && allowTouchDismiss) {
                    Box(
                        modifier = Modifier
                            .size(28.dp)
                            .clip(CircleShape)
                            .background(FDS.Colors.backgroundBgOnColor)
                            .safeClickable {
                                onResult(NoticeResult("dismissed"))
                            },
                        contentAlignment = Alignment.Center,
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_close),
                            contentDescription = "Đóng",
                            modifier = Modifier.size(16.dp),
                            tint = FDS.Colors.characterSecondary,
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))

            // Message content
            FoundationText(
                text = message,
                style = FDS.Typography.bodyB1,
                color = FDS.Colors.characterSecondary,
                textAlign = TextAlign.Start,
                modifier = Modifier.fillMaxWidth(),
            )

            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

            // Action buttons
            if (showNegativeButton) {
                // Two buttons layout
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap12),
                ) {
                    FoundationButton(
                        text = negativeButtonText,
                        onClick = {
                            onResult(NoticeResult("negative"))
                        },
                        isLightButton = true,
                        modifier = Modifier.weight(1f),
                    )

                    FoundationButton(
                        text = positiveButtonText,
                        onClick = {
                            onResult(NoticeResult("positive"))
                        },
                        modifier = Modifier.weight(1f),
                    )
                }
            } else {
                // Single button layout (centered)
                FoundationButton(
                    text = positiveButtonText,
                    onClick = {
                        onResult(NoticeResult("positive"))
                    },
                    modifier = Modifier.fillMaxWidth(),
                )
            }
        }
    }

    override fun getCancellationResult(): String {
        return "dismissed"
    }

    // NOTE: onResult and onCancelled methods removed from BaseDialog to prevent listener conflicts
    // Results are now handled via Fragment Result API in the parent Fragment/Activity
}

/**
 * Extension function for backward compatibility
 * Allows using similar API to old NoticeDialog
 */
fun FragmentManager.showNoticeDialog(
    message: String,
    title: String = "Thông báo",
    positiveButtonText: String = "Xác nhận",
    negativeButtonText: String = "Từ chối",
    showNegativeButton: Boolean = false,
    cancelable: Boolean = true,
    onPositiveClick: (() -> Unit)? = null,
    onNegativeClick: (() -> Unit)? = null,
    onDismiss: (() -> Unit)? = null,
) {
    NoticeDialogReplacement.show(
        fragmentManager = this,
        message = message,
        title = title,
        positiveButtonText = positiveButtonText,
        negativeButtonText = negativeButtonText,
        showNegativeButton = showNegativeButton,
        cancelable = cancelable,
        onResult = { result ->
            when (result.action) {
                "positive" -> onPositiveClick?.invoke()
                "negative" -> onNegativeClick?.invoke()
                "dismissed" -> onDismiss?.invoke()
            }
        },
    )
}