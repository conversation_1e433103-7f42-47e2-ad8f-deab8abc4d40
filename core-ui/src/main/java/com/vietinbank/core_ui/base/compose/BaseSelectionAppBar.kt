package com.vietinbank.core_ui.base.compose

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.utils.safeClickable

/**
 * App bar for selection mode
 */
@Preview
@Composable
fun BaseSelectionAppBar(
    selectedCount: Int = 0,
    totalCount: Int = 0,
    onCloseClick: (() -> Unit)? = null,
) {
    Box(
        modifier = Modifier
            .statusBarsPadding()
            .height(56.dp),
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 12.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // Left section with selection text
            Row(
                modifier = Modifier.weight(1f),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                // Checkbox indicator
                Box(
                    modifier = Modifier
                        .size(20.dp)
                        .background(Color.White.copy(alpha = 0.2f), CircleShape)
                        .border(
                            width = 0.5.dp,
                            color = Color.White,
                            shape = CircleShape,
                        ),
                    contentAlignment = Alignment.Center,
                ) {
                    if (selectedCount == totalCount && totalCount > 0) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_radio_check),
                            contentDescription = "All Selected",
                            tint = Color.White,
                            modifier = Modifier.size(14.dp),
                        )
                    }
                }

                Spacer(modifier = Modifier.width(8.dp))

                Text(
                    text = "Đã chọn $selectedCount/$totalCount giao dịch",
                    style = getComposeFont(4, 14.sp, Color.White),
                )
            }

            // Right section with selection count and close button
            Row(
                verticalAlignment = Alignment.CenterVertically,
            ) {
                // Close (X) button
                Box(
                    modifier = Modifier
                        .size(32.dp)
                        .clip(CircleShape)
                        .background(Color.White.copy(alpha = 0.2f))
                        .safeClickable { onCloseClick?.invoke() },
                    contentAlignment = Alignment.Center,
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_close),
                        contentDescription = "Close selection mode",
                        tint = Color.White,
                        modifier = Modifier.size(16.dp),
                    )
                }
            }
        }
    }
}