package com.vietinbank.core_ui.base.compose

import androidx.annotation.DrawableRes
import androidx.compose.animation.animateColor
import androidx.compose.animation.core.animateDp
import androidx.compose.animation.core.animateInt
import androidx.compose.animation.core.updateTransition
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.coerceIn
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.InputFilters
import com.vietinbank.core_ui.utils.safeClickable

@Composable
fun BaseInputText(
    modifier: Modifier = Modifier,
    label: String? = null,
    value: String,
    placeholder: String? = null,
    prefixEnd: String? = null,
    errorMessage: String? = null,
    isShowClear: Boolean = false,
    isShowLine: Boolean = false,
    singleLine: Boolean = true,
    isAmount: Boolean = false,
    maxLines: Int = 1,
    maxLength: Int = Int.MAX_VALUE,
    keyboardType: KeyboardType = KeyboardType.Text,
    imeAction: ImeAction = ImeAction.Done,
    pattern: Regex? = null,
    @DrawableRes iconEnd: Int? = null,
    onIconClick: ((Int) -> Unit)? = null, // 0 start, 1 top, 2 end, 3 bottom
    onValueChange: (String) -> Unit,
    onFocusChanged: ((Boolean) -> Unit)? = null,
) {
    // Theo dõi focus
    val focusRequester = remember { FocusRequester() }
    var isFocused by remember { mutableStateOf(false) }

    val transition = updateTransition(
        targetState = isFocused || value.isNotBlank(),
        label = "LabelTransition",
    )

    val labelFontSize by transition.animateInt(label = "LabelFontSize") { focused ->
        if (focused) 14 else 14
    }

    val labelFontCus by transition.animateInt(label = "labelFontCus") { focused ->
        if (focused) 2 else 2
    }

    val labelColor by transition.animateColor(label = "labelColor") { focused ->
        if (focused) AppColors.blue07 else AppColors.blue07
    }
    val labelOffsetY by transition.animateDp(label = "LabelOffsetY") { focused ->
        if (focused) 0.dp else 4.dp
    }
    val inputOffsetY by transition.animateDp(label = "InputOffsetY") { focused ->
        if (!placeholder.isNullOrEmpty() || !focused) 0.dp else 20.dp
    }

    // Xác định keyboardType dựa trên pattern nếu cần
    val finalKeyboardType = if (pattern != null && keyboardType == KeyboardType.Text) {
        InputFilters.getKeyboardOptions(pattern).keyboardType
    } else {
        keyboardType
    }

    val formattedValue = remember(value, isAmount) {
        if (isAmount) Utils.g().getDotMoney(value) else value
    }
    var textFieldValue by remember(formattedValue) {
        mutableStateOf(
            TextFieldValue(
                text = formattedValue,
                TextRange(formattedValue.length).coerceIn(0, formattedValue.length),
            ),
        )
    }

    // Xử lý onValueChange với pattern nếu có
    val handleValueChange: (String) -> Unit = remember(isAmount, pattern, maxLength) {
        {
                newText ->
            try {
                val cleanText = if (isAmount) {
                    newText.filter { it.isDigit() }.toLongOrNull().toString()
                } else {
                    newText.trimStart()
                }

                val filteredText = pattern?.let {
                    InputFilters.filterText(cleanText, it)
                } ?: cleanText

                val finalText =
                    if (filteredText.length <= maxLength) {
                        filteredText
                    } else {
                        filteredText.take(
                            maxLength,
                        )
                    }
                val displayText = if (isAmount) Utils.g().getDotMoney(finalText) else finalText

                textFieldValue = TextFieldValue(
                    displayText, TextRange(displayText.length).coerceIn(0, displayText.length),
                )
                onValueChange(finalText)
            } catch (_: Exception) {
            }
        }
    }

    Column(modifier = modifier) {
        Row(modifier = Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
            // co placeholder
            if (!placeholder.isNullOrEmpty() && value.isEmpty()) {
                Text(
                    text = placeholder,
                    style = getComposeFont(4, 12.sp, AppColors.grey07),
                )
            }

            Box(
                modifier = Modifier
                    .weight(1f)
                    .background(Color.White),
            ) {
                if (placeholder.isNullOrEmpty() && !label.isNullOrEmpty()) {
                    // khong co placeholder
                    Text(
                        text = label,
                        style = getComposeFont(labelFontCus, labelFontSize.sp, labelColor),
                        modifier = Modifier
                            .offset(y = labelOffsetY)
                            .zIndex(1f),
                    )
                }

                // Input Field

                BasicTextField(
                    modifier = Modifier
                        .padding(top = inputOffsetY)
                        .fillMaxWidth()
                        .focusRequester(focusRequester)
                        .onFocusChanged {
                            isFocused = it.isFocused
                            onFocusChanged?.invoke(it.isFocused)
                        },
                    value = textFieldValue,
                    onValueChange = {
                        if (it.text.length <= maxLength) {
                            handleValueChange(it.text)
                        }
                    },
                    textStyle = getComposeFont(2, 16.sp, AppColors.blue02),
                    singleLine = singleLine,
                    maxLines = maxLines,
                    keyboardOptions = KeyboardOptions(
                        keyboardType = finalKeyboardType,
                        imeAction = imeAction,
                    ),
                    cursorBrush = SolidColor(AppColors.blue02),
                )
            }

            // clear icon
            if (isShowClear && value.isNotEmpty() && isFocused) {
                Image(
                    painter = painterResource(R.drawable.ic_close),
                    contentDescription = null,
                    modifier = Modifier
                        .padding(start = 4.dp)
                        .safeClickable {
                            handleValueChange("")
                        },
                )
            }

            // end icon
            iconEnd?.let {
                Image(
                    painter = painterResource(it),
                    contentDescription = null,
                    modifier = Modifier
                        .padding(start = 4.dp)
                        .safeClickable {
                            onIconClick?.invoke(2)
                        },
                )
            }

            prefixEnd?.let {
                Text(
                    modifier = Modifier.padding(start = 4.dp),
                    text = it,
                    style = getComposeFont(4, 12.sp, AppColors.descriptionGrey),
                )
            }
        }

        if (isShowLine) {
            Spacer(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 4.dp)
                    .height(1.dp)
                    .background(AppColors.blue09),
            )
        }

        // Error message
        errorMessage?.let {
            Text(
                text = it,
                color = Color.Red,
                fontSize = 12.sp,
                modifier = Modifier.padding(start = 4.dp, top = 4.dp),
            )
        }
    }
}
