package com.vietinbank.core_ui.base.views

import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ScrollableTabRow
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRowDefaults
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ui.base.compose.BaseText

@Composable
fun BaseTabLayout(
    modifier: Modifier = Modifier,
    tabs: List<String>,
    selectedTabIndex: Int,
    onTabSelected: (Int) -> Unit,
) {
    ScrollableTabRow(
        edgePadding = 10.dp,
        selectedTabIndex = selectedTabIndex,
        containerColor = Color.Transparent,
        contentColor = Color.White,
        indicator = { tabPositions ->
            TabRowDefaults.Indicator(
                Modifier
                    .tabIndicatorOffset(tabPositions[selectedTabIndex])
                    .height(2.dp),
                color = Color.White,
            )
        },
        divider = {}, // Không có gạch chân giữa các tab
    ) {
        tabs.forEachIndexed { index, title ->
            Tab(
                modifier = Modifier.padding(horizontal = 4.dp),
                selected = selectedTabIndex == index,
                onClick = { onTabSelected(index) },
                selectedContentColor = Color.White,
                unselectedContentColor = Color.White.copy(alpha = 0.6f),
            ) {
                BaseText(
                    text = title,
                    modifier = Modifier.padding(vertical = 12.dp),
                    textSize = 12.sp,
//                        fontWeight = if (selectedTabIndex == index) FontWeight.Bold else FontWeight.Normal
                )
            }
        }
    }
}
