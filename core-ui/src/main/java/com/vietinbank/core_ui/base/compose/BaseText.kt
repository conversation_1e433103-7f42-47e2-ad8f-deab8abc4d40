package com.vietinbank.core_ui.base.compose

import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ui.utils.safeClickable

/**
 * Thay cho default TextView.
 *
 * - Tự nhận các param: leftDrawable, rightDrawable, fontCus, htmlText, checkSelected, v.v.
 * - Dùng [Text] + [Icon] thay cho compound drawables cũ.
 * - "background" cũ có thể thực hiện qua Modifier.background(...) (nếu là màu)
 *   hoặc 1 Box lồng Image (nếu là ảnh).
 */

@Composable
fun BaseText(
    text: String,
    modifier: Modifier = Modifier,
    leftDrawable: Int? = null,
    leftDrawableTint: Color = Color.Unspecified,
    leftDrawableSize: Dp = 16.dp,
    rightDrawable: Int? = null,
    rightDrawableTint: Color = Color.Unspecified,
    rightDrawableSize: Dp = 16.dp,
    fontCus: Int? = null,
    color: Color = Color.Unspecified,
    textColor: Color = Color.Unspecified,
    textSize: TextUnit = 16.sp,
    fontWeight: FontWeight? = null,
    textAlign: TextAlign = TextAlign.Start,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Clip,
    letterSpacing: TextUnit = TextUnit.Unspecified,
    lineHeight: TextUnit = TextUnit.Unspecified,
    textDecoration: TextDecoration? = null,
    htmlText: Boolean = false,
    checkSelected: Boolean = false,
    backgroundColor: Color = Color.Transparent,
    padding: PaddingValues = PaddingValues(0.dp),
    onClick: (() -> Unit)? = null,
    clickTimeWindow: Long = 1000,
) {
    // Use textColor if provided, otherwise fall back to color
    val finalColor = if (textColor != Color.Unspecified) textColor else color

    // Tạo style Compose từ fontCus
    var textStyle = getComposeFont(fontCus).copy(
        color = finalColor,
        fontSize = textSize,
        textAlign = textAlign,
        letterSpacing = letterSpacing,
        lineHeight = lineHeight,
    )

    // Áp dụng font weight nếu được cung cấp
    if (fontWeight != null) {
        textStyle = textStyle.copy(fontWeight = fontWeight)
    }

    // Áp dụng text decoration nếu được cung cấp
    if (textDecoration != null) {
        textStyle = textStyle.copy(textDecoration = textDecoration)
    }

    // Xử lý "isSelected" = checkSelected
    val finalStyle = if (checkSelected) {
        textStyle.copy(
            // Ví dụ highlight khi được chọn
            background = Color.Yellow,
        )
    } else {
        textStyle
    }

    // Parse HTML -> plain text
    val displayText = if (htmlText) parseHtmlToPlainText(text) else text

    // Logic clickable
    val finalModifier = if (onClick != null) {
        modifier
            .background(backgroundColor)
            .padding(padding)
            .safeClickable(timeWindow = clickTimeWindow) { onClick() }
    } else {
        modifier
            .background(backgroundColor)
            .padding(padding)
    }

    Row(
        modifier = finalModifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Start,
    ) {
        // Left icon
        if (leftDrawable != null && leftDrawable != 0) {
            Icon(
                painter = painterResource(leftDrawable),
                contentDescription = null,
                tint = leftDrawableTint,
                modifier = Modifier
                    .size(leftDrawableSize)
                    .wrapContentWidth(),
            )
            Spacer(modifier = Modifier.width(4.dp))
        }

        // Main text
        Text(
            text = displayText,
            style = finalStyle,
            maxLines = maxLines,
            overflow = overflow,
        )

        // Right icon
        if (rightDrawable != null && rightDrawable != 0) {
            Spacer(modifier = Modifier.width(4.dp))
            Icon(
                painter = painterResource(rightDrawable),
                contentDescription = null,
                tint = rightDrawableTint,
                modifier = Modifier
                    .size(rightDrawableSize)
                    .wrapContentWidth(),
            )
        }
    }
}

/**
 * Nếu cũ có "Html.fromHtml(...)",
 * ta tạm parse -> plain text (hoặc custom).
 */
@RequiresApi(Build.VERSION_CODES.N)
@Composable
fun parseHtmlToPlainText(html: String): String {
    return remember(html) {
        android.text.Html.fromHtml(html, android.text.Html.FROM_HTML_MODE_COMPACT).toString()
    }
}
