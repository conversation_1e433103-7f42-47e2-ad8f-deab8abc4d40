package com.vietinbank.core_ui.base.dialog.examples

import android.os.Parcelable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.fragment.app.FragmentManager
import com.vietinbank.core_ui.base.dialog.BaseDialog
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.dialog.DialogLayout
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import kotlinx.parcelize.Parcelize
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Result for confirmation dialog
 */
@Parcelize
data class ConfirmationResult(
    val confirmed: Boolean,
    val timestamp: Long = System.currentTimeMillis(),
) : Parcelable

/**
 * Simple confirmation dialog example showing BaseDialog usage
 *
 * Usage:
 * ```kotlin
 * ConfirmationDialog.show(
 *     fragmentManager = childFragmentManager,
 *     title = "Xác nhận",
 *     message = "Bạn có chắc chắn muốn thực hiện thao tác này?",
 *     positiveText = "Đồng ý",
 *     negativeText = "Hủy",
 *     onResult = { result ->
 *         if (result.confirmed) {
 *             // User confirmed
 *         } else {
 *             // User cancelled
 *         }
 *     }
 * )
 * ```
 */
class ConfirmationDialog : BaseDialog<ConfirmationResult>() {

    companion object {
        private const val ARG_TITLE = "arg_title"
        private const val ARG_MESSAGE = "arg_message"
        private const val ARG_POSITIVE_TEXT = "arg_positive_text"
        private const val ARG_NEGATIVE_TEXT = "arg_negative_text"
        private const val ARG_ICON_RES = "arg_icon_res"

        fun show(
            fragmentManager: FragmentManager,
            title: String,
            message: String,
            positiveText: String = "Xác nhận",
            negativeText: String = "Hủy",
            iconRes: Int? = null,
            onResult: ((ConfirmationResult) -> Unit)? = null,
        ) {
            val dialog = ConfirmationDialog().apply {
                arguments = android.os.Bundle().apply {
                    putString(ARG_TITLE, title)
                    putString(ARG_MESSAGE, message)
                    putString(ARG_POSITIVE_TEXT, positiveText)
                    putString(ARG_NEGATIVE_TEXT, negativeText)
                    iconRes?.let { putInt(ARG_ICON_RES, it) }
                }

                this.onResultCallback = onResult
            }

            dialog.show(fragmentManager, "ConfirmationDialog")
        }
    }

    private var onResultCallback: ((ConfirmationResult) -> Unit)? = null

    // Dialog configuration
    override val resultKey = "confirmation_result"
    override val layout = DialogLayout.Center
    override val allowTouchDismiss = false // Force user to make a choice
    override val requiresSecureFlag = false
    override val maxWidthDp = 400

    @Composable
    override fun DialogContent(
        visible: Boolean,
        onDismissRequest: () -> Unit,
        onResult: (ConfirmationResult) -> Unit,
    ) {
        val title = arguments?.getString(ARG_TITLE) ?: ""
        val message = arguments?.getString(ARG_MESSAGE) ?: ""
        val positiveText = arguments?.getString(ARG_POSITIVE_TEXT) ?: "Xác nhận"
        val negativeText = arguments?.getString(ARG_NEGATIVE_TEXT) ?: "Hủy"
        val iconRes = arguments?.getInt(ARG_ICON_RES, 0)?.takeIf { it != 0 }

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(FDS.Sizer.Padding.padding24),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            // Icon (optional)
            iconRes?.let { res ->
                Icon(
                    painter = painterResource(id = res),
                    contentDescription = null,
                    modifier = Modifier.size(48.dp),
                    tint = FDS.Colors.stateWarning,
                )

                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
            }

            // Title
            if (title.isNotEmpty()) {
                FoundationText(
                    text = title,
                    style = FDS.Typography.headingH3,
                    color = FDS.Colors.characterPrimary,
                    textAlign = TextAlign.Center,
                )

                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap12))
            }

            // Message
            FoundationText(
                text = message,
                style = FDS.Typography.bodyB1,
                color = FDS.Colors.characterSecondary,
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth(),
            )

            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

            // Action buttons
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap12),
            ) {
                FoundationButton(
                    text = negativeText,
                    onClick = {
                        onResult(ConfirmationResult(confirmed = false))
                    },
                    isLightButton = true,
                    modifier = Modifier.weight(1f),
                )

                FoundationButton(
                    text = positiveText,
                    onClick = {
                        onResult(ConfirmationResult(confirmed = true))
                    },
                    modifier = Modifier.weight(1f),
                )
            }
        }
    }

    // NOTE: onResult method removed from BaseDialog to prevent listener conflicts
    // Results are now handled via Fragment Result API in the parent Fragment/Activity
}