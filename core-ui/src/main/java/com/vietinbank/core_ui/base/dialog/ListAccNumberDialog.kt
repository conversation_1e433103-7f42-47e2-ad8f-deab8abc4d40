package com.vietinbank.core_ui.base.dialog

import android.annotation.SuppressLint
import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.graphics.toColorInt
import androidx.fragment.app.DialogFragment
import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_ui.databinding.ListAccNumberDialogBinding

class ListAccNumberDialog() : DialogFragment() {

    companion object {
        private const val ARG_TITLE = "arg_title"
        private const val ARG_LIST_ACC = "arg_list_acc"

        fun newInstance(title: String, listAcc: List<String>): ListAccNumberDialog {
            val json = Utils.g().provideGson().toJson(listAcc)
            val frag = ListAccNumberDialog()
            val bundle = Bundle()
            bundle.putString(ARG_TITLE, title)
            bundle.putString(ARG_LIST_ACC, json)
            frag.arguments = bundle
            return frag
        }
    }

    private var _binding: ListAccNumberDialogBinding? = null
    private val binding get() = _binding!!

    private var onItemClickListener: ((String) -> Unit)? = null
    private var onDismissClick: (() -> Unit)? = null

    fun setOnItemClickListener(listener: (String) -> Unit) {
        onItemClickListener = listener
    }

    @SuppressLint("UseGetLayoutInflater")
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        _binding = ListAccNumberDialogBinding.inflate(LayoutInflater.from(context))
        val view = binding.root

        val data = arguments?.getString(ARG_LIST_ACC)
        val type = object : TypeToken<List<String>>() {}.type
        val accNumberList = Utils.g().provideGson().fromJson<List<String>>(data, type)
        val title = arguments?.getString(ARG_TITLE) ?: "Số tài khoản nhận BĐSD"

        binding.tvDialogTitle.text = title
        binding.btnClose.setOnClickListener {
            onDismissClick?.invoke()
            dismiss()
        }

        accNumberList.forEach { accNumber ->
            val textView = TextView(requireContext()).apply {
                text = accNumber
                setPadding(16, 16, 16, 16)
                textSize = 16f
                setTextColor("#505050".toColorInt())
                setOnClickListener {
                    onItemClickListener?.invoke(accNumber)
                    dismiss()
                }
            }
            binding.accNumberListContainer.addView(textView)
            val divider = View(requireContext()).apply {
                layoutParams = LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    2,
                ).apply {
                    setMargins(0, 0, 0, 0)
                }
                setBackgroundColor("#DFF1F9".toColorInt())
            }
            binding.accNumberListContainer.addView(divider)
        }
        return view
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = Dialog(requireContext())
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)
        return dialog
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}