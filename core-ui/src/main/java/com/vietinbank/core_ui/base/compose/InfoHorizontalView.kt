package com.vietinbank.core_ui.base.compose

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable

@Preview
@Composable
fun InfoHorizontalView(
    modifier: Modifier = Modifier,
    title: String? = null,
    titleFont: Int = 4,
    titleColor: Color = AppColors.blue07,
    titleSize: TextUnit = 14.sp,
    value: String? = null,
    valueFont: Int = 2,
    valueColor: Color = AppColors.blue02,
    valueSize: TextUnit = 14.sp,
    valueTwo: String? = null,
    onClick: (() -> Unit)? = null,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(Color.White),
        verticalAlignment = Alignment.Top,
    ) {
        Text(
            text = title ?: "",
            style = getComposeFont(titleFont, titleSize, titleColor),
            modifier = Modifier.weight(0.35f),
        )

        Spacer(modifier = Modifier.width(16.dp))

        Column(
            modifier = Modifier.weight(0.65f),
            horizontalAlignment = Alignment.End,
        ) {
            Text(
                modifier = Modifier.safeClickable { onClick?.invoke() },
                text = value ?: "",
                style = getComposeFont(valueFont, valueSize, valueColor),
                textAlign = TextAlign.End,
            )

            valueTwo?.let {
                Text(
                    text = it,
                    style = getComposeFont(4, 14.sp, AppColors.blue02),
                    textAlign = TextAlign.End,
                    modifier = Modifier.padding(top = 4.dp),
                )
            }
        }
    }
}