package com.vietinbank.core_ui.base.compose

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Icon
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.InputFilters

/**
 * Thay cho default TextField.
 * - Nhận param leftDrawable, rightDrawable, background, fontCus, ...
 * - Sử dụng OutlinedTextField (hoặc TextField).
 */

@Composable
fun BaseEditText(
    value: String,
    label: String = "",
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    hint: String = "",
    hintColor: Color = Color.Gray,
    textColor: Color = Color.Black,
    textSize: TextUnit = 16.sp,
    leftDrawable: Int? = null,
    leftComposable: (@Composable () -> Unit)? = null,
    leftDrawableTint: Color = Color.Unspecified,
    rightDrawable: Int? = null,
    rightComposable: (@Composable () -> Unit)? = null,
    rightDrawableTint: Color = Color.Unspecified,
    onRightDrawableClick: (() -> Unit)? = null,
    showClearButton: Boolean = false, // Flag để hiển thị nút xóa
    clearIcon: Int? = null, // Icon cho nút xóa, nếu null sẽ dùng icon mặc định
    clearIconTint: Color = Color.White, // Màu của icon xóa
    fontCus: Int? = null,
    isPassword: Boolean = false,
    enabled: Boolean = true,
    isError: Boolean = false,
    errorText: String? = null,
    keyboardType: KeyboardType = KeyboardType.Text,
    imeAction: ImeAction = ImeAction.Done,
    onFocusChanged: ((Boolean) -> Unit)? = null,
    maxLines: Int = 1,
    singleLine: Boolean = true,
    backgroundColor: Color = Color.Transparent,
    borderColor: Color = Color.Gray,
    borderWidth: Dp = 1.dp,
    borderRadius: Dp = 4.dp,
    iconSize: Dp = 20.dp,
    // Thêm tham số pattern mới
    pattern: Regex? = null,
    isCounter: Boolean = false, // Flag để hiển thị input length
    maxLength: Int = Int.MAX_VALUE, // giới hạn số ký tự input

) {
    // Xử lý onValueChange với pattern nếu có
    val finalOnValueChange: (String) -> Unit = if (pattern != null) {
        { newText ->
            if (newText.isEmpty() || newText.matches(pattern)) {
                onValueChange(newText)
            } else {
                val filteredText = InputFilters.filterText(newText, pattern)
                onValueChange(filteredText)
            }
        }
    } else {
        onValueChange
    }

    // Xác định keyboardType dựa trên pattern nếu cần
    val finalKeyboardType = if (pattern != null && keyboardType == KeyboardType.Text) {
        InputFilters.getKeyboardOptions(pattern).keyboardType
    } else {
        keyboardType
    }

    // Tạo textStyle với font, color và size tùy chỉnh
    val textStyle = getComposeFont(fontCus).copy(
        color = textColor,
        fontSize = textSize,
    )

    // Màu sắc cho placeholder
    val placeholderStyle = textStyle.copy(color = hintColor)

    // Theo dõi focus
    val focusRequester = remember { FocusRequester() }
    val focusState = remember { mutableStateOf(false) }

    // Xử lý leading icon (leftDrawable hoặc leftComposable)
    val leadingIcon: @Composable (() -> Unit)? = when {
        // Ưu tiên leftComposable nếu được cung cấp
        leftComposable != null -> {
            {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    leftComposable()
                }
            }
        }
        // Sử dụng leftDrawable nếu có
        leftDrawable != null -> {
            {
                Icon(
                    painter = painterResource(leftDrawable),
                    contentDescription = null,
                    tint = leftDrawableTint,
                    modifier = Modifier.size(iconSize),
                )
                Spacer(modifier = Modifier.width(4.dp))
            }
        }
        // Không có icon
        else -> null
    }

    // Xử lý trailing icon (rightDrawable hoặc rightComposable) với onClick
    val trailingIcon: @Composable (() -> Unit)? = {
        Row(verticalAlignment = Alignment.CenterVertically) {
            // Hiển thị nút (x) nếu showClearButton = true và value có nội dung
            if (showClearButton && value.isNotEmpty()) {
                Icon(
                    painter = painterResource(
                        clearIcon ?: R.drawable.ic_close,
                    ), // Sử dụng icon mặc định nếu không được cung cấp
                    contentDescription = "Xóa",
                    tint = clearIconTint,
                    modifier = Modifier
                        .size(iconSize)
                        .clickable {
                            onValueChange("") // Xóa nội dung khi click
                        },
                )
                Spacer(modifier = Modifier.width(8.dp))
            }

            // Thêm rightComposable nếu có
            if (rightComposable != null) {
                rightComposable()
            }
            // Hoặc rightDrawable nếu có
            else if (rightDrawable != null) {
                Icon(
                    painter = painterResource(rightDrawable),
                    contentDescription = null,
                    tint = rightDrawableTint,
                    modifier = Modifier
                        .size(iconSize)
                        .clickable(enabled = onRightDrawableClick != null) {
                            onRightDrawableClick?.invoke()
                        },
                )
            }
        }
    }

    // Visual transformation cho password
    val visualTrans = if (isPassword) PasswordVisualTransformation() else VisualTransformation.None

    // Border và background
    val fieldModifier = Modifier
        .clip(RoundedCornerShape(borderRadius))
        .background(backgroundColor)
        .border(
            width = borderWidth,
            color = if (isError) Color.Red else borderColor,
            shape = RoundedCornerShape(borderRadius),
        )
        .then(modifier)

    Column {
        OutlinedTextField(
            value = value,
            onValueChange = finalOnValueChange, // Sử dụng finalOnValueChange để lọc pattern
            placeholder = {
                Text(
                    text = hint,
                    style = placeholderStyle,
                )
            },
            leadingIcon = leadingIcon,
            trailingIcon = trailingIcon,
            textStyle = textStyle,
            visualTransformation = visualTrans,
            isError = isError,
            enabled = enabled,
            singleLine = singleLine,
            maxLines = maxLines,
            keyboardOptions = KeyboardOptions(
                keyboardType = finalKeyboardType, // Sử dụng finalKeyboardType
                imeAction = imeAction,
            ),
            // Loại bỏ border của OutlinedTextField
            colors = OutlinedTextFieldDefaults.colors(
                unfocusedBorderColor = Color.Transparent,
                focusedBorderColor = Color.Transparent,
            ),
            modifier = fieldModifier
                .focusRequester(focusRequester)
                .onFocusChanged {
                    focusState.value = it.isFocused
                    onFocusChanged?.invoke(it.isFocused)
                },
        )

        // Hiển thị lỗi nếu có
        if (isError && !errorText.isNullOrEmpty()) {
            Text(
                text = errorText,
                color = Color.Red,
                fontSize = textSize.times(0.8f),
                modifier = Modifier.padding(start = 8.dp, top = 4.dp),
            )
        }

        if (isCounter) {
            Text(
                text = "${value.length}/$maxLength",
                style = getComposeFont(4, 10.sp, AppColors.grey08),
                textAlign = TextAlign.End,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp),
            )
        }
    }
}