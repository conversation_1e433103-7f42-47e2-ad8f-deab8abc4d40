package com.vietinbank.core_ui.base.sheet.examples

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.foundation.textfield.FoundationEditText
import com.vietinbank.core_ui.components.foundation.textfield.InputType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Example: Change Alias Bottom Sheet with Form Input
 *
 * Demonstrates:
 * - Using BaseBottomSheet with form input
 * - IME (keyboard) handling with imePadding
 * - Focus management
 * - Input validation
 * - FLAG_SECURE for sensitive data
 *
 * Usage:
 * ```kotlin
 * var showAliasSheet by remember { mutableStateOf(false) }
 *
 * ChangeAliasBottomSheet(
 *     visible = showAliasSheet,
 *     currentAlias = "Current Alias",
 *     onDismiss = { showAliasSheet = false },
 *     onSave = { newAlias ->
 *         updateAlias(newAlias)
 *         showAliasSheet = false
 *     }
 * )
 * ```
 */
@Composable
fun ChangeAliasBottomSheet(
    visible: Boolean,
    currentAlias: String = "",
    onDismiss: () -> Unit,
    onSave: (String) -> Unit,
) {
    // State for the alias input
    var alias by rememberSaveable { mutableStateOf(currentAlias) }
    var aliasError by remember { mutableStateOf<String?>(null) }
    var isProcessing by remember { mutableStateOf(false) }

    // Focus management
    val focusRequester = remember { FocusRequester() }
    val keyboardController = LocalSoftwareKeyboardController.current

    // Pre-resolve strings
    val aliasRequiredError = stringResource(R.string.validation_username_empty)
    val aliasTooShortError = "Bí danh phải có ít nhất 3 ký tự"

    BaseBottomSheet<String>(
        visible = visible,
        onDismissRequest = onDismiss,
        onResult = { newAlias ->
            onSave(newAlias)
            // Auto-dismiss handled by BaseBottomSheet
        },
        secureFlag = true, // Alias is sensitive data in banking context
        skipPartiallyExpanded = true,
        maxWidthDp = 600, // Tablet guard
    ) { onResult ->
        // Request focus after sheet animation
        LaunchedEffect(Unit) {
            kotlinx.coroutines.delay(300) // Wait for sheet animation
            focusRequester.requestFocus()
        }

        // Title
        FoundationText(
            text = "Đổi bí danh tài khoản",
            style = FDS.Typography.headingH3,
            color = FDS.Colors.characterHighlighted,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FDS.Sizer.Padding.padding16),
        )

        HorizontalDivider(
            color = FDS.Colors.strokeDivider,
            thickness = FDS.Sizer.Stroke.stroke1,
        )

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

        // Current alias display (if provided)
        if (currentAlias.isNotEmpty()) {
            FoundationText(
                text = "Bí danh hiện tại",
                style = FDS.Typography.captionCaptionL,
                color = FDS.Colors.characterTertiary,
                modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding16),
            )

            FoundationText(
                text = currentAlias,
                style = FDS.Typography.bodyB2Emphasized,
                color = FDS.Colors.characterSecondary,
                modifier = Modifier.padding(
                    horizontal = FDS.Sizer.Padding.padding16,
                    vertical = FDS.Sizer.Padding.padding8,
                ),
            )

            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
        }

        // Alias input field
        FoundationEditText(
            value = alias,
            onValueChange = { newValue ->
                alias = newValue
                // Clear error when user types
                aliasError = null
            },
            placeholder = "Nhập bí danh mới",
            inputType = InputType.TEXT,
            isError = aliasError != null,
            errorMessage = aliasError,
            showCharacterCounter = true,
            maxLength = 50,
            singleLine = true,
            imeAction = ImeAction.Done,
            keyboardActions = KeyboardActions(
                onDone = {
                    if (!isProcessing) {
                        when {
                            alias.isBlank() -> {
                                aliasError = aliasRequiredError
                            }
                            alias.length < 3 -> {
                                aliasError = aliasTooShortError
                            }
                            else -> {
                                isProcessing = true
                                keyboardController?.hide()
                                onResult(alias.trim())
                            }
                        }
                    }
                },
            ),
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = FDS.Sizer.Padding.padding16)
                .focusRequester(focusRequester),
            onFocusChanged = { /* Handle focus if needed */ },
        )

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

        // Action buttons
        FoundationButton(
            text = "Lưu bí danh",
            onClick = {
                if (!isProcessing) {
                    when {
                        alias.isBlank() -> {
                            aliasError = aliasRequiredError
                        }
                        alias.length < 3 -> {
                            aliasError = aliasTooShortError
                        }
                        else -> {
                            isProcessing = true
                            keyboardController?.hide()
                            onResult(alias.trim())
                        }
                    }
                }
            },
            enabled = alias.isNotBlank() && !isProcessing,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = FDS.Sizer.Padding.padding16),
        )

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))

        FoundationButton(
            text = stringResource(R.string.dialog_button_cancel),
            onClick = {
                keyboardController?.hide()
                onDismiss()
            },
            isLightButton = false,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = FDS.Sizer.Padding.padding16),
        )
    }
}

/**
 * Preview for ChangeAliasBottomSheet
 */
@Preview(showBackground = true, backgroundColor = 0xFF1A1A1A)
@Composable
private fun ChangeAliasBottomSheetPreview() {
    AppTheme {
        var showSheet by remember { mutableStateOf(true) }
        var savedAlias by remember { mutableStateOf("Tài khoản chính") }

        Column(modifier = Modifier.padding(FDS.Sizer.Padding.padding16)) {
            // Show current alias and button
            if (!showSheet) {
                FoundationText(
                    text = "Bí danh hiện tại: $savedAlias",
                    style = FDS.Typography.bodyB2,
                    color = FDS.Colors.characterPrimary,
                    modifier = Modifier.padding(bottom = FDS.Sizer.Gap.gap16),
                )

                FoundationButton(
                    text = "Đổi bí danh",
                    onClick = { showSheet = true },
                    modifier = Modifier.fillMaxWidth(),
                )
            }
        }

        // Change alias sheet
        ChangeAliasBottomSheet(
            visible = showSheet,
            currentAlias = savedAlias,
            onDismiss = { showSheet = false },
            onSave = { newAlias ->
                savedAlias = newAlias
                // No need to set showSheet = false, BaseBottomSheet auto-dismisses
            },
        )
    }
}