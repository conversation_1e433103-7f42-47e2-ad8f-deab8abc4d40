package com.vietinbank.core_ui.base.dialog

import android.app.Dialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.fragment.app.DialogFragment
import com.airbnb.lottie.LottieAnimationView
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_ui.R

/**
 * Created by vandz on 30/12/24.
 */
class LoadingDialog : DialogFragment() {
    private var rootView: View? = null
    private var lottieView: LottieAnimationView? = null
    private val handler = Handler(Looper.getMainLooper())
    private val autoDismissRunnable = Runnable {
        try {
            if (dialog?.isShowing == true) {
                dismissAllowingStateLoss()
            }
        } catch (e: Exception) {
            printLog("EX loading: ${e.message}")
        }
    }

    companion object {
        fun newInstance() = LoadingDialog()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        isCancelable = false
    }

    /**
     * onCreateDialog(): Tạo ra Dialog thực sự.
     * Có thể setCancelable, theme... tại đây.
     */
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = Dialog(requireContext())
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        dialog.setCancelable(false)
        return dialog
    }

    /**
     * onCreateView(): Inflate layout cho DialogFragment.
     */
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        rootView = inflater.inflate(R.layout.dialog_loading, container, false)
        lottieView = view?.findViewById(R.id.progressLoading)
        return rootView
    }

    /**
     * onResume() có thể là nơi ta "bắt đầu" countdown autoDismiss,
     * nếu muốn dialog tự đóng sau một khoảng thời gian.
     */
    override fun onResume() {
        super.onResume()
        // Ví dụ nếu cần tự tắt sau 90 giây:
        // handler.postDelayed(autoDismissRunnable, 90_000)
    }

    override fun onDestroyView() {
        lottieView?.cancelAnimation()
        lottieView?.removeAllAnimatorListeners()
        lottieView?.clearAnimation()
        lottieView = null
        // Hủy bỏ runnable trước khi View bị destroy
        handler.removeCallbacks(autoDismissRunnable)
        super.onDestroyView()
    }

    override fun onPause() {
        super.onPause()
        lottieView?.pauseAnimation()
    }

    /**
     * cho phép tuỳ chọn có chạm bên ngoài để đóng hay không.
     */
    fun setIsCancel(isCancel: Boolean) {
        isCancelable = isCancel
        dialog?.setCanceledOnTouchOutside(isCancel)
    }
}