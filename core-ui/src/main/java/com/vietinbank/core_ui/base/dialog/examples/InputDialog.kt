package com.vietinbank.core_ui.base.dialog.examples

import android.os.Parcelable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.fragment.app.FragmentManager
import com.vietinbank.core_ui.base.dialog.BaseDialog
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.dialog.DialogLayout
import com.vietinbank.core_ui.components.foundation.textfield.FoundationFieldType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import kotlinx.parcelize.Parcelize
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Result for input dialog
 */
@Parcelize
data class InputResult(
    val text: String,
    val timestamp: Long = System.currentTimeMillis(),
) : Parcelable

/**
 * Input dialog example with text field
 * Shows proper IME handling and validation
 *
 * Usage:
 * ```kotlin
 * InputDialog.show(
 *     fragmentManager = childFragmentManager,
 *     title = "Nhập tên",
 *     hint = "Nhập tên của bạn",
 *     initialValue = "",
 *     inputType = KeyboardType.Text,
 *     maxLength = 50,
 *     onResult = { result ->
 *         viewModel.updateName(result.text)
 *     }
 * )
 * ```
 */
class InputDialog : BaseDialog<InputResult>() {

    companion object {
        private const val ARG_TITLE = "arg_title"
        private const val ARG_HINT = "arg_hint"
        private const val ARG_INITIAL_VALUE = "arg_initial_value"
        private const val ARG_INPUT_TYPE = "arg_input_type"
        private const val ARG_MAX_LENGTH = "arg_max_length"
        private const val ARG_ERROR_MESSAGE = "arg_error_message"

        fun show(
            fragmentManager: FragmentManager,
            title: String,
            hint: String = "",
            initialValue: String = "",
            inputType: KeyboardType = KeyboardType.Text,
            maxLength: Int = 100,
            errorMessage: String = "Vui lòng nhập dữ liệu",
            onResult: ((InputResult) -> Unit)? = null,
            onCancelled: (() -> Unit)? = null,
        ) {
            val dialog = InputDialog().apply {
                arguments = android.os.Bundle().apply {
                    putString(ARG_TITLE, title)
                    putString(ARG_HINT, hint)
                    putString(ARG_INITIAL_VALUE, initialValue)
                    putInt(ARG_INPUT_TYPE, inputType.hashCode())
                    putInt(ARG_MAX_LENGTH, maxLength)
                    putString(ARG_ERROR_MESSAGE, errorMessage)
                }

                this.onResultCallback = onResult
                this.onCancelledCallback = onCancelled
            }

            dialog.show(fragmentManager, "InputDialog")
        }
    }

    private var onResultCallback: ((InputResult) -> Unit)? = null
    private var onCancelledCallback: (() -> Unit)? = null

    // Dialog configuration
    override val resultKey = "input_result"
    override val layout = DialogLayout.Center
    override val allowTouchDismiss = false
    override val requiresSecureFlag = false
    override val maxWidthDp = 400

    @Composable
    override fun DialogContent(
        visible: Boolean,
        onDismissRequest: () -> Unit,
        onResult: (InputResult) -> Unit,
    ) {
        val title = arguments?.getString(ARG_TITLE) ?: ""
        val hint = arguments?.getString(ARG_HINT) ?: ""
        val initialValue = arguments?.getString(ARG_INITIAL_VALUE) ?: ""
        val maxLength = arguments?.getInt(ARG_MAX_LENGTH) ?: 100
        val errorMessage = arguments?.getString(ARG_ERROR_MESSAGE) ?: "Vui lòng nhập dữ liệu"

        var text by remember { mutableStateOf(initialValue) }
        var hasError by remember { mutableStateOf(false) }
        val focusRequester = remember { FocusRequester() }
        val keyboardController = LocalSoftwareKeyboardController.current

        // Auto-focus when dialog opens
        LaunchedEffect(visible) {
            if (visible) {
                focusRequester.requestFocus()
            }
        }

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(FDS.Sizer.Padding.padding24),
        ) {
            // Title
            FoundationText(
                text = title,
                style = FDS.Typography.headingH3,
                color = FDS.Colors.characterPrimary,
            )

            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))

            // Input field
            Column {
                FoundationFieldType(
                    value = text,
                    onValueChange = { newText ->
                        if (newText.length <= maxLength) {
                            text = newText
                            hasError = false
                        }
                    },
                    placeholder = hint,
                    isError = hasError,
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Text,
                        imeAction = ImeAction.Done,
                    ),
                    keyboardActions = KeyboardActions(
                        onDone = {
                            if (text.isNotBlank()) {
                                keyboardController?.hide()
                                onResult(InputResult(text.trim()))
                            } else {
                                hasError = true
                            }
                        },
                    ),
                    modifier = Modifier
                        .fillMaxWidth()
                        .focusRequester(focusRequester),
                )

                // Show error message if needed
                if (hasError && errorMessage != null) {
                    FoundationText(
                        text = errorMessage,
                        style = FDS.Typography.captionCaptionL,
                        color = FDS.Colors.error,
                        modifier = Modifier.padding(top = FDS.Sizer.Gap.gap4),
                    )
                }
            }

            // Character counter
            FoundationText(
                text = "${text.length}/$maxLength",
                style = FDS.Typography.bodyB2,
                color = FDS.Colors.characterTertiary,
                modifier = Modifier.padding(top = FDS.Sizer.Gap.gap4),
            )

            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

            // Action buttons
            Row(
                modifier = Modifier.fillMaxWidth(),
            ) {
                FoundationButton(
                    text = "Hủy",
                    onClick = {
                        keyboardController?.hide()
                        onDismissRequest()
                    },
                    isLightButton = true,
                    modifier = Modifier.weight(1f),
                )

                Spacer(modifier = Modifier.weight(0.1f))

                FoundationButton(
                    text = "Xác nhận",
                    onClick = {
                        if (text.isNotBlank()) {
                            keyboardController?.hide()
                            onResult(InputResult(text.trim()))
                        } else {
                            hasError = true
                        }
                    },
                    enabled = text.isNotBlank(),
                    modifier = Modifier.weight(1f),
                )
            }
        }
    }

    // NOTE: onResult and onCancelled methods removed from BaseDialog to prevent listener conflicts
    // Results are now handled via Fragment Result API in the parent Fragment/Activity
}