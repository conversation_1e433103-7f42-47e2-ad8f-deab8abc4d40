package com.vietinbank.core_ui.base.compose

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.MutableTransitionState
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.drawscope.Fill
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.theme.AppSizer

/**
 * Các vị trí hiển thị arrow (mũi tên) của tooltip
 */
enum class ArrowPosition {
    TOP_START, TOP_CENTER, TOP_END,
    BOTTOM_START, BOTTOM_CENTER, BOTTOM_END,
    LEFT_CENTER, RIGHT_CENTER,
    NONE, // Không hiển thị arrow
}

/**
 * BaseTooltip component for displaying information tooltips
 *
 * @param modifier Modifier for the tooltip
 * @param title Optional title for the tooltip
 * @param content Content to display in the tooltip
 * @param titleStyle TextStyle for the title
 * @param contentStyle TextStyle for the content
 * @param backgroundColor Background color of the tooltip
 * @param shape Shape of the tooltip
 * @param padding Padding inside the tooltip
 * @param elevation Elevation (shadow) of the tooltip
 * @param maxWidth Maximum width of the tooltip
 * @param bulletPoints List of bullet points to display in the tooltip
 * @param visible Controls the visibility of the tooltip
 * @param onDismissRequest Callback when tooltip is dismissed
 * @param arrowPosition Vị trí của arrow
 * @param arrowSize Kích thước của arrow
 * @param arrowColor Màu sắc của arrow
 */
@Composable
fun BaseTooltip(
    modifier: Modifier = Modifier,
    title: String? = null,
    content: String? = null,
    titleStyle: TextStyle = getComposeFont(
        fontCus = 5, // Bold
        fontSize = 16.sp,
        fontColor = AppColors.contentPrimary,
    ),
    contentStyle: TextStyle = getComposeFont(
        fontCus = 1, // Regular
        fontSize = 14.sp,
        fontColor = AppColors.contentPrimary,
    ),
    backgroundColor: Color = AppColors.white,
    shape: Shape = RoundedCornerShape(AppSizer.Radius.radius10),
    padding: PaddingValues = PaddingValues(
        horizontal = AppSizer.Gap.gap24,
        vertical = AppSizer.Gap.gap16,
    ),
    elevation: Dp = 4.dp,
    maxWidth: Dp = 280.dp,
    bulletPoints: List<String>? = null,
    visible: Boolean = true,
    onDismissRequest: () -> Unit = {},
    alignment: Alignment = Alignment.TopStart,
    offset: IntOffset = IntOffset(0, 0),
    properties: PopupProperties = PopupProperties(focusable = true),
    arrowPosition: ArrowPosition = ArrowPosition.NONE,
    arrowSize: Dp = 8.dp,
    arrowColor: Color = backgroundColor,
) {
    if (!visible) return

    // Tạo state cho animation
    val tooltipAnimationState = remember {
        MutableTransitionState(false).apply {
            targetState = true
        }
    }

    // Chuyển đổi arrowSize sang Pixel
    val arrowSizePx = with(LocalDensity.current) { arrowSize.toPx() }

    // Hủy tooltip khi visible = false
    LaunchedEffect(visible) {
        if (!visible) {
            tooltipAnimationState.targetState = false
        }
    }

    // Tính toán padding cho Box chính dựa vào vị trí arrow
    val arrowPadding = when (arrowPosition) {
        ArrowPosition.TOP_START, ArrowPosition.TOP_CENTER, ArrowPosition.TOP_END ->
            PaddingValues(top = arrowSize)

        ArrowPosition.BOTTOM_START, ArrowPosition.BOTTOM_CENTER, ArrowPosition.BOTTOM_END ->
            PaddingValues(bottom = arrowSize)

        ArrowPosition.LEFT_CENTER ->
            PaddingValues(start = arrowSize)

        ArrowPosition.RIGHT_CENTER ->
            PaddingValues(end = arrowSize)

        ArrowPosition.NONE ->
            PaddingValues(0.dp)
    }

    Popup(
        alignment = alignment,
        offset = offset,
        onDismissRequest = onDismissRequest,
        properties = properties,
    ) {
        // Wrap Card với animation
        AnimatedVisibility(
            visibleState = tooltipAnimationState,
            enter = fadeIn(animationSpec = tween(durationMillis = 200)) +
                slideInVertically(
                    initialOffsetY = { -40 },
                    animationSpec = tween(durationMillis = 200),
                ),
            exit = fadeOut(animationSpec = tween(durationMillis = 200)) +
                slideOutVertically(
                    targetOffsetY = { -40 },
                    animationSpec = tween(durationMillis = 200),
                ),
        ) {
            // Layout chính chứa cả Card và Arrow - chiều sâu của padding đủ để mũi tên nằm ngoài tooltip
            Box(
                modifier = modifier
                    .widthIn(max = maxWidth)
                    .padding(arrowPadding),
            ) {
                // Card chứa nội dung tooltip
                Card(
                    shape = shape,
                    colors = CardDefaults.cardColors(containerColor = backgroundColor),
                    elevation = CardDefaults.cardElevation(defaultElevation = elevation),
                    modifier = Modifier,
                ) {
                    Column(
                        modifier = Modifier.padding(padding),
                    ) {
                        // Title
                        title?.let {
                            Text(
                                text = it,
                                style = titleStyle,
                                modifier = Modifier.fillMaxWidth(),
                            )
                            Spacer(modifier = Modifier.height(4.dp))
                        }

                        // Content
                        content?.let {
                            Text(
                                text = it,
                                style = contentStyle,
                                modifier = Modifier.fillMaxWidth(),
                            )
                        }

                        // Bullet Points
                        bulletPoints?.let {
                            Spacer(modifier = Modifier.height(4.dp))
                            Column {
                                bulletPoints.forEach { point ->
                                    Row(
                                        modifier = Modifier.padding(top = 4.dp),
                                        verticalAlignment = Alignment.Top,
                                    ) {
                                        Text(
                                            text = "•",
                                            style = contentStyle,
                                            modifier = Modifier.padding(end = 8.dp, top = 0.dp),
                                        )
                                        Text(
                                            text = point,
                                            style = contentStyle,
                                            modifier = Modifier.weight(1f),
                                        )
                                    }
                                }
                            }
                        }
                    }
                }

                // Arrow (mũi tên) - ĐIỀU CHỈNH VỊ TRÍ ĐỂ NẰM NGOÀI TOOLTIP
                if (arrowPosition != ArrowPosition.NONE) {
                    // Đặt arrow đúng vị trí để đảm bảo nằm ngoài tooltip
                    val arrowModifier = when (arrowPosition) {
                        ArrowPosition.TOP_START ->
                            Modifier
                                .align(Alignment.TopStart)
                                .offset(x = 16.dp, y = (-arrowSize))

                        ArrowPosition.TOP_CENTER ->
                            Modifier
                                .align(Alignment.TopCenter)
                                .offset(y = (-arrowSize))

                        ArrowPosition.TOP_END ->
                            Modifier
                                .align(Alignment.TopEnd)
                                .offset(x = (-16).dp, y = (-arrowSize))

                        ArrowPosition.BOTTOM_START ->
                            Modifier
                                .align(Alignment.BottomStart)
                                .offset(x = 16.dp, y = arrowSize)

                        ArrowPosition.BOTTOM_CENTER ->
                            Modifier
                                .align(Alignment.BottomCenter)
                                .offset(y = arrowSize)

                        ArrowPosition.BOTTOM_END ->
                            Modifier
                                .align(Alignment.BottomEnd)
                                .offset(x = (-16).dp, y = arrowSize)

                        ArrowPosition.LEFT_CENTER ->
                            Modifier
                                .align(Alignment.CenterStart)
                                .offset(x = (-arrowSize))

                        ArrowPosition.RIGHT_CENTER ->
                            Modifier
                                .align(Alignment.CenterEnd)
                                .offset(x = arrowSize)

                        ArrowPosition.NONE -> Modifier
                    }

                    Canvas(
                        modifier = arrowModifier.size(arrowSize * 2),
                    ) {
                        val path = Path()

                        when (arrowPosition) {
                            ArrowPosition.TOP_START, ArrowPosition.TOP_CENTER, ArrowPosition.TOP_END -> {
                                // Arrow pointing upward
                                path.moveTo(arrowSizePx, 0f)
                                path.lineTo(arrowSizePx * 2, arrowSizePx)
                                path.lineTo(0f, arrowSizePx)
                                path.close()
                            }

                            ArrowPosition.BOTTOM_START, ArrowPosition.BOTTOM_CENTER, ArrowPosition.BOTTOM_END -> {
                                // Arrow pointing downward - cải thiện để rõ ràng hơn
                                path.moveTo(arrowSizePx, arrowSizePx * 2)
                                path.lineTo(0f, arrowSizePx)
                                path.lineTo(arrowSizePx * 2, arrowSizePx)
                                path.close()
                            }

                            ArrowPosition.LEFT_CENTER -> {
                                // Arrow pointing leftward
                                path.moveTo(0f, arrowSizePx)
                                path.lineTo(arrowSizePx, 0f)
                                path.lineTo(arrowSizePx, arrowSizePx * 2)
                                path.close()
                            }

                            ArrowPosition.RIGHT_CENTER -> {
                                // Arrow pointing rightward
                                path.moveTo(arrowSizePx * 2, arrowSizePx)
                                path.lineTo(arrowSizePx, 0f)
                                path.lineTo(arrowSizePx, arrowSizePx * 2)
                                path.close()
                            }

                            ArrowPosition.NONE -> {
                                /* No arrow */
                            }
                        }

                        drawPath(path = path, color = arrowColor, style = Fill)
                    }
                }
            }
        }
    }
}

/**
 * BaseTooltipAnchor is a convenience component that wraps content with a tooltip
 * that appears when specified (e.g., on hover or click)
 *
 * @param tooltipContent Content of the tooltip
 * @param tooltipTitle Optional title for the tooltip
 * @param visible Controls whether the tooltip is visible
 * @param onVisibilityChange Callback when visibility changes
 * @param bulletPoints List of bullet points to display in the tooltip
 * @param modifier Modifier for the anchor content
 * @param tooltipOffset Offset position of the tooltip relative to the anchor
 * @param tooltipAlignment Alignment of the tooltip relative to the anchor
 * @param content The content that the tooltip is anchored to
 * @param arrowPosition Vị trí của arrow
 */
@Composable
fun BaseTooltipAnchor(
    tooltipContent: String? = null,
    tooltipTitle: String? = null,
    visible: Boolean = false,
    onVisibilityChange: (Boolean) -> Unit = {},
    bulletPoints: List<String>? = null,
    modifier: Modifier = Modifier,
    tooltipOffset: IntOffset = IntOffset(0, 0),
    tooltipAlignment: Alignment = Alignment.TopCenter,
    arrowPosition: ArrowPosition = ArrowPosition.NONE,
    content: @Composable () -> Unit,
) {
    Box(modifier = modifier) {
        // Anchor content
        content()

        // The tooltip
        if (visible) {
            BaseTooltip(
                title = tooltipTitle,
                content = tooltipContent,
                bulletPoints = bulletPoints,
                visible = true,
                onDismissRequest = { onVisibilityChange(false) },
                alignment = tooltipAlignment,
                offset = tooltipOffset,
                arrowPosition = arrowPosition,
            )
        }
    }
}