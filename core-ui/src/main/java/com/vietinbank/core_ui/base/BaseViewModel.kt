package com.vietinbank.core_ui.base

import android.os.Build
import android.text.Html
import android.text.Spanned
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.ListFunctionId
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.livedata.SingleLiveEvent
import com.vietinbank.core_common.models.ForceUpdateEvent
import com.vietinbank.core_common.models.VersionApp
import com.vietinbank.core_common.network.NetworkEvent
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.session.SessionEvent
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_ui.R
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import java.util.concurrent.atomic.AtomicInteger
import javax.net.ssl.SSLPeerUnverifiedException

/**
 * Created by vandz on 18/12/24.
 */
abstract class BaseViewModel : ViewModel() {
    protected abstract val resourceProvider: IResourceProvider
    protected abstract val appConfig: IAppConfigManager
    protected abstract val userProf: IUserProf
    protected abstract val sessionManager: ISessionManager
    protected abstract val ottSetupService: IOttSetupService

    /**
     * Check if user is logged in (has active session)
     * Used by BaseFragment to determine monitoring behavior
     */
    fun isUserLoggedIn(): Boolean = userProf.isLogined()

    fun getAppConfigManager(): IAppConfigManager = appConfig

    // recall api onViewCreate
    private var isKeepCallInit = false
    fun isKeepCallInit() = isKeepCallInit
    fun setKeepCallInit() {
        isKeepCallInit = true
    }

    // Các event để UI quan sát và hiển thị lỗi tương ứng
    val noInternetConnectionEvent = SingleLiveEvent<String>()
    val lostInternetConnectionEvent = SingleLiveEvent<String>()
    val connectTimeoutEvent = SingleLiveEvent<Boolean>()
    val invalidCertificateEvent = SingleLiveEvent<Boolean>()
    val serverErrorEvent = SingleLiveEvent<Boolean>()

    // Use Channel for session expired events - more reliable for banking app
    // CONFLATED: Only latest session timeout event matters, drops old events if not consumed
    private val _sessionExpiredChannel = Channel<Unit>(Channel.CONFLATED)
    val sessionExpiredFlow = _sessionExpiredChannel.receiveAsFlow()

    val apiErrorMessage = SingleLiveEvent<String>() // Hiển thị message từ server (nếu có)

    // Handler bắt mọi exception chưa được xử lý
    private val exceptionHandler = CoroutineExceptionHandler { _, throwable ->
        viewModelScope.launch {
            printLog("BaseViewModel Caught exception: ${throwable.message}")
            handleThrowable(throwable)
        }
    }

    private val _forceUpdateEvent = MutableLiveData<ForceUpdateEvent>()
    val forceUpdateEvent: LiveData<ForceUpdateEvent> = _forceUpdateEvent

    // send one time event using channel
    // CONFLATED: For one-time UI events, only the latest matters if not consumed
    private val _oneTimeEvent = Channel<OneTimeEvent>(Channel.CONFLATED)
    val oneTimeEvent: Flow<OneTimeEvent> = _oneTimeEvent.receiveAsFlow()

    fun sendEvent(oneTimeEvent: OneTimeEvent) = viewModelScope.launch {
        _oneTimeEvent.send(oneTimeEvent)
    }

    // Track network state to avoid duplicate dialogs
    private var isNetworkLost = false

    // Job + ExceptionHandler gán cho viewModelScope
    private val viewModelJob = SupervisorJob()
    private val coroutineContext = viewModelJob + Dispatchers.IO + exceptionHandler
    protected val vmScope =
        CoroutineScope(coroutineContext) // Cho phép các child coroutines fail độc lập

    init {
        // Observe SessionEvent từ InactivityManager và forward to Channel
        vmScope.launch {
            SessionEvent.sessionExpiredEvent.collect {
                _sessionExpiredChannel.send(Unit)
            }
        }

        // Observe NetworkEvent & trigger noInternetConnectionEvent
        vmScope.launch {
            NetworkEvent.networkLostEvent.collect {
                printLog("BaseViewModel: Network lost detected")
                isNetworkLost = true
                lostInternetConnectionEvent.postValue(
                    resourceProvider.getString(R.string.error_no_network),
                )
            }
        }

        // Observe network available event - log for now, add business logic later
        vmScope.launch {
            NetworkEvent.networkAvailableEvent.collect {
                printLog("BaseViewModel: Network available detected")
                isNetworkLost = false
                // Add business logic when needed (auto-retry, dismiss dialog, etc.)
            }
        }
    }

    private suspend fun handleThrowable(throwable: Throwable) {
        when (throwable) {
            // Mất kết nối mạng
            is UnknownHostException -> {
                if (!isNetworkLost) {
                    // Network OK but server unreachable
                    noInternetConnectionEvent.postValue("")
                }
                // Else: NetworkMonitor already handled the network loss dialog
            }
            // Thời gian kết nối hết hạn
            is SocketTimeoutException -> {
                connectTimeoutEvent.postValue(true)
            }
            // SSL certificate chưa validate
            is SSLPeerUnverifiedException -> {
                invalidCertificateEvent.postValue(true)
            }

            else -> {
                // Chuyển mọi lỗi sang AppException
                val baseEx = toAppException(throwable)
                onError(baseEx)
            }
        }
    }

    // Chuyển Throwable thành AppException tùy theo logic
    private fun toAppException(e: Throwable): AppException {
        return when (e) {
            is AppException -> e
            else -> AppException.UnknownException("An unexpected error occurred.", e)
        }
    }

    fun onError(exception: AppException) {
        val result = getFormattedException(exception)
        when (exception) {
            is AppException.ApiException -> {
                if (exception.code == Tags.SESSION_EXPIRED || exception.code == Tags.FORCE_SESSION_EXPIRED) {
                    vmScope.launch {
                        SessionEvent.emitSessionExpired()
                    }
                    printLog("Session expired from API")
                    return
                }

                // Các ApiException khác chuyển xuống onDisplayErrorMessage để ViewModel con xử lý
                val result = getFormattedException(exception)
                onDisplayErrorMessage(result)
                return
            }

            is AppException.NetworkException -> {
                // Xử lý lỗi mạng ở cấp BaseViewModel
                noInternetConnectionEvent.postValue(result.message)
            }

            is AppException.HttpException -> {
                // Xử lý lỗi HTTP ở cấp BaseViewModel
                apiErrorMessage.postValue(result.message)
            }

            is AppException.EncryptionException -> {
                // Xử lý lỗi mã hóa ở cấp BaseViewModel
                apiErrorMessage.postValue(result.message)
            }

            is AppException.UnknownException -> {
                // Xử lý lỗi không xác định ở cấp BaseViewModel
                apiErrorMessage.postValue(result.message)
            }

            is AppException.IllegalArgumentException -> {
                // Xử lý lỗi tham số ở cấp BaseViewModel
                apiErrorMessage.postValue(result.message)
            }
        }

        // Cac truong hop loi khac
        onDisplayErrorMessage(result)
    }

    // Hàm này có thể override ở ViewModel con nếu muốn xử lý riêng
    protected open fun onDisplayErrorMessage(exception: AppException) {
        if (exception is AppException.ApiException) {
            apiErrorMessage.postValue(exception.message)
        }
    }

    // Hàm helper: Xử lý Resource chung
    protected suspend fun <T> handleResource(
        resource: Resource<T>,
        onSuccess: suspend (T) -> Unit,
    ) {
        when (resource) {
            is Resource.Success -> onSuccess(resource.data)
            is Resource.Error -> {
                // Gom xử lý lỗi
                resource.exception?.let { ex ->
                    onError(ex)
                }
            }
        }
    }

    protected suspend fun <T> handleResourceSilent(
        resource: Resource<T>,
        onSuccess: suspend (T) -> Unit,
        onError: ((AppException) -> Unit)? = null,
    ) {
        when (resource) {
            is Resource.Success -> onSuccess(resource.data)
            is Resource.Error -> {
                // Chỉ ghi log lỗi nhưng không hiển thị UI
                printLog("Silent error: ${resource.message}")
                // Xử lý lỗi tùy chỉnh tùy chọn nếu được cung cấp
                resource.exception?.let { ex ->
                    onError?.invoke(ex)
                }
            }
        }
    }

    // LiveData để quan sát trạng thái loading
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> get() = _isLoading

    // Biến đếm để quản lý số lượng API calls đang chạy
    private var loadingCount = AtomicInteger(0)

    /**
     * Hàm để khởi chạy một coroutine với quản lý loading state và exception handling.
     * @param showLoading Nếu true, sẽ hiển thị loading khi bắt đầu coroutine và ẩn khi kết thúc.
     * @param block Khối mã suspend cần thực thi.
     */
    protected fun launchJob(showLoading: Boolean = true, block: suspend () -> Unit): Job {
        if (showLoading) {
            loadingCount.incrementAndGet()
            _isLoading.postValue(true)
        }
        return vmScope.launch {
            try {
                block()
            } finally {
                if (showLoading) {
                    loadingCount.decrementAndGet()
                    if (loadingCount.get() <= 0) {
                        _isLoading.postValue(false)
                        loadingCount.set(0)
                    }
                }
            }
        }
    }

    /**
     * Launches a coroutine job without showing loading indicators or error messages.
     * Useful for background tasks or API calls that should run silently.
     *
     * @param block The suspend function to execute.
     * @param onError Optional callback to handle exceptions silently.
     * @return The launched coroutine Job
     */
    protected fun launchJobSilent(
        onError: ((AppException) -> Unit)? = null,
        block: suspend () -> Unit,
    ): Job {
        return vmScope.launch {
            try {
                block()
            } catch (throwable: Throwable) {
                // Convert to AppException but don't trigger UI events
                val exception = when (throwable) {
                    is AppException -> throwable
                    else -> AppException.UnknownException("Silent error occurred.", throwable, "", "")
                }

                // Log the error but don't show UI
                printLog("launchJobSilent caught exception: ${exception.message}")

                // Optional custom error handling if provided
                onError?.invoke(exception)
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        viewModelJob.cancel() // hủy job khi ViewModel destroyed
    }

    fun showLoading() {
        loadingCount.incrementAndGet()
        _isLoading.postValue(true)
    }

    fun hideLoading() {
        loadingCount.decrementAndGet()
        if (loadingCount.get() <= 0) {
            _isLoading.postValue(false)
            loadingCount.set(0)
        }
    }

    fun forceAllHideLoading() {
        loadingCount.set(0)
        _isLoading.postValue(false)
    }

    private fun getFormattedException(exception: AppException): AppException {
        val baseMessage = when (exception) {
            is AppException.ApiException -> {
                exception.message?.takeIf { it.isNotEmpty() }
                    ?: resourceProvider.getString(R.string.error_at_server)
            }

            is AppException.HttpException -> {
                exception.message?.takeIf { it.isNotEmpty() }
                    ?: resourceProvider.getString(R.string.error_at_server)
            }

            is AppException.NetworkException -> {
                resourceProvider.getString(R.string.error_no_network)
            }

            is AppException.UnknownException -> {
                resourceProvider.getString(R.string.error_at_server)
            }

            is AppException.EncryptionException -> {
                resourceProvider.getString(R.string.error_at_server)
            }

            is AppException.IllegalArgumentException -> {
                resourceProvider.getString(R.string.error_at_server)
            }

            else -> resourceProvider.getString(R.string.error_at_server)
        }

        return when (exception) {
            is AppException.ApiException -> {
                val formattedMessage = if (!exception.requestId.isNullOrEmpty()) {
                    if (!exception.subCode.isNullOrEmpty()) {
                        "$baseMessage\n(${exception.subCode}) (RQ-${exception.requestId})"
                    } else {
                        "$baseMessage\n(C-${exception.code ?: "?"}) (RQ-${exception.requestId})"
                    }
                } else {
                    "$baseMessage\n(C-${exception.code ?: "?"})"
                }
                AppException.ApiException(
                    code = exception.code,
                    subCode = exception.subCode,
                    requestPath = exception.requestPath,
                    rawResponseJson = exception.rawResponseJson,
                    message = formattedMessage,
                    cause = exception.cause,
                    requestId = exception.requestId,
                )
            }

            is AppException.HttpException -> {
                val formattedMessage = if (!exception.requestId.isNullOrEmpty()) {
                    "$baseMessage\n(HTTP-${exception.httpCode}) (RQ-${exception.requestId})"
                } else {
                    baseMessage
                }
                AppException.HttpException(
                    httpCode = exception.httpCode,
                    errorBody = exception.errorBody,
                    message = formattedMessage,
                    cause = exception.cause,
                    requestId = exception.requestId,
                )
            }

            is AppException.NetworkException -> {
                val formattedMessage = if (!exception.requestId.isNullOrEmpty()) {
                    "$baseMessage\n(C-${exception.code ?: "?"}) (RQ-${exception.requestId})"
                } else {
                    "$baseMessage\n(C-${exception.code ?: "?"})"
                }
                AppException.NetworkException(
                    code = exception.code,
                    message = formattedMessage,
                    cause = exception.cause,
                    requestId = exception.requestId,
                )
            }

            is AppException.UnknownException -> {
                val formattedMessage = if (!exception.requestId.isNullOrEmpty()) {
                    "$baseMessage\n(C-${exception.code ?: "?"}) (RQ-${exception.requestId})"
                } else {
                    "$baseMessage\n(C-${exception.code ?: "?"})"
                }
                printLog("unkonw: $formattedMessage")
                AppException.UnknownException(
                    code = exception.code,
                    message = formattedMessage,
                    cause = exception.cause,
                    requestId = exception.requestId,
                )
            }

            is AppException.EncryptionException -> {
                val formattedMessage = if (!exception.requestId.isNullOrEmpty()) {
                    "$baseMessage\n(C-${exception.code ?: "?"}) (RQ-${exception.requestId})"
                } else {
                    "$baseMessage\n(C-${exception.code ?: "?"})"
                }
                AppException.EncryptionException(
                    code = exception.code,
                    message = formattedMessage,
                    cause = exception.cause,
                    requestId = exception.requestId,
                )
            }

            is AppException.IllegalArgumentException -> {
                val formattedMessage = if (!exception.requestId.isNullOrEmpty()) {
                    "$baseMessage\n(C-${exception.code ?: "?"}) (RQ-${exception.requestId})"
                } else {
                    "$baseMessage\n(C-${exception.code ?: "?"})"
                }
                AppException.IllegalArgumentException(
                    code = exception.code,
                    message = formattedMessage,
                    cause = exception.cause,
                    requestId = exception.requestId,
                )
            }

            else -> exception
        }
    }

    /**
     * Setup OTT for the current user.
     * This function is called from BaseFragment.onResume() to ensure OTT is setup on every screen.
     * It safely handles cases where userProf is not ready or user is not logged in.
     */
    fun setupOtt() {
        try {
            printLog("Setup OTT... sessionID: ${sessionManager.getSessionId()}")
            if (!sessionManager.getSessionId().isNullOrEmpty()) {
                val cifNo = userProf.getCifNo()
                val phoneNumber = userProf.getPhoneNo()
                val username = userProf.getUserName()

                if (!cifNo.isNullOrEmpty() && !phoneNumber.isNullOrEmpty() && !username.isNullOrEmpty()) {
                    ottSetupService.setupOttForUser(cifNo, phoneNumber, username)
                }
            }
        } catch (e: Exception) {
            // Silently handle any exceptions during OTT setup
            // This prevents crashes if userProf is not ready
            printLog("OTT setup failed: ${e.message}")
        }
    }

    /**
     * Sync OTT messages from server
     * This should be called manually when needed (e.g., from HomeFragment after login)
     */
    fun syncOttMessages(isForce: Boolean = false) {
        viewModelScope.launch {
            try {
                ottSetupService.syncOttMessages(forceFullSync = isForce)
            } catch (e: Exception) {
                printLog("OTT message sync failed: ${e.message}")
            }
        }
    }

    // Hàm check điều kiện ForceUpdate
    private fun isCheckForceUpdate(
        versionApp: VersionApp?,
        nameFunctionID: String,
        type: ForceUpdateType,
    ): Boolean {
        if (versionApp == null) return false
        if (versionApp.functionId?.contains(nameFunctionID) != true) return false

        return when (type) {
            ForceUpdateType.FORCE_F -> versionApp.status == Tags.STATUS_FORCE_UPDATE_F
            ForceUpdateType.FORCE_O ->
                versionApp.status == Tags.STATUS_FORCE_UPDATE_O &&
                    appConfig.isShowDisplayByKey(
                        versionApp.version.toString(),
                        versionApp.displayNum?.toInt() ?: 0,
                        Tags.PREF_KEY_SHOW_COUNT,
                        Tags.PREF_KEY_SHOW_COUNT,
                    )
            ForceUpdateType.FORCE_N ->
                versionApp.status == Tags.STATUS_FORCE_UPDATE_N &&
                    appConfig.isShowDisplayByKey(
                        versionApp.version.toString(),
                        versionApp.update_displayNum?.toInt() ?: 0,
                        Tags.PREF_KEY_VERSION_UPDATE,
                        Tags.PREF_KEY_SHOW_COUNT_UPDATE,
                    )
        }
    }

    // Hàm xử lý điều hướng ForceUpdate với tranType
    fun handleNavigateWithForceUpdate(tranType: String, navigateApprovalList: () -> Unit) {
        when (tranType) {
            Tags.CHECKER_TRANTYPE_CT -> handleForceUpdateOrNavigate(ListFunctionId.FORCE_UPDATE_C_TRANSFER_CT, navigateApprovalList)
            Tags.CHECKER_TRANTYPE_TX -> handleForceUpdateOrNavigate(ListFunctionId.FORCE_UPDATE_C_TAX_TX, navigateApprovalList)
            Tags.CHECKER_TRANTYPE_HU -> handleForceUpdateOrNavigate(ListFunctionId.FORCE_UPDATE_C_BULK_HU, navigateApprovalList)
            Tags.CHECKER_TRANTYPE_BA -> handleForceUpdateOrNavigate(ListFunctionId.FORCE_UPDATE_C_BULK_BA, navigateApprovalList)
            Tags.CHECKER_TRANTYPE_SL -> handleForceUpdateOrNavigate(ListFunctionId.FORCE_UPDATE_C_TRANSFER_SL, navigateApprovalList)
            Tags.CHECKER_TRANTYPE_SLO -> handleForceUpdateOrNavigate(ListFunctionId.FORCE_UPDATE_C_TRANSFER_SLO, navigateApprovalList)
            Tags.CHECKER_TRANTYPE_SX -> handleForceUpdateOrNavigate(ListFunctionId.FORCE_UPDATE_C_TRANSFER_SX, navigateApprovalList)
            Tags.CHECKER_TRANTYPE_TR -> handleForceUpdateOrNavigate(ListFunctionId.FORCE_UPDATE_C_OTHER_TR, navigateApprovalList)
            else -> navigateApprovalList()
        }
    }

    // Hàm xử lý điều hướng ForceUpdate với Funtion
    fun handleForceUpdateOrNavigate(
        functionId: String,
        onNavigate: () -> Unit,
    ) {
        val versionApp = Utils.g().provideGson().fromJson(appConfig.getConfigVersionApp(), VersionApp::class.java)

        when {
            isCheckForceUpdate(versionApp, functionId, ForceUpdateType.FORCE_F) -> {
                showPopupForceUpdate(versionApp, ForceUpdateType.FORCE_F)
            }
            isCheckForceUpdate(versionApp, functionId, ForceUpdateType.FORCE_O) -> {
                showPopupForceUpdate(versionApp, ForceUpdateType.FORCE_O)
            }
            isCheckForceUpdate(versionApp, functionId, ForceUpdateType.FORCE_N) -> {
                showPopupForceUpdate(versionApp, ForceUpdateType.FORCE_N)
            }
            else -> {
                onNavigate()
            }
        }
    }

    // Hàm gọi sang Fragment để show popup
    private fun showPopupForceUpdate(versionApp: VersionApp, type: ForceUpdateType) {
        val message = buildForceUpdateMessage(versionApp, type)

        val event = when (type) {
            ForceUpdateType.FORCE_F -> ForceUpdateEvent.ShowForceFDialog(message)
            ForceUpdateType.FORCE_O -> ForceUpdateEvent.ShowForceODialog(message)
            ForceUpdateType.FORCE_N -> ForceUpdateEvent.ShowForceNDialog(message)
        }

        _forceUpdateEvent.postValue(event)
    }

    private fun buildForceUpdateMessage(versionApp: VersionApp, type: ForceUpdateType): String {
        return when (type) {
            ForceUpdateType.FORCE_F, ForceUpdateType.FORCE_O ->
                parseHtml(versionApp.mess ?: "Vui lòng cập nhật ứng dụng để sử dụng dịch vụ.").toString()
            ForceUpdateType.FORCE_N ->
                parseHtml(versionApp.messUpdated ?: "Bạn đã cập nhật ứng dụng thành công.").toString()
        }
    }

    private fun parseHtml(html: String?): Spanned {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            Html.fromHtml(html ?: "", Html.FROM_HTML_MODE_LEGACY)
        } else {
            Html.fromHtml(html ?: "")
        }
    }

    enum class ForceUpdateType {
        FORCE_F, FORCE_O, FORCE_N
    }
}

interface OneTimeEvent
