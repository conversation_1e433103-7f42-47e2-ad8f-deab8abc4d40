package com.vietinbank.core_ui.base.compose

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.height
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.theme.AppColors

@Composable
fun BaseCustomSwitch(
    modifier: Modifier = Modifier,
    isChecked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
) {
    Box(
        modifier = Modifier.height(22.dp),
    ) {
        Switch(
            modifier = modifier.scale(0.8f)
                .align(Alignment.Center),
            checked = isChecked,
            onCheckedChange = onCheckedChange,
            colors = SwitchDefaults.colors(
                checkedThumbColor = Color.White,
                checkedTrackColor = AppColors.switchCheckedTrack, // Blue color for ON
                uncheckedThumbColor = AppColors.switchUncheckedThumb, // Grey circle when OFF
                uncheckedTrackColor = AppColors.switchUncheckedTrack, // Light blue background when OFF
                uncheckedBorderColor = AppColors.switchUncheckedBorder, // Border when OFF
                checkedBorderColor = Color.Transparent, // No border when ON
            ),
        )
    }
}
