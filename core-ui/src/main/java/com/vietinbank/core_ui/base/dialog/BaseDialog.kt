package com.vietinbank.core_ui.base.dialog

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import androidx.activity.OnBackPressedCallback
import androidx.compose.animation.core.MutableTransitionState
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.dialog.CancellableResult
import com.vietinbank.core_ui.components.dialog.DialogFrame
import com.vietinbank.core_ui.components.dialog.DialogLayout
import com.vietinbank.core_ui.components.dialog.DialogMotion
import com.vietinbank.core_ui.components.dialog.DialogStackManager
import com.vietinbank.core_ui.components.dialog.DialogUtils
import com.vietinbank.core_ui.theme.AppTheme
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.android.EntryPointAccessors
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.UUID
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Hilt EntryPoint for accessing IAppConfigManager from non-Hilt components
 */
@EntryPoint
@InstallIn(SingletonComponent::class)
interface BaseDialogEntryPoint {
    fun appConfigManager(): IAppConfigManager
}

/**
 * Constants for BaseDialog configuration
 */
private object BaseDialogConstants {
    // Animation durations in milliseconds
    const val EXIT_ANIMATION_DELAY_MS = 50L
    const val IME_HIDE_DELAY_MS = 100L

    // Default dimensions
    val DEFAULT_BOTTOM_SAFE_MIN_PADDING = 24.dp
}

/**
 * Base dialog implementation with Compose UI and proper lifecycle management.
 *
 * Features:
 * - Fragment Result API for type-safe result passing
 * - Proper animation synchronization with MutableTransitionState
 * - Process death recovery
 * - System bar appearance management
 * - IME-aware back handling
 * - Multi-dialog stack support
 * - Banking app security requirements (FLAG_SECURE)
 *
 * Usage Example:
 * ```kotlin
 * // 1. Create your dialog extending BaseDialog
 * class MyDialog : BaseDialog<MyResult>() {
 *     override val resultKey = "my_dialog_result"
 *     override val layout = DialogLayout.Center
 *
 *     @Composable
 *     override fun DialogContent(...) {
 *         // Your UI here
 *         Button(onClick = { onResult(MyResult(data)) }) {
 *             Text("Confirm")
 *         }
 *     }
 * }
 *
 * // 2. Show the dialog
 * MyDialog.show(childFragmentManager, "tag")
 *
 * // 3. Listen for results (in Fragment/Activity that shows the dialog)
 * childFragmentManager.setFragmentResultListener(
 *     "my_dialog_result",
 *     viewLifecycleOwner
 * ) { _, bundle ->
 *     val isCancelled = bundle.getBoolean("key_cancelled", false)
 *     if (!isCancelled) {
 *         val result = bundle.getParcelable<MyResult>("key_result")
 *         // Handle result
 *     }
 * }
 * ```
 *
 * @param R Result type that must be Parcelable
 */
abstract class BaseDialog<R : Parcelable> : DialogFragment() {

    companion object {
        private const val KEY_RESULT = "key_result"
        private const val KEY_CANCELLED = "key_cancelled"
        private const val KEY_DIALOG_ID = "key_dialog_id"
        private const val KEY_PENDING_RESULT = "key_pending_result"
        private const val RESULT_CANCELLED = "cancelled"
    }

    // Abstract properties to be implemented by subclasses
    protected abstract val resultKey: String
    protected open val layout: DialogLayout = DialogLayout.Center
    protected open val motion: DialogMotion = DialogMotion()
    protected open val allowTouchDismiss: Boolean = true
    protected open val requiresSecureFlag: Boolean = false
    protected open val maxWidthDp: Int? = null
    protected open val containerShape: Shape? = null
    protected open val containerColor: Color? = null
    protected open val containerElevation: Dp? = null
    protected open val containerPadding: PaddingValues? = null
    protected open val bottomSafeMinPadding: Dp? = BaseDialogConstants.DEFAULT_BOTTOM_SAFE_MIN_PADDING
    protected open val animateOnFirstShow: Boolean = true // Set to false for IME-focused dialogs

    /**
     * FragmentManager to use for setting fragment results.
     * Override this if your dialog needs to set results on a specific FragmentManager.
     * By default, uses parentFragmentManager.
     */
    protected open val resultFragmentManager: FragmentManager? = null

    // Internal state
    private val dialogId: String = UUID.randomUUID().toString()
    private var visible by mutableStateOf(false)
    private var pendingResult: R? = null
    private var isResultDelivered = false
    private var hasRequestedClose = false
    private val transitionState = MutableTransitionState(false)

    // Back handling
    private var backCallback: OnBackPressedCallback? = null

    @Composable
    protected abstract fun DialogContent(
        visible: Boolean,
        onDismissRequest: () -> Unit,
        onResult: (R) -> Unit,
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Restore pending result after process death
        savedInstanceState?.let { bundle ->
            pendingResult = DialogUtils.getParcelableCompat(bundle, KEY_PENDING_RESULT)
        }

        // NOTE: Do NOT register a listener here - it causes conflict with parent Fragment's listener
        // The parent Fragment/Activity should listen for results, not the dialog itself

        // Push to stack
        DialogStackManager.push(dialogId, this)
    }

    override fun getTheme(): Int {
        // Use custom theme with windowIsFloating=false for edge-to-edge display
        return com.vietinbank.core_ui.R.style.Theme_App_BaseDialog
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return super.onCreateDialog(savedInstanceState).apply {
            // Dialog configuration
            window?.let { window ->
                // Enable edge-to-edge display for the dialog
                // This allows the dialog to draw behind the status bar
                WindowCompat.setDecorFitsSystemWindows(window, false)

                // Handle secure flag for banking security
                if (requiresSecureFlag) {
                    window.addFlags(WindowManager.LayoutParams.FLAG_SECURE)
                }

                // CRITICAL: Prevent system from resizing dialog when keyboard appears
                // We use ADJUST_NOTHING to avoid double IME compensation:
                // - System's adjustResize would shrink window content
                // - Compose's imePadding() would add padding
                // Both together = dialog pushed too high with extra spacing
                // With ADJUST_NOTHING, only Compose handles keyboard positioning
                // REMOVED ALWAYS_VISIBLE to prevent IME flicker - now controlled by Compose
                window.setSoftInputMode(
                    WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING,
                )
            }

            // ALWAYS keep window non-cancelable to force all interactions through Compose
            // This ensures exit animations always play
            setCancelable(false)
            setCanceledOnTouchOutside(false)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        return ComposeView(requireContext()).apply {
            setViewCompositionStrategy(
                ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed,
            )

            setContent {
                // Get IAppConfigManager from Hilt EntryPoint
                val appConfigManager = remember {
                    try {
                        val entryPoint = EntryPointAccessors.fromApplication(
                            requireContext().applicationContext,
                            BaseDialogEntryPoint::class.java,
                        )
                        entryPoint.appConfigManager()
                    } catch (e: Exception) {
                        null // Fallback if Hilt is not configured
                    }
                }

                AppTheme(appConfigManager = appConfigManager) {
                    val scope = rememberCoroutineScope()
                    val view = LocalView.current

                    DialogFrame(
                        visible = visible,
                        layout = layout,
                        motion = motion,
                        transitionState = transitionState,
                        maxWidthDp = maxWidthDp,
                        allowTouchDismiss = allowTouchDismiss,
                        animateOnFirstShow = animateOnFirstShow,
                        horizontalPadding = FDS.Sizer.Padding.padding8,
                        onDismissRequest = { requestClose() },
                        containerShape = containerShape, // modal container from here
                        containerColor = containerColor,
                        containerElevation = containerElevation,
                        containerPadding = containerPadding,
                        bottomSafeMinPadding = bottomSafeMinPadding,
                    ) {
                        DialogContent(
                            visible = visible,
                            onDismissRequest = { requestClose() },
                            onResult = { result -> deliverResult(result) },
                        )
                    }

                    // Handle animation completion
                    LaunchedEffect(transitionState.currentState, transitionState.targetState) {
                        if (!transitionState.currentState && !transitionState.targetState) {
                            // Exit animation complete
                            delay(BaseDialogConstants.EXIT_ANIMATION_DELAY_MS) // Small delay for visual smoothness after animation finishes
                            dismissNow()
                        }
                    }
                }
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Setup back handling with IME awareness
        backCallback = object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                val imm = requireContext().getSystemService(Context.INPUT_METHOD_SERVICE) as? InputMethodManager
                val hasIme = ViewCompat.getRootWindowInsets(requireView())
                    ?.isVisible(WindowInsetsCompat.Type.ime()) == true

                if (hasIme) {
                    // Close keyboard first
                    imm?.hideSoftInputFromWindow(requireView().windowToken, 0)
                    // Let keyboard close, then handle back
                    lifecycleScope.launch {
                        delay(BaseDialogConstants.IME_HIDE_DELAY_MS)
                        if (isAdded) {
                            handleBackPressed()
                        }
                    }
                } else {
                    handleBackPressed()
                }
            }
        }.also { callback ->
            requireActivity().onBackPressedDispatcher.addCallback(
                viewLifecycleOwner,
                callback,
            )
        }
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.apply {
            setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
            // Set light icons for status/nav bar (for dark scrim)
            val controller = WindowCompat.getInsetsController(this, decorView)
            controller.isAppearanceLightStatusBars = false
            controller.isAppearanceLightNavigationBars = false
        }

        // Start enter animation immediately (no delay for IME orchestration)
        // Note: We're already in STARTED state when onStart() is called,
        // so no need for repeatOnLifecycle here
        // Only auto-show if not already requested to close
        if (!hasRequestedClose) {
            visible = true
            transitionState.targetState = true
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putString(KEY_DIALOG_ID, dialogId)
        pendingResult?.let {
            outState.putParcelable(KEY_PENDING_RESULT, it)
        }
    }

    override fun onDestroyView() {
        backCallback?.remove()
        backCallback = null
        super.onDestroyView()
    }

    override fun onDestroy() {
        super.onDestroy()
        // Only pop if we're the top dialog
        if (DialogStackManager.peek() == dialogId) {
            DialogStackManager.pop()
        }
    }

    override fun dismiss() {
        // Override dismiss to always use our animation-aware requestClose
        requestClose()
    }

    override fun dismissAllowingStateLoss() {
        // Override dismissAllowingStateLoss to use requestClose
        requestClose()
    }

    private fun handleBackPressed() {
        if (allowTouchDismiss) {
            requestClose()
        }
    }

    private fun requestClose() {
        if (!visible) return

        hasRequestedClose = true
        lifecycleScope.launch {
            visible = false
            transitionState.targetState = false
            // dismissNow() will be called when animation completes
        }
    }

    override fun dismissNow() {
        try {
            // Always dismiss if fragment is added, regardless of isStateSaved
            // dismissAllowingStateLoss() is designed to handle state loss scenarios
            if (isAdded) {
                super.dismissAllowingStateLoss()
            }
        } catch (e: Exception) {
            // Ignore dismissal errors - dialog may have been dismissed already
        }
    }

    private fun deliverResult(result: R) {
        if (isResultDelivered) return

        pendingResult = result
        isResultDelivered = true

        val bundle = Bundle().apply {
            putParcelable(KEY_RESULT, result)
            putBoolean(KEY_CANCELLED, false)
        }

        // Use the specified resultFragmentManager or fallback to parentFragmentManager
        val targetFragmentManager = resultFragmentManager ?: parentFragmentManager
        targetFragmentManager.setFragmentResult(resultKey, bundle)

        requestClose()
    }

    protected fun deliverCancellation() {
        if (isResultDelivered) return

        isResultDelivered = true

        val bundle = Bundle().apply {
            putBoolean(KEY_CANCELLED, true)
            if (this@BaseDialog is CancellableResult) {
                putString(KEY_RESULT, getCancellationResult())
            }
        }

        // Use the specified resultFragmentManager or fallback to parentFragmentManager
        val targetFragmentManager = resultFragmentManager ?: parentFragmentManager
        targetFragmentManager.setFragmentResult(resultKey, bundle)
        requestClose()
    }
}