package com.vietinbank.core_ui.base.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.commonRoundedCornerCard
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun GetKeypassInstruction(
    modifier: Modifier = Modifier,
    visible: Boolean = false,
    onBack: () -> Unit,
) {
    BaseBottomSheet(
        visible = visible,
        onDismissRequest = onBack,
        shape = RoundedCornerShape(FDS.Sizer.Radius.radius0),
    ) {
        Column {
            Column(
                modifier = modifier.commonRoundedCornerCard(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                FoundationText(
                    text = stringResource(R.string.keypass_dialog_instruction_title),
                    style = FDS.Typography.headingH3,
                    color = FDS.Colors.characterHighlighted,
                )
                HorizontalDivider(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = FDS.Sizer.Padding.padding16),
                    color = FDS.Colors.strokeDivider,
                    thickness = FDS.Sizer.Stroke.stroke1,
                )

                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(180.dp)
                        .padding(horizontal = FDS.Sizer.Padding.padding24)
                        .background(FDS.Colors.backgroundDarkBlue)
                        .clip(RoundedCornerShape(FDS.Sizer.Radius.radius16)),
                ) {
                }
            }

            FoundationButton(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        top = FDS.Sizer.Padding.padding16,
                        start = FDS.Sizer.Padding.padding24,
                        end = FDS.Sizer.Padding.padding24,
                        bottom = FDS.Sizer.Gap.gap36,
                    ),
                text = stringResource(R.string.back_button),
                isLightButton = false,
                onClick = onBack,
            )
        }
    }
}