package com.vietinbank.core_ui.base.sheet.examples

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.core_ui.utils.safeClickable
import java.text.NumberFormat
import java.util.Locale
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Transaction data model
 */
data class Transaction(
    val id: String,
    val description: String,
    val amount: Long, // Amount in VND
    val date: String,
    val type: TransactionType,
)

enum class TransactionType {
    CREDIT, DEBIT
}

/**
 * Example: Transaction List Bottom Sheet with Nested Scrolling
 *
 * Demonstrates:
 * - Nested scrolling with LazyColumn inside bottom sheet
 * - Complex list items with proper styling
 * - Handling large datasets
 * - FLAG_SECURE for sensitive financial data
 * - Selection handling with result
 *
 * Usage:
 * ```kotlin
 * var showTransactions by remember { mutableStateOf(false) }
 *
 * TransactionListBottomSheet(
 *     visible = showTransactions,
 *     transactions = transactionList,
 *     onDismiss = { showTransactions = false },
 *     onTransactionSelected = { transaction ->
 *         navigateToTransactionDetail(transaction.id)
 *         showTransactions = false
 *     }
 * )
 * ```
 */
@Composable
fun TransactionListBottomSheet(
    visible: Boolean,
    transactions: List<Transaction>,
    onDismiss: () -> Unit,
    onTransactionSelected: (Transaction) -> Unit,
) {
    val numberFormat = NumberFormat.getNumberInstance(Locale("vi", "VN"))

    BaseBottomSheet<Transaction>(
        visible = visible,
        onDismissRequest = onDismiss,
        onResult = { transaction ->
            onTransactionSelected(transaction)
            // Auto-dismiss handled by BaseBottomSheet
        },
        skipPartiallyExpanded = false, // Allow partial state for better UX with lists
        secureFlag = true, // Financial data requires FLAG_SECURE
        maxWidthDp = 640,
    ) { onResult ->
        // Title
        FoundationText(
            text = "Lịch sử giao dịch",
            style = FDS.Typography.headingH3,
            color = FDS.Colors.characterHighlighted,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FDS.Sizer.Padding.padding16),
        )

        HorizontalDivider(
            color = FDS.Colors.strokeDivider,
            thickness = FDS.Sizer.Stroke.stroke1,
        )

        // Transaction count
        FoundationText(
            text = "${transactions.size} giao dịch",
            style = FDS.Typography.captionCaptionL,
            color = FDS.Colors.characterTertiary,
            modifier = Modifier.padding(
                horizontal = FDS.Sizer.Padding.padding16,
                vertical = FDS.Sizer.Padding.padding8,
            ),
        )

        // Scrollable transaction list
        // heightIn ensures the sheet doesn't become too tall
        LazyColumn(
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(max = 400.dp), // Limit height for better UX
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
        ) {
            items(
                items = transactions,
                key = { it.id },
            ) { transaction ->
                TransactionItem(
                    transaction = transaction,
                    numberFormat = numberFormat,
                    onClick = { onResult(transaction) },
                )
            }

            // Bottom padding for last item
            item {
                Spacer(modifier = Modifier.height(FDS.Sizer.Padding.padding16))
            }
        }
    }
}

@Composable
private fun TransactionItem(
    transaction: Transaction,
    numberFormat: NumberFormat,
    onClick: () -> Unit,
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = FDS.Sizer.Padding.padding16)
            .safeClickable(onSafeClick = onClick),
        colors = CardDefaults.cardColors(
            containerColor = FDS.Colors.backgroundBgScreen,
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = FDS.Effects.elevationSm,
        ),
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(FDS.Sizer.Padding.padding16),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // Transaction info
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap4),
            ) {
                FoundationText(
                    text = transaction.description,
                    style = FDS.Typography.bodyB2Emphasized,
                    color = FDS.Colors.characterPrimary,
                    maxLines = 2,
                )

                FoundationText(
                    text = transaction.date,
                    style = FDS.Typography.captionCaptionL,
                    color = FDS.Colors.characterTertiary,
                )
            }

            // Amount
            Column(
                horizontalAlignment = Alignment.End,
            ) {
                FoundationText(
                    text = when (transaction.type) {
                        TransactionType.CREDIT -> "+ ${numberFormat.format(transaction.amount)} đ"
                        TransactionType.DEBIT -> "- ${numberFormat.format(transaction.amount)} đ"
                    },
                    style = FDS.Typography.bodyB2Emphasized,
                    color = when (transaction.type) {
                        TransactionType.CREDIT -> FDS.Colors.stateSuccess
                        TransactionType.DEBIT -> FDS.Colors.stateError
                    },
                )
            }
        }
    }
}

/**
 * Mock data generator for preview/testing
 */
fun generateMockTransactions(): List<Transaction> {
    return listOf(
        Transaction(
            id = "1",
            description = "Chuyển tiền đến Nguyễn Văn A",
            amount = 2_500_000,
            date = "15/01/2024 09:30",
            type = TransactionType.DEBIT,
        ),
        Transaction(
            id = "2",
            description = "Nhận tiền từ Công ty ABC",
            amount = 15_000_000,
            date = "14/01/2024 14:20",
            type = TransactionType.CREDIT,
        ),
        Transaction(
            id = "3",
            description = "Thanh toán hóa đơn điện",
            amount = 850_000,
            date = "13/01/2024 10:15",
            type = TransactionType.DEBIT,
        ),
        Transaction(
            id = "4",
            description = "Nhận lương tháng 01/2024",
            amount = 25_000_000,
            date = "10/01/2024 00:01",
            type = TransactionType.CREDIT,
        ),
        Transaction(
            id = "5",
            description = "Mua sắm tại Siêu thị BigC",
            amount = 1_250_000,
            date = "09/01/2024 18:45",
            type = TransactionType.DEBIT,
        ),
    )
}

/**
 * Preview for TransactionListBottomSheet
 */
@Preview(showBackground = true, backgroundColor = 0xFF1A1A1A)
@Composable
private fun TransactionListBottomSheetPreview() {
    AppTheme {
        var showTransactions by remember { mutableStateOf(true) }
        var selectedTransaction by remember { mutableStateOf<Transaction?>(null) }

        Column(modifier = Modifier.padding(FDS.Sizer.Padding.padding16)) {
            if (!showTransactions) {
                FoundationText(
                    text = "Giao dịch đã chọn: ${selectedTransaction?.description ?: "Chưa chọn"}",
                    style = FDS.Typography.bodyB2,
                    color = FDS.Colors.characterPrimary,
                    modifier = Modifier.padding(bottom = FDS.Sizer.Gap.gap16),
                )

                FoundationButton(
                    text = "Xem lịch sử giao dịch",
                    onClick = { showTransactions = true },
                    modifier = Modifier.fillMaxWidth(),
                )
            }
        }

        TransactionListBottomSheet(
            visible = showTransactions,
            transactions = generateMockTransactions(),
            onDismiss = { showTransactions = false },
            onTransactionSelected = { transaction ->
                selectedTransaction = transaction
                // No need to set showTransactions = false, BaseBottomSheet auto-dismisses
            },
        )
    }
}

/**
 * Preview with empty state
 */
@Preview(showBackground = true, backgroundColor = 0xFF1A1A1A, name = "Empty Transactions")
@Composable
private fun EmptyTransactionListPreview() {
    AppTheme {
        var showSheet by remember { mutableStateOf(true) }

        TransactionListBottomSheet(
            visible = showSheet,
            transactions = emptyList(),
            onDismiss = { showSheet = false },
            onTransactionSelected = { },
        )
    }
}