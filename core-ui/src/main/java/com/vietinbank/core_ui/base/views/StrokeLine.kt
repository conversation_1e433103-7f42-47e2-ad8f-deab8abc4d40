package com.vietinbank.core_ui.base.views

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.vietinbank.core_ui.theme.FoundationDesignSystem

@Composable
fun StrokeLine(
    modifier: Modifier = Modifier,
    showBorder: Boolean = true,
) {
    if (showBorder) {
        Box(
            modifier = modifier
                .fillMaxWidth()
                .height(FoundationDesignSystem.Sizer.Stroke.stroke1)
                .background(FoundationDesignSystem.Colors.strokeDivider),
        )
    }
}