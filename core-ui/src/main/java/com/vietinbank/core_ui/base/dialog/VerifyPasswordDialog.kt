package com.vietinbank.core_ui.base.dialog

import android.app.AlertDialog
import android.app.Dialog
import android.os.Bundle
import android.text.InputType
import android.view.LayoutInflater
import android.view.Window
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import com.vietinbank.core_ui.databinding.DialogVerifyPasswordBinding

/**
 * Dialog thông báo
 * Sử dụng AlertDialog truyền thống để đảm bảo tương thích với toàn bộ hệ thống
 */
class VerifyPasswordDialog : DialogFragment() {

    companion object {
        private const val ARG_TITLE = "arg_title"
        private const val ARG_MESSAGE = "arg_message"

        fun newInstance(title: String, message: String): VerifyPasswordDialog {
            val frag = VerifyPasswordDialog()
            val bundle = Bundle()
            bundle.putString(ARG_TITLE, title)
            bundle.putString(ARG_MESSAGE, message)
            frag.arguments = bundle
            return frag
        }
    }

    private var _binding: DialogVerifyPasswordBinding? = null
    private val binding get() = _binding!!

    private var onConfirmListener: ((String) -> Unit)? = null
    private var onDismissClick: (() -> Unit)? = null

    /** Gọi để nhận password. */
    fun setOnConfirmListener(listener: (String) -> Unit) {
        onConfirmListener = listener
    }

    fun setOnDismissClickListener(listener: () -> Unit) {
        onDismissClick = listener
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        // inflate binding
        _binding = DialogVerifyPasswordBinding.inflate(LayoutInflater.from(context))
        val view = binding.root

        // Lấy bundle
        val title = arguments?.getString(ARG_TITLE) ?: "Xác nhận mật khẩu"
        val msg = arguments?.getString(ARG_MESSAGE) ?: "Nhập mật khẩu để xác thực"

        // Setup UI
        binding.tvDialogTitle.text = title
        binding.tvDialogContent.text = msg

        // Nút đóng (X)
        binding.btnClose.setOnClickListener {
            onDismissClick?.invoke()
            dismiss()
        }

        // Ẩn/hiện password => ta dùng isSelected property
        // Lúc đầu => isSelected=false => ic_eye_close
        // Nhấn => toggle isSelected => eye_open + inputType
        binding.imgEye.isSelected = false // close
        binding.etPassword.inputType =
            InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_PASSWORD

        // Thêm hàm onRightDrawableClick => toggle
        binding.imgEye.setOnClickListener {
            togglePasswordVisibility()
        }

        // Xác nhận
        binding.btnConfirm.setOnClickListener {
            val pass = binding.etPassword.text?.toString() ?: ""
            if (pass.isEmpty()) {
                Toast.makeText(requireContext(), "Vui lòng nhập mật khẩu", Toast.LENGTH_SHORT)
                    .show()
            } else {
                onConfirmListener?.invoke(pass)
                dismiss()
            }
        }

        val dialog = AlertDialog.Builder(requireContext())
            .setView(view)
            .setCancelable(true)
            .create()

        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)

        return dialog
    }

    // Toggle password + toggle isSelected => thay đổi icon
    private fun togglePasswordVisibility() {
        binding.imgEye.isSelected = !binding.imgEye.isSelected
        val isVisible = binding.imgEye.isSelected
        if (isVisible) {
            // hiển thị password
            binding.etPassword.inputType =
                InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD
        } else {
            // ẩn password
            binding.etPassword.inputType =
                InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_PASSWORD
        }
        // Set con trỏ cuối text
        binding.etPassword.setSelection(binding.etPassword.text?.length ?: 0)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
