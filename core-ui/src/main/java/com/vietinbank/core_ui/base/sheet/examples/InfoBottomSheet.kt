package com.vietinbank.core_ui.base.sheet.examples

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Example: Info Bottom Sheet (No Result)
 *
 * Demonstrates:
 * - Simple informational sheet without result handling
 * - Using the simplified BaseBottomSheet overload
 * - Icon and styled text display
 * - Single action button
 *
 * Usage:
 * ```kotlin
 * var showInfo by remember { mutableStateOf(false) }
 *
 * InfoBottomSheet(
 *     visible = showInfo,
 *     title = "Thông báo",
 *     message = "Giao dịch của bạn đã được xử lý thành công",
 *     iconRes = R.drawable.ic_commom_success_24,
 *     onDismiss = { showInfo = false }
 * )
 * ```
 */
@Composable
fun InfoBottomSheet(
    visible: Boolean,
    title: String,
    message: String,
    iconRes: Int? = null,
    buttonText: String = stringResource(R.string.dialog_button_confirm),
    onDismiss: () -> Unit,
) {
    // Using simplified BaseBottomSheet without result type
    BaseBottomSheet(
        visible = visible,
        allowTouchDismiss = false,
        onDismissRequest = onDismiss,
        skipPartiallyExpanded = true, // Simple sheet - full or dismissed only
        secureFlag = false, // Info sheets usually don't need FLAG_SECURE
        maxWidthDp = 600, // Tablet guard
    ) {
        // Icon (optional)
        iconRes?.let { icon ->
            Image(
                painter = painterResource(id = icon),
                contentDescription = null,
                modifier = Modifier
                    .size(FDS.Sizer.Icon.icon72)
                    .align(Alignment.CenterHorizontally)
                    .padding(top = FDS.Sizer.Padding.padding16),
            )

            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
        }

        // Title
        FoundationText(
            text = title,
            style = FDS.Typography.headingH3,
            color = FDS.Colors.characterHighlighted,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = FDS.Sizer.Padding.padding16,
                    vertical = FDS.Sizer.Padding.padding16,
                ),
        )

        HorizontalDivider(
            color = FDS.Colors.strokeDivider,
            thickness = FDS.Sizer.Stroke.stroke1,
        )

        // Message
        FoundationText(
            text = message,
            style = FDS.Typography.bodyB2,
            color = FDS.Colors.characterPrimary,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = FDS.Sizer.Padding.padding24,
                    vertical = FDS.Sizer.Padding.padding24,
                ),
        )

        // Single confirmation button
        FoundationButton(
            text = buttonText,
            onClick = onDismiss,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = FDS.Sizer.Padding.padding16),
        )

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
    }
}

/**
 * Success variant with pre-configured icon and styling
 */
@Composable
fun SuccessBottomSheet(
    visible: Boolean,
    message: String,
    onDismiss: () -> Unit,
) {
    InfoBottomSheet(
        visible = visible,
        title = "Thành công",
        message = message,
        iconRes = R.drawable.ic_commom_success_24, // Success icon
        buttonText = "Đóng",
        onDismiss = onDismiss,
    )
}

/**
 * Error variant with pre-configured icon and styling
 */
@Composable
fun ErrorBottomSheet(
    visible: Boolean,
    message: String,
    onDismiss: () -> Unit,
) {
    InfoBottomSheet(
        visible = visible,
        title = "Lỗi",
        message = message,
        iconRes = R.drawable.ic_common_warning_72, // Error/warning icon
        buttonText = "Thử lại",
        onDismiss = onDismiss,
    )
}

/**
 * Preview for InfoBottomSheet variants
 */
@Preview(showBackground = true, backgroundColor = 0xFF1A1A1A, name = "Info Sheet")
@Composable
private fun InfoBottomSheetPreview() {
    AppTheme {
        var showInfo by remember { mutableStateOf(true) }

        Column(modifier = Modifier.padding(FDS.Sizer.Padding.padding16)) {
            if (!showInfo) {
                FoundationButton(
                    text = "Show Info Sheet",
                    onClick = { showInfo = true },
                    modifier = Modifier.fillMaxWidth(),
                )
            }
        }

        InfoBottomSheet(
            visible = showInfo,
            title = "Thông báo",
            message = "Giao dịch của bạn đã được xử lý thành công. Quý khách có thể kiểm tra lại trong lịch sử giao dịch.",
            iconRes = R.drawable.ic_common_warning_72,
            onDismiss = { showInfo = false },
        )
    }
}

@Preview(showBackground = true, backgroundColor = 0xFF1A1A1A, name = "Success Sheet")
@Composable
private fun SuccessBottomSheetPreview() {
    AppTheme {
        var showSuccess by remember { mutableStateOf(true) }

        Column(modifier = Modifier.padding(FDS.Sizer.Padding.padding16)) {
            if (!showSuccess) {
                FoundationButton(
                    text = "Show Success Sheet",
                    onClick = { showSuccess = true },
                    modifier = Modifier.fillMaxWidth(),
                )
            }
        }

        SuccessBottomSheet(
            visible = showSuccess,
            message = "Chuyển tiền thành công! Số tiền 2.500.000đ đã được chuyển đến tài khoản người nhận.",
            onDismiss = { showSuccess = false },
        )
    }
}

@Preview(showBackground = true, backgroundColor = 0xFF1A1A1A, name = "Error Sheet")
@Composable
private fun ErrorBottomSheetPreview() {
    AppTheme {
        var showError by remember { mutableStateOf(true) }

        Column(modifier = Modifier.padding(FDS.Sizer.Padding.padding16)) {
            if (!showError) {
                FoundationButton(
                    text = "Show Error Sheet",
                    onClick = { showError = true },
                    modifier = Modifier.fillMaxWidth(),
                )
            }
        }

        ErrorBottomSheet(
            visible = showError,
            message = "Không thể thực hiện giao dịch. Vui lòng kiểm tra kết nối mạng và thử lại.",
            onDismiss = { showError = false },
        )
    }
}