package com.vietinbank.core_ui.base.imageloader

import android.graphics.Bitmap
import android.net.Uri
import android.widget.ImageView
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier

/**
 * Created by vand<PERSON> on 10/4/25.
 */
/**
 * Interface trừu tượng để load ảnh ở cả XML và Compose.
 */
interface IImageLoader {
    /**
     * 1) Load ảnh từ URL vào ImageView (XML).
     *    Có default param [isCache], [placeholderRes], [errorRes].
     */
    fun loadUrl(
        imageView: ImageView,
        url: String?,
        isCache: Boolean = true,
        placeholderRes: Int? = null,
        errorRes: Int? = null,
    )

    /**
     * 2) Load ảnh từ Bitmap vào ImageView (XML).
     */
    fun loadBitmap(
        imageView: ImageView,
        bitmap: Bitmap?,
        isCache: Boolean = true,
        placeholderRes: Int? = null,
        errorRes: Int? = null,
    )

    /**
     * 3) Load ảnh từ Uri vào ImageView (XML).
     */
    fun loadUri(
        imageView: ImageView,
        uri: Uri?,
        isCache: Boolean = true,
        placeholderRes: Int? = null,
        errorRes: Int? = null,
    )

    /**
     * 4) Load ảnh từ drawable resource vào ImageView (XML).
     */
    fun loadDrawable(
        imageView: ImageView,
        drawableRes: Int,
        isCache: Boolean = true,
        placeholderRes: Int? = null,
        errorRes: Int? = null,
    )

    /**
     * 5) Load ảnh từ URL trong Jetpack Compose.
     *
     */
    @Composable
    fun LoadUrl(
        url: String?,
        isCache: Boolean,
        placeholderRes: Int?,
        errorRes: Int?,
        modifier: Modifier,
    )

    @Composable
    fun LoadByteArray(
        modifier: Modifier,
        byteArray: ByteArray?,
        isCache: Boolean,
        placeholderRes: Int?,
        errorRes: Int?,
    )
}