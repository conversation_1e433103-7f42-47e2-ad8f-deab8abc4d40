package com.vietinbank.core_ui.base.compose

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.RoundRect
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.withTransform
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable

@Composable
fun QrCodeOverlayView(
    modifier: Modifier = Modifier,
    onPickPicture: (() -> Unit)? = null,
) {
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current
    val (paddingHorizontal, scanFinderSizePx, scanFinderTopPx, scanFinderBottomPx, cornerRadiusPx) = remember(
        configuration,
    ) {
        with(density) {
            val paddingHorizontal = 20.dp
            val screenWidth = configuration.screenWidthDp.dp
            val screenHeight = configuration.screenHeightDp.dp
            val size = screenWidth - paddingHorizontal * 2
            val top = (screenHeight - size) / 2 - 70.dp
            val bottom = top + size
            Quintuple(paddingHorizontal, size.toPx(), top.toPx(), bottom.toPx(), 8.dp.toPx())
        }
    }

    val pathOver = remember(
        scanFinderSizePx,
        scanFinderTopPx,
        scanFinderBottomPx,
        cornerRadiusPx,
    ) {
        Path().apply {
            addRoundRect(
                RoundRect(
                    rect = Rect(
                        0f,
                        0f,
                        scanFinderSizePx,
                        scanFinderSizePx,
                    ),
                    cornerRadius = CornerRadius(cornerRadiusPx),
                ),
            )
        }
    }

    Box(modifier = modifier.fillMaxSize()) {
        Canvas(modifier = Modifier.fillMaxSize()) {
            drawRect(color = Color.Black.copy(alpha = 0.6f), size = size)
            val left = (size.width - scanFinderSizePx) / 2
            val top = scanFinderTopPx
            withTransform({ translate(left, top) }) {
                drawPath(path = pathOver, color = Color.Black, blendMode = BlendMode.Clear)
            }
        }
        // top layout
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = paddingHorizontal)
                .offset(y = with(density) { (scanFinderTopPx.toDp() - 40.dp) }),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center,
        ) {
            Image(painter = painterResource(R.drawable.ic_napas_white), contentDescription = null)
            Spacer(modifier = Modifier.width(16.dp))
            Image(
                painter = painterResource(R.drawable.ic_vietinbank_white),
                contentDescription = null,
            )
            Spacer(modifier = Modifier.width(16.dp))
            Image(painter = painterResource(R.drawable.ic_vietqr_white), contentDescription = null)
        }
        // bottom layout
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = paddingHorizontal)
                .offset(y = with(density) { scanFinderBottomPx.toDp() + 20.dp }),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Text(
                text = "Di chuyển camera đến vùng chứa mã QR, tiến trình quét mã sẽ diễn ra tự động",
                style = getComposeFont(4, 16.sp, AppColors.textWarningYellow),
                textAlign = TextAlign.Center,
            )
            Row(
                modifier = Modifier
                    .padding(top = 20.dp)
                    .background(Color(0x4D7ED3F7), RoundedCornerShape(5.dp))
                    .border(1.dp, Color.White, RoundedCornerShape(5.dp))
                    .padding(vertical = 8.dp, horizontal = 15.dp)
                    .safeClickable { onPickPicture?.invoke() },
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Image(
                    painter = painterResource(R.drawable.ic_upload_white),
                    contentDescription = null,
                )
                Spacer(modifier = Modifier.width(10.dp))
                Text(text = "Tải lên mã QR", style = getComposeFont(4, 14.sp, Color.White))
            }
        }
    }
}

data class Quintuple<A, B, C, D, E>(
    val first: A,
    val second: B,
    val third: C,
    val fourth: D,
    val fifth: E,
)