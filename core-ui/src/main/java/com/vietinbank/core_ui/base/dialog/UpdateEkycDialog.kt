package com.vietinbank.core_ui.base.dialog

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.fragment.app.DialogFragment
import com.vietinbank.core_ui.databinding.DialogUpdateEkycBinding

class UpdateEkycDialog : DialogFragment() {
    companion object {
        private const val ARG_TITLE = "arg_title"
        private const val ARG_MESSAGE = "arg_message"
        private const val ARG_BUTTON = "arg_button"
        private const val ARG_SHOW_IMAGE = "arg_show_image"

        fun newInstance(title: String, message: String, buttonText: String, showImage: Boolean = false): UpdateEkycDialog {
            val frag = UpdateEkycDialog()
            val bundle = Bundle()
            bundle.putString(ARG_TITLE, title)
            bundle.putString(ARG_MESSAGE, message)
            bundle.putString(ARG_BUTTON, buttonText)
            bundle.putBoolean(ARG_SHOW_IMAGE, showImage)
            frag.arguments = bundle
            return frag
        }
    }

    private var _binding: DialogUpdateEkycBinding? = null
    private val binding get() = _binding!!

    private var onConfirmListener: (() -> Unit)? = null
    private var onDismissClick: (() -> Unit)? = null

    fun setOnConfirmListener(listener: () -> Unit) {
        onConfirmListener = listener
    }

    fun setOnDismissClickListener(listener: () -> Unit) {
        onDismissClick = listener
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        _binding = DialogUpdateEkycBinding.inflate(LayoutInflater.from(context))
        val view = binding.root

        val title = arguments?.getString(ARG_TITLE) ?: "Thông báo"
        val msg = arguments?.getString(ARG_MESSAGE) ?: "Nhập mật khẩu để xác thực"
        val textButton = arguments?.getString(ARG_BUTTON) ?: "Đăng ký sinh trắc học"
        val showImage = arguments?.getBoolean(ARG_SHOW_IMAGE) ?: false

        binding.tvDialogTitle.text = title
        binding.tvDialogContent.text = msg
        binding.btnPositive.text = textButton
        if (showImage) {
            binding.imageContract.isVisible
        } else {
            binding.imageContract.isGone
        }

        binding.btnClose.setOnClickListener {
            onDismissClick?.invoke()
            dismiss()
        }

        binding.btnPositive.setOnClickListener {
            onConfirmListener?.invoke()
            dismiss()
        }
        return view
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = Dialog(requireContext())
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)

        return dialog
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}