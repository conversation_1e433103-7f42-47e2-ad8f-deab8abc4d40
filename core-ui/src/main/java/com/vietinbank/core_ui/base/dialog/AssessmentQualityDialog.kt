package com.vietinbank.core_ui.base.dialog

import android.app.AlertDialog
import android.app.Dialog
import android.os.Bundle
import android.text.TextUtils
import android.util.TypedValue
import android.view.Gravity
import android.view.LayoutInflater
import android.view.Window
import android.view.WindowManager
import androidx.core.view.isVisible
import androidx.fragment.app.DialogFragment
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.extensions.onSearchTextChanged
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.databinding.DialogAssessmentQualityBinding

class AssessmentQualityDialog : DialogFragment() {
    companion object {
        private const val ARG_MESSAGE = "arg_message"
        private const val RATING_ANGRY = "1"
        private const val RATING_DISAPPOINTED = "2"
        private const val RATING_OKAY = "3"
        private const val RATING_GOOD = "4"
        private const val RATING_EXCELLENT = "5"

        // Hint messages stored as constants to avoid repetition
        private const val HINT_NEGATIVE = "Xin lỗi Quý khách vì những trải nghiệm chưa tốt. Quý khách vui lòng để lại góp ý giúp VietinBank cải thiện dịch vụ."
        private const val HINT_POSITIVE = "Quý khách có thể để lại nhận xét ở đây hoặc bỏ qua"

        fun newInstance(message: String = ""): AssessmentQualityDialog {
            return AssessmentQualityDialog().apply {
                arguments = Bundle().apply {
                    putString(ARG_MESSAGE, message)
                }
            }
        }
    }
    fun newInstance(message: String = ""): AssessmentQualityDialog {
        return AssessmentQualityDialog().apply {
            arguments = Bundle().apply {
                putString(ARG_MESSAGE, message)
            }
        }
    }

    private var onConfirmListener: ((String, String) -> Unit)? = null
    private var _binding: DialogAssessmentQualityBinding? = null
    private val binding get() = _binding!!
    private var rateSubmit = ""

    fun setOnConfirmListener(listener: ((String, String) -> Unit)? = null) {
        onConfirmListener = listener
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        // Inflate binding
        _binding = DialogAssessmentQualityBinding.inflate(LayoutInflater.from(context))

        // Create dialog
        val dialog = AlertDialog.Builder(requireContext())
            .setView(binding.root)
            .setCancelable(true)
            .create()
            .apply {
                requestWindowFeature(Window.FEATURE_NO_TITLE)
                window?.setBackgroundDrawableResource(android.R.color.transparent)
            }

        // Setup UI
        setupInitialRating()
        setupListeners()

        // Apply margin to dialog
        dialog.setOnShowListener {
            dialog.window?.let { window ->
                // Set dialog width and height
                val params = window.attributes.apply {
                    width = WindowManager.LayoutParams.MATCH_PARENT
                    height = WindowManager.LayoutParams.WRAP_CONTENT
                    gravity = Gravity.CENTER
                }
                window.attributes = params

                // Apply 15dp margin
                val marginInDp = 15
                val marginInPx = TypedValue.applyDimension(
                    TypedValue.COMPLEX_UNIT_DIP,
                    marginInDp.toFloat(),
                    resources.displayMetrics,
                ).toInt()

                window.decorView.setPadding(marginInPx, marginInPx, marginInPx, marginInPx)
            }
        }

        return dialog
    }
    private fun setupInitialRating() {
        val ratingString = arguments?.getString(ARG_MESSAGE)

        if (!TextUtils.isEmpty(ratingString)) {
            updateRatingUI(ratingString ?: "")
        }
    }

    private fun setupListeners() {
        // Rating change listener
        binding.rgAssessment.setOnCheckedChangeListener { _, checkedId ->
            val rating = when (checkedId) {
                R.id.rbAngry -> RATING_ANGRY
                R.id.rbDisappointed -> RATING_DISAPPOINTED
                R.id.rbOkay -> RATING_OKAY
                R.id.rbGood -> RATING_GOOD
                else -> RATING_EXCELLENT
            }

            updateRatingUI(rating)
        }

        // Button listeners
        binding.btnConfirm.setThrottleClickListener {
            onConfirmListener?.invoke(rateSubmit, binding.edtComment.text.toString())
            dismiss()
        }

        binding.btnCancel.setThrottleClickListener {
            dismiss()
        }
        binding.edtComment.onSearchTextChanged {
            checkConfirmEnable()
        }
    }

    private fun updateRatingUI(rating: String) {
        rateSubmit = rating

        // Set radio button checked state
        when (rating) {
            RATING_ANGRY -> binding.rbAngry.isChecked = true
            RATING_DISAPPOINTED -> binding.rbDisappointed.isChecked = true
            RATING_OKAY -> binding.rbOkay.isChecked = true
            RATING_GOOD -> binding.rbGood.isChecked = true
            RATING_EXCELLENT -> binding.rbExcellent.isChecked = true
        }

        // Set comment hint based on rating (negative vs positive experience)
        val isNegativeRating = rating == RATING_ANGRY || rating == RATING_DISAPPOINTED
        binding.edtComment.hint = if (isNegativeRating) HINT_NEGATIVE else HINT_POSITIVE

        // Show comment field and confirm button
        binding.edtComment.isVisible = true
        binding.btnConfirm.isVisible = true
        checkConfirmEnable()
    }
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    private fun checkConfirmEnable() {
        binding.btnConfirm.isEnabled = when (rateSubmit) {
            "1", "2" -> {
                binding.edtComment.text.toString().isNotEmpty()
            }
            else -> true
        }
    }
}
