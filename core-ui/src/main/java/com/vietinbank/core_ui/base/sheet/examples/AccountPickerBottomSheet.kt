package com.vietinbank.core_ui.base.sheet.examples

import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.ListItem
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Example: Account Picker Bottom Sheet
 *
 * Demonstrates:
 * - Using BaseBottomSheet with result type (String)
 * - Simple list selection
 * - Proper styling with FDS
 *
 * Usage:
 * ```kotlin
 * var showPicker by remember { mutableStateOf(false) }
 * var selectedAccount by remember { mutableStateOf<String?>(null) }
 *
 * AccountPickerBottomSheet(
 *     visible = showPicker,
 *     accounts = listOf("Account 1", "Account 2", "Account 3"),
 *     onDismiss = { showPicker = false },
 *     onPicked = { account ->
 *         selectedAccount = account
 *         showPicker = false
 *     }
 * )
 * ```
 */
@Composable
fun AccountPickerBottomSheet(
    visible: Boolean,
    accounts: List<String>,
    onDismiss: () -> Unit,
    onPicked: (String) -> Unit,
) {
    BaseBottomSheet<String>(
        visible = visible,
        onDismissRequest = onDismiss,
        onResult = { picked ->
            onPicked(picked)
            // Auto-dismiss handled by BaseBottomSheet
        },
        secureFlag = false, // Set to true if showing sensitive account data
        maxWidthDp = 640, // Tablet guard - prevent too wide sheets
    ) { onResult ->
        // Title
        FoundationText(
            text = "Chọn tài khoản",
            style = FDS.Typography.headingH3,
            color = FDS.Colors.characterHighlighted,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FDS.Sizer.Padding.padding16),
        )

        HorizontalDivider(
            color = FDS.Colors.strokeDivider,
            thickness = FDS.Sizer.Stroke.stroke1,
        )

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))

        // Account list
        accounts.forEach { account ->
            ListItem(
                headlineContent = {
                    FoundationText(
                        text = account,
                        style = FDS.Typography.bodyB2,
                        color = FDS.Colors.characterPrimary,
                    )
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .safeClickable(
                        onSafeClick = {
                            onResult(account) // This will trigger onPicked and auto-dismiss
                        },
                    )
                    .padding(vertical = FDS.Sizer.Padding.padding4),
            )

            HorizontalDivider(
                color = FDS.Colors.strokeDivider,
                thickness = FDS.Sizer.Stroke.stroke1,
                modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding16),
            )
        }

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))

        // Cancel button
        TextButton(
            onClick = onDismiss,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = FDS.Sizer.Padding.padding16),
        ) {
            FoundationText(
                text = stringResource(R.string.dialog_button_cancel),
                style = FDS.Typography.bodyB2Emphasized,
                color = FDS.Colors.characterTertiary,
            )
        }
    }
}

/**
 * Preview wrapper for AccountPickerBottomSheet
 */
@Preview(showBackground = true, backgroundColor = 0xFF1A1A1A)
@Composable
private fun AccountPickerBottomSheetPreview() {
    AppTheme {
        var showPicker by remember { mutableStateOf(true) }
        var selectedAccount by remember { mutableStateOf<String?>(null) }

        // Show button to trigger sheet
        if (!showPicker) {
            FoundationButton(
                text = "Show Account Picker (Selected: ${selectedAccount ?: "None"})",
                onClick = { showPicker = true },
                modifier = Modifier.padding(FDS.Sizer.Padding.padding16),
            )
        }

        // Account picker sheet
        AccountPickerBottomSheet(
            visible = showPicker,
            accounts = listOf(
                "Tài khoản thanh toán - **********",
                "Tài khoản tiết kiệm - **********",
                "Tài khoản tín dụng - **********",
                "Tài khoản đầu tư - **********",
            ),
            onDismiss = { showPicker = false },
            onPicked = { account ->
                selectedAccount = account
                // No need to set showPicker = false, BaseBottomSheet auto-dismisses
            },
        )
    }
}