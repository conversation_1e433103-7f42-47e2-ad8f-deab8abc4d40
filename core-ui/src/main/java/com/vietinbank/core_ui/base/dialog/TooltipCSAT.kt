package com.vietinbank.core_ui.base.dialog

import android.content.Context
import android.graphics.drawable.BitmapDrawable
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_ui.databinding.TooltipCsatBinding

class TooltipCSAT(private val context: Context) {
    private lateinit var binding: TooltipCsatBinding
    private var popupWindow: PopupWindow? = null
    private var onClickEmoji: (String) -> Unit = {}

    fun setOnClickItemListener(action: (String?) -> Unit) {
        onClickEmoji = action
    }

    fun show(anchorView: View) {
        binding = TooltipCsatBinding.inflate(LayoutInflater.from(context))
        binding.rateSubmit1.setThrottleClickListener {
            onClickEmoji.invoke("1")
        }
        binding.rateSubmit2.setThrottleClickListener {
            onClickEmoji.invoke("2")
        }
        binding.rateSubmit3.setThrottleClickListener {
            onClickEmoji.invoke("3")
        }
        binding.rateSubmit4.setThrottleClickListener {
            onClickEmoji.invoke("4")
        }
        binding.rateSubmit5.setThrottleClickListener {
            onClickEmoji.invoke("5")
        }
        binding.tooltipArrow.rotation = 0f

        popupWindow = PopupWindow(
            binding.root, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT,
        ).apply {
            isOutsideTouchable = true
            isFocusable = false
            elevation = 10f
            setBackgroundDrawable(BitmapDrawable())
        }
        binding.root.measure(
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
        )

        Handler(Looper.getMainLooper()).post {
            val yOffset = -(binding.root.measuredHeight + anchorView.height)
            popupWindow?.showAsDropDown(anchorView, 0, yOffset, 0)
        }
    }

    fun dismiss() {
        popupWindow?.dismiss()
        popupWindow = null
    }
}