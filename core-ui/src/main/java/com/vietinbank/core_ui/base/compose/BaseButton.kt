package com.vietinbank.core_ui.base.compose

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ui.theme.AppColors

@Preview
@Composable
fun BaseButton(
    modifier: Modifier = Modifier,
    text: String? = null,
    isCancelButton: Boolean = false,
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(48.dp)
            .then(
                if (!isCancelButton) {
                    Modifier.border(
                        width = 1.dp,
                        brush = Brush.horizontalGradient(
                            colors = listOf(
                                AppColors.linearButtonStart,
                                AppColors.linearButtonEnd,
                            ),
                        ),
                        shape = RoundedCornerShape(2.dp),
                    )
                } else {
                    Modifier
                },
            )
            .background(
                brush = Brush.horizontalGradient(
                    colors = if (!isCancelButton) {
                        listOf(
                            AppColors.gradientButtonStart,
                            AppColors.gradientButtonEnd,
                        )
                    } else {
                        listOf(
                            Color(0xFFE6EDF3),
                            Color(0xFFE6EDF3),
                        )
                    },
                ),
                shape = RoundedCornerShape(2.dp),
            ),
        contentAlignment = Alignment.Center,
    ) {
        Text(
            modifier = Modifier.padding(7.dp),
            text = text ?: "",
            style = getComposeFont(
                5,
                14.sp,
                if (!isCancelButton) Color.White else Color(0xFF005993),
            ),
        )
    }
}