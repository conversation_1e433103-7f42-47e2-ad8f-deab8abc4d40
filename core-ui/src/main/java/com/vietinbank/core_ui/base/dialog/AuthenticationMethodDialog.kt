package com.vietinbank.core_ui.base.dialog

import android.os.Bundle
import android.os.Parcelable
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.RadioButton
import androidx.compose.material3.RadioButtonDefaults
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.FragmentManager
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.ButtonSize
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.dialog.DialogLayout
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.core_ui.utils.safeClickable
import kotlinx.parcelize.Parcelize
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Constants for AuthenticationMethodDialog
 * Only non-design values that are not part of FDS
 */
private object AuthenticationMethodDialogConstants {
    const val MAX_WIDTH_DP = 600 // Tablet constraint
    const val GRADIENT_START_Y = 0f // Gradient vertical start position
}

/**
 * Result for AuthenticationMethodDialog
 */
@Parcelize
data class AuthenticationMethodResult(
    val selectedMethod: String, // "softotp", "keypass", or "vnptsmartca"
) : Parcelable {
    override fun describeContents(): Int = 0
}

/**
 * Dialog for selecting authentication method after pre-approval
 * Extends BaseDialog which handles all lifecycle, animation, and backdrop
 * UI follows Figma design node-id=11451-61862
 */
class AuthenticationMethodDialog : BaseDialog<AuthenticationMethodResult>() {

    companion object {
        private const val ARG_HAS_SOFT_OTP = "arg_has_soft_otp"
        private const val ARG_HAS_KEYPASS = "arg_has_keypass"
        private const val ARG_HAS_SMART_CA = "arg_has_smart_ca"
        private const val ARG_IS_APPROVE = "arg_is_approve"

        fun newInstance(
            hasSoftOTP: Boolean = false,
            hasKeypass: Boolean = false,
            hasSmartCA: Boolean = false,
            isApprove: Boolean = true,
        ): AuthenticationMethodDialog {
            return AuthenticationMethodDialog().apply {
                arguments = Bundle().apply {
                    putBoolean(ARG_HAS_SOFT_OTP, hasSoftOTP)
                    putBoolean(ARG_HAS_KEYPASS, hasKeypass)
                    putBoolean(ARG_HAS_SMART_CA, hasSmartCA)
                    putBoolean(ARG_IS_APPROVE, isApprove)
                }
            }
        }

        fun show(
            fragmentManager: FragmentManager,
            hasSoftOTP: Boolean = false,
            hasKeypass: Boolean = false,
            hasSmartCA: Boolean = false,
            isApprove: Boolean = true,
        ): AuthenticationMethodDialog {
            com.vietinbank.core_common.extensions.printLog("AuthenticationMethodDialog.show: Creating new instance")
            val dialog = newInstance(hasSoftOTP, hasKeypass, hasSmartCA, isApprove)

            try {
                // Check if dialog already showing
                val existingDialog = fragmentManager.findFragmentByTag("AuthenticationMethodDialog")
                if (existingDialog != null) {
                    com.vietinbank.core_common.extensions.printLog("AuthenticationMethodDialog.show: Found existing dialog, dismissing it")
                    (existingDialog as? AuthenticationMethodDialog)?.dismissAllowingStateLoss()
                }

                // Check if fragment manager can commit
                if (fragmentManager.isStateSaved) {
                    com.vietinbank.core_common.extensions.printLog("AuthenticationMethodDialog.show: FragmentManager state is saved, using commitAllowingStateLoss")
                    dialog.showNow(fragmentManager, "AuthenticationMethodDialog")
                } else {
                    com.vietinbank.core_common.extensions.printLog("AuthenticationMethodDialog.show: Normal show")
                    dialog.show(fragmentManager, "AuthenticationMethodDialog")
                }

                // Verify dialog was added
                fragmentManager.executePendingTransactions()
                val addedDialog = fragmentManager.findFragmentByTag("AuthenticationMethodDialog")
                com.vietinbank.core_common.extensions.printLog("AuthenticationMethodDialog.show: Dialog added to FragmentManager? ${addedDialog != null}")

                if (addedDialog != null) {
                    com.vietinbank.core_common.extensions.printLog("AuthenticationMethodDialog.show: Dialog isAdded=${addedDialog.isAdded}, isVisible=${addedDialog.isVisible}")
                }
            } catch (e: Exception) {
                com.vietinbank.core_common.extensions.printLog("AuthenticationMethodDialog.show: Exception - ${e.message}")
                e.printStackTrace()
            }
            return dialog
        }
    }

    override val resultKey: String = "authentication_method_result"
    override val layout: DialogLayout = DialogLayout.BottomSheet // Bottom sheet as per Figma
    override val requiresSecureFlag: Boolean = true // Banking security
    override val maxWidthDp: Int =
        AuthenticationMethodDialogConstants.MAX_WIDTH_DP // Tablet constraint

    override fun onCreate(savedInstanceState: android.os.Bundle?) {
        super.onCreate(savedInstanceState)
        com.vietinbank.core_common.extensions.printLog("AuthenticationMethodDialog.onCreate: Dialog created")
        val hasSoftOTP = arguments?.getBoolean(ARG_HAS_SOFT_OTP, false) ?: false
        val hasKeypass = arguments?.getBoolean(ARG_HAS_KEYPASS, false) ?: false
        val hasSmartCA = arguments?.getBoolean(ARG_HAS_SMART_CA, false) ?: false
        val isApprove = arguments?.getBoolean(ARG_IS_APPROVE, true) ?: true
        com.vietinbank.core_common.extensions.printLog("AuthenticationMethodDialog.onCreate: hasSoftOTP=$hasSoftOTP, hasKeypass=$hasKeypass, hasSmartCA=$hasSmartCA, isApprove=$isApprove")
    }

    override fun onStart() {
        super.onStart()
        com.vietinbank.core_common.extensions.printLog("AuthenticationMethodDialog.onStart: Dialog started, isVisible=$isVisible, isAdded=$isAdded")
    }

    @Composable
    override fun DialogContent(
        visible: Boolean,
        onDismissRequest: () -> Unit,
        onResult: (AuthenticationMethodResult) -> Unit,
    ) {
        val hasSoftOTP = arguments?.getBoolean(ARG_HAS_SOFT_OTP, false) ?: false
        val hasKeypass = arguments?.getBoolean(ARG_HAS_KEYPASS, false) ?: false
        val hasSmartCA = arguments?.getBoolean(ARG_HAS_SMART_CA, false) ?: false

        com.vietinbank.core_common.extensions.printLog("AuthenticationMethodDialog.DialogContent: Composing UI, visible=$visible")

        AuthenticationMethodContent(
            hasSoftOTP = hasSoftOTP,
            hasKeypass = hasKeypass,
            hasSmartCA = hasSmartCA,
            onConfirm = { selectedMethod ->
                printLog("AuthenticationMethodDialog.DialogContent: User selected $selectedMethod, calling onResult")
                onResult(AuthenticationMethodResult(selectedMethod))
                printLog("AuthenticationMethodDialog.DialogContent: onResult called with AuthenticationMethodResult($selectedMethod)")
            },
            onBack = {
                printLog("AuthenticationMethodDialog.DialogContent: User clicked back")
                onDismissRequest()
            },
        )
    }
}

@Composable
private fun AuthenticationMethodContent(
    hasSoftOTP: Boolean,
    hasKeypass: Boolean,
    hasSmartCA: Boolean,
    onConfirm: (String) -> Unit,
    onBack: () -> Unit,
) {
    com.vietinbank.core_common.extensions.printLog("AuthenticationMethodContent: Rendering with hasSoftOTP=$hasSoftOTP, hasKeypass=$hasKeypass, hasSmartCA=$hasSmartCA")

    // Auto-select first available method
    var selectedMethod by remember {
        mutableStateOf(
            when {
                hasSoftOTP -> "softotp"
                hasKeypass -> "keypass"
                hasSmartCA -> "vnptsmartca"
                else -> ""
            },
        )
    }

    // Pre-resolve colors in Composable scope for DrawScope usage
    val gradientTopColor = FDS.Colors.blue800
    val gradientBottomColor = FDS.Colors.blue900

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = FDS.Sizer.Padding.padding8),
    ) {
        // White container matching Figma design
        Surface(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
            color = FDS.Colors.white,
            shadowElevation = FDS.Effects.elevationLg,
        ) {
            Column {
                // Header section
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = FDS.Sizer.Padding.padding24),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    // Title as per Figma - "Phương thức xác thực"
                    FoundationText(
                        text = stringResource(com.vietinbank.core_ui.R.string.auth_method_dialog_title),
                        style = FDS.Typography.headingH3, // 20sp SemiBold as per Figma
                        color = FDS.Colors.characterHighlighted, // #0F4C7A
                        textAlign = TextAlign.Center,
                    )
                }

                // Divider as per Figma
                HorizontalDivider(
                    color = FDS.Colors.strokeDivider,
                    thickness = FDS.Sizer.Stroke.stroke1,
                )

                // Options section
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            horizontal = FDS.Sizer.Padding.padding24,
                            vertical = FDS.Sizer.Padding.padding24,
                        ),
                    horizontalArrangement = Arrangement.Start,
                ) {
                    // Soft OTP option
                    if (hasSoftOTP) {
                        AuthMethodOption(
                            text = stringResource(R.string.auth_method_soft_otp),
                            selected = selectedMethod == "softotp",
                            onClick = { selectedMethod = "softotp" },
                            modifier = Modifier.weight(1f),
                        )
                    }

                    // Keypass option
                    if (hasKeypass) {
                        AuthMethodOption(
                            text = stringResource(R.string.auth_method_keypass),
                            selected = selectedMethod == "keypass",
                            onClick = { selectedMethod = "keypass" },
                            modifier = Modifier.weight(1f),
                        )
                    }

                    // Smart CA option (if needed)
                    if (hasSmartCA) {
                        AuthMethodOption(
                            text = stringResource(R.string.auth_method_smart_ca),
                            selected = selectedMethod == "vnptsmartca",
                            onClick = { selectedMethod = "vnptsmartca" },
                            modifier = Modifier.weight(1f),
                        )
                    }
                }
            }
        }

        // Spacing between container and buttons
        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))

        // Button area - separated from white container as per Figma
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = FDS.Sizer.Padding.padding16),
            horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
        ) {
            // "Quay lại" button with gradient background for visibility
            Box(
                modifier = Modifier.weight(1f),
            ) {
                // Gradient background only for dark button
                Box(
                    modifier = Modifier
                        .matchParentSize()
                        .background(
                            Brush.verticalGradient(
                                colors = listOf(
                                    gradientTopColor, // FDS.Colors.blue800
                                    gradientBottomColor, // FDS.Colors.blue900
                                ),
                                startY = AuthenticationMethodDialogConstants.GRADIENT_START_Y,
                            ),
                            shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
                        ),
                )

                FoundationButton(
                    text = stringResource(com.vietinbank.core_ui.R.string.auth_method_button_back),
                    onClick = onBack,
                    modifier = Modifier.fillMaxWidth(),
                    isLightButton = false, // Dark style (transparent)
                    size = ButtonSize.Large,
                )
            }

            // "Tiếp tục" button (no background needed - it's already visible)
            FoundationButton(
                text = stringResource(com.vietinbank.core_ui.R.string.auth_method_button_continue),
                onClick = {
                    com.vietinbank.core_common.extensions.printLog("AuthenticationMethodContent: Continue button clicked, selectedMethod = $selectedMethod")
                    if (selectedMethod.isNotEmpty()) {
                        com.vietinbank.core_common.extensions.printLog("AuthenticationMethodContent: Calling onConfirm with $selectedMethod")
                        onConfirm(selectedMethod)
                    } else {
                        com.vietinbank.core_common.extensions.printLog("AuthenticationMethodContent: selectedMethod is empty, not calling onConfirm")
                    }
                },
                modifier = Modifier.weight(1f),
                isLightButton = true, // Light style
                enabled = selectedMethod.isNotEmpty(),
                size = ButtonSize.Large,
            )
        }

        // Bottom padding
        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
    }
}

@Composable
private fun AuthMethodOption(
    text: String,
    selected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .safeClickable { onClick() }
            .padding(FDS.Sizer.Padding.padding8),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Start,
    ) {
        RadioButton(
            selected = selected,
            onClick = onClick,
            colors = RadioButtonDefaults.colors(
                selectedColor = FDS.Colors.primary,
                unselectedColor = FDS.Colors.characterSecondary,
            ),
        )

        Spacer(modifier = Modifier.padding(horizontal = FDS.Sizer.Gap.gap4))

        // Text style as per Figma - Body B1 Emphasized (18sp Bold)
        FoundationText(
            text = text,
            style = FDS.Typography.bodyB1Emphasized,
            color = FDS.Colors.characterPrimary,
        )
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewAuthenticationMethodContent() {
    AppTheme {
        AuthenticationMethodContent(
            hasSoftOTP = true,
            hasKeypass = true,
            hasSmartCA = false,
            onConfirm = {},
            onBack = {},
        )
    }
}