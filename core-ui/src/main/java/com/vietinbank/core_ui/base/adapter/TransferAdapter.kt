package com.vietinbank.core_ui.base.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.vietinbank.core_common.models.TransferObject
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.databinding.ItemContentConfirmMakerBinding
import com.vietinbank.core_ui.databinding.ItemTitleConfirmMakerBinding

class TransferAdapter :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    private var dataSet: MutableList<TransferObject> = mutableListOf()

    companion object {
        const val TYPE_GROUP_TITLE = 0
        const val TYPE_GROUP_CONTENT = 1
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setData(data: MutableList<TransferObject>) {
        dataSet.clear()
        dataSet.addAll(data)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_GROUP_TITLE -> GroupTitleViewHolder(
                ItemTitleConfirmMakerBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false,
                ),
            )

            TYPE_GROUP_CONTENT -> GroupContentViewHolder(
                ItemContentConfirmMakerBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false,
                ),
            )

            else -> throw IllegalArgumentException("Unknown view type")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is GroupTitleViewHolder -> holder.bind(position)
            is GroupContentViewHolder -> holder.bind(position)
        }
    }

    override fun getItemCount(): Int = dataSet.size
    override fun getItemViewType(position: Int): Int {
        return when (dataSet[position].group) {
            TYPE_GROUP_TITLE -> TYPE_GROUP_TITLE
            TYPE_GROUP_CONTENT -> TYPE_GROUP_CONTENT
            else -> throw IllegalArgumentException("Unknown group")
        }
    }

    inner class GroupTitleViewHolder(private val binding: ItemTitleConfirmMakerBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(position: Int) {
            val item = dataSet[position]
            binding.tvTitle.text = item.title ?: ""
        }
    }

    inner class GroupContentViewHolder(private val binding: ItemContentConfirmMakerBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(position: Int) {
            val item = dataSet[position]
            if (position >= 1 && item.group == TYPE_GROUP_CONTENT && dataSet[position - 1].group == TYPE_GROUP_TITLE) {
                // top
                binding.root.setBackgroundResource(R.drawable.bg_white_raidius_top_2dp)
            } else if (position >= 1 && position < dataSet.size - 1 && item.group == TYPE_GROUP_CONTENT && dataSet[position + 1].group != null && dataSet[position + 1].group == TYPE_GROUP_TITLE) {
                // bottom
                binding.root.setBackgroundResource(R.drawable.bg_white_raidius_bottom_2dp)
            } else if (position >= 1 && position == dataSet.size - 1 && item.group == TYPE_GROUP_CONTENT) {
                // bottom
                binding.root.setBackgroundResource(R.drawable.bg_white_raidius_bottom_2dp)
            } else if (position >= 1 && position < dataSet.size - 1 && item.group == TYPE_GROUP_CONTENT && dataSet[position + 1].group == TYPE_GROUP_CONTENT && dataSet[position - 1].group == TYPE_GROUP_CONTENT) {
                // center
                binding.root.setBackgroundColor(
                    ContextCompat.getColor(
                        binding.root.context,
                        R.color.white,
                    ),
                )
            } else {
                // alone
                binding.root.setBackgroundResource(R.drawable.bg_white_radius_2dp)
            }
            binding.tvTitle.text = item.title ?: ""
            binding.tvContent.text = item.content ?: ""
        }
    }
}
