package com.vietinbank.core_ui.base.compose

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.theme.AppTheme

/**
 * Input mode for the component
 */
enum class InputMode {
    PIN, // Individual digit boxes
    PASSWORD, // Single text field with visibility toggle
}

/**
 * A composable that provides PIN or Password input functionality
 *
 * @param value Current input value
 * @param onValueChange Callback when value changes
 * @param modifier Modifier for the component
 * @param inputMode Mode of input (PIN or PASSWORD)
 * @param pinLength Length of PIN (4-6 digits), only used in PIN mode
 * @param label Optional label text
 * @param hint Hint text for the input
 * @param isError Whether the input is in error state
 * @param errorMessage Error message to display
 * @param enabled Whether the input is enabled
 * @param textSize Size of the input text
 * @param textColor Color of the input text
 * @param hintColor Color of the hint text
 * @param backgroundColor Background color of the input
 * @param borderColor Border color of the input
 * @param errorBorderColor Border color when in error state
 * @param focusedBorderColor Border color when focused
 * @param borderWidth Width of the border
 * @param cornerRadius Corner radius of the input boxes
 * @param boxSize Size of each PIN box (only for PIN mode)
 * @param boxSpacing Spacing between PIN boxes (only for PIN mode)
 * @param onDone Callback when input is complete
 */
@Composable
fun PinPasswordInput(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    inputMode: InputMode = InputMode.PIN,
    pinLength: Int = 6,
    label: String? = null,
    hint: String = "",
    isError: Boolean = false,
    errorMessage: String? = null,
    enabled: Boolean = true,
    textSize: TextUnit = 18.sp,
    textColor: Color = AppColors.textPrimary,
    hintColor: Color = AppColors.textHint,
    backgroundColor: Color = AppColors.inputBackground,
    borderColor: Color = AppColors.borderColor,
    errorBorderColor: Color = AppColors.error,
    focusedBorderColor: Color = AppColors.primary,
    borderWidth: Dp = 1.dp,
    cornerRadius: Dp = 8.dp,
    boxSize: Dp = 48.dp,
    boxSpacing: Dp = 8.dp,
    onDone: (() -> Unit)? = null,
) {
    // Validate PIN length
    val validPinLength = pinLength.coerceIn(4, 6)

    Column(modifier = modifier) {
        // Label
        label?.let {
            BaseText(
                text = it,
                textSize = 14.sp,
                fontWeight = FontWeight.Medium,
                textColor = if (isError) errorBorderColor else textColor,
                modifier = Modifier.padding(bottom = 8.dp),
            )
        }

        // Input field based on mode
        when (inputMode) {
            InputMode.PIN -> {
                PinInputField(
                    value = value,
                    onValueChange = onValueChange,
                    pinLength = validPinLength,
                    isError = isError,
                    enabled = enabled,
                    textSize = textSize,
                    textColor = textColor,
                    backgroundColor = backgroundColor,
                    borderColor = borderColor,
                    errorBorderColor = errorBorderColor,
                    focusedBorderColor = focusedBorderColor,
                    borderWidth = borderWidth,
                    cornerRadius = cornerRadius,
                    boxSize = boxSize,
                    boxSpacing = boxSpacing,
                    onDone = onDone,
                )
            }
            InputMode.PASSWORD -> {
                PasswordInputField(
                    value = value,
                    onValueChange = onValueChange,
                    hint = hint,
                    isError = isError,
                    enabled = enabled,
                    textSize = textSize,
                    textColor = textColor,
                    hintColor = hintColor,
                    backgroundColor = backgroundColor,
                    borderColor = borderColor,
                    errorBorderColor = errorBorderColor,
                    focusedBorderColor = focusedBorderColor,
                    borderWidth = borderWidth,
                    cornerRadius = cornerRadius,
                    onDone = onDone,
                )
            }
        }

        // Error message
        if (isError && !errorMessage.isNullOrEmpty()) {
            BaseText(
                text = errorMessage,
                textSize = 12.sp,
                textColor = errorBorderColor,
                modifier = Modifier.padding(start = 8.dp, top = 4.dp),
            )
        }
    }
}

@Composable
private fun PinInputField(
    value: String,
    onValueChange: (String) -> Unit,
    pinLength: Int,
    isError: Boolean,
    enabled: Boolean,
    textSize: TextUnit,
    textColor: Color,
    backgroundColor: Color,
    borderColor: Color,
    errorBorderColor: Color,
    focusedBorderColor: Color,
    borderWidth: Dp,
    cornerRadius: Dp,
    boxSize: Dp,
    boxSpacing: Dp,
    onDone: (() -> Unit)?,
) {
    val focusRequester = remember { FocusRequester() }
    val focusManager = LocalFocusManager.current
    var isFocused by remember { mutableStateOf(false) }

    // Hidden text field for actual input
    Box {
        BasicTextField(
            value = value,
            onValueChange = { newValue ->
                // Only allow numeric input up to pinLength
                val filtered = newValue.filter { it.isDigit() }.take(pinLength)
                onValueChange(filtered)

                // Call onDone when PIN is complete
                if (filtered.length == pinLength) {
                    onDone?.invoke()
                    focusManager.clearFocus()
                }
            },
            modifier = Modifier
                .size(0.dp)
                .focusRequester(focusRequester)
                .onFocusChanged { isFocused = it.isFocused },
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.NumberPassword,
                imeAction = ImeAction.Done,
            ),
            visualTransformation = VisualTransformation.None,
            singleLine = true,
            enabled = enabled,
        )

        // Visual PIN boxes
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable(enabled = enabled) {
                    focusRequester.requestFocus()
                },
            horizontalArrangement = Arrangement.Center,
        ) {
            repeat(pinLength) { index ->
                PinBox(
                    digit = value.getOrNull(index),
                    isError = isError,
                    isFocused = isFocused && value.length == index,
                    backgroundColor = backgroundColor,
                    borderColor = borderColor,
                    errorBorderColor = errorBorderColor,
                    focusedBorderColor = focusedBorderColor,
                    borderWidth = borderWidth,
                    cornerRadius = cornerRadius,
                    boxSize = boxSize,
                    textSize = textSize,
                    textColor = textColor,
                )

                if (index < pinLength - 1) {
                    Spacer(modifier = Modifier.width(boxSpacing))
                }
            }
        }
    }
}

@Composable
private fun PinBox(
    digit: Char?,
    isError: Boolean,
    isFocused: Boolean,
    backgroundColor: Color,
    borderColor: Color,
    errorBorderColor: Color,
    focusedBorderColor: Color,
    borderWidth: Dp,
    cornerRadius: Dp,
    boxSize: Dp,
    textSize: TextUnit,
    textColor: Color,
) {
    val animatedBorderColor by animateColorAsState(
        targetValue = when {
            isError -> errorBorderColor
            isFocused -> focusedBorderColor
            else -> borderColor
        },
        label = "borderColor",
    )

    val animatedAlpha by animateFloatAsState(
        targetValue = if (digit != null) 1f else 0.3f,
        label = "alpha",
    )

    Box(
        modifier = Modifier
            .size(boxSize)
            .clip(RoundedCornerShape(cornerRadius))
            .background(backgroundColor)
            .border(
                width = if (isFocused) borderWidth * 2 else borderWidth,
                color = animatedBorderColor,
                shape = RoundedCornerShape(cornerRadius),
            ),
        contentAlignment = Alignment.Center,
    ) {
        if (digit != null) {
            BaseText(
                text = "•", // Masked digit
                textSize = textSize * 1.5f,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center,
                textColor = textColor,
            )
        } else {
            // Show placeholder dot when empty
            Box(
                modifier = Modifier
                    .size(8.dp)
                    .alpha(animatedAlpha)
                    .background(
                        color = textColor.copy(alpha = 0.3f),
                        shape = RoundedCornerShape(50),
                    ),
            )
        }
    }
}

@Composable
private fun PasswordInputField(
    value: String,
    onValueChange: (String) -> Unit,
    hint: String,
    isError: Boolean,
    enabled: Boolean,
    textSize: TextUnit,
    textColor: Color,
    hintColor: Color,
    backgroundColor: Color,
    borderColor: Color,
    errorBorderColor: Color,
    focusedBorderColor: Color,
    borderWidth: Dp,
    cornerRadius: Dp,
    onDone: (() -> Unit)?,
) {
    var passwordVisible by remember { mutableStateOf(false) }
    var isFocused by remember { mutableStateOf(false) }
    val focusRequester = remember { FocusRequester() }
    val focusManager = LocalFocusManager.current

    val animatedBorderColor by animateColorAsState(
        targetValue = when {
            isError -> errorBorderColor
            isFocused -> focusedBorderColor
            else -> borderColor
        },
        label = "borderColor",
    )

    OutlinedTextField(
        value = value,
        onValueChange = onValueChange,
        modifier = Modifier
            .fillMaxWidth()
            .focusRequester(focusRequester)
            .onFocusChanged { isFocused = it.isFocused },
        label = {
            BaseText(
                text = hint,
                textColor = if (isError) errorBorderColor else hintColor,
            )
        },
        placeholder = {
            if (isFocused && value.isEmpty()) {
                BaseText(
                    text = hint,
                    textColor = hintColor.copy(alpha = 0.6f),
                )
            }
        },
        trailingIcon = {
            IconButton(
                onClick = { passwordVisible = !passwordVisible },
                modifier = Modifier.size(24.dp),
            ) {
                Icon(
                    painter = painterResource(
                        id = if (passwordVisible) {
                            R.drawable.ic_dangnhap_eye_close
                        } else {
                            R.drawable.ic_dangnhap_eye_open
                        },
                    ),
                    contentDescription = if (passwordVisible) "Hide password" else "Show password",
                    tint = hintColor,
                    modifier = Modifier.size(20.dp),
                )
            }
        },
        visualTransformation = if (passwordVisible) {
            VisualTransformation.None
        } else {
            PasswordVisualTransformation()
        },
        keyboardOptions = KeyboardOptions(
            keyboardType = KeyboardType.NumberPassword,
            imeAction = ImeAction.Done,
        ),
        keyboardActions = KeyboardActions(
            onDone = {
                focusManager.clearFocus()
                onDone?.invoke()
            },
        ),
        singleLine = true,
        enabled = enabled,
        isError = isError,
        colors = OutlinedTextFieldDefaults.colors(
            focusedBorderColor = focusedBorderColor,
            unfocusedBorderColor = borderColor,
            errorBorderColor = errorBorderColor,
            disabledBorderColor = borderColor.copy(alpha = 0.5f),
            focusedLabelColor = focusedBorderColor,
            unfocusedLabelColor = hintColor,
            errorLabelColor = errorBorderColor,
            focusedTextColor = textColor,
            unfocusedTextColor = textColor,
            disabledTextColor = textColor.copy(alpha = 0.5f),
            cursorColor = textColor,
            focusedContainerColor = backgroundColor,
            unfocusedContainerColor = backgroundColor,
            errorContainerColor = backgroundColor,
            disabledContainerColor = backgroundColor,
        ),
        textStyle = TextStyle(
            fontSize = textSize,
            color = textColor,
        ),
        shape = RoundedCornerShape(cornerRadius),
    )
}

@Preview(showBackground = true)
@Composable
private fun PinPasswordInputPreview() {
    AppTheme {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            // PIN mode preview
            var pinValue by remember { mutableStateOf("") }
            PinPasswordInput(
                value = pinValue,
                onValueChange = { pinValue = it },
                inputMode = InputMode.PIN,
                label = "Enter PIN",
                pinLength = 6,
            )

            // PIN mode with error
            PinPasswordInput(
                value = "123",
                onValueChange = { },
                inputMode = InputMode.PIN,
                label = "PIN with Error",
                isError = true,
                errorMessage = "Invalid PIN",
                pinLength = 4,
            )

            // Password mode preview
            var passwordValue by remember { mutableStateOf("") }
            PinPasswordInput(
                value = passwordValue,
                onValueChange = { passwordValue = it },
                inputMode = InputMode.PASSWORD,
                label = "Enter Password",
                hint = "Password",
            )

            // Password mode with value
            PinPasswordInput(
                value = "mypassword",
                onValueChange = { },
                inputMode = InputMode.PASSWORD,
                label = "Password with Error",
                isError = true,
                errorMessage = "Password must be at least 8 characters",
            )
        }
    }
}