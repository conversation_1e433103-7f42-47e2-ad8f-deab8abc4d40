package com.vietinbank.core_ui.base.dialog

import android.app.AlertDialog
import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.Window
import androidx.fragment.app.DialogFragment
import com.vietinbank.core_ui.databinding.DialogAssessmentSuccessBinding

class AssessmentSuccessDialog : DialogFragment() {

    companion object {
        fun newInstance(): AssessmentSuccessDialog {
            val frag = AssessmentSuccessDialog()
            return frag
        }
    }

    private var _binding: DialogAssessmentSuccessBinding? = null
    private val binding get() = _binding!!

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        // inflate binding
        _binding = DialogAssessmentSuccessBinding.inflate(LayoutInflater.from(context))
        val view = binding.root
        val dialog =
            AlertDialog.Builder(requireContext()).setView(view).setCancelable(true).create()

        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)

        return dialog
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
