package com.vietinbank.core_ui.base

import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.session.InactivityManager
import com.vietinbank.core_ui.base.dialog.LoadingDialog
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

/**
 * Created by vandz on 18/12/24.
 */

@AndroidEntryPoint
open class BaseActivity : AppCompatActivity() {

    @Inject
    lateinit var configManager: IAppConfigManager

    @Inject
    lateinit var inactivityManager: InactivityManager
    private var loadingDialog: LoadingDialog? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setupEdgeToEdge()
    }

    /**
     * Override dispatchTouchEvent để ẩn bàn phím khi người dùng nhấn ra ngoài EditText
     * Phương pháp này sẽ áp dụng cho tất cả Activity kế thừa từ BaseActivity
     */
    override fun dispatchTouchEvent(event: MotionEvent): Boolean {
        if (event.action == MotionEvent.ACTION_DOWN) {
            // Notify inactivity manager for any touch in the activity window
            try {
                inactivityManager.onUserInteraction()
            } catch (ignored: Exception) {
                // Safe-guard: inactivity manager might not be ready in rare cases
            }
            val view = currentFocus
            if (view != null) {
                // Kiểm tra nếu người dùng nhấn ra ngoài EditText
                if (shouldHideKeyboard(view, event)) {
                    hideKeyboard(view)
                    // Thêm log để debug
                    printLog("BaseActivity Hiding keyboard from dispatchTouchEvent")
                }
            }
        }
        return super.dispatchTouchEvent(event)
    }

    /**
     * Kiểm tra xem người dùng có nhấn ra ngoài EditText hay không
     */
    private fun shouldHideKeyboard(view: View, event: MotionEvent): Boolean {
        // Sửa lại cách tính toán vị trí
        val screenLocation = IntArray(2)
        view.getLocationOnScreen(screenLocation)

        val x = event.rawX
        val y = event.rawY

        // Log để debug
        Log.d("BaseActivity", "Touch at: x=$x, y=$y")
        Log.d(
            "BaseActivity",
            "View bounds: left=${screenLocation[0]}, top=${screenLocation[1]}, right=${screenLocation[0] + view.width}, bottom=${screenLocation[1] + view.height}",
        )

        // Kiểm tra xem vị trí nhấn có nằm ngoài view hay không
        return !(
            x >= screenLocation[0] &&
                x <= screenLocation[0] + view.width &&
                y >= screenLocation[1] &&
                y <= screenLocation[1] + view.height
            )
    }

    /**
     * Ẩn bàn phím từ view đang focus
     */
    private fun hideKeyboard(view: View) {
        val imm = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(view.windowToken, 0)
        view.clearFocus()
    }

    /**
     * Hàm public để các Fragment có thể gọi khi cần ẩn bàn phím
     */
    fun hideKeyboard() {
        val view = currentFocus ?: return
        hideKeyboard(view)
    }

    /**
     * Hàm để xử lý trạng thái loading.
     * @param isLoading Trạng thái loading hiện tại.
     */
    private fun handleLoading(isLoading: Boolean) {
        if (isLoading) {
            showLoadingDialog()
        } else {
            hideLoadingDialog()
        }
    }

    /**
     * Hàm để hiển thị loading dialog.
     */
    private fun showLoadingDialog() {
        if (loadingDialog == null) {
            loadingDialog = LoadingDialog.newInstance()
            loadingDialog?.show(supportFragmentManager, "LoadingDialog")
        }
    }

    /**
     * Hàm để ẩn loading dialog.
     */
    private fun hideLoadingDialog() {
        loadingDialog?.dismiss()
        loadingDialog = null
    }

    /**
     * Hàm để hiển thị Toast.
     * @param message Thông điệp cần hiển thị.
     */
    protected fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    /**
     * Setup edge-to-edge display using modern WindowInsets API
     * This replaces the deprecated SYSTEM_UI_FLAG approach
     */
    private fun setupEdgeToEdge() {
        try {
            // Enable edge-to-edge display
            WindowCompat.setDecorFitsSystemWindows(window, false)

            // Hide action bar if present
            actionBar?.hide()
            supportActionBar?.hide()

            // Setup window insets controller for status bar appearance
            val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)

            // Set light status bar icons (dark icons on light background)
            // Individual screens can override this as needed
            windowInsetsController?.isAppearanceLightStatusBars = true

            // Make status bar transparent
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                window.statusBarColor = android.graphics.Color.TRANSPARENT
            }

            // Make navigation bar transparent (optional, can be configured per screen)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                window.navigationBarColor = android.graphics.Color.TRANSPARENT
                windowInsetsController?.isAppearanceLightNavigationBars = true
            }

            printLog("BaseActivity: Edge-to-edge enabled with modern WindowInsets API")
        } catch (e: Exception) {
            Log.e("BaseActivity", "Error setting up edge-to-edge", e)
        }
    }

    /**
     * Helper method to control status bar appearance
     * Can be called by child activities/fragments to customize per screen
     */
    protected fun setStatusBarAppearance(isLight: Boolean) {
        val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)
        windowInsetsController?.isAppearanceLightStatusBars = isLight
    }

    /**
     * Helper method to show/hide system bars
     * Useful for immersive experiences like video players
     */
    protected fun setSystemBarsVisibility(show: Boolean) {
        val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)
        if (show) {
            windowInsetsController?.show(WindowInsetsCompat.Type.systemBars())
        } else {
            windowInsetsController?.hide(WindowInsetsCompat.Type.systemBars())
            windowInsetsController?.systemBarsBehavior =
                WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        }
    }
}
