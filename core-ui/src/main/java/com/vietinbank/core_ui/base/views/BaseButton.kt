package com.vietinbank.core_ui.base.views

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Gravity
import androidx.appcompat.widget.AppCompatButton
import androidx.core.content.ContextCompat
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_ui.R

class BaseButton : AppCompatButton {
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(attrs)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr,
    ) {
        init(attrs)
    }

    private fun init(attrs: AttributeSet?) {
//        transformationMethod = null;
        isEnabled = true
        typeface = Utils.g().getFont(5, context) // REGULAR_BOLD
        gravity = Gravity.CENTER
        setTextSize(TypedValue.COMPLEX_UNIT_PX, resources.getDimension(R.dimen.sp14))
        setTextColor(Color.WHITE)
    }

    override fun setEnabled(enabled: Boolean) {
        super.setEnabled(enabled)
        background = if (enabled) {
            ContextCompat.getDrawable(context, R.drawable.bg_button_enable)
        } else {
            ContextCompat.getDrawable(context, R.drawable.bg_button_disable)
        }

        setTextColor(
            if (enabled) {
                Color.WHITE
            } else {
                ContextCompat.getColor(context, R.color.text_primary_3)
            },
        )
    }
}