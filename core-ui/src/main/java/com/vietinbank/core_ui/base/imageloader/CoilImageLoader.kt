package com.vietinbank.core_ui.base.imageloader

import android.graphics.Bitmap
import android.net.Uri
import android.widget.ImageView
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import coil3.ImageLoader
import coil3.compose.AsyncImage
import coil3.load
import coil3.request.CachePolicy
import coil3.request.ImageRequest
import coil3.request.error
import coil3.request.placeholder
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Created by vandz on 10/4/25.
 */
@Singleton
class CoilImageLoader @Inject constructor(
    private val coilImageLoader: ImageLoader,
) : IImageLoader {
    override fun loadUrl(
        imageView: ImageView,
        url: String?,
        isCache: Boolean,
        placeholderRes: Int?,
        errorRes: Int?,
    ) {
        imageView.load(url, coilImageLoader) {
            if (!isCache) {
                memoryCachePolicy(CachePolicy.DISABLED)
                diskCachePolicy(CachePolicy.DISABLED)
            }
            placeholderRes?.let { placeholder(it) }
            errorRes?.let { error(it) }
        }
    }

    override fun loadBitmap(
        imageView: ImageView,
        bitmap: Bitmap?,
        isCache: Boolean,
        placeholderRes: Int?,
        errorRes: Int?,
    ) {
        imageView.load(bitmap, coilImageLoader) {
            if (!isCache) {
                memoryCachePolicy(CachePolicy.DISABLED)
                diskCachePolicy(CachePolicy.DISABLED)
            }
            placeholderRes?.let { placeholder(it) }
            errorRes?.let { error(it) }
        }
    }

    override fun loadUri(
        imageView: ImageView,
        uri: Uri?,
        isCache: Boolean,
        placeholderRes: Int?,
        errorRes: Int?,
    ) {
        imageView.load(uri, coilImageLoader) {
            if (!isCache) {
                memoryCachePolicy(CachePolicy.DISABLED)
                diskCachePolicy(CachePolicy.DISABLED)
            }
            placeholderRes?.let { placeholder(it) }
            errorRes?.let { error(it) }
        }
    }

    override fun loadDrawable(
        imageView: ImageView,
        drawableRes: Int,
        isCache: Boolean,
        placeholderRes: Int?,
        errorRes: Int?,
    ) {
        imageView.load(drawableRes, coilImageLoader) {
            if (!isCache) {
                memoryCachePolicy(CachePolicy.DISABLED)
                diskCachePolicy(CachePolicy.DISABLED)
            }
            placeholderRes?.let { placeholder(it) }
            errorRes?.let { error(it) }
        }
    }

    @Composable
    override fun LoadUrl(
        url: String?,
        isCache: Boolean,
        placeholderRes: Int?,
        errorRes: Int?,
        modifier: Modifier,
    ) {
        val requestBuilder = ImageRequest.Builder(LocalContext.current)
            .data(url)

        if (!isCache) {
            requestBuilder.memoryCachePolicy(CachePolicy.DISABLED)
            requestBuilder.diskCachePolicy(CachePolicy.DISABLED)
        }
        placeholderRes?.let { requestBuilder.placeholder(it) }
        errorRes?.let { requestBuilder.error(it) }

        val request = requestBuilder.build()

        AsyncImage(
            model = request,
            contentDescription = null, // optional
            modifier = modifier,
            imageLoader = coilImageLoader, // Dùng loader do DI cung cấp
        )
    }

    @Composable
    override fun LoadByteArray(
        modifier: Modifier,
        byteArray: ByteArray?,
        isCache: Boolean,
        placeholderRes: Int?,
        errorRes: Int?,
    ) {
        val context = LocalContext.current

        AsyncImage(
            model = ImageRequest.Builder(context)
                .data(byteArray)
                .memoryCachePolicy(if (isCache) CachePolicy.ENABLED else CachePolicy.DISABLED)
                .placeholder(placeholderRes ?: 0)
                .error(errorRes ?: 0)
                .build(),
            contentDescription = null,
            modifier = modifier,
            imageLoader = coilImageLoader,
        )
    }
}