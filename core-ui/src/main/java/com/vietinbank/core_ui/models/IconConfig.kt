package com.vietinbank.core_ui.models

import androidx.annotation.DrawableRes
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * Configuration for icons used in Foundation text components
 *
 * @param icon Drawable resource ID for the icon
 * @param tint Color to apply to the icon (default: Unspecified - uses icon's original color)
 * @param size Size of the icon (default: 24dp)
 * @param contentDescription Accessibility content description for the icon
 */
data class IconConfig(
    @DrawableRes val icon: Int,
    val tint: Color = Color.Unspecified,
    val size: Dp = 24.dp,
    val contentDescription: String? = null,
)