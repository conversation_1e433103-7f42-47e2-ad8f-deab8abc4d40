package com.vietinbank.core_ui.state

import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import com.vietinbank.core_ui.components.ButtonSize
import com.vietinbank.core_ui.components.TabType

/**
 * Base interface for component states
 */
interface ComponentState

/**
 * Button state management
 */
@Immutable
data class ButtonState(
    val isEnabled: Boolean = true,
    val isPressed: Boolean = false,
    val size: ButtonSize = ButtonSize.Large,
) : ComponentState

/**
 * Tab state management
 */
@Immutable
data class TabState(
    val selectedIndex: Int = 0,
    val tabs: List<String> = emptyList(),
    val type: TabType = TabType.Pill,
    val isEnabled: Boolean = true,
) : ComponentState

/**
 * Composable state holders
 */
@Composable
fun rememberButtonState(
    isEnabled: Boolean = true,
    isPressed: Boolean = false,
    size: ButtonSize = ButtonSize.Large,
): State<ButtonState> {
    return remember(isEnabled, isPressed, size) {
        mutableStateOf(
            ButtonState(
                isEnabled = isEnabled,
                isPressed = isPressed,
                size = size,
            ),
        )
    }
}

@Composable
fun rememberTabState(
    selectedIndex: Int = 0,
    tabs: List<String> = emptyList(),
    type: TabType = TabType.Pill,
    isEnabled: Boolean = true,
): State<TabState> {
    return remember(selectedIndex, tabs, type, isEnabled) {
        mutableStateOf(
            TabState(
                selectedIndex = selectedIndex,
                tabs = tabs,
                type = type,
                isEnabled = isEnabled,
            ),
        )
    }
}

/**
 * State update helpers
 */
fun ButtonState.withEnabled(enabled: Boolean): ButtonState = copy(isEnabled = enabled)
fun ButtonState.withPressed(pressed: Boolean): ButtonState = copy(isPressed = pressed)

fun TabState.withSelectedIndex(index: Int): TabState = copy(selectedIndex = index)
fun TabState.withTabs(tabs: List<String>): TabState = copy(tabs = tabs)