package com.vietinbank.core_ui.theme

import androidx.compose.runtime.Composable
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * AppColors - Backward compatibility wrapper for FoundationDesignSystem.Colors
 *
 * For new code, use FoundationDesignSystem.Colors directly
 * This object only exists to support legacy code
 */
object AppColors {
    // ========================================
    // SEMANTIC COLORS
    // ========================================

    /* Brand colors */
    val primary @Composable get() = FDS.Colors.primary
    val secondary @Composable get() = FDS.Colors.secondary
    val error @Composable get() = FDS.Colors.error
    val success @Composable get() = FDS.Colors.success
    val warning @Composable get() = FDS.Colors.warning
    val info @Composable get() = FDS.Colors.info

    /* Surface & Background */
    val background @Composable get() = FDS.Colors.background
    val surface @Composable get() = FDS.Colors.surface
    val glassOverlay @Composable get() = FDS.Colors.glassOverlay
    val glassBorder @Composable get() = FDS.Colors.glassBorder

    /* Text colors */
    val textPrimary @Composable get() = FDS.Colors.textPrimary
    val textSecondary @Composable get() = FDS.Colors.textSecondary
    val textTertiary @Composable get() = FDS.Colors.textTertiary
    val textDisabled @Composable get() = FDS.Colors.textDisabled
    val textLink @Composable get() = FDS.Colors.textLink
    val textOnPrimary @Composable get() = FDS.Colors.textOnPrimary

    /* Border & Divider */
    val outline @Composable get() = FDS.Colors.outline
    val divider @Composable get() = FDS.Colors.divider

    // ========================================
    // PALETTE COLORS
    // ========================================

    /* Blue palette */
    val blue50 @Composable get() = FDS.Colors.blue50
    val blue100 @Composable get() = FDS.Colors.blue100
    val blue200 @Composable get() = FDS.Colors.blue200
    val blue300 @Composable get() = FDS.Colors.blue300
    val blue400 @Composable get() = FDS.Colors.blue400
    val blue500 @Composable get() = FDS.Colors.blue500
    val blue600 @Composable get() = FDS.Colors.blue600
    val blue700 @Composable get() = FDS.Colors.blue700
    val blue800 @Composable get() = FDS.Colors.blue800
    val blue900 @Composable get() = FDS.Colors.blue900

    /* Red palette */
    val red300 @Composable get() = FDS.Colors.red300
    val red500 @Composable get() = FDS.Colors.red500

    /* Gray palette */
    val gray100 @Composable get() = FDS.Colors.gray100
    val gray200 @Composable get() = FDS.Colors.gray200
    val gray600 @Composable get() = FDS.Colors.gray600
    val gray700 @Composable get() = FDS.Colors.gray700
    val gray800 @Composable get() = FDS.Colors.gray800
    val white @Composable get() = FDS.Colors.white

    // ========================================
    // COMPONENT SPECIFIC COLORS
    // ========================================

    /* Shadow colors */
    val shadowSm @Composable get() = FDS.Colors.shadowSm
    val shadowMd @Composable get() = FDS.Colors.shadowMd
    val shadowLg @Composable get() = FDS.Colors.shadowLg
    val shadowOuter @Composable get() = FDS.Colors.shadowOuter
    val shadowInnerWhite @Composable get() = FDS.Colors.shadowInnerWhite
    val shadowInnerWhiteSoft @Composable get() = FDS.Colors.shadowInnerWhiteSoft

    /* Button gradient colors */
    val buttonGradientPrimary @Composable get() = FDS.Colors.buttonGradientPrimary
    val buttonGradientPrimaryTransparent @Composable get() = FDS.Colors.buttonGradientPrimaryTransparent
    val buttonGradientPrimaryPressed @Composable get() = FDS.Colors.buttonGradientPrimaryPressed
    val buttonGradientSecondary @Composable get() = FDS.Colors.buttonGradientSecondary
    val buttonGradientSecondaryTransparent @Composable get() = FDS.Colors.buttonGradientSecondaryTransparent

    /* Tab Component Colors */
    val tabTextActiveInline @Composable get() = FDS.Colors.tabTextActiveInline
    val tabTextActivePill @Composable get() = FDS.Colors.tabTextActivePill
    val tabTextInactive @Composable get() = FDS.Colors.tabTextInactive
    val tabBorderActive @Composable get() = FDS.Colors.tabBorderActive
    val tabDividerInactive @Composable get() = FDS.Colors.tabDividerInactive
    val tabBgActivePill @Composable get() = FDS.Colors.tabBgActivePill
    val tabBgContainerInline @Composable get() = FDS.Colors.tabBgContainerInline
    val tabGradient1 @Composable get() = FDS.Colors.tabGradient1
    val tabGradient2 @Composable get() = FDS.Colors.tabGradient2
    val tabGradient3 @Composable get() = FDS.Colors.tabGradient3
    val tabGradient4 @Composable get() = FDS.Colors.tabGradient4
    val tabGradient5 @Composable get() = FDS.Colors.tabGradient5

    /* Glass Morphism Colors */
    val glassGradient1 @Composable get() = FDS.Colors.glassGradient1
    val glassGradient2 @Composable get() = FDS.Colors.glassGradient2
    val glassGradient3 @Composable get() = FDS.Colors.glassGradient3
    val glassGradient4 @Composable get() = FDS.Colors.glassGradient4
    val glassGradient5 @Composable get() = FDS.Colors.glassGradient5

    // ========================================
    // FUNCTIONAL TOKENS
    // ========================================

    /* Character Tokens */
    val characterHighlighted @Composable get() = FDS.Colors.characterHighlighted
    val characterHighlightedLighter @Composable get() = FDS.Colors.characterHighlightedLighter
    val characterPrimary @Composable get() = FDS.Colors.characterPrimary
    val characterSecondary @Composable get() = FDS.Colors.characterSecondary
    val characterTertiary @Composable get() = FDS.Colors.characterTertiary
    val characterInverse @Composable get() = FDS.Colors.characterInverse

    /* Background Tokens */
    val backgroundBgContainer @Composable get() = FDS.Colors.backgroundBgContainer
    val backgroundBgScreen @Composable get() = FDS.Colors.backgroundBgScreen
    val backgroundBgOnColor @Composable get() = FDS.Colors.backgroundBgOnColor

    /* Stroke Tokens */
    val strokeDivider @Composable get() = FDS.Colors.strokeDivider

    /* State Tokens */
    val stateError @Composable get() = FDS.Colors.stateError
    val stateWarning @Composable get() = FDS.Colors.stateWarning
    val stateSuccess @Composable get() = FDS.Colors.stateSuccess
    val stateActive @Composable get() = FDS.Colors.stateActive
    val stateErrorLighter @Composable get() = FDS.Colors.stateErrorLighter
    val stateWarningLighter @Composable get() = FDS.Colors.stateWarningLighter
    val stateSuccessLighter @Composable get() = FDS.Colors.stateSuccessLighter
    val stateActiveLighter @Composable get() = FDS.Colors.stateActiveLighter
    val stateBadgeCounting @Composable get() = FDS.Colors.stateBadgeCounting

    // ========================================
    // LEGACY COLORS - For backward compatibility only
    // DO NOT use for new features!
    // ========================================

    // Keep only the most essential legacy colors that are still in use
    val colorPrimary @Composable get() = FDS.Colors.colorResource(com.vietinbank.core_ui.R.color.colorPrimary)
    val colorAccent @Composable get() = FDS.Colors.colorResource(com.vietinbank.core_ui.R.color.colorAccent)
    val noticeDialogBackground @Composable get() = FDS.Colors.colorResource(com.vietinbank.core_ui.R.color.overlay_background)

    // Common legacy mappings - DO NOT use for new code!
    val borderColor @Composable get() = outline
    val itemBackground @Composable get() = glassOverlay
    val inputBackground @Composable get() = glassBorder
    val cardBackground @Composable get() = glassOverlay
    val redButton @Composable get() = secondary

    // Additional legacy colors still in use
    val linearButtonStart @Composable get() = blue300
    val linearButtonEnd @Composable get() = blue200
    val gradientButtonStart @Composable get() = blue300
    val gradientButtonEnd @Composable get() = blue600
    val switchCheckedTrack @Composable get() = blue700
    val switchUncheckedThumb @Composable get() = gray600
    val switchUncheckedTrack @Composable get() = gray100
    val switchUncheckedBorder @Composable get() = gray600
    val grey08 @Composable get() = gray700
    val blue07 @Composable get() = blue600
    val grey07 @Composable get() = gray600
    val blue02 @Composable get() = blue800
    val descriptionGrey @Composable get() = textSecondary
    val blue09 @Composable get() = blue100
    val contentPrimary @Composable get() = textPrimary
    val grey200 @Composable get() = gray200
    val contentSecondary @Composable get() = textSecondary
    val textHint @Composable get() = textTertiary
    val textWarningYellow @Composable get() = warning
    val successGreen @Composable get() = success
    val blue04 @Composable get() = blue400
    val trenBgr @Composable get() = blue50
    val grey09 @Composable get() = gray800
    val define01 @Composable get() = gray100
    val greyMist @Composable get() = gray200
    val primaryRed @Composable get() = secondary
    val surfaceNeutralWeak @Composable get() = gray100
    val primaryBlueVietinLight @Composable get() = blue200
    val backgroundMenuVert @Composable get() = glassOverlay
    val lineColor @Composable get() = divider
    val primary1 @Composable get() = blue50
    val primary2 @Composable get() = blue100
    val primary3 @Composable get() = blue200
    val primary4 @Composable get() = blue300
    val primary5 @Composable get() = blue400
    val primary6 @Composable get() = blue500
    val primary8 @Composable get() = blue700
    val primary10 @Composable get() = blue900
    val primaryDark @Composable get() = blue700
    val placeholder @Composable get() = textTertiary
    val blue01 @Composable get() = blue900
    val blue08 @Composable get() = blue300
    val textSecondary6 @Composable get() = secondary
    val textInformativeMedium @Composable get() = info
    val linkTextColor @Composable get() = textLink
    val buttonTextColor @Composable get() = textOnPrimary
    val backgroundGradientStart @Composable get() = blue800
    val backgroundGradientEnd @Composable get() = blue900
    val redButtonStart @Composable get() = red300
    val redButtonEnd @Composable get() = red500
    val gradientStart @Composable get() = blue300
    val gradientEnd @Composable get() = blue500
    val overlayBackground @Composable get() = glassOverlay
}