package com.vietinbank.core_ui.theme

import androidx.compose.runtime.staticCompositionLocalOf
import com.vietinbank.core_common.config.IAppConfigManager

/**
 * CompositionLocal for accessing IAppConfigManager in Compose UI.
 * This allows dialogs and other Compose components to access app configuration
 * without direct dependency injection.
 *
 * The value is provided by the app module which has access to Hilt dependencies.
 * Core-ui module remains DI-agnostic.
 */
val LocalAppConfigManager = staticCompositionLocalOf<IAppConfigManager?> { null }