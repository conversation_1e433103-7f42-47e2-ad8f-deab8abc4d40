package com.vietinbank.core_ui.theme

import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import com.vietinbank.core_ui.R

/**
 * SVN-<PERSON><PERSON>ont Family from Foundation Design System
 */
val SvnGilroyFamily = FontFamily(
    Font(R.font.regular, FontWeight.Normal),
    <PERSON><PERSON>(R.font.medium, FontWeight.Medium),
    <PERSON><PERSON>(R.font.semibold, FontWeight.SemiBold),
    <PERSON><PERSON>(R.font.bold, FontWeight.Bold),
    <PERSON><PERSON>(R.font.xbold, FontWeight.ExtraBold),
    <PERSON><PERSON>(R.font.heavy, FontWeight.Black),
)