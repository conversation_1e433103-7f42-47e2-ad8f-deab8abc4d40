package com.vietinbank.core_ui.theme

import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * AppEffects - Backward compatibility wrapper for FoundationDesignSystem.Effects
 *
 * For new code, use FoundationDesignSystem.Effects directly
 * This object only exists to support legacy code
 *
 * Foundation Design System - Effects
 * Elevation values for Material Design shadows
 */
object AppEffects {
    // Elevation levels from Foundation Design
    val elevationSm get() = FDS.Effects.elevationSm
    val elevationMd get() = FDS.Effects.elevationMd
    val elevationLg get() = FDS.Effects.elevationLg
    val elevationButton get() = FDS.Effects.elevationButton

    // Additional standard elevations
    val elevationNone get() = FDS.Effects.elevationNone
    val elevationXs get() = FDS.Effects.elevationXs
    val elevationXl get() = FDS.Effects.elevationXl
    val elevationXxl get() = FDS.Effects.elevationXxl
}