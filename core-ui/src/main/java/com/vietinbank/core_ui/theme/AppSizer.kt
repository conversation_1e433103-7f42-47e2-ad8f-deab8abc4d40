package com.vietinbank.core_ui.theme

import androidx.annotation.DimenRes
import androidx.compose.runtime.Composable
import androidx.compose.ui.unit.Dp
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * AppSizer - Backward compatibility wrapper for FoundationDesignSystem.Sizer
 *
 * For new code, use FoundationDesignSystem.Sizer directly
 * This object only exists to support legacy code
 *
 * Direct mapping với Figma Design System
 * Usage: AppSizer.[Category].[value]
 *
 * Example:
 * - Figma: "Sizer/Padding/padding-16" → Code: AppSizer.Padding.padding16
 * - Figma: "Sizer/Gap/8" → Code: AppSizer.Gap.gap8
 */
object AppSizer {
    /**
     * Padding values - Direct mapping with Figma Sizer/Padding
     */
    object Padding {
        val padding0 @Composable get() = FDS.Sizer.Padding.padding0
        val padding4 @Composable get() = FDS.Sizer.Padding.padding4
        val padding8 @Composable get() = FDS.Sizer.Padding.padding8
        val padding12 @Composable get() = FDS.Sizer.Padding.padding12
        val padding16 @Composable get() = FDS.Sizer.Padding.padding16
        val padding20 @Composable get() = FDS.Sizer.Padding.padding20
        val padding24 @Composable get() = FDS.Sizer.Padding.padding24
        val padding32 @Composable get() = FDS.Sizer.Padding.padding32
        val padding40 @Composable get() = FDS.Sizer.Padding.padding40
        val padding48 @Composable get() = FDS.Sizer.Padding.padding48
        val padding56 @Composable get() = FDS.Sizer.Padding.padding56
        val padding64 @Composable get() = FDS.Sizer.Padding.padding64
        val padding280 @Composable get() = FDS.Sizer.Padding.padding280
    }

    /**
     * Gap values - Direct mapping with Figma Sizer/Gap
     * Used for spacing between elements (Row/Column arrangements)
     */
    object Gap {
        val gap0 @Composable get() = FDS.Sizer.Gap.gap0
        val gap4 @Composable get() = FDS.Sizer.Gap.gap4
        val gap8 @Composable get() = FDS.Sizer.Gap.gap8
        val gap12 @Composable get() = FDS.Sizer.Gap.gap12
        val gap16 @Composable get() = FDS.Sizer.Gap.gap16
        val gap20 @Composable get() = FDS.Sizer.Gap.gap20
        val gap24 @Composable get() = FDS.Sizer.Gap.gap24
        val gap32 @Composable get() = FDS.Sizer.Gap.gap32
        val gap40 @Composable get() = FDS.Sizer.Gap.gap40
        val gap48 @Composable get() = FDS.Sizer.Gap.gap48
        val gap56 @Composable get() = FDS.Sizer.Gap.gap56
        val gap64 @Composable get() = FDS.Sizer.Gap.gap64
    }

    /**
     * Icon sizes - Direct mapping with Figma Sizer/Icon
     */
    object Icon {
        val icon16 @Composable get() = FDS.Sizer.Icon.icon16
        val icon20 @Composable get() = FDS.Sizer.Icon.icon20
        val icon24 @Composable get() = FDS.Sizer.Icon.icon24
        val icon32 @Composable get() = FDS.Sizer.Icon.icon32
        val icon40 @Composable get() = FDS.Sizer.Icon.icon40
        val icon48 @Composable get() = FDS.Sizer.Icon.icon48
        val icon56 @Composable get() = FDS.Sizer.Icon.icon56
    }

    /**
     * Border radius values - Direct mapping with Figma Sizer/Radius
     */
    object Radius {
        val radius0 @Composable get() = FDS.Sizer.Radius.radius0
        val radius4 @Composable get() = FDS.Sizer.Radius.radius4
        val radius8 @Composable get() = FDS.Sizer.Radius.radius8
        val radius10 @Composable get() = FDS.Sizer.Radius.radius10
        val radius16 @Composable get() = FDS.Sizer.Radius.radius16
        val radius20 @Composable get() = FDS.Sizer.Radius.radius20
        val radius32 @Composable get() = FDS.Sizer.Radius.radius32
        val radiusFull @Composable get() = FDS.Sizer.Radius.radiusFull
    }

    /**
     * Stroke/Border width values - Direct mapping with Figma Sizer/Stroke
     */
    object Stroke {
        val stroke05 @Composable get() = FDS.Sizer.Stroke.stroke05
        val stroke1 @Composable get() = FDS.Sizer.Stroke.stroke1
        val stroke2 @Composable get() = FDS.Sizer.Stroke.stroke2
        val stroke4 @Composable get() = FDS.Sizer.Stroke.stroke4
    }

    /**
     * Text size values - Direct mapping with Figma typography sizes
     * Note: These are for quick access. For full typography, use AppTypography
     */
    object Text {
        val text12 @Composable get() = FDS.Sizer.Text.text12
        val text14 @Composable get() = FDS.Sizer.Text.text14
        val text16 @Composable get() = FDS.Sizer.Text.text16
        val text18 @Composable get() = FDS.Sizer.Text.text18
        val text20 @Composable get() = FDS.Sizer.Text.text20
        val text24 @Composable get() = FDS.Sizer.Text.text24
        val text30 @Composable get() = FDS.Sizer.Text.text30
        val text36 @Composable get() = FDS.Sizer.Text.text36
    }

    /**
     * Legacy spacing values - Use Gap for new code
     * Kept for backward compatibility during migration
     */
    object Spacing {
        val spacingMini @Composable get() = FDS.Sizer.Spacing.spacingMini
        val spacingSmall @Composable get() = FDS.Sizer.Spacing.spacingSmall
        val spacingMedium @Composable get() = FDS.Sizer.Spacing.spacingMedium
        val spacingLarge @Composable get() = FDS.Sizer.Spacing.spacingLarge
        val spacingXLarge @Composable get() = FDS.Sizer.Spacing.spacingXLarge
    }
}

/**
 * Helper function to read dimension resources
 */
@Composable
fun dimensionResource(@DimenRes id: Int): Dp = FDS.Sizer.dimensionResource(id)