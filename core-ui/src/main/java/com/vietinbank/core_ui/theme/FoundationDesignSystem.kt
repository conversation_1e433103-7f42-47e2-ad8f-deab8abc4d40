package com.vietinbank.core_ui.theme

import androidx.annotation.ColorRes
import androidx.annotation.DimenRes
import androidx.compose.material3.Typography
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ui.R

/**
 * Foundation Design System
 *
 * Usage:
 * - Import: `import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS`
 * - Access: `FDS.Colors.primary`, `FDS.Typography.headingH1`, `FDS.Sizer.padding16`
 *
 * Structure follows Figma design system organization:
 * - Colors: Semantic colors, palettes, component-specific colors
 * - Typography: Text styles with functional tokens
 * - Sizer: Dimensions (padding, gap, radius, stroke, etc.)
 * - Effects: Elevations and shadows
 */
object FoundationDesignSystem {

    // ========================================
    // COLORS
    // ========================================
    object Colors {
        // Helper function
        @Composable
        fun colorResource(@ColorRes id: Int): Color {
            return Color(LocalContext.current.getColor(id))
        }

        // ====== SEMANTIC COLORS ======
        /* Brand colors */
        val primary @Composable get() = colorResource(R.color.foundation_primary)
        val secondary @Composable get() = colorResource(R.color.foundation_secondary)
        val error @Composable get() = colorResource(R.color.foundation_error)
        val success @Composable get() = colorResource(R.color.foundation_success)
        val warning @Composable get() = colorResource(R.color.foundation_warning)
        val info @Composable get() = colorResource(R.color.foundation_info)

        /* Surface & Background */
        val background @Composable get() = colorResource(R.color.foundation_background)
        val surface @Composable get() = colorResource(R.color.foundation_surface)
        val glassOverlay @Composable get() = colorResource(R.color.foundation_glass_overlay)
        val glassBorder @Composable get() = colorResource(R.color.foundation_glass_border)

        /* Text colors */
        val textPrimary @Composable get() = colorResource(R.color.foundation_text_primary)
        val textSecondary @Composable get() = colorResource(R.color.foundation_text_secondary)
        val textTertiary @Composable get() = colorResource(R.color.foundation_text_tertiary)
        val textDisabled @Composable get() = colorResource(R.color.foundation_text_disabled)
        val textLink @Composable get() = colorResource(R.color.foundation_text_link)
        val textOnPrimary @Composable get() = colorResource(R.color.foundation_on_primary)
        val textSelected @Composable get() = colorResource(R.color.text_selected)

        /* Border & Divider */
        val outline @Composable get() = colorResource(R.color.foundation_outline)
        val divider @Composable get() = colorResource(R.color.foundation_divider)

        // ====== PALETTE COLORS ======
        /* Blue palette (Primary) */
        val blue50 @Composable get() = colorResource(R.color.foundation_primary_blue50)
        val blue100 @Composable get() = colorResource(R.color.foundation_primary_blue100)
        val blue200 @Composable get() = colorResource(R.color.foundation_primary_blue200)
        val blue300 @Composable get() = colorResource(R.color.foundation_primary_blue300)
        val blue400 @Composable get() = colorResource(R.color.foundation_primary_blue400)
        val blue500 @Composable get() = colorResource(R.color.foundation_primary_blue500)
        val blue600 @Composable get() = colorResource(R.color.foundation_primary_blue600)
        val blue700 @Composable get() = colorResource(R.color.foundation_primary_blue700)
        val blue800 @Composable get() = colorResource(R.color.foundation_primary_blue800)
        val blue900 @Composable get() = colorResource(R.color.foundation_primary_blue900)

        /* Red palette (Secondary) */
        val red50 @Composable get() = colorResource(R.color.foundation_secondary_red50)
        val red300 @Composable get() = colorResource(R.color.foundation_secondary_red300)
        val red400 @Composable get() = colorResource(R.color.foundation_secondary_red400)
        val red500 @Composable get() = colorResource(R.color.foundation_secondary_red500)
        val red600 @Composable get() = colorResource(R.color.foundation_secondary_red600)

        /* Gray palette (Neutrals) */
        val gray50 @Composable get() = colorResource(R.color.foundation_neutrals_gray50)
        val gray100 @Composable get() = colorResource(R.color.foundation_neutrals_gray100)
        val gray200 @Composable get() = colorResource(R.color.foundation_neutrals_gray200)
        val gray500 @Composable get() = colorResource(R.color.foundation_neutrals_gray500)
        val gray600 @Composable get() = colorResource(R.color.foundation_neutrals_gray600)
        val gray700 @Composable get() = colorResource(R.color.foundation_neutrals_gray700)
        val gray800 @Composable get() = colorResource(R.color.foundation_neutrals_gray800)
        val gray900 @Composable get() = colorResource(R.color.foundation_neutrals_gray900)
        val white @Composable get() = colorResource(R.color.foundation_neutrals_white)
        val black @Composable get() = colorResource(R.color.black)
        val transparent @Composable get() = colorResource(R.color.transparent)

        /* Palette Neutral 100 from Figma */
        val paletteNeutral100 @Composable get() = colorResource(R.color.foundation_palette_neutral_100)

        /* green palatte */
        val green100 @Composable get() = colorResource(R.color.foundation_success_green100)
        val green700 @Composable get() = colorResource(R.color.foundation_success_green700)
        val green800 @Composable get() = colorResource(R.color.foundation_success_green800)

        /* PIN/OTP Component Specific Palette */
        val palettePinGreen500 @Composable get() = colorResource(R.color.foundation_palette_pin_green500)
        val palettePinRed400 @Composable get() = colorResource(R.color.foundation_palette_pin_red400)

        // ====== FUNCTIONAL TOKENS ======
        /* Character Tokens */
        val characterHighlighted @Composable get() = colorResource(R.color.foundation_character_highlighted)
        val characterHighlightedLighter @Composable get() = colorResource(R.color.foundation_character_highlighted_lighter)
        val characterPrimary @Composable get() = colorResource(R.color.foundation_character_primary)
        val characterSecondary @Composable get() = colorResource(R.color.foundation_character_secondary)
        val characterTertiary @Composable get() = colorResource(R.color.foundation_character_tertiary)
        val characterInverse @Composable get() = colorResource(R.color.foundation_character_inverse)

        /* Background Tokens */
        val backgroundBgHighlight @Composable get() = colorResource(R.color.foundation_background_bg_highlight)
        val backgroundBgContainer @Composable get() = colorResource(R.color.foundation_background_bg_container)
        val backgroundBgScreen @Composable get() = colorResource(R.color.foundation_background_bg_screen)
        val backgroundBgOnColor @Composable get() = colorResource(R.color.foundation_background_bg_on_color)

        /* Stroke Tokens */
        val strokeDivider @Composable get() = colorResource(R.color.foundation_stroke_divider)

        /* State Tokens */
        val stateError @Composable get() = colorResource(R.color.foundation_state_error)
        val stateWarning @Composable get() = colorResource(R.color.foundation_state_warning)
        val stateSuccess @Composable get() = colorResource(R.color.foundation_state_success)
        val stateActive @Composable get() = colorResource(R.color.foundation_state_active)
        val stateErrorLighter @Composable get() = colorResource(R.color.foundation_state_error_lighter)
        val stateWarningLighter @Composable get() = colorResource(R.color.foundation_state_warning_lighter)
        val stateSuccessLighter @Composable get() = colorResource(R.color.foundation_state_success_lighter)
        val stateActiveLighter @Composable get() = colorResource(R.color.foundation_state_active_lighter)
        val stateBadgeCounting @Composable get() = colorResource(R.color.foundation_state_badge_counting)

        // ====== COMPONENT-SPECIFIC COLORS ======
        /* Shadow colors */
        val shadowSm @Composable get() = colorResource(R.color.foundation_shadow_sm)
        val shadowMd @Composable get() = colorResource(R.color.foundation_shadow_md)
        val shadowLg @Composable get() = colorResource(R.color.foundation_shadow_lg)
        val shadowOuter @Composable get() = colorResource(R.color.foundation_shadow_outer)
        val shadowInnerWhite @Composable get() = colorResource(R.color.foundation_shadow_inner_white)
        val shadowInnerWhiteSoft @Composable get() = colorResource(R.color.foundation_shadow_inner_white_soft)

        /* Button gradient colors */
        val buttonGradientPrimary @Composable get() = colorResource(R.color.foundation_button_gradient_primary)
        val buttonGradientPrimaryTransparent @Composable get() = colorResource(R.color.foundation_button_gradient_primary_transparent)
        val buttonGradientPrimaryPressed @Composable get() = colorResource(R.color.foundation_button_gradient_primary_pressed)
        val buttonGradientSecondary @Composable get() = colorResource(R.color.foundation_button_gradient_secondary)
        val buttonGradientSecondaryTransparent @Composable get() = colorResource(R.color.foundation_button_gradient_secondary_transparent)
        val buttonDarkDisablePrimary @Composable get() = colorResource(R.color.foundation_button_dark_disable_primary)
        val buttonDarkDisableSecondary @Composable get() = colorResource(R.color.foundation_button_dark_disable_secondary)
        val darkButtonPressedState @Composable get() = colorResource(R.color.foundation_button_dark_pressed)
        val darkButtonEnableState @Composable get() = colorResource(R.color.foundation_button_dark_enable)

        /* Tab Component Colors */
        val tabTextActiveInline @Composable get() = colorResource(R.color.foundation_tab_text_active_inline)
        val tabTextSecondary @Composable get() = colorResource(R.color.foundation_tab_text_secondary)
        val tabTextActivePill @Composable get() = colorResource(R.color.foundation_tab_text_active_pill)
        val tabTextInactive @Composable get() = colorResource(R.color.foundation_tab_text_inactive)
        val tabBorderActive @Composable get() = colorResource(R.color.foundation_tab_border_active)
        val tabDividerInactive @Composable get() = colorResource(R.color.foundation_tab_divider_inactive)
        val tabBgActivePill @Composable get() = colorResource(R.color.foundation_tab_bg_active_pill)
        val tabBgContainerInline @Composable get() = colorResource(R.color.foundation_tab_bg_container_inline)

        /* Tab gradient colors */
        val tabGradient1 @Composable get() = colorResource(R.color.foundation_tab_gradient_1)
        val tabGradient2 @Composable get() = colorResource(R.color.foundation_tab_gradient_2)
        val tabGradient3 @Composable get() = colorResource(R.color.foundation_tab_gradient_3)
        val tabGradient4 @Composable get() = colorResource(R.color.foundation_tab_gradient_4)
        val tabGradient5 @Composable get() = colorResource(R.color.foundation_tab_gradient_5)

        /* Glass Morphism gradient colors */
        val glassGradient1 @Composable get() = colorResource(R.color.foundation_glass_gradient_1)
        val glassGradient2 @Composable get() = colorResource(R.color.foundation_glass_gradient_2)
        val glassGradient3 @Composable get() = colorResource(R.color.foundation_glass_gradient_3)
        val glassGradient4 @Composable get() = colorResource(R.color.foundation_glass_gradient_4)
        val glassGradient5 @Composable get() = colorResource(R.color.foundation_glass_gradient_5)

        val homeBorderButton @Composable get() = colorResource(R.color.home_border_button)
        val homeTextButton @Composable get() = colorResource(R.color.home_text_button)
        val homeBackgroundIcon @Composable get() = colorResource(R.color.home_background_icon)

        val foundationDarkButtonPrimary @Composable get() = colorResource(R.color.foundation_dark_button_primary)
        val foundationDarkButtonPressedPrimary @Composable get() = colorResource(R.color.foundation_dark_button_pressed_state_primary)
        val foundationDarkButtonPressesSecondary @Composable get() = colorResource(R.color.foundation_dark_button_pressed_state_secondary)
        val foundationDarkButtonTextDisable @Composable get() = colorResource(R.color.foundation_dark_button_text_disable)

        /* Dialog colors */
        val dialogBackground @Composable get() = colorResource(R.color.foundation_dialog_background)

        /* New Login UI colors */
        val backgroundDarkBlue @Composable get() = colorResource(R.color.foundation_background_dark_blue)
        val borderGradientStart @Composable get() = colorResource(R.color.foundation_border_gradient_start)
        val avatarGradientEnd @Composable get() = colorResource(R.color.foundation_avatar_gradient_end)
        val textLinkBlue @Composable get() = colorResource(R.color.foundation_text_link_blue)
        val fillTextPrimary @Composable get() = colorResource(R.color.foundation_fill_text_primary)
        val textGuideLine @Composable get() = colorResource(R.color.foundation_text_guide_line)
    }

    // ========================================
    // TYPOGRAPHY
    // ========================================
    object Typography {
        // Material3 Typography instance
        val material3Typography = Typography(
            displayLarge = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.Bold,
                fontSize = 32.sp,
                lineHeight = 40.sp,
                letterSpacing = 0.sp,
            ),
            displayMedium = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.Bold,
                fontSize = 28.sp,
                lineHeight = 36.sp,
                letterSpacing = 0.sp,
            ),
            displaySmall = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.Bold,
                fontSize = 24.sp,
                lineHeight = 32.sp,
                letterSpacing = 0.sp,
            ),
            headlineLarge = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.SemiBold,
                fontSize = 24.sp,
                lineHeight = 32.sp,
                letterSpacing = 0.sp,
            ),
            headlineMedium = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.SemiBold,
                fontSize = 20.sp,
                lineHeight = 28.sp,
                letterSpacing = 0.sp,
            ),
            headlineSmall = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.SemiBold,
                fontSize = 18.sp,
                lineHeight = 24.sp,
                letterSpacing = 0.sp,
            ),
            titleLarge = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.SemiBold,
                fontSize = 20.sp,
                lineHeight = 28.sp,
                letterSpacing = 0.sp,
            ),
            titleMedium = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.SemiBold,
                fontSize = 16.sp,
                lineHeight = 24.sp,
                letterSpacing = 0.15.sp,
            ),
            titleSmall = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.Medium,
                fontSize = 14.sp,
                lineHeight = 20.sp,
                letterSpacing = 0.1.sp,
            ),
            bodyLarge = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.Normal,
                fontSize = 16.sp,
                lineHeight = 24.sp,
                letterSpacing = 0.15.sp,
            ),
            bodyMedium = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.Normal,
                fontSize = 14.sp,
                lineHeight = 20.sp,
                letterSpacing = 0.25.sp,
            ),
            bodySmall = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.Normal,
                fontSize = 12.sp,
                lineHeight = 16.sp,
                letterSpacing = 0.4.sp,
            ),
            labelLarge = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.Medium,
                fontSize = 14.sp,
                lineHeight = 20.sp,
                letterSpacing = 0.1.sp,
            ),
            labelMedium = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.Medium,
                fontSize = 12.sp,
                lineHeight = 16.sp,
                letterSpacing = 0.5.sp,
            ),
            labelSmall = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.Medium,
                fontSize = 10.sp,
                lineHeight = 16.sp,
                letterSpacing = 0.5.sp,
            ),
        )

        // ====== FUNCTIONAL TOKENS ======
        /* Heading Tokens */
        val headingH1 = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.SemiBold,
            fontSize = 32.sp,
            lineHeight = 40.sp,
            letterSpacing = (-0.01).sp,
        )

        val headingH2 = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.SemiBold,
            fontSize = 24.sp,
            lineHeight = 32.sp,
            letterSpacing = (-0.01).sp,
        )

        val headingH3 = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.SemiBold,
            fontSize = 20.sp,
            lineHeight = 28.sp, // Updated from 24 to match Figma
            letterSpacing = (-0.01).sp,
        )

        val headingH4 = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.SemiBold,
            fontSize = 18.sp, // Updated from 16 to match Figma
            lineHeight = 24.sp,
            letterSpacing = (-0.01).sp,
        )

        /* Body Tokens */
        val bodyB1 = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.SemiBold, // Updated from Medium to match Figma
            fontSize = 18.sp, // Updated from 16 to match Figma
            lineHeight = 28.sp, // Updated from 24 to match Figma
            letterSpacing = (-0.015).sp,
        )

        val bodyB1Emphasized = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.Bold, // Updated from SemiBold to match Figma
            fontSize = 18.sp, // Updated from 16 to match Figma
            lineHeight = 28.sp, // Updated from 24 to match Figma
            letterSpacing = (-0.015).sp,
        )

        val bodyB2 = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.SemiBold, // Updated from Medium to match Figma
            fontSize = 16.sp, // Updated from 14 to match Figma
            lineHeight = 24.sp, // Updated from 20 to match Figma
            letterSpacing = (-0.015).sp,
        )

        val bodyB2Emphasized = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.Bold, // Updated from SemiBold to match Figma
            fontSize = 16.sp, // Updated from 14 to match Figma
            lineHeight = 24.sp, // Updated from 20 to match Figma
            letterSpacing = (-0.015).sp,
        )

        /* Interaction Tokens */
        val interactionButton = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.SemiBold,
            fontSize = 16.sp,
            lineHeight = 24.sp,
            letterSpacing = (-0.02).sp,
        )

        val interactionSmallButton = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.Bold, // Updated from SemiBold to match Figma
            fontSize = 14.sp, // Updated from 13 to match Figma
            lineHeight = 16.sp,
            letterSpacing = (-0.015).sp,
        )

        val interactionLink = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.SemiBold,
            fontSize = 14.sp, // Updated from 12 to match Figma
            lineHeight = 16.sp,
            letterSpacing = (-0.015).sp,
        )

        /* Caption Tokens */
        val captionCaptionL = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.SemiBold, // Updated from Medium to match Figma
            fontSize = 14.sp, // Updated from 12 to match Figma
            lineHeight = 18.sp, // Updated from 16 to match Figma
            letterSpacing = (-0.0008).sp,
        )

        val captionCaptionLBold = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.Bold,
            fontSize = 14.sp, // Updated from 12 to match Figma
            lineHeight = 18.sp, // Updated from 16 to match Figma
            letterSpacing = (-0.0008).sp,
        )

        val captionCaptionM = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.Medium,
            fontSize = 12.sp, // Updated from 10 to match Figma
            lineHeight = 16.sp, // Updated from 12 to match Figma
            letterSpacing = (-0.0008).sp,
        )

        val captionCaptionMBold = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.Bold,
            fontSize = 12.sp, // Updated from 10 to match Figma
            lineHeight = 16.sp, // Updated from 12 to match Figma
            letterSpacing = (-0.0008).sp,
        )
    }

    // ========================================
    // SIZER (Dimensions)
    // ========================================
    object Sizer {
        /**
         * Padding values - Direct mapping with Figma Sizer/Padding
         */
        object Padding {
            val padding0 @Composable get() = 0.dp
            val padding2 @Composable get() = 2.dp
            val padding4 @Composable get() = 4.dp
            val padding6 @Composable get() = 6.dp
            val padding8 @Composable get() = 8.dp
            val padding10 @Composable get() = 10.dp
            val padding12 @Composable get() = 12.dp
            val padding16 @Composable get() = 16.dp
            val padding20 @Composable get() = 20.dp
            val padding24 @Composable get() = 24.dp
            val padding32 @Composable get() = 32.dp
            val padding40 @Composable get() = 40.dp
            val padding48 @Composable get() = 48.dp
            val padding56 @Composable get() = 56.dp
            val padding64 @Composable get() = 64.dp
            val padding96 @Composable get() = 96.dp
            val padding100 @Composable get() = 100.dp
            val padding144 @Composable get() = 144.dp
            val padding260 @Composable get() = 260.dp
            val padding280 @Composable get() = 280.dp
        }

        /**
         * Gap values - Direct mapping with Figma Sizer/Gap
         * Used for spacing between elements (Row/Column arrangements)
         */
        object Gap {
            val gap0 @Composable get() = 0.dp
            val gap1 @Composable get() = 1.dp
            val gap2 @Composable get() = 2.dp
            val gap4 @Composable get() = 4.dp
            val gap6 @Composable get() = 6.dp
            val gap8 @Composable get() = 8.dp
            val gap12 @Composable get() = 12.dp
            val gap16 @Composable get() = 16.dp
            val gap20 @Composable get() = 20.dp
            val gap24 @Composable get() = 24.dp
            val gap32 @Composable get() = 32.dp
            val gap36 @Composable get() = 36.dp
            val gap40 @Composable get() = 40.dp
            val gap48 @Composable get() = 48.dp
            val gap56 @Composable get() = 56.dp
            val gap60 @Composable get() = 60.dp
            val gap64 @Composable get() = 64.dp
            val gap96 @Composable get() = 96.dp
            val gap300 @Composable get() = 300.dp
        }

        /**
         * Icon sizes - Direct mapping with Figma Sizer/Icon
         */
        object Icon {
            val icon4 @Composable get() = 4.dp // xxs - indicators
            val icon8 @Composable get() = 8.dp // xxs - indicators
            val icon14 @Composable get() = 14.dp // xs- - small flags/badges
            val icon16 @Composable get() = 16.dp // xs
            val icon18 @Composable get() = 18.dp // sm-
            val icon20 @Composable get() = 20.dp // sm
            val icon24 @Composable get() = 24.dp // md - default
            val icon32 @Composable get() = 32.dp // lg
            val icon40 @Composable get() = 40.dp // xl
            val icon48 @Composable get() = 48.dp // xxl
            val icon56 @Composable get() = 56.dp // 3xl
            val icon72 @Composable get() = 72.dp // icon
            val icon96 @Composable get() = 96.dp // 3xl
        }

        /**
         * Border radius values - Direct mapping with Figma Sizer/Radius
         */
        object Radius {
            val radius0 @Composable get() = 0.dp
            val radius4 @Composable get() = 4.dp // xs
            val radius8 @Composable get() = 8.dp // sm
            val radius10 @Composable get() = 10.dp // md
            val radius16 @Composable get() = 16.dp // lg
            val radius20 @Composable get() = 20.dp // xl
            val radius32 @Composable get() = 32.dp // default
            val radiusFull @Composable get() = 999.dp // full - circular
        }

        /**
         * Stroke/Border width values - Direct mapping with Figma Sizer/Stroke
         */
        object Stroke {
            val stroke05 @Composable get() = 0.5.dp // xs - thin
            val stroke1 @Composable get() = 1.dp // sm - default
            val stroke2 @Composable get() = 2.dp // md - medium
            val stroke4 @Composable get() = 4.dp // lg - thick
        }

        /**
         * Text size values - Direct mapping with Figma typography sizes
         * Note: These are for quick access. For full typography, use Typography
         */
        object Text {
            val text12 @Composable get() = 12.sp // xs
            val text14 @Composable get() = 14.sp // sm - default body
            val text16 @Composable get() = 16.sp // md - large body
            val text18 @Composable get() = 18.sp // lg
            val text20 @Composable get() = 20.sp // xl
            val text24 @Composable get() = 24.sp // 2xl
            val text30 @Composable get() = 30.sp // 3xl
            val text36 @Composable get() = 36.sp // 4xl
        }

        /**
         * Tab component dimensions - Direct mapping with foundation_tab dimensions
         */
        object Tab {
            val tabHeight @Composable get() = 32.dp // foundation_tab_height
            val containerHeight @Composable get() = 48.dp // foundation_tab_container_height
        }

        /**
         * Dialog and Modal dimensions - Maximum constraints for responsive design
         */
        object Dialog {
            val maxWidth @Composable get() = 600.dp // Maximum width for dialogs on tablets
            val maxListHeight @Composable get() = 400.dp // Maximum height for scrollable lists in dialogs
            val minWidth @Composable get() = 280.dp // Minimum width for dialogs
        }

        /**
         * Button dimensions - Common button sizes
         */
        object Button {
            val smallButtonWidth @Composable get() = 61.dp // Small elliptical buttons (language selector)
            val defaultButtonHeight @Composable get() = 48.dp // Default button height
            val largeButtonHeight @Composable get() = 56.dp // Large button height
        }

        /**
         * Offset values - For positioning adjustments
         */
        object Offset {
            val offsetNegative2 @Composable get() = (-2).dp // Small negative offset
            val offsetPositive2 @Composable get() = 2.dp // Small positive offset
            val offsetNegative6 @Composable get() = (-6).dp // Medium negative offset
            val offsetPositive6 @Composable get() = 6.dp // Medium positive offset
        }

        /**
         * Legacy spacing values - Use Gap for new code
         * Kept for backward compatibility during migration
         */
        object Spacing {
            val spacingMini @Composable get() = 4.dp // xs - same as gap4
            val spacingSmall @Composable get() = 8.dp // sm - same as gap8
            val spacingMedium @Composable get() = 16.dp // md - same as gap16
            val spacingLarge @Composable get() = 24.dp // lg - same as gap24
            val spacingXLarge @Composable get() = 32.dp // xl - same as gap32
        }

        /**
         * Container dimensions for content areas
         * Used for loading states, error states, and minimum content heights
         */
        object Container {
            val contentMinHeight @Composable get() = 400.dp // Minimum height for loading/error states
        }

        /**
         * Screen breakpoint values for responsive design
         * Note: These are not from Figma design tokens but necessary for responsive layouts
         */
        object ScreenBreakpoint {
            val tabletMinWidth @Composable get() = 600.dp // Standard tablet breakpoint
        }

        /**
         * Helper function to read dimension resources
         */
        @Composable
        fun dimensionResource(@DimenRes id: Int): Dp {
            return LocalContext.current.resources.getDimension(id).dp
        }
    }

    // ========================================
    // EFFECTS
    // ========================================
    object Effects {
        // Elevation levels from Foundation Design
        val elevationSm: Dp = 2.dp // Small shadow for cards
        val elevationMd: Dp = 4.dp // Medium shadow for dialogs, popups
        val elevationLg: Dp = 8.dp // Large shadow for modals, overlays
        val elevationButton: Dp = 10.dp // Button elevation per Foundation Design

        // Additional standard elevations
        val elevationNone: Dp = 0.dp
        val elevationXs: Dp = 1.dp
        val elevationXl: Dp = 12.dp
        val elevationXxl: Dp = 16.dp
    }
}

// Type alias for convenience
typealias FDS = FoundationDesignSystem