package com.vietinbank.core_ui.theme

import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * AppTypography - Backward compatibility wrapper for FoundationDesignSystem.Typography
 *
 * For new code, use FoundationDesignSystem.Typography directly
 * This object only exists to support legacy code
 */
object AppTypography {

    // Material3 Typography instance
    val typography get() = FDS.Typography.material3Typography

    // ========================================
    // FUNCTIONAL TOKENS
    // ========================================

    /* Heading Tokens */
    val headingH1 get() = FDS.Typography.headingH1
    val headingH2 get() = FDS.Typography.headingH2
    val headingH3 get() = FDS.Typography.headingH3
    val headingH4 get() = FDS.Typography.headingH4

    /* Body Tokens */
    val bodyB1 get() = FDS.Typography.bodyB1
    val bodyB1Emphasized get() = FDS.Typography.bodyB1Emphasized
    val bodyB2 get() = FDS.Typography.bodyB2
    val bodyB2Emphasized get() = FDS.Typography.bodyB2Emphasized

    /* Interaction Tokens */
    val interactionButton get() = FDS.Typography.interactionButton
    val interactionSmallButton get() = FDS.Typography.interactionSmallButton
    val interactionLink get() = FDS.Typography.interactionLink

    /* Caption Tokens */
    val captionCaptionL get() = FDS.Typography.captionCaptionL
    val captionCaptionLBold get() = FDS.Typography.captionCaptionLBold
    val captionCaptionM get() = FDS.Typography.captionCaptionM
    val captionCaptionMBold get() = FDS.Typography.captionCaptionMBold
}