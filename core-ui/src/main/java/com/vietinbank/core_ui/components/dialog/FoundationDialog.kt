package com.vietinbank.core_ui.components.dialog

import android.os.Bundle
import android.os.Parcelable
import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.dialog.BaseDialog
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppTheme
import kotlinx.parcelize.Parcelize
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

private object FoundationDialogConstants {
    // Non-design values (multipliers only)
    const val MAX_WIDTH_RATIO = 0.9f // Dialog takes up 90% of screen width max
}

enum class FDLayout { Center, BottomSheet }

/**
 * Dialog types for automatic icon selection
 */
enum class DialogType {
    WARNING, // Default - uses ic_common_warning_72
    ERROR, // For error messages
    SUCCESS, // For success confirmations
    INFO, // For informational messages
    SECURITY, // For security-related dialogs (fingerprint, password, etc.)
    CUSTOM, // When custom icon is provided
}

/**
 * Result for FoundationDialog actions
 */
@Parcelize
data class FoundationDialogResult(
    val action: DialogAction,
    val timestamp: Long = System.currentTimeMillis(),
) : Parcelable

/**
 * Dialog actions
 */
enum class DialogAction {
    POSITIVE,
    NEGATIVE,
    DISMISSED,
}

/**
 * Foundation Dialog using Jetpack Compose
 * Drop-in replacement for NoticeDialog with same API
 */
class FoundationDialog : BaseDialog<FoundationDialogResult>() {

    // Dialog configuration for BaseDialog
    override val resultKey = "foundation_dialog_result"
    override val layout: DialogLayout
        get() = when (FDLayout.valueOf(arguments?.getString(ARG_LAYOUT) ?: FDLayout.Center.name)) {
            FDLayout.Center -> DialogLayout.Center
            FDLayout.BottomSheet -> DialogLayout.BottomSheet
        }
    override val allowTouchDismiss: Boolean
        get() = arguments?.getBoolean(ARG_CANCELABLE, true) ?: true
    override val requiresSecureFlag = false
    override val maxWidthDp = 400

    // Override motion to support custom scrimAlpha from arguments
    override val motion: DialogMotion
        get() {
            val scrimAlpha = arguments?.getFloat(ARG_SCRIM_ALPHA, 0.52f) ?: 0.52f
            return DialogMotion(scrimAlpha = scrimAlpha)
        }

    companion object {
        const val ARG_TITLE = "arg_title"
        const val ARG_MESSAGE = "arg_message"
        const val ARG_POSITIVE_BUTTON = "arg_positive_button"
        const val ARG_NEGATIVE_BUTTON = "arg_negative_button"
        const val ARG_SHOW_NEGATIVE = "arg_show_negative"
        const val ARG_CANCELABLE = "arg_cancelable"
        const val ARG_ICON = "arg_icon"
        const val ARG_TYPE = "arg_type"
        const val ARG_LAYOUT = "arg_layout"
        const val ARG_SCRIM_ALPHA = "arg_scrim_alpha"

        /**
         * Maps DialogType to appropriate icon resource
         * Can be extended later with more icons
         */
        private fun getIconForType(type: DialogType): Int {
            return when (type) {
                DialogType.WARNING -> R.drawable.ic_common_warning_72
                DialogType.ERROR -> R.drawable.ic_common_warning_72 // TODO: Replace with error icon when available
                DialogType.SUCCESS -> R.drawable.ic_commom_success_24 // TODO: Replace with 72dp success icon when available
                DialogType.INFO -> R.drawable.ic_common_warning_72 // TODO: Replace with info icon when available
                DialogType.SECURITY -> R.drawable.ic_lock
                DialogType.CUSTOM -> 0 // No default icon for custom type
            }
        }

        fun newInstance(
            message: String,
            title: String? = null,
            positiveButtonText: String? = null,
            negativeButtonText: String? = null,
            showNegativeButton: Boolean = false,
            cancelable: Boolean = true,
            type: DialogType = DialogType.WARNING,
            @DrawableRes icon: Int? = null,
            layout: FDLayout = FDLayout.BottomSheet,
            scrimAlpha: Float? = 0.80f,
        ): FoundationDialog {
            val fragment = FoundationDialog()
            val bundle = Bundle()
            bundle.putString(ARG_TITLE, title)
            bundle.putString(ARG_MESSAGE, message)
            bundle.putString(ARG_POSITIVE_BUTTON, positiveButtonText)
            bundle.putString(ARG_NEGATIVE_BUTTON, negativeButtonText)
            bundle.putBoolean(ARG_SHOW_NEGATIVE, showNegativeButton)
            bundle.putBoolean(ARG_CANCELABLE, cancelable)
            bundle.putString(ARG_LAYOUT, layout.name)
            bundle.putString(ARG_TYPE, type.name)
            scrimAlpha?.let { bundle.putFloat(ARG_SCRIM_ALPHA, it) }

            // If custom icon is provided, use it. Otherwise use icon based on type
            val finalIcon = icon ?: if (type != DialogType.CUSTOM) getIconForType(type) else null
            finalIcon?.let {
                if (it != 0) bundle.putInt(ARG_ICON, it)
            }

            fragment.arguments = bundle
            return fragment
        }
    }

    // Callbacks for backward compatibility
    private var onPositiveClick: (() -> Unit)? = null
    private var onNegativeClick: (() -> Unit)? = null
    private var onDismissClick: (() -> Unit)? = null

    fun setOnPositiveClickListener(listener: () -> Unit) {
        onPositiveClick = listener
    }

    fun setOnNegativeClickListener(listener: () -> Unit) {
        onNegativeClick = listener
    }

    fun setOnDismissClickListener(listener: () -> Unit) {
        onDismissClick = listener
    }

    // setCancelable is handled by BaseDialog's allowTouchDismiss property

    @Composable
    override fun DialogContent(
        visible: Boolean,
        onDismissRequest: () -> Unit,
        onResult: (FoundationDialogResult) -> Unit,
    ) {
        val title = arguments?.getString(ARG_TITLE) ?: stringResource(R.string.dialog_title_default)
        val message = arguments?.getString(ARG_MESSAGE) ?: ""
        val positiveButtonText = arguments?.getString(ARG_POSITIVE_BUTTON) ?: stringResource(
            R.string.dialog_button_confirm,
        )
        val negativeButtonText = arguments?.getString(ARG_NEGATIVE_BUTTON) ?: stringResource(
            R.string.dialog_button_cancel,
        )
        val showNegativeButton = arguments?.getBoolean(ARG_SHOW_NEGATIVE) ?: false

        // Get icon: if provided use it, otherwise default to warning icon
        val iconRes = if (arguments?.containsKey(ARG_ICON) == true) {
            arguments?.getInt(ARG_ICON)
        } else {
            // Default to warning icon if no icon is provided
            R.drawable.ic_common_warning_72
        }

        // Internal handler that wraps both Fragment Result API and callbacks
        val handlePositive = {
            onPositiveClick?.invoke() // Backward compatibility
            onResult(FoundationDialogResult(DialogAction.POSITIVE)) // Fragment Result API
        }

        val handleNegative = {
            onNegativeClick?.invoke() // Backward compatibility
            onResult(FoundationDialogResult(DialogAction.NEGATIVE)) // Fragment Result API
        }

        val handleDismiss = {
            onDismissClick?.invoke() // Backward compatibility
            onResult(FoundationDialogResult(DialogAction.DISMISSED)) // Fragment Result API
        }

        Column(
            modifier = Modifier
                .fillMaxWidth(if (layout == DialogLayout.BottomSheet) 1f else FoundationDialogConstants.MAX_WIDTH_RATIO)
                .widthIn(max = FDS.Sizer.Dialog.maxWidth) // Limit width on tablets
                .padding(horizontal = FDS.Sizer.Padding.padding8),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            // White content area
            Surface(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
                color = FDS.Colors.backgroundBgContainer,
                shadowElevation = FDS.Effects.elevationLg,
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            top = FDS.Sizer.Padding.padding24,
                            bottom = FDS.Sizer.Padding.padding32,
                        ),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    // Title
                    FoundationText(
                        text = title,
                        style = FDS.Typography.headingH3,
                        color = FDS.Colors.characterHighlighted,
                        textAlign = TextAlign.Center,
                    )

                    // Divider
                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
                    HorizontalDivider(
                        color = FDS.Colors.divider,
                        thickness = 1.dp,
                    )

                    // Icon - includes background from Figma export (always shown with default)
                    iconRes?.let { icon ->
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
                        Image(
                            painter = painterResource(id = icon),
                            contentDescription = null,
                            modifier = Modifier.size(FDS.Sizer.Icon.icon96), // Full size including background
                        )
                    }

                    // Message content
                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
                    FoundationText(
                        text = message,
                        style = FDS.Typography.bodyB2,
                        color = FDS.Colors.characterPrimary,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
                    )
                }
            }

            // Button area - separate from white content
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        horizontal = FDS.Sizer.Padding.padding8, // Additional padding for buttons
                        vertical = FDS.Sizer.Padding.padding16,
                    ),
                horizontalArrangement = if (showNegativeButton) {
                    Arrangement.spacedBy(FDS.Sizer.Gap.gap8)
                } else {
                    Arrangement.Center
                },
            ) {
                if (showNegativeButton) {
                    // Negative button - dark style
                    FoundationButton(
                        text = negativeButtonText,
                        onClick = handleNegative,
                        modifier = Modifier
                            .weight(1f)
                            .height(FDS.Sizer.Padding.padding56),
                        isLightButton = false,
                    )

                    // Positive button - light style
                    FoundationButton(
                        text = positiveButtonText,
                        onClick = handlePositive,
                        modifier = Modifier
                            .weight(1f)
                            .height(FDS.Sizer.Padding.padding56),
                    )
                } else {
                    // Single button - light style
                    FoundationButton(
                        text = positiveButtonText,
                        onClick = handlePositive,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(FDS.Sizer.Padding.padding56),
                    )
                }
            }
        }
    }

    // Cleanup is handled by BaseDialog lifecycle
}

/**
 * Preview helper - shows dialog content without animation wrapper
 */
@Composable
private fun PreviewDialogContent(
    title: String = "Thông báo",
    message: String,
    iconRes: Int? = R.drawable.ic_common_warning_72,
    showNegativeButton: Boolean = false,
    positiveButtonText: String = "Xác nhận",
    negativeButtonText: String = "Hủy bỏ",
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = FDS.Sizer.Padding.padding16),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        // White content area
        Surface(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
            color = FDS.Colors.backgroundBgContainer,
            shadowElevation = FDS.Effects.elevationLg,
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        top = FDS.Sizer.Padding.padding24,
                        bottom = FDS.Sizer.Padding.padding32,
                    ),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                // Title
                FoundationText(
                    text = title,
                    style = FDS.Typography.headingH3,
                    color = FDS.Colors.characterHighlighted,
                    textAlign = TextAlign.Center,
                )

                // Divider
                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
                HorizontalDivider(
                    color = FDS.Colors.divider,
                    thickness = 1.dp,
                )

                // Icon
                iconRes?.let { icon ->
                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
                    Image(
                        painter = painterResource(id = icon),
                        contentDescription = null,
                        modifier = Modifier.size(FDS.Sizer.Icon.icon96),
                    )
                }

                // Message content
                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
                FoundationText(
                    text = message,
                    style = FDS.Typography.bodyB1,
                    color = FDS.Colors.characterPrimary,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
                )
            }
        }

        // Button area
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = FDS.Sizer.Padding.padding8,
                    vertical = FDS.Sizer.Padding.padding16,
                ),
            horizontalArrangement = if (showNegativeButton) {
                Arrangement.spacedBy(FDS.Sizer.Gap.gap8)
            } else {
                Arrangement.Center
            },
        ) {
            if (showNegativeButton) {
                FoundationButton(
                    text = negativeButtonText,
                    onClick = {},
                    modifier = Modifier
                        .weight(1f)
                        .height(FDS.Sizer.Padding.padding56),
                    isLightButton = false,
                )

                FoundationButton(
                    text = positiveButtonText,
                    onClick = {},
                    modifier = Modifier
                        .weight(1f)
                        .height(FDS.Sizer.Padding.padding56),
                )
            } else {
                FoundationButton(
                    text = positiveButtonText,
                    onClick = {},
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(FDS.Sizer.Padding.padding56),
                )
            }
        }
    }
}

@Preview(showBackground = true, backgroundColor = 0xFF000000)
@Composable
private fun FoundationDialogPreview() {
    AppTheme {
        PreviewDialogContent(
            message = "Đây là nội dung thông báo mẫu để kiểm tra giao diện dialog. Nội dung có thể dài hoặc ngắn tùy theo yêu cầu.",
        )
    }
}

@Preview(showBackground = true, backgroundColor = 0xFF000000)
@Composable
private fun FoundationDialogTwoButtonsPreview() {
    AppTheme {
        PreviewDialogContent(
            title = "Xác nhận",
            message = "Bạn có chắc chắn muốn thực hiện giao dịch này không?",
            showNegativeButton = true,
            positiveButtonText = "Xác nhận",
            negativeButtonText = "Hủy bỏ",
        )
    }
}

@Preview(showBackground = true, backgroundColor = 0xFF000000)
@Composable
private fun FoundationDialogWithIconPreview() {
    AppTheme {
        PreviewDialogContent(
            title = "Thông báo",
            message = "Thiết bị chưa cài đặt nhận diện khuôn mặt. Quý khách vui lòng cài đặt nhận diện khuôn mặt trên thiết bị trước.",
            iconRes = R.drawable.ic_lock,
            showNegativeButton = true,
            positiveButtonText = "Hướng dẫn",
            negativeButtonText = "Quay lại",
        )
    }
}