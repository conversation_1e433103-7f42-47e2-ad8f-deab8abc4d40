package com.vietinbank.core_ui.components.foundation.textfield.transformations

import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.input.OffsetMapping
import androidx.compose.ui.text.input.TransformedText
import androidx.compose.ui.text.input.VisualTransformation
import com.vietinbank.core_common.utils.Utils

/**
 * Visual transformation for amount fields
 * Uses existing Utils.g().getDotMoneyHasCcy for formatting
 */
class AmountVisualTransformation() : VisualTransformation {

    override fun filter(text: AnnotatedString): TransformedText {
        // Remove all non-digit characters
        val cleanText = text.text.filter { it.isDigit() }

        // Format using existing utility
        val formattedText = if (cleanText.isEmpty()) {
            ""
        } else {
            Utils.g().getDotMoney(cleanText)
        }

        // Create offset mapping for cursor position
        val offsetMapping = object : OffsetMapping {
            override fun originalToTransformed(offset: Int): Int {
                // For simplicity, place cursor at the end after formatting
                // A more sophisticated implementation would calculate exact position
                return formattedText.length
            }

            override fun transformedToOriginal(offset: Int): Int {
                // Map back to original position
                return cleanText.length.coerceAtMost(offset)
            }
        }

        return TransformedText(
            text = AnnotatedString(formattedText),
            offsetMapping = offsetMapping,
        )
    }
}

/**
 * Visual transformation for card numbers
 * Formats as: 1234 5678 9012 3456
 */
class CardNumberVisualTransformation : VisualTransformation {
    override fun filter(text: AnnotatedString): TransformedText {
        val cleanText = text.text.filter { it.isDigit() }.take(16)

        // Add spaces every 4 digits
        val formatted = buildString {
            cleanText.forEachIndexed { index, char ->
                if (index > 0 && index % 4 == 0) {
                    append(' ')
                }
                append(char)
            }
        }

        val offsetMapping = object : OffsetMapping {
            override fun originalToTransformed(offset: Int): Int {
                // Account for added spaces
                val spaces = offset / 4
                return (offset + spaces).coerceAtMost(formatted.length)
            }

            override fun transformedToOriginal(offset: Int): Int {
                // Remove spaces from count
                return formatted.take(offset).count { it.isDigit() }
            }
        }

        return TransformedText(
            text = AnnotatedString(formatted),
            offsetMapping = offsetMapping,
        )
    }
}

/**
 * Visual transformation for phone numbers (Vietnam format)
 * Formats as: 0912 345 678
 */
class PhoneNumberVisualTransformation : VisualTransformation {
    override fun filter(text: AnnotatedString): TransformedText {
        val cleanText = text.text.filter { it.isDigit() }.take(10)

        // Format phone number
        val formatted = when (cleanText.length) {
            in 0..3 -> cleanText
            in 4..6 -> "${cleanText.substring(0, 4)} ${cleanText.substring(4)}"
            in 7..10 -> "${cleanText.substring(0, 4)} ${cleanText.substring(4, 7)} ${cleanText.substring(7)}"
            else -> cleanText
        }

        val offsetMapping = object : OffsetMapping {
            override fun originalToTransformed(offset: Int): Int {
                return when {
                    offset <= 4 -> offset
                    offset <= 7 -> offset + 1 // Account for first space
                    else -> offset + 2 // Account for both spaces
                }.coerceAtMost(formatted.length)
            }

            override fun transformedToOriginal(offset: Int): Int {
                return formatted.take(offset).count { it.isDigit() }
            }
        }

        return TransformedText(
            text = AnnotatedString(formatted),
            offsetMapping = offsetMapping,
        )
    }
}