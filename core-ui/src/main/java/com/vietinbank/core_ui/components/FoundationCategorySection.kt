package com.vietinbank.core_ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun FoundationCategorySection(
    modifier: Modifier = Modifier,
    title: String? = null,
    value: String? = null,
    icon: Int? = null,
    titleColor: Color = FDS.Colors.characterSecondary,
    titleStyle: TextStyle = FDS.Typography.captionCaptionL,
    valueColor: Color = FDS.Colors.characterHighlighted,
    valueStyle: TextStyle = FDS.Typography.bodyB1,
    isShowLine: Boolean = false,
    onClick: (() -> Unit)? = null,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .safeClickable { onClick?.invoke() },
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
            ) {
                if (value.isNullOrEmpty()) {
                    Text(
                        modifier = Modifier.padding(vertical = FDS.Sizer.Gap.gap8),
                        text = title ?: "",
                        color = FDS.Colors.characterSecondary,
                        style = FDS.Typography.bodyB2Emphasized,
                    )
                } else {
                    Text(text = title ?: "", color = titleColor, style = titleStyle)

                    Text(text = value, color = valueColor, style = valueStyle)
                }
            }

            icon?.let {
                Spacer(modifier = Modifier.width(FDS.Sizer.Padding.padding16))
                Image(
                    painter = painterResource(icon),
                    contentDescription = null,
                )
            }
        }

        if (isShowLine) {
            FoundationDivider(modifier = Modifier.padding(top = FDS.Sizer.Gap.gap8))
        }
    }
}

@Preview
@Composable
fun FoundationCategorySectionPreview() {
    FoundationCategorySection(
        title = "Số tài khoản",
        value = "12345678987654",
    )
}