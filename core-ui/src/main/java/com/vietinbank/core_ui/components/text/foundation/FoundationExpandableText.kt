package com.vietinbank.core_ui.components.text.foundation

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Foundation Expandable Text Component
 *
 * Text component that can expand/collapse when content exceeds specified lines.
 * Shows "Xem thêm" when collapsed and "Thu gọn" when expanded.
 *
 * @param text The text to display
 * @param modifier Modifier for the component
 * @param collapsedMaxLines Maximum lines to show when collapsed
 * @param expandText Text to show for expanding (default: "Xem thêm")
 * @param collapseText Text to show for collapsing (default: "Thu gọn")
 * @param style Text style from FDS.Typography (default: bodyB2)
 * @param color Text color from FDS.Colors (default: textPrimary)
 * @param expandTextColor Color for expand/collapse text (default: textLink)
 * @param textAlign Text alignment
 * @param clickTimeWindow Time window for double-click protection
 * @param showExpandTextInline Whether to show expand text inline with content
 */
@Composable
fun FoundationExpandableText(
    text: String,
    modifier: Modifier = Modifier,
    collapsedMaxLines: Int = 3,
    expandText: String = "Xem thêm",
    collapseText: String = "Thu gọn",
    style: TextStyle = FDS.Typography.bodyB2,
    color: Color = FDS.Colors.textPrimary,
    expandTextColor: Color = FDS.Colors.textLink,
    textAlign: TextAlign? = null,
    clickTimeWindow: Long = 1000L,
    showExpandTextInline: Boolean = true,
) {
    var isExpanded by remember { mutableStateOf(false) }
    var hasOverflow by remember { mutableStateOf(false) }
    var textLayoutResult by remember { mutableStateOf<TextLayoutResult?>(null) }

    Column(
        modifier = modifier.animateContentSize(),
    ) {
        if (showExpandTextInline && !isExpanded && hasOverflow) {
            // Show text with inline "Xem thêm"
            val displayText = buildAnnotatedString {
                // Calculate how much text fits in collapsed lines
                val lastLineEndIndex = textLayoutResult?.let { result ->
                    if (result.lineCount > collapsedMaxLines) {
                        // Get the end index of the last visible line
                        val lastVisibleLine = collapsedMaxLines - 1
                        val lineEnd = result.getLineEnd(lastVisibleLine)
                        // Leave room for "... Xem thêm"
                        val expandTextLength = "... $expandText".length
                        (lineEnd - expandTextLength).coerceAtLeast(0)
                    } else {
                        text.length
                    }
                } ?: text.length

                // Add the truncated text
                append(text.substring(0, lastLineEndIndex.coerceAtMost(text.length)))

                if (hasOverflow) {
                    append("... ")
                    withStyle(
                        style = style.toSpanStyle().copy(
                            color = expandTextColor,
                            textDecoration = TextDecoration.Underline,
                        ),
                    ) {
                        append(expandText)
                    }
                }
            }

            Text(
                text = displayText,
                modifier = Modifier.safeClickable(
                    timeWindow = clickTimeWindow,
                    onSafeClick = { isExpanded = true },
                ),
                style = style,
                color = color,
                textAlign = textAlign,
                maxLines = collapsedMaxLines,
                overflow = TextOverflow.Ellipsis,
            )
        } else {
            // Regular text display
            Text(
                text = text,
                modifier = Modifier,
                style = style,
                color = color,
                textAlign = textAlign,
                maxLines = if (isExpanded) Int.MAX_VALUE else collapsedMaxLines,
                overflow = TextOverflow.Ellipsis,
                onTextLayout = { result ->
                    textLayoutResult = result
                    hasOverflow = result.hasVisualOverflow || result.lineCount > collapsedMaxLines
                },
            )

            // Show expand/collapse button below text when not inline
            if (!showExpandTextInline && hasOverflow) {
                Row(
                    modifier = Modifier.safeClickable(
                        timeWindow = clickTimeWindow,
                        onSafeClick = { isExpanded = !isExpanded },
                    ),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap4))
                    Text(
                        text = if (isExpanded) collapseText else expandText,
                        style = FDS.Typography.interactionLink,
                        color = expandTextColor,
                        textDecoration = TextDecoration.Underline,
                    )
                }
            }
        }

        // Show collapse option when expanded
        if (isExpanded && hasOverflow) {
            Row(
                modifier = Modifier.safeClickable(
                    timeWindow = clickTimeWindow,
                    onSafeClick = { isExpanded = false },
                ),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap4))
                Text(
                    text = collapseText,
                    style = FDS.Typography.interactionLink,
                    color = expandTextColor,
                    textDecoration = TextDecoration.Underline,
                )
            }
        }
    }
}