package com.vietinbank.core_ui.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.withTransform
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.theme.AppEffects
import com.vietinbank.core_ui.utils.ellipticalGradientBackground
import com.vietinbank.core_ui.utils.gradientBorder
import com.vietinbank.core_ui.utils.innerShadow
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun FoundationProgressBar(
    modifier: Modifier = Modifier,
    progress: Float = 0f,
) {
    val density = LocalDensity.current
    val cornerRadiusPx =
        with(density) { dimensionResource(R.dimen.foundation_radius_default).toPx() }
    val backgroundColor = AppColors.white
    val (startProgress, endProgress) = Pair(Color(0xFF005993), Color(0xFF8FD9FF))
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(FDS.Sizer.Gap.gap12)
            .gradientBorder()
            .clip(RoundedCornerShape(cornerRadiusPx))
            .innerShadow(
                shape = CircleShape,
                color = Color.White.copy(0.56f),
                offsetY = (-2).dp,
            )
            .innerShadow(
                shape = CircleShape,
                color = Color.White.copy(0.56f),
                offsetY = (2).dp,
            )
            .ellipticalGradientBackground(
                colors = listOf(
                    FDS.Colors.foundationDarkButtonPressedPrimary.copy(alpha = 0.5F),
                    Color.White.copy(alpha = 0.5F),
                ),
                radiusFraction = 1f,
            ),
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth(progress)
                .height(FDS.Sizer.Gap.gap12)
                .shadow(
                    elevation = AppEffects.elevationXs,
                    spotColor = AppColors.shadowSm, // 10% opacity black from Foundation
                    ambientColor = AppColors.shadowSm, // 10% opacity black from Foundation
                    shape = RoundedCornerShape(dimensionResource(R.dimen.foundation_radius_default)),
                    clip = false,
                )
                .clip(RoundedCornerShape(dimensionResource(R.dimen.foundation_radius_default)))
                .drawBehind {
                    drawRoundRect(
                        color = backgroundColor,
                        cornerRadius = CornerRadius(cornerRadiusPx),
                        size = this.size,
                    )
                    val center = Offset(this.size.width / 2f, this.size.height)
                    val radius = this.size.height
                    val gradient = Brush.radialGradient(
                        0f to startProgress,
                        1f to endProgress,
                        center = center,
                        radius = radius * 1f,
                    )

                    // Scale horizontally
                    val scaleX = this.size.width / this.size.height
                    withTransform({
                        scale(
                            scaleX = scaleX,
                            scaleY = 1f,
                            pivot = center,
                        )
                    }) {
                        drawRoundRect(
                            brush = gradient,
                            cornerRadius = CornerRadius(cornerRadiusPx),
                        )
                    }
                },
        )
    }
}

@Preview
@Composable
fun FoundationProgressBarPreview() {
    FoundationProgressBar()
}
