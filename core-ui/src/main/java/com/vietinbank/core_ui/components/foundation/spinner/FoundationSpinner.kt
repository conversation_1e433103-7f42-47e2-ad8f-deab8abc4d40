package com.vietinbank.core_ui.components.foundation.spinner

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Foundation Spinner Component
 *
 * A dropdown selector following the Foundation Design System.
 *
 * Features two states:
 * - Default: Shows placeholder text only
 * - Selected: Shows label above and selected value below
 *
 * @param items List of items to display in the dropdown
 * @param selectedItem Currently selected item (null for default state)
 * @param onItemSelected Callback when an item is selected
 * @param modifier Modifier for the component
 * @param label Label text (shows as placeholder when nothing selected, moves above when selected)
 * @param enabled Whether the spinner is enabled
 * @param dropdownIcon Custom dropdown icon resource (optional)
 */
@Composable
fun <T> FoundationSpinner(
    modifier: Modifier = Modifier,
    items: List<T>,
    selectedItem: T? = null,
    onItemSelected: (T) -> Unit,
    label: String = "",
    enabled: Boolean = true,
    itemContent: @Composable (T) -> Unit = { item ->
        Text(
            text = item.toString(),
            style = FDS.Typography.bodyB1,
            color = FDS.Colors.characterPrimary,
        )
    },
    dropdownIcon: Int? = null,
) {
    var expanded by remember { mutableStateOf(false) }
    val hasSelection = selectedItem != null

    // Rotation animation for dropdown icon
    val rotation by animateFloatAsState(
        targetValue = if (expanded) 180f else 0f,
        label = "dropdown_rotation",
    )

    Box(modifier = modifier) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .safeClickable(
                    enabled = enabled,
                    onSafeClick = { expanded = !expanded },
                )
                .padding(vertical = FDS.Sizer.Padding.padding8),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // Content section
            Column(
                modifier = Modifier.weight(1f),
            ) {
                if (hasSelection) {
                    // Selected state - show label on top
                    Text(
                        text = label,
                        style = FDS.Typography.captionCaptionL,
                        color = FDS.Colors.characterSecondary,
                        modifier = Modifier.padding(bottom = FDS.Sizer.Gap.gap2),
                    )

                    // Selected value
                    Text(
                        text = selectedItem.toString(),
                        style = FDS.Typography.bodyB1,
                        color = FDS.Colors.characterHighlighted,
                    )
                } else {
                    // Default state - show placeholder
                    Text(
                        text = label,
                        style = FDS.Typography.bodyB2Emphasized,
                        color = FDS.Colors.characterSecondary,
                    )
                }
            }

            // Dropdown icon
            Icon(
                painter = painterResource(
                    id = dropdownIcon ?: R.drawable.ic_drop_down,
                ),
                contentDescription = if (expanded) "Collapse" else "Expand",
                tint = FDS.Colors.characterSecondary,
                modifier = Modifier
                    .size(FDS.Sizer.Icon.icon24)
                    .rotate(rotation),
            )
        }

        // Dropdown menu
        DropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false },
            modifier = Modifier.fillMaxWidth(),
        ) {
            items.forEach { item ->
                DropdownMenuItem(
                    text = { itemContent(item) },
                    onClick = {
                        onItemSelected(item)
                        expanded = false
                    },
                    modifier = Modifier.fillMaxWidth(),
                )
            }
        }
    }
}

/**
 * Data class for spinner items with display text
 */
data class SpinnerItem(
    val id: String,
    val displayText: String,
    val value: Any? = null,
) {
    override fun toString(): String = displayText
}

@Preview(showBackground = true)
@Composable
private fun FoundationSpinnerPreview() {
    AppTheme {
        val items = listOf(
            SpinnerItem("all", "Tất cả"),
            SpinnerItem("savings", "Tiết kiệm"),
            SpinnerItem("checking", "Thanh toán"),
            SpinnerItem("credit", "Tín dụng"),
        )

        var selectedItem by remember { mutableStateOf<SpinnerItem?>(null) }

        Column(
            modifier = Modifier.padding(FDS.Sizer.Padding.padding16),
        ) {
            // Default state
            FoundationSpinner(
                items = items,
                selectedItem = null,
                onItemSelected = { },
                label = "Từ tài khoản",
                modifier = Modifier.fillMaxWidth(),
            )

            Box(modifier = Modifier.padding(vertical = FDS.Sizer.Padding.padding16))

            // Selected state
            FoundationSpinner(
                items = items,
                selectedItem = items[0],
                onItemSelected = { },
                label = "Từ tài khoản",
                modifier = Modifier.fillMaxWidth(),
            )

            Box(modifier = Modifier.padding(vertical = FDS.Sizer.Padding.padding16))

            // Interactive example
            FoundationSpinner(
                items = items,
                selectedItem = selectedItem,
                onItemSelected = { selectedItem = it },
                label = "Chọn loại tài khoản",
                modifier = Modifier.fillMaxWidth(),
            )
        }
    }
}

@Preview(showBackground = true, name = "Spinner States")
@Composable
private fun FoundationSpinnerStatesPreview() {
    AppTheme {
        Column(
            modifier = Modifier.padding(FDS.Sizer.Padding.padding24),
        ) {
            Text(
                text = "Default State",
                style = FDS.Typography.headingH3,
                color = FDS.Colors.characterPrimary,
                modifier = Modifier.padding(bottom = FDS.Sizer.Padding.padding8),
            )

            FoundationSpinner(
                items = listOf("Option 1", "Option 2", "Option 3"),
                selectedItem = null,
                onItemSelected = { },
                label = "Từ tài khoản",
                modifier = Modifier.fillMaxWidth(),
            )

            Box(modifier = Modifier.padding(vertical = FDS.Sizer.Padding.padding24))

            Text(
                text = "Selected State",
                style = FDS.Typography.headingH3,
                color = FDS.Colors.characterPrimary,
                modifier = Modifier.padding(bottom = FDS.Sizer.Padding.padding8),
            )

            FoundationSpinner(
                items = listOf("Tất cả", "Tiết kiệm", "Thanh toán"),
                selectedItem = "Tất cả",
                onItemSelected = { },
                label = "Từ tài khoản",
                modifier = Modifier.fillMaxWidth(),
            )
        }
    }
}