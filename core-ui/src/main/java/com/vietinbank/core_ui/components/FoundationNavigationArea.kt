package com.vietinbank.core_ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * FoundationNavigationArea
 *
 * Full‑width bottom scrim area with rounded top corners, intended to host
 * floating navigation controls (e.g. bottom action buttons). The content is
 * placed on top, aligned to the bottom center by default.
 */
@Composable
fun FoundationNavigationArea(
    modifier: Modifier = Modifier,
    height: Dp = 136.dp,
    topRadius: Dp = 16.dp,
    baseColor: Color = FDS.Colors.backgroundDarkBlue,
    startStop: Float = 0.5f,
    content: @Composable BoxScope.() -> Unit,
) {
    val clampedStart = startStop.coerceIn(0f, 1f)

    Box(modifier = modifier.fillMaxWidth()) {
        // Background scrim
        Box(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth()
                .height(height)
                .clip(RoundedCornerShape(topStart = topRadius, topEnd = topRadius))
                .background(
                    brush = Brush.verticalGradient(
                        colorStops = arrayOf(
                            0.0f to Color.Transparent,
                            clampedStart to baseColor,
                            1.0f to baseColor,
                        ),
                    ),
                ),
        )

        // Foreground content
        Box(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth(),
            content = content,
        )
    }
}
