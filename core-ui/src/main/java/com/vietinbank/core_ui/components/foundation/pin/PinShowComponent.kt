package com.vietinbank.core_ui.components.foundation.pin

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * PIN Show state enum for PinShowComponent
 */
enum class PinShowState {
    TYPED, // All digits entered
    TYPING, // Currently entering digits
    RESEND, // Ready to resend OTP
    EXPIRED, // OTP has expired
    VERIFYING, // Verifying OTP
}

/**
 * PIN Show Component - A stateless UI component for displaying OTP/PIN
 *
 * This component displays OTP/PIN digits in visible boxes only (no dots).
 * It supports different visual states and includes animations for digit entry.
 *
 * Features:
 * - Always shows boxes (no hidden mode)
 * - 4 visual states: TYPED, TYPING, RESEND, EXPIRED
 * - Pop-in animation when digits appear
 * - Color transitions between states
 * - Responsive layout for narrow screens
 * - Accessibility support with content descriptions
 *
 * Important: This component does NOT render timer text or resend messages.
 * Those should be handled by the parent screen/component.
 *
 * @param state Current PIN show state (TYPED, TYPING, RESEND, EXPIRED)
 * @param pinValues List of PIN values (e.g., ["1","6","_","_","_","_"])
 * @param onRowTap Optional callback when PIN row is tapped (for focus management)
 * @param modifier Optional modifier for the component
 *
 * Usage example:
 * ```kotlin
 * var showState by remember { mutableStateOf(PinShowState.TYPING) }
 * val pinValues = remember { listOf("1", "6", "_", "_", "_", "_") }
 *
 * PinShowComponent(
 *     state = showState,
 *     pinValues = pinValues
 * )
 *
 * // Parent handles timer and messages
 * Text("OTP có hiệu lực trong 04:30")
 * ```
 */
@Composable
fun PinShowComponent(
    state: PinShowState,
    pinValues: List<String>,
    onRowTap: () -> Unit = {},
    modifier: Modifier = Modifier,
) {
    // Guard against size changes and reinitialize if needed
    val currentSize = pinValues.size
    val previousValues = remember(currentSize) {
        mutableStateListOf(*pinValues.toTypedArray())
    }
    val animationScales = remember(currentSize) {
        mutableStateListOf(*Array(currentSize) { 1f })
    }

    // Detect digit changes for pop-in animation
    LaunchedEffect(pinValues) {
        pinValues.forEachIndexed { index, value ->
            if (previousValues.getOrNull(index) == "_" && value != "_") {
                // New digit appeared, trigger pop-in animation
                animationScales[index] = 0.8f
                kotlinx.coroutines.delay(50)
                animationScales[index] = 1.0f
            }
        }
        previousValues.clear()
        previousValues.addAll(pinValues)
    }

    BoxWithConstraints(
        modifier = modifier
            .fillMaxWidth()
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null,
                onClick = onRowTap,
            )
            .padding(vertical = FDS.Sizer.Padding.padding8),
        contentAlignment = Alignment.Center,
    ) {
        val isNarrow = maxWidth < OtpCellConstants.MIN_CONTAINER_WIDTH

        PinShowRow(
            state = state,
            pinValues = pinValues,
            animationScales = animationScales,
            isNarrow = isNarrow,
        )
    }
}

/**
 * Row of visible PIN boxes for PinShowComponent
 */
@Composable
private fun PinShowRow(
    state: PinShowState,
    pinValues: List<String>,
    animationScales: List<Float>,
    isNarrow: Boolean,
) {
    // Determine colors based on state
    val (backgroundColor, textColor) = when (state) {
        PinShowState.TYPED, PinShowState.TYPING, PinShowState.RESEND, PinShowState.VERIFYING -> {
            FDS.Colors.blue100 to FDS.Colors.characterHighlighted
        }
        PinShowState.EXPIRED -> {
            FDS.Colors.stateWarningLighter to FDS.Colors.stateWarning
        }
    }

    // Animate color transitions
    val animatedBgColor by animateColorAsState(
        targetValue = backgroundColor,
        animationSpec = tween(300),
        label = "BackgroundColorAnimation",
    )

    val animatedTextColor by animateColorAsState(
        targetValue = textColor,
        animationSpec = tween(300),
        label = "TextColorAnimation",
    )

    // Create animated boxes with pop-in effect (secure content description)
    Box(
        modifier = Modifier.semantics {
            contentDescription = when (state) {
                PinShowState.TYPED -> "OTP đã nhập đầy đủ" // Don't expose actual OTP
                PinShowState.TYPING -> "Đang nhập OTP"
                PinShowState.RESEND -> "Có thể gửi lại OTP"
                PinShowState.EXPIRED -> "OTP đã hết hạn"
                PinShowState.VERIFYING -> "Đang xác thực OTP"
            }
        },
    ) {
        AnimatedPinBoxes(
            pinValues = pinValues,
            animationScales = animationScales,
            backgroundColor = animatedBgColor,
            textColor = animatedTextColor,
            isNarrow = isNarrow,
        )
    }
}

/**
 * Animated PIN boxes with individual scale animations
 */
@Composable
private fun AnimatedPinBoxes(
    pinValues: List<String>,
    animationScales: List<Float>,
    backgroundColor: Color,
    textColor: Color,
    isNarrow: Boolean,
) {
    val spacing = if (isNarrow) {
        OtpCellConstants.BOX_SPACING_NARROW
    } else {
        OtpCellConstants.BOX_SPACING
    }

    // Use weight-based distribution within a constrained width
    androidx.compose.foundation.layout.Row(
        modifier = Modifier
            .widthIn(max = OtpCellConstants.PIN_SHOW_MAX_WIDTH) // Constrain max width
            .fillMaxWidth(), // Fill up to max width
        horizontalArrangement = androidx.compose.foundation.layout.Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        pinValues.forEachIndexed { index, value ->
            // Animate scale for each box
            val scale by animateFloatAsState(
                targetValue = animationScales.getOrElse(index) { 1f },
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioMediumBouncy,
                    stiffness = 200f,
                ),
                label = "BoxScaleAnimation_$index",
            )

            Box(
                modifier = Modifier
                    .weight(1f) // Even distribution
                    .aspectRatio(OtpCellConstants.BOX_ASPECT_RATIO) // Maintain aspect ratio
                    .scale(scale)
                    .semantics {
                        contentDescription = if (value == "_") {
                            "Ô số thứ ${index + 1}, chưa nhập"
                        } else {
                            "Ô số thứ ${index + 1}, giá trị $value"
                        }
                    },
            ) {
                OtpNumberBox(
                    text = value,
                    backgroundColor = backgroundColor,
                    textColor = textColor,
                    modifier = Modifier.fillMaxSize(),
                )
            }

            // Add spacing between cells (except for the last one)
            if (index < pinValues.size - 1) {
                androidx.compose.foundation.layout.Spacer(
                    modifier = Modifier.width(spacing),
                )
            }
        }
    }
}

/**
 * Extension function to create a PIN value list from a string
 * Useful for converting "165" to ["1", "6", "5", "_", "_", "_"]
 */
fun String.toPinValues(length: Int = 6): List<String> {
    // Guard input
    val safeString = this.take(length)
    return List(length) { i ->
        if (i < safeString.length) safeString[i].toString() else "_"
    }
}

/**
 * Private constants for animations
 */
private object PinShowConstants {
    const val POP_IN_SCALE_START = 0.8f
    const val POP_IN_DELAY_MS = 50
    const val COLOR_ANIMATION_DURATION = 300
    const val SPRING_DAMPING = 0.6f
    const val SPRING_STIFFNESS = 200f
}