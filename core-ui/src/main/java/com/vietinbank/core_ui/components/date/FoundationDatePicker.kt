package com.vietinbank.core_ui.components.date

import androidx.compose.material3.DatePickerDialog
import androidx.compose.material3.DateRangePicker
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.SelectableDates
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.rememberDateRangePickerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_common.extensions.dd_MM_yyyy_1
import com.vietinbank.core_common.extensions.toDateString
import com.vietinbank.core_common.extensions.toTimeInMillis
import com.vietinbank.core_ui.R

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FoundationDateRangePicker(
    startDate: String? = null,
    endDate: String? = null,
    rangeValidate: Int = 0,
    onDismiss: () -> Unit,
    onSelected: (String?, String?) -> Unit,
) {
    val todayTimeMillis = remember { System.currentTimeMillis() }
    val initStart = remember(startDate) { startDate.toTimeInMillis(dd_MM_yyyy_1) }
    val initEnd = remember(endDate) { endDate.toTimeInMillis(dd_MM_yyyy_1) }
    var endLimitTimeMillis by remember { mutableLongStateOf(todayTimeMillis) }
    val selectableDates = remember(endLimitTimeMillis) {
        object : SelectableDates {
            override fun isSelectableDate(utcTimeMillis: Long): Boolean {
                return if (endLimitTimeMillis <= todayTimeMillis) {
                    utcTimeMillis <= endLimitTimeMillis
                } else {
                    utcTimeMillis <= todayTimeMillis
                }
            }
        }
    }

    val dateRangePickerState = rememberDateRangePickerState(
        initialSelectedStartDateMillis = initStart,
        initialSelectedEndDateMillis = initEnd,
        selectableDates = selectableDates,
    )

    LaunchedEffect(
        dateRangePickerState.selectedStartDateMillis,
        dateRangePickerState.selectedEndDateMillis,
    ) {
        val currentStart = dateRangePickerState.selectedStartDateMillis
        val currentEnd = dateRangePickerState.selectedEndDateMillis
        when {
            // lần đầu => luôn chọn ngày bắt đầu
            // user chọn Start Date -> cập nhật giới hạn End Date
            currentStart != null && currentEnd == null -> {
                endLimitTimeMillis = currentStart.plus(rangeValidate * 86_400_000L)
            }
            // chọn range khong phải range vừa chọn => tự dọng dismiss dialog
            currentStart != null && currentEnd != null && (currentStart != initStart && currentEnd != initEnd) -> {
                endLimitTimeMillis = todayTimeMillis
                onDismiss()
                onSelected(
                    currentStart.toDateString(),
                    currentEnd.toDateString(),
                )
            }

            else -> {
            }
        }
    }

    DatePickerDialog(onDismissRequest = {
        onDismiss()
    }, confirmButton = {
        TextButton(onClick = { onDismiss() }) {
            Text(text = stringResource(R.string.common_cancel))
        }
    }) {
        DateRangePicker(
            state = dateRangePickerState,
            showModeToggle = false,
        )
    }
}