package com.vietinbank.core_ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil3.Bitmap
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.safeClickable

@Composable
fun FoundationAvatar(
    modifier: Modifier = Modifier,
    avatar: Bitmap? = null,
    name: String? = null,
) {
    Box(
        modifier = modifier
            .size(FoundationDesignSystem.Sizer.Icon.icon48)
            .clip(CircleShape)
            .background(FoundationDesignSystem.Colors.blue200),
        contentAlignment = Alignment.Center,
    ) {
        if (avatar != null) {
            Image(
                contentScale = ContentScale.FillBounds,
                painter = painterResource(R.drawable.background),
                contentDescription = null,
            )
        } else if (!name.isNullOrEmpty()) {
            Text(
                text = name,
                color = FoundationDesignSystem.Colors.characterHighlighted,
                style = FoundationDesignSystem.Typography.bodyB2Emphasized,
            )
        } else {
            Image(
                contentScale = ContentScale.FillBounds,
                painter = painterResource(R.drawable.ic_commom_avatar_none_64),
                contentDescription = "None",
            )
        }
    }
}

@Composable
fun FoundationStatus(
    modifier: Modifier = Modifier,
    statusMessage: String? = null,
    statusCode: Status = Status.Pending,
) {
    Box(
        modifier = modifier
            .background(
                when (statusCode) {
                    Status.Success -> FoundationDesignSystem.Colors.stateSuccess
                    Status.MakerTransferSuccess -> FoundationDesignSystem.Colors.stateSuccess
                    Status.Fail -> FoundationDesignSystem.Colors.stateError
                    else -> FoundationDesignSystem.Colors.stateWarning
                },
                RoundedCornerShape(FoundationDesignSystem.Sizer.Radius.radius32),
            )
            .padding(
                FoundationDesignSystem.Sizer.Radius.radius4,
                FoundationDesignSystem.Sizer.Radius.radius4,
                FoundationDesignSystem.Sizer.Radius.radius8,
                FoundationDesignSystem.Sizer.Radius.radius4,
            ),
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(FoundationDesignSystem.Sizer.Padding.padding4),
        ) {
            Image(
                painter = painterResource(
                    when (statusCode) {
                        Status.Success -> R.drawable.ic_commom_success_24
                        Status.MakerTransferSuccess -> R.drawable.ic_commom_success_24
                        Status.Fail -> R.drawable.ic_commom_fail_24
                        else -> R.drawable.ic_commom_pending_24
                    },
                ),
                contentDescription = null,
            )

            Text(
                text = statusMessage ?: when (statusCode) {
                    Status.Success -> stringResource(R.string.feature_checker_need_approved)
                    Status.MakerTransferSuccess -> stringResource(R.string.feature_maker_transfer_success)
                    Status.Fail -> stringResource(R.string.feature_checker_need_rejected)
                    else -> stringResource(R.string.feature_checker_need_pending)
                },
                color = when (statusCode) {
                    Status.Success -> FoundationDesignSystem.Colors.stateSuccessLighter
                    Status.MakerTransferSuccess -> FoundationDesignSystem.Colors.stateSuccessLighter
                    Status.Fail -> FoundationDesignSystem.Colors.stateErrorLighter
                    else -> FoundationDesignSystem.Colors.stateWarningLighter
                },
                style = FoundationDesignSystem.Typography.captionCaptionLBold,
            )
        }
    }
}

@Composable
fun FoundationChips(
    modifier: Modifier = Modifier,
    drawable: Int? = null,
    text: String? = null,
    subChipSize: Int = 0,
    isSelector: Boolean = false,
    onClick: (() -> Unit) = {},
) {
    Row(
        modifier = modifier
            .height(40.dp)
            .background(
                if (isSelector) {
                    FoundationDesignSystem.Colors.blue100
                } else {
                    FoundationDesignSystem.Colors.blue50
                },
                RoundedCornerShape(FoundationDesignSystem.Sizer.Radius.radiusFull),
            )
            .border(
                FoundationDesignSystem.Sizer.Stroke.stroke1,
                if (isSelector) {
                    FoundationDesignSystem.Colors.blue600
                } else {
                    FoundationDesignSystem.Colors.blue50
                },
                RoundedCornerShape(FoundationDesignSystem.Sizer.Radius.radiusFull),
            )
            .padding(horizontal = FoundationDesignSystem.Sizer.Padding.padding16)
            .safeClickable {
                onClick()
            },
        verticalAlignment = Alignment.CenterVertically,
    ) {
        drawable?.let {
            Icon(
                painter = painterResource(it),
                contentDescription = null,
                tint = FoundationDesignSystem.Colors.characterHighlighted,
            )
            Spacer(modifier = Modifier.width(FoundationDesignSystem.Sizer.Padding.padding4))
        }

        Text(
            text = text ?: "",
            color = FoundationDesignSystem.Colors.characterHighlighted,
            style = FoundationDesignSystem.Typography.captionCaptionLBold,
        )

        if (subChipSize > 0) {
            Spacer(modifier = Modifier.width(FoundationDesignSystem.Sizer.Padding.padding4))
            Box(
                modifier = Modifier
                    .size(FoundationDesignSystem.Sizer.Icon.icon24)
                    .clip(CircleShape)
                    .background(FoundationDesignSystem.Colors.red400)
                    .padding(FoundationDesignSystem.Sizer.Padding.padding4),
                contentAlignment = Alignment.Center,
            ) {
                Text(
                    text = if (subChipSize > 100) {
                        "99+"
                    } else {
                        subChipSize.toString()
                    },
                    color = FoundationDesignSystem.Colors.characterInverse,
                    style = FoundationDesignSystem.Typography.captionCaptionMBold,
                )
            }
        }
    }
}

@Composable
fun FoundationSelector(
    modifier: Modifier = Modifier,
    boxType: SelectorType = SelectorType.Radio,
    title: String? = null,
    description: String? = null,
    isSelected: Boolean = false,
    isEnable: Boolean = true,
    onClick: (() -> Unit) = {},
) {
    Column(
        modifier = modifier.safeClickable {
            if (isEnable) {
                onClick()
            }
        },
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(FoundationDesignSystem.Sizer.Spacing.spacingSmall),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Image(
                modifier = Modifier,
                painter = painterResource(
                    when {
                        // radio
                        boxType == SelectorType.Radio && !isEnable -> R.drawable.ic_common_radio_uncheck_24
                        boxType == SelectorType.Radio && isSelected -> R.drawable.ic_common_radio_check_24
                        boxType == SelectorType.Radio && !isSelected -> R.drawable.ic_common_radio_uncheck_24
                        // switch
                        boxType == SelectorType.Switch && !isEnable -> R.drawable.ic_common_switch_off_36_20
                        boxType == SelectorType.Switch && isSelected -> R.drawable.ic_common_switch_on_36_20
                        boxType == SelectorType.Switch && !isSelected -> R.drawable.ic_common_switch_off_36_20
                        // checkbox
                        boxType == SelectorType.Checkbox && !isEnable -> R.drawable.ic_common_uncheckbox_20
                        boxType == SelectorType.Checkbox && isSelected -> R.drawable.ic_common_checkbox_20
                        boxType == SelectorType.Checkbox && !isSelected -> R.drawable.ic_common_uncheckbox_20
                        else -> R.drawable.ic_common_uncheckbox_20
                    },
                ),
                contentDescription = null,
            )

            Text(
                text = title ?: "",
                color = when {
                    isEnable -> FoundationDesignSystem.Colors.characterHighlighted
                    else -> FoundationDesignSystem.Colors.characterTertiary
                },
                style = FoundationDesignSystem.Typography.bodyB2Emphasized,
            )
        }

        description?.let {
            Text(
                modifier = Modifier.padding(start = FoundationDesignSystem.Sizer.Spacing.spacingXLarge),
                text = it,
                color = when {
                    isEnable -> FoundationDesignSystem.Colors.characterSecondary
                    else -> FoundationDesignSystem.Colors.characterTertiary
                },
                style = FoundationDesignSystem.Typography.captionCaptionL,
            )
        }
    }
}

@Composable
fun FoundationDivider(modifier: Modifier = Modifier) {
    Spacer(
        modifier = modifier
            .height(FoundationDesignSystem.Sizer.Stroke.stroke1)
            .fillMaxWidth()
            .background(FoundationDesignSystem.Colors.strokeDivider),
    )
}

enum class Status { Success, Fail, Pending, MakerTransferSuccess }

enum class SelectorType { Radio, Checkbox, Switch, }

@Preview
@Composable
fun AvatarPreview() {
    Row {
        FoundationAvatar(name = "AT")
        FoundationAvatar()
    }
}

@Preview
@Composable
fun EFastBaseStatusPreview() {
    Column {
        FoundationStatus(statusMessage = "Thành công", statusCode = Status.Success)
        FoundationStatus(modifier = Modifier.padding(top = 8.dp), statusCode = Status.Success)
        FoundationStatus(modifier = Modifier.padding(top = 8.dp), statusCode = Status.Fail)
        FoundationStatus(modifier = Modifier.padding(top = 8.dp), statusCode = Status.Pending)
    }
}

@Preview
@Composable
fun ChipsPreview() {
    Column {
        FoundationChips(text = "Hóa đơn", subChipSize = 0, isSelector = true)
        FoundationChips(
            modifier = Modifier.padding(top = 8.dp),
            text = "Hóa đơn",
            subChipSize = 10,
            isSelector = true,
        )
        FoundationChips(modifier = Modifier.padding(top = 8.dp), text = "Hóa đơn", subChipSize = 0)
        FoundationChips(modifier = Modifier.padding(top = 8.dp), text = "Hóa đơn", subChipSize = 10)
    }
}

@Preview
@Composable
fun SelectorBasePreview() {
    Column {
        FoundationSelector(
            boxType = SelectorType.Radio,
            title = "Title text goes here",
            isEnable = false,
        )
        Spacer(modifier = Modifier.height(8.dp))
        FoundationSelector(boxType = SelectorType.Radio, title = "Title text goes here")
        Spacer(modifier = Modifier.height(8.dp))
        FoundationSelector(
            boxType = SelectorType.Radio,
            title = "Title text goes here",
            isSelected = true,
        )
        Spacer(modifier = Modifier.height(8.dp))
        FoundationSelector(boxType = SelectorType.Checkbox, title = "Title text goes here")
        Spacer(modifier = Modifier.height(8.dp))
        FoundationSelector(
            boxType = SelectorType.Checkbox,
            title = "Title text goes here",
            isSelected = true,
        )
        Spacer(modifier = Modifier.height(8.dp))
        FoundationSelector(boxType = SelectorType.Switch, title = "Title text goes here")
        Spacer(modifier = Modifier.height(8.dp))
        FoundationSelector(
            boxType = SelectorType.Switch,
            title = "Title text goes here",
            isSelected = true,
        )
        Spacer(modifier = Modifier.height(8.dp))
        FoundationSelector(
            boxType = SelectorType.Radio,
            title = "Title text goes here",
            description = "Secondary text line goes here",
        )
        Spacer(modifier = Modifier.height(8.dp))
        FoundationSelector(
            boxType = SelectorType.Radio,
            title = "Title text goes here",
            description = "Secondary text line goes here",
            isSelected = true,
        )
    }
}

@Preview
@Composable
fun DividerPreview() {
    FoundationDivider()
}