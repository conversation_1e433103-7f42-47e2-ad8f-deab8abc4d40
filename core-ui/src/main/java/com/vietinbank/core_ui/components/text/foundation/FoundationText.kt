package com.vietinbank.core_ui.components.text.foundation

import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Foundation Text Component
 *
 * Base text component that integrates with Foundation Design System.
 * Provides consistent typography and color styling across the app.
 *
 * @param text The text to display
 * @param modifier Modifier for the text
 * @param style Text style from FDS.Typography (default: bodyB2)
 * @param color Text color from FDS.Colors (default: textPrimary)
 * @param textAlign Alignment of the text
 * @param overflow How to handle text overflow
 * @param softWrap Whether text should break at soft line breaks
 * @param maxLines Maximum number of lines
 * @param minLines Minimum number of lines
 * @param textDecoration Decoration to apply to text
 * @param onTextLayout Callback when text layout is calculated
 */
@Composable
fun FoundationText(
    text: String,
    modifier: Modifier = Modifier,
    style: TextStyle = FDS.Typography.bodyB2,
    color: Color = FDS.Colors.textPrimary,
    textAlign: TextAlign? = null,
    overflow: TextOverflow = TextOverflow.Clip,
    softWrap: Boolean = true,
    maxLines: Int = Int.MAX_VALUE,
    minLines: Int = 1,
    textDecoration: TextDecoration? = null,
    onTextLayout: ((TextLayoutResult) -> Unit)? = null,
) {
    Text(
        text = text,
        modifier = modifier,
        style = style.let { baseStyle ->
            if (textDecoration != null) {
                baseStyle.copy(textDecoration = textDecoration)
            } else {
                baseStyle
            }
        },
        color = color,
        textAlign = textAlign,
        overflow = overflow,
        softWrap = softWrap,
        maxLines = maxLines,
        minLines = minLines,
        onTextLayout = onTextLayout,
    )
}

/**
 * Foundation Text Component with AnnotatedString support
 *
 * Use this overload when you need to display rich text with multiple styles.
 *
 * @param text The annotated string to display
 * @param modifier Modifier for the text
 * @param style Base text style from FDS.Typography (default: bodyB2)
 * @param color Default text color from FDS.Colors (default: textPrimary)
 * @param textAlign Alignment of the text
 * @param overflow How to handle text overflow
 * @param softWrap Whether text should break at soft line breaks
 * @param maxLines Maximum number of lines
 * @param minLines Minimum number of lines
 * @param onTextLayout Callback when text layout is calculated
 */
@Composable
fun FoundationText(
    text: AnnotatedString,
    modifier: Modifier = Modifier,
    style: TextStyle = FDS.Typography.bodyB2,
    color: Color = FDS.Colors.textPrimary,
    textAlign: TextAlign? = null,
    overflow: TextOverflow = TextOverflow.Clip,
    softWrap: Boolean = true,
    maxLines: Int = Int.MAX_VALUE,
    minLines: Int = 1,
    onTextLayout: ((TextLayoutResult) -> Unit)? = null,
) {
    if (onTextLayout != null) {
        Text(
            text = text,
            modifier = modifier,
            style = style,
            color = color,
            textAlign = textAlign,
            overflow = overflow,
            softWrap = softWrap,
            maxLines = maxLines,
            onTextLayout = onTextLayout,
        )
    } else {
        Text(
            text = text,
            modifier = modifier,
            style = style,
            color = color,
            textAlign = textAlign,
            overflow = overflow,
            softWrap = softWrap,
            maxLines = maxLines,
        )
    }
}