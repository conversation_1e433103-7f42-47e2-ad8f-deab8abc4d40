package com.vietinbank.core_ui.components.foundation.pin

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * PIN Authentication Dialog Step States
 * Represents the two main states of the PIN authentication flow:
 * 1. InputPin - User enters their PIN (hidden/visible toggle)
 * 2. DisplayOtp - Shows the OTP code with timer and resend functionality
 */
sealed class PinAuthStep {

    /**
     * PIN Input State
     * User enters their PIN with option to show/hide digits
     * Matches Figma design: https://www.figma.com/design/ShNHImLlXTtnDpJz3fcqB6/eFAST---Design-Review--19-08-?node-id=5208-28163
     */
    data class InputPin(
        val digits: String = "",
        val showNumbers: Boolean = false,
        val state: PinState = PinState.INPUT,
        val length: Int = 6,
        val isVerifying: Boolean = false,
        val attemptsLeft: Int? = null,
        val errorMessage: String? = null,
    ) : PinAuthStep()

    /**
     * OTP Display State
     * Shows generated OTP with validity timer and resend functionality
     * Matches Figma design: https://www.figma.com/design/ShNHImLlXTtnDpJz3fcqB6/eFAST---Design-Review--19-08-?node-id=5208-28178
     */
    data class DisplayOtp(
        val state: PinShowState = PinShowState.TYPING,
        val pinValues: List<String> = emptyList(),
        val validitySeconds: Int = 270, // 4:30 default
        val currentValidityRemaining: Int = 270,
        val canResend: Boolean = false,
        val resendCooldown: Int = 60, // 60 seconds cooldown
        val currentResendRemaining: Int = 60,
        val isResending: Boolean = false,
    ) : PinAuthStep()
}

/**
 * Result from PIN Authentication Dialog
 */
@Parcelize
data class PinAuthResult(
    val isSuccess: Boolean,
    val otpCode: String? = null,
    val pinCode: String? = null,
    val transactionId: String? = null,
    val timestamp: Long = System.currentTimeMillis(),
    val verificationType: PinVerificationType = PinVerificationType.PIN_AND_OTP,
) : Parcelable

/**
 * Verification types for PIN authentication
 */
enum class PinVerificationType {
    PIN_ONLY, // Only PIN was verified
    OTP_ONLY, // Only OTP was verified
    PIN_AND_OTP, // Both PIN and OTP were verified
}

/**
 * PIN Authentication Dialog Actions
 * User interactions that can occur in the dialog
 */
sealed class PinAuthAction {
    // Input state actions
    object OnBackPressed : PinAuthAction()
    object OnContinue : PinAuthAction()
    object OnToggleVisibility : PinAuthAction()
    data class OnPinChanged(val pin: String) : PinAuthAction()
    object OnForgotPin : PinAuthAction()

    // Display state actions
    object OnResendOtp : PinAuthAction()
    data class OnOtpChanged(val otp: String) : PinAuthAction()
    object OnTimerExpired : PinAuthAction()

    // Common actions
    object OnDismiss : PinAuthAction()
}

/**
 * Constants for PIN Authentication Dialog
 */
object PinAuthConstants {
    const val DIALOG_TAG = "pin_auth_dialog"
    const val RESULT_KEY = "pin_auth_result"
    const val MAX_WIDTH_DP = 400 // For tablet support
    const val DEFAULT_PIN_LENGTH = 6
    const val DEFAULT_OTP_LENGTH = 6
    const val DEFAULT_OTP_VALIDITY = 270 // 4:30 in seconds
    const val DEFAULT_RESEND_COOLDOWN = 60 // 60 seconds
    const val MAX_PIN_ATTEMPTS = 3

    // Animation durations
    const val ENTER_DURATION_MS = 220
    const val EXIT_DURATION_MS = 180
    const val STATE_TRANSITION_MS = 250
}

/**
 * Helper extension functions
 */

/**
 * Converts a string of digits to a list of PIN values for display
 * Example: "165" -> ["1", "6", "5", "_", "_", "_"] for length 6
 */
fun String.toPinValuesList(length: Int): List<String> {
    val safeString = this.take(length)
    return List(length) { index ->
        if (index < safeString.length) safeString[index].toString() else "_"
    }
}

/**
 * Formats seconds to MM:SS display format
 * Example: 270 -> "04:30"
 */
fun Int.toTimerFormat(): String {
    val minutes = this / 60
    val seconds = this % 60
    return String.format("%02d:%02d", minutes, seconds)
}