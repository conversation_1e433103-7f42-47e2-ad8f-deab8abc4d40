package com.vietinbank.core_ui.components.common

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Created by vandz on 20/8/25.
 */
@Composable
fun BoxModalContainer(
    modifier: Modifier = Modifier,
    shape: Shape? = null,
    background: Color = FDS.Colors.backgroundBgContainer,
    border: BorderStroke? = null,
    shadowElevation: Dp = 0.dp,
    contentPadding: PaddingValues = PaddingValues(
        horizontal = FDS.Sizer.Padding.padding16,
        vertical = FDS.Sizer.Padding.padding12,
    ),
    content: @Composable BoxScope.() -> Unit,
) {
    Surface(
        modifier = modifier,
        shape = shape ?: RectangleShape,
        color = background,
        border = border,
        shadowElevation = shadowElevation,
    ) {
        Column(Modifier.padding(contentPadding)) {
            Box(Modifier.fillMaxWidth()) {
                // Cấp BoxScope cho content
                content.invoke(this)
            }
        }
    }
}

@Composable
fun ColumnModalContainer(
    modifier: Modifier = Modifier,
    shape: Shape? = null,
    background: Color = FDS.Colors.backgroundBgContainer,
    border: BorderStroke? = null,
    shadowElevation: Dp = 0.dp,
    contentPadding: PaddingValues = PaddingValues(
        horizontal = FDS.Sizer.Padding.padding16,
        vertical = FDS.Sizer.Padding.padding12,
    ),
    content: @Composable ColumnScope.() -> Unit, // <-- overload ColumnScope
) {
    Surface(
        modifier = modifier,
        shape = shape ?: RectangleShape,
        color = background,
        border = border,
        shadowElevation = shadowElevation,
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(contentPadding),
            content = content,
        )
    }
}