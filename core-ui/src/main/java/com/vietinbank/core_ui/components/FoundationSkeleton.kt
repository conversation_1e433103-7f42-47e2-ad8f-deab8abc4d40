package com.vietinbank.core_ui.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.unit.Dp
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Foundation skeleton components following Figma design system.
 * Used for loading states with proper shimmer animation.
 */

/**
 * Rectangular skeleton placeholder with shimmer effect.
 * Used for text placeholders.
 */
@Composable
fun FoundationSkeletonRectangle(
    modifier: Modifier = Modifier,
    width: Dp? = null,
    height: Dp = FDS.Sizer.Icon.icon16,
    cornerRadius: Dp = FDS.Sizer.Radius.radius4,
) {
    Box(
        modifier = modifier
            .let { if (width != null) it.width(width) else it }
            .height(height)
            .clip(RoundedCornerShape(cornerRadius))
            .shimmer(),
    )
}

/**
 * Circular skeleton placeholder with shimmer effect.
 * Used for avatar placeholders.
 */
@Composable
fun FoundationSkeletonCircle(
    modifier: Modifier = Modifier,
    size: Dp = FDS.Sizer.Icon.icon32,
) {
    Box(
        modifier = modifier
            .size(size)
            .clip(CircleShape)
            .shimmer(),
    )
}

/**
 * Complete skeleton item matching Figma design (2455-395).
 * Shows circular avatar + two lines of text with proper spacing.
 */
@Composable
fun FoundationSkeletonListItem(
    modifier: Modifier = Modifier,
    avatarSize: Dp = FDS.Sizer.Icon.icon32,
    firstLineWidthFraction: Float = 0.8f,
    secondLineWidthFraction: Float = 0.5f,
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap12),
    ) {
        // Circular avatar placeholder
        FoundationSkeletonCircle(size = avatarSize)

        // Text content
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap4),
        ) {
            // First line - longer
            FoundationSkeletonRectangle(
                modifier = Modifier.fillMaxWidth(firstLineWidthFraction),
                height = FDS.Sizer.Icon.icon16,
            )

            // Second line - shorter
            FoundationSkeletonRectangle(
                modifier = Modifier.fillMaxWidth(secondLineWidthFraction),
                height = FDS.Sizer.Icon.icon16,
            )
        }
    }
}

/**
 * Complete skeleton card for transaction details.
 * Shows multiple skeleton list items with proper spacing.
 */
@Composable
fun FoundationSkeletonCard(
    modifier: Modifier = Modifier,
    itemCount: Int = 2,
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap24),
    ) {
        repeat(itemCount) {
            FoundationSkeletonListItem()
        }
    }
}