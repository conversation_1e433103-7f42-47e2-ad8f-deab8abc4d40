package com.vietinbank.core_ui.components.dialog

import com.vietinbank.core_ui.base.dialog.BaseDialog
import java.lang.ref.WeakReference

/**
 * Thread-safe dialog stack manager for managing multiple dialogs
 * and handling system bar appearance handoff.
 *
 * Features:
 * - Track dialog stacking order
 * - Immediate system bar handoff via WeakReference registry
 * - Thread-safe operations
 * - Memory leak prevention with weak references
 */
object DialogStackManager {
    private val stack = mutableListOf<String>()
    private val refs = HashMap<String, WeakReference<BaseDialog<*>>>()
    private val lock = Any()

    /**
     * Push a dialog onto the stack
     */
    fun push(dialogId: String, dialog: BaseDialog<*>) {
        synchronized(lock) {
            stack.add(dialogId)
            refs[dialogId] = WeakReference(dialog)
            cleanupStaleReferences()
        }
    }

    /**
     * Pop the top dialog from the stack
     */
    fun pop(): String? {
        synchronized(lock) {
            return if (stack.isNotEmpty()) {
                val id = stack.removeAt(stack.size - 1)
                refs.remove(id)
                cleanupStaleReferences()
                id
            } else {
                null
            }
        }
    }

    /**
     * Peek at the top dialog without removing it
     */
    fun peek(): String? {
        synchronized(lock) {
            return stack.lastOrNull()
        }
    }

    /**
     * Get the next visible dialog in the stack (for system bar handoff)
     */
    fun getNextVisibleDialog(currentId: String): BaseDialog<*>? {
        synchronized(lock) {
            val currentIndex = stack.indexOf(currentId)
            if (currentIndex > 0) {
                // Get the dialog below the current one
                val nextId = stack[currentIndex - 1]
                return refs[nextId]?.get()
            }
            return null
        }
    }

    /**
     * Get all dialog IDs in the stack (for debugging)
     */
    fun getStackIds(): List<String> {
        synchronized(lock) {
            return stack.toList()
        }
    }

    /**
     * Clear the entire stack (use with caution)
     */
    fun clear() {
        synchronized(lock) {
            stack.clear()
            refs.clear()
        }
    }

    /**
     * Remove a specific dialog from the stack
     */
    fun remove(dialogId: String) {
        synchronized(lock) {
            stack.remove(dialogId)
            refs.remove(dialogId)
            cleanupStaleReferences()
        }
    }

    /**
     * Check if a dialog is in the stack
     */
    fun contains(dialogId: String): Boolean {
        synchronized(lock) {
            return stack.contains(dialogId)
        }
    }

    /**
     * Get the size of the stack
     */
    fun size(): Int {
        synchronized(lock) {
            return stack.size
        }
    }

    /**
     * Check if the stack is empty
     */
    fun isEmpty(): Boolean {
        synchronized(lock) {
            return stack.isEmpty()
        }
    }

    /**
     * Clean up stale weak references
     */
    private fun cleanupStaleReferences() {
        val staleIds = refs.entries
            .filter { it.value.get() == null }
            .map { it.key }

        staleIds.forEach { id ->
            refs.remove(id)
            stack.remove(id)
        }
    }
}