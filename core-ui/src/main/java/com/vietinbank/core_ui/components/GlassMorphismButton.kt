package com.vietinbank.core_ui.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.drawscope.withTransform
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.utils.drawCircularInnerShadow
import com.vietinbank.core_ui.utils.gradientBorder
import com.vietinbank.core_ui.utils.innerShadow
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Constants for GlassMorphismButton styling
 */
private object GlassMorphismButtonConstants {
    // Gradient parameters matching CircularIconButton
    const val GRADIENT_RADIUS_MULTIPLIER = 1.2f
    const val GRADIENT_FADE_STOP = 0.6f

    // Gradient stops for blue area limitation (bottom 50% only)
    const val GRADIENT_BLUE_END = 0.50f // Blue gradient ends at 50%
    const val GRADIENT_WHITE_START = 0.55f // White starts at 55%

    // Shadow parameters
    const val SHADOW_ALPHA = 0.15f
    const val INNER_SHADOW_ALPHA = 0.1f
    const val INNER_SHADOW_GRADIENT_END_Y = 20f

    // Dark button specific
    const val DARK_RADIUS_FRACTION_PRESSED = 0.75f
    const val DARK_RADIUS_FRACTION_DEFAULT = 0.5f
}

/**
 * Shape configuration for GlassMorphismButton
 * Allows creation of circular, elliptical, or custom shaped buttons
 */
sealed class GlassMorphismShape {
    /**
     * Circular shape with equal width and height
     * @param size The diameter of the circle
     */
    data class Circle(val size: Dp) : GlassMorphismShape()

    /**
     * Elliptical shape with adaptive dimensions
     * @param width Optional width of the ellipse (null for content-based width)
     * @param height Optional height of the ellipse (null for content-based height)
     * @param cornerRadius The corner radius for rounded corners
     * @param horizontalPadding Horizontal padding for content
     * @param verticalPadding Vertical padding for content
     */
    data class Ellipse(
        val width: Dp? = null,
        val height: Dp? = null,
        val cornerRadius: Dp = 21.dp,
        val horizontalPadding: Dp = 16.dp,
        val verticalPadding: Dp = 8.dp,
    ) : GlassMorphismShape()

    /**
     * Custom shape with user-defined dimensions and shape
     * @param width Optional width of the button (null for content-based width)
     * @param height Optional height of the button (null for content-based height)
     * @param shape The custom shape to apply
     * @param horizontalPadding Horizontal padding for content
     * @param verticalPadding Vertical padding for content
     */
    data class Custom(
        val width: Dp? = null,
        val height: Dp? = null,
        val shape: Shape,
        val horizontalPadding: Dp = 16.dp,
        val verticalPadding: Dp = 8.dp,
    ) : GlassMorphismShape()
}

/**
 * Generic Glass Morphism Button Component
 * Supports any shape (circular, elliptical, custom) with glass morphism effect
 * Exactly matches CircularIconButton's visual implementation
 *
 * Features:
 * - Flexible shape support (circle, ellipse, custom)
 * - Exact same gradient and shadow effects as CircularIconButton
 * - Light and dark variants matching CircularIconButton
 * - Pressed state visual feedback
 * - Safe click handling to prevent double-clicks
 *
 * @param shapeConfig Configuration for button shape and size
 * @param onClick Click handler callback
 * @param modifier Additional modifiers
 * @param isLightButton Whether to use light variant (true) or dark variant (false)
 * @param enabled Whether the button is enabled
 * @param content The content to display inside the button
 */
@Composable
fun GlassMorphismButton(
    shapeConfig: GlassMorphismShape,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    isLightButton: Boolean = true,
    enabled: Boolean = true,
    content: @Composable BoxScope.() -> Unit,
) {
    // Helper data class for 5 values
    data class Quintuple<A, B, C, D, E>(val first: A, val second: B, val third: C, val fourth: D, val fifth: E)

    // Extract dimensions, shape, and padding from configuration
    val (width, height, shape, horizontalPadding, verticalPadding) = when (shapeConfig) {
        is GlassMorphismShape.Circle -> Quintuple(
            shapeConfig.size,
            shapeConfig.size,
            CircleShape,
            0.dp,
            0.dp,
        )
        is GlassMorphismShape.Ellipse -> Quintuple(
            shapeConfig.width,
            shapeConfig.height,
            RoundedCornerShape(shapeConfig.cornerRadius),
            shapeConfig.horizontalPadding,
            shapeConfig.verticalPadding,
        )
        is GlassMorphismShape.Custom -> Quintuple(
            shapeConfig.width,
            shapeConfig.height,
            shapeConfig.shape,
            shapeConfig.horizontalPadding,
            shapeConfig.verticalPadding,
        )
    }

    if (isLightButton) {
        LightGlassMorphismButton(
            width = width,
            height = height,
            shape = shape,
            horizontalPadding = horizontalPadding,
            verticalPadding = verticalPadding,
            onClick = onClick,
            modifier = modifier,
            enabled = enabled,
            content = content,
        )
    } else {
        DarkGlassMorphismButton(
            width = width,
            height = height,
            shape = shape,
            horizontalPadding = horizontalPadding,
            verticalPadding = verticalPadding,
            onClick = onClick,
            modifier = modifier,
            enabled = enabled,
            content = content,
        )
    }
}

/**
 * Light variant implementation - exactly matches CircularIconButton's LightIconButton
 */
@Composable
private fun LightGlassMorphismButton(
    width: Dp?,
    height: Dp?,
    shape: Shape,
    horizontalPadding: Dp,
    verticalPadding: Dp,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    content: @Composable BoxScope.() -> Unit,
) {
    var isPressed by remember { mutableStateOf(false) }

    // Resolve colors for drawing operations - same as CircularIconButton
    val backgroundColor = FDS.Colors.white
    val (gradientColor, gradientTransparentColor) = if (isPressed) {
        Pair(FDS.Colors.buttonGradientPrimaryPressed, FDS.Colors.buttonGradientPrimary)
    } else {
        Pair(FDS.Colors.buttonGradientPrimary, FDS.Colors.buttonGradientPrimaryTransparent)
    }
    val gradientOpacityFactor = if (enabled) 1f else 0.2f

    // Build size modifier conditionally
    val sizeModifier = when {
        width != null && height != null -> Modifier.size(width = width, height = height)
        width != null -> Modifier.width(width)
        height != null -> Modifier.height(height)
        else -> Modifier
    }

    Box(
        modifier = modifier
            .then(sizeModifier)
            .shadow(
                elevation = FDS.Effects.elevationButton,
                shape = shape,
                spotColor = Color.Black.copy(alpha = GlassMorphismButtonConstants.SHADOW_ALPHA),
                ambientColor = Color.Black.copy(alpha = GlassMorphismButtonConstants.SHADOW_ALPHA),
            )
            .clip(shape)
            .drawBehind {
                // White background first (like FoundationButtonLight)
                drawRect(
                    color = backgroundColor,
                    size = size,
                )

                // Radial gradient background following CircularIconButton pattern
                val center = Offset(size.width / 2f, size.height)
                val radius = size.height

                // Gradient logic matching CircularIconButton
                val gradient = if (isPressed) {
                    // Pressed state - simple 2-color gradient
                    Brush.radialGradient(
                        0f to gradientColor,
                        1f to gradientTransparentColor,
                        center = center,
                        radius = radius * GlassMorphismButtonConstants.GRADIENT_RADIUS_MULTIPLIER,
                    )
                } else {
                    // Default state with fade effect - blue only in bottom 50%
                    Brush.radialGradient(
                        0f to gradientColor.copy(alpha = gradientColor.alpha * gradientOpacityFactor),
                        GlassMorphismButtonConstants.GRADIENT_BLUE_END to gradientTransparentColor.copy(alpha = 0f),
                        GlassMorphismButtonConstants.GRADIENT_WHITE_START to backgroundColor,
                        1f to backgroundColor,
                        center = center,
                        radius = radius * GlassMorphismButtonConstants.GRADIENT_RADIUS_MULTIPLIER,
                    )
                }

                // Scale for elliptical shapes
                val scaleX = size.width / size.height
                withTransform({
                    scale(
                        scaleX = scaleX,
                        scaleY = 1f,
                        pivot = center,
                    )
                }) {
                    drawCircle(
                        brush = gradient,
                        center = center,
                        radius = radius * GlassMorphismButtonConstants.GRADIENT_RADIUS_MULTIPLIER,
                    )
                }

                // Inner shadow for glass effect
                drawCircularInnerShadow(
                    alpha = GlassMorphismButtonConstants.INNER_SHADOW_ALPHA,
                    gradientEndY = GlassMorphismButtonConstants.INNER_SHADOW_GRADIENT_END_Y,
                )
            }
            .safeClickable(
                enabled = enabled,
                onSafeClick = onClick,
                onPressedChanged = {
                    isPressed = it
                },
            ),
        contentAlignment = Alignment.Center,
    ) {
        // Apply padding to content inside the button
        Box(
            modifier = Modifier.padding(
                horizontal = horizontalPadding,
                vertical = verticalPadding,
            ),
            contentAlignment = Alignment.Center,
            content = content,
        )
    }
}

/**
 * Dark variant implementation - exactly matches CircularIconButton's DarkIconButton
 */
@Composable
private fun DarkGlassMorphismButton(
    width: Dp?,
    height: Dp?,
    shape: Shape,
    horizontalPadding: Dp,
    verticalPadding: Dp,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    content: @Composable BoxScope.() -> Unit,
) {
    var isPressed by remember { mutableStateOf(false) }

    val listColor = when {
        !enabled -> listOf(
            FDS.Colors.buttonDarkDisablePrimary.copy(alpha = 0.1F),
            FDS.Colors.buttonDarkDisableSecondary.copy(alpha = 0.1F),
        )
        isPressed -> listOf(
            FDS.Colors.darkButtonPressedState.copy(alpha = 0.3F),
            Color.White.copy(alpha = 0F),
        )
        else -> listOf(
            FDS.Colors.darkButtonEnableState.copy(alpha = 0.2F),
            Color.White.copy(alpha = 0F),
        )
    }
    val radiusFraction = if (isPressed) {
        GlassMorphismButtonConstants.DARK_RADIUS_FRACTION_PRESSED
    } else {
        GlassMorphismButtonConstants.DARK_RADIUS_FRACTION_DEFAULT
    }

    // Build size modifier conditionally
    val sizeModifier = when {
        width != null && height != null -> Modifier.size(width = width, height = height)
        width != null -> Modifier.width(width)
        height != null -> Modifier.height(height)
        else -> Modifier
    }

    Box(
        modifier = modifier
            .then(sizeModifier)
            .clip(shape)
            .radialGradientBackground(
                colors = listColor,
                radiusFraction = radiusFraction,
            )
            .gradientBorder()
            .innerShadow(
                shape = shape,
                color = Color.White.copy(0.5f),
                offsetY = (-2).dp,
                blur = 2.dp,
            )
            .innerShadow(
                shape = shape,
                color = Color.White.copy(0.5f),
                offsetY = (2).dp,
                blur = 2.dp,
            )
            .safeClickable(
                enabled = enabled,
                onPressedChanged = {
                    isPressed = it
                },
                onSafeClick = onClick,
            ),
        contentAlignment = Alignment.Center,
    ) {
        // Apply padding to content inside the button
        Box(
            modifier = Modifier.padding(
                horizontal = horizontalPadding,
                vertical = verticalPadding,
            ),
            contentAlignment = Alignment.Center,
            content = content,
        )
    }
}

/**
 * Convenience function for creating a circular glass morphism button
 * @param size The diameter of the circular button
 * @param onClick Click handler callback
 * @param modifier Additional modifiers
 * @param isLightButton Whether to use light variant
 * @param enabled Whether the button is enabled
 * @param content The content to display inside the button
 */
@Composable
fun CircularGlassMorphismButton(
    size: Dp,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    isLightButton: Boolean = true,
    enabled: Boolean = true,
    content: @Composable BoxScope.() -> Unit,
) {
    GlassMorphismButton(
        shapeConfig = GlassMorphismShape.Circle(size),
        onClick = onClick,
        modifier = modifier,
        isLightButton = isLightButton,
        enabled = enabled,
        content = content,
    )
}

/**
 * Convenience function for creating an elliptical glass morphism button
 * @param onClick Click handler callback
 * @param modifier Additional modifiers
 * @param width Optional width of the elliptical button (null for content-based)
 * @param height Optional height of the elliptical button (null for content-based)
 * @param cornerRadius The corner radius for rounded corners
 * @param horizontalPadding Horizontal padding for content
 * @param verticalPadding Vertical padding for content
 * @param isLightButton Whether to use light variant
 * @param enabled Whether the button is enabled
 * @param content The content to display inside the button
 */
@Composable
fun EllipticalGlassMorphismButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    width: Dp? = null,
    height: Dp? = null,
    cornerRadius: Dp = 21.dp,
    horizontalPadding: Dp = 16.dp,
    verticalPadding: Dp = 8.dp,
    isLightButton: Boolean = true,
    enabled: Boolean = true,
    content: @Composable BoxScope.() -> Unit,
) {
    GlassMorphismButton(
        shapeConfig = GlassMorphismShape.Ellipse(
            width = width,
            height = height,
            cornerRadius = cornerRadius,
            horizontalPadding = horizontalPadding,
            verticalPadding = verticalPadding,
        ),
        onClick = onClick,
        modifier = modifier,
        isLightButton = isLightButton,
        enabled = enabled,
        content = content,
    )
}