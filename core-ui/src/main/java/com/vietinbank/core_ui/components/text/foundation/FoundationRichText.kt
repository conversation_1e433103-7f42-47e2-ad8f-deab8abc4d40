package com.vietinbank.core_ui.components.text.foundation

import androidx.compose.foundation.text.InlineTextContent
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Foundation Rich Text Component
 *
 * Advanced text component with support for AnnotatedString and clickable spans.
 * Allows mixing different text styles, colors, and clickable regions within a single text.
 *
 * @param text The annotated string to display
 * @param modifier Modifier for the component
 * @param style Base text style from FDS.Typography (default: bodyB2)
 * @param onTextPartClick Click handler for annotated text parts - protected with safeClickable
 * @param clickTimeWindow Time window for double-click protection in milliseconds
 * @param inlineContent Map of inline content for placeholders in the text
 * @param textAlign Text alignment
 * @param overflow How to handle text overflow
 * @param softWrap Whether text should break at soft line breaks
 * @param maxLines Maximum number of lines
 * @param minLines Minimum number of lines
 * @param onTextLayout Callback when text layout is calculated
 */
@Composable
fun FoundationRichText(
    text: AnnotatedString,
    modifier: Modifier = Modifier,
    style: TextStyle = FDS.Typography.bodyB2,
    onTextPartClick: ((String) -> Unit)? = null,
    clickTimeWindow: Long = 1000L,
    inlineContent: Map<String, InlineTextContent> = emptyMap(),
    textAlign: TextAlign? = null,
    overflow: TextOverflow = TextOverflow.Clip,
    softWrap: Boolean = true,
    maxLines: Int = Int.MAX_VALUE,
    minLines: Int = 1,
    onTextLayout: ((TextLayoutResult) -> Unit)? = null,
) {
    if (onTextPartClick != null) {
        var lastClickTime by remember { mutableStateOf(0L) }
        var layoutResult by remember { mutableStateOf<TextLayoutResult?>(null) }

        Text(
            text = text,
            modifier = modifier.safeClickable(
                timeWindow = clickTimeWindow,
                onSafeClick = {
                    // For simplicity, we'll check all annotations and call handler for the first one
                    // In a real implementation, you'd need to track click position
                    text.getStringAnnotations(0, text.length).firstOrNull()?.let { annotation ->
                        onTextPartClick(annotation.item)
                    }
                },
            ),
            style = style,
            textAlign = textAlign,
            overflow = overflow,
            softWrap = softWrap,
            maxLines = maxLines,
            onTextLayout = { result ->
                layoutResult = result
                onTextLayout?.invoke(result)
            },
            inlineContent = inlineContent,
        )
    } else {
        if (onTextLayout != null) {
            Text(
                text = text,
                modifier = modifier,
                style = style,
                textAlign = textAlign,
                overflow = overflow,
                softWrap = softWrap,
                maxLines = maxLines,
                onTextLayout = onTextLayout,
                inlineContent = inlineContent,
            )
        } else {
            Text(
                text = text,
                modifier = modifier,
                style = style,
                textAlign = textAlign,
                overflow = overflow,
                softWrap = softWrap,
                maxLines = maxLines,
                inlineContent = inlineContent,
            )
        }
    }
}

/**
 * Foundation Rich Text Component with Builder
 *
 * Convenience function that allows building AnnotatedString inline using a DSL.
 *
 * @param modifier Modifier for the component
 * @param style Base text style from FDS.Typography (default: bodyB2)
 * @param onTextPartClick Click handler for annotated text parts
 * @param clickTimeWindow Time window for double-click protection
 * @param textAlign Text alignment
 * @param overflow How to handle text overflow
 * @param softWrap Whether text should break at soft line breaks
 * @param maxLines Maximum number of lines
 * @param minLines Minimum number of lines
 * @param onTextLayout Callback when text layout is calculated
 * @param builder DSL builder for creating the AnnotatedString
 */
@Composable
fun FoundationRichText(
    modifier: Modifier = Modifier,
    style: TextStyle = FDS.Typography.bodyB2,
    onTextPartClick: ((String) -> Unit)? = null,
    clickTimeWindow: Long = 1000L,
    textAlign: TextAlign? = null,
    overflow: TextOverflow = TextOverflow.Clip,
    softWrap: Boolean = true,
    maxLines: Int = Int.MAX_VALUE,
    minLines: Int = 1,
    onTextLayout: ((TextLayoutResult) -> Unit)? = null,
    builder: AnnotatedString.Builder.() -> Unit,
) {
    val annotatedString = buildAnnotatedString(builder)

    FoundationRichText(
        text = annotatedString,
        modifier = modifier,
        style = style,
        onTextPartClick = onTextPartClick,
        clickTimeWindow = clickTimeWindow,
        textAlign = textAlign,
        overflow = overflow,
        softWrap = softWrap,
        maxLines = maxLines,
        minLines = minLines,
        onTextLayout = onTextLayout,
    )
}

/**
 * Extension functions for AnnotatedString.Builder to make it easier to use FDS styles
 */

/**
 * Append text with link style and annotation for click handling
 */
fun AnnotatedString.Builder.appendLink(
    text: String,
    tag: String,
    annotation: String = text,
    color: Color,
    textDecoration: TextDecoration = TextDecoration.Underline,
) {
    pushStringAnnotation(tag = tag, annotation = annotation)
    withStyle(
        style = SpanStyle(
            color = color,
            textDecoration = textDecoration,
        ),
    ) {
        append(text)
    }
    pop()
}

/**
 * Append text with colored style
 */
fun AnnotatedString.Builder.appendColored(
    text: String,
    color: Color,
    fontWeight: FontWeight? = null,
) {
    withStyle(
        style = SpanStyle(
            color = color,
            fontWeight = fontWeight,
        ),
    ) {
        append(text)
    }
}

/**
 * Append text with bold style
 */
fun AnnotatedString.Builder.appendBold(
    text: String,
    color: Color? = null,
) {
    withStyle(
        style = SpanStyle(
            fontWeight = FontWeight.Bold,
            color = color ?: Color.Unspecified,
        ),
    ) {
        append(text)
    }
}