package com.vietinbank.core_ui.components.foundation.pin

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity

@Composable
fun OTPPinInput(
    modifier: Modifier = Modifier,
    pinState: PinState,
    pinValue: String,
    showNumber: Boolean = true,
    focusRequester: FocusRequester? = null,
    enabled: Boolean = true,
    otpLength: Int = 6,
    onDone: (() -> Unit)? = null,
    onPinValueChange: (String) -> Unit,
) {
    val focusRequester = remember {
        focusRequester ?: FocusRequester()
    }

    var otpLayoutHeight by remember {
        mutableIntStateOf(50)
    }

    Box {
        PinHiddenComponent(
            modifier = modifier.onGloballyPositioned { position ->
                otpLayoutHeight = position.size.height
            },
            state = pinState,
            showNumbers = showNumber,
            digits = pinValue,
            onRowTap = {
                focusRequester.requestFocus()
            },
            onToggleVisibility = {
                // suppress
            },
            hideToggleIcon = true,
        )

        StealthOtpInputField(
            modifier = Modifier
                .fillMaxWidth()
                .height(
                    with(LocalDensity.current) {
                        otpLayoutHeight.toDp()
                    },
                ), // Overlay over input area
            value = pinValue,
            length = otpLength,
            onValueChange = onPinValueChange,
            focusRequester = focusRequester,
            enabled = enabled,
            onDone = onDone,
        )
    }
}