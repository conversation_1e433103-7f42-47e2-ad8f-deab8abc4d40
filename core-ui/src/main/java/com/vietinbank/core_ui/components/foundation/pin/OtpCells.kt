package com.vietinbank.core_ui.components.foundation.pin

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * OTP Cell Components - Shared building blocks for PIN/OTP UI
 *
 * These components are used by both PinHiddenComponent and PinShowComponent
 * to render individual cells and rows of cells.
 */

/**
 * Constants for OTP cell dimensions based on Figma design
 */
internal object OtpCellConstants {
    // Aspect ratios from Figma design
    const val DOT_ASPECT_RATIO = 0.75f // 24/32 = 0.75
    const val BOX_ASPECT_RATIO = 0.75f // 48/64 = 0.75

    // Maximum sizes to prevent cells from being too large on tablets
    val MAX_BOX_WIDTH = 56.dp
    val MAX_DOT_WIDTH = 32.dp

    // Spacing between cells
    val BOX_SPACING = 4.dp
    val BOX_SPACING_NARROW = 2.dp
    val DOT_SPACING = 16.dp

    // Dynamic spacing range for PIN SHOW
    val BOX_SPACING_MIN = 2.dp
    val BOX_SPACING_MAX = 8.dp

    // Narrow screen threshold
    val MIN_CONTAINER_WIDTH = 345.dp

    // Maximum width for PIN Show container (from Figma design)
    val PIN_SHOW_MAX_WIDTH = 345.dp

    // Fixed dimensions for PIN components (as per Figma design)
    val DOT_WIDTH = 24.dp // Fixed width for dots (PIN Hidden closed eye)
    val DOT_HEIGHT = 32.dp // Fixed height for dots (PIN Hidden closed eye)
    val BOX_WIDTH = 48.dp // Reference width for boxes (PIN Show)
    val BOX_HEIGHT = 64.dp // Reference height for boxes (PIN Show)

    // Smaller boxes for PIN Hidden with open eye
    val SMALL_BOX_WIDTH = 36.dp // Smaller width for PIN Hidden open eye
    val SMALL_BOX_HEIGHT = 48.dp // Smaller height for PIN Hidden open eye
    val SMALL_BOX_MAX_WIDTH = 280.dp // Max container width for small boxes
}

/**
 * Individual dot cell for hidden PIN display
 *
 * @param color The background color of the dot
 * @param modifier Optional modifier for the cell
 */
@Composable
fun OtpDotCell(
    color: Color,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .background(
                color = color,
                shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
            ),
    )
}

/**
 * Individual number box for visible PIN display
 *
 * @param text The text to display (digit or underscore)
 * @param backgroundColor Background color of the box
 * @param textColor Text color
 * @param modifier Optional modifier for the cell
 */
@Composable
fun OtpNumberBox(
    text: String,
    backgroundColor: Color,
    textColor: Color,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .background(
                color = backgroundColor,
                shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
            ),
        contentAlignment = Alignment.Center,
    ) {
        FoundationText(
            text = text,
            style = FDS.Typography.headingH2,
            color = textColor,
        )
    }
}

/**
 * Row of visible number boxes - Uses weight distribution with max width constraint
 * PIN Show state should have evenly distributed boxes but not ultra-thin
 *
 * @param texts List of texts to display in each box
 * @param backgroundColor Background color for all boxes
 * @param textColor Text color for all texts
 * @param modifier Optional modifier for the row
 * @param isNarrow Whether to use narrow dimensions (for small screens)
 */
@Composable
fun OtpRowVisible(
    texts: List<String>,
    backgroundColor: Color,
    textColor: Color,
    modifier: Modifier = Modifier,
    isNarrow: Boolean = false,
) {
    // Calculate dynamic spacing based on screen size
    // Larger screens get more spacing, smaller screens get less
    val spacing = when {
        isNarrow -> OtpCellConstants.BOX_SPACING_MIN
        else -> {
            // Use slightly larger spacing for better visual balance
            // This is proportional but clamped to reasonable range
            (OtpCellConstants.BOX_SPACING * 1.5f).coerceIn(
                OtpCellConstants.BOX_SPACING_MIN,
                OtpCellConstants.BOX_SPACING_MAX,
            )
        }
    }

    // Use weight-based distribution within a constrained width
    // This ensures all boxes have equal width but don't stretch too thin
    Row(
        modifier = modifier
            .widthIn(max = OtpCellConstants.PIN_SHOW_MAX_WIDTH) // Constrain max width
            .fillMaxWidth(), // Fill up to max width
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        texts.forEachIndexed { index, text ->
            OtpNumberBox(
                text = text,
                backgroundColor = backgroundColor,
                textColor = textColor,
                modifier = Modifier
                    .weight(1f) // Even distribution
                    .aspectRatio(OtpCellConstants.BOX_ASPECT_RATIO), // Maintain aspect ratio
            )
            // Add spacing between cells (except for the last one)
            if (index < texts.size - 1) {
                Spacer(modifier = Modifier.width(spacing))
            }
        }
    }
}

/**
 * Row of visible boxes for PIN HIDDEN with open eye - Uses smaller fixed sizes
 * This is specifically for PIN Hidden component when showing numbers
 *
 * @param texts List of texts to display in each box
 * @param backgroundColor Background color for all boxes
 * @param textColor Text color for all texts
 * @param modifier Optional modifier for the row
 * @param isNarrow Whether to use narrow dimensions (for small screens)
 */
@Composable
fun OtpRowVisibleSmall(
    texts: List<String>,
    backgroundColor: Color,
    textColor: Color,
    modifier: Modifier = Modifier,
    isNarrow: Boolean = false,
) {
    val spacing = if (isNarrow) {
        OtpCellConstants.BOX_SPACING_NARROW
    } else {
        OtpCellConstants.BOX_SPACING
    }

    val boxWidth = if (isNarrow) 32.dp else OtpCellConstants.SMALL_BOX_WIDTH
    val boxHeight = if (isNarrow) 42.dp else OtpCellConstants.SMALL_BOX_HEIGHT

    // Use fixed-size smaller boxes centered in container
    Row(
        modifier = modifier
            .widthIn(max = OtpCellConstants.SMALL_BOX_MAX_WIDTH),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        texts.forEachIndexed { index, text ->
            OtpNumberBox(
                text = text,
                backgroundColor = backgroundColor,
                textColor = textColor,
                modifier = Modifier
                    .width(boxWidth)
                    .height(boxHeight),
            )
            // Add spacing between cells (except for the last one)
            if (index < texts.size - 1) {
                Spacer(modifier = Modifier.width(spacing))
            }
        }
    }
}

/**
 * Row of hidden dots - Always uses fixed size (24x32dp) as per design
 * PIN Hidden state should show small dots, not expand to fill width
 *
 * @param count Number of dots to display
 * @param activeCount Number of active (filled) dots
 * @param activeColor Color for active dots
 * @param inactiveColor Color for inactive dots
 * @param modifier Optional modifier for the row
 */
@Composable
fun OtpRowHidden(
    count: Int,
    activeCount: Int,
    activeColor: Color,
    inactiveColor: Color,
    modifier: Modifier = Modifier,
) {
    // Always use fixed-size dots (24x32dp) centered in the container
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        repeat(count) { index ->
            OtpDotCell(
                color = if (index < activeCount) activeColor else inactiveColor,
                modifier = Modifier
                    .width(OtpCellConstants.DOT_WIDTH)
                    .height(OtpCellConstants.DOT_HEIGHT)
                    .semantics {
                        contentDescription = "Ô số thứ ${index + 1}, " +
                            if (index < activeCount) "đã nhập" else "chưa nhập"
                    },
            )
            // Add spacing between cells (except for the last one)
            if (index < count - 1) {
                Spacer(modifier = Modifier.width(OtpCellConstants.DOT_SPACING))
            }
        }
    }
}

/**
 * Row of uniform colored dots (for success/error states)
 * Always uses fixed size (24x32dp) as per design
 *
 * @param count Number of dots to display
 * @param color Color for all dots
 * @param modifier Optional modifier for the row
 */
@Composable
fun OtpRowUniformDots(
    count: Int,
    color: Color,
    modifier: Modifier = Modifier,
) {
    // Always use fixed-size dots (24x32dp) centered in the container
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        repeat(count) { index ->
            OtpDotCell(
                color = color,
                modifier = Modifier
                    .width(OtpCellConstants.DOT_WIDTH)
                    .height(OtpCellConstants.DOT_HEIGHT)
                    .semantics {
                        contentDescription = "Ô số thứ ${index + 1}"
                    },
            )
            // Add spacing between cells (except for the last one)
            if (index < count - 1) {
                Spacer(modifier = Modifier.width(OtpCellConstants.DOT_SPACING))
            }
        }
    }
}