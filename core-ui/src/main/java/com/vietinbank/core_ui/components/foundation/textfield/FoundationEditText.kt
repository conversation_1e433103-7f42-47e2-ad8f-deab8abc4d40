package com.vietinbank.core_ui.components.foundation.textfield

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.text.selection.LocalTextSelectionColors
import androidx.compose.foundation.text.selection.TextSelectionColors
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.error
import androidx.compose.ui.semantics.password
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.em
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.foundation.textfield.transformations.AmountVisualTransformation
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Creates a TextStyle for password masking with exact dot size and spacing
 * Uses standard PasswordVisualTransformation with proper sizing
 *
 * @param dotSizeDp The visual size of each dot (default 8dp per Figma)
 * @param gapDp The gap between dots (default 8dp per Figma)
 * @param color The color for the dots
 */
@Composable
private fun maskedPasswordStyle(
    dotSizeDp: Dp = FDS.Sizer.Icon.icon8,
    gapDp: Dp = FDS.Sizer.Gap.gap8,
    color: Color,
): TextStyle {
    val density = LocalDensity.current
    // Convert dp to sp for exact sizing regardless of font scale
    val fontSizeSp = with(density) { dotSizeDp.toSp() }
    // Calculate gap as em units (relative to font size)
    // letterSpacing is the additional space added between characters
    val gapEm = (gapDp.value / dotSizeDp.value).em

    return FDS.Typography.bodyB1.copy(
        color = color,
        fontSize = fontSizeSp,
        letterSpacing = gapEm,
        fontFamily = FontFamily.Monospace, // Ensures consistent dot spacing
    )
}

/**
 * Business constants for FoundationEditText (InputField design)
 */
private object FoundationEditTextConstants {
    // Business rule: Default max character limit
    const val DEFAULT_MAX_LENGTH = 146

    // Business rule: Character warning threshold
    const val CHARACTER_WARNING_THRESHOLD = 0.9f
}

/**
 * State enum for FoundationEditText (InputField design)
 */
enum class EditTextState {
    DEFAULT, // Empty field
    FILLED, // Has content
    ACTIVE, // Focused
    EXTENDED, // Error state with message
    PASSWORD, // Password mode with dots
}

/**
 * Extension properties for InputType enum to work with FoundationEditText
 */
val InputType.isPasswordField: Boolean
    get() = this == InputType.PASSWORD

val InputType.keyboardType: KeyboardType
    get() = when (this) {
        InputType.PASSWORD -> KeyboardType.Password
        InputType.EMAIL -> KeyboardType.Email
        InputType.NUMBER -> KeyboardType.Number
        else -> KeyboardType.Text
    }

/**
 * Foundation EditText Component (InputField design from Figma)
 *
 * Simple text input with placeholder, character counter, and expandable error state.
 * Different from FoundationFieldType which has rounded container design.
 * Based on Figma node 3219-3590.
 *
 * ## Usage Examples:
 *
 * ### Basic text input
 * ```kotlin
 * FoundationEditText(
 *     value = text,
 *     onValueChange = { text = it },
 *     placeholder = "Nhận xét của quý khách"
 * )
 * ```
 *
 * ### Password with error state
 * ```kotlin
 * FoundationEditText(
 *     value = password,
 *     onValueChange = { password = it },
 *     inputType = InputType.PASSWORD,
 *     placeholder = "Mật khẩu",
 *     isError = passwordError != null,
 *     errorMessage = passwordError // Red dots and border on error
 * )
 * ```
 *
 * ### Email input
 * ```kotlin
 * FoundationEditText(
 *     value = email,
 *     onValueChange = { email = it },
 *     inputType = InputType.EMAIL,
 *     placeholder = "Email"
 * )
 * ```
 *
 * @param value The input text value
 * @param onValueChange Callback when text changes
 * @param modifier Modifier for the component
 * @param inputType Type of input (TEXT, EMAIL, PASSWORD, NUMBER, AMOUNT)
 * @param placeholder Placeholder text (shown on top line when empty or has value)
 * @param enabled Whether the field is enabled
 * @param isError Whether the field has an error
 * @param errorMessage Error message to display below field
 * @param showErrorMessage Whether to show error message below field (default: true)
 * @param currency Currency code to display (e.g., "VND") - only used with InputType.AMOUNT
 * @param maxLength Maximum character length (default: 146)
 * @param showCharacterCounter Whether to show character counter
 * @param showClearButton Whether to show clear button
 * @param singleLine Whether to limit to single line
 * @param imeAction IME action for keyboard
 * @param keyboardActions Keyboard actions
 * @param visualTransformation Optional visual transformation (auto-applied for AMOUNT type)
 * @param expandClickArea Whether clicking on label/placeholder area should focus the field (default: true)
 * @param onFocusChanged Focus change callback
 */
@Composable
fun FoundationEditText(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    inputType: InputType = InputType.TEXT,
    placeholder: String = "",
    hintText: String = "",
    enabled: Boolean = true,
    isError: Boolean = false,
    errorMessage: String? = null,
    showErrorMessage: Boolean = true,
    currency: String? = null,
    maxLength: Int = FoundationEditTextConstants.DEFAULT_MAX_LENGTH,
    showCharacterCounter: Boolean = true,
    showClearButton: Boolean = true,
    showBottomBorder: Boolean = false, // Default to no border as per Figma
    singleLine: Boolean = true,
    imeAction: ImeAction = ImeAction.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    visualTransformation: VisualTransformation? = null,
    trailingIcon: @Composable (() -> Unit)? = null,
    expandClickArea: Boolean = true,
    onFocusChanged: ((Boolean) -> Unit)? = null,
) {
    // State management
    var isFocused by remember { mutableStateOf(false) }
    var passwordVisible by remember { mutableStateOf(false) }
    val focusRequester = remember { FocusRequester() }
    var didSnapCursorOnFocus by remember { mutableStateOf(false) }

    // Use TextFieldValue for better cursor control
    val textFieldValue = remember { mutableStateOf(TextFieldValue(text = value)) }

    // Sync external value changes with proper selection management
    LaunchedEffect(value, inputType.isPasswordField) {
        if (value != textFieldValue.value.text) {
            val selection = if (inputType.isPasswordField) {
                // Password: always position at end
                TextRange(value.length)
            } else {
                // Other fields: maintain selection within bounds
                val current = textFieldValue.value.selection
                TextRange(
                    start = current.start.coerceIn(0, value.length),
                    end = current.end.coerceIn(0, value.length),
                )
            }
            textFieldValue.value = TextFieldValue(text = value, selection = selection)
        }
    }

    val hasError = isError || errorMessage != null
    val displayErrorMessage = errorMessage

    // Determine state
    val state = when {
        inputType.isPasswordField && !passwordVisible -> EditTextState.PASSWORD
        hasError -> EditTextState.EXTENDED
        isFocused -> EditTextState.ACTIVE
        value.isNotEmpty() -> EditTextState.FILLED
        else -> EditTextState.DEFAULT
    }

    // Configure keyboard based on InputType
    val keyboardOptions = KeyboardOptions(
        keyboardType = when (inputType) {
            InputType.PASSWORD -> KeyboardType.Password
            InputType.EMAIL -> KeyboardType.Email
            InputType.NUMBER -> KeyboardType.Number
            InputType.AMOUNT -> KeyboardType.Number
            else -> KeyboardType.Text
        },
        imeAction = imeAction,
        autoCorrectEnabled = if (inputType.isPasswordField) false else null, // No autocorrect for passwords
    )

    // Border color based on state
    val borderColor by animateColorAsState(
        targetValue = when (state) {
            EditTextState.EXTENDED -> FDS.Colors.stateErrorLighter
            EditTextState.ACTIVE -> FDS.Colors.characterHighlightedLighter
            else -> FDS.Colors.paletteNeutral100
        },
        animationSpec = tween(200),
        label = "border_color_animation",
    )

    // Text color based on state
    val textColor = when (state) {
        EditTextState.EXTENDED -> FDS.Colors.stateError
        EditTextState.ACTIVE -> FDS.Colors.characterHighlighted
        EditTextState.FILLED -> FDS.Colors.characterPrimary
        EditTextState.PASSWORD -> FDS.Colors.characterHighlighted
        else -> FDS.Colors.characterTertiary
    }

    // Apply visual transformation based on input type
    val finalVisualTransformation = visualTransformation ?: when {
        inputType.isPasswordField && !passwordVisible ->
            PasswordVisualTransformation(mask = '●') // Use filled circle for 1em size
        inputType == InputType.AMOUNT -> AmountVisualTransformation()
        else -> VisualTransformation.None
    }

    // Filter value for AMOUNT type to only allow digits
    val processedOnValueChange: (String) -> Unit = if (inputType == InputType.AMOUNT) {
        { newValue ->
            // Only allow digits for amount input
            val filtered = newValue.filter { it.isDigit() }
            if (filtered.length <= maxLength) {
                onValueChange(filtered)
            }
        }
    } else {
        { newValue ->
            if (newValue.length <= maxLength) {
                onValueChange(newValue)
            }
        }
    }

    Column(
        modifier = modifier
            .then(
                if (expandClickArea && enabled && !isFocused) {
                    Modifier.safeClickable(
                        onSafeClick = {
                            focusRequester.requestFocus()
                        },
                    )
                } else {
                    Modifier
                },
            )
            .semantics {
                if (inputType.isPasswordField) password()
                if (hasError && displayErrorMessage != null) {
                    error(displayErrorMessage)
                }
            },
    ) {
        // Placeholder and character counter/currency row (always visible)
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(FDS.Sizer.Padding.padding16),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // Placeholder on left
            Text(
                text = placeholder.ifEmpty { " " }, // Keep space to maintain height
                style = FDS.Typography.bodyB2,
                color = FDS.Colors.characterSecondary,
                modifier = Modifier.weight(1f, fill = false),
            )

            // Currency for AMOUNT type OR Character counter for other types
            when {
                // Show currency for AMOUNT type
                inputType == InputType.AMOUNT && !currency.isNullOrEmpty() -> {
                    Text(
                        text = currency,
                        style = FDS.Typography.bodyB2,
                        color = FDS.Colors.characterSecondary,
                    )
                }
                // Show character counter for non-password, non-amount types
                showCharacterCounter && !inputType.isPasswordField && inputType != InputType.AMOUNT -> {
                    Text(
                        text = "${value.length}/$maxLength",
                        style = FDS.Typography.bodyB2,
                        color = when {
                            value.length >= maxLength -> FDS.Colors.stateError
                            value.length >= maxLength * FoundationEditTextConstants.CHARACTER_WARNING_THRESHOLD -> FDS.Colors.stateWarning
                            else -> FDS.Colors.characterSecondary
                        },
                    )
                }
            }
        }

        // Input container with bottom border
        val strokeWidth = FDS.Sizer.Stroke.stroke1 // Resolve in Composable scope
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = FDS.Sizer.Gap.gap4)
                .then(
                    if (showBottomBorder) {
                        Modifier.drawBehind {
                            // Draw bottom border line
                            drawLine(
                                color = borderColor,
                                start = Offset(0f, size.height),
                                end = Offset(size.width, size.height),
                                strokeWidth = strokeWidth.toPx(),
                            )
                        }
                    } else {
                        Modifier
                    },
                ),
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FDS.Sizer.Padding.padding8),
                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                // Input field with proper selection colors
                Box(
                    modifier = Modifier.weight(1f),
                ) {
                    // Hide selection handle for password field when hidden
                    val selectionColors = if (inputType.isPasswordField && !passwordVisible) {
                        TextSelectionColors(
                            handleColor = Color.Transparent,
                            backgroundColor = Color.Transparent,
                        )
                    } else {
                        LocalTextSelectionColors.current
                    }

                    CompositionLocalProvider(LocalTextSelectionColors provides selectionColors) {
                        Box(
                            modifier = Modifier.fillMaxWidth(),
                        ) {
                            BasicTextField(
                                value = textFieldValue.value,
                                onValueChange = { newValue ->
                                    if (inputType.isPasswordField) {
                                        val newText = newValue.text
                                        if (newText.length <= maxLength) {
                                            textFieldValue.value = TextFieldValue(
                                                text = newText,
                                                selection = TextRange(newText.length),
                                            )
                                            processedOnValueChange(newText)
                                        }
                                    } else if (inputType == InputType.AMOUNT) {
                                        if (newValue.text.all { it.isDigit() } && newValue.text.length <= maxLength) {
                                            textFieldValue.value = newValue
                                            processedOnValueChange(newValue.text)
                                        }
                                    } else {
                                        processedOnValueChange(newValue.text)
                                        if (newValue.text.length <= maxLength) {
                                            textFieldValue.value = newValue
                                        }
                                    }
                                },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .focusRequester(focusRequester)
                                    .onFocusChanged { focusState ->
                                        isFocused = focusState.isFocused
                                        if (focusState.isFocused && inputType.isPasswordField && !didSnapCursorOnFocus) {
                                            textFieldValue.value = textFieldValue.value.copy(
                                                selection = TextRange(textFieldValue.value.text.length),
                                            )
                                            didSnapCursorOnFocus = true
                                        }
                                        if (!focusState.isFocused) {
                                            didSnapCursorOnFocus = false
                                        }
                                        onFocusChanged?.invoke(focusState.isFocused)
                                    },
                                enabled = enabled,
                                singleLine = singleLine,
                                textStyle = if (inputType.isPasswordField && !passwordVisible) {
                                    maskedPasswordStyle(
                                        dotSizeDp = 16.dp,
                                        gapDp = 8.dp,
                                        color = if (hasError) FDS.Colors.stateError else FDS.Colors.characterHighlighted,
                                    )
                                } else {
                                    FDS.Typography.bodyB2.copy(
                                        color = textColor,
                                        textAlign = TextAlign.Start,
                                    )
                                },
                                keyboardOptions = keyboardOptions,
                                keyboardActions = keyboardActions,
                                visualTransformation = finalVisualTransformation,
                                cursorBrush = SolidColor(
                                    if (hasError) {
                                        FDS.Colors.stateError
                                    } else {
                                        FDS.Colors.stateActive
                                    },
                                ),
                            )
                            // hint text
                            if (textFieldValue.value.text.isEmpty() && !isFocused && hintText.isNotEmpty()) {
                                FoundationText(
                                    text = hintText,
                                    color = FDS.Colors.characterTertiary,
                                    style = FDS.Typography.bodyB1,
                                )
                            }
                        }
                    }
                }

                // Right side actions: clear button and password toggle
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    // Clear button for all fields when focused and has text
                    if (showClearButton && value.isNotEmpty() && isFocused) {
                        IconButton(
                            onClick = { onValueChange("") },
                            modifier = Modifier.size(16.dp), // tam thoi, sua sau
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_common_close_24),
                                contentDescription = "Clear",
                                tint = FDS.Colors.characterSecondary,
                                modifier = Modifier.size(FDS.Sizer.Icon.icon16),
                            )
                        }
                    }

                    // Password toggle for password fields
                    if (inputType.isPasswordField) {
                        IconButton(
                            onClick = { passwordVisible = !passwordVisible },
                            modifier = Modifier
                                .size(FDS.Sizer.Icon.icon32)
                                .padding(start = FDS.Sizer.Gap.gap4), // Consistent touch target
                        ) {
                            Icon(
                                painter = painterResource(
                                    id = if (passwordVisible) {
                                        R.drawable.ic_common_eye_on_24
                                    } else {
                                        R.drawable.ic_common_eye_off_24
                                    },
                                ),
                                contentDescription = if (passwordVisible) "Hide password" else "Show password",
                                tint = FDS.Colors.characterSecondary,
                                modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                            )
                        }
                    }

                    // end icon
                    trailingIcon?.invoke()
                }
            }
        }

        // Error message with animation
        AnimatedVisibility(
            visible = showErrorMessage && hasError && displayErrorMessage != null,
            enter = fadeIn(animationSpec = tween(200)) + expandVertically(animationSpec = tween(250)),
            exit = fadeOut(animationSpec = tween(150)) + shrinkVertically(animationSpec = tween(200)),
        ) {
            Text(
                text = displayErrorMessage ?: "",
                style = FDS.Typography.captionCaptionL,
                color = FDS.Colors.stateError,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = FDS.Sizer.Gap.gap4), // Small top spacing as error is in its own section
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun FoundationEditTextPreview() {
    AppTheme {
        Column(
            modifier = Modifier.padding(FDS.Sizer.Padding.padding16),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap24),
        ) {
            // Default state
            FoundationEditText(
                value = "",
                onValueChange = {},
                placeholder = "Nhận xét của quý khách",
            )

            // Filled state
            FoundationEditText(
                value = "Tạm ổn",
                onValueChange = {},
                placeholder = "Nhận xét của quý khách",
            )

            // Password state
            FoundationEditText(
                value = "password123",
                onValueChange = {},
                placeholder = "Mật khẩu",
                inputType = InputType.PASSWORD,
            )

            // Error state
            FoundationEditText(
                value = "Tạm ổn",
                onValueChange = {},
                placeholder = "Nhận xét của quý khách",
                isError = true,
                errorMessage = "Nội dung báo lỗi",
            )

            // Password with error (red dots)
            FoundationEditText(
                value = "pass123",
                onValueChange = {},
                placeholder = "Mật khẩu",
                inputType = InputType.PASSWORD,
                isError = true,
                errorMessage = "Mật khẩu không đúng định dạng",
            )

            // Email input
            FoundationEditText(
                value = "<EMAIL>",
                onValueChange = {},
                placeholder = "Email",
                inputType = InputType.EMAIL,
            )

            // Number input
            FoundationEditText(
                value = "123",
                onValueChange = {},
                placeholder = "Số lượng",
                inputType = InputType.NUMBER,
            )

            // Amount input with currency
            FoundationEditText(
                value = "1000000",
                onValueChange = {},
                placeholder = "Số tiền",
                inputType = InputType.AMOUNT,
                currency = "VND",
            )

            // Amount input with USD
            FoundationEditText(
                value = "5000",
                onValueChange = {},
                placeholder = "Amount",
                inputType = InputType.AMOUNT,
                currency = "USD",
            )
        }
    }
}

@Preview(showBackground = true, name = "Password Validation Screen")
@Composable
private fun PasswordValidationPreview() {
    AppTheme {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    color = FDS.Colors.backgroundBgContainer,
                    shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
                )
                .padding(vertical = FDS.Sizer.Padding.padding16),
        ) {
            // Mật khẩu cũ với error
            FoundationEditText(
                value = "1234567",
                onValueChange = {},
                inputType = InputType.PASSWORD,
                placeholder = "Mật khẩu cũ",
                isError = true,
                errorMessage = "Mật khẩu không hợp lệ. Quý khách vui lòng kiểm tra lại.",
                modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
            )

            // Divider
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(FDS.Sizer.Stroke.stroke05)
                    .background(FDS.Colors.strokeDivider),
            )

            // Mật khẩu mới với error
            FoundationEditText(
                value = "abc123",
                onValueChange = {},
                inputType = InputType.PASSWORD,
                placeholder = "Mật khẩu mới",
                isError = true,
                errorMessage = "Mật khẩu không hợp lệ. Quý khách vui lòng kiểm tra lại.",
                modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
            )

            // Divider
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(FDS.Sizer.Stroke.stroke05)
                    .background(FDS.Colors.strokeDivider),
            )

            // Nhập lại mật khẩu mới với error
            FoundationEditText(
                value = "abc1234",
                onValueChange = {},
                inputType = InputType.PASSWORD,
                placeholder = "Nhập lại mật khẩu mới",
                isError = true,
                errorMessage = "Mật khẩu không hợp lệ. Quý khách vui lòng kiểm tra lại.",
                modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
            )
        }
    }
}