package com.vietinbank.core_ui.components.dialog.csat_dialog

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.dismissRippleClickable
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
@Preview
fun CsatViewIcon(
    modifier: Modifier = Modifier,
    selectedIndex: Int = -1,
    selectItem: (Int) -> Unit = {},
    isRowClickable: Boolean = false,
) {
    val icons = listOf(
        R.drawable.ic_csat_disappointed,
        R.drawable.ic_csat_bad,
        R.drawable.ic_csat_neutral,
        R.drawable.ic_csat_good,
        R.drawable.ic_csat_exellent,
    )

    Column(
        modifier = modifier
            .padding(vertical = FDS.Sizer.Padding.padding16),

    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            icons.forEachIndexed { index, icon ->
                Box(
                    modifier = Modifier
                        .weight(1f) // chia đều 5 phần
                        .aspectRatio(1f)
                        .dismissRippleClickable(
                            onClick = {
                                selectItem(index)
                            },
                        ), // giữ icon vuông (nếu muốn)
                    contentAlignment = Alignment.Center,
                ) {
                    val sizeFraction = if (selectedIndex == index && !isRowClickable) 1f else 0.8f
                    Image(
                        painter = painterResource(id = icon),
                        contentDescription = null,
                        modifier = Modifier.fillMaxSize(sizeFraction), // icon chiếm 60% box
                    )
                    if (selectedIndex != index && !isRowClickable) {
                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .background(Color.White.copy(0.5f)),
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun CsatView(
    modifier: Modifier = Modifier,
    onClick: () -> Unit = {},
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 38.dp, vertical = 24.dp),
    ) {
        FoundationText(
            text = stringResource(R.string.csat_view_text_holder),
            color = Color.White,
        )
        CsatViewIcon(
            modifier = Modifier.padding(top = 16.dp),
            isRowClickable = true,
            selectItem = {
                onClick()
            },
            selectedIndex = 1,

        )
    }
}