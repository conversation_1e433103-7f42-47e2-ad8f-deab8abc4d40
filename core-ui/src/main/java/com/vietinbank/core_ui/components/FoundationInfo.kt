package com.vietinbank.core_ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.safeClickable

@Composable
fun FoundationInfoHorizontal(
    modifier: Modifier = Modifier,
    title: String? = null,
    value: String? = null,
    icon: Int? = null,
    titleColor: Color = FoundationDesignSystem.Colors.textSecondary,
    valueColor: Color = FoundationDesignSystem.Colors.textPrimary,
    titleStyle: TextStyle = FoundationDesignSystem.Typography.bodyB2,
    valueStyle: TextStyle = FoundationDesignSystem.Typography.bodyB2Emphasized,
    onClick: (() -> Unit)? = null,
    textAlign: TextAlign = TextAlign.End,
) {
    Row(modifier = modifier.fillMaxWidth()) {
        Text(
            modifier = Modifier.weight(1f),
            text = title ?: "",
            color = titleColor,
            style = titleStyle,
        )

        Spacer(modifier = Modifier.width(FoundationDesignSystem.Sizer.Gap.gap8))

        Text(
            modifier = Modifier.weight(1f),
            text = value ?: "",
            color = valueColor,
            style = valueStyle,
            textAlign = textAlign,

        )
    }
}

@Composable
fun FoundationInfoVertical(
    modifier: Modifier = Modifier,
    title: String? = null,
    value: String? = null,
    icon: Int? = null,
    titleColor: Color = FoundationDesignSystem.Colors.textSecondary,
    valueColor: Color = FoundationDesignSystem.Colors.textPrimary,
    titleStyle: TextStyle = FoundationDesignSystem.Typography.captionCaptionL,
    valueStyle: TextStyle = FoundationDesignSystem.Typography.bodyB2Emphasized,
    isShowLine: Boolean = false,
    onClick: (() -> Unit)? = null,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .safeClickable {
                onClick?.invoke()
            },
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(FoundationDesignSystem.Sizer.Gap.gap4),
            ) {
                title?.let {
                    Text(text = it, color = titleColor, style = titleStyle)
                }

                value?.let {
                    Text(text = it, color = valueColor, style = valueStyle)
                }
            }

            icon?.let {
                Spacer(modifier = Modifier.width(FoundationDesignSystem.Sizer.Padding.padding16))
                Image(
                    painter = painterResource(icon),
                    contentDescription = null,
                )
            }
        }

        if (isShowLine) {
            FoundationDivider(modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap4))
        }
    }
}

@Preview
@Composable
fun BaseInfoHorizontalPreview() {
    FoundationInfoHorizontal(
        title = "Số tài khoản",
        value = "12345678987654",
    )
}

@Preview
@Composable
fun BaseInfoVerticalPreview() {
    Column {
        FoundationInfoVertical(
            title = "Số tài khoản",
            value = "12345678987654",
            icon = R.drawable.ic_user,
            isShowLine = true,
        )

        FoundationInfoVertical(
            modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap16),
            title = "Số tài khoản",
            value = "12345678987654",
            icon = R.drawable.ic_user,
        )
    }
}