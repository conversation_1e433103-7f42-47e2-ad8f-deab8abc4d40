package com.vietinbank.core_ui.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import com.vietinbank.core_common.models.AppBarAction
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Foundation App Bar Component
 * Transparent app bar with glass morphism buttons based on Foundation Design System
 *
 * @param title The title text to display (left-aligned below buttons)
 * @param onNavigationClick Navigation click handler (required if navigationIcon is null and not using default)
 * @param navigationIcon Optional custom navigation icon painter
 * @param isBackHome Whether to show home icon instead of back icon
 * @param actions List of action buttons to display on the right side
 * @param customActions Optional custom composable for right side actions
 * @param modifier Modifier to be applied to the app bar
 * @param isLightIcon Whether to use light icon theme (true) or dark icon theme (false)
 */
@Composable
fun FoundationAppBar(
    title: String,
    onNavigationClick: () -> Unit,
    modifier: Modifier = Modifier,
    navigationIcon: Painter? = null,
    isBackHome: Boolean = false,
    actions: List<AppBarAction> = emptyList(),
    customActions: @Composable (() -> Unit)? = null,
    isLightIcon: Boolean = true,
    titleStyle: TextStyle = FDS.Typography.headingH2,
    isSingleLineAppBar: Boolean = false,
    isCustomActionUseSafeClick: Boolean = true,
) {
    val titleColor = if (isLightIcon) FDS.Colors.tabTextActiveInline else Color.White
    val defaultNavIcon = if (isBackHome) {
        painterResource(R.drawable.ic_home)
    } else {
        painterResource(R.drawable.ic_common_back_24dp)
    }

    if (isSingleLineAppBar) {
        // Single-line layout: nav, title, and actions
        Row(
            modifier = modifier
                .fillMaxWidth()
                .padding(horizontal = FDS.Sizer.Padding.padding16),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // Navigation button
            CircularIconButton(
                icon = navigationIcon ?: defaultNavIcon,
                onClick = onNavigationClick,
                contentDescription = if (isBackHome) "Home" else "Back",
                size = CircularIconButtonSize.Medium, // Use medium size for app bar
                isLightIcon = isLightIcon,
            )

            Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap16))

            // Title next to navigation button with ellipsis
            Text(
                text = title,
                style = titleStyle,
                color = titleColor,
                modifier = Modifier.weight(1f),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )

            // Custom actions or default actions
            if (customActions != null) {
                customActions()
            } else {
                actions.forEachIndexed { index, action ->
                    if (index == 0) {
                        Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap16))
                    }
                    CircularIconButton(
                        icon = painterResource(action.icon),
                        onClick = action.onClick,
                        contentDescription = action.contentDescription,
                        size = CircularIconButtonSize.Medium,
                        isLightIcon = isLightIcon,
                        isUseSafeClick = isCustomActionUseSafeClick,
                    )
                    if (index < actions.size - 1) {
                        Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap16))
                    }
                }
            }
        }
    } else {
        // Multi-line layout : navigation row, then title below
        Column(
            modifier = modifier
                .fillMaxWidth()
                .padding(horizontal = FDS.Sizer.Padding.padding16),
        ) {
            // Button row
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                CircularIconButton(
                    icon = navigationIcon ?: defaultNavIcon,
                    onClick = onNavigationClick,
                    contentDescription = if (isBackHome) "Home" else "Back",
                    size = CircularIconButtonSize.Medium,
                    isLightIcon = isLightIcon,
                )

                Spacer(modifier = Modifier.weight(1f))

                // Custom actions or default actions
                if (customActions != null) {
                    customActions()
                } else {
                    actions.forEachIndexed { index, action ->
                        CircularIconButton(
                            icon = painterResource(action.icon),
                            onClick = action.onClick,
                            contentDescription = action.contentDescription,
                            size = CircularIconButtonSize.Medium,
                            isLightIcon = isLightIcon,
                            isUseSafeClick = isCustomActionUseSafeClick,
                        )
                        if (index < actions.size - 1) {
                            Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap16))
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))

            // Title - Left aligned with ellipsis for overflow
            Text(
                text = title,
                style = titleStyle,
                color = titleColor,
                modifier = Modifier.fillMaxWidth(),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
            )
        }
    }
}

/**
 * Preview functions for FoundationAppBar
 */
@Preview(name = "AppBar with Back Navigation", backgroundColor = 0xFF000000, showBackground = true)
@Composable
private fun FoundationAppBarBackPreview() {
    FoundationAppBar(
        title = "Chuyển tiền",
        onNavigationClick = { },
    )
}

@Preview(name = "AppBar with Home Navigation", backgroundColor = 0xFF000000, showBackground = true)
@Composable
private fun FoundationAppBarHomePreview() {
    FoundationAppBar(
        title = "Dashboard",
        onNavigationClick = { },
        isBackHome = true,
    )
}

@Preview(name = "AppBar with Actions", backgroundColor = 0xFF000000, showBackground = true)
@Composable
private fun FoundationAppBarActionsPreview() {
    FoundationAppBar(
        title = "Settings",
        onNavigationClick = { },
        actions = listOf(
            AppBarAction(
                icon = R.drawable.ic_common_back_24dp,
                contentDescription = "Notifications",
                onClick = { },
            ),
            AppBarAction(
                icon = R.drawable.ic_common_back_24dp,
                contentDescription = "Settings",
                onClick = { },
            ),
        ),
    )
}

@Preview(name = "AppBar with Dark Icons", backgroundColor = 0xFFE8F0F8, showBackground = true)
@Composable
private fun FoundationAppBarDarkIconsPreview() {
    FoundationAppBar(
        title = "Dark Theme",
        onNavigationClick = { },
        isLightIcon = false,
        actions = listOf(
            AppBarAction(
                icon = R.drawable.ic_common_back_24dp,
                contentDescription = "Notifications",
                onClick = { },
            ),
        ),
    )
}
