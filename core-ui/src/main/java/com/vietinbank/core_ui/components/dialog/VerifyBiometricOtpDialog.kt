package com.vietinbank.core_ui.components.dialog

import android.os.Bundle
import android.os.Parcelable
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.vietinbank.core_common.extensions.toMMSS
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.dialog.BaseDialog
import com.vietinbank.core_ui.base.views.StrokeLine
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.foundation.pin.PinAuthConstants
import com.vietinbank.core_ui.components.foundation.pin.PinHiddenComponent
import com.vietinbank.core_ui.components.foundation.pin.PinState
import com.vietinbank.core_ui.components.foundation.pin.StealthOtpInputField
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.dismissRippleClickable
import kotlinx.coroutines.delay
import kotlinx.parcelize.Parcelize
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Parcelize
data class VerifyBiometricOtpResult(
    val otpValue: String = "",
    val isResend: Boolean = false,
) : Parcelable

class VerifyBiometricOtpDialog : BaseDialog<VerifyBiometricOtpResult>() {
    override val resultKey: String = "VerifyBiometricOtpDialog"

    companion object {
        private const val ARG_OTP_VALIDITY = "ARG_OTP_VALIDITY"
        private const val ARG_RESEND_TIME = "ARG_RESEND_TIME"
        private const val ARG_PHONE_NUM = "ARG_PHONE_NUM"
        private const val ARG_EMAIL = "ARG_EMAIL"

        const val BIOMETRIC_KEY = "verify_biometric_result"

        fun newInstance(
            otpValiditySeconds: Int = PinAuthConstants.DEFAULT_OTP_VALIDITY,
            resendTime: Int = PinAuthConstants.DEFAULT_RESEND_COOLDOWN,
            phoneNum: String = "",
            email: String = "",
        ): VerifyBiometricOtpDialog {
            val dialog = VerifyBiometricOtpDialog().apply {
                arguments = Bundle().apply {
                    putInt(ARG_OTP_VALIDITY, otpValiditySeconds)
                    putInt(ARG_RESEND_TIME, resendTime)
                    putString(ARG_PHONE_NUM, phoneNum)
                    putString(ARG_EMAIL, email)
                }
            }
            return dialog
        }
    }

    // Dialog configuration
    override val layout: DialogLayout = DialogLayout.BottomSheet // Bottom sheet style
    override val maxWidthDp: Int = 600 // Max width for tablets
    override val allowTouchDismiss = false // Force user to make a choice
    override val requiresSecureFlag = false

    private var pinState by mutableStateOf(PinState.INPUT)
    private var reLoad by mutableStateOf(false)

    fun afterCallDone(isSuccess: Boolean, isResend: Boolean = false) {
        if (isResend) {
            reLoad = true
        } else {
            pinState = if (!isSuccess) {
                PinState.ERROR
            } else {
                PinState.SUCCESS
            }
        }
    }

    @Composable
    override fun DialogContent(
        visible: Boolean,
        onDismissRequest: () -> Unit,
        onResult: (VerifyBiometricOtpResult) -> Unit,
    ) {
        val phoneNum = arguments?.getString(ARG_PHONE_NUM) ?: ""
        val email = arguments?.getString(ARG_EMAIL) ?: ""
        val resendTime =
            arguments?.getInt(ARG_RESEND_TIME) ?: PinAuthConstants.DEFAULT_RESEND_COOLDOWN
        val validTime = arguments?.getInt(ARG_OTP_VALIDITY) ?: PinAuthConstants.DEFAULT_OTP_VALIDITY

        var validTimeLeft by remember { mutableIntStateOf(validTime) }
        LaunchedEffect(key1 = validTimeLeft) {
            if (validTimeLeft > 0) {
                delay(1000L) // chờ 1 giây
                validTimeLeft--
            }
        }

        var resendTimeLeft by remember { mutableIntStateOf(resendTime) }
        LaunchedEffect(key1 = resendTimeLeft) {
            if (resendTimeLeft > 0) {
                delay(1000L) // chờ 1 giây
                resendTimeLeft--
            }
        }

        var otpValue by remember { mutableStateOf("") }

        var isVerifyingPin by remember { mutableStateOf(false) }
        var isResendOtp by remember { mutableStateOf(false) }

        LaunchedEffect(reLoad) {
            if (reLoad) {
                resendTimeLeft = 60
                validTimeLeft = 300
                otpValue = ""
                pinState = PinState.INPUT
                reLoad = false
            }
        }

        LaunchedEffect(isVerifyingPin) {
            if (isVerifyingPin) {
                val bundle = Bundle().apply {
                    putParcelable(
                        "key_result",
                        VerifyBiometricOtpResult(
                            otpValue = otpValue,
                        ),
                    )
                }
                parentFragmentManager.setFragmentResult(BIOMETRIC_KEY, bundle)
                isVerifyingPin = false
            }
        }

        LaunchedEffect(isResendOtp) {
            if (isResendOtp) {
                val bundle = Bundle().apply {
                    putParcelable(
                        "key_result",
                        VerifyBiometricOtpResult(
                            isResend = isResendOtp,
                        ),
                    )
                }
                parentFragmentManager.setFragmentResult(BIOMETRIC_KEY, bundle)
                isResendOtp = false
            }
        }

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(FDS.Sizer.Padding.padding8),
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(FDS.Sizer.Padding.padding32))
                    .background(FDS.Colors.white)
                    .padding(vertical = FDS.Sizer.Padding.padding24),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                FoundationText(
                    text = stringResource(R.string.verify_biometric_title),
                    style = FDS.Typography.headingH3,
                    color = FDS.Colors.characterHighlighted,
                    textAlign = TextAlign.Center,
                )

                StrokeLine(modifier = Modifier.padding(vertical = FDS.Sizer.Padding.padding24))

                Column(
                    Modifier
                        .wrapContentSize()
                        .padding(horizontal = FDS.Sizer.Padding.padding24),
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    val guideText = if (email.isBlank()) {
                        stringResource(R.string.verify_biometric_description, phoneNum)
                    } else {
                        stringResource(R.string.verify_biometric_description_email, phoneNum, email)
                    }
                    FoundationText(
                        text = guideText,
                        style = FDS.Typography.bodyB2,
                        color = FDS.Colors.characterPrimary,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth(),
                    )
                    OTPInput(
                        modifier = Modifier.padding(vertical = FDS.Sizer.Padding.padding16),
                        pinState = pinState,
                        pinValue = otpValue,
                        onValueChange = {
                            if (it.length < 6) {
                                pinState = PinState.INPUT
                            }
                            otpValue = it
                        },
                    )
                    if (pinState == PinState.ERROR) {
                        FoundationText(
                            text = stringResource(R.string.verify_biometric_error),
                            style = FDS.Typography.captionCaptionL,
                            color = FDS.Colors.stateError,
                            textAlign = TextAlign.Center,
                        )
                    } else {
                        Row {
                            FoundationText(
                                text = stringResource(R.string.verify_biometric_valid_time),
                                style = FDS.Typography.captionCaptionL,
                                color = FDS.Colors.characterTertiary,
                                textAlign = TextAlign.Center,
                            )
                            FoundationText(
                                text = validTimeLeft.toMMSS(),
                                style = FDS.Typography.captionCaptionL,
                                color = FDS.Colors.characterHighlightedLighter,
                                textAlign = TextAlign.Center,
                            )
                        }
                    }
                    Spacer(modifier = Modifier.size(FDS.Sizer.Padding.padding16))
                    Row {
                        FoundationText(
                            text = stringResource(R.string.verify_biometric_resend_time_1),
                            style = FDS.Typography.captionCaptionLBold,
                            color = FDS.Colors.characterSecondary,
                            textAlign = TextAlign.Center,
                        )
                        if (resendTimeLeft > 0) {
                            FoundationText(
                                text = stringResource(
                                    R.string.verify_biometric_resend_time_2,
                                    resendTimeLeft,
                                ),
                                style = FDS.Typography.captionCaptionLBold,
                                color = FDS.Colors.characterSecondary,
                                textAlign = TextAlign.Center,
                            )
                        } else {
                            FoundationText(
                                modifier = Modifier.dismissRippleClickable {
                                    isResendOtp = true
                                },
                                text = stringResource(R.string.verify_biometric_resend),
                                style = FDS.Typography.captionCaptionLBold,
                                color = FDS.Colors.characterHighlightedLighter,
                                textAlign = TextAlign.Center,
                            )
                        }
                    }
                }
            }
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap12),
            ) {
                FoundationButton(
                    text = stringResource(R.string.biometric_verify_button_back),
                    onClick = {
                        onDismissRequest()
                    },
                    isLightButton = false,
                    modifier = Modifier.weight(1f),
                )

                FoundationButton(
                    text = stringResource(R.string.common_confirm),
                    enabled = otpValue.length == 6,
                    onClick = {
                        isVerifyingPin = true
                    },
                    modifier = Modifier.weight(1f),
                )
            }
        }
    }
}

@Composable
fun OTPInput(
    modifier: Modifier = Modifier,
    pinState: PinState,
    pinValue: String,
    onValueChange: (String) -> Unit,
    onDone: () -> Unit = {},
    enabled: Boolean = true,
) {
    val focusRequester = remember {
        FocusRequester()
    }

    var layoutHeight by remember {
        mutableIntStateOf(50)
    }
    Box(modifier = modifier) {
        PinHiddenComponent(
            modifier = Modifier.onGloballyPositioned { position ->
                layoutHeight = position.size.height
            },
            state = pinState,
            showNumbers = true,
            digits = pinValue,
            length = 6,
            onToggleVisibility = {
            },
            onRowTap = {
                focusRequester.requestFocus()
            },
            hideToggleIcon = true,
        )

        StealthOtpInputField(
            modifier = Modifier
                .fillMaxWidth()
                .height(
                    with(LocalDensity.current) {
                        layoutHeight.toDp()
                    },
                ),
            value = pinValue,
            length = 6,
            onValueChange = onValueChange,
            focusRequester = focusRequester,
            onDone = onDone,
            enabled = enabled,
        )
    }
}
