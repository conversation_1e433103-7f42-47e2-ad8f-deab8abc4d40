package com.vietinbank.core_ui.components.foundation.pin

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.text.selection.LocalTextSelectionColors
import androidx.compose.foundation.text.selection.TextSelectionColors
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.sp

/**
 * Stealth OTP Input Field - A completely invisible text field for PIN/OTP input
 *
 * This component provides a hidden text input that overlays on top of visual PIN/OTP components.
 * It handles all keyboard input while remaining completely invisible to avoid selection handles
 * and cursor artifacts.
 *
 * Features:
 * - Completely transparent (no visible cursor, selection, or handles)
 * - Fills parent size to capture taps anywhere in the container
 * - Filters input to only allow digits
 * - Automatic length limiting
 * - Proper keyboard type for PIN/OTP input
 *
 * @param value Current input value
 * @param length Maximum length of input
 * @param onValueChange Callback when input changes
 * @param focusRequester FocusRequester to control focus programmatically
 * @param onDone Optional callback when user presses Done on keyboard
 * @param enabled Whether the field is enabled for input
 * @param modifier Optional modifier for the component
 *
 * Usage example:
 * ```kotlin
 * val focusRequester = remember { FocusRequester() }
 * val keyboardController = LocalSoftwareKeyboardController.current
 *
 * Box {
 *     // Visual PIN component
 *     PinHiddenComponent(
 *         onRowTap = {
 *             focusRequester.requestFocus()
 *             keyboardController?.show()
 *         }
 *     )
 *
 *     // Invisible input overlay
 *     StealthOtpInputField(
 *         value = pinValue,
 *         length = 6,
 *         onValueChange = { pinValue = it },
 *         focusRequester = focusRequester
 *     )
 * }
 * ```
 */
@Composable
fun StealthOtpInputField(
    value: String,
    length: Int,
    onValueChange: (String) -> Unit,
    focusRequester: FocusRequester,
    modifier: Modifier = Modifier,
    onDone: (() -> Unit)? = null,
    enabled: Boolean = true,
) {
    val keyboardController = LocalSoftwareKeyboardController.current

    // Completely transparent text selection colors to hide handles
    val transparentSelectionColors = TextSelectionColors(
        handleColor = Color.Transparent,
        backgroundColor = Color.Transparent,
    )

    CompositionLocalProvider(LocalTextSelectionColors provides transparentSelectionColors) {
        BasicTextField(
            value = value,
            onValueChange = { raw ->
                // Filter to only digits and limit to max length
                val filtered = raw.filter { it.isDigit() }.take(length)
                onValueChange(filtered)
            },
            modifier = modifier
                .fillMaxWidth() // Cover width, parent controls height
                .alpha(0.01f) // Nearly invisible but still interactive
                .focusRequester(focusRequester),
            enabled = enabled,
            singleLine = true,
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Number, // Changed from NumberPassword to reduce IME transitions
                imeAction = if (value.length < length) ImeAction.Next else ImeAction.Done,
            ),
            keyboardActions = KeyboardActions(
                onDone = {
                    onDone?.invoke()
                    // DON'T hide IME here - let dialog control when to hide
                    // This prevents flicker when transitioning between steps
                },
            ),
            cursorBrush = SolidColor(Color.Transparent), // Hide cursor
            textStyle = TextStyle(
                color = Color.Transparent,
                fontSize = 1.sp, // Need actual size for IME recognition, not 0
            ),
        )
    }
}

/**
 * Extension function to request focus and show keyboard
 */
fun FocusRequester.requestFocusAndShowKeyboard(
    keyboardController: androidx.compose.ui.platform.SoftwareKeyboardController?,
) {
    this.requestFocus()
    keyboardController?.show()
}