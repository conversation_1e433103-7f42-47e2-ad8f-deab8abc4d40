package com.vietinbank.core_ui.components.text.foundation

import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.core.text.HtmlCompat
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Foundation HTML Text Component
 *
 * Renders HTML content as styled text using AnnotatedString.
 * Supports common HTML tags like <b>, <i>, <u>, <a>, <font>, etc.
 *
 * @param html The HTML string to display
 * @param modifier Modifier for the component
 * @param style Base text style from FDS.Typography (default: bodyB2)
 * @param color Default text color from FDS.Colors (default: textPrimary)
 * @param linkColor Color for links (default: textLink)
 * @param onLinkClick Click handler for links - protected with safeClickable
 * @param clickTimeWindow Time window for double-click protection
 * @param textAlign Text alignment
 * @param overflow How to handle text overflow
 * @param softWrap Whether text should break at soft line breaks
 * @param maxLines Maximum number of lines
 * @param minLines Minimum number of lines
 * @param onTextLayout Callback when text layout is calculated
 */
@Composable
fun FoundationHtmlText(
    html: String,
    modifier: Modifier = Modifier,
    style: TextStyle = FDS.Typography.bodyB2,
    color: Color = FDS.Colors.textPrimary,
    linkColor: Color = FDS.Colors.textLink,
    onLinkClick: ((String) -> Unit)? = null,
    clickTimeWindow: Long = 1000L,
    textAlign: TextAlign? = null,
    overflow: TextOverflow = TextOverflow.Clip,
    softWrap: Boolean = true,
    maxLines: Int = Int.MAX_VALUE,
    minLines: Int = 1,
    onTextLayout: ((TextLayoutResult) -> Unit)? = null,
) {
    val annotatedString = remember(html, color, linkColor) {
        parseHtmlToAnnotatedString(html, color, linkColor)
    }

    val clickableModifier = if (onLinkClick != null && annotatedString.getStringAnnotations("URL", 0, annotatedString.length).isNotEmpty()) {
        modifier.safeClickable(
            timeWindow = clickTimeWindow,
            onSafeClick = {
                // For simplicity, we'll handle the first URL annotation
                // In a real implementation, you'd need to track click position
                annotatedString.getStringAnnotations("URL", 0, annotatedString.length)
                    .firstOrNull()?.let { annotation ->
                        onLinkClick(annotation.item)
                    }
            },
        )
    } else {
        modifier
    }

    if (onTextLayout != null) {
        Text(
            text = annotatedString,
            modifier = clickableModifier,
            style = style,
            textAlign = textAlign,
            overflow = overflow,
            softWrap = softWrap,
            maxLines = maxLines,
            onTextLayout = onTextLayout,
        )
    } else {
        Text(
            text = annotatedString,
            modifier = clickableModifier,
            style = style,
            textAlign = textAlign,
            overflow = overflow,
            softWrap = softWrap,
            maxLines = maxLines,
        )
    }
}

/**
 * Parse HTML string to AnnotatedString with proper styling
 */
private fun parseHtmlToAnnotatedString(
    html: String,
    defaultColor: Color,
    linkColor: Color,
): AnnotatedString {
    return buildAnnotatedString {
        try {
            // Parse HTML using Android's HtmlCompat
            val spanned = HtmlCompat.fromHtml(html, HtmlCompat.FROM_HTML_MODE_COMPACT)

            append(spanned.toString())

            // Apply spans
            spanned.getSpans(0, spanned.length, Any::class.java).forEach { span ->
                val start = spanned.getSpanStart(span)
                val end = spanned.getSpanEnd(span)

                when (span) {
                    is android.text.style.StyleSpan -> {
                        when (span.style) {
                            android.graphics.Typeface.BOLD -> {
                                addStyle(
                                    style = SpanStyle(fontWeight = FontWeight.Bold),
                                    start = start,
                                    end = end,
                                )
                            }
                            android.graphics.Typeface.ITALIC -> {
                                addStyle(
                                    style = SpanStyle(fontStyle = FontStyle.Italic),
                                    start = start,
                                    end = end,
                                )
                            }
                            android.graphics.Typeface.BOLD_ITALIC -> {
                                addStyle(
                                    style = SpanStyle(
                                        fontWeight = FontWeight.Bold,
                                        fontStyle = FontStyle.Italic,
                                    ),
                                    start = start,
                                    end = end,
                                )
                            }
                        }
                    }
                    is android.text.style.UnderlineSpan -> {
                        addStyle(
                            style = SpanStyle(textDecoration = TextDecoration.Underline),
                            start = start,
                            end = end,
                        )
                    }
                    is android.text.style.StrikethroughSpan -> {
                        addStyle(
                            style = SpanStyle(textDecoration = TextDecoration.LineThrough),
                            start = start,
                            end = end,
                        )
                    }
                    is android.text.style.URLSpan -> {
                        addStyle(
                            style = SpanStyle(
                                color = linkColor,
                                textDecoration = TextDecoration.Underline,
                            ),
                            start = start,
                            end = end,
                        )
                        addStringAnnotation(
                            tag = "URL",
                            annotation = span.url,
                            start = start,
                            end = end,
                        )
                    }
                    is android.text.style.ForegroundColorSpan -> {
                        addStyle(
                            style = SpanStyle(color = Color(span.foregroundColor)),
                            start = start,
                            end = end,
                        )
                    }
                    is android.text.style.BackgroundColorSpan -> {
                        addStyle(
                            style = SpanStyle(background = Color(span.backgroundColor)),
                            start = start,
                            end = end,
                        )
                    }
                }
            }
        } catch (e: Exception) {
            // Fallback to plain text if HTML parsing fails
            append(html)
        }
    }
}

/**
 * Common HTML tags reference for documentation
 *
 * Supported tags:
 * - <b>, <strong>: Bold text
 * - <i>, <em>: Italic text
 * - <u>: Underlined text
 * - <strike>, <s>, <del>: Strikethrough text
 * - <a href="...">: Links (clickable if onLinkClick provided)
 * - <font color="...">: Colored text
 * - <br>: Line break
 * - <p>: Paragraph
 * - <h1> to <h6>: Headers (rendered as bold)
 * - <ul>, <ol>, <li>: Lists (basic support)
 *
 * Note: Complex HTML features like tables, images, or CSS are not supported.
 * For complex layouts, consider using native Compose components instead.
 */