package com.vietinbank.core_ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import com.vietinbank.core_ui.utils.glassMorphismEffect
import com.vietinbank.core_ui.utils.gradientFadeBorder
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

private object GlassMorphismConstants {
    // Alpha values for gradient layers
    const val GLASS_ALPHA_PRIMARY = 0.4f
    const val GLASS_ALPHA_SECONDARY = 0.2f
    const val GLASS_ALPHA_TERTIARY = 0.08f

    // Gradient positioning
    const val GRADIENT_CENTER_Y_OFFSET = -0.05f // Offset above component for top sheen
    const val GRADIENT_RADIUS_MULTIPLIER = 1.6f // Wider spread horizontally

    // Border gradient fade
    const val BORDER_FADE_START_ALPHA = 1.0f
    const val BORDER_FADE_END_ALPHA = 0.1f // Almost transparent at bottom left
    const val BORDER_FADE_END_POSITION = 0.3f // Fade covers 30% of width
}

@Composable
fun FoundationBanner(
    bannerView: (@Composable () -> Unit)? = null,
) {
    // Promotion card - Same as NewUserContent
    val promoGlassColors = listOf(
        FDS.Colors.blue300.copy(alpha = GlassMorphismConstants.GLASS_ALPHA_PRIMARY),
        FDS.Colors.blue600.copy(alpha = GlassMorphismConstants.GLASS_ALPHA_SECONDARY),
        FDS.Colors.white.copy(alpha = GlassMorphismConstants.GLASS_ALPHA_TERTIARY),
        Color.Transparent,
    )
    val promoBorderColor = FDS.Colors.blue800
    val promoCornerRadius = FDS.Sizer.Radius.radius32

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(promoCornerRadius))
            .background(FDS.Colors.backgroundDarkBlue)
            .glassMorphismEffect(
                glassColors = promoGlassColors,
                centerYOffset = GlassMorphismConstants.GRADIENT_CENTER_Y_OFFSET,
                radiusMultiplier = GlassMorphismConstants.GRADIENT_RADIUS_MULTIPLIER,
            )
            .gradientFadeBorder(
                borderColor = promoBorderColor,
                cornerRadius = promoCornerRadius,
                strokeWidth = FDS.Sizer.Stroke.stroke1,
                fadeStartAlpha = GlassMorphismConstants.BORDER_FADE_START_ALPHA,
                fadeEndAlpha = GlassMorphismConstants.BORDER_FADE_END_ALPHA,
                fadeEndPosition = GlassMorphismConstants.BORDER_FADE_END_POSITION,
            )
            .padding(FDS.Sizer.Gap.gap24),
    ) {
        bannerView?.invoke()
    }
}
