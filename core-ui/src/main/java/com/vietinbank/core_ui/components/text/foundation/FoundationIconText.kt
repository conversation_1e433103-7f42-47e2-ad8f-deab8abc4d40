package com.vietinbank.core_ui.components.text.foundation

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import com.vietinbank.core_ui.models.IconConfig
import com.vietinbank.core_ui.models.IconPosition
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Foundation Icon Text Component
 *
 * Text component with support for icons in multiple positions.
 * Icons can be placed in any of the 8 positions around the text.
 *
 * @param text The text to display
 * @param modifier Modifier for the component
 * @param icons Map of icon positions to their configurations
 * @param style Text style from FDS.Typography (default: bodyB2)
 * @param color Text color from FDS.Colors (default: textPrimary)
 * @param iconTextGap Gap between icon and text (default: 8dp - similar to drawablePadding)
 * @param verticalAlignment Vertical alignment for horizontal icons
 * @param horizontalAlignment Horizontal alignment for vertical icons
 * @param textAlign Text alignment within its container
 * @param overflow How to handle text overflow
 * @param softWrap Whether text should break at soft line breaks
 * @param maxLines Maximum number of lines for text
 * @param minLines Minimum number of lines for text
 * @param textDecoration Decoration to apply to text
 * @param onTextLayout Callback when text layout is calculated
 */
@Composable
fun FoundationIconText(
    text: String,
    modifier: Modifier = Modifier,
    icons: Map<IconPosition, IconConfig> = emptyMap(),
    style: TextStyle = FDS.Typography.bodyB2,
    color: Color = FDS.Colors.textPrimary,
    iconTextGap: Dp = FDS.Sizer.Gap.gap8,
    verticalAlignment: Alignment.Vertical = Alignment.CenterVertically,
    horizontalAlignment: Alignment.Horizontal = Alignment.Start,
    textAlign: TextAlign? = null,
    overflow: TextOverflow = TextOverflow.Clip,
    softWrap: Boolean = true,
    maxLines: Int = Int.MAX_VALUE,
    minLines: Int = 1,
    textDecoration: TextDecoration? = null,
    onTextLayout: ((TextLayoutResult) -> Unit)? = null,
) {
    Box(modifier = modifier) {
        Column(
            horizontalAlignment = horizontalAlignment,
        ) {
            // Top row (TOP_LEFT, TOP, TOP_RIGHT)
            val hasTopIcons = icons.keys.any { it in listOf(IconPosition.TOP_LEFT, IconPosition.TOP, IconPosition.TOP_RIGHT) }
            val hasBottomIcons = icons.keys.any { it in listOf(IconPosition.BOTTOM_LEFT, IconPosition.BOTTOM, IconPosition.BOTTOM_RIGHT) }

            if (hasTopIcons) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    // TOP_LEFT
                    icons[IconPosition.TOP_LEFT]?.let { config ->
                        DrawIcon(config)
                    } ?: Spacer(modifier = Modifier.size(FDS.Sizer.Icon.icon24))

                    // TOP
                    Box(modifier = Modifier.weight(1f), contentAlignment = Alignment.Center) {
                        icons[IconPosition.TOP]?.let { config ->
                            DrawIcon(config)
                        }
                    }

                    // TOP_RIGHT
                    icons[IconPosition.TOP_RIGHT]?.let { config ->
                        DrawIcon(config)
                    } ?: Spacer(modifier = Modifier.size(FDS.Sizer.Icon.icon24))
                }

                Spacer(modifier = Modifier.height(iconTextGap))
            }

            // Main content row (LEFT, Text, RIGHT)
            Row(
                verticalAlignment = verticalAlignment,
                horizontalArrangement = when {
                    hasTopIcons || hasBottomIcons -> Arrangement.Center
                    else -> Arrangement.Start
                },
                modifier = if (hasTopIcons || hasBottomIcons) Modifier.fillMaxWidth() else Modifier,
            ) {
                // LEFT icon
                icons[IconPosition.LEFT]?.let { config ->
                    DrawIcon(config)
                    Spacer(modifier = Modifier.width(iconTextGap))
                }

                // Text
                Text(
                    text = text,
                    style = style.let { baseStyle ->
                        if (textDecoration != null) {
                            baseStyle.copy(textDecoration = textDecoration)
                        } else {
                            baseStyle
                        }
                    },
                    color = color,
                    textAlign = textAlign,
                    overflow = overflow,
                    softWrap = softWrap,
                    maxLines = maxLines,
                    minLines = minLines,
                    onTextLayout = onTextLayout,
                )

                // RIGHT icon
                icons[IconPosition.RIGHT]?.let { config ->
                    Spacer(modifier = Modifier.width(iconTextGap))
                    DrawIcon(config)
                }
            }

            // Bottom row (BOTTOM_LEFT, BOTTOM, BOTTOM_RIGHT)
            if (hasBottomIcons) {
                Spacer(modifier = Modifier.height(iconTextGap))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    // BOTTOM_LEFT
                    icons[IconPosition.BOTTOM_LEFT]?.let { config ->
                        DrawIcon(config)
                    } ?: Spacer(modifier = Modifier.size(FDS.Sizer.Icon.icon24))

                    // BOTTOM
                    Box(modifier = Modifier.weight(1f), contentAlignment = Alignment.Center) {
                        icons[IconPosition.BOTTOM]?.let { config ->
                            DrawIcon(config)
                        }
                    }

                    // BOTTOM_RIGHT
                    icons[IconPosition.BOTTOM_RIGHT]?.let { config ->
                        DrawIcon(config)
                    } ?: Spacer(modifier = Modifier.size(FDS.Sizer.Icon.icon24))
                }
            }
        }
    }
}

/**
 * Internal composable to draw an icon with its configuration
 */
@Composable
private fun DrawIcon(config: IconConfig) {
    Icon(
        painter = painterResource(id = config.icon),
        contentDescription = config.contentDescription,
        modifier = Modifier.size(config.size),
        tint = config.tint,
    )
}