package com.vietinbank.core_ui.components.text.foundation

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun FoundationCounterText(
    modifier: Modifier = Modifier,
    counter: Int,
) {
    if (counter > 0) {
        Box(
            modifier = modifier
                .size(FDS.Sizer.Icon.icon24)
                .clip(CircleShape)
                .background(FDS.Colors.red400),
            contentAlignment = Alignment.Center,
        ) {
            Text(
                text = if (counter > 100) {
                    "99+"
                } else {
                    counter.toString()
                },
                color = FDS.Colors.characterInverse,
                style = FDS.Typography.captionCaptionMBold,
            )
        }
    }
}
