package com.vietinbank.core_ui.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Foundation Tab Component
 * Supports both Pill and Inline variants
 */

/**
 * Constants for Tab Component styling
 */
private object TabConstants {
    // Glass morphism - no real blur for performance
    const val GLASS_BASE_ALPHA = 0.95f // 95% opacity for base white
    const val GLASS_GRADIENT_START_ALPHA = 0.15f // 15% for gradient start
    const val GLASS_GRADIENT_END_ALPHA = 0.05f // 5% for gradient end
    const val GLASS_OVERLAY_ALPHA = 0.08f // 8% for color overlay

    // Animation
    const val TAB_ANIMATION_DURATION = 300 // milliseconds
}

/**
 * Sealed class for Tab types with encapsulated styling properties
 * Using @Immutable for Compose compiler optimizations
 */
@Immutable
sealed class TabType {
    data object Pill : TabType()
    data object Inline : TabType()
}

/**
 * Extension functions to resolve theme-dependent properties
 */
@Composable
fun TabType.resolveTextColor(selected: Boolean): Color = when {
    this is TabType.Pill && selected -> FDS.Colors.tabTextActivePill
    this is TabType.Inline && selected -> FDS.Colors.tabTextActiveInline
    else -> FDS.Colors.tabTextInactive
}

@Composable
fun TabType.resolveBackgroundColor(selected: Boolean): Color = when {
    this is TabType.Pill && selected -> FDS.Colors.tabBgActivePill
    else -> Color.Transparent
}

@Composable
fun TabType.resolveBorderColors(): Pair<Color, Color> = when (this) {
    is TabType.Pill -> Pair(Color.Transparent, Color.Transparent)
    is TabType.Inline -> Pair(FDS.Colors.tabBorderActive, FDS.Colors.tabDividerInactive)
}

@Composable
fun TabType.resolveContainerBackground(): Color = when (this) {
    is TabType.Pill -> Color.Transparent // Glass morphism handled separately
    is TabType.Inline -> FDS.Colors.tabBgContainerInline
}

@Composable
fun TabType.shouldShowGlassMorphism(): Boolean = this is TabType.Pill

@Composable
fun TabType.shouldShowTextShadow(selected: Boolean): Boolean =
    this is TabType.Pill && selected

/**
 * Individual Tab Item
 */
@Composable
fun TabItem(
    text: String,
    selected: Boolean,
    onClick: () -> Unit,
    type: TabType,
    modifier: Modifier = Modifier,
) {
    var isPressed by remember { mutableStateOf(false) }
    // Resolve colors based on type and state using extension functions
    val textColor by animateColorAsState(
        targetValue = type.resolveTextColor(selected),
        animationSpec = tween(TabConstants.TAB_ANIMATION_DURATION, easing = FastOutSlowInEasing),
        label = "TabTextColor",
    )

    val backgroundColor = type.resolveBackgroundColor(selected)

    // Resolve border colors for Inline type
    val (borderActiveColor, dividerInactiveColor) = type.resolveBorderColors()

    // Animate border color for smooth transitions
    val borderColor by animateColorAsState(
        targetValue = if (type is TabType.Inline) {
            if (selected) borderActiveColor else dividerInactiveColor
        } else {
            Color.Transparent
        },
        animationSpec = tween(TabConstants.TAB_ANIMATION_DURATION, easing = FastOutSlowInEasing),
        label = "TabBorderColor",
    )

    // Resolve dimensions in Composable context
    val tabHeight = FDS.Sizer.Tab.tabHeight
    val horizontalPadding = FDS.Sizer.Padding.padding16
    val verticalPadding = FDS.Sizer.Padding.padding8
    val pillCornerRadius = FDS.Sizer.Radius.radius32
    val borderWidthActive = FDS.Sizer.Stroke.stroke2
    val borderWidthInactive = FDS.Sizer.Stroke.stroke1

    Box(
        modifier = modifier
            .height(tabHeight)
            .then(
                when (type) {
                    is TabType.Pill ->
                        Modifier
                            .clip(RoundedCornerShape(pillCornerRadius))
                            .background(backgroundColor)
                    is TabType.Inline ->
                        Modifier
                            .drawBehind {
                                // Draw bottom border for Inline type
                                val strokeWidth = if (selected) {
                                    borderWidthActive.toPx()
                                } else {
                                    borderWidthInactive.toPx()
                                }

                                drawLine(
                                    color = borderColor,
                                    start = Offset(0f, size.height - strokeWidth / 2),
                                    end = Offset(size.width, size.height - strokeWidth / 2),
                                    strokeWidth = strokeWidth,
                                )
                            }
                },
            )
            .safeClickable(
                onPressedChanged = {
                    isPressed = it
                },
                onSafeClick = onClick,
            )
            .padding(
                horizontal = horizontalPadding,
                vertical = verticalPadding,
            ),
        contentAlignment = Alignment.Center,
    ) {
        Text(
            text = text,
            color = textColor,
            style = FDS.Typography.interactionLink.copy(
                letterSpacing = (-0.24).sp, // -2% of 12sp as per Figma
            ),
            textAlign = TextAlign.Center,
            modifier = Modifier.then(
                if (type.shouldShowTextShadow(selected)) {
                    Modifier // Text shadow will be handled separately
                } else {
                    Modifier
                },
            ),
        )
    }
}

/**
 * Tab Container with glass morphism effect
 */
@Composable
fun FoundationTabs(
    modifier: Modifier = Modifier,
    tabs: List<String>,
    selectedIndex: Int,
    onTabSelected: (Int) -> Unit,
    type: TabType = TabType.Pill,
) {
    // Pre-resolve glass morphism colors in Composable context for DrawScope
    val glassColors = if (type.shouldShowGlassMorphism()) {
        listOf(
            FDS.Colors.white.copy(alpha = TabConstants.GLASS_GRADIENT_START_ALPHA),
            FDS.Colors.white.copy(alpha = TabConstants.GLASS_GRADIENT_END_ALPHA * 2),
            FDS.Colors.blue200.copy(alpha = TabConstants.GLASS_GRADIENT_END_ALPHA),
            FDS.Colors.white.copy(alpha = TabConstants.GLASS_GRADIENT_END_ALPHA),
            Color.Transparent,
        )
    } else {
        emptyList()
    }

    // Pre-resolve overlay color for glass morphism
    val glassOverlayColor = FDS.Colors.blue300.copy(alpha = TabConstants.GLASS_OVERLAY_ALPHA)

    // Resolve dimensions in Composable context
    val containerHeight = FDS.Sizer.Tab.containerHeight
    val containerPadding = FDS.Sizer.Padding.padding8
    val pillCornerRadius = FDS.Sizer.Radius.radius32

    // Resolve border color and stroke width for Pill type
    val borderColor = FDS.Colors.glassBorder
    val borderStroke = FDS.Sizer.Stroke.stroke1

    val containerModifier = when (type) {
        is TabType.Pill -> {
            modifier
                .fillMaxWidth()
                .height(containerHeight)
                .clip(RoundedCornerShape(pillCornerRadius))
                .drawWithContent {
                    // Draw glass morphism background with pre-resolved colors
                    if (glassColors.isNotEmpty()) {
                        drawGlassMorphismBackground(
                            glassColors = glassColors,
                            overlayColor = glassOverlayColor,
                        )
                    }

                    // Draw content (tabs)
                    drawContent()

                    // Draw border on top of everything
                    drawRoundRect(
                        color = borderColor,
                        cornerRadius = CornerRadius(
                            x = pillCornerRadius.toPx(),
                            y = pillCornerRadius.toPx(),
                        ),
                        style = Stroke(width = borderStroke.toPx()),
                    )
                }
                .padding(containerPadding)
        }
        is TabType.Inline -> {
            modifier
                .fillMaxWidth()
                .background(type.resolveContainerBackground())
        }
    }

    Row(
        modifier = containerModifier,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        tabs.forEachIndexed { index, tab ->
            TabItem(
                text = tab,
                selected = index == selectedIndex,
                onClick = { onTabSelected(index) },
                type = type,
                modifier = Modifier.weight(1f),
            )
        }
    }
}

/**
 * Draw glass morphism background for Pill type
 * Creates a frosted glass effect WITHOUT blur for performance
 * Uses layered gradients to simulate glass appearance
 *
 * @param glassColors Pre-resolved colors for glass effect [white, white, blue200, white, transparent]
 * @param overlayColor Pre-resolved overlay color
 */
private fun DrawScope.drawGlassMorphismBackground(
    glassColors: List<Color>,
    overlayColor: Color,
) {
    // Multi-stop radial gradient to simulate glass depth
    // No actual blur is used to maintain performance
    val glassGradient = Brush.radialGradient(
        colors = glassColors,
        center = Offset(size.width / 2f, size.height / 2f),
        radius = size.width * 0.8f,
    )

    // Draw gradient that simulates glass
    drawRect(
        brush = glassGradient,
    )

    // Add subtle color overlay for depth
    drawRect(
        color = overlayColor,
    )
}

/**
 * Preview functions
 */
@Preview(showBackground = true, backgroundColor = 0xFF000C28)
@Composable
fun PreviewFoundationTabsPill() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
    ) {
        FoundationTabs(
            tabs = listOf("Tab 1", "Tab 2", "Tab 3"),
            selectedIndex = 0,
            onTabSelected = {},
            type = TabType.Pill,
        )
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewFoundationTabsInline() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(0.dp),
    ) {
        FoundationTabs(
            tabs = listOf("Tab 1", "Tab 2", "Tab 3"),
            selectedIndex = 0,
            onTabSelected = {},
            type = TabType.Inline,
        )
    }
}