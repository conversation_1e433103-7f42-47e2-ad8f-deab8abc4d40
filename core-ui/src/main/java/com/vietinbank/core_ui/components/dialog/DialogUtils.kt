package com.vietinbank.core_ui.components.dialog

import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import android.os.Build
import android.os.Bundle
import android.os.Parcelable
import android.view.View
import android.view.Window
import androidx.core.graphics.ColorUtils

/**
 * Utility functions for dialog operations
 */
object DialogUtils {

    /**
     * Get Parcelable from Bundle with API compatibility
     * This version is NOT reified to avoid generic type issues
     */
    @Suppress("DEPRECATION")
    fun <T : Parcelable> getParcelableCompat(bundle: Bundle, key: String): T? {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            bundle.getParcelable(key, Parcelable::class.java) as? T
        } else {
            bundle.getParcelable(key) as? T
        }
    }

    /**
     * Get Parcelable from Bundle with API compatibility and type
     * This version uses reified for convenient type inference
     */
    @Suppress("DEPRECATION")
    inline fun <reified T : Parcelable> getParcelableCompatReified(bundle: Bundle, key: String): T? {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            bundle.getParcelable(key, T::class.java)
        } else {
            bundle.getParcelable(key) as? T
        }
    }

    /**
     * Calculate effective luminance for a color
     * Uses Android's ColorUtils for accurate calculation
     */
    fun calculateEffectiveLuminance(color: Int): Double {
        return ColorUtils.calculateLuminance(color)
    }

    /**
     * Determine if a color is considered "light"
     * Returns true if the color would need dark text/icons for contrast
     */
    fun isLightColor(color: Int): Boolean {
        return calculateEffectiveLuminance(color) > 0.5
    }

    /**
     * Generate a unique dialog tag for fragment transaction
     */
    fun generateDialogTag(prefix: String): String {
        return "${prefix}_${System.currentTimeMillis()}"
    }

    /**
     * Check if a bundle contains a valid result
     */
    fun hasValidResult(bundle: Bundle, resultKey: String): Boolean {
        return bundle.containsKey(resultKey) && !bundle.getBoolean("key_cancelled", false)
    }

    /**
     * Check if a bundle represents a cancellation
     */
    fun isCancellation(bundle: Bundle): Boolean {
        return bundle.getBoolean("key_cancelled", false)
    }

    /**
     * Create a result bundle
     */
    fun <T : Parcelable> createResultBundle(result: T, cancelled: Boolean = false): Bundle {
        return Bundle().apply {
            putParcelable("key_result", result)
            putBoolean("key_cancelled", cancelled)
        }
    }

    /**
     * Create a cancellation bundle
     */
    fun createCancellationBundle(cancellationReason: String? = null): Bundle {
        return Bundle().apply {
            putBoolean("key_cancelled", true)
            cancellationReason?.let {
                putString("key_cancellation_reason", it)
            }
        }
    }

    @Suppress("DEPRECATION")
    inline fun <reified T : Parcelable> Bundle.getResult(key: String? = "key_result"): T? {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                getParcelable(key, T::class.java)
            } else {
                getParcelable(key) as? T
            }
        } catch (_: Exception) {
            null
        }
    }

    /**
     * Find the Activity from a Context by traversing the ContextWrapper chain
     */
    fun findActivity(context: Context): Activity? {
        var currentContext = context
        while (currentContext is ContextWrapper) {
            if (currentContext is Activity) {
                return currentContext
            }
            currentContext = currentContext.baseContext
        }
        return null
    }

    /**
     * Get Window from a Compose View
     * This traverses the context to find the Activity and returns its Window
     */
    fun findWindowFromView(view: View): Window? {
        val context = view.context
        val activity = findActivity(context)
        return activity?.window
    }

    /**
     * Alternative: Get Window from View using window token
     * This is more direct but might not work in all contexts
     */
    fun findWindowFromViewToken(view: View): Window? {
        return try {
            val context = view.context
            val activity = findActivity(context)
            activity?.window
        } catch (_: Exception) {
            null
        }
    }
}