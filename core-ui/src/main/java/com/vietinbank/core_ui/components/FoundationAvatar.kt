package com.vietinbank.core_ui.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.unit.Dp
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Default circular avatar placeholder with shimmer effect.
 * <PERSON>ze defaults to 40.dp (FDS icon40).
 */
@Composable
fun FoundationAvatarShimmer(
    modifier: Modifier = Modifier,
    size: Dp = FDS.Sizer.Icon.icon40,
) {
    Box(
        modifier = modifier
            .size(size)
            .clip(CircleShape)
            .shimmer(),
    )
}
