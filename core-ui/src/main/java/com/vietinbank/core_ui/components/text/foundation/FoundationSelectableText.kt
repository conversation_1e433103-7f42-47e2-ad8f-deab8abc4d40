package com.vietinbank.core_ui.components.text.foundation

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Selection indicator types for FoundationSelectableText
 */
enum class SelectionIndicator {
    /** Background color change when selected */
    Background,

    /** Border around text when selected */
    Border,

    /** Underline text when selected */
    Underline,

    /** Show checkmark icon when selected */
    CheckMark,
}

/**
 * Foundation Selectable Text Component
 *
 * Text component with selection state visualization.
 * Can show selection through background, border, underline, or checkmark.
 *
 * @param text The text to display
 * @param modifier Modifier for the component
 * @param selected Whether the text is currently selected
 * @param onSelectionChange Callback when selection state should change (optional - for toggle behavior)
 * @param style Text style from FDS.Typography (default: bodyB2)
 * @param color Text color when unselected (default: textPrimary)
 * @param selectedColor Text color when selected (default: characterHighlighted)
 * @param selectedBackgroundColor Background color when selected (default: stateActiveLighter)
 * @param selectionIndicator How to show selection state
 * @param clickTimeWindow Time window for double-click protection
 * @param enabled Whether the text is clickable
 * @param borderColor Border color when using Border indicator
 * @param borderWidth Border width when using Border indicator
 * @param cornerRadius Corner radius for background/border
 * @param padding Padding around text content
 * @param textAlign Text alignment
 * @param overflow How to handle text overflow
 * @param softWrap Whether text should break at soft line breaks
 * @param maxLines Maximum number of lines
 * @param minLines Minimum number of lines
 * @param onTextLayout Callback when text layout is calculated
 */
@Composable
fun FoundationSelectableText(
    text: String,
    modifier: Modifier = Modifier,
    selected: Boolean = false,
    onSelectionChange: ((Boolean) -> Unit)? = null,
    style: TextStyle = FDS.Typography.bodyB2,
    color: Color = FDS.Colors.textPrimary,
    selectedColor: Color = FDS.Colors.characterHighlighted,
    selectedBackgroundColor: Color = FDS.Colors.stateActiveLighter,
    selectionIndicator: SelectionIndicator = SelectionIndicator.Background,
    clickTimeWindow: Long = 1000L,
    enabled: Boolean = true,
    borderColor: Color = FDS.Colors.stateActive,
    borderWidth: Dp = FDS.Sizer.Stroke.stroke1,
    cornerRadius: Dp = FDS.Sizer.Radius.radius4,
    padding: Dp = FDS.Sizer.Padding.padding8,
    textAlign: TextAlign? = null,
    overflow: TextOverflow = TextOverflow.Clip,
    softWrap: Boolean = true,
    maxLines: Int = Int.MAX_VALUE,
    minLines: Int = 1,
    onTextLayout: ((TextLayoutResult) -> Unit)? = null,
) {
    val textColor = if (selected) selectedColor else color
    val textDecoration = if (selected && selectionIndicator == SelectionIndicator.Underline) {
        TextDecoration.Underline
    } else {
        null
    }

    val finalStyle = style.let { baseStyle ->
        if (textDecoration != null) {
            baseStyle.copy(textDecoration = textDecoration)
        } else {
            baseStyle
        }
    }

    val clickableModifier = if (onSelectionChange != null && enabled) {
        modifier.safeClickable(
            timeWindow = clickTimeWindow,
            enabled = enabled,
            onSafeClick = { onSelectionChange(!selected) },
        )
    } else {
        modifier
    }

    when (selectionIndicator) {
        SelectionIndicator.Background -> {
            Box(
                modifier = clickableModifier
                    .clip(RoundedCornerShape(cornerRadius))
                    .background(
                        color = if (selected) selectedBackgroundColor else Color.Transparent,
                    )
                    .padding(padding),
            ) {
                Text(
                    text = text,
                    style = finalStyle,
                    color = textColor,
                    textAlign = textAlign,
                    overflow = overflow,
                    softWrap = softWrap,
                    maxLines = maxLines,
                    minLines = minLines,
                    onTextLayout = onTextLayout,
                )
            }
        }

        SelectionIndicator.Border -> {
            Box(
                modifier = clickableModifier
                    .clip(RoundedCornerShape(cornerRadius))
                    .border(
                        border = if (selected) {
                            BorderStroke(borderWidth, borderColor)
                        } else {
                            BorderStroke(0.dp, Color.Transparent)
                        },
                        shape = RoundedCornerShape(cornerRadius),
                    )
                    .padding(padding),
            ) {
                Text(
                    text = text,
                    style = finalStyle,
                    color = textColor,
                    textAlign = textAlign,
                    overflow = overflow,
                    softWrap = softWrap,
                    maxLines = maxLines,
                    minLines = minLines,
                    onTextLayout = onTextLayout,
                )
            }
        }

        SelectionIndicator.Underline -> {
            Box(
                modifier = clickableModifier.padding(padding),
            ) {
                Text(
                    text = text,
                    style = finalStyle,
                    color = textColor,
                    textAlign = textAlign,
                    overflow = overflow,
                    softWrap = softWrap,
                    maxLines = maxLines,
                    minLines = minLines,
                    onTextLayout = onTextLayout,
                )
            }
        }

        SelectionIndicator.CheckMark -> {
            Row(
                modifier = clickableModifier.padding(padding),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                if (selected) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_checkbox),
                        contentDescription = "Selected",
                        modifier = Modifier.size(FDS.Sizer.Icon.icon16),
                        tint = FDS.Colors.success,
                    )
                    Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap4))
                }

                Text(
                    text = text,
                    style = finalStyle,
                    color = textColor,
                    textAlign = textAlign,
                    overflow = overflow,
                    softWrap = softWrap,
                    maxLines = maxLines,
                    minLines = minLines,
                    onTextLayout = onTextLayout,
                )
            }
        }
    }
}