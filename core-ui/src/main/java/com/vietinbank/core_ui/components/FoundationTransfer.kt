package com.vietinbank.core_ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import com.vietinbank.core_common.models.TransferResult
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun FoundationTransfer(
    modifier: Modifier = Modifier,
    accountFrom: TransferResult? = null,
    accountTo: TransferResult? = null,
    lstContent: List<Pair<String?, String?>> = emptyList(),
    imageLoader: CoilImageLoader? = null,
    contentTop: (@Composable () -> Unit)? = null,
    verticalPadding: Dp = FDS.Sizer.Gap.gap24,
    horizontalPadding: Dp = FDS.Sizer.Gap.gap24,
) {
    Box(
        modifier = modifier
            .fillMaxWidth(),

    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(AppSizer.Radius.radius32))
                .background(FDS.Colors.backgroundBgContainer)
                .padding(vertical = verticalPadding),
        ) {
            contentTop?.let {
                it()
                FoundationDivider(modifier = Modifier.padding(vertical = FDS.Sizer.Gap.gap16))
            }

            accountFrom?.let { account ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = horizontalPadding),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    account.bankIconURL?.let {
                        // icon bank
                        Box(
                            modifier = Modifier.padding(end = FDS.Sizer.Gap.gap8)
                                .size(FDS.Sizer.Icon.icon40)
                                .clip(CircleShape)
                                .background(FDS.Colors.gray50),
                            contentAlignment = Alignment.Center,
                        ) {
                            imageLoader?.LoadUrl(
                                url = it,
                                isCache = true,
                                placeholderRes = com.vietinbank.core_ui.R.drawable.ic_search,
                                errorRes = com.vietinbank.core_ui.R.drawable.ic_search,
                                modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                            )
                        }
                    }
                    account.bankIconResource?.let {
                        // icon bank
                        Box(
                            modifier = Modifier.padding(end = FDS.Sizer.Gap.gap8)
                                .size(FDS.Sizer.Icon.icon40)
                                .clip(CircleShape)
                                .background(FDS.Colors.gray50),
                            contentAlignment = Alignment.Center,
                        ) {
                            Icon(
                                painter = painterResource(it),
                                contentDescription = "ic_feature_maker_transfer_card",
                                tint = Color.Unspecified,
                                modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                            )
                        }
                    }

                    Column(modifier = Modifier.weight(1f)) {
                        // tên tài khoản
                        account.accountName?.let {
                            FoundationText(
                                text = it,
                                style = FDS.Typography.bodyB2,
                                color = FDS.Colors.characterPrimary,
                            )
                        }
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
                        // số tài khoản
                        account.accountNo?.let {
                            FoundationText(
                                text = it,
                                style = FDS.Typography.captionCaptionL,
                                color = FDS.Colors.characterSecondary,
                            )
                        }
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
                        if (account.fromAccBalance?.isNotEmpty() == true) {
                            FoundationText(
                                text = account.fromAccBalance ?: "",
                                style = FDS.Typography.captionCaptionL,
                                color = FDS.Colors.characterSecondary,
                            )
                        }
                    }
                }
            }

            accountTo?.let { account ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = FDS.Sizer.Gap.gap8, horizontal = horizontalPadding),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    FoundationDivider(modifier = Modifier.weight(1f))

                    Image(
                        modifier = Modifier.padding(horizontal = FDS.Sizer.Gap.gap8),
                        painter = painterResource(R.drawable.ic_common_arrow_20),
                        contentDescription = null,
                    )

                    FoundationDivider(modifier = Modifier.weight(1f))
                }

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = horizontalPadding),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    account.bankIconURL?.let {
                        Box(
                            modifier = Modifier.padding(end = FDS.Sizer.Gap.gap8)
                                .size(FDS.Sizer.Icon.icon40)
                                .clip(CircleShape)
                                .background(FDS.Colors.gray50),
                            contentAlignment = Alignment.Center,
                        ) {
                            imageLoader?.LoadUrl(
                                url = it,
                                isCache = true,
                                placeholderRes = com.vietinbank.core_ui.R.drawable.ic_search,
                                errorRes = com.vietinbank.core_ui.R.drawable.ic_search,
                                modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                            )
                        }
                    }

                    Column(modifier = Modifier.weight(1f)) {
                        account.accountName?.let {
                            FoundationText(
                                text = it,
                                style = FDS.Typography.bodyB2,
                                color = FDS.Colors.characterPrimary,
                            )
                        }

                        account.bankName?.let {
                            FoundationText(
                                text = it,
                                style = FDS.Typography.captionCaptionL,
                                color = FDS.Colors.characterSecondary,
                            )
                        }
                        account.accountNo?.let {
                            FoundationText(
                                text = it,
                                style = FDS.Typography.captionCaptionL,
                                color = FDS.Colors.characterSecondary,
                            )
                        }
                    }
                }
            }

            FoundationDivider(
                modifier = Modifier
                    .padding(top = FDS.Sizer.Gap.gap16)
                    .padding(horizontal = horizontalPadding),
            )

            lstContent.forEachIndexed { index, item ->
                FoundationInfoHorizontal(
                    modifier = Modifier
                        .padding(top = FDS.Sizer.Gap.gap16)
                        .padding(horizontal = horizontalPadding),
                    title = item.first ?: "",
                    value = item.second ?: "",
                )
            }
        }
    }
}

@Preview
@Composable
fun FoundationTransferPreview() {
    Column {
        FoundationTransfer(
            contentTop = {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = FDS.Sizer.Gap.gap24),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    FoundationText(
                        text = stringResource(R.string.common_transaction),
                        color = FDS.Colors.characterSecondary,
                        style = FDS.Typography.bodyB2,
                    )

                    FoundationText(
                        modifier = Modifier
                            .weight(1f)
                            .padding(start = FDS.Sizer.Gap.gap4),
                        text = "info.first",
                        color = FDS.Colors.characterPrimary,
                        style = FDS.Typography.bodyB2Emphasized,
                    )
                    FoundationStatus(statusCode = Status.Success)
                }
            },
            accountFrom = TransferResult(
                "",
                null,
                "Vietinbank",
                "Lê Thanh Trúc Phương",
                "*********",
                "Tài khoản nguồn",
            ),
            accountTo = TransferResult(
                "",
                null,

                "Vietinbank",
                "Lê Thanh Trúc Phương",
                "*********",
                "Tài khoản nguồn",
            ),
            lstContent = listOf(
                Pair("Nội dung", "Le Thanh Truc Phuong chuyen tien"),
                Pair("Phí giao dịch", "0 VND"),
                Pair("Hình thức thu phí", "Phí ngoài"),
            ),
        )

        FoundationTransfer(
            modifier = Modifier.padding(top = FDS.Sizer.Gap.gap24),
            accountFrom = TransferResult(
                "",
                null,

                "Vietinbank",
                "Lê Thanh Trúc Phương",
                "*********",
                "Tài khoản nguồn",
            ),
            accountTo = TransferResult(
                "",
                null,
                "Vietinbank",
                "Lê Thanh Trúc Phương",
                "*********",
                "Tài khoản nguồn",
            ),
            lstContent = listOf(
                Pair("Nội dung", "Le Thanh Truc Phuong chuyen tien"),
                Pair("Phí giao dịch", "0 VND"),
                Pair("Hình thức thu phí", "Phí ngoài"),
            ),
        )
    }
}