package com.vietinbank.core_ui.components.foundation.pin

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Previews for PIN Authentication Components
 * Demonstrates both states of the dialog in various conditions
 */

@Preview(name = "PIN Input - Empty", widthDp = 393, showBackground = true)
@Composable
private fun PinInputEmptyPreview() {
    AppTheme {
        InputPinVisual(
            step = PinAuthStep.InputPin(
                digits = "",
                showNumbers = false,
                state = PinState.INPUT,
                length = 6,
            ),
            onToggleVisibility = {},
            onBackClick = {},
            onContinueClick = {},
            modifier = Modifier
                .fillMaxWidth()
                .padding(FDS.Sizer.Padding.padding16),
        )
    }
}

@Preview(name = "PIN Input - Partial", widthDp = 393, showBackground = true)
@Composable
private fun PinInputPartialPreview() {
    AppTheme {
        InputPinVisual(
            step = PinAuthStep.InputPin(
                digits = "123",
                showNumbers = false,
                state = PinState.INPUT,
                length = 6,
            ),
            onToggleVisibility = {},
            onBackClick = {},
            onContinueClick = {},
            modifier = Modifier
                .fillMaxWidth()
                .padding(FDS.Sizer.Padding.padding16),
        )
    }
}

@Preview(name = "PIN Input - Show Numbers", widthDp = 393, showBackground = true)
@Composable
private fun PinInputShowNumbersPreview() {
    AppTheme {
        InputPinVisual(
            step = PinAuthStep.InputPin(
                digits = "165",
                showNumbers = true,
                state = PinState.INPUT,
                length = 6,
            ),
            onToggleVisibility = {},
            onBackClick = {},
            onContinueClick = {},
            modifier = Modifier
                .fillMaxWidth()
                .padding(FDS.Sizer.Padding.padding16),
        )
    }
}

@Preview(name = "PIN Input - Error", widthDp = 393, showBackground = true)
@Composable
private fun PinInputErrorPreview() {
    AppTheme {
        InputPinVisual(
            step = PinAuthStep.InputPin(
                digits = "123456",
                showNumbers = false,
                state = PinState.ERROR,
                length = 6,
                errorMessage = "Mã PIN không đúng. Còn 2 lần thử",
                attemptsLeft = 2,
            ),
            onToggleVisibility = {},
            onBackClick = {},
            onContinueClick = {},
            modifier = Modifier
                .fillMaxWidth()
                .padding(FDS.Sizer.Padding.padding16),
        )
    }
}

@Preview(name = "PIN Input - Verifying", widthDp = 393, showBackground = true)
@Composable
private fun PinInputVerifyingPreview() {
    AppTheme {
        InputPinVisual(
            step = PinAuthStep.InputPin(
                digits = "123456",
                showNumbers = false,
                state = PinState.INPUT,
                length = 6,
                isVerifying = true,
            ),
            onToggleVisibility = {},
            onBackClick = {},
            onContinueClick = {},
            modifier = Modifier
                .fillMaxWidth()
                .padding(FDS.Sizer.Padding.padding16),
        )
    }
}

@Preview(name = "OTP Display - Normal", widthDp = 393, showBackground = true)
@Composable
private fun OtpDisplayNormalPreview() {
    AppTheme {
        DisplayOtpVisual(
            step = PinAuthStep.DisplayOtp(
                state = PinShowState.TYPED,
                pinValues = listOf("1", "6", "5", "7", "2", "3"),
                validitySeconds = 270,
                currentValidityRemaining = 270,
                canResend = false,
                resendCooldown = 60,
                currentResendRemaining = 45,
            ),
            onResendClick = {},
            modifier = Modifier
                .fillMaxWidth()
                .padding(FDS.Sizer.Padding.padding16),
        )
    }
}

@Preview(name = "OTP Display - Can Resend", widthDp = 393, showBackground = true)
@Composable
private fun OtpDisplayCanResendPreview() {
    AppTheme {
        DisplayOtpVisual(
            step = PinAuthStep.DisplayOtp(
                state = PinShowState.TYPED,
                pinValues = listOf("1", "6", "5", "7", "2", "3"),
                validitySeconds = 270,
                currentValidityRemaining = 180,
                canResend = true,
                resendCooldown = 60,
                currentResendRemaining = 0,
            ),
            onResendClick = {},
            modifier = Modifier
                .fillMaxWidth()
                .padding(FDS.Sizer.Padding.padding16),
        )
    }
}

@Preview(name = "OTP Display - Expiring Soon", widthDp = 393, showBackground = true)
@Composable
private fun OtpDisplayExpiringSoonPreview() {
    AppTheme {
        DisplayOtpVisual(
            step = PinAuthStep.DisplayOtp(
                state = PinShowState.TYPED,
                pinValues = listOf("1", "6", "5", "7", "2", "3"),
                validitySeconds = 270,
                currentValidityRemaining = 25, // Less than 30 seconds
                canResend = false,
                resendCooldown = 60,
                currentResendRemaining = 10,
            ),
            onResendClick = {},
            modifier = Modifier
                .fillMaxWidth()
                .padding(FDS.Sizer.Padding.padding16),
        )
    }
}

@Preview(name = "OTP Display - Expired", widthDp = 393, showBackground = true)
@Composable
private fun OtpDisplayExpiredPreview() {
    AppTheme {
        DisplayOtpVisual(
            step = PinAuthStep.DisplayOtp(
                state = PinShowState.EXPIRED,
                pinValues = listOf("1", "6", "5", "7", "2", "3"),
                validitySeconds = 270,
                currentValidityRemaining = 0,
                canResend = true,
                resendCooldown = 60,
                currentResendRemaining = 0,
            ),
            onResendClick = {},
            modifier = Modifier
                .fillMaxWidth()
                .padding(FDS.Sizer.Padding.padding16),
        )
    }
}

@Preview(name = "OTP Display - Resending", widthDp = 393, showBackground = true)
@Composable
private fun OtpDisplayResendingPreview() {
    AppTheme {
        DisplayOtpVisual(
            step = PinAuthStep.DisplayOtp(
                state = PinShowState.TYPED,
                pinValues = listOf("1", "6", "5", "7", "2", "3"),
                validitySeconds = 270,
                currentValidityRemaining = 270,
                canResend = false,
                resendCooldown = 60,
                currentResendRemaining = 60,
                isResending = true,
            ),
            onResendClick = {},
            modifier = Modifier
                .fillMaxWidth()
                .padding(FDS.Sizer.Padding.padding16),
        )
    }
}

@Preview(name = "Both States - Full Flow", widthDp = 393, showBackground = true)
@Composable
private fun BothStatesPreview() {
    AppTheme {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .verticalScroll(rememberScrollState())
                .padding(FDS.Sizer.Padding.padding16),
        ) {
            // PIN Input State
            InputPinVisual(
                step = PinAuthStep.InputPin(
                    digits = "165",
                    showNumbers = false,
                    state = PinState.INPUT,
                    length = 6,
                ),
                onToggleVisibility = {},
                onBackClick = {},
                onContinueClick = {},
                modifier = Modifier.fillMaxWidth(),
            )

            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

            // OTP Display State
            DisplayOtpVisual(
                step = PinAuthStep.DisplayOtp(
                    state = PinShowState.TYPED,
                    pinValues = listOf("1", "6", "5", "7", "2", "3"),
                    validitySeconds = 270,
                    currentValidityRemaining = 270,
                    canResend = false,
                    resendCooldown = 60,
                    currentResendRemaining = 45,
                ),
                onResendClick = {},
                modifier = Modifier.fillMaxWidth(),
            )
        }
    }
}

@Preview(name = "Tablet - PIN Input", widthDp = 600, showBackground = true)
@Composable
private fun TabletPinInputPreview() {
    AppTheme {
        InputPinVisual(
            step = PinAuthStep.InputPin(
                digits = "165",
                showNumbers = false,
                state = PinState.INPUT,
                length = 6,
            ),
            onToggleVisibility = {},
            onBackClick = {},
            onContinueClick = {},
            modifier = Modifier
                .fillMaxWidth()
                .padding(FDS.Sizer.Padding.padding24),
        )
    }
}

@Preview(name = "Tablet - OTP Display", widthDp = 600, showBackground = true)
@Composable
private fun TabletOtpDisplayPreview() {
    AppTheme {
        DisplayOtpVisual(
            step = PinAuthStep.DisplayOtp(
                state = PinShowState.TYPED,
                pinValues = listOf("1", "6", "5", "7", "2", "3"),
                validitySeconds = 270,
                currentValidityRemaining = 180,
                canResend = false,
                resendCooldown = 60,
                currentResendRemaining = 30,
            ),
            onResendClick = {},
            modifier = Modifier
                .fillMaxWidth()
                .padding(FDS.Sizer.Padding.padding24),
        )
    }
}