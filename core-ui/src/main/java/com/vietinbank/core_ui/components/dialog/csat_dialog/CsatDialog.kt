package com.vietinbank.core_ui.components.dialog.csat_dialog

import android.os.Parcelable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.dialog.BaseDialog
import com.vietinbank.core_ui.components.ButtonSize
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.dialog.DialogLayout
import com.vietinbank.core_ui.components.foundation.textfield.FoundationEditText
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import kotlinx.parcelize.Parcelize
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Parcelize
data class CsatResult(
    val feedbackPoint: String,
    val feedbackComment: String,
) : Parcelable

class CsatDialog : BaseDialog<CsatResult>() {

    companion object {
        fun newInstance(): CsatDialog {
            return CsatDialog()
        }
    }
    override val resultKey: String = "csat_dialog_result"
    override val layout: DialogLayout = DialogLayout.BottomSheet // Bottom sheet style
    override val requiresSecureFlag: Boolean = true // Banking security requirement
    override val maxWidthDp: Int = 600 // Max width for tablets

    @Composable
    override fun DialogContent(
        visible: Boolean,
        onDismissRequest: () -> Unit,
        onResult: (CsatResult) -> Unit,
    ) {
        CsatDialogContent(
            onDismiss = onDismissRequest,
            onConfirm = { feedbackPoint, feedbackComment ->
                onResult(CsatResult(feedbackPoint, feedbackComment))
            },
        )
    }
}

@Composable
fun CsatDialogContent(
    onDismiss: () -> Unit,
    onConfirm: (String, String) -> Unit,
) {
    var feedbackComment by remember { mutableStateOf("") }
    var selectedFeedbackType by remember { mutableStateOf(FeedbackType.EXCELLENT) }
    var selectedIcon by remember { mutableIntStateOf(4) }

    val focusRequester = remember { FocusRequester() }
    val keyboardController = LocalSoftwareKeyboardController.current

    LaunchedEffect(selectedIcon) {
        selectedFeedbackType = when (selectedIcon) {
            0 -> {
                FeedbackType.DISAPPOINTED
            }

            1 -> {
                FeedbackType.BAD
            }

            2 -> {
                FeedbackType.NEUTRAL
            }

            3 -> {
                FeedbackType.GOOD
            }

            else -> {
                FeedbackType.EXCELLENT
            }
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = FDS.Sizer.Padding.padding8),
    ) {
        // White container with FULLY ROUNDED corners
        Surface(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
            color = FDS.Colors.white,
            shadowElevation = FDS.Effects.elevationLg,
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = FDS.Sizer.Padding.padding24),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

                CsatViewIcon(
                    isRowClickable = false,
                    selectedIndex = selectedIcon,
                    selectItem = {
                        selectedIcon = it
                    },
                )

                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

                // Message - Body B2 (16sp SemiBold) per Figma
                FoundationText(
                    text = selectedFeedbackType.title,
                    style = FDS.Typography.headingH3, // 16sp SemiBold
                    color = FDS.Colors.tabTextActiveInline, // #353C4D per Figma (character/secondary)
                    textAlign = TextAlign.Center,
                )
                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

                FoundationText(
                    text = selectedFeedbackType.content,
                    style = FDS.Typography.bodyB2, // 16sp SemiBold
                    color = FDS.Colors.textGuideLine, // #353C4D per Figma (character/secondary)
                    textAlign = TextAlign.Center,
                )

                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

                // Password field using FoundationEditText with label as placeholder
                // Matches Figma design node-id=7447-53836
                FoundationEditText(
                    value = feedbackComment,
                    onValueChange = {
                        feedbackComment = it
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .focusRequester(focusRequester),
                    placeholder = stringResource(R.string.csat_place_holder), // Label shown at top per Figma
                    hintText = stringResource(R.string.csat_hint_text),
                    maxLength = 146, // Use constant from ViewModel
                    showCharacterCounter = true, // No character counter for password in dialog
                    showBottomBorder = true, // Show border line as per Figma
                    singleLine = false,
                    imeAction = ImeAction.Done,
                    keyboardActions = KeyboardActions(
                        onDone = {
                            onConfirm(
                                (selectedIcon + 1).toString(),
                                feedbackComment,
                            )
                        },
                    ),
                    onFocusChanged = { isFocused ->
                        // Optional: Handle focus state if needed
                    },
                )

                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
            }
        }

        // Spacing between white container and buttons
        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))

        // Button area - SEPARATED from white container
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = FDS.Sizer.Padding.padding16),
            horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
        ) {
            // Secondary button - "Quay lại" (correct per user requirement)
            FoundationButton(
                text = stringResource(R.string.back_button),
                onClick = {
                    keyboardController?.hide()
                    onDismiss()
                },
                modifier = Modifier.weight(1f),
                isLightButton = false, // Dark style for secondary
                size = ButtonSize.Large,
            )

            FoundationButton(
                text = stringResource(R.string.csat_confirm_button),
                onClick = {
                    onConfirm(
                        (selectedIcon + 1).toString(),
                        feedbackComment,
                    )
                },
                modifier = Modifier.weight(1f),
                isLightButton = true,
                size = ButtonSize.Large,
            )
        }

        // Bottom padding
        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
    }
}

enum class FeedbackType(
    val point: Int,
    val title: String,
    val content: String,
) {
    DISAPPOINTED(
        point = 1,
        title = "Trải nghiệm thất vọng",
        content = "Chúng tôi rất tiếc vì Quý khách đã những trải nghiệm chưa tốt. Phản hồi của Quý khách sẽ giúp VietinBank hoàn thiện hơn, vui lòng để lại nhận xét để gửi đánh giá!",
    ),
    BAD(
        point = 2,
        title = "Trải nghiệm không tốt",
        content = "Chúng tôi rất tiếc vì Quý khách đã những trải nghiệm chưa tốt. Phản hồi của Quý khách sẽ giúp VietinBank hoàn thiện hơn, vui lòng để lại nhận xét để gửi đánh giá!",
    ),
    NEUTRAL(
        point = 3,
        title = "Trải nghiệm tạm ổn",
        content = "Xin cảm ơn Quý khách! Phản hồi của Quý khách sẽ giúp VietinBank hoàn thiện dịch vụ.",
    ),
    GOOD(
        point = 4,
        title = "Trải nghiệm tốt",
        content = "Xin cảm ơn Quý khách! Phản hồi của Quý khách sẽ giúp VietinBank hoàn thiện dịch vụ.",
    ),
    EXCELLENT(
        point = 5,
        title = "Trải nghiệm tuyệt vời",
        content = "Xin cảm ơn Quý khách! Phản hồi của Quý khách sẽ giúp VietinBank hoàn thiện dịch vụ.",
    ),
}
