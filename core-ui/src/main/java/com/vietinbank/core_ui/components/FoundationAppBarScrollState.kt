@file:OptIn(androidx.compose.material3.ExperimentalMaterial3Api::class)

package com.vietinbank.core_ui.components

import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.rememberTopAppBarState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection

/**
 * Wrapper to avoid leaking experimental Material3 types to call sites.
 */
class FoundationAppBarScrollState internal constructor(
    internal val material: androidx.compose.material3.TopAppBarScrollBehavior,
) {
    val nestedScrollConnection: NestedScrollConnection
        get() = material.nestedScrollConnection

    val collapsedFraction: Float
        get() = material.state.collapsedFraction
}

@Composable
fun rememberFoundationAppBarScrollState(): FoundationAppBarScrollState {
    val appBarState = rememberTopAppBarState()
    val behavior = TopAppBarDefaults.exitUntilCollapsedScrollBehavior(appBarState)
    return remember(behavior) { FoundationAppBarScrollState(behavior) }
}
