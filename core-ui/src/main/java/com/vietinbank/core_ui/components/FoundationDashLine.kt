package com.vietinbank.core_ui.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.unit.Dp
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun FoundationDashLine(
    modifier: Modifier = Modifier,
    brushLine: Brush,
    strokeWidth: Dp = FDS.Sizer.Stroke.stroke1,
    dashGap: Float = 8f, // khoảng trống
    dashWidth: Float = 8f, // độ dài nét dứt
) {
    Canvas(modifier = modifier.width(strokeWidth)) {
        drawLine(
            brush = brushLine,
            start = Offset(strokeWidth.toPx() / 2, 0f),
            end = Offset(strokeWidth.toPx() / 2, size.height),
            strokeWidth = strokeWidth.toPx(),
            pathEffect = PathEffect.dashPathEffect(floatArrayOf(dashWidth, dashGap), 0f),
        )
    }
}