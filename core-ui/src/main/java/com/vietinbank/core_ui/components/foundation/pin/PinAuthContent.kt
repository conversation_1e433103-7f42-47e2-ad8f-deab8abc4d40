package com.vietinbank.core_ui.components.foundation.pin

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.ButtonSize
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.text.foundation.FoundationClickableText
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.safeClickable
import kotlinx.coroutines.delay
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Visual component for PIN Input step
 * Displays the PIN entry UI with title, instruction, PIN component, and action buttons
 *
 * @param step Current InputPin state containing digits, visibility, and validation state
 * @param onToggleVisibility Callback when user toggles PIN visibility
 * @param onBackClick Callback when user clicks back button
 * @param onContinueClick Callback when user clicks continue button
 * @param onRowTap Callback when user taps the PIN row (for focus management)
 */
@Composable
fun InputPinVisual(
    step: PinAuthStep.InputPin,
    onToggleVisibility: () -> Unit,
    onBackClick: () -> Unit,
    onContinueClick: () -> Unit,
    onRowTap: () -> Unit = {},
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        // White container for content (PIN input area)
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    color = FDS.Colors.backgroundBgContainer,
                    shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
                )
                .padding(vertical = FDS.Sizer.Padding.padding24),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            // Title
            FoundationText(
                text = stringResource(R.string.pin_auth_input_title),
                style = FDS.Typography.headingH3,
                color = FDS.Colors.characterHighlighted,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
            )

            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

            // Divider
            HorizontalDivider(
                thickness = 1.dp,
                color = FDS.Colors.strokeDivider,
                modifier = Modifier.fillMaxWidth(),
            )

            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

            // Instruction text
            FoundationText(
                text = stringResource(R.string.pin_auth_input_instruction),
                style = FDS.Typography.bodyB1,
                color = FDS.Colors.characterPrimary,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = FDS.Sizer.Padding.padding24),
            )

            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

            // PIN Hidden Component with padding adjustment for container
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = FDS.Sizer.Padding.padding24),
            ) {
                PinHiddenComponent(
                    state = step.state,
                    showNumbers = step.showNumbers,
                    digits = step.digits,
                    length = step.length,
                    onToggleVisibility = onToggleVisibility,
                    onRowTap = onRowTap,
                    hideToggleIcon = false,
                    modifier = Modifier.fillMaxWidth(),
                )

                // Loading overlay when verifying
                if (step.isVerifying) {
                    Box(
                        modifier = Modifier
                            .matchParentSize()
                            .background(FDS.Colors.white.copy(alpha = 0.8f)),
                        contentAlignment = Alignment.Center,
                    ) {
                        CircularProgressIndicator(
                            color = FDS.Colors.primary,
                            modifier = Modifier.size(32.dp),
                        )
                    }
                }
            }

            // Error message if present
            if (step.errorMessage != null && step.state == PinState.ERROR) {
                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
                FoundationText(
                    text = step.errorMessage,
                    style = FDS.Typography.captionCaptionM,
                    color = FDS.Colors.stateError,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
                )
            }

            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
        } // End of white container

        // Spacing between container and buttons
        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))

        // Action buttons - OUTSIDE white container (matching Figma design)
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = FDS.Sizer.Padding.padding16),
            horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
        ) {
            // "Quay lại" button with gradient background for visibility
            Box(
                modifier = Modifier.weight(1f),
            ) {
                // Gradient background only for dark button
                Box(
                    modifier = Modifier
                        .matchParentSize()
                        .background(
                            androidx.compose.ui.graphics.Brush.verticalGradient(
                                colors = listOf(
                                    FDS.Colors.blue800,
                                    FDS.Colors.blue900,
                                ),
                                startY = 0f,
                            ),
                            shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
                        ),
                )

                FoundationButton(
                    text = stringResource(R.string.back_button),
                    onClick = onBackClick,
                    modifier = Modifier.fillMaxWidth(),
                    isLightButton = false, // Dark style (transparent)
                    size = ButtonSize.Large,
                    enabled = !step.isVerifying,
                )
            }

            // "Xác nhận" button
            FoundationButton(
                text = stringResource(R.string.continue_button),
                onClick = onContinueClick,
                modifier = Modifier.weight(1f),
                isLightButton = true, // Light style
                size = ButtonSize.Large,
                enabled = step.digits.length == step.length && !step.isVerifying,
            )
        }
    } // End of outer Column
}

/**
 * Visual component for OTP Display step
 * Displays the OTP code with timer and resend functionality
 *
 * @param step Current DisplayOtp state containing OTP values, timer, and resend state
 * @param onResendClick Callback when user clicks resend button
 * @param onRowTap Callback when user taps the OTP row (for focus management)
 */
@Composable
fun DisplayOtpVisual(
    step: PinAuthStep.DisplayOtp,
    onResendClick: () -> Unit,
    onBackClick: () -> Unit = {}, // Add back button handler
    onConfirmClick: () -> Unit = {}, // Add confirm button handler
    onRowTap: () -> Unit = {},
    modifier: Modifier = Modifier,
) {
    // Timer countdown effect
    var displayTime by remember(step.currentValidityRemaining) {
        mutableStateOf(step.currentValidityRemaining)
    }

    LaunchedEffect(step.currentValidityRemaining) {
        displayTime = step.currentValidityRemaining
        while (displayTime > 0) {
            delay(1000)
            displayTime--
        }
    }

    // Resend countdown effect
    var resendTime by remember(step.currentResendRemaining) {
        mutableStateOf(step.currentResendRemaining)
    }

    LaunchedEffect(step.currentResendRemaining) {
        resendTime = step.currentResendRemaining
        while (resendTime > 0 && !step.canResend) {
            delay(1000)
            resendTime--
        }
    }

    Column(
        modifier = modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        // White container for OTP display content
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    color = FDS.Colors.backgroundBgContainer,
                    shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
                )
                .padding(vertical = FDS.Sizer.Padding.padding24),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            // Title
            FoundationText(
                text = stringResource(R.string.pin_auth_display_title),
                style = FDS.Typography.headingH3,
                color = FDS.Colors.characterHighlighted,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
            )

            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

            // Divider
            HorizontalDivider(
                thickness = 1.dp,
                color = FDS.Colors.strokeDivider,
                modifier = Modifier.fillMaxWidth(),
            )

            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

            // Instruction text
            FoundationText(
                text = stringResource(R.string.pin_auth_display_instruction),
                style = FDS.Typography.bodyB1,
                color = FDS.Colors.characterPrimary,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = FDS.Sizer.Padding.padding24),
            )

            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

            // PIN Show Component with proper container padding
            Box(
                modifier = Modifier.fillMaxWidth(),
                contentAlignment = Alignment.Center,
            ) {
                PinShowComponent(
                    state = step.state,
                    pinValues = step.pinValues,
                    onRowTap = onRowTap,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = FDS.Sizer.Padding.padding24),
                )
            }

            // Timer text
            if (step.state != PinShowState.EXPIRED) {
                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
                FoundationText(
                    text = stringResource(
                        R.string.otp_valid_for,
                        displayTime.toTimerFormat(),
                    ),
                    style = FDS.Typography.captionCaptionLBold,
                    color = if (displayTime <= 30) {
                        FDS.Colors.stateWarning
                    } else {
                        FDS.Colors.characterTertiary
                    },
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
                )
            }

            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))

            // Resend section
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = FDS.Sizer.Padding.padding24),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                FoundationText(
                    text = stringResource(R.string.otp_not_received_question),
                    style = FDS.Typography.captionCaptionLBold,
                    color = FDS.Colors.characterSecondary,
                    textAlign = TextAlign.Center,
                )

                Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap4))

                if (step.canResend) {
                    // Clickable resend text
                    FoundationClickableText(
                        text = stringResource(R.string.resend_otp),
                        onClick = { onResendClick() },
                        style = FDS.Typography.captionCaptionLBold,
                        color = FDS.Colors.characterHighlighted,
                        modifier = Modifier.safeClickable(
                            onSafeClick = onResendClick,
                            enabled = !step.isResending,
                        ),
                    )
                } else {
                    // Countdown text
                    FoundationText(
                        text = stringResource(R.string.resend_otp_in, resendTime),
                        style = FDS.Typography.captionCaptionLBold,
                        color = FDS.Colors.characterSecondary,
                        textAlign = TextAlign.Center,
                    )
                }
            }

            // Loading indicator when resending
            if (step.isResending) {
                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
                Row(
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    CircularProgressIndicator(
                        color = FDS.Colors.primary,
                        modifier = Modifier.height(16.dp).width(16.dp),
                    )
                    Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap8))
                    FoundationText(
                        text = stringResource(R.string.sending),
                        style = FDS.Typography.captionCaptionM,
                        color = FDS.Colors.characterSecondary,
                    )
                }
            }
        } // End of white container for OTP display

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

        // Action buttons (same as PIN input for consistency)
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
        ) {
            // Back button with gradient background
            Box(
                modifier = Modifier.weight(1f),
            ) {
                // Gradient background for dark button
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(48.dp) // Standard button height
                        .background(
                            brush = Brush.linearGradient(
                                colors = listOf(
                                    FDS.Colors.glassGradient1.copy(alpha = 0.10f),
                                    FDS.Colors.glassGradient2.copy(alpha = 0.10f),
                                ),
                            ),
                            shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
                        ),
                )

                FoundationButton(
                    text = stringResource(R.string.back_button),
                    onClick = onBackClick,
                    isLightButton = false,
                    modifier = Modifier.fillMaxWidth(),
                )
            }

            // Confirm button - light style
            FoundationButton(
                text = if (step.state == PinShowState.VERIFYING) {
                    stringResource(R.string.verifying)
                } else {
                    stringResource(R.string.continue_button)
                },
                onClick = onConfirmClick,
                isLightButton = true,
                enabled = step.pinValues.all { it != "_" } && step.state != PinShowState.VERIFYING, // Disable when verifying
                modifier = Modifier.weight(1f),
            )
        }
    } // End of outer Column
}