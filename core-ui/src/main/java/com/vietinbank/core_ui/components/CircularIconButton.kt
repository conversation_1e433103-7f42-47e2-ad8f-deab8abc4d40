package com.vietinbank.core_ui.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.withTransform
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.utils.drawCircularInnerShadow
import com.vietinbank.core_ui.utils.gradientBorder
import com.vietinbank.core_ui.utils.innerShadow
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Size variants for CircularIconButton
 */
enum class CircularIconButtonSize {
    Small, // 40dp
    Medium, // 48dp
    Large, // 56dp - default from Figma
}

/**
 * Constants for CircularIconButton styling
 */
private object CircularIconButtonConstants {
    // Gradient parameters matching FoundationButtonLight
    const val GRADIENT_RADIUS_MULTIPLIER = 1.2f
    const val GRADIENT_FADE_STOP = 0.6f

    // Gradient stops for blue area limitation (bottom 50% only)
    const val GRADIENT_BLUE_END = 0.50f // Blue gradient ends at 50%
    const val GRADIENT_WHITE_START = 0.55f // White starts at 55%

    // Shadow parameters
    const val SHADOW_ELEVATION_DP = 10
    const val SHADOW_ALPHA = 0.15f
    const val INNER_SHADOW_ALPHA = 0.1f
    const val INNER_SHADOW_GRADIENT_END_Y = 20f
}

/**
 * Circular Icon Button with Glass Morphism Effect
 * Based on Foundation Design System
 *
 * Features:
 * - Radial gradient background (blue200 to white)
 * - Outer shadow for elevation
 * - Inner shadow for glass effect
 * - Size variants: Small (40dp), Medium (48dp), Large (56dp)
 * - Icon color: blue700
 */
@Composable
fun CircularIconButton(
    icon: Painter,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    size: CircularIconButtonSize = CircularIconButtonSize.Large,
    contentDescription: String? = null,
    enabled: Boolean = true,
    isLightIcon: Boolean = true,
    isUseSafeClick: Boolean = true,
) {
    // Get size values from FDS based on variant
    val buttonSize = when (size) {
        CircularIconButtonSize.Small -> FDS.Sizer.Icon.icon40
        CircularIconButtonSize.Medium -> FDS.Sizer.Icon.icon48
        CircularIconButtonSize.Large -> FDS.Sizer.Icon.icon56
    }

    // Icon size is always 24dp per Figma
    val iconSize = FDS.Sizer.Icon.icon24

    if (isLightIcon) {
        LightIconButton(
            modifier = modifier,
            isEnable = enabled,
            icon = icon,
            onClick = onClick,
            buttonSize = buttonSize,
            iconSize = iconSize,
            contentDescription = contentDescription,
            isUseSafeClick = isUseSafeClick,
        )
    } else {
        DarkIconButton(
            modifier = modifier,
            isEnable = enabled,
            icon = icon,
            onClick = onClick,
            buttonSize = buttonSize,
            iconSize = iconSize,
            contentDescription = contentDescription,
            isUseSafeClick = isUseSafeClick,
        )
    }
}

@Composable
private fun LightIconButton(
    modifier: Modifier = Modifier,
    isEnable: Boolean = true,
    icon: Painter,
    onClick: () -> Unit,
    buttonSize: Dp,
    iconSize: Dp,
    contentDescription: String? = null,
    isUseSafeClick: Boolean = true,
) {
    var isPressed by remember { mutableStateOf(false) }
    // Resolve colors for drawing operations
    val backgroundColor = FDS.Colors.white
    val iconColor = if (isEnable) FDS.Colors.blue700 else FDS.Colors.textDisabled
    val (gradientColor, gradientTransparentColor) = if (isPressed) {
        Pair(FDS.Colors.buttonGradientPrimaryPressed, FDS.Colors.buttonGradientPrimary)
    } else {
        Pair(FDS.Colors.buttonGradientPrimary, FDS.Colors.buttonGradientPrimaryTransparent)
    }
    val gradientOpacityFactor = if (isEnable) 1f else 0.2f

    Box(
        modifier = modifier
            .size(buttonSize)
            .shadow(
                elevation = FDS.Effects.elevationButton,
                shape = CircleShape,
                spotColor = Color.Black.copy(alpha = CircularIconButtonConstants.SHADOW_ALPHA),
                ambientColor = Color.Black.copy(alpha = CircularIconButtonConstants.SHADOW_ALPHA),
            )
            .clip(CircleShape)
            .drawBehind {
                // White background circle first (like FoundationButtonLight)
                drawCircle(
                    color = backgroundColor,
                    radius = this.size.minDimension / 2f,
                )

                // Radial gradient background following FoundationButtonLight pattern
                val center = Offset(this.size.width / 2f, this.size.height)
                val radius = this.size.height

                // Gradient logic matching FoundationButtonLight
                val gradient = if (isPressed) {
                    // Pressed state - simple 2-color gradient
                    Brush.radialGradient(
                        0f to gradientColor, // buttonGradientPrimaryPressed
                        1f to gradientTransparentColor, // buttonGradientPrimary
                        center = center,
                        radius = radius * CircularIconButtonConstants.GRADIENT_RADIUS_MULTIPLIER,
                    )
                } else {
                    // Default state with fade effect - blue only in bottom 50%
                    Brush.radialGradient(
                        0f to gradientColor.copy(alpha = gradientColor.alpha * gradientOpacityFactor),
                        CircularIconButtonConstants.GRADIENT_BLUE_END to gradientTransparentColor.copy(alpha = 0f),
                        CircularIconButtonConstants.GRADIENT_WHITE_START to backgroundColor,
                        1f to backgroundColor,
                        center = center,
                        radius = radius * CircularIconButtonConstants.GRADIENT_RADIUS_MULTIPLIER,
                    )
                }

                // Scale horizontally to create elliptical gradient
                val scaleX = this.size.width / this.size.height
                withTransform({
                    scale(
                        scaleX = scaleX,
                        scaleY = 1f,
                        pivot = center,
                    )
                }) {
                    drawCircle(
                        brush = gradient,
                        center = center,
                        radius = radius * CircularIconButtonConstants.GRADIENT_RADIUS_MULTIPLIER,
                    )
                }

                // Inner shadow for glass effect
                drawCircularInnerShadow(
                    alpha = CircularIconButtonConstants.INNER_SHADOW_ALPHA,
                    gradientEndY = CircularIconButtonConstants.INNER_SHADOW_GRADIENT_END_Y,
                )
            }
            .safeClickable(
                isDelayClick = isUseSafeClick,
                enabled = isEnable,
                onSafeClick = onClick,
                onPressedChanged = {
                    isPressed = it
                },
            ),
        contentAlignment = Alignment.Center,
    ) {
        Icon(
            painter = icon,
            contentDescription = contentDescription,
            tint = iconColor,
            modifier = Modifier.size(iconSize),
        )
    }
}

@Composable
private fun DarkIconButton(
    modifier: Modifier = Modifier,
    isEnable: Boolean = true,
    icon: Painter,
    onClick: () -> Unit,
    buttonSize: Dp,
    iconSize: Dp,
    contentDescription: String? = null,
    isUseSafeClick: Boolean = true,
) {
    var isPressed by remember { mutableStateOf(false) }
    val listColor = when {
        !isEnable -> listOf(FDS.Colors.buttonDarkDisablePrimary.copy(alpha = 0.1F), FDS.Colors.buttonDarkDisableSecondary.copy(alpha = 0.1F))
        isPressed -> listOf(FDS.Colors.darkButtonPressedState.copy(alpha = 0.3F), Color.White.copy(alpha = 0F))
        else -> listOf(FDS.Colors.darkButtonEnableState.copy(alpha = 0.2F), Color.White.copy(alpha = 0F))
    }
    val radiusFraction = if (isPressed) 0.75f else 0.5F
    Box(
        modifier = modifier
            .size(buttonSize)
            .clip(CircleShape)
            .radialGradientBackground(
                colors = listColor,
                radiusFraction = radiusFraction,
            )
            .gradientBorder()
            .innerShadow(
                shape = CircleShape,
                color = Color.White.copy(0.5f),
                offsetY = (-2).dp,
                blur = 2.dp,
            )
            .innerShadow(
                shape = CircleShape,
                color = Color.White.copy(0.5f),
                offsetY = (2).dp,
                blur = 2.dp,
            )
            .safeClickable(
                isDelayClick = isUseSafeClick,
                enabled = isEnable,
                onPressedChanged = {
                    isPressed = it
                },
                onSafeClick = onClick,
            ),
        contentAlignment = Alignment.Center,
    ) {
        Icon(
            painter = icon,
            contentDescription = contentDescription,
            tint = if (isEnable) Color.White else FDS.Colors.textDisabled,
            modifier = Modifier.size(iconSize),
        )
    }
}

fun Modifier.radialGradientBackground(
    colors: List<Color>,
    radiusFraction: Float = 0.75f,
    centerFraction: Offset = Offset(0.5f, 1f),
): Modifier = this.drawBehind {
    val radius = size.minDimension * radiusFraction
    val center = Offset(size.width * centerFraction.x, size.height * centerFraction.y)

    drawRect(
        brush = Brush.radialGradient(
            colors = colors,
            center = center,
            radius = radius,
        ),
    )
}

@Preview(
    name = "Circular Icon Button - Large",
    backgroundColor = 0xFF000000,
    showBackground = true,
)
@Composable
private fun CircularIconButtonLargePreview() {
    Box(modifier = Modifier.size(100.dp), contentAlignment = Alignment.Center) {
        CircularIconButton(
            icon = painterResource(R.drawable.ic_common_back_24dp),
            onClick = { },
            size = CircularIconButtonSize.Large,
        )
    }
}

@Preview(
    name = "Circular Icon Button - Medium",
    backgroundColor = 0xFF000000,
    showBackground = true,
)
@Composable
private fun CircularIconButtonMediumPreview() {
    Box(modifier = Modifier.size(100.dp), contentAlignment = Alignment.Center) {
        CircularIconButton(
            icon = painterResource(R.drawable.ic_common_back_24dp),
            onClick = { },
            size = CircularIconButtonSize.Medium,
        )
    }
}

@Preview(
    name = "Circular Icon Button - Small",
    backgroundColor = 0xFF000000,
    showBackground = true,
)
@Composable
private fun CircularIconButtonSmallPreview() {
    Box(modifier = Modifier.size(100.dp), contentAlignment = Alignment.Center) {
        CircularIconButton(
            icon = painterResource(R.drawable.ic_common_back_24dp),
            onClick = { },
            size = CircularIconButtonSize.Small,
        )
    }
}