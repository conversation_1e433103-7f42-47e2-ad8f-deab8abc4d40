package com.vietinbank.core_ui.components.text.foundation

import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import com.vietinbank.core_ui.models.IconConfig
import com.vietinbank.core_ui.models.IconPosition
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Foundation Clickable Text Component
 *
 * Interactive text component with built-in double-click protection using safeClickable.
 * Supports optional icons and customizable click behavior.
 *
 * @param text The text to display
 * @param onClick Click handler - protected against double clicks
 * @param modifier Modifier for the component
 * @param style Text style from FDS.Typography (default: interactionLink)
 * @param color Text color from FDS.Colors (default: textLink)
 * @param clickTimeWindow Time window for double-click protection in milliseconds
 * @param enabled Whether the text is clickable
 * @param rippleEnabled Whether to show ripple effect on click
 * @param underline Whether to underline the text
 * @param icons Optional icons to display with the text
 * @param iconTextGap Gap between icon and text
 * @param textAlign Text alignment
 * @param overflow How to handle text overflow
 * @param softWrap Whether text should break at soft line breaks
 * @param maxLines Maximum number of lines
 * @param minLines Minimum number of lines
 * @param onTextLayout Callback when text layout is calculated
 * @param onPressedChanged Callback when pressed state changes
 */
@Composable
fun FoundationClickableText(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    style: TextStyle = FDS.Typography.interactionLink,
    color: Color = FDS.Colors.textLink,
    clickTimeWindow: Long = 1000L,
    enabled: Boolean = true,
    rippleEnabled: Boolean = true,
    underline: Boolean = false,
    icons: Map<IconPosition, IconConfig> = emptyMap(),
    iconTextGap: Dp = FDS.Sizer.Gap.gap4,
    textAlign: TextAlign? = null,
    overflow: TextOverflow = TextOverflow.Clip,
    softWrap: Boolean = true,
    maxLines: Int = Int.MAX_VALUE,
    minLines: Int = 1,
    onTextLayout: ((TextLayoutResult) -> Unit)? = null,
    onPressedChanged: (Boolean) -> Unit = {},
) {
    val interactionSource = remember { MutableInteractionSource() }
    val indication = if (rippleEnabled) ripple() else null

    val textDecoration = if (underline) TextDecoration.Underline else null
    val finalStyle = style.let { baseStyle ->
        if (textDecoration != null) {
            baseStyle.copy(textDecoration = textDecoration)
        } else {
            baseStyle
        }
    }

    val clickableModifier = modifier.safeClickable(
        timeWindow = clickTimeWindow,
        enabled = enabled,
        onPressedChanged = onPressedChanged,
        onSafeClick = onClick,
    )

    // If no icons, just render text
    if (icons.isEmpty()) {
        Text(
            text = text,
            modifier = clickableModifier,
            style = finalStyle,
            color = if (enabled) color else FDS.Colors.textDisabled,
            textAlign = textAlign,
            overflow = overflow,
            softWrap = softWrap,
            maxLines = maxLines,
            minLines = minLines,
            onTextLayout = onTextLayout,
        )
    } else {
        // Use FoundationIconText for icon support, but with clickable modifier
        Column(
            modifier = clickableModifier,
            horizontalAlignment = when {
                icons.containsKey(IconPosition.LEFT) -> Alignment.Start
                icons.containsKey(IconPosition.RIGHT) -> Alignment.End
                else -> Alignment.CenterHorizontally
            },
        ) {
            // Top icons
            val topIcon = icons[IconPosition.TOP]
            if (topIcon != null) {
                Icon(
                    painter = painterResource(id = topIcon.icon),
                    contentDescription = topIcon.contentDescription,
                    modifier = Modifier.size(topIcon.size),
                    tint = if (enabled) topIcon.tint else FDS.Colors.textDisabled,
                )
                Spacer(modifier = Modifier.height(iconTextGap))
            }

            // Main row with left/right icons
            Row(
                verticalAlignment = Alignment.CenterVertically,
            ) {
                icons[IconPosition.LEFT]?.let { config ->
                    Icon(
                        painter = painterResource(id = config.icon),
                        contentDescription = config.contentDescription,
                        modifier = Modifier.size(config.size),
                        tint = if (enabled) config.tint else FDS.Colors.textDisabled,
                    )
                    Spacer(modifier = Modifier.width(iconTextGap))
                }

                Text(
                    text = text,
                    style = finalStyle,
                    color = if (enabled) color else FDS.Colors.textDisabled,
                    textAlign = textAlign,
                    overflow = overflow,
                    softWrap = softWrap,
                    maxLines = maxLines,
                    minLines = minLines,
                    onTextLayout = onTextLayout,
                )

                icons[IconPosition.RIGHT]?.let { config ->
                    Spacer(modifier = Modifier.width(iconTextGap))
                    Icon(
                        painter = painterResource(id = config.icon),
                        contentDescription = config.contentDescription,
                        modifier = Modifier.size(config.size),
                        tint = if (enabled) config.tint else FDS.Colors.textDisabled,
                    )
                }
            }

            // Bottom icons
            val bottomIcon = icons[IconPosition.BOTTOM]
            if (bottomIcon != null) {
                Spacer(modifier = Modifier.height(iconTextGap))
                Icon(
                    painter = painterResource(id = bottomIcon.icon),
                    contentDescription = bottomIcon.contentDescription,
                    modifier = Modifier.size(bottomIcon.size),
                    tint = if (enabled) bottomIcon.tint else FDS.Colors.textDisabled,
                )
            }
        }
    }
}