package com.vietinbank.core_ui.components.foundation.textfield.validators

/**
 * Simple validator interface for input validation
 * Used by Foundation components for validating user input
 */
fun interface Validator {
    /**
     * Validates the input string
     * @param input The string to validate
     * @return Error message if validation fails, null if validation passes
     */
    fun validate(input: String): String?
}