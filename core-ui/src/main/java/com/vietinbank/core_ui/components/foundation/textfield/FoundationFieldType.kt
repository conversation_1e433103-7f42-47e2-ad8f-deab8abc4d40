package com.vietinbank.core_ui.components.foundation.textfield

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.Crossfade
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.utils.captures.MoneyCurrency
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.foundation.textfield.transformations.AmountVisualTransformation
import com.vietinbank.core_ui.components.foundation.textfield.transformations.CardNumberVisualTransformation
import com.vietinbank.core_ui.components.foundation.textfield.transformations.PhoneNumberVisualTransformation
import com.vietinbank.core_ui.components.foundation.textfield.validators.FoundationValidators
import com.vietinbank.core_ui.components.foundation.textfield.validators.Validator
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Business constants for FoundationFieldType
 * These are NOT design tokens - they are business logic constants
 */
private object FoundationFieldTypeConstants {
    // Business rule: Standard credit/debit card has 16 digits
    const val CARD_NUMBER_LENGTH = 16

    // Business rule: Show warning when user reaches 80% of max character limit
    const val CHARACTER_WARNING_THRESHOLD = 0.8f

    // DESIGN TOKEN - But not yet available in FDS
    // TODO: Request designer to add this to Figma design system as FDS.Sizer.Field.fieldHeight
    // This is a design dimension, NOT a business constant
    const val FIELD_HEIGHT_DP = 40
}

/**
 * Field variant enum for different visual styles
 */
enum class FieldVariant {
    OUTLINED, // Default - with borders on focus/error
    FILLED, // With background, no borders
    TRANSPARENT, // No background, no borders (for login screens)
}

/**
 * Input type enum for FoundationFieldType
 */
enum class InputType {
    TEXT, // Default text input
    PASSWORD, // Password with show/hide toggle
    PHONE, // Phone number with Vietnam format
    EMAIL, // Email with email keyboard
    AMOUNT, // Currency amount with formatting
    CARD, // Card number with xxxx xxxx xxxx xxxx format
    NUMBER, // Pure number input
}

/**
 * Field state enum matching Figma design
 */
enum class FieldState {
    DEFAULT, // Default state - gray text
    ACTIVE, // Active/focused state - dark text
    SELECTED, // Selected state - blue border
    ERROR, // Error state - red border and text
}

/**
 * Configuration data class for different input types
 */
@Immutable
private data class InputConfig(
    val keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    val validator: Validator? = null,
    val leadingIcon: (@Composable () -> Unit)? = null,
    val onValueChange: ((String, (String) -> Unit) -> Unit)? = null,
)

/**
 * Get configuration for different input types
 */
@Composable
private fun getInputTypeConfig(
    inputType: InputType,
    currency: String,
): InputConfig {
    return when (inputType) {
        InputType.PASSWORD -> InputConfig(
            keyboardOptions = KeyboardOptions(keyboardType = androidx.compose.ui.text.input.KeyboardType.Password),
        )

        InputType.PHONE -> InputConfig(
            keyboardOptions = KeyboardOptions(keyboardType = androidx.compose.ui.text.input.KeyboardType.Phone),
            validator = FoundationValidators.phone,
        )

        InputType.EMAIL -> InputConfig(
            keyboardOptions = KeyboardOptions(keyboardType = androidx.compose.ui.text.input.KeyboardType.Email),
            validator = FoundationValidators.email,
        )

        InputType.AMOUNT -> InputConfig(
            keyboardOptions = KeyboardOptions(keyboardType = androidx.compose.ui.text.input.KeyboardType.Number),
            onValueChange = { newValue, originalOnChange ->
                // Only allow digits
                val filtered = newValue.filter { it.isDigit() }
                originalOnChange(filtered)
            },
        )

        InputType.CARD -> InputConfig(
            keyboardOptions = KeyboardOptions(keyboardType = androidx.compose.ui.text.input.KeyboardType.Number),
            onValueChange = { newValue, originalOnChange ->
                // Only allow digits and limit to standard card length
                val filtered = newValue.filter { it.isDigit() }
                    .take(FoundationFieldTypeConstants.CARD_NUMBER_LENGTH)
                originalOnChange(filtered)
            },
        )

        InputType.NUMBER -> InputConfig(
            keyboardOptions = KeyboardOptions(keyboardType = androidx.compose.ui.text.input.KeyboardType.Number),
        )

        InputType.TEXT -> InputConfig() // Default configuration
    }
}

/**
 * @param value The input value
 * @param onValueChange Callback when value changes
 * @param modifier Modifier for the component
 * @param variant Field variant (OUTLINED, FILLED, TRANSPARENT)
 * @param inputType Type of input (affects keyboard, validation, formatting)
 * @param height Custom height (default: 40.dp, use 64.dp for login screens)
 * @param currency Currency code for AMOUNT type (default: VND)
 * @param placeholder Placeholder text (acts as label)
 * @param enabled Whether the field is enabled
 * @param isError Whether to show error state (red border/text)
 * @param errorMessage Error message to display below field (optional)
 * @param singleLine Whether to limit to single line
 * @param leadingIcon Optional leading icon
 * @param trailingIcon Optional trailing icon
 * @param keyboardOptions Keyboard configuration
 * @param keyboardActions Keyboard actions
 * @param visualTransformation Visual transformation for input
 * @param validator Input validator (auto-shows errors)
 * @param onFocusChanged Focus change callback
 * @param maxLength Maximum character length
 * @param showCharacterCounter Whether to show character counter
 * @param pattern Regex pattern for input validation
 */
@Composable
fun FoundationFieldType(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    variant: FieldVariant = FieldVariant.TRANSPARENT,
    inputType: InputType = InputType.TEXT,
    height: Dp = FoundationFieldTypeConstants.FIELD_HEIGHT_DP.dp, // Customizable height
    currency: String = MoneyCurrency.VND.value,
    placeholder: String = "",
    enabled: Boolean = true,
    isError: Boolean = false,
    errorMessage: String? = null,
    singleLine: Boolean = true,
    leadingIcon: @Composable (() -> Unit)? = null,
    trailingIcon: @Composable (() -> Unit)? = null,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    visualTransformation: VisualTransformation = VisualTransformation.None,
    validator: Validator? = null,
    onFocusChanged: ((Boolean) -> Unit)? = null,
    maxLength: Int? = null,
    showCharacterCounter: Boolean = false,
    pattern: Regex? = null,
    isHaveClearIcon: Boolean = false,
    clearValue: () -> Unit = {},
) {
    // State management
    var isFocused by remember { mutableStateOf(false) }
    var passwordVisible by remember { mutableStateOf(false) }

    // Configure based on input type
    val config = getInputTypeConfig(inputType, currency)

    // Apply input type configurations
    val finalKeyboardOptions = if (keyboardOptions == KeyboardOptions.Default) {
        config.keyboardOptions
    } else {
        keyboardOptions
    }

    val finalVisualTransformation = if (visualTransformation == VisualTransformation.None) {
        when (inputType) {
            InputType.PASSWORD -> if (passwordVisible) {
                VisualTransformation.None
            } else {
                androidx.compose.ui.text.input.PasswordVisualTransformation()
            }

            InputType.PHONE -> PhoneNumberVisualTransformation()
            InputType.AMOUNT -> AmountVisualTransformation()
            InputType.CARD -> CardNumberVisualTransformation()
            else -> VisualTransformation.None
        }
    } else {
        visualTransformation
    }

    val finalValidator = validator ?: config.validator
    val finalOnValueChange = config.onValueChange?.let { configOnChange ->
        {
                newValue: String ->
            configOnChange(newValue, onValueChange)
        }
    } ?: onValueChange

    val finalLeadingIcon = leadingIcon ?: config.leadingIcon
    val finalTrailingIcon = trailingIcon ?: when (inputType) {
        InputType.PASSWORD -> {
            {
                Crossfade(
                    targetState = passwordVisible,
                    animationSpec = tween(200),
                    label = "password_visibility_toggle",
                ) { visible ->
                    Icon(
                        painter = painterResource(
                            id = if (visible) {
                                R.drawable.ic_dangnhap_eye_open
                            } else {
                                R.drawable.ic_dangnhap_eye_close
                            },
                        ),
                        contentDescription = if (visible) "Hide password" else "Show password",
                        tint = FDS.Colors.characterTertiary,
                        modifier = Modifier
                            .size(FDS.Sizer.Icon.icon16)
                            .safeClickable { passwordVisible = !passwordVisible },
                    )
                }
            }
        }
        else -> null
    }

    // Auto-validate if validator is provided
    val validationError = if (finalValidator != null && value.isNotEmpty()) {
        finalValidator.validate(value)
    } else {
        null
    }

    val showError = isError || validationError != null
    val displayErrorMessage = errorMessage ?: validationError

    // Determine field state
    val fieldState = when {
        showError -> FieldState.ERROR
        isFocused -> FieldState.SELECTED
        value.isNotEmpty() -> FieldState.ACTIVE
        else -> FieldState.DEFAULT
    }

    // Animated text color based on field state
    val animatedTextColor by animateColorAsState(
        targetValue = when (fieldState) {
            FieldState.ERROR -> FDS.Colors.stateError
            FieldState.SELECTED -> FDS.Colors.characterHighlighted
            FieldState.ACTIVE -> FDS.Colors.characterPrimary
            else -> FDS.Colors.characterPrimary
        },
        animationSpec = tween(200),
        label = "text_color_animation",
    )

    // Handle pattern validation
    val patternFilteredOnValueChange: (String) -> Unit = if (pattern != null) {
        { newValue ->
            if (newValue.isEmpty() || newValue.matches(pattern)) {
                finalOnValueChange(newValue)
            }
        }
    } else {
        finalOnValueChange
    }

    Column(modifier = modifier) {
        // Main field container
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(height) // Use customizable height
                .clip(RoundedCornerShape(FDS.Sizer.Radius.radius32))
                .then(
                    // Background based on variant
                    when (variant) {
                        FieldVariant.TRANSPARENT -> Modifier // No background
                        FieldVariant.FILLED -> Modifier.background(FDS.Colors.backgroundBgOnColor)
                        FieldVariant.OUTLINED -> Modifier.background(FDS.Colors.backgroundBgOnColor)
                    },
                )
                .then(
                    // Border based on variant and state
                    when (variant) {
                        FieldVariant.TRANSPARENT -> Modifier // No border for transparent
                        FieldVariant.FILLED -> Modifier // No border for filled
                        FieldVariant.OUTLINED -> when (fieldState) {
                            FieldState.SELECTED -> Modifier.border(
                                width = FDS.Sizer.Stroke.stroke1,
                                color = FDS.Colors.stateActive,
                                shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
                            )

                            FieldState.ERROR -> Modifier.border(
                                width = FDS.Sizer.Stroke.stroke1,
                                color = FDS.Colors.stateBadgeCounting,
                                shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
                            )

                            else -> Modifier
                        }
                    },
                )
                .padding(
                    horizontal = FDS.Sizer.Padding.padding16,
                    vertical = if (variant == FieldVariant.TRANSPARENT && height.value >= 64f) {
                        FDS.Sizer.Padding.padding20 // More vertical padding for login fields
                    } else {
                        FDS.Sizer.Padding.padding8
                    },
                ),
        ) {
            Row(
                modifier = Modifier.fillMaxSize(),
                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                // Leading icon
                finalLeadingIcon?.invoke()

                // Text field
                Box(modifier = Modifier.weight(1f)) {
                    BasicTextField(
                        value = value,
                        onValueChange = patternFilteredOnValueChange,
                        modifier = Modifier
                            .fillMaxWidth()
                            .onFocusChanged { focusState ->
                                isFocused = focusState.isFocused
                                onFocusChanged?.invoke(focusState.isFocused)
                            },
                        enabled = enabled,
                        singleLine = singleLine,
                        textStyle = FDS.Typography.bodyB2.copy(
                            color = animatedTextColor,
                        ),
                        keyboardOptions = finalKeyboardOptions,
                        keyboardActions = keyboardActions,
                        visualTransformation = finalVisualTransformation,
                        cursorBrush = SolidColor(
                            when (fieldState) {
                                FieldState.ERROR -> FDS.Colors.stateError
                                else -> FDS.Colors.stateActive
                            },
                        ),
                    )

                    // Placeholder
                    if (value.isEmpty()) {
                        Text(
                            text = placeholder,
                            style = FDS.Typography.bodyB2,
                            color = FDS.Colors.characterTertiary, // #afbec8
                        )
                    }
                }

                // Trailing icon
                if (isHaveClearIcon) {
                    if (value.isNotEmpty()) {
                        Icon(
                            painter = painterResource(
                                id = R.drawable.ic_field_text_clear,
                            ),
                            contentDescription = "",
                            tint = FDS.Colors.characterTertiary,
                            modifier = Modifier
                                .size(FDS.Sizer.Icon.icon16)
                                .safeClickable { clearValue() },
                        )
                    }
                } else {
                    finalTrailingIcon?.invoke()
                }
            }
        }

        // Error message with animation
        AnimatedVisibility(
            visible = showError && displayErrorMessage != null,
            enter = fadeIn(animationSpec = tween(200)) + expandVertically(animationSpec = tween(250)),
            exit = fadeOut(animationSpec = tween(150)) + shrinkVertically(animationSpec = tween(200)),
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = FDS.Sizer.Gap.gap4),
                horizontalArrangement = Arrangement.Start,
            ) {
                Text(
                    text = displayErrorMessage ?: "",
                    style = FDS.Typography.captionCaptionL,
                    color = FDS.Colors.stateError,
                    modifier = Modifier.weight(1f, fill = false),
                )
            }
        }

        // Character counter (separate from error, always visible when enabled)
        if (showCharacterCounter && maxLength != null) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = if (showError && displayErrorMessage != null) FDS.Sizer.Padding.padding2 else FDS.Sizer.Gap.gap4),
                horizontalArrangement = Arrangement.End,
            ) {
                Text(
                    text = "${value.length}/$maxLength",
                    style = FDS.Typography.captionCaptionL,
                    color = when {
                        value.length > maxLength -> FDS.Colors.stateError
                        value.length > maxLength * FoundationFieldTypeConstants.CHARACTER_WARNING_THRESHOLD -> FDS.Colors.stateWarning
                        else -> FDS.Colors.characterSecondary
                    },
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun FoundationFieldTypePreview() {
    AppTheme {
        Column(
            modifier = Modifier.padding(FDS.Sizer.Padding.padding16),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
        ) {
            // Default state
            FoundationFieldType(
                value = "",
                onValueChange = {},
                placeholder = "Tìm tính năng",
            )

            // Active state with value
            FoundationFieldType(
                value = "Chuyển tiền",
                onValueChange = {},
                placeholder = "Tìm tính năng",
            )

            // Password type
            FoundationFieldType(
                value = "",
                onValueChange = {},
                inputType = InputType.PASSWORD,
                placeholder = "Mật khẩu",
            )

            // Error state
            FoundationFieldType(
                value = "Invalid",
                onValueChange = {},
                placeholder = "Email",
                isError = true,
                errorMessage = "Email không hợp lệ",
            )
        }
    }
}