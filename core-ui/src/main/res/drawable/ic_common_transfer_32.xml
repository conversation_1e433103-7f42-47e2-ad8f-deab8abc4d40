<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="33dp"
    android:height="32dp"
    android:viewportWidth="33"
    android:viewportHeight="32">
  <path
      android:pathData="M16.375,0.5C24.935,0.5 31.875,7.44 31.875,16C31.875,24.56 24.935,31.5 16.375,31.5C7.815,31.5 0.875,24.56 0.875,16C0.875,7.44 7.815,0.5 16.375,0.5Z"
      android:fillAlpha="0.5">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="0.375"
          android:startY="0"
          android:endX="32.375"
          android:endY="32"
          android:type="linear">
        <item android:offset="0" android:color="#FF72CEFF"/>
        <item android:offset="1" android:color="#FFF5FCFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:strokeWidth="1"
      android:pathData="M16.375,0.5C24.935,0.5 31.875,7.44 31.875,16C31.875,24.56 24.935,31.5 16.375,31.5C7.815,31.5 0.875,24.56 0.875,16C0.875,7.44 7.815,0.5 16.375,0.5Z"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="0.375"
          android:startY="0"
          android:endX="32.375"
          android:endY="32"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#FF8FD9FF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M10.375,14L13.042,11.333M13.042,11.333L15.708,14M13.042,11.333V20.667M22.375,18L19.708,20.667M19.708,20.667L17.042,18M19.708,20.667V11.333"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#0F4C7A"
      android:strokeLineCap="round"/>
</vector>
