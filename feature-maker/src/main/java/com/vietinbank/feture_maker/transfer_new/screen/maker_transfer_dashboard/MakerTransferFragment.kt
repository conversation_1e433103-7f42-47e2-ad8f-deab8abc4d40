package com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_dashboard

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.UTF8
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.net.URLEncoder
import javax.inject.Inject
import kotlin.getValue

@AndroidEntryPoint
class MakerTransferFragment : BaseFragment<MakerTransferViewModel>() {
    override val viewModel: MakerTransferViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var imageLoader: CoilImageLoader
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.getNapasBankList()
        observeEvents()
    }

    private fun observeEvents() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.events.collect { event ->
                when (event) {
                    MakerTransferViewModel.MakerTransferAccountEvent.NavigateBack -> {
                        appNavigator.goToHome()
                    }

                    is MakerTransferViewModel.MakerTransferAccountEvent.ClickBankItem -> {
                        val bankItemString = Utils.g().provideGson().toJson(event.bank)
                        val encodedBankItem = URLEncoder.encode(bankItemString, UTF8)
                        appNavigator.goToMakerTransferBankChoosed(
                            typeTransfer = TransferConstants.Bundle.KEY_TRANSFER_TYPE_ACCOUNT,
                            itemStringObj = encodedBankItem,
                        )
                    }
                    is MakerTransferViewModel.MakerTransferAccountEvent.ClickTransferCard -> {
                        appNavigator.goToMakerTransferCard(
                            typeTransfer = TransferConstants.Bundle.KEY_TRANSFER_TYPE_CARD,
                        )
                    }
                    is MakerTransferViewModel.MakerTransferAccountEvent.ClickContactItem -> {
                        val contactItemString = Utils.g().provideGson().toJson(event.contactItem)
                        val encodedContactItem = URLEncoder.encode(contactItemString, UTF8)
                        appNavigator.goToMakerTransferBankChoosed(
                            typeTransfer = TransferConstants.Bundle.KEY_TRANSFER_TYPE_SAVED_BANK,
                            itemStringObj = encodedContactItem,
                        )
                    }
                    is MakerTransferViewModel.MakerTransferAccountEvent.ClickTempItem -> {
                        val contactItemString = Utils.g().provideGson().toJson(event.tempTransactionItem)
                        val encodedContactItem = URLEncoder.encode(contactItemString, UTF8)
                        appNavigator.goToMakerTransferBankChoosed(
                            typeTransfer = TransferConstants.Bundle.KEY_TRANSFER_TYPE_SAVED_TEMP,
                            itemStringObj = encodedContactItem,
                        )
                    }

                    is MakerTransferViewModel.MakerTransferAccountEvent.ClickRecentItem -> {
                        val contactItemString = Utils.g().provideGson().toJson(event.transactionInfo)
                        val encodedContactItem = URLEncoder.encode(contactItemString, UTF8)
                        appNavigator.goToMakerTransferBankChoosed(
                            typeTransfer = TransferConstants.Bundle.KEY_TRANSFER_TYPE_SAVED_RECENT,
                            itemStringObj = encodedContactItem,
                        )
                    }
                }
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.state.collectAsState()
        AppTheme {
            MakerTransferAccountScreen(
                state = uiState,
                onAction = viewModel::onAction,
                imageLoader = imageLoader,
            )
        }
    }

    override fun onBackPressed(): Boolean {
        appNavigator.goToHome()
        return true
    }
}