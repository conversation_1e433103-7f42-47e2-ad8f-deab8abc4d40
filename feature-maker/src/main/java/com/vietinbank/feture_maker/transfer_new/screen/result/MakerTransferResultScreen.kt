package com.vietinbank.feture_maker.transfer_new.screen.result

import android.text.TextUtils
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.models.AppBarAction
import com.vietinbank.core_common.models.TransferResult
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.maker.UserApprovalItem
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationStatus
import com.vietinbank.core_ui.components.FoundationTabs
import com.vietinbank.core_ui.components.FoundationTransfer
import com.vietinbank.core_ui.components.Status
import com.vietinbank.core_ui.components.TabType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.core_ui.theme.FoundationDesignSystem.Colors.colorResource
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feture_maker.transfer_new.screen.bottomSheet.SaveContactBottomSheet
import com.vietinbank.feture_maker.transfer_new.screen.bottomSheet.SaveTemplateBottomSheet
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE_ACCOUNT
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.CONFIRM_SAVE_TEMP_TRANSFER_NO
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.TAB_RESULT_REQUEST
import com.vietinbank.feture_maker.transfer_new.screen.result.MakerTransferResultViewModel.MakerTransferResultAction
import com.vietinbank.feture_maker.transfer_new.screen.result.MakerTransferResultViewModel.MakerTransferResultState
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun MakerTransferResultScreen(
    state: MakerTransferResultState,
    onAction: ((MakerTransferResultAction) -> Unit),
    imageLoader: CoilImageLoader,
) {
    val scrollState = rememberScrollState()
    val lineColor = colorResource(id = R.color.foundation_state_warning_lighter)

    Box(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding()
            .imePadding()
            .padding(top = FDS.Sizer.Gap.gap16),
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize(),
        ) {
            FoundationAppBar(
                isLightIcon = false,
                isSingleLineAppBar = true,
                title = "",
                navigationIcon = painterResource(R.drawable.ic_core_ui_home),
                onNavigationClick = { onAction.invoke(MakerTransferResultAction.OnBackPressed) },
                actions = listOf(
                    AppBarAction(
                        icon = R.drawable.ic_share,
                        contentDescription = "share",
                        onClick = { },
                    ),
                ),
            )
            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(vertical = FDS.Sizer.Gap.gap8)
                    .verticalScroll(scrollState),
            ) {
                FoundationText(
                    modifier = Modifier.padding(
                        top = FDS.Sizer.Gap.gap8,
                        start = FDS.Sizer.Gap.gap24,
                        end = FDS.Sizer.Gap.gap24,
                    ),
                    text = state.amountString,
                    style = FDS.Typography.headingH2,
                    color = FDS.Colors.white,
                )
                FoundationText(
                    modifier = Modifier.padding(
                        top = FDS.Sizer.Gap.gap8,
                        start = FDS.Sizer.Gap.gap24,
                        end = FDS.Sizer.Gap.gap24,
                    ),
                    text = state.amountReader,
                    style = FDS.Typography.bodyB1,
                    color = FDS.Colors.gray200,
                )
                FoundationTabs(
                    tabs = listOf(
                        stringResource(R.string.maker_transfer_account_result_request),
                        stringResource(R.string.maker_transfer_account_result_trans_his),
                    ),
                    selectedIndex = state.selectedTabIndex,
                    onTabSelected = { onAction.invoke(MakerTransferResultAction.SelectedTabIndex(it)) },
                    type = TabType.Pill,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = FDS.Sizer.Gap.gap24, vertical = FDS.Sizer.Gap.gap24),
                )
                if (TAB_RESULT_REQUEST == state.selectedTabIndex) {
                    if (KEY_TRANSFER_TYPE_ACCOUNT == state.isTransferTypeKey) {
                        FoundationTransfer(
                            imageLoader = imageLoader,
                            contentTop = {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(horizontal = FDS.Sizer.Gap.gap24),
                                    verticalAlignment = Alignment.CenterVertically,
                                ) {
                                    Column(
                                        modifier = Modifier.weight(1f),
                                    ) {
                                        FoundationText(
                                            text = stringResource(R.string.common_transaction_no),
                                            color = FDS.Colors.characterSecondary,
                                            style = FDS.Typography.captionCaptionL,
                                        )

                                        FoundationText(
                                            modifier = Modifier,
                                            text = state.createTransferDomain?.mtId ?: "",
                                            color = FDS.Colors.characterPrimary,
                                            style = FDS.Typography.bodyB2Emphasized,
                                        )
                                    }
                                    FoundationStatus(statusCode = Status.MakerTransferSuccess)
                                }
                            },
                            modifier = Modifier.padding(
                                horizontal = FDS.Sizer.Gap.gap8,
                            ),
                            accountFrom = TransferResult(
                                bankIconResource = com.vietinbank.feature_maker.R.drawable.ic_vietinbank,
                                accountName = state.createTransferDomain?.fullNameCreate ?: "",
                                accountNo = state.validateNapasAccountTransferDomain?.fromAcctNo
                                    ?: "",
                                fromAccBalance = "",
                            ),
                            accountTo = TransferResult(
                                bankIconURL = state.validateNapasAccountTransferDomain?.toBankIconURL
                                    ?: "",
                                bankName = state.validateNapasAccountTransferDomain?.bankDomain?.shortName
                                    ?: "",
                                accountName = state.validateNapasAccountTransferDomain?.toAcctName
                                    ?: "",
                                accountNo = state.validateNapasAccountTransferDomain?.toAcctNo
                                    ?: "",
                            ),
                            lstContent = buildList<Pair<String?, String?>> {
                                add(
                                    Pair(
                                        stringResource(R.string.maker_transfer_dashboard_content),
                                        state.validateNapasAccountTransferDomain?.remark ?: "",
                                    ),
                                )

                                add(
                                    Pair(
                                        stringResource(R.string.maker_transfer_dashboard_fee),
                                        Utils.g().getDotMoneyHasCcy(
                                            state.validateNapasAccountTransferDomain?.feeVat ?: "",
                                            state.validateNapasAccountTransferDomain?.currency
                                                ?: "",
                                        ),
                                    ),
                                )

                                if (state.isTypeSplitTransfer) {
                                    add(
                                        Pair(
                                            stringResource(R.string.maker_transfer_dashboard_fee_split),
                                            state.totalVatSplitTransfer,
                                        ),
                                    )
                                }

                                add(
                                    Pair(
                                        stringResource(R.string.maker_transfer_dashboard_type_fee),
                                        TransferConstants.Transfer.getFeeMethodName(
                                            state.validateNapasAccountTransferDomain?.feePayMethod
                                                ?: "",
                                        ),
                                    ),
                                )

                                if (state.isTypeSplitTransfer) {
                                    addAll(
                                        listOf(
                                            Pair(
                                                stringResource(
                                                    R.string.maker_transfer_split_number_content_transaction,
                                                    state.validateNapasAccountTransferDomain?.subTransactionCount
                                                        ?: "",
                                                ),
                                                Utils.g().getDotMoneyHasCcy(
                                                    state.validateNapasAccountTransferDomain?.subSplitTransAmount
                                                        ?: "",
                                                    state.validateNapasAccountTransferDomain?.currency
                                                        ?: "",
                                                ),
                                            ),
                                            Pair(
                                                stringResource(
                                                    R.string.maker_transfer_split_number_content_transaction,
                                                    state.validateNapasAccountTransferDomain?.subTransactionsRemainderCount
                                                        ?: "",
                                                ),
                                                Utils.g().getDotMoneyHasCcy(
                                                    state.validateNapasAccountTransferDomain?.subTransactionsRemainderAmount
                                                        ?: "",
                                                    state.validateNapasAccountTransferDomain?.currency
                                                        ?: "",
                                                ),
                                            ),
                                        ),
                                    )
                                }

                                if (TextUtils.isEmpty(state.validateNapasAccountTransferDomain?.processDate)) {
                                    addAll(
                                        listOf(
                                            Pair(
                                                stringResource(R.string.maker_transfer_dashboard_transfer_date_time_transfer),
                                                stringResource(R.string.maker_transfer_dashboard_now),
                                            ),
                                            Pair(
                                                stringResource(R.string.maker_transfer_dashboard_transfer_date_time_transfer_maker),
                                                state.createTransferDomain?.createdDate
                                                    ?: "",
                                            ),
                                        ),
                                    )
                                } else {
                                    addAll(
                                        listOf(
                                            Pair(
                                                stringResource(R.string.maker_transfer_dashboard_transfer_date_time_transfer),
                                                stringResource(R.string.maker_transfer_dashboard_schedule),
                                            ),
                                            Pair(
                                                stringResource(R.string.maker_transfer_dashboard_transfer_date_schedule),
                                                state.validateNapasAccountTransferDomain?.processDate
                                                    ?: "",
                                            ),
                                        ),
                                    )
                                }

//                                if (state.validateNapasAccountTransferDomain?.nextApprovers?.isNotEmpty() == true) {
//                                    onAction.invoke(MakerTransferResultAction.OnGetNextApproversListString)
//                                    add(
//                                        Pair(
//                                            stringResource(R.string.maker_transfer_account_confirm_approver),
//                                            state.listApproverComfirmString,
//                                        ),
//                                    )
//                                }
                            },

                        )
                    } else {
                        FoundationTransfer(
                            imageLoader = imageLoader,
                            contentTop = {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(horizontal = FDS.Sizer.Gap.gap24),
                                    verticalAlignment = Alignment.CenterVertically,
                                ) {
                                    Column(
                                        modifier = Modifier.weight(1f),
                                    ) {
                                        FoundationText(
                                            text = stringResource(R.string.common_transaction_no),
                                            color = FDS.Colors.characterSecondary,
                                            style = FDS.Typography.bodyB2,
                                        )

                                        FoundationText(
                                            modifier = Modifier,
                                            text = state.createTransferDomain?.mtId ?: "",
                                            color = FDS.Colors.characterPrimary,
                                            style = FDS.Typography.bodyB2Emphasized,
                                        )
                                    }
                                    FoundationStatus(statusCode = Status.MakerTransferSuccess)
                                }
                            },
                            modifier = Modifier.padding(
                                horizontal = FDS.Sizer.Gap.gap8,
                            ),
                            accountFrom = TransferResult(
                                bankIconResource = com.vietinbank.feature_maker.R.drawable.ic_vietinbank,
                                accountName = state.createTransferDomain?.fullNameCreate
                                    ?: "",
                                accountNo = state.validateNapasCardTransferDomains?.fromAcctNo
                                    ?: "",
                                fromAccBalance = state.validateNapasCardTransferDomains?.fromAccBalance
                                    ?: "",
                            ),
                            accountTo = TransferResult(
                                bankIconURL = state.validateNapasCardTransferDomains?.toBankIconURL
                                    ?: "",
                                bankName = state.validateNapasCardTransferDomains?.toBankName ?: "",
                                accountName = state.validateNapasCardTransferDomains?.toCardName
                                    ?: "",
                                accountNo = state.validateNapasCardTransferDomains?.toCardNo ?: "",
                            ),
                            lstContent = buildList<Pair<String, String>> {
                                add(
                                    Pair(
                                        stringResource(R.string.maker_transfer_dashboard_content),
                                        state.validateNapasCardTransferDomains?.remark ?: "",
                                    ),
                                )

                                add(
                                    Pair(
                                        stringResource(R.string.maker_transfer_dashboard_fee),
                                        Utils.g().getDotMoneyHasCcy(
                                            state.validateNapasCardTransferDomains?.feeVat ?: "",
                                            state.validateNapasCardTransferDomains?.currency ?: "",
                                        ),
                                    ),
                                )

                                add(
                                    Pair(
                                        stringResource(R.string.maker_transfer_dashboard_type_fee),
                                        TransferConstants.Transfer.getFeeMethodName(
                                            state.validateNapasCardTransferDomains?.feePayMethod
                                                ?: "",
                                        ),
                                    ),
                                )

                                if (TextUtils.isEmpty(state.validateNapasCardTransferDomains?.processDate)) {
                                    addAll(
                                        listOf(
                                            Pair(
                                                stringResource(R.string.maker_transfer_dashboard_transfer_date_time_transfer),
                                                stringResource(R.string.maker_transfer_dashboard_now),
                                            ),
                                            Pair(
                                                stringResource(R.string.maker_transfer_dashboard_transfer_date_time_transfer_maker),
                                                state.createTransferDomain?.createdDate
                                                    ?: "",
                                            ),
                                        ),
                                    )
                                } else {
                                    addAll(
                                        listOf(
                                            Pair(
                                                stringResource(R.string.maker_transfer_dashboard_transfer_date_time_transfer),
                                                stringResource(R.string.maker_transfer_dashboard_schedule),
                                            ),
                                            Pair(
                                                stringResource(R.string.maker_transfer_dashboard_transfer_date_schedule),
                                                state.validateNapasCardTransferDomains?.processDate
                                                    ?: "",
                                            ),
                                        ),
                                    )
                                }

//                                if (state.validateNapasCardTransferDomains?.nextApprovers?.isNotEmpty() == true) {
//                                    onAction.invoke(MakerTransferResultAction.OnGetNextApproversListString)
//                                    add(
//                                        Pair(
//                                            stringResource(R.string.maker_transfer_account_confirm_approver),
//                                            state.listApproverComfirmString,
//                                        ),
//                                    )
//                                }
                            },
                        )
                    }

                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(
                                horizontal = FDS.Sizer.Gap.gap8,
                                vertical = FDS.Sizer.Gap.gap4,
                            )
                            .clip(RoundedCornerShape(AppSizer.Radius.radius32))
                            .background(FDS.Colors.backgroundBgContainer)
                            .height(FDS.Sizer.Gap.gap64),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        if (!state.isSaveTempSuccess) {
                            Row(
                                modifier = Modifier
                                    .weight(1f)
                                    .safeClickable {
                                        onAction.invoke(
                                            MakerTransferResultAction.OnShowSaveTempBottomSheet(
                                                true,
                                            ),
                                        )
                                    }
                                    .fillMaxSize(),
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Center,
                            ) {
                                Icon(
                                    painter = painterResource(com.vietinbank.feature_maker.R.drawable.ic_feature_transfer_copy),
                                    contentDescription = "ic_feature_transfer_copy",
                                    tint = Color.Unspecified,
                                )
                                Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap4))
                                FoundationText(
                                    modifier = Modifier,
                                    text = stringResource(R.string.maker_transfer_account_result_save_template),
                                    color = FDS.Colors.characterHighlighted,
                                    style = FDS.Typography.interactionSmallButton,
                                )
                            }
                        } else {
                            Column(
                                modifier = Modifier
                                    .padding(start = FDS.Sizer.Gap.gap16)
                                    .weight(1f)
                                    .fillMaxSize()
                                    .safeClickable {},
                                verticalArrangement = Arrangement.Center,
                            ) {
                                FoundationText(
                                    modifier = Modifier,
                                    text = stringResource(R.string.maker_transfer_account_result_save_template),
                                    color = FDS.Colors.characterSecondary,
                                    style = FDS.Typography.captionCaptionL,
                                )
                                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
                                FoundationText(
                                    modifier = Modifier,
                                    text = state.customerCodeTemp,
                                    maxLines = 1,
                                    color = FDS.Colors.characterPrimary,
                                    style = FDS.Typography.bodyB2Emphasized,
                                )
                            }
                        }

                        VerticalDivider(
                            modifier = Modifier
                                .height(FDS.Sizer.Gap.gap60),
                            color = FDS.Colors.gray100,
                            thickness = FDS.Sizer.Stroke.stroke1,
                        )
                        if (!state.isSaveContactSuccess) {
                            Row(
                                modifier = Modifier
                                    .weight(1f)
                                    .fillMaxSize()
                                    .safeClickable {
                                        onAction.invoke(
                                            MakerTransferResultAction.OnShowSaveContactBottomSheet(
                                                true,
                                            ),
                                        )
                                    },
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Center,
                            ) {
                                Icon(
                                    painter = painterResource(com.vietinbank.feature_maker.R.drawable.ic_feature_maker_contact),
                                    contentDescription = "ic_feature_maker_contact",
                                    tint = Color.Unspecified,
                                )
                                Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap4))
                                FoundationText(
                                    modifier = Modifier,
                                    text = stringResource(R.string.maker_transfer_account_result_save_contact),
                                    color = FDS.Colors.characterHighlighted,
                                    style = FDS.Typography.interactionSmallButton,
                                )
                            }
                        } else {
                            Column(
                                modifier = Modifier
                                    .padding(start = FDS.Sizer.Gap.gap16)
                                    .weight(1f)
                                    .fillMaxSize()
                                    .safeClickable {},
                                verticalArrangement = Arrangement.Center,
                            ) {
                                FoundationText(
                                    modifier = Modifier,
                                    text = stringResource(R.string.maker_transfer_account_result_save_contact),
                                    color = FDS.Colors.characterSecondary,
                                    style = FDS.Typography.captionCaptionL,
                                )
                                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
                                FoundationText(
                                    modifier = Modifier,
                                    text = state.customerCodeContact,
                                    maxLines = 1,
                                    color = FDS.Colors.characterPrimary,
                                    style = FDS.Typography.bodyB2Emphasized,
                                )
                            }
                        }
                    }
                } else {
                    Column(
                        modifier = Modifier
                            .heightIn(max = FDS.Sizer.Gap.gap300)
                            .padding(horizontal = FDS.Sizer.Gap.gap8)
                            .clip(RoundedCornerShape(AppSizer.Radius.radius32))
                            .background(FDS.Colors.backgroundBgContainer)
                            .padding(horizontal = FDS.Sizer.Gap.gap24),
                    ) {
                        if (state.createTransferDomain?.userApprovalList?.isNotEmpty() != false) {
                            LazyColumn(
                                modifier = Modifier
                                    .fillMaxWidth(),

                            ) {
                                state.createTransferDomain?.userApprovalList?.let {
                                    itemsIndexed(it) { index, item ->
                                        ApprovalItem(
                                            item = item,
                                            isFirt = index == 0,
                                            isLast = index == it.lastIndex,
                                            lineColor = lineColor,
                                        )
                                    }
                                }
                            }
                        } else {
                            // todo xử lý khi null list
                        }
                    }
                }
            }
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = FDS.Sizer.Gap.gap24),
            ) {
                FoundationButton(
                    isLightButton = true,
                    leadingIcon = painterResource(com.vietinbank.feature_maker.R.drawable.ic_feature_transfer_maker_square_rounded_plus),
                    text = stringResource(R.string.maker_transfer_account_result_create_new_trans),
                    onClick = {
                        onAction.invoke(MakerTransferResultAction.MakerNewTransfer)
                    },
                    enabled = true,
                    modifier = Modifier
                        .fillMaxWidth(),
                )
                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
                FoundationButton(
                    isLightButton = false,
                    text = stringResource(R.string.maker_transfer_account_result_trans_manager),
                    onClick = {
                    },
                    enabled = true,
                    modifier = Modifier
                        .fillMaxWidth(),
                )
            }
        }
    }
    SaveContactBottomSheet(
        isVisible = state.canShowBottomSheetSaveContact == true,
        onCancel = {
            onAction.invoke(MakerTransferResultAction.OnShowSaveContactBottomSheet(false))
        },
        onConfirm = {
            onAction.invoke(MakerTransferResultAction.OnClickSaveContactBottomSheet(it))
        },
        imageLoader = imageLoader,
        bankItem = state.validateNapasAccountTransferDomain?.bankDomain,
        accountNo = state.validateNapasAccountTransferDomain?.toAcctNo ?: "",
        accountName = state.createTransferDomain?.fullNameCreate,
    )
    SaveTemplateBottomSheet(
        isVisible = state.canShowBottomSheetSaveTemp == true,
        onCancel = {
            onAction.invoke(
                MakerTransferResultAction.OnShowSaveTempBottomSheet(
                    false,
                ),
            )
        },
        onConfirm = {
            onAction.invoke(
                MakerTransferResultAction.OnClickSaveTempBottomSheet(customercode = it, confirm = CONFIRM_SAVE_TEMP_TRANSFER_NO),
            )
        },
        imageLoader = imageLoader,
        isTransferAccount = KEY_TRANSFER_TYPE_ACCOUNT == state.isTransferTypeKey,
        validateNapasAccountTransferDomain = state.validateNapasAccountTransferDomain,
        validateNapasCardTransferDomains = state.validateNapasCardTransferDomains,
        createTransferDomain = state.createTransferDomain,
    )
}

@Composable
fun ApprovalItem(
    item: UserApprovalItem,
    isLast: Boolean,
    lineColor: Color,
    isFirt: Boolean,
) {
    Row(modifier = Modifier.fillMaxWidth()) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .width(FDS.Sizer.Gap.gap32),
        ) {
            if (isFirt) {
                Spacer(Modifier.height(FDS.Sizer.Gap.gap24))
            }
            Icon(
                painter = painterResource(R.drawable.ic_commom_pending_24),
                contentDescription = "ic_commom_pending_24",
                tint = Color.Unspecified,
            )
            // Vertical line (if not last)
            if (!isLast) {
                Canvas(
                    modifier = Modifier
                        .width(1.dp)
                        .height(50.dp),
                ) {
                    val pathEffect = PathEffect.dashPathEffect(floatArrayOf(10f, 10f), 0f)
                    drawLine(
                        color = lineColor,
                        start = Offset(0f, 0f),
                        end = Offset(0f, size.height),
                        strokeWidth = 2.dp.toPx(),
                        pathEffect = pathEffect,
                    )
                }
            }
        }

        // Right side content
        Column(modifier = Modifier.padding(start = FDS.Sizer.Gap.gap8)) {
            FoundationText(
                text = item.fullname ?: "",
                style = FDS.Typography.bodyB1,
                color = FDS.Colors.gray900,
            )
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
            FoundationText(
                text = item.username ?: "",
                style = FDS.Typography.captionCaptionL,
                color = FDS.Colors.gray600,
            )
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
            FoundationText(
                text = stringResource(R.string.maker_transfer_result_pending),
                style = FDS.Typography.captionCaptionL,
                color = FDS.Colors.gray600,
            )
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
        }
    }
}