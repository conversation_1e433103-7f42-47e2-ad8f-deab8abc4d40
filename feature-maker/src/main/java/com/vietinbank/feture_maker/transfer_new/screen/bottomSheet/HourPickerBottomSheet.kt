package com.vietinbank.feture_maker.transfer_new.screen.bottomSheet

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationSelector
import com.vietinbank.core_ui.components.SelectorType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.HourPickerBottomSheetUiState
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

data class HourEntity(
    val hourID: String = "",
    val hourString: String = "",
    val isSelected: Boolean = false,
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HourPickerBottomSheet(
    isVisible: Boolean,
    onDismiss: () -> Unit,
    onItemHourSelected: (HourEntity) -> Unit,
    hourPickerBottomSheetUiState: HourPickerBottomSheetUiState,
) {
    val coroutineScope = rememberCoroutineScope()
    val focusManager = LocalFocusManager.current
    val keyboardController = LocalSoftwareKeyboardController.current
    if (isVisible) {
        BaseBottomSheet(
            visible = true,
            onDismissRequest = {
                onDismiss()
                coroutineScope.launch {
                    delay(50)
                    focusManager.clearFocus()
                    keyboardController?.hide()
                }
            },
            containerColor = FDS.Colors.backgroundBgContainer,
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(0.9f)
                    .clip(RoundedCornerShape(AppSizer.Radius.radius32)),
            ) {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    FoundationText(
                        text = stringResource(R.string.maker_payment_order_dashboard_choose_hour),
                        style = FDS.Typography.headingH4,
                        color = FDS.Colors.characterPrimary,
                        modifier = Modifier.padding(
                            vertical = FDS.Sizer.Gap.gap24,
                        ),
                    )

                    HorizontalDivider(
                        color = FDS.Colors.divider,
                        thickness = FDS.Sizer.Stroke.stroke1,
                        modifier = Modifier.padding(bottom = FDS.Sizer.Gap.gap8),
                    )
                    hourPickerBottomSheetUiState.itemsHour.let { listHour ->
                        LazyColumn(
                            modifier = Modifier.padding(
                                horizontal = FDS.Sizer.Gap.gap24,
                            ),
                        ) {
                            items(listHour) { hourItem ->
                                HourItem(
                                    hourItem = hourItem,
                                    onClick = { onItemHourSelected(hourItem) },
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun HourItem(hourItem: HourEntity, onClick: () -> Unit) {
    Row(
        modifier = Modifier.safeClickable { onClick() },
        verticalAlignment = Alignment.CenterVertically,
    ) {
        FoundationText(
            text = hourItem.hourString.orEmpty(),
            style = FDS.Typography.bodyB1Emphasized,
            color = FDS.Colors.characterPrimary,
            modifier = Modifier
                .padding(
                    vertical = FDS.Sizer.Gap.gap16,
                )
                .weight(1f),
        )
        Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap8))
        FoundationSelector(
            boxType = SelectorType.Radio,
            isSelected = hourItem.isSelected,
            modifier = Modifier.size(FDS.Sizer.Icon.icon24),
            isClickable = false,
        )
    }
}
