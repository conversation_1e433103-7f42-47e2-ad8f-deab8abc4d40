package com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_dashboard

import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_domain.models.home.ListLatestParams
import com.vietinbank.core_domain.models.home.TransactionInfo
import com.vietinbank.core_domain.models.maker.ContactDomains
import com.vietinbank.core_domain.models.maker.ContactListParams
import com.vietinbank.core_domain.models.maker.DataBankDomain
import com.vietinbank.core_domain.models.maker.GetPaymentTemplateListParams
import com.vietinbank.core_domain.models.maker.NapasBankListParams
import com.vietinbank.core_domain.models.maker.TempTransactionDomains
import com.vietinbank.core_domain.models.maker.TempTransactionParams
import com.vietinbank.core_domain.repository.cache.ITransferCacheManager
import com.vietinbank.core_domain.usecase.home.HomeUseCase
import com.vietinbank.core_domain.usecase.transfer.TransferUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.TAB_RECENT
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.TAB_SAVED
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.TRANSFER_LIST_LATEST_PARAMS_PAGE_SIZE
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_dashboard.MakerTransferViewModel.MakerTransferAccountEvent.*
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class MakerTransferViewModel @Inject constructor(
    private val transferUseCase: TransferUseCase,
    val transferCacheManager: ITransferCacheManager,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
    private val homeUseCase: HomeUseCase,
) : BaseViewModel() {

    private val _state = MutableStateFlow(MakerTransferAccountState())
    val state: StateFlow<MakerTransferAccountState> = _state.asStateFlow()

    private val _events = Channel<MakerTransferAccountEvent>(Channel.BUFFERED)
    val events = _events.receiveAsFlow()
    val originalBankList: MutableList<DataBankDomain> = mutableListOf()
    val originalContactList: MutableList<ContactDomains> = mutableListOf()
    val originalTempTransactionList: MutableList<TempTransactionDomains> = mutableListOf()
    val originalTransactionInfoList: MutableList<TransactionInfo> = mutableListOf()

    private fun getListLatest() {
        updateIsLoadingListRecent(true)
        if (transferCacheManager.getLatTestList()?.isNotEmpty() == true) {
            updateTransactionInfoList(
                transferCacheManager.getLatTestList()?.toMutableList() ?: mutableListOf(),
            )
            originalTransactionInfoList.clear()
            originalTransactionInfoList.addAll(transferCacheManager.getLatTestList()?.toMutableList() ?: mutableListOf())
            updateIsLoadingListRecent(false)
            return
        }
        launchJob(showLoading = true) {
            val res = homeUseCase.listLatest(
                ListLatestParams(
                    username = userProf.getUserName() ?: "",
                    role = userProf.getRoleId() ?: "",
                    status = Tags.HOME_LIST_LATEST_PARAMS_STATUS,
                    pageSize = TRANSFER_LIST_LATEST_PARAMS_PAGE_SIZE,
                    pageNum = Tags.HOME_LIST_LATEST_PARAMS_PAGE_NUM,
                ),
            )
            handleResource(res) { data ->
                updateTransactionInfoList(data.transactionInfoList ?: mutableListOf())
                transferCacheManager.saveLatestList(
                    data.transactionInfoList?.toMutableList() ?: mutableListOf(),
                )
                originalTransactionInfoList.clear()
                originalTransactionInfoList.addAll(data.transactionInfoList?.toMutableList() ?: mutableListOf())
                updateIsLoadingListRecent(false)
            }
        }
    }

    fun getPaymentTemplateList() {
        updateIsLoadingListTemp(true)
        if (transferCacheManager.getTempList()?.isNotEmpty() == true) {
            updateListTempDomains(
                transferCacheManager.getTempList()?.toMutableList() ?: mutableListOf(),
            )
            originalTempTransactionList.clear()
            originalTempTransactionList.addAll(transferCacheManager.getTempList()?.toMutableList() ?: mutableListOf())
            updateIsLoadingListTemp(false)
            return
        }
        launchJob(showLoading = true) {
            val params = GetPaymentTemplateListParams(
                TempTransactionParams(tranType = "all"),
                username = userProf.getUserName() ?: "",
            )
            val res = transferUseCase.getPaymentTemplateList(params)
            handleResource(res) { data ->
                updateListTempDomains(data.tempTransactionList)
                transferCacheManager.saveTempList(data.tempTransactionList)
                originalTempTransactionList.clear()
                originalTempTransactionList.addAll(data.tempTransactionList)
                updateIsLoadingListTemp(false)
            }
        }
    }

    fun getContactList() {
        updateIsLoadingListContact(true)
        if (transferCacheManager.getContactList()?.isNotEmpty() == true) {
            updateListContactDomains(
                transferCacheManager.getContactList()?.toMutableList() ?: mutableListOf(),
            )
            originalContactList.clear()
            originalContactList.addAll(transferCacheManager.getContactList()?.toMutableList() ?: mutableListOf())
            updateIsLoadingListContact(false)
            return
        }
        launchJob(showLoading = true) {
            val params = ContactListParams(
                username = userProf.getUserName().toString(),
                serviceId = "",
            )
            val res = transferUseCase.contactList(params)
            handleResource(res) { data ->
                updateListContactDomains(data.contacts)
                transferCacheManager.saveContactist(data.contacts)
                originalContactList.clear()
                originalContactList.addAll(data.contacts)
                updateIsLoadingListContact(false)
            }
        }
    }

    fun getNapasBankList() {
        if (transferCacheManager.getBankList()?.isNotEmpty() == true) {
            updateListDataBanks(
                transferCacheManager.getBankList()?.toMutableList() ?: mutableListOf(),
            )
            originalBankList.clear()
            originalBankList.addAll(transferCacheManager.getBankList()?.toMutableList() ?: mutableListOf())
            return
        }
        launchJob(showLoading = true) {
            val res = transferUseCase.getNapasBankList(
                NapasBankListParams(
                    username = userProf.getUserName() ?: "",
                    cifno = userProf.getCifNo() ?: "",
                ),
            )
            handleResource(res) { data ->
                originalBankList.clear()
                originalBankList.addAll(data.dataBanks)
                updateListDataBanks(data.dataBanks)
                transferCacheManager.saveBankList(data.dataBanks)
            }
        }
    }

    private fun updateListDataBanks(listDataBanks: MutableList<DataBankDomain>) {
        _state.update { currentState ->
            currentState.copy(
                listDataBanks = listDataBanks,
            )
        }
    }
    private fun updateSearchTextBank(value: String) {
        _state.update { currentState ->
            currentState.copy(
                searchTextBank = value,
            )
        }
    }

    private fun onSelectedIndexChange(value: Int) {
        _state.update { currentState ->
            currentState.copy(
                selectedIndex = value,
            )
        }
    }

    private fun updateListContactDomains(value: MutableList<ContactDomains>) {
        _state.update { currentState ->
            currentState.copy(
                listContactDomains = value,
            )
        }
    }
    private fun updateIsLoadingListContact(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                isLoadingListContact = value,
            )
        }
    }
    private fun updateIsLoadingListTemp(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                isLoadingListTemp = value,
            )
        }
    }
    private fun updateIsLoadingListRecent(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                isLoadingListRecent = value,
            )
        }
    }
    private fun updateListTempDomains(value: MutableList<TempTransactionDomains>) {
        _state.update { currentState ->
            currentState.copy(
                tempTransactionList = value,
            )
        }
    }

    private fun updateTransactionInfoList(value: MutableList<TransactionInfo>) {
        _state.update { currentState ->
            currentState.copy(
                transactionInfoList = value,
            )
        }
    }

    private fun updateSearchContactText(value: String) {
        _state.update { currentState ->
            currentState.copy(
                searchContactText = value,
            )
        }
    }

    private fun updateSearchRecentText(value: String) {
        _state.update { currentState ->
            currentState.copy(
                searchRecentText = value,
            )
        }
    }

    private fun updateSelectedTabContactIndex(value: Int) {
        _state.update { currentState ->
            currentState.copy(
                selectedTabContactIndex = value,
            )
        }
    }

    private fun canShowBottomSheetSaveContact(show: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                canShowBottomSheetSaveContact = show,
            )
        }
    }
    private fun updateBankItem(value: DataBankDomain) {
        _state.update { currentState ->
            currentState.copy(
                bankItem = value,
            )
        }
    }

    private fun updateAccountNoContact(value: String) {
        _state.update { currentState ->
            currentState.copy(
                accountNoContact = value,
            )
        }
    }
    private fun updateAccountNameContact(value: String) {
        _state.update { currentState ->
            currentState.copy(
                accountNameContact = value,
            )
        }
    }
    private fun mapMapFieldContactToBank(contactDomains: ContactDomains) {
        val bankItem = DataBankDomain(
            binCode = "",
            bankName = "",
            shortName = "Text", //
            ebankCode = "",
            icon = contactDomains.iconUrl ?: "", //
            name = "",
            type = "",
            status = "",
        )
        updateBankItem(bankItem)
    }

    fun onAction(action: MakerTransferAccountAction) {
        when (action) {
            is MakerTransferAccountAction.OnBackPressed -> {
                viewModelScope.launch {
                    _events.send(NavigateBack)
                }
            }

            is MakerTransferAccountAction.OnSearchTextBankChange -> {
                updateSearchTextBank(action.searchTextBank)

                val searchText = action.searchTextBank.trim()
                val filteredBanks = if (searchText.isBlank()) {
                    originalBankList
                } else {
                    originalBankList.filter { bank ->
                        bank.bankName?.contains(searchText, ignoreCase = true) == true ||
                            bank.shortName?.contains(searchText, ignoreCase = true) == true ||
                            bank.name?.contains(searchText, ignoreCase = true) == true
                    }
                }
                updateListDataBanks(filteredBanks.toMutableList())
            }

            is MakerTransferAccountAction.ClickBankItem -> {
                viewModelScope.launch {
                    _events.send(ClickBankItem(action.bank))
                }
            }

            is MakerTransferAccountAction.OnSelectedIndex -> {
                onSelectedIndexChange(action.selectedIndex)
                if (TAB_SAVED == action.selectedIndex) {
                    getContactList()
                    getPaymentTemplateList()
                } else if (TAB_RECENT == action.selectedIndex) {
                    getListLatest()
                }
            }

            is MakerTransferAccountAction.OnSearchContactText -> {
                updateSearchContactText(action.searchContactText)
                val searchText = action.searchContactText.trim()
                val filteredContacts = if (searchText.isBlank()) {
                    originalContactList
                } else {
                    originalContactList.filter { bank ->
                        bank.bankName?.contains(searchText, ignoreCase = true) == true ||
                            bank.account?.contains(searchText, ignoreCase = true) == true ||
                            bank.payeename?.contains(searchText, ignoreCase = true) == true ||
                            bank.payeenickname?.contains(searchText, ignoreCase = true) == true ||
                            bank.cardnumber?.contains(searchText, ignoreCase = true) == true
                    }
                }
                updateListContactDomains(filteredContacts.toMutableList())

                val filteredTemps = if (searchText.isBlank()) {
                    originalTempTransactionList
                } else {
                    originalTempTransactionList.filter { bank ->
                        bank.toAccountName?.contains(searchText, ignoreCase = true) == true ||
                            bank.content?.contains(searchText, ignoreCase = true) == true
                    }
                }
                updateListTempDomains(filteredTemps.toMutableList())
            }

            is MakerTransferAccountAction.OnSelectedTabContactIndex -> {
                updateSelectedTabContactIndex(action.selectedTabIndex)
            }

            is MakerTransferAccountAction.OnSearchRecentText -> {
                updateSearchRecentText(action.searchRecentText)
                val searchText = action.searchRecentText.trim()
                val filteredTemps = if (searchText.isBlank()) {
                    originalTransactionInfoList
                } else {
                    originalTransactionInfoList.filter { bank ->
                        bank.toAccountName?.contains(searchText, ignoreCase = true) == true ||
                            bank.toAccount?.contains(searchText, ignoreCase = true) == true ||
                            bank.bankName?.contains(searchText, ignoreCase = true) == true
                    }
                }
                updateTransactionInfoList(filteredTemps.toMutableList())
            }

            is MakerTransferAccountAction.ClickContactItem -> {
                viewModelScope.launch {
                    _events.send(ClickContactItem(action.contactItem))
                }
            }

            is MakerTransferAccountAction.ClickTempItem -> {
                viewModelScope.launch {
                    _events.send(ClickTempItem(action.tempTransactionItem))
                }
            }
            is MakerTransferAccountAction.ClickRecentItem -> {
                viewModelScope.launch {
                    _events.send(ClickRecentItem(action.transactionInfo))
                }
            }

            is MakerTransferAccountAction.ClickTransferCard -> {
                viewModelScope.launch {
                    _events.send(ClickTransferCard)
                }
            }

            is MakerTransferAccountAction.OnShowSaveContactBottomSheet -> {
                canShowBottomSheetSaveContact(action.canShowBottomSheetSaveContact)
            }
            is MakerTransferAccountAction.MapFieldContactToBank -> {
                updateAccountNoContact(value = action.contactDomains.account ?: "")
                if (Tags.TransferType.TYPE_IN == action.contactDomains.trantype || Tags.TransferType.TYPE_NAPAS_ACCOUNT == action.contactDomains.trantype) {
                    updateAccountNameContact(value = action.contactDomains.payeename ?: "")
                } else {
                    updateAccountNameContact(value = action.contactDomains.cardnumber ?: "")
                }
                mapMapFieldContactToBank(contactDomains = action.contactDomains)
                canShowBottomSheetSaveContact(true)
            }
        }
    }

    sealed class MakerTransferAccountEvent {
        data object NavigateBack : MakerTransferAccountEvent()
        data class ClickBankItem(val bank: DataBankDomain) : MakerTransferAccountEvent()
        data class ClickContactItem(val contactItem: ContactDomains) : MakerTransferAccountEvent()
        data class ClickTempItem(val tempTransactionItem: TempTransactionDomains) : MakerTransferAccountEvent()
        data class ClickRecentItem(val transactionInfo: TransactionInfo) : MakerTransferAccountEvent()
        data object ClickTransferCard : MakerTransferAccountEvent()
    }

    sealed class MakerTransferAccountAction {
        data object OnBackPressed : MakerTransferAccountAction()
        data class ClickBankItem(val bank: DataBankDomain) : MakerTransferAccountAction()
        data class ClickContactItem(val contactItem: ContactDomains) : MakerTransferAccountAction()
        data class ClickTempItem(val tempTransactionItem: TempTransactionDomains) : MakerTransferAccountAction()
        data class ClickRecentItem(val transactionInfo: TransactionInfo) : MakerTransferAccountAction()
        data class OnSearchTextBankChange(val searchTextBank: String) : MakerTransferAccountAction()
        data class OnSelectedIndex(val selectedIndex: Int) : MakerTransferAccountAction()
        data class OnSearchContactText(val searchContactText: String) : MakerTransferAccountAction()
        data class OnSearchRecentText(val searchRecentText: String) : MakerTransferAccountAction()
        data class OnSelectedTabContactIndex(val selectedTabIndex: Int) : MakerTransferAccountAction()
        data object ClickTransferCard : MakerTransferAccountAction()
        data class OnShowSaveContactBottomSheet(val canShowBottomSheetSaveContact: Boolean) :
            MakerTransferAccountAction()
        data class MapFieldContactToBank(val contactDomains: ContactDomains) :
            MakerTransferAccountAction()
    }

    data class MakerTransferAccountState(
        val listDataBanks: MutableList<DataBankDomain> = mutableListOf(),
        var searchTextBank: String = "",
        var selectedIndex: Int = 0,
        val listContactDomains: MutableList<ContactDomains> = mutableListOf(),
        val tempTransactionList: MutableList<TempTransactionDomains> = mutableListOf(),
        val transactionInfoList: MutableList<TransactionInfo> = mutableListOf(),
        val searchContactText: String = "",
        val searchRecentText: String = "",
        val selectedTabContactIndex: Int = 0,
        val isLoadingListContact: Boolean = true,
        val isLoadingListTemp: Boolean = true,
        val isLoadingListRecent: Boolean = true,
        val canShowBottomSheetSaveContact: Boolean = false,
        val bankItem: DataBankDomain? = null,
        val accountNoContact: String? = null,
        val accountNameContact: String? = null,
    )
}