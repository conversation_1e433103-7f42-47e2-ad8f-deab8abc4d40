package com.vietinbank.feture_maker.transfer_new.screen.maker_payment_order_dashboard

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.provider.MediaStore
import android.view.View
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.vietinbank.core_common.extensions.isFileSizeValid
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE_PAYMENT_ORDER
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.UTF8
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.net.URLEncoder
import javax.inject.Inject
import kotlin.getValue

@AndroidEntryPoint
class PaymentOrderFragment : BaseFragment<PaymentOrderViewModel>() {
    override val viewModel: PaymentOrderViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var imageLoader: CoilImageLoader
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initData()
        observeEvents()
    }

    private fun initData() {
        viewModel.getAccList()
        viewModel.getBranch()
    }

    private fun observeEvents() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.events.collect { event ->
                when (event) {
                    PaymentOrderViewModel.PaymentOrderEvent.NavigateBack -> {
                        appNavigator.goToHome()
                    }

                    is PaymentOrderViewModel.PaymentOrderEvent.ShowErrorMess -> {
                        showNoticeDialog(message = event.errorMess)
                    }

                    is PaymentOrderViewModel.PaymentOrderEvent.ShowErrorValidateFieldTransferAccount -> {
                        showNoticeDialog(
                            message = event.errorObject.errorMessage ?: "",
                            positiveAction = {
                                event.errorObject.focusAction?.invoke()
                            },
                        )
                    }

                    is PaymentOrderViewModel.PaymentOrderEvent.OnClickGotoGallery -> {
                        openImagePicker()
                    }
                    is PaymentOrderViewModel.PaymentOrderEvent.OnClickGotoUploadFile -> {
                        openFileChooser()
                    }

                    is PaymentOrderViewModel.PaymentOrderEvent.ValidatePaymentOrderState -> {
                        val itemString =
                            Utils.g().provideGson().toJson(event.validatePaymentOrderTransferDomains)
                        val encodedItem = URLEncoder.encode(itemString, UTF8)
                        appNavigator.goToMakerTransferConfirm(
                            typeTransfer = KEY_TRANSFER_TYPE_PAYMENT_ORDER,
                            validateItem = encodedItem,
                        )
                    }
                }
            }
        }
    }

    private fun openImagePicker() {
        val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
        imagePickerLauncher.launch(intent)
    }

    private val imagePickerLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult(),
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val imageUri = result.data?.data
            imageUri?.let { uri ->
                requireContext().let {
                    viewModel.getFileUriState(ctx = it, uri = uri)
                }
            }
        }
    }

    private fun openFileChooser() {
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
            addCategory(Intent.CATEGORY_OPENABLE)
            type = "*/*"
            putExtra(
                Intent.EXTRA_MIME_TYPES,
                arrayOf(
                    "application/pdf", // PDF
                    "application/zip", // ZIP
                    "application/vnd.ms-excel", // XLS
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // XLSX
                    "text/csv", // CSV
                    "application/msword", // DOC
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // DOCX
                ),
            )
        }
        filePickerLauncher.launch(intent)
    }

    private val filePickerLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult(),
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                requireContext().let {
                    if (isFileSizeValid(it, uri)) {
                        viewModel.getFileUriState(ctx = it, uri = uri)
                    } else {
                        Toast.makeText(
                            it,
                            resources.getString(com.vietinbank.core_ui.R.string.maker_payment_order_dashboard_upload_file_error),
                            Toast.LENGTH_SHORT,
                        ).show()
                    }
                }
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.state.collectAsState()
        AppTheme {
            PaymentOrderScreen(
                state = uiState,
                onAction = viewModel::onAction,
                imageLoader = imageLoader,
            )
        }
    }

    override fun onBackPressed(): Boolean {
        appNavigator.goToHome()
        return true
    }
}