package com.vietinbank.feture_maker.transfer_new.screen.empty_list

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun EmptyListScreen(
    iconResourceID: Int,
    title: String,
    content: String,
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(FDS.Sizer.Gap.gap24),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
    ) {
        Icon(
            painter = painterResource(id = iconResourceID),
            contentDescription = null,
            tint = Color.Unspecified,
            modifier = Modifier.size(FDS.Sizer.Gap.gap96),
        )

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

        // Title
        FoundationText(
            text = title,
            style = FDS.Typography.headingH3,
            textAlign = TextAlign.Center,
            color = FDS.Colors.characterHighlighted,
        )

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
        // content
        FoundationText(
            text = content,
            style = FDS.Typography.bodyB2,
            color = FDS.Colors.characterPrimary,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(horizontal = FDS.Sizer.Gap.gap24),
        )
    }
}