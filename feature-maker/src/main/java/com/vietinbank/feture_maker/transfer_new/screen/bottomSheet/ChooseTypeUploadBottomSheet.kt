package com.vietinbank.feture_maker.transfer_new.screen.bottomSheet

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChooseTypeUploadBottomSheet(
    isVisible: Boolean,
    onDismiss: () -> Unit,
    onClickGotoGallery: () -> Unit,
    onClickGotoUploadFile: () -> Unit,
) {
    if (isVisible) {
        BaseBottomSheet(
            visible = true,
            onDismissRequest = {
                onDismiss()
            },

        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(AppSizer.Radius.radius32)),
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth(),
                ) {
                    Column(
                        modifier = Modifier.fillMaxWidth().background(
                            color = FDS.Colors.backgroundBgContainer,
                            shape = RoundedCornerShape(
                                FDS.Sizer.Gap.gap32,

                            ),
                        ),
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        FoundationText(
                            text = stringResource(R.string.maker_payment_order_dashboard_upload_file),
                            style = FDS.Typography.headingH4,
                            color = FDS.Colors.characterPrimary,
                            modifier = Modifier.padding(
                                vertical = FDS.Sizer.Gap.gap24,
                            ),
                        )

                        HorizontalDivider(
                            color = FDS.Colors.divider,
                            thickness = FDS.Sizer.Stroke.stroke1,
                            modifier = Modifier.padding(bottom = FDS.Sizer.Gap.gap8),
                        )
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = FDS.Sizer.Gap.gap24),
                        ) {
                            Row(
                                modifier = Modifier.safeClickable { onClickGotoGallery.invoke() },
                                verticalAlignment = Alignment.CenterVertically,
                            ) {
                                Box(
                                    modifier = Modifier
                                        .size(FDS.Sizer.Icon.icon40)
                                        .clip(CircleShape)
                                        .background(FDS.Colors.gray50),
                                    contentAlignment = Alignment.Center,
                                ) {
                                    Icon(
                                        painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_gallery),
                                        contentDescription = "ic_upload_white",
                                        tint = Color.Unspecified,
                                    )
                                }
                                Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap16))
                                FoundationText(
                                    modifier = Modifier.weight(1f),
                                    text = stringResource(com.vietinbank.core_ui.R.string.maker_payment_order_dashboard_upload_file_type_gallery),
                                    color = FDS.Colors.characterHighlighted,
                                    style = FDS.Typography.bodyB1,
                                )
                            }
                            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

                            Row(
                                modifier = Modifier.safeClickable { onClickGotoUploadFile.invoke() },
                                verticalAlignment = Alignment.CenterVertically,
                            ) {
                                Box(
                                    modifier = Modifier
                                        .size(FDS.Sizer.Icon.icon40)
                                        .clip(CircleShape)
                                        .background(FDS.Colors.gray50),
                                    contentAlignment = Alignment.Center,
                                ) {
                                    Icon(
                                        painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_upload_white),
                                        contentDescription = "ic_upload_white",
                                        tint = FDS.Colors.characterHighlighted,
                                    )
                                }
                                Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap16))
                                FoundationText(
                                    modifier = Modifier.weight(1f),
                                    text = stringResource(com.vietinbank.core_ui.R.string.maker_payment_order_dashboard_upload_file_from_device),
                                    color = FDS.Colors.characterHighlighted,
                                    style = FDS.Typography.bodyB1,
                                )
                            }
                            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
                            FoundationText(
                                text = stringResource(R.string.maker_payment_order_dashboard_upload_file_dialog_note),
                                color = FDS.Colors.characterPrimary,
                                style = FDS.Typography.captionCaptionL,
                            )
                            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
                        }
                    }
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color.Transparent)
                            .padding(
                                horizontal = FDS.Sizer.Gap.gap24,
                                vertical = FDS.Sizer.Gap.gap16,
                            ),
                    ) {
                        FoundationButton(
                            isLightButton = false,
                            text = stringResource(R.string.common_back),
                            onClick = { onDismiss.invoke() },
                            enabled = true,
                            modifier = Modifier.fillMaxWidth(),
                        )
                    }
                }
            }
        }
    }
}