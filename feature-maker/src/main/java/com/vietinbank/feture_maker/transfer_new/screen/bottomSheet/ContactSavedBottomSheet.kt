package com.vietinbank.feture_maker.transfer_new.screen.bottomSheet

import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.vietinbank.core_domain.models.maker.ContactDomains
import com.vietinbank.core_domain.models.maker.TempTransactionDomains
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.feture_maker.transfer_new.screen.contact_template_recent.ContactSavedScreen

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ContactSavedBottomSheet(
    isVisible: Boolean,
    onDismiss: () -> Unit,
    imageLoader: CoilImageLoader,
    modifier: Modifier,
    searchContactText: String,
    onSearchContactText: (searchContactText: String) -> Unit,
    listContactDomains: MutableList<ContactDomains>,
    listTempDomains: MutableList<TempTransactionDomains>,
    selectedTabIndex: Int,
    onSelectedTabIndex: (selectedTabIndex: Int) -> Unit,
    onClickItemContact: (selectedTabIndex: ContactDomains) -> Unit,
    onClickItemTemp: (selectedTabIndex: TempTransactionDomains) -> Unit,
    isLoadingListContact: Boolean,
    isLoadingListTemp: Boolean,
) {
    if (isVisible) {
        BaseBottomSheet(
            visible = true,
            onDismissRequest = onDismiss,
            containerColor = FoundationDesignSystem.Colors.backgroundBgContainer,
        ) {
            ContactSavedScreen(
                imageLoader = imageLoader,
                modifier = modifier,
                searchContactText = searchContactText,
                onSearchContactText = onSearchContactText,
                listContactDomains = listContactDomains,
                listTempDomains = listTempDomains,
                selectedTabIndex = selectedTabIndex,
                onSelectedTabIndex = onSelectedTabIndex,
                onClickItemContact = onClickItemContact,
                onClickItemTemp = onClickItemTemp,
                isLoadingListContact = isLoadingListContact,
                isLoadingListTemp = isLoadingListTemp,
                onClickEditItemContact = {},
            )
        }
    }
}