package com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE_ACCOUNT
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE_CARD
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.UTF8
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.MakerTransferBankChoosedViewModel.MakerTransferBankChooseEvent
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.net.URLEncoder
import javax.inject.Inject
import kotlin.getValue

@AndroidEntryPoint
class MakerTransferBankChoosedFragment : BaseFragment<MakerTransferBankChoosedViewModel>() {
    override val viewModel: MakerTransferBankChoosedViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var imageLoader: CoilImageLoader

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initData()
        observeEvents()
    }

    private fun initData() {
        arguments?.let { bundle ->
            viewModel.handleArguments(bundle)
        }
    }

    private fun observeEvents() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.events.collect { event ->
                when (event) {
                    MakerTransferBankChooseEvent.NavigateBack -> {
                        appNavigator.goToMakerTransfer(bundleOf())
                    }

                    is MakerTransferBankChooseEvent.ValidateNapasAccountTransferState -> {
                        val itemString = Utils.g().provideGson().toJson(event.validateNapasAccountTransferDomain)
                        val encodedItem = URLEncoder.encode(itemString, UTF8)
                        appNavigator.goToMakerTransferConfirm(typeTransfer = KEY_TRANSFER_TYPE_ACCOUNT, validateItem = encodedItem)
                    }

                    is MakerTransferBankChooseEvent.ShowErrorMess -> {
                        if (event.textButton.isNotEmpty()) {
                            showNoticeDialog(message = event.errorMess, positiveButtonText = event.textButton)
                        } else {
                            showNoticeDialog(message = event.errorMess)
                        }
                    }

                    is MakerTransferBankChooseEvent.ValidateNapasCardTransferState -> {
                        val itemString = Utils.g().provideGson().toJson(event.validateNapasCardTransferDomains)
                        val encodedItem = URLEncoder.encode(itemString, UTF8)
                        appNavigator.goToMakerTransferConfirm(typeTransfer = KEY_TRANSFER_TYPE_CARD, validateItem = encodedItem)
                    }
                }
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.state.collectAsState()
        AppTheme {
            MakerTransferBankChoosedScreen(
                state = uiState,
                onAction = viewModel::onAction,
                imageLoader = imageLoader,
            )
        }
    }

    override fun onBackPressed(): Boolean {
        appNavigator.goToMakerTransfer(bundleOf())
        return true
    }
}