package com.vietinbank.feture_maker.transfer_new.screen.bottomSheet

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountTransferDomain
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_maker.R
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SplitTransferBottomSheet(
    isVisible: Boolean,
    onDismiss: () -> Unit,
    onTransferSplit: () -> Unit,
    onTransferNormal: () -> Unit,
    onClickTypeLoan: () -> Unit,
    modifier: Modifier = Modifier,
    itemValidate: ValidateNapasAccountTransferDomain? = null,
) {
    val scrollState = rememberScrollState()

    if (isVisible) {
        BaseBottomSheet(
            visible = true,
            onDismissRequest = onDismiss,
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(0.9f),
            ) {
                Column(
                    modifier = Modifier.fillMaxSize(),
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(
                                color = FDS.Colors.backgroundBgContainer,
                                shape = RoundedCornerShape(
                                    FDS.Sizer.Gap.gap32,
                                ),
                            )
                            .padding(FDS.Sizer.Gap.gap24)
                            .verticalScroll(scrollState)
                            .weight(1f),
                    ) {
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
                        Icon(
                            painter = painterResource(R.mipmap.ic_feature_transfer_split),
                            contentDescription = "ic_drop_down",
                            tint = Color.Unspecified,
                            modifier = Modifier.size(FDS.Sizer.Icon.icon96),
                        )
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
                        FoundationText(
                            textAlign = TextAlign.Center,
                            text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_split_title),
                            color = FDS.Colors.characterHighlighted,
                            style = FDS.Typography.headingH3,
                        )
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
                        FoundationText(
                            textAlign = TextAlign.Center,
                            text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_split_content),
                            color = FDS.Colors.gray800,
                            style = FDS.Typography.bodyB2,
                        )
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(
                                    FDS.Colors.blue50,
                                    RoundedCornerShape(FDS.Sizer.Radius.radius16),
                                )
                                .padding(FDS.Sizer.Gap.gap16),
                        ) {
                            FoundationText(
                                text = stringResource(
                                    com.vietinbank.core_ui.R.string.maker_transfer_split_number_total,
                                    (
                                        itemValidate?.subTransactionCount?.toIntOrNull()
                                            ?: 0
                                        ) + (
                                        itemValidate?.subTransactionsRemainderCount?.toIntOrNull()
                                            ?: 0
                                        ),
                                ),
                                color = FDS.Colors.characterPrimary,
                                style = FDS.Typography.bodyB2Emphasized,
                            )
                            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically,
                            ) {
                                FoundationText(
                                    text = stringResource(
                                        com.vietinbank.core_ui.R.string.maker_transfer_split_number_content_transaction,
                                        itemValidate?.subTransactionCount ?: "",
                                    ),
                                    color = FDS.Colors.gray600,
                                    style = FDS.Typography.bodyB2,
                                    modifier = Modifier.weight(1f),
                                    textAlign = TextAlign.Start,

                                )
                                FoundationText(
                                    text = Utils.g().getDotMoneyHasCcy(
                                        itemValidate?.subSplitTransAmount ?: "",
                                        (itemValidate?.currency ?: ""),
                                    ),
                                    color = FDS.Colors.characterPrimary,
                                    style = FDS.Typography.bodyB2Emphasized,
                                    modifier = Modifier.weight(1f),
                                    textAlign = TextAlign.Start,
                                )
                            }
                            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))

                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically,
                            ) {
                                FoundationText(
                                    text = stringResource(
                                        com.vietinbank.core_ui.R.string.maker_transfer_split_number_content_transaction,
                                        itemValidate?.subTransactionsRemainderCount ?: "",
                                    ),
                                    color = FDS.Colors.gray600,
                                    style = FDS.Typography.bodyB2,
                                    modifier = Modifier.weight(1f),
                                    textAlign = TextAlign.Start,

                                )
                                FoundationText(
                                    text = Utils.g().getDotMoneyHasCcy(
                                        itemValidate?.subTransactionsRemainderAmount ?: "",
                                        (itemValidate?.currency ?: ""),
                                    ),
                                    color = FDS.Colors.characterPrimary,
                                    style = FDS.Typography.bodyB2Emphasized,
                                    modifier = Modifier.weight(1f),
                                    textAlign = TextAlign.Start,
                                )
                            }
                            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))

                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically,
                            ) {
                                FoundationText(
                                    text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_split_transfer_type_fee),
                                    color = FDS.Colors.gray600,
                                    style = FDS.Typography.bodyB2,
                                    modifier = Modifier.weight(1f),
                                    textAlign = TextAlign.Start,

                                )
                                FoundationText(
                                    text = TransferConstants.Transfer.getFeeMethodName(
                                        itemValidate?.feePayMethod ?: "",
                                    ),
                                    color = FDS.Colors.characterPrimary,
                                    style = FDS.Typography.bodyB2Emphasized,
                                    modifier = Modifier.weight(1f),
                                    textAlign = TextAlign.Start,
                                )
                            }
                            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap20))

                            Row(
                                modifier = Modifier.fillMaxWidth().safeClickable {
                                    onClickTypeLoan.invoke()
                                },
                                verticalAlignment = Alignment.CenterVertically,
                            ) {
                                Icon(
                                    painter = painterResource(R.drawable.ic_feature_transfer_question),
                                    contentDescription = "ic_drop_down",
                                    tint = Color.Unspecified,
                                    modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                                )
                                Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap4))
                                FoundationText(
                                    text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_split_transfer_type_loan),
                                    color = FDS.Colors.characterHighlighted,
                                    style = FDS.Typography.interactionSmallButton,
                                    textAlign = TextAlign.Start,
                                )
                            }
                        }
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(
                                    FDS.Colors.blue50,
                                    RoundedCornerShape(FDS.Sizer.Radius.radius16),
                                )
                                .padding(FDS.Sizer.Gap.gap16),
                        ) {
                            FoundationText(
                                text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_normal),
                                color = FDS.Colors.characterPrimary,
                                style = FDS.Typography.bodyB1Emphasized,
                            )
                            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
                            FoundationText(
                                text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_normal_content),
                                color = FDS.Colors.gray600,
                                style = FDS.Typography.bodyB2,
                            )
                        }
                    }
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(FDS.Sizer.Gap.gap16),
                    ) {
                        FoundationButton(
                            isLightButton = true,
                            text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_split_transfer_action_split),
                            onClick = { onTransferSplit.invoke() },
                            enabled = true,
                            modifier = Modifier.fillMaxWidth(),
                        )
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
                        FoundationButton(
                            isLightButton = false,
                            text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_normal),
                            onClick = { onTransferNormal.invoke() },
                            enabled = true,
                            modifier = Modifier.fillMaxWidth(),
                        )
                    }
                }
            }
        }
    }
}