package com.vietinbank.feture_maker.transfer_new.screen.bottomSheet

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.DatePicker
import androidx.compose.material3.DatePickerDefaults
import androidx.compose.material3.DatePickerDialog
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.SelectableDates
import androidx.compose.material3.TextButton
import androidx.compose.material3.rememberDatePickerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_common.extensions.EEEE_dd_MMMM_yyyy
import com.vietinbank.core_common.extensions.dd_MM_yyyy_1
import com.vietinbank.core_common.extensions.toDateString
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import java.util.Calendar
import java.util.Locale
import java.util.TimeZone
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DatePickerDialog(
    showDatePicker: Boolean,
    onDateSelected: (String) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
) {
    if (showDatePicker) {
        // Calculate tomorrow's date in UTC to ensure consistency with DatePicker
        val tomorrowMillis = remember {
            Calendar.getInstance().apply {
                // Set to UTC timezone to match DatePicker's behavior
                timeZone = TimeZone.getTimeZone("UTC")
                set(Calendar.HOUR_OF_DAY, 0)
                set(Calendar.MINUTE, 0)
                set(Calendar.SECOND, 0)
                set(Calendar.MILLISECOND, 0)
                add(Calendar.DAY_OF_MONTH, 1)
            }.timeInMillis
        }

        val datePickerState = rememberDatePickerState(
            initialSelectedDateMillis = tomorrowMillis,
            selectableDates = object : SelectableDates {
                override fun isSelectableDate(utcTimeMillis: Long): Boolean {
                    return utcTimeMillis >= tomorrowMillis
                }
            },
        )

        val confirmEnabled by remember {
            derivedStateOf { datePickerState.selectedDateMillis != null }
        }

        DatePickerDialog(
            colors = DatePickerDefaults.colors(
                containerColor = Color.White,
                headlineContentColor = Color.White,
                weekdayContentColor = Color.White,
            ),
            onDismissRequest = onDismiss,
            confirmButton = {
                TextButton(
                    onClick = {
                        datePickerState.selectedDateMillis?.let { millis ->
                            onDateSelected(millis.toDateString(pattern = dd_MM_yyyy_1))
                        }
                    },
                    enabled = confirmEnabled,
                ) {
                    FoundationText(
                        text = stringResource(com.vietinbank.core_ui.R.string.dialog_button_confirm),
                        style = FDS.Typography.bodyB2Emphasized,
                        color = if (confirmEnabled) FDS.Colors.characterPrimary else FDS.Colors.gray600,
                    )
                }
            },
            dismissButton = {
                TextButton(onClick = onDismiss) {
                    FoundationText(
                        text = stringResource(com.vietinbank.core_ui.R.string.dialog_button_back),
                        style = FDS.Typography.bodyB2,
                        color = FDS.Colors.gray600,
                    )
                }
            },
            modifier = modifier,
        ) {
            DatePicker(
                state = datePickerState,
                colors = DatePickerDefaults.colors(
                    containerColor = FDS.Colors.backgroundBgContainer,
                    titleContentColor = FDS.Colors.characterPrimary,
                    headlineContentColor = FDS.Colors.characterSecondary,
                    weekdayContentColor = FDS.Colors.characterSecondary,
                    subheadContentColor = FDS.Colors.characterPrimary,
                    navigationContentColor = FDS.Colors.characterPrimary,
                    yearContentColor = FDS.Colors.characterPrimary,
                    disabledYearContentColor = FDS.Colors.gray600,
                    currentYearContentColor = FDS.Colors.characterPrimary,
                    selectedYearContentColor = Color.White,
                    disabledDayContentColor = FDS.Colors.gray600,
                    todayContentColor = FDS.Colors.characterPrimary,
                    dayContentColor = FDS.Colors.characterPrimary,
                    selectedDayContentColor = FDS.Colors.white,
                    selectedDayContainerColor = FDS.Colors.characterPrimary,
                    todayDateBorderColor = FDS.Colors.characterPrimary,
                ),
                title = {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth(),
                    ) {
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
                        FoundationText(
                            text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_choose_date),
                            style = FDS.Typography.headingH3,
                            color = FDS.Colors.characterHighlighted,
                            modifier = Modifier
                                .padding(
                                    horizontal = FDS.Sizer.Gap.gap24,
                                )
                                .align(Alignment.CenterHorizontally),
                        )
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
                        HorizontalDivider(
                            color = FDS.Colors.divider,
                            thickness = FDS.Sizer.Stroke.stroke1,
                        )
                    }
                },
                headline = {
                    datePickerState.selectedDateMillis?.let { millis ->
                        FoundationText(
                            // todo locale sau phaỉ chuyển lại theo language app
                            text = millis.toDateString(pattern = EEEE_dd_MMMM_yyyy, locale = Locale("vi", "VN")),
                            style = FDS.Typography.bodyB1Emphasized,
                            color = FDS.Colors.characterPrimary,
                            modifier = Modifier.padding(horizontal = FDS.Sizer.Gap.gap16),
                        )
                    }
                },
            )
        }
    }
}