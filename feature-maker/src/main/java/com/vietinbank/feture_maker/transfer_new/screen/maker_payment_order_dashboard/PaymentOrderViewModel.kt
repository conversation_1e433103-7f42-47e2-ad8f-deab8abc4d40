package com.vietinbank.feture_maker.transfer_new.screen.maker_payment_order_dashboard

import android.content.Context
import android.net.Uri
import androidx.compose.ui.focus.FocusRequester
import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.extensions.convertFileToBase64
import com.vietinbank.core_common.extensions.getFileNameFromUri
import com.vietinbank.core_common.extensions.getFileType
import com.vietinbank.core_common.extensions.resizeAndConvertUriToBase64
import com.vietinbank.core_common.models.ImageBase64Object
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyCurrency
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.home.ListLatestParams
import com.vietinbank.core_domain.models.home.TransactionInfo
import com.vietinbank.core_domain.models.login.AccountDefaultDomain
import com.vietinbank.core_domain.models.login.AccountListParams
import com.vietinbank.core_domain.models.maker.ApproverDomains
import com.vietinbank.core_domain.models.maker.BranchDomains
import com.vietinbank.core_domain.models.maker.BranchParams
import com.vietinbank.core_domain.models.maker.ContactDomains
import com.vietinbank.core_domain.models.maker.ContactListParams
import com.vietinbank.core_domain.models.maker.DataBankDomain
import com.vietinbank.core_domain.models.maker.GetPaymentTemplateListParams
import com.vietinbank.core_domain.models.maker.NextStatusTransactionByRuleParams
import com.vietinbank.core_domain.models.maker.TempTransactionDomains
import com.vietinbank.core_domain.models.maker.TempTransactionParams
import com.vietinbank.core_domain.models.maker.ValidatePaymentOrderTransferDomains
import com.vietinbank.core_domain.models.maker.ValidatePaymentOrderTransferParams
import com.vietinbank.core_domain.repository.cache.ITransferCacheManager
import com.vietinbank.core_domain.usecase.home.HomeUseCase
import com.vietinbank.core_domain.usecase.login.LoginUseCase
import com.vietinbank.core_domain.usecase.transfer.TransferUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.feature_maker.R
import com.vietinbank.feture_maker.maker_ui.adapter.AccountAdapter
import com.vietinbank.feture_maker.transfer_new.screen.bottomSheet.BottomSheetItem
import com.vietinbank.feture_maker.transfer_new.screen.bottomSheet.HourEntity
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE_PAYMENT_ORDER
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.TAB_RECENT
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.TAB_SAVED
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.TRANSFER_LIST_LATEST_PARAMS_PAGE_SIZE
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.TYPE_D
import com.vietinbank.feture_maker.transfer_new.screen.maker_payment_order_dashboard.PaymentOrderViewModel.PaymentOrderEvent.NavigateBack
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.BranchBottomSheetUiState
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.ChooseAccountBottomSheetUiState
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.ContactSavedBottomSheetUiState
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.HourPickerBottomSheetUiState
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.NextApproverBottomSheetUiState
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.RadioBottomSheetUiState
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.TransferValidationResult
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_dashboard.MakerTransferViewModel.MakerTransferAccountEvent.ClickContactItem
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale
import javax.inject.Inject

@HiltViewModel
class PaymentOrderViewModel @Inject constructor(
    private val transferUseCase: TransferUseCase,
    val transferCacheManager: ITransferCacheManager,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
    private val loginUseCase: LoginUseCase,
    private val moneyHelper: MoneyHelper,
    private val homeUseCase: HomeUseCase,
) : BaseViewModel() {
    private val _state = MutableStateFlow(PaymentOrderState())
    val state: StateFlow<PaymentOrderState> = _state.asStateFlow()

    private val _events = Channel<PaymentOrderEvent>(Channel.BUFFERED)
    val events = _events.receiveAsFlow()

    private val originalAccountList: MutableList<AccountDefaultDomain> = mutableListOf()
    private val originalBranchList: MutableList<BranchDomains> = mutableListOf()
    private var branchList: MutableList<BranchDomains> = mutableListOf()
    private var originalNextApproversList: MutableList<ApproverDomains> = mutableListOf()
    private val originalContactList: MutableList<ContactDomains> = mutableListOf()
    private val originalTempTransactionList: MutableList<TempTransactionDomains> = mutableListOf()
    private val originalTransactionInfoList: MutableList<TransactionInfo> = mutableListOf()

    private var isLoadingAccountDone = false
    private var isLoadingBranchDone = false
    private var transferTimeNow = BottomSheetItem(
        id = TransferConstants.Transfer.ID_TRANSFER_TIME_NOW,
        icon = R.drawable.ic_feature_maker_transfer_now,
        text = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_now),
        isSelected = true,
    )

    private var transferTimeSchedule = BottomSheetItem(
        id = TransferConstants.Transfer.ID_TRANSFER_TIME_SCHEDULE,
        icon = R.drawable.ic_feature_maker_transfer_schedule,
        text = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_schedule),
        isSelected = false,
    )
    private var transferTypeOUR = BottomSheetItem(
        id = TransferConstants.Transfer.ID_TRANSFER_TYPE_OUR,
        icon = R.drawable.ic_feature_maker_transfer_our,
        text = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_OUR),
        isSelected = true,
        content = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_OUR_content),
    )

    private var transferTypeBEN = BottomSheetItem(
        id = TransferConstants.Transfer.ID_TRANSFER_TYPE_BEN,
        icon = R.drawable.ic_feature_maker_transfer_ben,
        text = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_BEN),
        isSelected = false,
        content = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_BEN_content),
    )

    init {
        updateListTransferTime()
        updateListTransferType()
    }

    private fun getListLatest() {
        updateIsLoadingListRecent(true)
        if (transferCacheManager.getLatTestList()?.isNotEmpty() == true) {
            updateTransactionInfoList(
                transferCacheManager.getLatTestList()?.toMutableList() ?: mutableListOf(),
            )
            originalTransactionInfoList.clear()
            originalTransactionInfoList.addAll(
                transferCacheManager.getLatTestList()?.toMutableList() ?: mutableListOf(),
            )
            updateIsLoadingListRecent(false)
            return
        }
        launchJob(showLoading = true) {
            val res = homeUseCase.listLatest(
                ListLatestParams(
                    username = userProf.getUserName() ?: "",
                    role = userProf.getRoleId() ?: "",
                    status = Tags.HOME_LIST_LATEST_PARAMS_STATUS,
                    pageSize = TRANSFER_LIST_LATEST_PARAMS_PAGE_SIZE,
                    pageNum = Tags.HOME_LIST_LATEST_PARAMS_PAGE_NUM,
                ),
            )
            handleResource(res) { data ->
                updateTransactionInfoList(data.transactionInfoList ?: mutableListOf())
                transferCacheManager.saveLatestList(
                    data.transactionInfoList?.toMutableList() ?: mutableListOf(),
                )
                originalTransactionInfoList.clear()
                originalTransactionInfoList.addAll(
                    data.transactionInfoList?.toMutableList() ?: mutableListOf(),
                )
                updateIsLoadingListRecent(false)
            }
        }
    }

    private fun getPaymentTemplateList() {
        updateIsLoadingListTemp(true)
        if (transferCacheManager.getTempList()?.isNotEmpty() == true) {
            updateListTempDomains(
                transferCacheManager.getTempList()?.toMutableList() ?: mutableListOf(),
            )
            originalTempTransactionList.clear()
            originalTempTransactionList.addAll(
                transferCacheManager.getTempList()?.toMutableList() ?: mutableListOf(),
            )
            updateIsLoadingListTemp(false)
            return
        }
        launchJob(showLoading = true) {
            val params = GetPaymentTemplateListParams(
                TempTransactionParams(tranType = Tags.TransferType.TYPE_PAYMENT_ORDER_TRANSFER),
                username = userProf.getUserName() ?: "",
            )
            val res = transferUseCase.getPaymentTemplateList(params)
            handleResource(res) { data ->
                updateListTempDomains(data.tempTransactionList)
                transferCacheManager.saveTempList(data.tempTransactionList)
                originalTempTransactionList.clear()
                originalTempTransactionList.addAll(data.tempTransactionList)
                updateIsLoadingListTemp(false)
            }
        }
    }

    private fun getContactList() {
        updateIsLoadingListContact(true)
        if (transferCacheManager.getContactList()?.isNotEmpty() == true) {
            updateListContactDomains(
                transferCacheManager.getContactList()?.toMutableList() ?: mutableListOf(),
            )
            originalContactList.clear()
            originalContactList.addAll(
                transferCacheManager.getContactList()?.toMutableList() ?: mutableListOf(),
            )
            updateIsTransferTypeKeyContactSaveBottomSheet()
            updateIsLoadingListContact(false)
            if (0 == state.value.selectedIndex) {
                canShowBottomSheetContactSaved(true)
            }
            return
        }
        launchJob(showLoading = true) {
            val params = ContactListParams(
                username = userProf.getUserName().toString(),
                serviceId = "",
            )
            val res = transferUseCase.contactList(params)
            handleResource(res) { data ->
                updateListContactDomains(data.contacts)
                updateIsTransferTypeKeyContactSaveBottomSheet()
                transferCacheManager.saveContactist(data.contacts)
                originalContactList.clear()
                originalContactList.addAll(data.contacts)
                updateIsLoadingListContact(false)
                if (0 == state.value.selectedIndex) {
                    canShowBottomSheetContactSaved(true)
                }
            }
        }
    }

    private fun updateIsTransferTypeKeyContactSaveBottomSheet() {
        _state.update { currentState ->
            val value =
                currentState.contactSavedBottomSheetUiState.copy(isTransferTypeKey = KEY_TRANSFER_TYPE_PAYMENT_ORDER)
            currentState.copy(
                contactSavedBottomSheetUiState = value,
            )
        }
    }

    private fun updateListContactDomains(value: MutableList<ContactDomains>) {
        _state.update { currentState ->
            val listContactDomains =
                currentState.contactSavedBottomSheetUiState.copy(
                    isEdited = false,
                    listContactDomains = value,
                )
            currentState.copy(
                contactSavedBottomSheetUiState = listContactDomains,
            )
        }
    }

    private fun updateIsLoadingListContact(value: Boolean) {
        _state.update { currentState ->
            val isLoadingListContact =
                currentState.contactSavedBottomSheetUiState.copy(isLoadingListContact = value)
            currentState.copy(
                contactSavedBottomSheetUiState = isLoadingListContact,
            )
        }
    }

    private fun updateIsLoadingListTemp(value: Boolean) {
        _state.update { currentState ->
            val isLoadingListTemp =
                currentState.contactSavedBottomSheetUiState.copy(isLoadingListTemp = value)
            currentState.copy(
                contactSavedBottomSheetUiState = isLoadingListTemp,
            )
        }
    }

    private fun updateIsLoadingListRecent(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                isLoadingListRecent = value,
            )
        }
    }

    private fun updateListTempDomains(value: MutableList<TempTransactionDomains>) {
        _state.update { currentState ->
            val listTempDomains =
                currentState.contactSavedBottomSheetUiState.copy(listTempDomains = value)
            currentState.copy(
                contactSavedBottomSheetUiState = listTempDomains,
            )
        }
    }

    private fun updateTransactionInfoList(value: MutableList<TransactionInfo>) {
        _state.update { currentState ->
            currentState.copy(
                transactionInfoList = value,
            )
        }
    }

    private fun updateSearchContactText(value: String) {
        _state.update { currentState ->
            val searchContactText =
                currentState.contactSavedBottomSheetUiState.copy(searchContactText = value)
            currentState.copy(
                contactSavedBottomSheetUiState = searchContactText,
            )
        }
    }

    private fun updateSearchRecentText(value: String) {
        _state.update { currentState ->
            currentState.copy(
                searchRecentText = value,
            )
        }
    }

    private fun updateSelectedTabContactIndex(value: Int) {
        _state.update { currentState ->
            val selectedTabContactIndex =
                currentState.contactSavedBottomSheetUiState.copy(selectedTabIndex = value)
            currentState.copy(
                contactSavedBottomSheetUiState = selectedTabContactIndex,
            )
        }
    }

    fun getFileUriState(ctx: Context, uri: Uri) {
        val fileType = getFileType(context = ctx, uri = uri)
        val isImage = fileType?.let { type ->
            type.contains("image") || type.contains("jpeg") || type.contains("jpg")
        } ?: false
        val imageBase64Object = ImageBase64Object(
            imageBase64 = if (isImage) {
                resizeAndConvertUriToBase64(context = ctx, uri = uri)
            } else {
                convertFileToBase64(context = ctx, uri = uri)
            },
            fileName = getFileNameFromUri(context = ctx, uri = uri),
        )
        updateImageBase64Object(imageBase64Object)
        updateUriFile(uri)
    }

    private fun updateImageBase64Object(imageBase64Object: ImageBase64Object?) {
        _state.update { currentState ->
            currentState.copy(
                imageBase64Object = imageBase64Object,
            )
        }
    }

    private fun updateUriFile(value: Uri) {
        _state.update { currentState ->
            currentState.copy(
                uriFile = value,
            )
        }
    }

    private fun nextStatusTransactionByRule() {
        launchJob(showLoading = true) {
            val params = buildNextStatusParams()
            val res = transferUseCase.nextStatusTransactionByRule(params)

            handleResource(res) { data ->
                val nextApprovers = data.nextApprovers.orEmpty().toMutableList()
                nextApprovers.forEach { it.isSelected = true }
                updateOnAllItemApproverSelected(true)
                updateNextApproversList(nextApprovers)
                originalNextApproversList.clear()
                originalNextApproversList.addAll(nextApprovers)

                updateCanShowBottomSheetNextApprovers(true)
            }
        }
    }

    private fun buildNextStatusParams(): NextStatusTransactionByRuleParams {
        val stateValue = state.value
        val serviceType = Tags.TransferType.SERVICE_TYPE_PAYORD
        return NextStatusTransactionByRuleParams(
            amount = stateValue.amountHolder,
            creator = userProf.getUserName() ?: "",
            currentStatus = "",
            currentUserGroup = "",
            currentUserLevel = "0",
            customerNumber = userProf.getCifNo() ?: "",
            fromAccountNo = stateValue.currentAccountDefaultDomain?.accountNo.orEmpty(),
            serviceCode = serviceType,
            toAccountNo = stateValue.beneficiaryAccountHolder,
            username = userProf.getUserName() ?: "",
            mtId = "",
        )
    }

    private fun updateCanShowBottomSheetNextApprovers(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                canShowBottomSheetNextApprovers = value,
            )
        }
    }

    private fun updateListHour(generateTimeSlots: List<HourEntity>) {
        val updatedList = generateTimeSlots.map { hourItem ->
            when (hourItem.hourID) {
                state.value.currentHourEntity?.hourID -> hourItem.copy(isSelected = true)
                else -> hourItem.copy(isSelected = false)
            }
        }
        _state.update { currentState ->
            currentState.copy(
                itemsHour = updatedList,
            )
        }
    }

    private fun generateTimeSlots(): List<HourEntity> {
        val formatter = SimpleDateFormat("HH:mm:ss", Locale.getDefault())

        val startOfDay = Calendar.getInstance().apply {
            set(Calendar.HOUR_OF_DAY, 8)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }

        val endOfDay = Calendar.getInstance().apply {
            set(Calendar.HOUR_OF_DAY, 16)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }

        val timeSlots = mutableListOf<HourEntity>()
        val current = startOfDay.clone() as Calendar
        var id = 1

        while (current <= endOfDay) {
            timeSlots.add(
                HourEntity(
                    hourID = id.toString(),
                    hourString = formatter.format(current.time),
                    isSelected = false,
                ),
            )
            current.add(Calendar.MINUTE, 30)
            id++
        }

        return timeSlots
    }
    fun getBranch() {
        val cachedList = transferCacheManager.getBranchList()
        if (!cachedList.isNullOrEmpty()) {
            branchList = cachedList.toMutableList()
            isLoadingBranchDone = true
            updateOriginalBranchList(branchList)

            if (isLoadingAccountDone) sortListBranch(branchList)
            return
        }

        launchJob(showLoading = true) {
            val params = BranchParams(
                branchId = "",
                username = userProf.getUserName().orEmpty(),
            )
            val res = transferUseCase.getBranch(params)
            handleResource(res) { data ->
                branchList = data.data.toMutableList()
                transferCacheManager.saveBranch(branchList)
                isLoadingBranchDone = true

                if (isLoadingAccountDone) {
                    sortListBranch(branchList)
                } else {
                    updateOriginalBranchList(branchList)
                }
            }
        }
    }

    private fun sortListBranch(branchList: MutableList<BranchDomains>) {
        val branchID = state.value.currentAccountDefaultDomain?.branchId ?: return

        val sortedList = branchList.sortedWith { a, b ->
            when {
                a.branchId == branchID && b.branchId != branchID -> -1
                a.branchId != branchID && b.branchId == branchID -> 1
                else -> 0
            }
        }.toMutableList()

        updateBranchListBottomSheet(sortedList)
        updateOriginalBranchList(sortedList)
    }

    private fun updateOriginalBranchList(list: MutableList<BranchDomains>) {
        originalBranchList.apply {
            clear()
            addAll(list)
        }
    }

    fun getAccList() {
        val cachedAccounts = transferCacheManager.getAccountsPM()
        if (cachedAccounts?.isNotEmpty() == true) {
            processAccounts(cachedAccounts)
        } else {
            fetchAccountsFromApi()
        }
    }

    private fun fetchAccountsFromApi() {
        launchJob(showLoading = true) {
            val params = AccountListParams(
                accountType = "",
                currencySort = "",
                username = userProf.getUserName().orEmpty(),
                serviceType = Tags.TransferType.SERVICE_TYPE_PAYORD,
            )
            val result = loginUseCase.accList(params)

            handleResource(result) { data ->
                val activeAccounts = filterActiveAccounts(data.accountDefault)
                cacheAccountsForTransferType(activeAccounts)
                processAccounts(data.accountDefault)
            }
        }
    }

    private fun cacheAccountsForTransferType(activeAccounts: MutableList<AccountDefaultDomain>) {
        transferCacheManager.saveAccountsPM(activeAccounts)
    }

    private fun filterActiveAccounts(accounts: List<AccountDefaultDomain>): MutableList<AccountDefaultDomain> {
        return accounts.filter { it.status == "0" }.toMutableList()
    }

    private fun processAccounts(accounts: MutableList<AccountDefaultDomain>) {
        updateCurrentAccountDefault(null)
        val groupedByAccountType: Map<String?, List<AccountDefaultDomain>> =
            accounts.groupBy { it.accountType }
        val adapterData = mapGroupedAccountsToAdapterData(groupedByAccountType)
        updateAccountList(adapterData.toMutableList())
        setDefaultAccount(adapterData)
        updateIsFocusedBankNameHolder(true)
    }

    private fun setDefaultAccount(
        adapterData: MutableList<AccountDefaultDomain>,
    ) {
        getDefaultAccount(adapterData)?.let { defaultAccount ->
            updateCurrentAccountDefault(defaultAccount)
        }
        isLoadingAccountDone = true
        if (isLoadingBranchDone) {
            sortListBranch(branchList)
        }
    }

    private fun mapGroupedAccountsToAdapterData(groupedByAccountType: Map<String?, List<AccountDefaultDomain>>): MutableList<AccountDefaultDomain> {
        val adapterData = mutableListOf<AccountDefaultDomain>()

        // Process each group
        groupedByAccountType.forEach { (accountType, accounts) ->
            // First add a title item for this group
            adapterData.add(
                AccountDefaultDomain(
                    group = AccountAdapter.TYPE_GROUP_TITLE,
                    accountType = accountType,
                    accountName = "",
                    accountNo = "",
                    accrueInterest = "",
                    accruedInterest = "",
                    acctNbr = "",
                    acctType = "",
                    aliasName = "",
                    availableBalance = "",
                    benBankName = "",
                    beneficiaryName = "",
                    branchId = "",
                    branchName = "",
                    cifNo = "",
                    closeDate = "",
                    closingOutstandingBalance = "",
                    contractNbr = "",
                    corpName = "",
                    creditCardLst = emptyList(),
                    creditLimit = "",
                    currency = "",
                    currentBalance = "",
                    depositCardSerialNumber = "",
                    depositContractNumber = "",
                    districtId = "",
                    districtName = "",
                    dueDate = "",
                    escrowAmt = "",
                    feeAcctNo = "",
                    feePlans = emptyList(),
                    fullPayLeft = "",
                    holdBalance = "",
                    interestAmount = "",
                    interestNotDueBilled = "",
                    interestPastDue = "",
                    interestRate = "",
                    interestTerm = "",
                    issuedDate = "",
                    lang = "",
                    lateCharge = "",
                    mainAccountId = "",
                    maturityDate = "",
                    minPayLeft = "",
                    minimumAmount = "",
                    nextPaymentDate = "",
                    openDate = "",
                    origIssueDt = "",
                    outstandingBalance = "",
                    payOffAmount = "",
                    penaltyAmount = "",
                    principalAmount = "",
                    principalNotDueBilled = "",
                    principalPastDue = "",
                    productId = "",
                    productName = "",
                    provinceId = "",
                    provinceName = "",
                    remainingLoanTotal = "",
                    settlementDate = "",
                    statementDate = "",
                    status = "",
                    statusName = "",
                    term = "",
                    totalCredit = "",
                    totalPayment = "",
                    cifName = "",
                ),
            )

            // Then add all accounts for this group, setting their type to content
            accounts.forEach { account ->
                adapterData.add(
                    account.copy(group = AccountAdapter.TYPE_GROUP_CONTENT),
                )
            }
        }

        return adapterData
    }

    private fun updateCurrentBranchDefault(value: BranchDomains?) {
        val updatedList = state.value.branchBottomSheetUiState.listBranch.map { itemBranch ->
            when (itemBranch.branchId) {
                state.value.currentBranchDefaultDomain?.branchId -> itemBranch.copy(isSelected = false)
                value?.branchId -> itemBranch.copy(isSelected = true)
                else -> itemBranch
            }
        }
        updateBranchListBottomSheet(updatedList.toMutableList())
        _state.update { currentState ->
            currentState.copy(
                currentBranchDefaultDomain = value,
            )
        }
    }

    private fun updateCurrentAccountDefault(value: AccountDefaultDomain?) {
        val updatedList = state.value.accountList?.map { account ->
            when (account.accountNo) {
                state.value.currentAccountDefaultDomain?.accountNo -> account.copy(isSelected = false)
                value?.accountNo -> account.copy(isSelected = true)
                else -> account
            }
        }

        updateAccountListBottomSheet(updatedList?.toMutableList() ?: mutableListOf())
        updateOriginalAccountList(updatedList?.toMutableList() ?: mutableListOf())
        updateAccountList(updatedList?.toMutableList() ?: mutableListOf())

        _state.update { currentState ->
            currentState.copy(
                currentAccountDefaultDomain = value,
            )
        }
        // nếu tài khoản type D thì update luôn, còn không thì phải lấy tài khoản mặc định của danh sách gốc api trả về
        if (TYPE_D == value?.accountType) {
            _state.update { currentState ->
                currentState.copy(
                    currentAccountDefaultDomainDDA = value,
                )
            }
        } else {
            _state.update { currentState ->
                currentState.copy(
                    currentAccountDefaultDomainDDA = getDefaultAccount(
                        state.value.accountList?.toMutableList() ?: mutableListOf(),
                    ),
                )
            }
        }
    }

    private fun updateBranchListBottomSheet(value: MutableList<BranchDomains>) {
        _state.update { currentState ->
            val items = currentState.branchBottomSheetUiState.copy(listBranch = value)
            currentState.copy(
                branchBottomSheetUiState = items,
            )
        }
    }

    private fun updateAccountList(value: List<AccountDefaultDomain>) {
        _state.update { currentState ->
            currentState.copy(
                accountList = value,
            )
        }
    }

    private fun onSelectedIndexChange(value: Int) {
        _state.update { currentState ->
            currentState.copy(
                selectedIndex = value,
            )
        }
    }

    private fun updateIsFocusedBankNameHolder(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                isFocusBankNameHolder = if (currentState.bankNameHolder.isNotEmpty()) {
                    false
                } else {
                    value
                },
            )
        }
    }
    private fun updateIsFocusAccountNameHolder(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                isFocusAccountNameHolder = if (currentState.accountNameHolder.isNotEmpty()) {
                    false
                } else {
                    value
                },
            )
        }
    }

    private fun updateIsFocusBeneficiaryAccountHolder(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                isFocusBeneficiaryAccountHolder = if (currentState.beneficiaryAccountHolder.isNotEmpty()) {
                    false
                } else {
                    value
                },
            )
        }
    }

    private fun updateBankNameHolder(value: String) {
        _state.update { currentState ->
            currentState.copy(
                bankNameHolder = value,
            )
        }
    }

    private fun updateAccountNameHolder(value: String) {
        _state.update { currentState ->
            currentState.copy(
                accountNameHolder = value,
            )
        }
    }

    private fun updateBeneficiaryAccountHolder(value: String, isValidate: Boolean = true) {
        _state.update { currentState ->
            currentState.copy(
                beneficiaryAccountHolder = value,
            )
        }
        if (isValidate) {
            _state.update { currentState ->
                currentState.copy(
                    isErrorBeneficiaryAccount = value.isEmpty(),
                )
            }
        }

        if (value.isEmpty() && isValidate) {
            _state.update { currentState ->
                currentState.copy(
                    isErrorBeneficiaryAccountMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_beneficiary_account_input),
                )
            }
        }
    }

    private fun updateAccountListBottomSheet(value: MutableList<AccountDefaultDomain>) {
        _state.update { currentState ->
            val accountList = currentState.chooseAccountBottomSheetUiState.copy(accountList = value)
            currentState.copy(
                chooseAccountBottomSheetUiState = accountList,
            )
        }
    }

    private fun updateSearchAccountText(value: String) {
        _state.update { currentState ->
            val searchAccountText =
                currentState.chooseAccountBottomSheetUiState.copy(searchAccountText = value)
            currentState.copy(
                chooseAccountBottomSheetUiState = searchAccountText,
            )
        }
    }

    private fun updateOriginalAccountList(listDataAccount: MutableList<AccountDefaultDomain>) {
        originalAccountList.clear()
        originalAccountList.addAll(listDataAccount)
    }

    private fun getDefaultAccount(accountDefault: MutableList<AccountDefaultDomain>): AccountDefaultDomain? {
        return accountDefault.maxByOrNull {
            it.currentBalance?.toDoubleOrNull() ?: Double.MIN_VALUE
        }
    }

    private fun clearAllFocus() {
        updateIsFocusedBankNameHolder(false)
        updateIsFocusBeneficiaryAccountHolder(false)
        updateIsFocusAccountNameHolder(false)
        updateIsFocusedAmount(false)
        updateIsFocusedContent(false)
    }

    private fun canShowBottomSheetChooseAccount(show: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                canShowBottomSheetChooseAccount = show,
            )
        }
    }

    private fun canShowBottomSheetChooseBranch(show: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                canShowBottomSheetChooseBranch = show,
            )
        }
    }

    private fun updateSearchTextBranchText(value: String) {
        _state.update { currentState ->
            val searchBankText =
                currentState.branchBottomSheetUiState.copy(searchBranchText = value)
            currentState.copy(
                branchBottomSheetUiState = searchBankText,
            )
        }
    }

    private fun updateAmountHolder(value: String) {
        _state.update { currentState ->
            currentState.copy(
                amountHolder = value,
            )
        }
        if (value.isNotEmpty()) {
            updateAmountHolderReader(
                readAmountInWord(
                    amount = value.replace(",", ""),
                    ccy = state.value.currentAccountDefaultDomain?.currency
                        ?: MoneyCurrency.VND.name,
                ),
            )
        }
    }

    fun readAmountInWord(amount: String? = null, ccy: String? = null) =
        moneyHelper.convertAmountToWords(amount ?: "", ccy ?: MoneyCurrency.VND.name)

    private fun updateAmountHolderReader(value: String) {
        _state.update { currentState ->
            currentState.copy(
                amountHolderReader = value,
            )
        }
    }

    private fun updateIsFocusedAmount(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                isFocusedAmount = if (currentState.amountHolder.isNotEmpty()) {
                    false
                } else {
                    value
                },
            )
        }
    }

    private fun updateContentHolder(value: String) {
        _state.update { currentState ->
            currentState.copy(
                contentHolder = value,
            )
        }
    }

    private fun updateIsFocusedContent(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                isFocusedContent = if (currentState.contentHolder.isNotEmpty()) {
                    false
                } else {
                    value
                },
            )
        }
    }

    private fun canShowBottomSheetTransferTime(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                canShowBottomSheetTransferTime = value,
            )
        }
    }

    private fun canShowBottomSheetTransferHour(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                canShowBottomSheetTransferHour = value,
            )
        }
    }

    private fun updateTransferTime(value: BottomSheetItem) {
        _state.update { currentState ->
            currentState.copy(
                transferTime = value,
            )
        }
    }

    private fun updateTransferDate(value: String) {
        _state.update { currentState ->
            currentState.copy(
                transferDate = value,
            )
        }
    }

    private fun isShowTransferTimeSchedule(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                showTransferTimeSchedule = value,
            )
        }
    }

    private fun updateListTransferTime() {
        val selectedItems = mutableListOf(transferTimeNow, transferTimeSchedule)
        updateTransferTime(transferTimeNow)
        isShowTransferTimeSchedule(false)
        _state.update { currentState ->
            val items = currentState.radioBottomSheetTransferTimeUiState.copy(items = selectedItems)
            currentState.copy(
                radioBottomSheetTransferTimeUiState = items,
            )
        }
    }

    private fun updateOnTransferTimeText(value: String) {
        _state.update { currentState ->
            currentState.copy(
                transferTimeText = value,
            )
        }
    }

    private fun updateCurrentHourEntity(value: HourEntity) {
        _state.update { currentState ->
            currentState.copy(
                currentHourEntity = value,
            )
        }
    }

    private fun updateHourPickerBottomSheetUiState() {
        _state.update { currentState ->
            val items =
                currentState.hourPickerBottomSheetUiState.copy(itemsHour = state.value.itemsHour)
            currentState.copy(
                hourPickerBottomSheetUiState = items,
            )
        }
    }

    private fun canShowBottomSheetTransferType(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                canShowBottomSheetTransferType = value,
            )
        }
    }

    private fun updateTransferType(value: BottomSheetItem) {
        _state.update { currentState ->
            currentState.copy(
                transferTypeFee = value,
            )
        }
    }

    private fun updateListTransferType() {
        val selectedItems = mutableListOf(transferTypeOUR, transferTypeBEN)
        updateTransferType(transferTypeOUR)
        _state.update { currentState ->
            val items =
                currentState.radioBottomSheetTransferTypeFeeUiState.copy(items = selectedItems)
            currentState.copy(
                radioBottomSheetTransferTypeFeeUiState = items,
            )
        }
    }

    private fun validatePaymentOrderFields(): TransferValidationResult? {
        val s = state.value
        return when {
            s.bankNameHolder.isBlank() -> TransferValidationResult(
                focusAction = { updateIsFocusedBankNameHolder(true) },
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_payment_order_dashboard_bank_name_input),
            )

            s.beneficiaryAccountHolder.isBlank() -> TransferValidationResult(
                focusAction = { updateIsFocusBeneficiaryAccountHolder(true) },
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_account_beneficiary_account_name_validate),
            )

            s.accountNameHolder.isBlank() -> TransferValidationResult(
                focusAction = { updateIsFocusAccountNameHolder(true) },
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.manager_filter_beneficiary_name_input),
            )

            s.currentAccountDefaultDomain == null -> TransferValidationResult(
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_account_current_account_default_validate),
            )

            s.currentBranchDefaultDomain == null -> TransferValidationResult(
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_payment_order_dashboard_choose_processing_branch),
            )

            s.amountHolder.isBlank() -> TransferValidationResult(
                focusAction = { updateIsFocusedAmount(true) },
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_account_amount_holder_validate),
            )

            s.contentHolder.isBlank() -> TransferValidationResult(
                focusAction = { updateIsFocusedContent(true) },
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_account_content_holder_validate),
            )

            else -> null
        }
    }

    private fun validateNextStatusTransactionByRuleFields(): TransferValidationResult? {
        val s = state.value
        return when {
            s.currentAccountDefaultDomain == null -> TransferValidationResult(
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_account_current_account_default_validate),
            )

            s.beneficiaryAccountHolder.isBlank() -> TransferValidationResult(
                focusAction = { updateIsFocusBeneficiaryAccountHolder(true) },
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_account_beneficiary_account_name_validate),
            )

            s.amountHolder.isBlank() -> TransferValidationResult(
                focusAction = { updateIsFocusedAmount(true) },
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_account_amount_holder_validate),
            )

            else -> null
        }
    }

    private fun handleErrorValidate(error: TransferValidationResult) {
        viewModelScope.launch {
            _events.send(PaymentOrderEvent.ShowErrorValidateFieldTransferAccount(error))
        }
    }

    private fun updateSwitchNextApproversChange(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                switchNextApprovers = value,
            )
        }
    }

    private fun clearApprovers() {
        updateNextApproversList(mutableListOf())
        updateNextApproversListString()
        updateOnAllItemApproverSelected(false)
        updateSwitchNextApproversChange(false)
    }

    private fun updateNextApproversListString() {
        _state.update { currentState ->
            currentState.copy(
                nextApproversListString = getNextApproversListString(),
            )
        }
    }

    private fun getNextApproversListString(): String {
        return if (state.value.nextApproverBottomSheetUiState.nextApproversList.all { it.isSelected }) {
            resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_next_approver_register)
        } else if (state.value.nextApproverBottomSheetUiState.nextApproversList.any { it.isSelected }) {
            resourceProvider.getString(
                com.vietinbank.core_ui.R.string.maker_transfer_dashboard_next_approver_people,
                state.value.nextApproverBottomSheetUiState.nextApproversList.filter { it.isSelected }.size.toString(),
            )
        } else {
            ""
        }
    }

    private fun updateOnAllItemApproverSelected(value: Boolean) {
        _state.update { currentState ->
            val onAllItemSelected =
                currentState.nextApproverBottomSheetUiState.copy(onAllItemApproverSelected = value)
            currentState.copy(
                nextApproverBottomSheetUiState = onAllItemSelected,
            )
        }
    }

    private fun updateNextApproversList(value: MutableList<ApproverDomains>) {
        _state.update { currentState ->
            val items =
                currentState.nextApproverBottomSheetUiState.copy(nextApproversList = value)
            currentState.copy(
                nextApproverBottomSheetUiState = items,
            )
        }
    }

    private fun updateCanShowBottomSheetChooseTypeUpload(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                canShowBottomSheetChooseTypeUpload = value,
            )
        }
    }

    private fun validatePaymentOrderTransfer() {
        launchJob(showLoading = true) {
            val params = ValidatePaymentOrderTransferParams(
                amount = state.value.amountHolder,
                content = state.value.contentHolder,
                feePayMethod = state.value.transferTypeFee?.id ?: "",
                fromAccountNo = state.value.currentAccountDefaultDomain?.accountNo ?: "",
                toAccountName = state.value.accountNameHolder,
                toAccountNo = state.value.beneficiaryAccountHolder,
                toBankName = state.value.bankNameHolder,
                username = userProf.getUserName() ?: "",
                file = state.value.imageBase64Object?.imageBase64?.replace("\n", "") ?: "",
                processTime = if (TransferConstants.Transfer.ID_TRANSFER_TIME_NOW == state.value.transferTime?.id) "" else state.value.transferTimeText,
                fileName = state.value.imageBase64Object?.fileName ?: "",
                branchId = state.value.currentBranchDefaultDomain?.branchId ?: "",
                bankProcess = state.value.currentBranchDefaultDomain?.bankId ?: "",
                nextApprovers = state.value.nextApproverBottomSheetUiState.nextApproversList.filter { it.isSelected },
                fromAccountFeeNo = state.value.currentAccountDefaultDomainDDA?.accountNo ?: "",
            )
            val res = transferUseCase.validatePaymentOrderTransfer(params)
            handleResource(res) { data ->
                data.toBankIconURL = ""
                data.uriFile = state.value.uriFile.toString()
                data.uriFileName = state.value.imageBase64Object?.fileName ?: ""
                data.typeTransfer = Tags.TransferType.TYPE_PAYMENT_ORDER
                data.fromAccBalance = Utils.g().getDotMoneyHasCcy(
                    state.value.currentAccountDefaultDomain?.availableBalance ?: "",
                    state.value.currentAccountDefaultDomain?.currency ?: "",
                )
                data.bankDomain = DataBankDomain(
                    binCode = "99998",
                    bankName = state.value.bankNameHolder,
                    shortName = state.value.bankNameHolder,
                    ebankCode = "99998",
                    icon = "",
                    name = "",
                    type = "",
                    status = "",
                )

                _events.send(PaymentOrderEvent.ValidatePaymentOrderState(data))
            }
        }
    }

    private fun canShowBottomSheetContactSaved(show: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                canShowBottomSheetContactSaved = show,
            )
        }
    }

    override fun onDisplayErrorMessage(exception: AppException) {
        if (exception is AppException.ApiException) {
            when (exception.requestPath) {
                Constants.MB_ACCOUNT_LIST,
                -> {
                    sendErrorMessage(exception.message)
                }
                else -> {
                    super.onDisplayErrorMessage(exception)
                    return
                }
            }
            return
        }
        super.onDisplayErrorMessage(exception)
    }

    private fun sendErrorMessage(message: String?) {
        viewModelScope.launch {
            _events.send(PaymentOrderEvent.ShowErrorMess(message.orEmpty()))
        }
    }

    fun onAction(action: PaymentOrderAction) {
        when (action) {
            PaymentOrderAction.OnBackPressed -> {
                viewModelScope.launch {
                    _events.send(NavigateBack)
                }
            }

            is PaymentOrderAction.OnSelectedIndex -> {
                onSelectedIndexChange(action.selectedIndex)
                if (TAB_SAVED == action.selectedIndex) {
                    getContactList()
                    getPaymentTemplateList()
                } else if (TAB_RECENT == action.selectedIndex) {
                    getListLatest()
                }
            }

            is PaymentOrderAction.OnBankNameHolderChange -> {
                updateBankNameHolder(action.bankNameHolder)
            }

            is PaymentOrderAction.OnFocusBankNameHolderChange -> {
                updateIsFocusedBankNameHolder(action.isFocusBankNameHolder)
            }

            is PaymentOrderAction.OnBeneficiaryAccountFocusChange -> {
                updateIsFocusBeneficiaryAccountHolder(action.isFocusBeneficiaryAccountHolder)
            }

            is PaymentOrderAction.OnBeneficiaryAccountTextChange -> {
                updateBeneficiaryAccountHolder(action.beneficiaryAccountHolder)
            }

            is PaymentOrderAction.OnClearBeneficiaryAccount -> {
                updateBeneficiaryAccountHolder("")
            }

            is PaymentOrderAction.OnAccountNameHolder -> {
                updateAccountNameHolder(action.accountNameHolder)
            }

            is PaymentOrderAction.OnFocusAccountNameHolderChange -> {
                updateIsFocusAccountNameHolder(action.isFocusAccountNameHolder)
            }

            is PaymentOrderAction.OnSearchAccountText -> {
                updateSearchAccountText(action.searchAccountText)
                val searchText = action.searchAccountText.trim()
                val filteredBanks = if (searchText.isBlank()) {
                    originalAccountList
                } else {
                    originalAccountList.filter { item ->
                        item.accountName?.contains(
                            searchText,
                            ignoreCase = true,
                        ) == true || item.aliasName?.contains(
                            searchText,
                            ignoreCase = true,
                        ) == true || item.accountNo?.contains(
                            searchText, ignoreCase = true,
                        ) == true
                    }
                }
                updateAccountListBottomSheet(filteredBanks.toMutableList())
            }

            is PaymentOrderAction.CanShowBottomSheetChooseAccount -> {
                clearAllFocus()
                if (!action.isShow) {
                    canShowBottomSheetChooseAccount(false)
                }

                val baseList = if (action.isOnlyTypeDDA) {
                    state.value.accountList?.filter { it.accountType == TYPE_D }?.toMutableList()
                        ?: mutableListOf()
                } else {
                    state.value.accountList?.toMutableList() ?: mutableListOf()
                }

                val updatedList = baseList.map { account ->
                    val isCurrentDefault =
                        account.accountNo == state.value.currentAccountDefaultDomain?.accountNo
                    val isCurrentDDA =
                        account.accountNo == state.value.currentAccountDefaultDomainDDA?.accountNo

                    when {
                        state.value.currentAccountDefaultDomain?.accountType == TYPE_D -> {
                            when {
                                isCurrentDefault -> account.copy(isSelected = true)
                                isCurrentDDA -> account.copy(isSelected = false)
                                else -> account
                            }
                        }

                        else -> {
                            when {
                                isCurrentDefault -> account.copy(isSelected = false)
                                isCurrentDDA -> account.copy(isSelected = true)
                                else -> account
                            }
                        }
                    }
                }.toMutableList()

                updateAccountListBottomSheet(updatedList)
                updateOriginalAccountList(updatedList)
                canShowBottomSheetChooseAccount(action.isShow)
            }

            is PaymentOrderAction.OnCurrentAccountDataChange -> {
                updateCurrentAccountDefault(action.accountDefaultDomain)
                canShowBottomSheetChooseAccount(show = false)
            }

            is PaymentOrderAction.CanShowBottomSheetChooseBranch -> {
                clearAllFocus()
                canShowBottomSheetChooseBranch(action.isShow)
            }

            is PaymentOrderAction.OnSearchTextBranchText -> {
                updateSearchTextBranchText(action.searchTextBranch)
                val searchText = action.searchTextBranch.trim()
                val filteredBanks = if (searchText.isBlank()) {
                    originalBranchList
                } else {
                    originalBranchList.filter { bank ->
                        bank.branchName?.contains(
                            searchText,
                            ignoreCase = true,
                        ) == true || bank.provinceName?.contains(
                            searchText, ignoreCase = true,
                        ) == true
                    }
                }
                updateBranchListBottomSheet(filteredBanks.toMutableList())
            }

            is PaymentOrderAction.OnCurrentBranchDataChange -> {
                updateCurrentBranchDefault(action.branchDomains)
                canShowBottomSheetChooseBranch(show = false)
            }

            is PaymentOrderAction.OnAmountHolderChange -> {
                updateAmountHolder(action.amountHolder)
            }

            is PaymentOrderAction.OnFocusAmountChange -> {
                updateIsFocusedAmount(action.isFocusedAmount)
            }

            is PaymentOrderAction.OnContentHolderChange -> {
                updateContentHolder(action.contentHolder)
            }

            is PaymentOrderAction.OnFocusContentChange -> {
                updateIsFocusedContent(action.isFocusedContent)
            }

            is PaymentOrderAction.CanShowBottomSheetTransferTime -> {
                canShowBottomSheetTransferTime(action.isShow)
                clearAllFocus()
            }

            is PaymentOrderAction.OnChooseTransferTime -> {
                updateTransferTime(action.transferTime)
            }

            is PaymentOrderAction.ShowTransferTimeSchedule -> {
                clearAllFocus()
                isShowTransferTimeSchedule(action.show)
            }

            is PaymentOrderAction.OnTransferTimeTextChange -> {
                updateTransferTime(transferTimeSchedule)
                updateCurrentHourEntity(action.transferHourEntity)
                updateOnTransferTimeText(state.value.transferDate + " - " + action.transferHourEntity.hourString)
                canShowBottomSheetTransferHour(false)
                isShowTransferTimeSchedule(true)
            }

            is PaymentOrderAction.CanShowBottomSheetTransferHour -> {
                updateListHour(generateTimeSlots())
                updateHourPickerBottomSheetUiState()
                canShowBottomSheetTransferHour(action.isShow)
            }

            is PaymentOrderAction.OnTransferDateTextChange -> {
                if (action.transferDate != state.value.transferDate) {
                    updateCurrentHourEntity(HourEntity())
                }
                updateTransferDate(action.transferDate)
            }

            is PaymentOrderAction.CanShowBottomSheetTransferType -> {
                canShowBottomSheetTransferType(action.isShow)
                clearAllFocus()
            }

            is PaymentOrderAction.OnChooseTransferType -> {
                clearAllFocus()
                updateTransferType(action.transferType)
            }

            is PaymentOrderAction.OnSwitchNextApproversChange -> {
                clearAllFocus()
                val errorObject = validateNextStatusTransactionByRuleFields()
                clearAllFocus()
                if (errorObject != null) {
                    handleErrorValidate(errorObject)
                    return
                }

                updateSwitchNextApproversChange(action.switchNextApprovers)

                if (state.value.switchNextApprovers) {
                    nextStatusTransactionByRule()
                    updateOnAllItemApproverSelected(false)
                } else {
                    updateNextApproversList(mutableListOf())
                    updateNextApproversListString()
                }
            }

            is PaymentOrderAction.DismissShowBottomSheetNextApprovers -> {
                updateCanShowBottomSheetNextApprovers(false)

                val hasSelectedItem =
                    state.value.nextApproverBottomSheetUiState.nextApproversList.any { it.isSelected }
                val hadSelectedBefore = originalNextApproversList.any { it.isSelected }

                if (action.isConfirm) {
                    if (!hasSelectedItem) {
                        updateOnAllItemApproverSelected(false)
                        updateSwitchNextApproversChange(false)
                    }
                    originalNextApproversList.apply {
                        clear()
                        addAll(state.value.nextApproverBottomSheetUiState.nextApproversList)
                    }
                    updateNextApproversList(originalNextApproversList)
                    updateNextApproversListString()
                } else {
                    when {
                        !hasSelectedItem && hadSelectedBefore -> {
                            clearApprovers()
                        }

                        hadSelectedBefore -> {
                            return
                        }

                        else -> {
                            clearApprovers()
                        }
                    }
                }
            }

            is PaymentOrderAction.OnClickItemApprove -> {
                val newList =
                    state.value.nextApproverBottomSheetUiState.nextApproversList.map { approver ->
                        if (approver == action.approverDomains) {
                            approver.copy(isSelected = !approver.isSelected)
                        } else {
                            approver
                        }
                    }
                updateNextApproversList(newList.toMutableList())
                updateOnAllItemApproverSelected(state.value.nextApproverBottomSheetUiState.nextApproversList.all { it.isSelected })
            }

            is PaymentOrderAction.OnClickAllItemApprove -> {
                val newList =
                    state.value.nextApproverBottomSheetUiState.nextApproversList.map { approver ->
                        approver.copy(isSelected = action.isAllItemSelected)
                    }
                updateNextApproversList(newList.toMutableList())
                updateOnAllItemApproverSelected(!state.value.nextApproverBottomSheetUiState.onAllItemApproverSelected)
            }

            is PaymentOrderAction.ShowBottomSheetNextApprovers -> {
                clearAllFocus()
                updateNextApproversList(originalNextApproversList)
                updateCanShowBottomSheetNextApprovers(value = true)
            }

            is PaymentOrderAction.CanShowBottomSheetChooseTypeUpload -> {
                clearAllFocus()
                updateCanShowBottomSheetChooseTypeUpload(action.isShow)
            }

            is PaymentOrderAction.OnClickGotoGallery -> {
                updateCanShowBottomSheetChooseTypeUpload(false)
                viewModelScope.launch {
                    _events.send(PaymentOrderEvent.OnClickGotoGallery)
                }
            }

            is PaymentOrderAction.OnClickGotoUploadFile -> {
                clearAllFocus()
                updateCanShowBottomSheetChooseTypeUpload(false)
                viewModelScope.launch {
                    _events.send(PaymentOrderEvent.OnClickGotoUploadFile)
                }
            }

            is PaymentOrderAction.OnDeleteFileUpload -> {
                clearAllFocus()
                updateImageBase64Object(null)
            }

            is PaymentOrderAction.OnClickContinue -> {
                clearAllFocus()
                val errorObject = validatePaymentOrderFields()
                if (errorObject != null) {
                    handleErrorValidate(errorObject)
                    return
                }
                validatePaymentOrderTransfer()
            }

            is PaymentOrderAction.GetAccountList -> {
                getAccList()
            }

            is PaymentOrderAction.OnSearchContactText -> {
                updateSearchContactText(action.searchContactText)
                val searchText = action.searchContactText.trim()
                val filteredContacts = if (searchText.isBlank()) {
                    originalContactList
                } else {
                    originalContactList.filter { bank ->
                        bank.bankName?.contains(searchText, ignoreCase = true) == true ||
                            bank.account?.contains(searchText, ignoreCase = true) == true ||
                            bank.payeename?.contains(searchText, ignoreCase = true) == true ||
                            bank.payeenickname?.contains(
                                searchText,
                                ignoreCase = true,
                            ) == true ||
                            bank.cardnumber?.contains(searchText, ignoreCase = true) == true
                    }
                }
                updateListContactDomains(filteredContacts.toMutableList())

                val filteredTemps = if (searchText.isBlank()) {
                    originalTempTransactionList
                } else {
                    originalTempTransactionList.filter { bank ->
                        bank.toAccountName?.contains(searchText, ignoreCase = true) == true ||
                            bank.content?.contains(searchText, ignoreCase = true) == true
                    }
                }
                updateListTempDomains(filteredTemps.toMutableList())
            }

            is PaymentOrderAction.OnSelectedTabContactIndex -> {
                updateSelectedTabContactIndex(action.selectedTabIndex)
            }

            is PaymentOrderAction.ClickContactItem -> {
                val contactItem = action.contactItem
                if (0 != state.value.selectedIndex) {
                    onSelectedIndexChange(0)
                }
                updateBankNameHolder(contactItem.bankName ?: "")
                updateBeneficiaryAccountHolder(contactItem.account ?: "")
                updateAccountNameHolder(contactItem.payeename ?: "")
                canShowBottomSheetContactSaved(false)
            }

            is PaymentOrderAction.ClickTempItem -> {
                val tempTransactionItem = action.tempTransactionItem

                if (0 != state.value.selectedIndex) {
                    onSelectedIndexChange(0)
                }
                updateBankNameHolder(tempTransactionItem.toBankName ?: "")
                updateBeneficiaryAccountHolder(tempTransactionItem.toAccountNo ?: "")
                updateAccountNameHolder(tempTransactionItem.toAccountName ?: "")
                updateAmountHolder(tempTransactionItem.amount ?: "")
                updateContentHolder(tempTransactionItem.content ?: "")
                canShowBottomSheetContactSaved(false)
            }

            is PaymentOrderAction.ClickRecentItem -> {
                if (0 != state.value.selectedIndex) {
                    onSelectedIndexChange(0)
                }
                canShowBottomSheetContactSaved(false)
            }

            is PaymentOrderAction.OnSearchRecentText -> {
                updateSearchRecentText(action.searchRecentText)
                val searchText = action.searchRecentText.trim()
                val filteredTemps = if (searchText.isBlank()) {
                    originalTransactionInfoList
                } else {
                    originalTransactionInfoList.filter { bank ->
                        bank.toAccountName?.contains(searchText, ignoreCase = true) == true ||
                            bank.toAccount?.contains(searchText, ignoreCase = true) == true ||
                            bank.bankName?.contains(searchText, ignoreCase = true) == true
                    }
                }
                updateTransactionInfoList(filteredTemps.toMutableList())
            }

            is PaymentOrderAction.OnClickContactBottomSheet -> {
                clearAllFocus()
                if (!action.canShowBottomSheetContactSaved) {
                    canShowBottomSheetContactSaved(false)
                    return
                }
                getContactList()
            }
        }
    }

    sealed class PaymentOrderEvent {
        data object NavigateBack : PaymentOrderEvent()
        data class ShowErrorMess(val errorMess: String) : PaymentOrderEvent()
        data class ShowErrorValidateFieldTransferAccount(val errorObject: TransferValidationResult) :
            PaymentOrderEvent()

        data object OnClickGotoGallery : PaymentOrderEvent()
        data object OnClickGotoUploadFile : PaymentOrderEvent()
        data class ValidatePaymentOrderState(val validatePaymentOrderTransferDomains: ValidatePaymentOrderTransferDomains) :
            PaymentOrderEvent()
    }

    sealed class PaymentOrderAction {
        data object OnBackPressed : PaymentOrderAction()
        data object GetAccountList : PaymentOrderAction()

        data class OnSelectedIndex(val selectedIndex: Int) : PaymentOrderAction()
        data class OnFocusBankNameHolderChange(val isFocusBankNameHolder: Boolean) :
            PaymentOrderAction()

        data class OnBeneficiaryAccountFocusChange(val isFocusBeneficiaryAccountHolder: Boolean) :
            PaymentOrderAction()

        data class OnBankNameHolderChange(val bankNameHolder: String) : PaymentOrderAction()

        data class OnBeneficiaryAccountTextChange(val beneficiaryAccountHolder: String) :
            PaymentOrderAction()

        data object OnClearBeneficiaryAccount : PaymentOrderAction()
        data class OnAccountNameHolder(val accountNameHolder: String) : PaymentOrderAction()

        data class OnFocusAccountNameHolderChange(val isFocusAccountNameHolder: Boolean) :
            PaymentOrderAction()

        data class OnSearchAccountText(val searchAccountText: String) : PaymentOrderAction()

        data class CanShowBottomSheetChooseAccount(
            val isShow: Boolean,
            val isOnlyTypeDDA: Boolean,
        ) : PaymentOrderAction()

        data class OnCurrentAccountDataChange(val accountDefaultDomain: AccountDefaultDomain) :
            PaymentOrderAction()

        data class CanShowBottomSheetChooseBranch(val isShow: Boolean) : PaymentOrderAction()
        data class CanShowBottomSheetTransferTime(val isShow: Boolean) : PaymentOrderAction()

        data class OnSearchTextBranchText(val searchTextBranch: String) : PaymentOrderAction()
        data class OnCurrentBranchDataChange(val branchDomains: BranchDomains) :
            PaymentOrderAction()

        data class OnAmountHolderChange(val amountHolder: String) : PaymentOrderAction()
        data class OnFocusAmountChange(val isFocusedAmount: Boolean) : PaymentOrderAction()

        data class OnContentHolderChange(val contentHolder: String) : PaymentOrderAction()

        data class OnFocusContentChange(val isFocusedContent: Boolean) : PaymentOrderAction()

        data class OnChooseTransferTime(val transferTime: BottomSheetItem) : PaymentOrderAction()

        data class ShowTransferTimeSchedule(val show: Boolean) : PaymentOrderAction()
        data class OnTransferTimeTextChange(val transferHourEntity: HourEntity) :
            PaymentOrderAction()

        data class OnTransferDateTextChange(val transferDate: String) : PaymentOrderAction()
        data class CanShowBottomSheetTransferHour(val isShow: Boolean) : PaymentOrderAction()
        data class CanShowBottomSheetTransferType(val isShow: Boolean) : PaymentOrderAction()
        data class OnChooseTransferType(val transferType: BottomSheetItem) :
            PaymentOrderAction()

        data class OnSwitchNextApproversChange(val switchNextApprovers: Boolean) :
            PaymentOrderAction()

        data class DismissShowBottomSheetNextApprovers(val isConfirm: Boolean) :
            PaymentOrderAction()

        data class OnClickItemApprove(val approverDomains: ApproverDomains) :
            PaymentOrderAction()

        data class OnClickAllItemApprove(val isAllItemSelected: Boolean) :
            PaymentOrderAction()

        data object ShowBottomSheetNextApprovers : PaymentOrderAction()
        data object OnClickGotoGallery : PaymentOrderAction()
        data object OnClickGotoUploadFile : PaymentOrderAction()
        data class CanShowBottomSheetChooseTypeUpload(val isShow: Boolean) : PaymentOrderAction()
        data object OnDeleteFileUpload : PaymentOrderAction()
        data object OnClickContinue : PaymentOrderAction()
        data class OnSearchContactText(val searchContactText: String) : PaymentOrderAction()
        data class OnSelectedTabContactIndex(val selectedTabIndex: Int) : PaymentOrderAction()
        data class ClickContactItem(val contactItem: ContactDomains) : PaymentOrderAction()
        data class ClickTempItem(val tempTransactionItem: TempTransactionDomains) :
            PaymentOrderAction()

        data class OnSearchRecentText(val searchRecentText: String) : PaymentOrderAction()
        data class ClickRecentItem(val transactionInfo: TransactionInfo) : PaymentOrderAction()
        data class OnClickContactBottomSheet(val canShowBottomSheetContactSaved: Boolean) :
            PaymentOrderAction()
    }

    data class PaymentOrderState(
        val listDataBanks: MutableList<DataBankDomain> = mutableListOf(),
        var selectedIndex: Int = 0,
        var bankNameHolder: String = "",
        var beneficiaryAccountHolder: String = "",
        var accountNameHolder: String = "",
        val accountList: List<AccountDefaultDomain>? = emptyList(),
        val amountHolder: String = "",
        val amountHolderReader: String = "",
        val contentHolder: String = "",
        var transferDate: String = "",
        var transferTimeText: String = "",

        val isErrorBeneficiaryAccount: Boolean = false,
        val isErrorBeneficiaryAccountMessage: String = "",
        val branchBottomSheetUiState: BranchBottomSheetUiState = BranchBottomSheetUiState(),
        var currentBranchDefaultDomain: BranchDomains? = null,
        var transferTime: BottomSheetItem? = null,
        var transferTypeFee: BottomSheetItem? = null,

        var currentAccountDefaultDomain: AccountDefaultDomain? = null,
        var currentAccountDefaultDomainDDA: AccountDefaultDomain? = null,
        var chooseAccountBottomSheetUiState: ChooseAccountBottomSheetUiState = ChooseAccountBottomSheetUiState(),
        var canShowBottomSheetChooseAccount: Boolean = false,
        var canShowBottomSheetChooseBranch: Boolean = false,
        var canShowBottomSheetTransferTime: Boolean = false,
        var showTransferTimeSchedule: Boolean = false,
        var radioBottomSheetTransferTimeUiState: RadioBottomSheetUiState = RadioBottomSheetUiState(),
        var hourPickerBottomSheetUiState: HourPickerBottomSheetUiState = HourPickerBottomSheetUiState(),
        var canShowBottomSheetTransferHour: Boolean = false,
        var itemsHour: List<HourEntity> = emptyList(),
        var currentHourEntity: HourEntity? = null,
        var canShowBottomSheetTransferType: Boolean = false,
        var radioBottomSheetTransferTypeFeeUiState: RadioBottomSheetUiState = RadioBottomSheetUiState(),
        val nextApproversListString: String = "",
        var switchNextApprovers: Boolean = false,
        var nextApproverBottomSheetUiState: NextApproverBottomSheetUiState = NextApproverBottomSheetUiState(),
        var canShowBottomSheetNextApprovers: Boolean = false,
        var canShowBottomSheetChooseTypeUpload: Boolean = false,
        var imageBase64Object: ImageBase64Object? = null,
        var uriFile: Uri = Uri.EMPTY,
        var contactSavedBottomSheetUiState: ContactSavedBottomSheetUiState = ContactSavedBottomSheetUiState(),
        val isLoadingListRecent: Boolean = true,
        val transactionInfoList: MutableList<TransactionInfo> = mutableListOf(),
        val searchRecentText: String = "",
        var canShowBottomSheetContactSaved: Boolean = false,

        val focusRequesterBankName: FocusRequester = FocusRequester(),
        val focusRequesterAccountNumber: FocusRequester = FocusRequester(),
        val focusRequesterAccountName: FocusRequester = FocusRequester(),
        val focusRequesterAmount: FocusRequester = FocusRequester(),
        val focusRequesterContent: FocusRequester = FocusRequester(),

        var isFocusBankNameHolder: Boolean = false,
        var isFocusBeneficiaryAccountHolder: Boolean = false,
        var isFocusAccountNameHolder: Boolean = false,
        val isFocusedAmount: Boolean = false,
        val isFocusedContent: Boolean = false,

    )
}
