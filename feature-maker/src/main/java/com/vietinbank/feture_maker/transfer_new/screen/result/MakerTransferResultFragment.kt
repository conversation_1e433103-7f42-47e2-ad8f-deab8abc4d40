package com.vietinbank.feture_maker.transfer_new.screen.result

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.UTF8
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.CONFIRM_SAVE_TEMP_TRANSFER_YES
import com.vietinbank.feture_maker.transfer_new.screen.result.MakerTransferResultViewModel.MakerTransferResultAction
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.net.URLEncoder
import javax.inject.Inject
import kotlin.getValue

@AndroidEntryPoint
class MakerTransferResultFragment : BaseFragment<MakerTransferResultViewModel>() {

    override val viewModel: MakerTransferResultViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var imageLoader: CoilImageLoader

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initData()
        observeEvents()
    }
    private fun initData() {
        arguments?.let { bundle ->
            viewModel.handleArguments(bundle)
        }
    }
    private fun observeEvents() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.events.collect { event ->
                when (event) {
                    MakerTransferResultViewModel.MakerTransferResultEvent.NavigateBack -> {
                        appNavigator.goToHome()
                    }
                    is MakerTransferResultViewModel.MakerTransferResultEvent.ShowErrorMess -> {
                        showNoticeDialog(event.errorMess)
                    }
                    is MakerTransferResultViewModel.MakerTransferResultEvent.MakerNewTransferAccount -> {
                        val bankItemString = Utils.g().provideGson().toJson(event.bankDomain)
                        val encodedBankItem = URLEncoder.encode(bankItemString, UTF8)
                        appNavigator.goToMakerTransferBankChoosed(
                            typeTransfer = TransferConstants.Bundle.KEY_TRANSFER_TYPE_ACCOUNT,
                            itemStringObj = encodedBankItem,
                        )
                    }

                    MakerTransferResultViewModel.MakerTransferResultEvent.MakerNewTransferCard -> {
                        appNavigator.goToMakerTransferCard(
                            typeTransfer = TransferConstants.Bundle.KEY_TRANSFER_TYPE_CARD,
                        )
                    }

                    is MakerTransferResultViewModel.MakerTransferResultEvent.HandleErrorCreateTemplateState -> {
                        showConfirmDialog(
                            message = event.errorMess,
                            positiveButtonText = getString(com.vietinbank.core_ui.R.string.dialog_button_confirm),
                            negativeButtonText = getString(com.vietinbank.core_ui.R.string.dialog_button_back),
                            positiveAction = {
                                viewModel::onAction.invoke(MakerTransferResultAction.OnClickSaveTempBottomSheet(customercode = "", confirm = CONFIRM_SAVE_TEMP_TRANSFER_YES))
                            },
                            negativeAction = {
                                dismissNoticeDialog()
                            },
                        )
                    }
                }
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.state.collectAsState()
        AppTheme {
            MakerTransferResultScreen(
                state = uiState,
                onAction = viewModel::onAction,
                imageLoader = imageLoader,
            )
        }
    }
    override fun onBackPressed(): Boolean {
        appNavigator.goToHome()
        return true
    }
}
