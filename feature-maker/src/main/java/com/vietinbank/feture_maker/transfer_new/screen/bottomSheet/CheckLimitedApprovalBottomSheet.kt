package com.vietinbank.feture_maker.transfer_new.screen.bottomSheet

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyCurrency
import com.vietinbank.core_domain.models.maker.UserApprovalItem
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.CheckLimitedApprovalBottomSheetUiState
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CheckLimitedApprovalBottomSheet(
    isVisible: Boolean,
    onDismiss: () -> Unit,
    state: CheckLimitedApprovalBottomSheetUiState,
) {
    if (isVisible) {
        BaseBottomSheet(
            visible = true,
            containerColor = Color.Transparent,
            onDismissRequest = onDismiss,
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(0.9f),
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth(),
                ) {
                    Column(
                        modifier = Modifier
                            .clip(RoundedCornerShape(AppSizer.Radius.radius32))
                            .background(FDS.Colors.backgroundBgContainer),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center,
                    ) {
                        FoundationText(
                            text = stringResource(R.string.maker_transfer_account_result_limited_approval),
                            style = FDS.Typography.headingH4,
                            color = FDS.Colors.characterPrimary,
                            modifier = Modifier
                                .padding(
                                    vertical = FDS.Sizer.Gap.gap24,
                                ),
                        )

                        HorizontalDivider(
                            color = FDS.Colors.divider,
                            thickness = FoundationDesignSystem.Sizer.Stroke.stroke1,
                        )
                        // Nội dung scroll
                        if (state.userApprovalList?.isNotEmpty() != false) {
                            LazyColumn(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(horizontal = FDS.Sizer.Gap.gap24),
                            ) {
                                state.userApprovalList?.drop(1)?.let {
                                    itemsIndexed(it) { index, item ->
                                        ApprovalItem(
                                            item = item,
                                            isFirst = index == 0,
                                            isLast = index == it.lastIndex,
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun ApprovalItem(
    item: UserApprovalItem,
    isFirst: Boolean,
    isLast: Boolean,
) {
    Column(
        modifier = Modifier
            .fillMaxWidth(),
    ) {
        if (isFirst) {
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
        } else if (isLast) {
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
        } else {
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
        }
        Row(
            verticalAlignment = Alignment.CenterVertically,
        ) {
            FoundationText(
                text = item.fullname.orEmpty(),
                color = FDS.Colors.characterPrimary,
                style = FDS.Typography.bodyB2Emphasized,
            )
            Spacer(modifier = Modifier.weight(1f))

            Box(
                modifier = Modifier
                    .padding(start = FDS.Sizer.Gap.gap16)
                    .border(
                        width = FDS.Sizer.Stroke.stroke1,
                        color = FDS.Colors.backgroundBgHighlight,
                        shape = RoundedCornerShape(FDS.Sizer.Gap.gap32),
                    )
                    .background(
                        color = FDS.Colors.blue50,
                        shape = RoundedCornerShape(FDS.Sizer.Gap.gap32),
                    )
                    .padding(horizontal = FDS.Sizer.Gap.gap8, vertical = FDS.Sizer.Gap.gap4),
            ) {
                FoundationText(
                    text = stringResource(
                        R.string.maker_transfer_account_result_approval_lever,
                        item.approverlevel.orEmpty(),
                    ),
                    color = FDS.Colors.characterHighlighted,
                    style = FDS.Typography.captionCaptionMBold,
                )
            }
        }

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))

        FoundationText(
            text = item.username.orEmpty(),
            style = FDS.Typography.captionCaptionL,
            color = FDS.Colors.characterSecondary,
        )

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            FoundationText(
                text = stringResource(R.string.maker_transfer_account_result_transLimit),
                style = FDS.Typography.captionCaptionL,
                color = FDS.Colors.characterSecondary,
            )

            Spacer(modifier = Modifier.weight(1f))

            FoundationText(
                text = Utils.g().getDotMoneyHasCcy(item.transLimit.orEmpty(), MoneyCurrency.VND.name),
                style = FDS.Typography.captionCaptionLBold,
                color = FDS.Colors.characterPrimary,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )
        }
        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            FoundationText(
                text = stringResource(R.string.maker_transfer_account_result_dailyLimit),
                style = FDS.Typography.captionCaptionL,
                color = FDS.Colors.characterSecondary,
            )

            Spacer(modifier = Modifier.weight(1f))

            FoundationText(
                text = Utils.g().getDotMoneyHasCcy(item.dailyLimit.orEmpty(), MoneyCurrency.VND.name),
                style = FDS.Typography.captionCaptionLBold,
                color = FDS.Colors.characterPrimary,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )
        }
        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
        HorizontalDivider(
            color = FDS.Colors.divider,
            thickness = FDS.Sizer.Stroke.stroke1,
        )
    }
}