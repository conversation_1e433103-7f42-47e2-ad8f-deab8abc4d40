package com.vietinbank.feture_maker.transfer_new.screen.constant

import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.Fragment

class FileOpenerHelper(private val fragment: Fragment) {
    fun openFile(context: Context, uri: Uri, mimeType: String? = null) {
        try {
            val intent = Intent(Intent.ACTION_VIEW).apply {
                setDataAndType(uri, mimeType ?: context.contentResolver.getType(uri))
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }

            context.startActivity(intent)
        } catch (e: SecurityException) {
            // Nếu bị SecurityException, cho user chọn lại file để mở
            Toast.makeText(context, "Không thể truy cập file. Vui lòng chọn file để mở", Toast.LENGTH_SHORT).show()
            openFileWithDocumentPicker(mimeType)
        } catch (e: ActivityNotFoundException) {
            Toast.makeText(context, "Không tìm thấy ứng dụng để mở file này", Toast.LENGTH_SHORT).show()
        }
    }

    // Hàm mở document picker khi gặp lỗi permission
    private fun openFileWithDocumentPicker(mimeType: String? = null) {
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
            addCategory(Intent.CATEGORY_OPENABLE)
            type = mimeType ?: "*/*"

            // Nếu là file cụ thể, set mime types tương ứng
            if (mimeType == null) {
                putExtra(
                    Intent.EXTRA_MIME_TYPES,
                    arrayOf(
                        "application/pdf", // PDF
                        "application/zip", // ZIP
                        "application/vnd.ms-excel", // XLS
                        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // XLSX
                        "text/csv", // CSV
                        "application/msword", // DOC
                        "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // DOCX
                        "image/*", // Images
                    ),
                )
            }
        }

        documentOpenerLauncher.launch(intent)
    }

    // THÊM launcher cho việc mở file khi bị lỗi permission
    private val documentOpenerLauncher = fragment.registerForActivityResult(
        ActivityResultContracts.StartActivityForResult(),
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val selectedUri = result.data?.data
            selectedUri?.let { uri ->
                // Mở file được chọn
                openSelectedFile(uri)
            }
        }
    }
    private fun openSelectedFile(uri: Uri) {
        val mimeType = fragment.requireContext().contentResolver.getType(uri)
        val intent = Intent(Intent.ACTION_VIEW).apply {
            setDataAndType(uri, mimeType)
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }

        try {
            fragment.startActivity(intent)
        } catch (e: ActivityNotFoundException) {
            Toast.makeText(fragment.requireContext(), "Không tìm thấy ứng dụng để mở file này", Toast.LENGTH_SHORT).show()
        }
    }
}