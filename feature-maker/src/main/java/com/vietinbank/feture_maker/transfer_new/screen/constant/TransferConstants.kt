package com.vietinbank.feture_maker.transfer_new.screen.constant

import com.vietinbank.core_common.constants.Tags

object TransferConstants {
    object Bundle {
        const val KEY_TRANSFER_TYPE = "KEY_TRANSFER_TYPE"
        const val KEY_TRANSFER_TYPE_ACCOUNT = "KEY_TRANSFER_TYPE_ACCOUNT"
        const val KEY_TRANSFER_TYPE_CARD = "KEY_TRANSFER_TYPE_CARD"
        const val KEY_TRANSFER_TYPE_SAVED_BANK = "KEY_TRANSFER_TYPE_SAVED_BANK"
        const val KEY_TRANSFER_TYPE_SAVED_TEMP = "KEY_TRANSFER_TYPE_SAVED_TEMP"
        const val KEY_TRANSFER_TYPE_SAVED_RECENT = "KEY_TRANSFER_TYPE_SAVED_RECENT"
        const val KEY_BANK_ITEM = "KEY_BANK_ITEM"
        const val KEY_CONTACT_ITEM = "KEY_CONTACT_ITEM"
        const val UTF8 = "UTF-8"
        const val CONFIRM_TRANSFER_ITEM = "CONFIRM_TRANSFER_ITEM"
        const val RESULT_TRANSFER_ITEM = "RESULT_TRANSFER_ITEM"
    }
    object ValidationConstants {
        const val BENEFICIARY_REGEX = "^[A-Za-z0-9]+$"
        const val AMOUNT_REGEX = "^[0-9]+$"
    }
    object Transfer {
        const val ID_TRANSFER_TIME_NOW = "1"
        const val ID_TRANSFER_TIME_SCHEDULE = "2"
        const val ID_TRANSFER_TYPE_OUR = "1"
        const val ID_TRANSFER_TYPE_BEN = "2"
        const val ID_SERVICE_TYPE_TRANSFER = "CT"
        const val TAB_NEW = 0
        const val TAB_SAVED = 1
        const val TAB_RECENT = 2
        const val TAB_RESULT_REQUEST = 0
        const val TAB_RESULT_HISTORY = 1
        const val TRANSFER_LIST_LATEST_PARAMS_PAGE_SIZE = "15"
        const val CONFIRM_SPLIT_TRANSFER_YES = "Y"
        const val CONFIRM_SAVE_TEMP_TRANSFER_NO = "N"
        const val CONFIRM_SAVE_CONTACT_TRANSFER_NO = "N"
        const val CONFIRM_SAVE_TEMP_TRANSFER_YES = "Y"

        fun getFeeMethodName(id: String): String {
            return when (id) {
                "1" -> Tags.TransferType.dataSetFillDataOUR.name
                "0" -> Tags.TransferType.dataSetFillDataBEN.name
                else -> ""
            }
        }

        fun getServiceId(type: String): String {
            return when (type) {
                Tags.TransferType.TYPE_IN -> Tags.TransferType.TYPE_IN
                Tags.TransferType.TYPE_OUT -> Tags.TransferType.TYPE_OUT
                Tags.TransferType.TYPE_NAPAS -> Tags.TransferType.TYPE_NAPAS_ACCOUNT
                else -> ""
            }
        }
    }
}
