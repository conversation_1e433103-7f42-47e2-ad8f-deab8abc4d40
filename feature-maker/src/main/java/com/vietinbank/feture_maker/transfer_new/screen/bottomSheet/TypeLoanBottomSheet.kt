package com.vietinbank.feture_maker.transfer_new.screen.bottomSheet

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TypeLoanBottomSheet(
    isVisible: Boolean,
    onDismiss: () -> Unit,
) {
    val scrollState = rememberScrollState()
    if (isVisible) {
        BaseBottomSheet(
            visible = true,
            containerColor = Color.Transparent,
            onDismissRequest = onDismiss,
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(AppSizer.Radius.radius32)),
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .verticalScroll(scrollState),
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(
                                color = FDS.Colors.backgroundBgContainer,
                                shape = RoundedCornerShape(
                                    FDS.Sizer.Gap.gap32,

                                ),
                            )
                            .padding(vertical = FDS.Sizer.Gap.gap24),
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        FoundationText(
                            text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_split_transfer_type_loan),
                            style = FDS.Typography.headingH3,
                            color = FDS.Colors.characterHighlighted,
                            modifier = Modifier,
                        )

                        HorizontalDivider(
                            color = FDS.Colors.divider,
                            thickness = FDS.Sizer.Stroke.stroke1,
                        )
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
                        FoundationText(
                            text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_split_transfer_type_loan_content),
                            color = FDS.Colors.characterPrimary,
                            style = FDS.Typography.bodyB1,
                            modifier = Modifier.padding(horizontal = FDS.Sizer.Gap.gap24),

                        )
                    }
                }
            }
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = FDS.Sizer.Gap.gap16),
            ) {
                FoundationButton(
                    isLightButton = true,
                    text = stringResource(com.vietinbank.core_ui.R.string.dialog_button_understood),
                    onClick = {
                        onDismiss.invoke()
                    },
                    enabled = true,
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .padding(
                            horizontal = FDS.Sizer.Gap.gap4,
                        ),
                )
            }
        }
    }
}