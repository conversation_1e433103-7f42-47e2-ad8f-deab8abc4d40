package com.vietinbank.feture_maker.transfer_new.screen.confirm

import android.text.TextUtils
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_common.models.TransferResult
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationTransfer
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_maker.R
import com.vietinbank.feture_maker.transfer_new.screen.confirm.MakerTransferConfirmViewModel.MakerTransferConfirmAction
import com.vietinbank.feture_maker.transfer_new.screen.confirm.MakerTransferConfirmViewModel.MakerTransferConfirmState
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE_ACCOUNT
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun MakerTransferConfirmScreen(
    state: MakerTransferConfirmState,
    onAction: ((MakerTransferConfirmAction) -> Unit),
    imageLoader: CoilImageLoader,
) {
    val scrollState = rememberScrollState()
    Box(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding()
//            .imePadding()
            .padding(top = FDS.Sizer.Gap.gap8),
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize(),
        ) {
            FoundationAppBar(
                isLightIcon = false,
                isSingleLineAppBar = true,
                title = "",
                onNavigationClick = { onAction.invoke(MakerTransferConfirmAction.OnBackPressed) },
            )
            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(vertical = FDS.Sizer.Gap.gap16)
                    .verticalScroll(scrollState),
            ) {
                FoundationText(
                    modifier = Modifier
                        .padding(horizontal = FDS.Sizer.Gap.gap24),
                    text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_account_confirm_request),
                    style = FDS.Typography.bodyB2Emphasized,
                    color = FDS.Colors.blue300,
                )
                FoundationText(
                    modifier = Modifier.padding(
                        top = FDS.Sizer.Gap.gap8,
                        start = FDS.Sizer.Gap.gap24,
                        end = FDS.Sizer.Gap.gap24,
                    ),
                    text = state.amountString,
                    style = FDS.Typography.headingH2,
                    color = FDS.Colors.white,
                )
                FoundationText(
                    modifier = Modifier.padding(
                        top = FDS.Sizer.Gap.gap8,
                        start = FDS.Sizer.Gap.gap24,
                        end = FDS.Sizer.Gap.gap24,
                    ),
                    text = state.amountReader,
                    style = FDS.Typography.bodyB1,
                    color = FDS.Colors.gray200,
                )
                if (KEY_TRANSFER_TYPE_ACCOUNT == state.isTransferTypeKey) {
                    FoundationTransfer(
                        imageLoader = imageLoader,
                        modifier = Modifier.padding(
                            top = FDS.Sizer.Gap.gap16,
                            start = FDS.Sizer.Gap.gap8,
                            end = FDS.Sizer.Gap.gap8,
                        ),
                        accountFrom = TransferResult(
                            bankIconResource = R.drawable.ic_vietinbank,
                            accountName = state.validateNapasAccountTransferDomain?.username ?: "",
                            accountNo = state.validateNapasAccountTransferDomain?.fromAcctNo ?: "",
                            fromAccBalance = state.validateNapasAccountTransferDomain?.fromAccBalance
                                ?: "",
                        ),
                        accountTo = TransferResult(
                            bankIconURL = state.validateNapasAccountTransferDomain?.toBankIconURL
                                ?: "",
                            bankName = state.validateNapasAccountTransferDomain?.toBankName ?: "",
                            accountName = state.validateNapasAccountTransferDomain?.toAcctName
                                ?: "",
                            accountNo = state.validateNapasAccountTransferDomain?.toAcctNo ?: "",
                        ),
                        lstContent = buildList<Pair<String?, String?>> {
                            add(
                                Pair(
                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_content),
                                    state.validateNapasAccountTransferDomain?.remark ?: "",
                                ),
                            )

                            add(
                                Pair(
                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_fee),
                                    Utils.g().getDotMoneyHasCcy(
                                        state.validateNapasAccountTransferDomain?.feeVat ?: "",
                                        state.validateNapasAccountTransferDomain?.currency ?: "",
                                    ),
                                ),
                            )

                            if (state.isTypeSplitTransfer) {
                                add(
                                    Pair(
                                        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_fee_split),
                                        state.totalVatSplitTransfer,
                                    ),
                                )
                            }

                            add(
                                Pair(
                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_fee),
                                    TransferConstants.Transfer.getFeeMethodName(
                                        state.validateNapasAccountTransferDomain?.feePayMethod
                                            ?: "",
                                    ),
                                ),
                            )

                            if (state.isTypeSplitTransfer) {
                                addAll(
                                    listOf(
                                        Pair(
                                            stringResource(
                                                com.vietinbank.core_ui.R.string.maker_transfer_split_number_content_transaction,
                                                state.validateNapasAccountTransferDomain?.subTransactionCount
                                                    ?: "",
                                            ),
                                            Utils.g().getDotMoneyHasCcy(
                                                state.validateNapasAccountTransferDomain?.subSplitTransAmount
                                                    ?: "",
                                                state.validateNapasAccountTransferDomain?.currency
                                                    ?: "",
                                            ),
                                        ),
                                        Pair(
                                            stringResource(
                                                com.vietinbank.core_ui.R.string.maker_transfer_split_number_content_transaction,
                                                state.validateNapasAccountTransferDomain?.subTransactionsRemainderCount
                                                    ?: "",
                                            ),
                                            Utils.g().getDotMoneyHasCcy(
                                                state.validateNapasAccountTransferDomain?.subTransactionsRemainderAmount
                                                    ?: "",
                                                state.validateNapasAccountTransferDomain?.currency
                                                    ?: "",
                                            ),
                                        ),
                                    ),
                                )
                            }

                            if (TextUtils.isEmpty(state.validateNapasAccountTransferDomain?.processDate)) {
                                add(
                                    Pair(
                                        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_transfer_date_time_transfer),
                                        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_now),
                                    ),
                                )
                            } else {
                                addAll(
                                    listOf(
                                        Pair(
                                            stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_transfer_date_time_transfer),
                                            stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_schedule),
                                        ),
                                        Pair(
                                            stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_transfer_date_schedule),
                                            state.validateNapasAccountTransferDomain?.processDate
                                                ?: "",
                                        ),
                                    ),
                                )
                            }

                            if (state.validateNapasAccountTransferDomain?.nextApprovers?.isNotEmpty() == true) {
                                onAction.invoke(MakerTransferConfirmAction.OnGetNextApproversListString)
                                add(
                                    Pair(
                                        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_account_confirm_approver),
                                        state.listApproverComfirmString,
                                    ),
                                )
                            }
                        },
                    )
                } else {
                    FoundationTransfer(
                        imageLoader = imageLoader,
                        modifier = Modifier.padding(
                            top = FDS.Sizer.Gap.gap16,
                            start = FDS.Sizer.Gap.gap8,
                            end = FDS.Sizer.Gap.gap8,
                        ),
                        accountFrom = TransferResult(
                            bankIconResource = R.drawable.ic_vietinbank,
                            accountName = state.validateNapasCardTransferDomains?.username ?: "",
                            accountNo = state.validateNapasCardTransferDomains?.fromAcctNo ?: "",
                            fromAccBalance = state.validateNapasCardTransferDomains?.fromAccBalance
                                ?: "",
                        ),
                        accountTo = TransferResult(
                            bankIconURL = state.validateNapasCardTransferDomains?.toBankIconURL
                                ?: "",
                            bankName = state.validateNapasCardTransferDomains?.toBankName ?: "",
                            accountName = state.validateNapasCardTransferDomains?.toCardName
                                ?: "",
                            accountNo = state.validateNapasCardTransferDomains?.toCardNo ?: "",
                        ),
                        lstContent = buildList<Pair<String, String>> {
                            add(
                                Pair(
                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_content),
                                    state.validateNapasCardTransferDomains?.remark ?: "",
                                ),
                            )

                            add(
                                Pair(
                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_fee),
                                    Utils.g().getDotMoneyHasCcy(
                                        state.validateNapasCardTransferDomains?.feeVat ?: "",
                                        state.validateNapasCardTransferDomains?.currency ?: "",
                                    ),
                                ),
                            )

                            add(
                                Pair(
                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_fee),
                                    TransferConstants.Transfer.getFeeMethodName(
                                        state.validateNapasCardTransferDomains?.feePayMethod ?: "",
                                    ),
                                ),
                            )

                            if (TextUtils.isEmpty(state.validateNapasCardTransferDomains?.processDate)) {
                                add(
                                    Pair(
                                        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_transfer_date_time_transfer),
                                        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_now),
                                    ),
                                )
                            } else {
                                addAll(
                                    listOf(
                                        Pair(
                                            stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_transfer_date_time_transfer),
                                            stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_schedule),
                                        ),
                                        Pair(
                                            stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_transfer_date_schedule),
                                            state.validateNapasCardTransferDomains?.processDate
                                                ?: "",
                                        ),
                                    ),
                                )
                            }

                            if (state.validateNapasCardTransferDomains?.nextApprovers?.isNotEmpty() == true) {
                                onAction.invoke(MakerTransferConfirmAction.OnGetNextApproversListString)
                                add(
                                    Pair(
                                        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_account_confirm_approver),
                                        state.listApproverComfirmString,
                                    ),
                                )
                            }
                        },
                    )
                }
            }
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        FDS.Sizer.Gap.gap8,
                    ),
            ) {
                FoundationButton(
                    isLightButton = true,
                    text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_account_confirm_approver_btn),
                    onClick = {
                        onAction.invoke(MakerTransferConfirmAction.OnConfirmPressed)
                    },
                    enabled = true,
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .padding(
                            horizontal = FDS.Sizer.Gap.gap4,
                        ),
                )
            }
        }
    }
}