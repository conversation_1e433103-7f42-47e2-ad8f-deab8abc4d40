package com.vietinbank.feture_maker.transfer_new.screen.contact_template_recent

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.maker.ContactDomains
import com.vietinbank.core_domain.models.maker.TempTransactionDomains
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.components.foundation.textfield.FoundationFieldType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_maker.R
import com.vietinbank.feture_maker.transfer_new.screen.empty_list.EmptyListScreen
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun ContactSavedScreen(
    imageLoader: CoilImageLoader,
    modifier: Modifier,
    searchContactText: String,
    onSearchContactText: (searchContactText: String) -> Unit,
    listContactDomains: MutableList<ContactDomains>,
    listTempDomains: MutableList<TempTransactionDomains>,
    selectedTabIndex: Int,
    onSelectedTabIndex: (selectedTabIndex: Int) -> Unit,
    onClickItemContact: (selectedTabIndex: ContactDomains) -> Unit,
    onClickItemTemp: (selectedTabIndex: TempTransactionDomains) -> Unit,
    isLoadingListContact: Boolean,
    isLoadingListTemp: Boolean,
    onClickEditItemContact: (selectedTabIndex: ContactDomains) -> Unit,
) {
    val tabs = listOf(
        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_tab_saved_account),
        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_tab_saved_card),
        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_tab_saved_template),
    )

    Box(
        modifier = modifier,
    ) {
        Column() {
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
            FoundationFieldType(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = FDS.Sizer.Gap.gap24)
                    .clip(RoundedCornerShape(AppSizer.Radius.radius32))
                    .background(FDS.Colors.gray50),
                value = searchContactText,
                onValueChange = { onSearchContactText.invoke(it) },
                placeholder = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_find_contact_sample_template),
                leadingIcon = {
                    Icon(
                        painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_search),
                        contentDescription = "Search",
                        tint = Color.Unspecified,
                    )
                },
            )
            Spacer(Modifier.height(FDS.Sizer.Gap.gap8))

            TabRow(
                modifier = Modifier.padding(horizontal = FDS.Sizer.Gap.gap24),
                selectedTabIndex = selectedTabIndex,
                contentColor = FDS.Colors.characterHighlightedLighter,
                indicator = { tabPositions ->
                    TabRowDefaults.SecondaryIndicator(
                        Modifier.tabIndicatorOffset(tabPositions[selectedTabIndex]),
                        color = FDS.Colors.characterHighlightedLighter,
                    )
                },
                divider = {
                    HorizontalDivider(
                        color = FDS.Colors.divider,
                        thickness = FDS.Sizer.Stroke.stroke1,
                    )
                },
            ) {
                tabs.forEachIndexed { index, title ->
                    Tab(
                        selected = selectedTabIndex == index,
                        onClick = { onSelectedTabIndex.invoke(index) },
                        text = {
                            FoundationText(
                                modifier = Modifier.fillMaxWidth(),
                                textAlign = TextAlign.Center,
                                text = title,
                                style = FDS.Typography.interactionLink,
                                color = if (selectedTabIndex == index) FDS.Colors.characterHighlighted else FDS.Colors.gray500,
                            )
                        },
                    )
                }
            }

            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))

            // Nội dung thay đổi theo tab
            when (selectedTabIndex) {
                0 -> {
                    if (isLoadingListContact == true) {
                        return
                    }
                    SavedTab(
                        imageLoader = imageLoader,
                        listContactDomains = listContactDomains.filter {
                            Tags.TransferType.TYPE_IN == it.trantype ||
                                (Tags.TransferType.TYPE_NAPAS_ACCOUNT == it.trantype && it.bank?.isNotEmpty() == true) ||
                                (Tags.TransferType.TYPE_OUT == it.trantype && it.bank?.isNotEmpty() == true)
                        }
                            .toMutableList(),
                        onClickItemContact = onClickItemContact,
                        onClickEditItemContact = onClickEditItemContact,
                    )
                }

                1 -> {
                    if (isLoadingListContact == true) {
                        return
                    }
                    SavedTab(
                        imageLoader = imageLoader,
                        listContactDomains = listContactDomains.filter { Tags.TransferType.TYPE_NAPAS_CARD == it.trantype && it.bank?.isNotEmpty() == true }
                            .toMutableList(),
                        onClickItemContact = onClickItemContact,
                        onClickEditItemContact = onClickEditItemContact,
                    )
                }

                2 -> {
                    if (isLoadingListTemp == true) {
                        return
                    }
                    TempTab(
                        imageLoader = imageLoader,
                        listTempTransactionDomains = listTempDomains,
                        onClickItemTemp = onClickItemTemp,
                    )
                }
            }
        }
    }
}

@Composable
fun SavedTab(
    imageLoader: CoilImageLoader,
    listContactDomains: MutableList<ContactDomains>,
    onClickItemContact: (ContactDomains) -> Unit,
    onClickEditItemContact: (selectedTabIndex: ContactDomains) -> Unit,
) {
    if (listContactDomains.isNotEmpty()) {
        LazyColumn(
            modifier = Modifier.padding(horizontal = FDS.Sizer.Gap.gap24),
        ) {
            items(listContactDomains) { item ->
                SavedItem(
                    imageLoader = imageLoader,
                    savedItem = item,
                    onClick = { onClickItemContact.invoke(item) },
                    onClickItemEdit = { onClickEditItemContact.invoke(item) },
                )
            }
        }
    } else {
        EmptyListScreen(
            iconResourceID = R.mipmap.ic_feature_maker_transfer_contact_temp_list_null,
            title = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_tab_saved_contact_list_null),
            content = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_tab_saved_contact_list_null_content),
        )
    }
}

@Composable
fun TempTab(
    imageLoader: CoilImageLoader,
    listTempTransactionDomains: MutableList<TempTransactionDomains>,
    onClickItemTemp: (TempTransactionDomains) -> Unit,
) {
    if (listTempTransactionDomains.isNotEmpty()) {
        LazyColumn(
            modifier = Modifier.padding(horizontal = FDS.Sizer.Gap.gap24),
        ) {
            items(listTempTransactionDomains) { item ->
                TempItem(
                    imageLoader = imageLoader,
                    savedItem = item,
                    onClick = { onClickItemTemp.invoke(item) },
                )
            }
        }
    } else {
        EmptyListScreen(
            iconResourceID = R.mipmap.ic_feature_maker_transfer_contact_temp_list_null,
            title = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_tab_saved_contact_list_null),
            content = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_tab_saved_contact_list_null_content),
        )
    }
}

@Composable
fun TempItem(imageLoader: CoilImageLoader, savedItem: TempTransactionDomains, onClick: () -> Unit) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .safeClickable { onClick.invoke() }.padding(bottom = FDS.Sizer.Gap.gap16),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Box(
            modifier = Modifier
                .size(FDS.Sizer.Icon.icon40)
                .clip(CircleShape)
                .background(FDS.Colors.gray50),
            contentAlignment = Alignment.Center,
        ) {
            imageLoader.LoadUrl(
                url = savedItem.iconUrl ?: "",
                isCache = true,
                placeholderRes = com.vietinbank.core_ui.R.drawable.ic_search,
                errorRes = com.vietinbank.core_ui.R.drawable.ic_search,
                modifier = Modifier.size(FDS.Sizer.Icon.icon24),
            )
        }
        Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap12))
        Column(
            modifier = Modifier
                .weight(1f),
        ) {
            FoundationText(
                text = savedItem.toAccountName ?: "",
                style = FDS.Typography.bodyB2,
                color = FDS.Colors.characterPrimary,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
            FoundationText(
                text = savedItem.content ?: "",
                style = FDS.Typography.captionCaptionL,
                color = FDS.Colors.characterSecondary,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
            FoundationText(
                text = Utils.g()
                    .getDotMoneyHasCcy(savedItem.amount ?: "", savedItem.currency ?: ""),
                style = FDS.Typography.captionCaptionL,
                color = FDS.Colors.characterSecondary,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )
        }
        Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap12))
        Icon(
            painter = painterResource(com.vietinbank.feature_maker.R.drawable.ic_feature_maker_edit),
            contentDescription = "Edit",
            tint = Color.Unspecified,
            modifier = Modifier.size(FDS.Sizer.Gap.gap16),
        )
    }
}

@Composable
fun SavedItem(
    imageLoader: CoilImageLoader,
    savedItem: ContactDomains,
    onClick: () -> Unit,
    onClickItemEdit: () -> Unit,
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(bottom = FDS.Sizer.Gap.gap16)
            .safeClickable { onClick.invoke() },
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Box(
            modifier = Modifier
                .size(FDS.Sizer.Icon.icon40)
                .clip(CircleShape)
                .background(FDS.Colors.gray50),
            contentAlignment = Alignment.Center,
        ) {
            if (Tags.TransferType.TYPE_IN == savedItem.trantype) {
                Icon(
                    painter = painterResource(com.vietinbank.feature_maker.R.drawable.ic_vietinbank),
                    contentDescription = "ic_vietinbank",
                    tint = Color.Unspecified,
                    modifier = Modifier.size(FDS.Sizer.Gap.gap24),
                )
            } else {
                imageLoader.LoadUrl(
                    url = savedItem.iconUrl ?: "",
                    isCache = true,
                    placeholderRes = com.vietinbank.core_ui.R.drawable.ic_search,
                    errorRes = com.vietinbank.core_ui.R.drawable.ic_search,
                    modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                )
            }
        }
        Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap12))
        Column(
            modifier = Modifier
                .weight(1f),
        ) {
            FoundationText(
                text = savedItem.payeename ?: "",
                style = FDS.Typography.bodyB2,
                color = FDS.Colors.characterPrimary,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )
            if (Tags.TransferType.TYPE_IN == savedItem.trantype || Tags.TransferType.TYPE_NAPAS_ACCOUNT == savedItem.trantype) {
                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
                FoundationText(
                    text = savedItem.account ?: "",
                    style = FDS.Typography.captionCaptionL,
                    color = FDS.Colors.characterSecondary,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )
                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
            } else {
                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
                FoundationText(
                    text = savedItem.cardnumber ?: "",
                    style = FDS.Typography.captionCaptionL,
                    color = FDS.Colors.characterSecondary,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )
            }
            if (savedItem.bankName?.isNotEmpty() == true) {
                FoundationText(
                    text = savedItem.bankName ?: "",
                    style = FDS.Typography.captionCaptionL,
                    color = FDS.Colors.characterSecondary,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )
            }
        }
        Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap12))
        Icon(
            painter = painterResource(com.vietinbank.feature_maker.R.drawable.ic_feature_maker_edit),
            contentDescription = "Edit",
            tint = Color.Unspecified,
            modifier = Modifier.size(FDS.Sizer.Gap.gap16).safeClickable {
                onClickItemEdit.invoke()
            },
        )
    }
}
