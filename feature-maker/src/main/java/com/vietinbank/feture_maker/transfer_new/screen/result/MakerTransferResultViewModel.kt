package com.vietinbank.feture_maker.transfer_new.screen.result

import android.os.Bundle
import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.R
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyCurrency
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.maker.ContactCreateParams
import com.vietinbank.core_domain.models.maker.CreateTemplateParams
import com.vietinbank.core_domain.models.maker.CreateTransferDomain
import com.vietinbank.core_domain.models.maker.DataBankDomain
import com.vietinbank.core_domain.models.maker.TempTransactionParams
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountTransferDomain
import com.vietinbank.core_domain.models.maker.ValidateNapasCardTransferDomains
import com.vietinbank.core_domain.repository.cache.ITransferCacheManager
import com.vietinbank.core_domain.usecase.transfer.TransferUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.CONFIRM_TRANSFER_ITEM
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE_ACCOUNT
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE_CARD
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.RESULT_TRANSFER_ITEM
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.UTF8
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.CONFIRM_SAVE_CONTACT_TRANSFER_NO
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.CONFIRM_SAVE_TEMP_TRANSFER_NO
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.CONFIRM_SAVE_TEMP_TRANSFER_YES
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.getServiceId
import com.vietinbank.feture_maker.transfer_new.screen.result.MakerTransferResultViewModel.MakerTransferResultEvent.*
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.net.URLDecoder
import javax.inject.Inject

@HiltViewModel
class MakerTransferResultViewModel @Inject constructor(
    val transferCacheManager: ITransferCacheManager,
    private val moneyHelper: MoneyHelper,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
    private val transferUseCase: TransferUseCase,
) : BaseViewModel() {
    private val _state = MutableStateFlow(MakerTransferResultState())
    val state: StateFlow<MakerTransferResultState> = _state.asStateFlow()

    private val _events = Channel<MakerTransferResultEvent>(Channel.BUFFERED)
    val events = _events.receiveAsFlow()

    fun updateValidateNapasAccountTransferDomain(validateNapasAccountTransferDomain: ValidateNapasAccountTransferDomain?) {
        validateNapasAccountTransferDomain.let { item ->
            _state.update { currentState ->
                currentState.copy(
                    validateNapasAccountTransferDomain = item,
                )
            }
            updateAmountString(amount = item?.amount ?: "", currency = item?.currency ?: "")
            updateMoneyReader(amount = item?.amount ?: "", currency = item?.currency ?: "")
            updateIsTypeSplitTransfer(Tags.TransferType.TYPE_SPLIT_TRANSFER_YES == item?.isSplit)
        }
    }

    fun updateValidateNapasCardTransferDomains(validateNapasCardTransferDomains: ValidateNapasCardTransferDomains?) {
        validateNapasCardTransferDomains.let { item ->
            _state.update { currentState ->
                currentState.copy(
                    validateNapasCardTransferDomains = item,
                )
            }
            updateAmountString(amount = item?.amount ?: "", currency = item?.currency ?: "")
            updateMoneyReader(amount = item?.amount ?: "", currency = item?.currency ?: "")
        }
    }

    fun updateCreateTransferDomain(createTransferDomain: CreateTransferDomain) {
        createTransferDomain.let { item ->
            _state.update { currentState ->
                currentState.copy(
                    createTransferDomain = item,
                )
            }
        }
    }

    private fun updateIsTransferTypeKey(value: String) {
        _state.update { currentState ->
            currentState.copy(
                isTransferTypeKey = value,
            )
        }
    }

    private fun updateAmountString(amount: String = "", currency: String = "") {
        val newValue = Utils.g().getDotMoneyHasCcy(amount, currency)
        _state.update { currentState ->
            currentState.copy(
                amountString = "${getTitleForTransferType()}\n$newValue",
            )
        }
    }

    private fun getTitleForTransferType(): String {
        val resId = if (KEY_TRANSFER_TYPE_ACCOUNT == state.value.isTransferTypeKey) {
            when (state.value.validateNapasAccountTransferDomain?.bankDomain?.type) {
                Tags.TransferType.TYPE_IN -> com.vietinbank.core_ui.R.string.maker_transfer_dashboard_title_in
                Tags.TransferType.TYPE_OUT -> com.vietinbank.core_ui.R.string.maker_transfer_dashboard_normal
                Tags.TransferType.TYPE_NAPAS -> com.vietinbank.core_ui.R.string.maker_transfer_dashboard_title_np247
                Tags.TransferType.TYPE_NORMAL -> com.vietinbank.core_ui.R.string.maker_transfer_dashboard_title_normal
                else -> com.vietinbank.core_ui.R.string.maker_transfer_dashboard_title_normal
            }
        } else {
            com.vietinbank.core_ui.R.string.maker_transfer_dashboard_card
        }
        return resourceProvider.getString(resId)
    }

    private fun updateIsTypeSplitTransfer(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                isTypeSplitTransfer = value,
            )
        }
        if (value) {
            val totalVAT = (
                state.value.validateNapasAccountTransferDomain?.feeSplitNonVat
                    ?: ""
                ).toBigDecimal()
                .add(
                    (
                        state.value.validateNapasAccountTransferDomain?.feeSplitVat
                            ?: ""
                        ).toBigDecimal(),
                )
            val totalVatString = Utils.g()
                .getDotMoneyHasCcy(
                    totalVAT.toString(),
                    state.value.validateNapasAccountTransferDomain?.currency ?: "",
                )
            updateTotalVatSplitTransfer(totalVatString)
        }
    }

    private fun updateTotalVatSplitTransfer(value: String) {
        _state.update { currentState ->
            currentState.copy(
                totalVatSplitTransfer = value,
            )
        }
    }

    private fun updateSelectedTabIndex(value: Int) {
        _state.update { currentState ->
            currentState.copy(
                selectedTabIndex = value,
            )
        }
    }

    private fun canShowBottomSheetSaveContact(show: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                canShowBottomSheetSaveContact = show,
            )
        }
    }

    private fun isSaveContactSuccess(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                isSaveContactSuccess = value,
            )
        }
    }
    private fun isSaveTempSuccess(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                isSaveTempSuccess = value,
            )
        }
    }

    private fun updateCanShowBottomSheetSaveTemp(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                canShowBottomSheetSaveTemp = value,
            )
        }
    }

    private fun updateCustomerCodeContact(value: String) {
        _state.update { currentState ->
            currentState.copy(
                customerCodeContact = value,
            )
        }
    }
    private fun updateCustomerCodeTemp(value: String) {
        _state.update { currentState ->
            currentState.copy(
                customerCodeTemp = value,
            )
        }
    }

    private fun sendErrorMessage(message: String?) {
        viewModelScope.launch {
            _events.send(ShowErrorMess(message.orEmpty()))
        }
    }

    fun createTemplateAccount(
        confirm: String = CONFIRM_SAVE_CONTACT_TRANSFER_NO,
        validateNapasAccountTransferDomain: ValidateNapasAccountTransferDomain?,
    ) {
        val params = CreateTemplateParams(
            username = userProf.getUserName() ?: "",
            tempTransaction = TempTransactionParams(
                toAccountName = state.value.customerCodeTemp,
                toBankName = validateNapasAccountTransferDomain?.toBankName ?: "",
                fromAccountNo = validateNapasAccountTransferDomain?.fromAcctNo ?: "",
                toAccountNo = validateNapasAccountTransferDomain?.toAcctNo ?: "",
                amount = validateNapasAccountTransferDomain?.amount ?: "",
                toBankCode = validateNapasAccountTransferDomain?.binCode ?: "",
                userId = userProf.getUserName() ?: "",
                currency = validateNapasAccountTransferDomain?.currency ?: "",
                content = validateNapasAccountTransferDomain?.remark ?: "",
                tranType = validateNapasAccountTransferDomain?.trxType ?: "",
                confirm = confirm,
            ),
        )
        launchJob(showLoading = true) {
            val res = transferUseCase.createTemplate(params)
            handleResource(res) { data ->
                isSaveTempSuccess(true)
                sendErrorMessage(resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_saved_temp_success))
            }
        }
    }

    fun createTemplateCard(
        confirm: String = CONFIRM_SAVE_TEMP_TRANSFER_NO,
        validateNapasCardTransferDomains: ValidateNapasCardTransferDomains?,
    ) {
        val params = CreateTemplateParams(
            username = userProf.getUserName() ?: "",
            tempTransaction = TempTransactionParams(
                toBankName = validateNapasCardTransferDomains?.toBankName ?: "",
                fromAccountNo = validateNapasCardTransferDomains?.fromAcctNo ?: "",
                toAccountName = state.value.customerCodeTemp,
                amount = validateNapasCardTransferDomains?.amount ?: "",
                userId = userProf.getUserName() ?: "",
                currency = validateNapasCardTransferDomains?.currency ?: "",
                content = validateNapasCardTransferDomains?.remark ?: "",
                tranType = Tags.TransferType.SERVICE_TYPE_TRANSFER_NAPAS_CARD,
                toCardNo = validateNapasCardTransferDomains?.toCardNo ?: "",
                confirm = confirm,
            ),
        )
        launchJob(showLoading = true) {
            val res = transferUseCase.createTemplate(params)
            handleResource(res) { data ->
                isSaveTempSuccess(true)
                sendErrorMessage(resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_saved_temp_success))
            }
        }
    }

    fun contactAccountCreate(validateNapasAccountTransferDomain: ValidateNapasAccountTransferDomain?) {
        val params = ContactCreateParams(
            accountNo = validateNapasAccountTransferDomain?.toAcctNo ?: "",
            confirm = CONFIRM_SAVE_CONTACT_TRANSFER_NO,
            currency = validateNapasAccountTransferDomain?.currency ?: "",
            customercode = state.value.customerCodeContact,
            payeeName = validateNapasAccountTransferDomain?.toAcctName ?: "",
            receiveBankName = Utils.g()
                .removeAccent(validateNapasAccountTransferDomain?.toBankName ?: ""),
            revBankCode = validateNapasAccountTransferDomain?.binCode ?: "",
            serviceId = getServiceId(validateNapasAccountTransferDomain?.trxType ?: ""),
            username = userProf.getUserName() ?: "",
            tranType = validateNapasAccountTransferDomain?.trxType ?: "",
        )
        launchJob(showLoading = true) {
            val res = transferUseCase.contactCreate(params)
            handleResource(res) { data ->
                isSaveContactSuccess(true)
                transferCacheManager.clearContactList()
            }
        }
    }

    fun contactCardCreate(validateNapasCardTransferDomains: ValidateNapasCardTransferDomains?) {
        val params = ContactCreateParams(
            cardNo = validateNapasCardTransferDomains?.toCardNo ?: "",
            confirm = CONFIRM_SAVE_CONTACT_TRANSFER_NO,
            currency = validateNapasCardTransferDomains?.currency ?: "",
            customercode = state.value.customerCodeContact,
            payeeName = validateNapasCardTransferDomains?.toCardName ?: "",
            receiveBankName = Utils.g()
                .removeAccent(validateNapasCardTransferDomains?.toBankName ?: ""),
            revBankCode = "99998",
            serviceId = Tags.TransferType.TYPE_NAPAS_CARD,
            username = userProf.getUserName() ?: "",
            tranType = Tags.TransferType.TYPE_NAPAS_CARD,
        )
        launchJob(showLoading = true) {
            val res = transferUseCase.contactCreate(params)
            handleResource(res) { data ->
                isSaveContactSuccess(true)
                transferCacheManager.clearContactList()
            }
        }
    }

    private fun getNextApproversListString() {
        if (KEY_TRANSFER_TYPE_ACCOUNT == state.value.isTransferTypeKey) {
            _state.update { currentState ->
                currentState.copy(
                    listApproverComfirmString = if (state.value.validateNapasAccountTransferDomain?.nextApprovers?.isNotEmpty() == true) {
                        state.value.validateNapasAccountTransferDomain?.nextApprovers?.joinToString("\n") { it.username ?: "" } ?: ""
                    } else {
                        ""
                    },
                )
            }
        } else {
            _state.update { currentState ->
                currentState.copy(
                    listApproverComfirmString = if (state.value.validateNapasCardTransferDomains?.nextApprovers?.isNotEmpty() == true) {
                        state.value.validateNapasAccountTransferDomain?.nextApprovers?.joinToString("\n") { it.username ?: "" } ?: ""
                    } else {
                        ""
                    },
                )
            }
        }
    }

    fun readAmountInWord(amount: String? = null, ccy: String? = null) =
        moneyHelper.convertAmountToWords(amount ?: "", ccy ?: MoneyCurrency.VND.name)

    private fun updateMoneyReader(amount: String, currency: String) {
        _state.update { currentState ->
            currentState.copy(
                amountReader = readAmountInWord(amount, currency),
            )
        }
    }

    override fun onDisplayErrorMessage(exception: AppException) {
        if (exception is AppException.ApiException && exception.requestPath == Constants.MB_CREATE_TEMPLATE) {
            if ("2" == exception.code) {
                viewModelScope.launch {
                    _events.send(HandleErrorCreateTemplateState(exception.message ?: ""))
                }
                return
            }
        }
        super.onDisplayErrorMessage(exception)
    }

    fun onAction(action: MakerTransferResultAction) {
        when (action) {
            is MakerTransferResultAction.OnBackPressed -> {
                viewModelScope.launch {
                    _events.send(NavigateBack)
                }
            }

            is MakerTransferResultAction.MakerNewTransfer -> {
                viewModelScope.launch {
                    if (KEY_TRANSFER_TYPE_ACCOUNT == state.value.isTransferTypeKey) {
                        state.value.validateNapasAccountTransferDomain?.bankDomain?.let {
                            _events.send(
                                MakerNewTransferAccount(it),
                            )
                        }
                    } else if (KEY_TRANSFER_TYPE_CARD == state.value.isTransferTypeKey) {
                        _events.send(MakerNewTransferCard)
                    }
                }
            }

            is MakerTransferResultAction.OnGetNextApproversListString -> {
                getNextApproversListString()
            }

            is MakerTransferResultAction.SelectedTabIndex -> {
                updateSelectedTabIndex(action.selectedTabIndex)
            }

            is MakerTransferResultAction.OnShowSaveContactBottomSheet -> {
                canShowBottomSheetSaveContact(action.canShowBottomSheetSaveContact)
            }

            is MakerTransferResultAction.OnClickSaveContactBottomSheet -> {
                if (action.customercode.isNotEmpty()) {
                    updateCustomerCodeContact(action.customercode)
                    if (KEY_TRANSFER_TYPE_ACCOUNT == state.value.isTransferTypeKey) {
                        contactAccountCreate(state.value.validateNapasAccountTransferDomain)
                    } else {
                        contactCardCreate(state.value.validateNapasCardTransferDomains)
                    }
                    canShowBottomSheetSaveContact(false)
                } else {
                    sendErrorMessage(resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_saved_contact_input))
                }
            }

            is MakerTransferResultAction.OnShowSaveTempBottomSheet -> {
                updateCanShowBottomSheetSaveTemp(action.canShowBottomSheetSaveTemp)
            }

            is MakerTransferResultAction.OnClickSaveTempBottomSheet -> {
                val isAccountTransfer = state.value.isTransferTypeKey == KEY_TRANSFER_TYPE_ACCOUNT

                val createTemplate: () -> Unit = {
                    if (isAccountTransfer) {
                        createTemplateAccount(
                            confirm = action.confirm,
                            state.value.validateNapasAccountTransferDomain,
                        )
                    } else {
                        createTemplateCard(
                            confirm = action.confirm,
                            state.value.validateNapasCardTransferDomains,
                        )
                    }
                }

                when {
                    action.customercode.isNotEmpty() -> {
                        updateCustomerCodeTemp(action.customercode)
                        createTemplate()
                        updateCanShowBottomSheetSaveTemp(false)
                    }

                    CONFIRM_SAVE_TEMP_TRANSFER_YES == action.confirm -> {
                        createTemplate()
                    }

                    else -> {
                        sendErrorMessage(
                            resourceProvider.getString(
                                com.vietinbank.core_ui.R.string.maker_transfer_saved_contact_input,
                            ),
                        )
                    }
                }
            }
        }
    }

    sealed class MakerTransferResultEvent {
        data object NavigateBack : MakerTransferResultEvent()
        data class MakerNewTransferAccount(val bankDomain: DataBankDomain) :
            MakerTransferResultEvent()

        data object MakerNewTransferCard : MakerTransferResultEvent()
        data class ShowErrorMess(val errorMess: String) : MakerTransferResultEvent()
        data class HandleErrorCreateTemplateState(val errorMess: String) :
            MakerTransferResultEvent()
    }

    sealed class MakerTransferResultAction {
        data object OnBackPressed : MakerTransferResultAction()
        data object MakerNewTransfer : MakerTransferResultAction()
        data object OnGetNextApproversListString : MakerTransferResultAction()
        data class SelectedTabIndex(val selectedTabIndex: Int) : MakerTransferResultAction()
        data class OnShowSaveContactBottomSheet(val canShowBottomSheetSaveContact: Boolean) :
            MakerTransferResultAction()

        data class OnShowSaveTempBottomSheet(val canShowBottomSheetSaveTemp: Boolean) :
            MakerTransferResultAction()

        data class OnClickSaveContactBottomSheet(val customercode: String) :
            MakerTransferResultAction()

        data class OnClickSaveTempBottomSheet(val customercode: String, val confirm: String) :
            MakerTransferResultAction()
    }

    data class MakerTransferResultState(
        var validateNapasAccountTransferDomain: ValidateNapasAccountTransferDomain? = null,
        var validateNapasCardTransferDomains: ValidateNapasCardTransferDomains? = null,
        var listApproverComfirmString: String = "",
        var createTransferDomain: CreateTransferDomain? = null,
        var amountString: String = "",
        var amountReader: String = "",
        var isTransferTypeKey: String = "",
        var isTypeSplitTransfer: Boolean = false,
        var totalVatSplitTransfer: String = "",
        var selectedTabIndex: Int = 0,
        var canShowBottomSheetSaveContact: Boolean = false,
        var isSaveContactSuccess: Boolean = false,
        var isSaveTempSuccess: Boolean = false,
        var customerCodeContact: String = "",
        var customerCodeTemp: String = "",
        var canShowBottomSheetSaveTemp: Boolean = false,

    )

    inline fun <reified T> decodeArgumentJson(arguments: Bundle, key: String): T? {
        return try {
            val json = arguments.getString(key, "") ?: return null
            Utils.g().provideGson().fromJson(
                URLDecoder.decode(json, UTF8),
                T::class.java,
            )
        } catch (_: Exception) {
            null
        }
    }

    fun handleArguments(arguments: Bundle) {
        decodeArgumentJson<CreateTransferDomain>(
            arguments,
            RESULT_TRANSFER_ITEM,
        )?.let { updateCreateTransferDomain(it) }

        when (arguments.getString(KEY_TRANSFER_TYPE, "")) {
            KEY_TRANSFER_TYPE_ACCOUNT -> {
                updateIsTransferTypeKey(KEY_TRANSFER_TYPE_ACCOUNT)
                decodeArgumentJson<ValidateNapasAccountTransferDomain>(
                    arguments,
                    CONFIRM_TRANSFER_ITEM,
                )?.let { updateValidateNapasAccountTransferDomain(it) }
            }

            KEY_TRANSFER_TYPE_CARD -> {
                updateIsTransferTypeKey(KEY_TRANSFER_TYPE_CARD)
                decodeArgumentJson<ValidateNapasCardTransferDomains>(
                    arguments,
                    CONFIRM_TRANSFER_ITEM,
                )?.let { updateValidateNapasCardTransferDomains(it) }
            }
        }
    }
}