package com.vietinbank.feture_maker.transfer_new.screen.bottomSheet

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_domain.models.maker.DataBankDomain
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.foundation.textfield.FoundationFieldType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_maker.R
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.MakerTransferBankChoosedViewModel.MakerTransferBankChooseAction
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_dashboard.BankItem
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChooseBankBottomSheet(
    isVisible: Boolean,
    title: String,
    items: List<DataBankDomain>,
    onItemSelected: (DataBankDomain) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
    imageLoader: CoilImageLoader,
    searchTextBank: String,
    onAction: ((MakerTransferBankChooseAction) -> Unit),
) {
    if (isVisible) {
        BaseBottomSheet(
            visible = true,
            onDismissRequest = onDismiss,
            containerColor = FDS.Colors.backgroundBgContainer,
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(0.9f).clip(RoundedCornerShape(AppSizer.Radius.radius32)),
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth(),
                ) {
                    Icon(
                        modifier = Modifier
                            .safeClickable {
                                onDismiss.invoke()
                            },
                        painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_close),
                        contentDescription = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_account_search),
                        tint = FDS.Colors.white,
                    )
                    Column(
                        modifier = Modifier
                            .fillMaxWidth(),
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        // Title
                        FoundationText(
                            text = title,
                            style = FDS.Typography.headingH4,
                            color = FDS.Colors.characterPrimary,
                            modifier = Modifier.padding(
                                bottom = FDS.Sizer.Gap.gap24,
                            ).padding(horizontal = FDS.Sizer.Gap.gap24),
                        )

                        HorizontalDivider(
                            color = FDS.Colors.divider,
                            thickness = FoundationDesignSystem.Sizer.Stroke.stroke1,
                            modifier = Modifier.padding(bottom = FDS.Sizer.Gap.gap8),
                        )

                        FoundationFieldType(
                            modifier = Modifier
                                .fillMaxWidth().padding(horizontal = FDS.Sizer.Gap.gap24)
                                .clip(RoundedCornerShape(AppSizer.Radius.radius32)).background(FDS.Colors.gray50),
                            value = searchTextBank,
                            onValueChange = {
                                onAction.invoke(
                                    MakerTransferBankChooseAction.OnSearchTextBankText(
                                        searchTextBank = it,
                                    ),
                                )
                            },
                            placeholder = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_find_bank),
                            leadingIcon = {
                                Icon(
                                    painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_search),
                                    contentDescription = "Search",
                                    tint = Color.Unspecified,
                                )
                            },
                        )

                        items.let { listDataBanks ->
                            LazyColumn(
                                modifier = Modifier.padding(top = FDS.Sizer.Gap.gap12).padding(horizontal = FDS.Sizer.Gap.gap24),
                                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
                            ) {
                                items(listDataBanks) { bank ->
                                    BankItem(
                                        bank = bank,
                                        imageLoader = imageLoader,
                                        onAction = { onItemSelected(it) },
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
