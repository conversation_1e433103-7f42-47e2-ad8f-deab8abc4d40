package com.vietinbank.feture_maker.transfer_new.screen.bottomSheet

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.login.AccountDefaultDomain
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationSelector
import com.vietinbank.core_ui.components.SelectorType
import com.vietinbank.core_ui.components.foundation.textfield.FoundationFieldType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_maker.R
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.MakerTransferBankChoosedViewModel.MakerTransferBankChooseAction
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChooseAccountBottomSheet(
    isVisible: Boolean,
    title: String,
    items: List<AccountDefaultDomain>,
    onItemSelected: (AccountDefaultDomain) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
    searchAccountText: String,
    onAction: ((MakerTransferBankChooseAction) -> Unit),
) {
    if (isVisible) {
        BaseBottomSheet(
            visible = true,
            onDismissRequest = onDismiss,
            containerColor = FDS.Colors.backgroundBgContainer,
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(0.9f).clip(RoundedCornerShape(AppSizer.Radius.radius32)),
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    Icon(
                        modifier = Modifier
                            .safeClickable {
                                onDismiss.invoke()
                            },
                        painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_close),
                        contentDescription = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_account_search),
                        tint = FDS.Colors.white,
                    )
                    Column(
                        modifier = Modifier
                            .fillMaxWidth(),
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        // Title
                        FoundationText(
                            text = title,
                            style = FDS.Typography.headingH4,
                            color = FDS.Colors.characterPrimary,
                            modifier = Modifier
                                .padding(
                                    bottom = FDS.Sizer.Gap.gap24,
                                )
                                .padding(horizontal = FDS.Sizer.Gap.gap24),
                        )

                        HorizontalDivider(
                            color = FDS.Colors.divider,
                            thickness = FDS.Sizer.Stroke.stroke1,
                            modifier = Modifier.padding(bottom = FDS.Sizer.Gap.gap8),
                        )
                        FoundationFieldType(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = FDS.Sizer.Gap.gap24)
                                .clip(RoundedCornerShape(AppSizer.Radius.radius32))
                                .background(FDS.Colors.gray50),
                            value = searchAccountText,
                            onValueChange = {
                                onAction.invoke(
                                    MakerTransferBankChooseAction.OnSearchAccountText(
                                        searchAccountText = it,
                                    ),
                                )
                            },
                            placeholder = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_find_account),
                            leadingIcon = {
                                Icon(
                                    painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_search),
                                    contentDescription = "Search",
                                    tint = Color.Unspecified,
                                )
                            },
                        )
                        items.let { listDataAccounts ->
                            LazyColumn(
                                modifier = Modifier
                                    .padding(top = FDS.Sizer.Gap.gap12)
                                    .padding(
                                        horizontal = FDS.Sizer.Gap.gap24,
                                    ),
                            ) {
                                items(listDataAccounts) { bank ->
                                    AccountItem(
                                        accountItem = bank,
                                        onClick = { onItemSelected(bank) },
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun AccountItem(
    accountItem: AccountDefaultDomain,
    onClick: () -> Unit,
) {
    Column {
        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap12))
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .safeClickable { onClick.invoke() },
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Icon(
                painter = painterResource(id = com.vietinbank.feature_maker.R.drawable.ic_feature_maker_transfer_wallet),
                contentDescription = "item.text",
                tint = Color.Unspecified,
                modifier = Modifier.size(FDS.Sizer.Icon.icon40),
            )
            Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap16))
            Column(
                modifier = Modifier.weight(1f),
            ) {
                FoundationText(
                    text = accountItem.accountName ?: "",
                    style = FDS.Typography.bodyB2,
                    color = FDS.Colors.characterHighlighted,
                )
                FoundationText(
                    text = accountItem.accountNo ?: "",
                    style = FDS.Typography.captionCaptionL,
                    color = FDS.Colors.characterSecondary,
                )
                FoundationText(
                    text = Utils.g()
                        .getDotMoneyHasCcy(
                            accountItem.availableBalance ?: "",
                            accountItem.currency ?: "",
                        ),
                    style = FDS.Typography.captionCaptionL,
                    color = FDS.Colors.characterSecondary,
                )
            }
            FoundationSelector(
                boxType = SelectorType.Radio,
                isSelected = accountItem.isSelected,
                modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                onClick = { onClick.invoke() },
            )
        }
        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))

        HorizontalDivider(
            color = FDS.Colors.divider,
            thickness = FoundationDesignSystem.Sizer.Stroke.stroke1,
            modifier = Modifier.padding(bottom = FDS.Sizer.Gap.gap16),
        )
    }
}
