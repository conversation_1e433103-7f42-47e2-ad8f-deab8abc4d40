package com.vietinbank.feture_maker.transfer_new.screen.contact_template_recent

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.home.TransactionInfo
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.components.foundation.textfield.FoundationFieldType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_maker.R
import com.vietinbank.feture_maker.transfer_new.screen.empty_list.EmptyListScreen
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun RecentTransactionScreen(
    imageLoader: CoilImageLoader,
    modifier: Modifier,
    searchRecentText: String,
    onSearchRecentText: (searchContactText: String) -> Unit,
    listLatest: MutableList<TransactionInfo>,
    onRecentItemClick: (onRecentItemClick: TransactionInfo) -> Unit,
    isLoadingListRecent: Boolean,
) {
    Box(
        modifier = modifier,
    ) {
        Column {
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
            FoundationFieldType(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = FDS.Sizer.Gap.gap24)
                    .clip(RoundedCornerShape(AppSizer.Radius.radius32))
                    .background(FDS.Colors.gray50),
                value = searchRecentText,
                onValueChange = { onSearchRecentText.invoke(it) },
                placeholder = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_find_recent),
                leadingIcon = {
                    Icon(
                        painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_search),
                        contentDescription = "Search",
                        tint = Color.Unspecified,
                        modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                    )
                },
            )
            Spacer(Modifier.height(FDS.Sizer.Gap.gap8))
            if (isLoadingListRecent == true) {
                return
            }
            RecentTransTab(imageLoader = imageLoader, listLatest = listLatest, onRecentItemClick = onRecentItemClick)
        }
    }
}

@Composable
fun RecentTransTab(
    imageLoader: CoilImageLoader,
    listLatest: MutableList<TransactionInfo>,
    onRecentItemClick: (TransactionInfo) -> Unit,
) {
    if (listLatest.isNotEmpty()) {
        LazyColumn(
            modifier = Modifier.padding(horizontal = FDS.Sizer.Gap.gap24),
        ) {
            items(listLatest) { item ->
                RecentTransItem(imageLoader = imageLoader, recentItem = item, onClick = { onRecentItemClick.invoke(item) })
            }
        }
    } else {
        EmptyListScreen(
            iconResourceID = R.mipmap.ic_feature_maker_transfer_recent_list_null,
            title = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_tab_saved_temp_list_null),
            content = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_tab_saved_temp_list_null_content),
        )
    }
}

@Composable
fun RecentTransItem(
    imageLoader: CoilImageLoader,
    recentItem: TransactionInfo,
    onClick: () -> Unit,
) {
    Column {
        Row(
            modifier = Modifier
                .fillMaxWidth().padding(vertical = FDS.Sizer.Gap.gap16)
                .safeClickable { onClick.invoke() },
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Box(
                modifier = Modifier
                    .size(FDS.Sizer.Icon.icon40)
                    .clip(CircleShape)
                    .background(FDS.Colors.gray50),
                contentAlignment = Alignment.Center,
            ) {
                imageLoader.LoadUrl(
                    url = recentItem.iconUrl ?: "",
                    isCache = true,
                    placeholderRes = com.vietinbank.core_ui.R.drawable.ic_search,
                    errorRes = com.vietinbank.core_ui.R.drawable.ic_search,
                    modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                )
            }
            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(start = FDS.Sizer.Gap.gap16),
            ) {
                FoundationText(
                    text = recentItem.toAccountName ?: "",
                    style = FDS.Typography.bodyB2,
                    color = FDS.Colors.characterPrimary,
                )
                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))

                FoundationText(
                    text = recentItem.toAccount ?: "",
                    style = FDS.Typography.captionCaptionL,
                    color = FDS.Colors.characterSecondary,
                )

                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))

                FoundationText(
                    text = recentItem.bankName ?: "",
                    style = FDS.Typography.captionCaptionL,
                    color = FDS.Colors.characterSecondary,
                )
                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
            }
        }
        Row(
            modifier = Modifier
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.End,
        ) {
            FoundationText(
                text = Utils.g()
                    .getDotMoneyHasCcy(recentItem.amount ?: "", recentItem.currency ?: ""),
                style = FDS.Typography.bodyB2Emphasized,
                color = FDS.Colors.characterHighlighted,
            )
        }
        HorizontalDivider(
            color = FDS.Colors.divider,
            thickness = FoundationDesignSystem.Sizer.Stroke.stroke1,
            modifier = Modifier.padding(vertical = FDS.Sizer.Gap.gap16),
        )
    }
}