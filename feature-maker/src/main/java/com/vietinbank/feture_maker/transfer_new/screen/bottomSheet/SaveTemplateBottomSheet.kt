package com.vietinbank.feture_maker.transfer_new.screen.bottomSheet

import android.text.TextUtils
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_common.R
import com.vietinbank.core_common.models.TransferResult
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.maker.CreateTransferDomain
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountTransferDomain
import com.vietinbank.core_domain.models.maker.ValidateNapasCardTransferDomains
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationTransfer
import com.vietinbank.core_ui.components.foundation.textfield.FoundationEditText
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SaveTemplateBottomSheet(
    isVisible: Boolean,
    onCancel: () -> Unit,
    onConfirm: (String) -> Unit,
    modifier: Modifier = Modifier,
    imageLoader: CoilImageLoader? = null,
    isTransferAccount: Boolean? = true,
    validateNapasAccountTransferDomain: ValidateNapasAccountTransferDomain? = null,
    validateNapasCardTransferDomains: ValidateNapasCardTransferDomains? = null,
    createTransferDomain: CreateTransferDomain? = null,
) {
    var customerCode by remember { mutableStateOf("") }
    val scrollState = rememberScrollState()

    if (isVisible) {
        BaseBottomSheet(
            visible = true,
            containerColor = Color.Transparent,
            onDismissRequest = onCancel,
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(AppSizer.Radius.radius32)),
            ) {
                Column(
                    modifier = Modifier.fillMaxWidth().verticalScroll(scrollState),
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(
                                color = FDS.Colors.backgroundBgContainer,
                                shape = RoundedCornerShape(
                                    FDS.Sizer.Gap.gap32,

                                ),
                            ),
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        // Title
                        FoundationText(
                            text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_saved_temp),
                            style = FDS.Typography.headingH3,
                            color = FDS.Colors.characterHighlighted,
                            modifier = Modifier.padding(vertical = FDS.Sizer.Gap.gap24),
                        )

                        HorizontalDivider(
                            color = FDS.Colors.divider,
                            thickness = FDS.Sizer.Stroke.stroke1,
                        )
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = FDS.Sizer.Gap.gap16)
                                .border(
                                    width = FDS.Sizer.Stroke.stroke1,
                                    color = FDS.Colors.strokeDivider,
                                    shape = RoundedCornerShape(FDS.Sizer.Gap.gap16),
                                ),
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            if (isTransferAccount == true) {
                                FoundationTransfer(
                                    verticalPadding = FDS.Sizer.Gap.gap16,
                                    horizontalPadding = FDS.Sizer.Gap.gap16,
                                    imageLoader = imageLoader,

                                    accountFrom = TransferResult(
                                        bankIconResource = com.vietinbank.feature_maker.R.drawable.ic_vietinbank,
                                        accountName = createTransferDomain?.fullNameCreate ?: "",
                                        accountNo = validateNapasAccountTransferDomain?.fromAcctNo
                                            ?: "",
                                        fromAccBalance = "",
                                    ),
                                    accountTo = TransferResult(
                                        bankIconURL = validateNapasAccountTransferDomain?.toBankIconURL
                                            ?: "",
                                        bankName = validateNapasAccountTransferDomain?.bankDomain?.shortName
                                            ?: "",
                                        accountName = validateNapasAccountTransferDomain?.toAcctName
                                            ?: "",
                                        accountNo = validateNapasAccountTransferDomain?.toAcctNo
                                            ?: "",
                                    ),
                                    lstContent = buildList<Pair<String?, String?>> {
                                        add(
                                            Pair(
                                                stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_content),
                                                validateNapasAccountTransferDomain?.remark ?: "",
                                            ),
                                        )

                                        add(
                                            Pair(
                                                stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_fee),
                                                Utils.g().getDotMoneyHasCcy(
                                                    validateNapasAccountTransferDomain?.feeVat
                                                        ?: "",
                                                    validateNapasAccountTransferDomain?.currency
                                                        ?: "",
                                                ),
                                            ),
                                        )

                                        add(
                                            Pair(
                                                stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_fee),
                                                TransferConstants.Transfer.getFeeMethodName(
                                                    validateNapasAccountTransferDomain?.feePayMethod
                                                        ?: "",
                                                ),
                                            ),
                                        )

                                        if (TextUtils.isEmpty(validateNapasAccountTransferDomain?.processDate)) {
                                            addAll(
                                                listOf(
                                                    Pair(
                                                        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_transfer_date_time_transfer),
                                                        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_now),
                                                    ),
                                                    Pair(
                                                        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_transfer_date_time_transfer_maker),
                                                        createTransferDomain?.createdDate
                                                            ?: "",
                                                    ),
                                                ),
                                            )
                                        } else {
                                            addAll(
                                                listOf(
                                                    Pair(
                                                        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_transfer_date_time_transfer),
                                                        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_schedule),
                                                    ),
                                                    Pair(
                                                        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_transfer_date_schedule),
                                                        validateNapasAccountTransferDomain?.processDate
                                                            ?: "",
                                                    ),
                                                ),
                                            )
                                        }
                                    },
                                )
                            } else {
                                FoundationTransfer(
                                    imageLoader = imageLoader,
                                    accountFrom = TransferResult(
                                        bankIconResource = com.vietinbank.feature_maker.R.drawable.ic_vietinbank,
                                        accountName = createTransferDomain?.fullNameCreate
                                            ?: "",
                                        accountNo = validateNapasCardTransferDomains?.fromAcctNo
                                            ?: "",
                                        fromAccBalance = validateNapasCardTransferDomains?.fromAccBalance
                                            ?: "",
                                    ),
                                    accountTo = TransferResult(
                                        bankIconURL = validateNapasCardTransferDomains?.toBankIconURL
                                            ?: "",
                                        bankName = validateNapasCardTransferDomains?.toBankName
                                            ?: "",
                                        accountName = validateNapasCardTransferDomains?.toCardName
                                            ?: "",
                                        accountNo = validateNapasCardTransferDomains?.toCardNo
                                            ?: "",
                                    ),
                                    lstContent = buildList<Pair<String, String>> {
                                        add(
                                            Pair(
                                                stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_content),
                                                validateNapasCardTransferDomains?.remark ?: "",
                                            ),
                                        )

                                        add(
                                            Pair(
                                                stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_fee),
                                                Utils.g().getDotMoneyHasCcy(
                                                    validateNapasCardTransferDomains?.feeVat ?: "",
                                                    validateNapasCardTransferDomains?.currency
                                                        ?: "",
                                                ),
                                            ),
                                        )

                                        add(
                                            Pair(
                                                stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_fee),
                                                TransferConstants.Transfer.getFeeMethodName(
                                                    validateNapasCardTransferDomains?.feePayMethod
                                                        ?: "",
                                                ),
                                            ),
                                        )

                                        if (TextUtils.isEmpty(validateNapasCardTransferDomains?.processDate)) {
                                            addAll(
                                                listOf(
                                                    Pair(
                                                        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_transfer_date_time_transfer),
                                                        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_now),
                                                    ),
                                                    Pair(
                                                        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_transfer_date_schedule),
                                                        validateNapasCardTransferDomains?.processDate
                                                            ?: "",
                                                    ),
                                                ),
                                            )
                                        }
                                    },
                                )
                            }
                        }
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
                        FoundationEditText(
                            modifier = Modifier.padding(
                                horizontal = FDS.Sizer.Gap.gap24,
                            ),
                            value = customerCode,
                            onValueChange = { newValue ->
                                customerCode = newValue
                            },
                            placeholder = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_saved_contact_input),
                            hintText = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_saved_contact_input),
                            maxLength = 146,
                        )
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
                    }
                }
            }
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = FDS.Sizer.Gap.gap16),
            ) {
                FoundationButton(
                    isLightButton = false,
                    text = stringResource(com.vietinbank.core_ui.R.string.dialog_button_back),
                    onClick = {
                        onCancel.invoke()
                    },
                    enabled = true,
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .padding(
                            horizontal = FDS.Sizer.Gap.gap4,

                        ),
                )
                FoundationButton(
                    isLightButton = true,
                    text = stringResource(com.vietinbank.core_ui.R.string.dialog_button_confirm),
                    onClick = { onConfirm.invoke(customerCode) },
                    enabled = true,
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .padding(
                            horizontal = FDS.Sizer.Gap.gap4,
                        ),
                )
            }
        }
    }
}