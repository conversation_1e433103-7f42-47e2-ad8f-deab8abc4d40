package com.vietinbank.feture_maker.di

import com.vietinbank.feture_maker.factory.BottomSheetFactory
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Created by vandz on 25/4/25.
 * Hilt Module cung cấp BottomSheetFactory cho feature-maker
 */
@Module
@InstallIn(SingletonComponent::class)
object MakerUIModule {

    @Provides
    @Singleton
    fun provideBottomSheetFactory(): BottomSheetFactory {
        return BottomSheetFactory()
    }
}