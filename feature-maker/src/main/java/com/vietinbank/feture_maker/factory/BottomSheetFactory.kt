package com.vietinbank.feture_maker.factory

import com.vietinbank.feture_maker.maker_ui.bottomSheet.AccListBottomSheetFragment
import com.vietinbank.feture_maker.maker_ui.bottomSheet.BankListBottomSheetFragment
import com.vietinbank.feture_maker.maker_ui.bottomSheet.DataFillBottomSheetFragment
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Created by vand<PERSON> on 25/4/25.
 *
 * Factory class để tạo và quản lý các BottomSheetFragment
 */
@Singleton
class BottomSheetFactory @Inject constructor() {
    /**
     * Tạo một instance mới của AccListBottomSheetFragment
     */
    fun createAccountSelectorBottomSheet(): AccListBottomSheetFragment {
        return AccListBottomSheetFragment()
    }

    /**
     * Tạo một instance mới của BankListBottomSheetFragment
     */
    fun createBankSelectorBottomSheet(): BankListBottomSheetFragment {
        return BankListBottomSheetFragment()
    }

    /**
     * Tạo một instance mới của DataFillBottomSheetFragment
     */
    fun createDataFillBottomSheet(): DataFillBottomSheetFragment {
        return DataFillBottomSheetFragment()
    }
}