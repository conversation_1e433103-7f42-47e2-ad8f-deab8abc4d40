package com.vietinbank.feture_maker.maker_ui.create

import android.Manifest
import android.os.Bundle
import android.text.Editable
import android.text.InputFilter
import android.text.TextUtils
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.viewbinding.ViewBinding
import com.google.gson.Gson
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.extensions.getNextDay
import com.vietinbank.core_common.extensions.loadUrl
import com.vietinbank.core_common.extensions.onSearchTextChanged
import com.vietinbank.core_common.extensions.showDatePickerDialog
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.Utils.asciiOnlyFilter
import com.vietinbank.core_domain.models.login.AccountDefaultDomain
import com.vietinbank.core_domain.models.maker.ContactDomains
import com.vietinbank.core_domain.models.maker.DataBankDomain
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountTransferDomain
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_maker.R
import com.vietinbank.feature_maker.databinding.FragmentMakerCreateTransferAccountBinding
import com.vietinbank.feture_maker.maker_ui.adapter.BankAdapter
import com.vietinbank.feture_maker.maker_ui.bottomSheet.DataFillBottomSheetFragment
import com.vietinbank.feture_maker.maker_ui.bottomSheet.NextApproverBottomSheetFragment
import com.vietinbank.feture_maker.maker_ui.bottomSheet.SplitTransferBottomSheetFragment
import com.vietinbank.feture_maker.maker_ui.viewmodel.MakeTransferViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class MakerTransferAccountFragment : BaseFragment<MakeTransferViewModel>() {
    override val viewModel: MakeTransferViewModel by activityViewModels()
    override val useCompose: Boolean = false
    private val chooseTimeScheduleTypeDialog = DataFillBottomSheetFragment()
    val splitTransferBottomSheetFragment = SplitTransferBottomSheetFragment()
    var nextApproverBottomSheetAccFragment = NextApproverBottomSheetFragment()

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override fun inflateViewBinding(inflater: LayoutInflater, container: ViewGroup?): ViewBinding {
        return FragmentMakerCreateTransferAccountBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initListener()
        initData()
        viewModel.getNapasBankList()
        viewModel.contactList()
    }

    private fun initData() {
        (binding as FragmentMakerCreateTransferAccountBinding).apply {
            setMaxLengthEdtRemark(146)
            viewModel.setUpBranchDialog()
            viewModel.setDataFeePaymentMethod()
            viewModel.setDataTypeScheduleTime(chooseTimeScheduleTypeDialog)
            val layoutManager = LinearLayoutManager(context, GridLayoutManager.HORIZONTAL, false)
            rcvListBankDefault.layoutManager = layoutManager
            rcvListBankDefault.adapter = viewModel.bankDefaultAdapter
            viewModel.bankDefaultAdapter.setData(mutableListOf())
            rcvContact.adapter = viewModel.contactAccountAdapter
            viewModel.contactAccountAdapter.setData(mutableListOf())
            tvTypeFee.text = viewModel.currentTypeTransfer?.name ?: ""
            tvTimeSchedule.text = viewModel.currentTypeAccountSchedule?.name ?: ""
            viewModel.accListState.observe(viewLifecycleOwner) { state ->
                when (state) {
                    is Resource.Success -> {
                        viewModel.getDefaultAccount(state.data.accountDefault)?.let {
                            clFromAccount.visibility = View.VISIBLE
                            setTextAccountInfo(viewModel.currentAccountDefaultDomain)
                            if (!TextUtils.isEmpty(edtReceiveAccount.text)) {
                                viewModel.validateNapasAccount(edtReceiveAccount.text.toString())
                            }
                        }
                    }

                    is Resource.Error -> {
                        clFromAccount.visibility = View.GONE
                    }
                }
            }
            viewModel.handleErrorvalidateNapasAccountState.observe(viewLifecycleOwner) {
                makeTransferTypeNormal()
            }
            viewModel.validateNapasAccountState.observe(viewLifecycleOwner) { state ->
                when (state) {
                    is Resource.Success -> {
                        tvReceiveAccount.text = state.data.accountOwner ?: ""
                        tvReceiveAccount.visibility = View.VISIBLE
                        tvReceiveAccountTitle.visibility = View.VISIBLE
                        edtAccountOwner.visibility = View.GONE
                        edtCustomerCode.setText("")
                        if (viewModel.isEnableSaveAccount(receiveAccount = edtReceiveAccount.text.toString())) {
                            swCreateContact.visibility = View.VISIBLE
                            tvSaveAccount.visibility = View.VISIBLE
                        } else {
                            swCreateContact.visibility = View.GONE
                            tvSaveAccount.visibility = View.GONE
                        }
                    }

                    is Resource.Error -> {
                        tvReceiveAccount.text = ""
                    }
                }
            }
            viewModel.validateNapasAccountTransferSplitState.observe(viewLifecycleOwner) { state ->
                makeTransferNomal(state)
            }
            viewModel.validateNapasAccountTransferState.observe(viewLifecycleOwner) { state ->
                if (Tags.TransferType.TYPE_SPLIT_TRANSFER_YES == state.isSplit && !viewModel.isTransferNomal()) {
                    splitTransferBottomSheetFragment.setData(state)
                    splitTransferBottomSheetFragment.show(
                        childFragmentManager,
                        "chooseContactAndSample",
                    )
                } else {
                    makeTransferNomal(state)
                }
            }

            viewModel.clickItemBankFromContactFragmentState.observe(viewLifecycleOwner) { contactDomains ->
                onItemContactClicked(contactDomains)
            }

            viewModel.clickAccountShampleFromContactFragmentState.observe(viewLifecycleOwner) {
                it.let {
                    viewModel.findMatchingDataBankDomain(
                        bank = it.toBankCode,
                        trantype = it.tranType,
                    )?.let { setTextBankInfo(it) }
                    edtReceiveAccount.setText(it.toAccountNo.toString())
                    val formattedText = Utils.g().getDotMoney(it.amount ?: "")
                    edtAmount.setText(formattedText)
                    edtRemark.setText(it.content ?: "")
                }
                viewModel.validateNapasAccount(it.toAccountNo ?: "")
            }

            viewModel.handleDatePickerAccountState.observe(viewLifecycleOwner) {
                tvDaySchedule.text = it
            }
            viewModel.onSelectTabAccountState.observe(viewLifecycleOwner) {
                clearAll()
            }
            viewModel.getNapasBankList.observe(viewLifecycleOwner) {
                if (viewModel.napasTransferParams != null) {
                    viewModel.napasTransferParams?.let { model ->
                        val bankName = if (model.receiveBank == Tags.TransferType.TYPE_00) {
                            ""
                        } else {
                            model.receiveBank.orEmpty()
                        }
                        val transferType = if (model.receiveBank == Tags.TransferType.TYPE_00) {
                            Tags.TransferType.TYPE_IN
                        } else {
                            model.tranType.orEmpty()
                        }
                        viewModel.findMatchingDataBankDomain(
                            bank = bankName,
                            trantype = transferType,
                        )?.let {
                            setTextBankInfo(it)
                        }
                        viewModel.getAccList()
                        edtReceiveAccount.setText(model.receiveAcct.orEmpty())
                        if (edtReceiveAccount.text?.isNotEmpty() == true) {
                            viewModel.validateNapasAccount(edtReceiveAccount.text.toString())
                        }
                        val formattedText = Utils.g().getDotMoney(model.amount ?: "")
                        edtAmount.setText(formattedText)
                        edtRemark.setText(model.remark.orEmpty())
                        // khong cho phep thay doi so tien voi th qr co so tien
                        enableFilterTransfer(model.allowEditAmount)
                    }
                }
            }
            viewModel.nextStatusTransactionByRuleState.observe(viewLifecycleOwner) { data ->
                if (data.nextApprovers?.size == 0) {
                    showNoticeDialog("Hiện không có người phê duyệt tiếp theo")
                } else {
                    data.nextApprovers?.let { nextApproverBottomSheetAccFragment.setData(it) }
                    nextApproverBottomSheetAccFragment.show(
                        childFragmentManager,
                        "chooseTimeScheduleTypeDialog",
                    )
                }
            }
        }
    }

    private fun FragmentMakerCreateTransferAccountBinding.makeTransferTypeNormal() {
        tvReceiveAccount.text = ""
        tvReceiveAccount.visibility = View.GONE
        tvReceiveAccountTitle.visibility = View.GONE
        edtAccountOwner.visibility = View.VISIBLE
        viewModel.changeType247ToNormalListBank()
        tvType.text = BankAdapter.getTypeName(
            Tags.TransferType.TYPE_NORMAL,
        )
        titleTypeFee.isEnabled = true
        tvTypeFee.isEnabled = true
        titleTimeSchedule.isEnabled = true
        tvTimeSchedule.isEnabled = true
    }

    private fun FragmentMakerCreateTransferAccountBinding.makeTransferNomal(
        state: ValidateNapasAccountTransferDomain,
    ) {
        // listConfirm
        val listConfirmTransferObjects = viewModel.confirmTransferAccountObjects(
            state,
            viewModel.currentAccountDefaultDomain?.currentBalance,
        )
        val listConfirmJson = Gson().toJson(listConfirmTransferObjects)
        // create bundle

        val bundle = Bundle().apply {
            if (Tags.TransferType.TYPE_SPLIT_TRANSFER_YES == state.isSplit && !viewModel.isTransferNomal()) {
                putString(
                    Tags.TransferType.TYPE_TRANSFER,
                    Tags.TransferType.TYPE_ACCOUNT_SPLIT,
                )
            } else {
                putString(
                    Tags.TransferType.TYPE_TRANSFER,
                    Tags.TransferType.TYPE_ACCOUNT,
                )
            }
            putBoolean(
                Tags.TransferType.IS_CONTACT_CREATE,
                viewModel.isSaveAccount,
            )
            state.customercode = (edtCustomerCode.text.toString())
            putString(
                Tags.TransferType.CONFIRM_OBJECT,
                Utils.g().provideGson().toJson(state),
            )
            putString(
                Tags.TransferType.LIST_CONFIRM_OBJECT,
                listConfirmJson,
            )
        }
//        appNavigator.goToMakerConfirmTransferFragment(bundle)
    }

    private fun initListener() {
        (binding as FragmentMakerCreateTransferAccountBinding).apply {
            edtReceiveAccount.filters = arrayOf(asciiOnlyFilter(), InputFilter.LengthFilter(30))
            edtAccountOwner.filters = arrayOf(asciiOnlyFilter())
            clDaySchedule.setThrottleClickListener {
                context?.let {
                    it.showDatePickerDialog { selectedDate ->
                        viewModel.handleDatePickerAccountState.postValue(selectedDate)
                    }
                }
            }
            viewModel.bankDefaultAdapter.onClickItem = {
                setTextBankInfo(it)
                viewModel.getAccList()
            }
            titleTypeFee.setThrottleClickListener {
                if (viewModel.canOpenTransferTypeAccDialog()) {
                    viewModel.chooseTransferTypeDialog.show(
                        parentFragmentManager,
                        "chooseTransferTypeDialog",
                    )
                }
            }
            titleTimeSchedule.setThrottleClickListener {
                if (viewModel.canOpenTimeScheduleTypeDialog()) {
                    chooseTimeScheduleTypeDialog.show(
                        parentFragmentManager,
                        "chooseTimeScheduleTypeDialog",
                    )
                }
            }
            tvTypeFee.setThrottleClickListener {
                titleTypeFee.callOnClick()
            }
            tvTimeSchedule.setThrottleClickListener {
                titleTimeSchedule.callOnClick()
            }
            viewModel.contactAccountAdapter.onClickItem = {
                onItemContactClicked(it)
            }
            tvAccountNumber.setThrottleClickListener {
                viewModel.chooseAccountNoDialog.show(parentFragmentManager, "chooseAccountNo")
            }

            viewModel.chooseAccountNoDialog.setOnClickItemListener {
                viewModel.currentAccountDefaultDomain = it
                setTextAccountInfo(viewModel.currentAccountDefaultDomain)
            }
            clBankDefault.setThrottleClickListener {
                viewModel.chooseBankDialog.show(parentFragmentManager, "clBankDefault")
            }
            clBank.setThrottleClickListener {
                viewModel.chooseBankDialog.show(parentFragmentManager, "choooseBank")
            }
            viewModel.chooseTransferTypeDialog.setOnClickItemListener {
                tvTypeFee.text = it.name
                viewModel.currentTypeTransfer = it
            }
            chooseTimeScheduleTypeDialog.setOnClickItemListener {
                tvTimeSchedule.text = it.name
                viewModel.currentTypeAccountSchedule = it
                if ("0" == it.id) {
                    clDaySchedule.visibility = View.GONE
                    tvDaySchedule.text = ""
                    titleTypeFee.isEnabled = true
                    tvTypeFee.isEnabled = true
                } else {
                    titleTypeFee.isEnabled = false
                    tvTypeFee.isEnabled = false
                    clDaySchedule.visibility = View.VISIBLE
                    tvDaySchedule.text = getNextDay()
                    viewModel.chooseTypeFeeBEN()
                    tvTypeFee.text = viewModel.currentTypeTransfer?.name
                }
            }
            viewModel.chooseBankDialog.setOnClickItemListener {
                resetFilterTransfer()
                setTextBankInfo(it)
                viewModel.getAccList()
            }
            edtReceiveAccount.setOnFocusChangeListener { _, hasFocus ->
                if (!hasFocus && !TextUtils.isEmpty(edtReceiveAccount.text)) {
                    viewModel.validateNapasAccount(receiveAccount = edtReceiveAccount.text.toString())
                }
            }
            edtReceiveAccount.onSearchTextChanged { s ->
                clearListApprover()
                if (viewModel.isTransferIn() || viewModel.isTransferNapas()) {
                    tvReceiveAccount.visibility = View.GONE
                    tvReceiveAccountTitle.visibility = View.GONE
                    edtAccountOwner.visibility = View.GONE
                }
                val newList = if (s.isEmpty()) {
                    viewModel.listContactFilter
                } else {
                    viewModel.listContactFilter.filter {
                        (it.account ?: "").lowercase().contains(
                            s.lowercase(),
                        ) || (it.placetoreceive ?: "").lowercase().contains(
                            s.lowercase(),
                        ) || (it.payeename ?: "").lowercase().contains(
                            s.lowercase(),
                        )
                    }.toMutableList()
                }
                viewModel.contactAccountAdapter.setData(newList.take(5).toMutableList())
            }

            edtAmount.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                }

                override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                }

                override fun afterTextChanged(p0: Editable?) {
                    if (!TextUtils.isEmpty(p0)) {
                        val formattedText = Utils.g().getDotMoney(p0.toString())
                        edtAmount.removeTextChangedListener(this)
                        if (edtAmount.text.toString().replace(",", "").length >= 16) {
                            edtAmount.setText(
                                Utils.g()
                                    .getDotMoney(formattedText.replace(",", "").substring(0, 16)),
                            )
                            edtAmount.setSelection(edtAmount.text.toString().length)
                        } else {
                            edtAmount.setText(formattedText)
                            tvAmountToWord.text =
                                viewModel.readAmountInWord(p0.toString().replace(",", ""))
                            edtAmount.setSelection(formattedText.length)
                        }

                        edtAmount.addTextChangedListener(this)
                    } else {
                        tvAmountToWord.text = ""
                    }
                }
            })
            btnCreateMaker.setThrottleClickListener {
                val errorMessage = viewModel.validateTransferFields(
                    edtReceiveAccount.text?.toString(),
                    edtAmount.text?.toString(),
                    edtRemark.text?.toString(),
                    edtAccountOwner.text?.toString(),
                )

                if (errorMessage != null) {
                    showNoticeDialog(errorMessage)
                    return@setThrottleClickListener
                }
                viewModel.validateNapasAccountTransfer(
                    amount = edtAmount.text.toString().replace(",", ""),
                    remark = edtRemark.text.toString(),
                    toAcctNo = edtReceiveAccount.text.toString(),
                    edtAccountOwner = edtAccountOwner.text.toString(),
                    processDate = tvDaySchedule.text.toString(),
                )
            }
            btnOpenContact.setThrottleClickListener {
                viewModel.viewPagerContactAndSampleBottomSheet.show(
                    childFragmentManager,
                    "chooseContactAndSample",
                )
            }
            swCreateContact.setOnCheckedChangeListener { _, isChecked ->
                viewModel.isSaveAccount = isChecked
                edtCustomerCode.setText("")
                if (isChecked) {
                    edtCustomerCode.visibility = View.VISIBLE
                    btnSaveContact.visibility = View.VISIBLE
                } else {
                    edtCustomerCode.visibility = View.GONE
                    btnSaveContact.visibility = View.GONE
                }
            }
            btnSaveContact.setThrottleClickListener {
                val errorMessage = viewModel.validateCreateContact(
                    edtReceiveAccount = edtReceiveAccount.text.toString(),
                    edtCustomerCode = edtCustomerCode.text.toString(),
                )

                if (errorMessage != null) {
                    showNoticeDialog(errorMessage)
                    return@setThrottleClickListener
                }
                viewModel.contactAccountCreate(edtReceiveAccount = edtReceiveAccount.text.toString(), edtCustomerCode = edtCustomerCode.text.toString())
            }
            clNextApprover.setThrottleClickListener {
                val errorMessage = viewModel.validateNextStatusTransactionByRuleFields(
                    amount = edtAmount.text.toString().replace(",", ""),
                    toAccountNo = edtReceiveAccount.text.toString(),
                )

                if (errorMessage != null) {
                    showNoticeDialog(errorMessage)
                    return@setThrottleClickListener
                }
                viewModel.nextStatusTransactionByRule(
                    amount = edtAmount.text.toString().replace(",", ""),
                    toAccountNo = edtReceiveAccount.text.toString(),
                )
            }
            nextApproverBottomSheetAccFragment.setOnClickItemListener { it ->
                viewModel.listAccountApprovers = it
                contentNextApprover.text = viewModel.getNextApproversListString()
            }
            splitTransferBottomSheetFragment.setOnClickItemListener { isSplit ->
                if (isSplit) {
                    viewModel.validateNapasAccountTransfer(
                        amount = edtAmount.text.toString().replace(",", ""),
                        remark = edtRemark.text.toString(),
                        toAcctNo = edtReceiveAccount.text.toString(),
                        edtAccountOwner = edtAccountOwner.text.toString(),
                        processDate = tvDaySchedule.text.toString(),
                        confirm = "Y",
                    )
                } else {
//                    viewModel.validateNapasAccountTransferDomain?.let { makeTransferNomal(it) }
                    makeTransferTypeNormal()
                    if (viewModel.validateNapasAccountDomain != null) {
                        edtAccountOwner.setText(
                            viewModel.validateNapasAccountDomain?.accountOwner ?: "",
                        )
                    }
                }
            }
            // scan qr code
            btnScanQr.setThrottleClickListener {
                // yeu cau quyen truy cap camera
                requestAccessPermission(Manifest.permission.CAMERA)
            }

            requestAccessPermissionListener { allowCamera ->
                if (allowCamera) {
                    appNavigator.goToMakerQRTransferFragment()
                }
            }
        }
    }

    private fun FragmentMakerCreateTransferAccountBinding.clearListApprover() {
        viewModel.clearListApprover()
        nextApproverBottomSheetAccFragment.setData(mutableListOf())
        contentNextApprover.text = "Theo đăng ký với VietinBank"
    }

    private fun FragmentMakerCreateTransferAccountBinding.clearAll() {
        clBankDefault.visibility = View.VISIBLE
        clBank.visibility = View.GONE
        edtReceiveAccount.text?.clear()
        tvReceiveAccount.text = ""
        edtAccountOwner.text.clear()
        edtAccountOwner.visibility = View.GONE
        clFromAccount.visibility = View.GONE
        edtAmount.text?.clear()
        edtRemark.text?.clear()
        viewModel.setDefaultCcurrentTypeAccountSchedule()
        tvTimeSchedule.text = viewModel.currentTypeAccountSchedule?.name ?: ""
        clDaySchedule.visibility = View.GONE
        viewModel.chooseTypeFeeBEN()
        tvTypeFee.text = viewModel.currentTypeTransfer?.name ?: ""
        clearListApprover()
        viewModel.currentDataBankDomain = null
        viewModel.currentAccountDefaultDomain = null
        viewModel.setDataContactAccAdapter(
            viewModel.listContact.filter { it.trantype != Tags.TransferType.TYPE_NAPAS_CARD && it.trantype != Tags.TransferType.TYPE_PAYMENT_ORDER_TRANSFER }
                .toMutableList(),
        )
    }

    private fun setTextAccountInfo(it: AccountDefaultDomain?) {
        (binding as FragmentMakerCreateTransferAccountBinding).apply {
//            edtAmount.setText("")
            tvAccountNumber.text = it?.accountNo ?: ""
            tvCurrentBalnce.text =
                Utils.g().getDotMoneyHasCcy((it?.currentBalance ?: "0"), it?.currency ?: "")
            tvCurrentcyAmount.text = it?.currency.toString()
            viewModel.setCcy(it?.currency ?: "")
        }
    }

    private fun setTextBankInfo(currentBank: DataBankDomain) {
        (binding as FragmentMakerCreateTransferAccountBinding).apply {
            viewModel.setDataTypeScheduleTime(chooseTimeScheduleTypeDialog)
            currentBank.let {
                clDaySchedule.visibility = View.GONE
                tvDaySchedule.text = ""
                tvDaySchedule.text = ""
                edtAmount.text?.clear()
                edtRemark.text?.clear()
                viewModel.setDefaultCcurrentTypeAccountSchedule()
                tvTimeSchedule.text = viewModel.currentTypeAccountSchedule?.name ?: ""
                viewModel.chooseTypeFeeBEN()
                tvTypeFee.text = Tags.TransferType.dataSetFillDataOUR.name
                clBankDefault.visibility = View.GONE
                clBank.visibility = View.VISIBLE
                viewModel.currentDataBankDomain = currentBank
                viewModel.contactList()
                edtReceiveAccount.text?.clear()
                edtAccountOwner.text?.clear()
                tvReceiveAccount.text = ""
                tvRecieveBank.text = (viewModel.currentDataBankDomain?.shortName ?: "")
                tvRecieveBankName.text = (viewModel.currentDataBankDomain?.bankName ?: "")
                tvType.text = BankAdapter.getTypeName(
                    viewModel.currentDataBankDomain?.type ?: "",
                )

                clearListApprover()
                if (viewModel.isTransferIn()) {
                    setMaxLengthEdtRemark(146)
                    requireContext().let {
                        titleTypeFee.isEnabled = true
                        tvTypeFee.isEnabled = true
                        titleTimeSchedule.isEnabled = true
                        tvTimeSchedule.isEnabled = true
                        tvTypeFee.setTextColor(
                            ContextCompat.getColor(
                                it,
                                R.color.text_blue_02,
                            ),
                        )
                    }

                    tvReceiveAccount.visibility = View.GONE
                    tvReceiveAccountTitle.visibility = View.GONE
                    edtAccountOwner.visibility = View.GONE
                } else if (viewModel.isTransferNapas()) {
                    setMaxLengthEdtRemark(210)
                    titleTypeFee.isEnabled = false
                    tvTypeFee.isEnabled = false
                    titleTimeSchedule.isEnabled = false
                    tvTimeSchedule.isEnabled = false
                    requireContext().let {
                        tvTypeFee.setTextColor(
                            ContextCompat.getColor(
                                it,
                                R.color.text_blue_02,
                            ),
                        )
                    }
                    tvReceiveAccount.visibility = View.GONE
                    tvReceiveAccountTitle.visibility = View.GONE
                    edtAccountOwner.visibility = View.GONE
                } else {
                    setMaxLengthEdtRemark(210)
                    titleTypeFee.isEnabled = true
                    tvTypeFee.isEnabled = true
                    titleTimeSchedule.isEnabled = true
                    tvTimeSchedule.isEnabled = true
                    tvReceiveAccount.visibility = View.GONE
                    tvReceiveAccountTitle.visibility = View.GONE
                    edtAccountOwner.visibility = View.VISIBLE
                }
                imgIconBank.loadUrl(viewModel.currentDataBankDomain?.icon ?: "")
            }
        }
    }

    private fun FragmentMakerCreateTransferAccountBinding.setMaxLengthEdtRemark(maxLength: Int) {
        edtRemark.filters = arrayOf(asciiOnlyFilter(), InputFilter.LengthFilter(maxLength))
        edtRemark.onSearchTextChanged { s ->
            tvRemarkCount.text = s.length.toString() + "/210"
        }
    }

    private fun onItemContactClicked(contactDomains: ContactDomains) {
        (binding as FragmentMakerCreateTransferAccountBinding).apply {
            contactDomains.let {
                resetFilterTransfer()
                viewModel.findMatchingDataBankDomain(
                    bank = contactDomains.bank,
                    trantype = contactDomains.trantype,
                )?.let { setTextBankInfo(it) }
                edtReceiveAccount.setText(contactDomains.account.toString())
            }
            viewModel.validateNapasAccount(contactDomains.account ?: "")
        }
    }

    // khi chuyen tien bang qr -> không cho phep sua so tien + noi dung + so tien khoan
    private fun enableFilterTransfer(isEnable: Boolean = true) {
        (binding as FragmentMakerCreateTransferAccountBinding).apply {
            edtAmount.isEnabled = isEnable
            edtRemark.isEnabled = isEnable
            edtReceiveAccount.isEnabled = isEnable
        }
    }

    private fun resetFilterTransfer() {
        (binding as FragmentMakerCreateTransferAccountBinding).apply {
            viewModel.napasTransferParams = null
            edtAmount.isEnabled = true
            edtAmount.setText("")
            edtRemark.isEnabled = true
            edtRemark.setText("")
            edtReceiveAccount.isEnabled = true
            edtReceiveAccount.setText("")
        }
    }
}