package com.vietinbank.feture_maker.maker_ui.bottomSheet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_domain.models.maker.DataBankDomain
import com.vietinbank.core_ui.base.BaseBottomSheetFragment
import com.vietinbank.feature_maker.databinding.FragmentBankListBinding
import com.vietinbank.feture_maker.maker_ui.adapter.BankAdapter
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class BankListBottomSheetFragment : BaseBottomSheetFragment() {
    private var _binding: FragmentBankListBinding? = null
    private val binding get() = _binding!!
    private val bankAdapter = BankAdapter()
    private var onItemListener: ((DataBankDomain) -> Unit)? = null
    private var dataSet: MutableList<DataBankDomain> = mutableListOf()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        _binding = FragmentBankListBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun initView() {
        binding.rcvBank.adapter = bankAdapter
    }

    fun setData(bankList: MutableList<DataBankDomain>) {
        dataSet = bankList
        bankAdapter.setData(bankList)
    }

    override fun initData() {
        searchText()
    }

    private fun searchText() {
        binding.etSearch.searchText(0) { s ->
            val newList = if (s.isEmpty()) {
                dataSet.toList()
            } else {
                dataSet.filter {
                    (it.bankName ?: "").lowercase().contains(
                        s.lowercase(),
                    ) || (it.name ?: "").lowercase().contains(
                        s.lowercase(),
                    ) || (it.shortName ?: "").lowercase().contains(
                        s.lowercase(),
                    )
                }
            }
            if (newList.isEmpty()) {
//                tạo 1 view khi null list
            } else {
            }
            bankAdapter.setData(newList as MutableList<DataBankDomain>)
        }
    }

    override fun initListener() {
        bankAdapter.onClickItem = {
            onItemListener?.invoke(it)
            dismiss()
        }
        binding.ivBack.setThrottleClickListener {
            dismiss()
        }
    }

    fun setOnClickItemListener(action: ((DataBankDomain) -> Unit)?) {
        onItemListener = action
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        onItemListener = null
    }
}