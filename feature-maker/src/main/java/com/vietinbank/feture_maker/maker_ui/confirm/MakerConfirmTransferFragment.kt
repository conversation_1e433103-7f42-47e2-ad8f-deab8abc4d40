package com.vietinbank.feture_maker.maker_ui.confirm

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.viewbinding.ViewBinding
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.models.TransferObject
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountTransferDomain
import com.vietinbank.core_domain.models.maker.ValidateNapasCardTransferDomains
import com.vietinbank.core_domain.models.maker.ValidatePaymentOrderTransferDomains
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.base.adapter.TransferAdapter
import com.vietinbank.feature_maker.databinding.FragmentMakerConfirmTransferBinding
import com.vietinbank.feture_maker.maker_ui.viewmodel.ResultViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class MakerConfirmTransferFragment : BaseFragment<ResultViewModel>() {
    override val viewModel: ResultViewModel by viewModels()
    override val useCompose: Boolean = false
    private val transferAdapter = TransferAdapter()
    private var jsonConfirmObject = ""
    private var jsonResultObject = ""

    @Inject
    override lateinit var appNavigator: IAppNavigator
    override fun inflateViewBinding(inflater: LayoutInflater, container: ViewGroup?): ViewBinding {
        return FragmentMakerConfirmTransferBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.typeTransfer =
            arguments?.getString(Tags.TransferType.TYPE_TRANSFER, "").toString()

        when (viewModel.typeTransfer) {
            Tags.TransferType.TYPE_ACCOUNT, Tags.TransferType.TYPE_ACCOUNT_SPLIT -> {
                viewModel.validateNapasAccountTransferDomain = Utils.g().provideGson().fromJson(
                    arguments?.getString(
                        Tags.TransferType.CONFIRM_OBJECT, "",
                    ),
                    ValidateNapasAccountTransferDomain::class.java,
                )
                viewModel.tranType = viewModel.validateNapasAccountTransferDomain?.trxType ?: ""
                jsonResultObject = Gson().toJson(
                    viewModel.resultTransferAccountObjects(viewModel.validateNapasAccountTransferDomain),
                )
            }

            Tags.TransferType.TYPE_CARD -> {
                viewModel.validateNapasCardTransferDomains = Utils.g().provideGson().fromJson(
                    arguments?.getString(
                        Tags.TransferType.CONFIRM_OBJECT, "",
                    ),
                    ValidateNapasCardTransferDomains::class.java,
                )
                viewModel.tranType = Tags.TransferType.TYPE_NAPAS_CARD
                jsonResultObject = Gson().toJson(
                    viewModel.resultTransferCardObjects(viewModel.validateNapasCardTransferDomains),
                )
            }

            Tags.TransferType.TYPE_PAYMENT_ORDER -> {
                viewModel.validatePaymentOrderTransferDomains = Utils.g().provideGson().fromJson(
                    arguments?.getString(
                        Tags.TransferType.CONFIRM_OBJECT, "",
                    ),
                    ValidatePaymentOrderTransferDomains::class.java,
                )
                viewModel.tranType = Tags.TransferType.TYPE_PAYMENT_ORDER_TRANSFER
                jsonResultObject = Gson().toJson(
                    viewModel.resultPaymentOrderTransferObjects(viewModel.validatePaymentOrderTransferDomains),
                )
            }

            else -> Unit
        }

        viewModel.createNapasAccountTransferState.observe(viewLifecycleOwner) { state ->
            val createNapasAccountTransferObject = Gson().toJson(state)
            val bundle = Bundle().apply {
                putString(
                    Tags.TransferType.TYPE_TRANSFER,
                    viewModel.typeTransfer,
                )
                putBoolean(
                    Tags.TransferType.IS_CONTACT_CREATE,
                    arguments?.getBoolean(
                        Tags.TransferType.IS_CONTACT_CREATE, false,
                    ) == false,

                )
                putString(
                    Tags.TransferType.CONFIRM_OBJECT,
                    arguments?.getString(
                        Tags.TransferType.CONFIRM_OBJECT,
                        "",
                    ),
                )
                putString(
                    Tags.TransferType.CREATE_TRANSFER_OBJECT,
                    createNapasAccountTransferObject,
                )
                putString(
                    Tags.TransferType.LIST_RESULT_OBJECT,
                    jsonResultObject,
                )
            }
            appNavigator.goToMakerResultTransferFragment(bundle)
        }
        setDataListConfirm()
        initListener()
    }

    private fun setDataListConfirm() {
        jsonConfirmObject =
            arguments?.getString(Tags.TransferType.LIST_CONFIRM_OBJECT, "")
                .toString()
        val transferObjectList: List<TransferObject> = jsonConfirmObject.let {
            val type = object : TypeToken<List<TransferObject>>() {}.type
            Gson().fromJson(it, type)
        } ?: emptyList()
        (binding as FragmentMakerConfirmTransferBinding).apply {
            rcvInfo.adapter = transferAdapter
        }
        transferAdapter.setData(transferObjectList as MutableList<TransferObject>)
    }

    private fun initListener() {
        (binding as FragmentMakerConfirmTransferBinding).apply {
            openCustomToolbar.tvTitleToolbar.text = "Xác nhận giao dịch"
            btnCreateMaker.setThrottleClickListener {
                viewModel.createNapasAccountTransfer()
            }

            openCustomToolbar.btnBack.setThrottleClickListener {
                appNavigator.popBackStack()
            }
        }
    }
}