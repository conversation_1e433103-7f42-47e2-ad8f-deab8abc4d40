package com.vietinbank.feture_maker.maker_ui.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.extensions.loadDrawable
import com.vietinbank.core_common.extensions.loadUrl
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.maker.TempTransactionDomains
import com.vietinbank.feature_maker.R
import com.vietinbank.feature_maker.databinding.ItemShampleTypeBinding

class ShampleTypeAdapter : RecyclerView.Adapter<ShampleTypeAdapter.ViewHolder>() {
    private var dataSet: MutableList<TempTransactionDomains> = mutableListOf()
    var context: Context? = null

    @SuppressLint("NotifyDataSetChanged")
    var onClickItem: ((TempTransactionDomains) -> Unit)? = null

    @SuppressLint("NotifyDataSetChanged")
    fun setData(data: MutableList<TempTransactionDomains>) {
        dataSet.clear()
        dataSet.addAll(data)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): ShampleTypeAdapter.ViewHolder {
        val binding =
            ItemShampleTypeBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false,
            )
        return ViewHolder(binding)
    }

    inner class ViewHolder(var binding: ItemShampleTypeBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun getItemCount(): Int = dataSet.size

    override fun onBindViewHolder(holder: ShampleTypeAdapter.ViewHolder, position: Int) {
        val item = dataSet[position]
        with(holder.binding) {
            root.setThrottleClickListener {
                onClickItem?.invoke(item)
            }
            tvAccountName.text = item.toAccountName ?: ""
            tvRemark.text = item.content ?: ""
            tvAmount.text = Utils.g().getDotMoneyHasCcy(item.amount ?: "", item.currency ?: "")
            if (Tags.TransferType.TYPE_IN == item.tranType) {
                imgIcon.loadDrawable(R.drawable.ic_vietinbank)
            } else {
                imgIcon.loadUrl(item.icon ?: "")
            }
        }
    }
}