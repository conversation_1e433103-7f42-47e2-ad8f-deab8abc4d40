package com.vietinbank.feture_maker.maker_ui.scan

import androidx.annotation.OptIn
import androidx.camera.core.Camera
import androidx.camera.core.CameraSelector
import androidx.camera.core.ExperimentalGetImage
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.google.mlkit.vision.barcode.BarcodeScanning
import com.google.mlkit.vision.common.InputImage
import com.vietinbank.core_common.models.AppBarAction
import com.vietinbank.core_domain.models.maker.ScanQrAction
import com.vietinbank.core_ui.base.compose.BaseAppBar
import com.vietinbank.core_ui.base.compose.QrCodeOverlayView
import com.vietinbank.feture_maker.maker_ui.viewmodel.MakeTransferViewModel
import java.util.concurrent.Executors

@OptIn(ExperimentalGetImage::class)
@Composable
fun MakerQRScanScreen(
    viewModel: MakeTransferViewModel,
    actions: ScanQrAction?,
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val previewView = remember { PreviewView(context) }
    var cameraManager by remember { mutableStateOf<Camera?>(null) }
    var isFlashOn by remember { mutableStateOf(false) }
    LaunchedEffect(Unit) {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(context)
        val cameraProvider = cameraProviderFuture.get()
        val preview = Preview.Builder().build().apply {
            setSurfaceProvider(previewView.surfaceProvider)
        }
        val backgroundExecutor = Executors.newSingleThreadExecutor()
        val barcodeScanner = BarcodeScanning.getClient()
        var lastAnalyzedTimestamp = 0L
        val imageAnalysis = ImageAnalysis.Builder()
            .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST).build()
        imageAnalysis.setAnalyzer(backgroundExecutor) { imageProxy ->
            val currentTimestamp = System.currentTimeMillis()
            // Chỉ cho quét ~10 ảnh/giây
            if (currentTimestamp - lastAnalyzedTimestamp < 100) {
                imageProxy.close()
                return@setAnalyzer
            }
            lastAnalyzedTimestamp = currentTimestamp
            val mediaImage = imageProxy.image
            if (mediaImage != null) {
                val image = InputImage.fromMediaImage(
                    mediaImage,
                    imageProxy.imageInfo.rotationDegrees,
                )
                barcodeScanner.process(image).addOnSuccessListener { barcodes ->
                    for (barcode in barcodes) {
                        barcode.rawValue?.let { value ->
                            viewModel.submitQrResult(value)
                        }
                    }
                }.addOnFailureListener {
                    viewModel.resetScanning()
                    actions?.onError(it)
                }.addOnCompleteListener {
                    imageProxy.close()
                }
            } else {
                imageProxy.close()
            }
        }
        val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA
        try {
            cameraProvider.unbindAll()
            cameraManager = cameraProvider.bindToLifecycle(
                lifecycleOwner,
                cameraSelector,
                preview,
                imageAnalysis,
            )
        } catch (_: Exception) {
        }
    }

    Box(modifier = Modifier.fillMaxSize()) {
        val appBarActions = mutableListOf<AppBarAction>()
        if (true == cameraManager?.cameraInfo?.hasFlashUnit()) {
            appBarActions.add(
                AppBarAction(
                    icon = if (isFlashOn) {
                        com.vietinbank.core_ui.R.drawable.ic_flash_off
                    } else {
                        com.vietinbank.core_ui.R.drawable.ic_flash_on
                    },
                    contentDescription = "Flash",
                    onClick = {
                        isFlashOn = !isFlashOn
                        cameraManager?.cameraControl?.enableTorch(isFlashOn)
                    },
                ),
            )
        }

        AndroidView(modifier = Modifier.fillMaxSize(), factory = { previewView })
        QrCodeOverlayView(onPickPicture = actions?.onPictureClick)
        // AppBar
        BaseAppBar(
            title = "Quét QR",
            onBackClick = actions?.onBackClick ?: {},
            actions = appBarActions,
        )
    }
}