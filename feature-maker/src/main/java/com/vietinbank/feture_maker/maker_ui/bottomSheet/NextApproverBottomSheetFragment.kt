package com.vietinbank.feture_maker.maker_ui.bottomSheet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.extensions.loadDrawable
import com.vietinbank.core_domain.models.maker.ApproverDomains
import com.vietinbank.core_ui.base.BaseBottomSheetFragment
import com.vietinbank.feature_maker.R
import com.vietinbank.feature_maker.databinding.FragmentAccountListBinding
import com.vietinbank.feture_maker.maker_ui.adapter.NextApproverAdapter
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class NextApproverBottomSheetFragment : BaseBottomSheetFragment() {
    private var _binding: FragmentAccountListBinding? = null
    private val binding get() = _binding!!
    private val adapter = NextApproverAdapter()
    private var onItemListener: ((List<ApproverDomains>) -> Unit)? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        _binding = FragmentAccountListBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun initView() {
        binding.rcvAccount.adapter = adapter
        binding.tvType.text = "Chọn người phê duyệt tiếp theo"
        binding.ivBack.loadDrawable(R.drawable.ic_check)
    }

    fun setData(accountDefault: MutableList<ApproverDomains>) {
        adapter.setData(accountDefault)
    }

    override fun initData() {
    }

    override fun initListener() {
        adapter.onClickItem = {
            if (adapter.getSelectedItems().isNotEmpty()) {
                binding.ivBack.loadDrawable(R.drawable.ic_check)
            } else {
                binding.ivBack.loadDrawable(com.vietinbank.core_ui.R.drawable.ic_close)
            }
        }
        binding.ivBack.setThrottleClickListener {
            onItemListener?.invoke(adapter.getSelectedItems())
            dismiss()
        }
    }

    fun setOnClickItemListener(action: ((List<ApproverDomains>) -> Unit)?) {
        onItemListener = action
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        onItemListener = null
    }
}