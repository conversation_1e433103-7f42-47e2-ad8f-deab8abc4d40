package com.vietinbank.feture_maker.maker_ui.contact_sample_transfer

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.viewbinding.ViewBinding
import com.google.android.flexbox.AlignItems
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexboxLayoutManager
import com.google.android.flexbox.JustifyContent
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.onSearchTextChanged
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_domain.models.maker.ContactDomains
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_maker.databinding.FragmentContactBinding
import com.vietinbank.feture_maker.maker_ui.adapter.ContactTypeAdapter
import com.vietinbank.feture_maker.maker_ui.viewmodel.MakeTransferViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class ContactFragment : BaseFragment<MakeTransferViewModel>() {
    override val viewModel: MakeTransferViewModel by activityViewModels()
    override val useCompose: Boolean = false

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override fun inflateViewBinding(inflater: LayoutInflater, container: ViewGroup?): ViewBinding {
        return FragmentContactBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initData()
        initView()
        initListener()
    }

    private fun initData() {
        (binding as FragmentContactBinding).apply {
            etSearch.setText("")
        }
    }

    private fun setDataContatAdapter(state: MutableList<ContactDomains>) {
        viewModel.contactAdapter.setData(state)
        viewModel.listContactFilter = state
    }

    private fun initView() {
        val flexboxLayoutManager = FlexboxLayoutManager(context)
        flexboxLayoutManager.flexDirection = FlexDirection.ROW
        flexboxLayoutManager.justifyContent = JustifyContent.FLEX_START
        flexboxLayoutManager.alignItems = AlignItems.STRETCH
        (binding as FragmentContactBinding).apply {
            rcvContact.adapter = viewModel.contactAdapter
            rcvContactType.layoutManager = flexboxLayoutManager
            viewModel.contactTypeAdapter = ContactTypeAdapter()
            rcvContactType.adapter = viewModel.contactTypeAdapter

            if (viewModel.isTransferAccountTab) {
                val filteredContact =
                    viewModel.listContact.filter { it.trantype == Tags.TransferType.TYPE_IN }
                val clonedContacts = filteredContact.map { it.copy() }
                setDataContatAdapter(clonedContacts as MutableList<ContactDomains>)
            } else {
                val filteredContact =
                    viewModel.listContact.filter { it.trantype == Tags.TransferType.contactTypeNPC.id }
                val clonedContacts = filteredContact.map { it.copy() }
                setDataContatAdapter(clonedContacts as MutableList<ContactDomains>)
            }
        }
        viewModel.setEnableContactType()
        viewModel.contactTypeAdapter.setData(Tags.TransferType.listTypeContact)
    }

    private fun initListener() {
        (binding as FragmentContactBinding).apply {
            viewModel.contactAdapter.onClickItem = {
                if (viewModel.isTransferAccountTab) {
                    viewModel.clickItemBankFromContactFragment(it)
                } else {
                    viewModel.clickItemCardFromContactFragment(it)
                }
            }
            viewModel.contactTypeAdapter.onClickItem = { contactTypeItem ->
                etSearch.setText("")
                val filteredContact =
                    viewModel.listContact.filter { it.trantype == contactTypeItem.id }
                val clonedContacts = filteredContact.map { it.copy() }
                setDataContatAdapter(clonedContacts as MutableList<ContactDomains>)
            }
            etSearch.onSearchTextChanged { s ->
                val newList = if (s.isEmpty()) {
                    viewModel.listContactFilter
                } else {
                    viewModel.listContactFilter.filter {
                        (it.account ?: "").lowercase().contains(
                            s.lowercase(),
                        ) || (it.placetoreceive ?: "").lowercase().contains(
                            s.lowercase(),
                        ) || (it.payeename ?: "").lowercase().contains(
                            s.lowercase(),
                        )
                    }.toMutableList()
                }
                viewModel.contactAdapter.setData(newList)
            }
        }
    }
}