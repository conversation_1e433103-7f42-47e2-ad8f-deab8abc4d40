package com.vietinbank.feture_maker.maker_ui.adapter

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.vietinbank.feture_maker.maker_ui.contact_sample_transfer.ContactFragment
import com.vietinbank.feture_maker.maker_ui.contact_sample_transfer.ShampleFragment

class ViewPagerContactAndSampleAdapter(
    fragmentManager: FragmentManager,
    viewLifecycleOwner: Lifecycle,
) :
    FragmentStateAdapter(fragmentManager, viewLifecycleOwner) {
    override fun createFragment(position: Int): Fragment {
        return when (position) {
            0 -> ContactFragment()
            1 -> ShampleFragment()
            else -> throw IllegalStateException("Invalid position")
        }
    }

    // Return the total number of pages
    override fun getItemCount(): Int = 2
}