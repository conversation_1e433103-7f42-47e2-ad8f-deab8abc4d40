package com.vietinbank.feture_maker.maker_ui.viewmodel

import android.content.Context
import android.os.CountDownTimer
import android.text.TextUtils
import androidx.appcompat.widget.AppCompatButton
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.extensions.dd_MM_yyyy_HH_mm_ss
import com.vietinbank.core_common.extensions.todayAsString
import com.vietinbank.core_common.livedata.SingleLiveEvent
import com.vietinbank.core_common.models.TransferObject
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.csat.CSatConfigDomain
import com.vietinbank.core_domain.models.csat.CSatConfigParams
import com.vietinbank.core_domain.models.csat.CSatRateDomain
import com.vietinbank.core_domain.models.csat.CSatRateParams
import com.vietinbank.core_domain.models.maker.ContactCreateDomains
import com.vietinbank.core_domain.models.maker.ContactCreateParams
import com.vietinbank.core_domain.models.maker.CreateTemplateDomains
import com.vietinbank.core_domain.models.maker.CreateTemplateParams
import com.vietinbank.core_domain.models.maker.CreateTransferDomain
import com.vietinbank.core_domain.models.maker.CreateTransferParams
import com.vietinbank.core_domain.models.maker.TempTransactionParams
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountTransferDomain
import com.vietinbank.core_domain.models.maker.ValidateNapasCardTransferDomains
import com.vietinbank.core_domain.models.maker.ValidatePaymentOrderTransferDomains
import com.vietinbank.core_domain.repository.cache.ITransferCacheManager
import com.vietinbank.core_domain.usecase.checker.CheckerUserCase
import com.vietinbank.core_domain.usecase.transfer.CSatUseCase
import com.vietinbank.core_domain.usecase.transfer.TransferUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.adapter.TransferAdapter
import com.vietinbank.core_ui.base.dialog.TooltipCSAT
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class ResultViewModel @Inject constructor(
    private val transferUseCase: TransferUseCase,
    private val checkerUserCase: CheckerUserCase,
    private val cSatUseCase: CSatUseCase,
    private val moneyHelper: MoneyHelper,
    override val resourceProvider: IResourceProvider,
    val transferCacheManager: ITransferCacheManager,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
) : BaseViewModel() {
    var typeTransfer = ""
    var tranType = ""
    var validateNapasAccountTransferDomain: ValidateNapasAccountTransferDomain? = null
    var validateNapasCardTransferDomains: ValidateNapasCardTransferDomains? = null
    var validatePaymentOrderTransferDomains: ValidatePaymentOrderTransferDomains? = null

    private val _createNapasAccountTransferState = SingleLiveEvent<CreateTransferDomain>()
    val createNapasAccountTransferState: SingleLiveEvent<CreateTransferDomain> get() = _createNapasAccountTransferState

    private val _createTemplateState = SingleLiveEvent<Resource<CreateTemplateDomains>>()
    val createTemplateState: SingleLiveEvent<Resource<CreateTemplateDomains>> get() = _createTemplateState

    private val _handleErrorCreateTemplateState = SingleLiveEvent<AppException>()
    val handleErrorCreateTemplateState: SingleLiveEvent<AppException> get() = _handleErrorCreateTemplateState

    private val _contactCreateState = SingleLiveEvent<Resource<ContactCreateDomains>>()
    val contactCreateState: SingleLiveEvent<Resource<ContactCreateDomains>> get() = _contactCreateState

    fun createNapasAccountTransfer() {
        val params = CreateTransferParams(
            username = userProf.getUserName() ?: "",
            tranType = tranType,
            transferType = typeTransfer,
        )
        launchJob(showLoading = true) {
            val res = transferUseCase.createNapasAccountTransfer(params)
            handleResource(res) { data ->
                _createNapasAccountTransferState.postValue(data)
            }
        }
    }

    fun resultTransferAccountObjects(validateNapasAccountTransferDomain: ValidateNapasAccountTransferDomain?): MutableList<TransferObject> {
        val data = validateNapasAccountTransferDomain
        val transferObjects = mutableListOf<TransferObject>()
        if (data != null) {
            transferObjects.addAll(
                mutableListOf(
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "Tài khoản chuyển",
                        data.fromAcctNo + "\n" + (data.username ?: ""),
                    ),
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "Tài khoản hưởng",
                        data.toAcctNo + "\n" + data.toAcctName,
                    ),
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "Ngân hàng",
                        data.toBankName ?: "",
                    ),
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "Phí giao dịch",
                        Utils.g().getDotMoneyHasCcy(data.feeVat ?: "", data.currency ?: ""),
                    ),
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "Hình thức thu phí",
                        if (data.feePayMethod == "0") Tags.TransferType.dataSetFillDataBEN.name else Tags.TransferType.dataSetFillDataOUR.name,
                    ),
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "Nội dung",
                        data.remark.toString(),
                    ),
                ),
            )
            if (Tags.TransferType.TYPE_SPLIT_TRANSFER_YES == data.isSplit) {
                transferObjects.add(
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        title = (data.subTransactionCount ?: "") + " giao dịch",
                        content = Utils.g().getDotMoneyHasCcy(
                            data.subSplitTransAmount ?: "",
                            data.currency ?: "",
                        ) + "\n" + moneyHelper.convertAmountToWords(
                            data.subSplitTransAmount ?: "",
                            data.currency ?: "",
                        ),
                    ),
                )
                transferObjects.add(
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        title = (data.subTransactionsRemainderCount ?: "") + " giao dịch",
                        content = Utils.g().getDotMoneyHasCcy(
                            data.subTransactionsRemainderAmount ?: "",
                            data.currency ?: "",
                        ) + "\n" + moneyHelper.convertAmountToWords(
                            data.subTransactionsRemainderAmount ?: "", data.currency ?: "",
                        ),
                    ),
                )
                try {
                    val totalVAT = (data.feeSplitNonVat ?: "").toBigDecimal()
                        .add((data.feeSplitVat ?: "").toBigDecimal())
                    transferObjects.add(
                        TransferObject(
                            TransferAdapter.TYPE_GROUP_CONTENT,
                            title = "Phí tách lệnh",
                            content = Utils.g().getDotMoney(totalVAT.toString()),
                        ),
                    )
                } catch (_: Exception) {
                }

                transferObjects.add(
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        title = "Hình thức thu phí",
                        content = if ("F" != data.feeSplitType) "Phí trong" else "Phí Ngoài",
                    ),
                )
            }

            if (TextUtils.isEmpty(data.processDate)) {
                transferObjects.add(
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "Thời gian chuyển",
                        todayAsString(dd_MM_yyyy_HH_mm_ss),
                    ),
                )
            } else {
                transferObjects.add(
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "Thời gian chuyển",
                        "Đặt lịch",
                    ),
                )
                transferObjects.add(
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "Ngày đặt lịch",
                        data.processDate.toString(),
                    ),
                )
            }
            if (data.nextApprovers?.isNotEmpty() == true) {
                val groupedApprovers = data.nextApprovers?.groupBy { it.approverlevel }
                val sortedLevels = groupedApprovers?.keys?.sortedBy { it }
                if (sortedLevels != null) {
                    for (level in sortedLevels) {
                        val approvers = groupedApprovers[level] ?: continue
                        val approverNames = approvers.map { it.username }
                        val approversString = approverNames.joinToString("\n")
                        transferObjects.add(
                            TransferObject(
                                TransferAdapter.TYPE_GROUP_CONTENT,
                                title = "Người phê duyệt cấp $level",
                                content = approversString,
                            ),
                        )
                    }
                }
            }
        }
        return transferObjects
    }

    fun resultTransferCardObjects(validateNapasCardTransferDomains: ValidateNapasCardTransferDomains?): MutableList<TransferObject> {
        val data = validateNapasCardTransferDomains
        val transferObjects = mutableListOf<TransferObject>()

        if (data != null) {
            transferObjects.addAll(
                mutableListOf(
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "Tài khoản chuyển",
                        data.fromAcctNo + "\n" + (data.username ?: ""),
                    ),
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "Số thẻ hưởng",
                        data.toCardNo + "\n" + data.toCardName,
                    ),
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "Ngân hàng",
                        data.toBankName,
                    ),
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "Phí giao dịch",
                        Utils.g().getDotMoneyHasCcy(data.feeVat ?: "", data.currency ?: ""),
                    ),
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "Hình thức thu phí",
                        if (data.feePayMethod == "0") Tags.TransferType.dataSetFillDataBEN.name else Tags.TransferType.dataSetFillDataOUR.name,
                    ),
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "Nội dung",
                        data.remark.toString(),
                    ),
                ),
            )
            transferObjects.add(
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Thời gian chuyển",
                    todayAsString(dd_MM_yyyy_HH_mm_ss),
                ),
            )
            if (data.nextApprovers?.isNotEmpty() == true) {
                val groupedApprovers = data.nextApprovers?.groupBy { it.approverlevel }
                val sortedLevels = groupedApprovers?.keys?.sortedBy { it }
                if (sortedLevels != null) {
                    for (level in sortedLevels) {
                        val approvers = groupedApprovers[level] ?: continue
                        val approverNames = approvers.map { it.username }
                        val approversString = approverNames.joinToString("\n")
                        transferObjects.add(
                            TransferObject(
                                TransferAdapter.TYPE_GROUP_CONTENT,
                                title = "Người phê duyệt cấp $level",
                                content = approversString,
                            ),
                        )
                    }
                }
            }
        }
        return transferObjects
    }

    fun resultPaymentOrderTransferObjects(validatePaymentOrderTransferDomains: ValidatePaymentOrderTransferDomains?): MutableList<TransferObject> {
        val data = validatePaymentOrderTransferDomains
        val transferObjects = mutableListOf<TransferObject>()
        if (data != null) {
            transferObjects.addAll(
                mutableListOf(
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "Tài khoản chuyển",
                        data.fromAcctNo + "\n" + userProf.getFullName(),
                    ),
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "Tài khoản hưởng",
                        data.toAcctNo + "\n" + data.receiveName,
                    ),
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "Ngân hàng",
                        data.receiveBankName ?: "",
                    ),
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "Phí giao dịch",
                        Utils.g().getDotMoneyHasCcy(data.feeVat ?: "", data.currency ?: ""),
                    ),
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "Hình thức thu phí",
                        if (data.feePayMethod == "0") Tags.TransferType.dataSetFillDataBEN.name else Tags.TransferType.dataSetFillDataOUR.name,
                    ),
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "Nội dung",
                        data.remark.toString(),
                    ),
                ),
            )
            if (TextUtils.isEmpty(data.processDate)) {
                transferObjects.add(
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "Thời gian chuyển",
                        todayAsString(dd_MM_yyyy_HH_mm_ss),
                    ),
                )
            } else {
                transferObjects.add(
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "Thời gian chuyển",
                        "Đặt lịch",
                    ),
                )
                transferObjects.add(
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "Ngày đặt lịch",
                        data.processDate.toString(),
                    ),
                )
            }
        }
        return transferObjects
    }

    fun contactAccountCreate(validateNapasAccountTransferDomain: ValidateNapasAccountTransferDomain?) {
        val params = ContactCreateParams(
            accountNo = validateNapasAccountTransferDomain?.toAcctNo ?: "",
            confirm = "N",
            currency = validateNapasAccountTransferDomain?.currency ?: "",
            customercode = validateNapasAccountTransferDomain?.customercode ?: "",
            payeeName = validateNapasAccountTransferDomain?.toAcctName ?: "",
            receiveBankName = Utils.g()
                .removeAccent(validateNapasAccountTransferDomain?.toBankName ?: ""),
            revBankCode = validateNapasAccountTransferDomain?.binCode ?: "",
            serviceId = getServiceId(validateNapasAccountTransferDomain?.trxType ?: ""),
            username = userProf.getUserName() ?: "",
            tranType = validateNapasAccountTransferDomain?.trxType ?: "",
        )
        launchJob(showLoading = true) {
            val res = transferUseCase.contactCreate(params)
            handleResource(res) { data ->
                _contactCreateState.postValue(Resource.Success(data))
                transferCacheManager.clearContactList()
            }
        }
    }

    fun contactCardCreate(validateNapasCardTransferDomains: ValidateNapasCardTransferDomains?) {
        val params = ContactCreateParams(
            cardNo = validateNapasCardTransferDomains?.toCardNo ?: "",
            confirm = "N",
            currency = validateNapasCardTransferDomains?.currency ?: "",
            customercode = validateNapasCardTransferDomains?.customercode ?: "",
            payeeName = validateNapasCardTransferDomains?.toCardName ?: "",
            receiveBankName = Utils.g()
                .removeAccent(validateNapasCardTransferDomains?.toBankName ?: ""),
            revBankCode = "99998",
            serviceId = Tags.TransferType.TYPE_NAPAS_CARD,
            username = userProf.getUserName() ?: "",
            tranType = Tags.TransferType.TYPE_NAPAS_CARD,
        )
        launchJob(showLoading = true) {
            val res = transferUseCase.contactCreate(params)
            handleResource(res) { data ->
                _contactCreateState.postValue(Resource.Success(data))
            }
        }
    }

    fun createTemplateAccount(
        confirm: String = "N",
        validateNapasAccountTransferDomain: ValidateNapasAccountTransferDomain?,
    ) {
        val params = CreateTemplateParams(
            username = userProf.getUserName() ?: "",
            tempTransaction = TempTransactionParams(
                toAccountName = validateNapasAccountTransferDomain?.toAcctName ?: "",
                toBankName = validateNapasAccountTransferDomain?.toBankName ?: "",
                fromAccountNo = validateNapasAccountTransferDomain?.fromAcctNo ?: "",
                toAccountNo = validateNapasAccountTransferDomain?.toAcctNo ?: "",
                amount = validateNapasAccountTransferDomain?.amount ?: "",
                toBankCode = validateNapasAccountTransferDomain?.binCode ?: "",
                userId = userProf.getUserName() ?: "",
                currency = validateNapasAccountTransferDomain?.currency ?: "",
                content = validateNapasAccountTransferDomain?.remark ?: "",
                tranType = validateNapasAccountTransferDomain?.trxType ?: "",
                confirm = confirm,
            ),
        )
        launchJob(showLoading = true) {
            val res = transferUseCase.createTemplate(params)
            handleResource(res) { data ->
                _createTemplateState.postValue(Resource.Success(data))
            }
        }
    }

    fun createTemplateCard(
        confirm: String = "N",
        validateNapasCardTransferDomains: ValidateNapasCardTransferDomains?,
    ) {
        val params = CreateTemplateParams(
            username = userProf.getUserName() ?: "",
            tempTransaction = TempTransactionParams(
                toBankName = validateNapasCardTransferDomains?.toBankName ?: "",
                fromAccountNo = validateNapasCardTransferDomains?.fromAcctNo ?: "",
                amount = validateNapasCardTransferDomains?.amount ?: "",
                userId = userProf.getUserName() ?: "",
                currency = validateNapasCardTransferDomains?.currency ?: "",
                content = validateNapasCardTransferDomains?.remark ?: "",
                tranType = Tags.TransferType.SERVICE_TYPE_TRANSFER_NAPAS_CARD,
                toCardNo = validateNapasCardTransferDomains?.toCardNo ?: "",
                confirm = confirm,

            ),
        )
        launchJob(showLoading = true) {
            val res = transferUseCase.createTemplate(params)
            handleResource(res) { data ->
                _createTemplateState.postValue(Resource.Success(data))
            }
        }
    }

    private fun getServiceId(type: String): String {
        return when (type) {
            Tags.TransferType.TYPE_IN -> Tags.TransferType.TYPE_IN
            Tags.TransferType.TYPE_OUT -> Tags.TransferType.TYPE_OUT
            Tags.TransferType.TYPE_NAPAS -> Tags.TransferType.TYPE_NAPAS_ACCOUNT
            else -> ""
        }
    }

    override fun onDisplayErrorMessage(exception: AppException) {
        if (exception is AppException.ApiException && exception.requestPath == Constants.MB_CREATE_TEMPLATE) {
            if ("2" == exception.code) {
                _handleErrorCreateTemplateState.postValue(exception)
                return
            }
        }
        super.onDisplayErrorMessage(exception)
    }

    // danh gia cl dich vu
    private val _rateCSATStatus = SingleLiveEvent<CSatRateDomain?>()
    val rateCSATStatus: SingleLiveEvent<CSatRateDomain?> get() = _rateCSATStatus
    fun rateCSat(functionId: String? = null, point: String? = null, comment: String? = null) {
        launchJob {
            val params = CSatRateParams(
                comment = comment,
                functionId = functionId,
                ratePoint = point,
                userName = userProf.getUserName(),
            )
            val res = cSatUseCase.rateCSAT(params)
            handleResource(res) { data ->
                _rateCSATStatus.postValue(data)
            }
        }
    }

    // cau hinh danh gia cl dich vu
    private val _configCSATStatus = SingleLiveEvent<CSatConfigDomain?>()
    val configCSat: SingleLiveEvent<CSatConfigDomain?> get() = _configCSATStatus
    fun configCSat(functionId: String? = null) = launchJobSilent {
        val params = CSatConfigParams(
            functionId = functionId,
            userName = userProf.getUserName(),
        )
        val res = cSatUseCase.configCSAT(params)
        handleResourceSilent(
            resource = res,
            onSuccess = { config ->
                _configCSATStatus.postValue(config)
            },
        )
    }

    var tooltip: TooltipCSAT? = null

    fun showToolTipCSAT(
        btnCSAT: AppCompatButton,
        timeShow: Long = 5000,
        onItemClick: (String) -> Unit,
        countDownTimerFinish: () -> Unit,
        context: Context,
    ) {
        tooltip = TooltipCSAT(context).apply {
            show(anchorView = btnCSAT)
        }

        tooltip?.setOnClickItemListener { rateSubmitString ->
            rateSubmitString?.let { onItemClick(it) } // <-- invoke callback
        }

        val countdownTimer: CountDownTimer = object : CountDownTimer(timeShow, 1000) {
            override fun onTick(millisUntilFinished: Long) {}
            override fun onFinish() {
                tooltip?.dismiss()
                countDownTimerFinish()
            }
        }
        countdownTimer.start()
    }

    fun clearCacheAccount() {
        transferCacheManager.clearAccountsIN()
        transferCacheManager.clearAccountsOUT()
        transferCacheManager.clearAccountsNP247()
        transferCacheManager.clearAccountsCARD()
        transferCacheManager.clearAccountsPM()
    }

    fun readAmountInWord(amount: String? = null, ccy: String? = null) = moneyHelper.convertAmountToWords(amount ?: "", ccy ?: "")
}