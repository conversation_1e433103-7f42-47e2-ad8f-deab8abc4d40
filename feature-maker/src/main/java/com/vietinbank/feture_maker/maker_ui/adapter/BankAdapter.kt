package com.vietinbank.feture_maker.maker_ui.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.extensions.loadDrawable
import com.vietinbank.core_common.extensions.loadUrl
import com.vietinbank.core_domain.models.maker.DataBankDomain
import com.vietinbank.feature_maker.R
import com.vietinbank.feature_maker.databinding.ItemBankBinding

class BankAdapter : RecyclerView.Adapter<BankAdapter.BankHolder>() {
    var dataSet: MutableList<DataBankDomain> = mutableListOf()
    var context: Context? = null

    @SuppressLint("NotifyDataSetChanged")
    var onClickItem: ((DataBankDomain) -> Unit)? = null

    @SuppressLint("NotifyDataSetChanged")
    fun setData(data: MutableList<DataBankDomain>) {
        dataSet.clear()
        dataSet.addAll(data)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): BankHolder {
        val binding = ItemBankBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false,
        )
        return BankHolder(binding)
    }

    inner class BankHolder(var binding: ItemBankBinding) : RecyclerView.ViewHolder(binding.root)

    override fun onBindViewHolder(holder: BankHolder, position: Int) {
        val item = dataSet[position]
        with(holder.binding) {
            context = holder.itemView.context
            tvBankName.text = (item.shortName ?: "")
            tvName.text = (item.bankName ?: "")
            tvType.text = getTypeName(item.type)
            root.setThrottleClickListener {
                dataSet[position].let { it1 -> onClickItem?.invoke(it1) }
            }
            if (Tags.TransferType.TYPE_IN == item.type) {
                imgIcon.loadDrawable(R.drawable.ic_vietinbank)
            } else {
                imgIcon.loadUrl(url = item.icon ?: "", isCache = true)
            }
        }
    }

    override fun getItemCount(): Int = dataSet.size

    companion object {

        fun getTypeName(type: String?): String {
            return when (type) {
                Tags.TransferType.TYPE_IN -> {
                    "Cùng hệ thống"
                }

                Tags.TransferType.TYPE_OUT -> {
                    "Ngoài hệ thống"
                }

                Tags.TransferType.TYPE_NAPAS -> {
                    "Nhanh 24/7"
                }
                Tags.TransferType.TYPE_NORMAL -> {
                    "Chuyển thường"
                }

                else -> ""
            }
        }
    }
}