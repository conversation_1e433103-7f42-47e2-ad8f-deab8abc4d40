package com.vietinbank.feture_maker.maker_ui.bottomSheet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_domain.models.login.AccountDefaultDomain
import com.vietinbank.core_ui.base.BaseBottomSheetFragment
import com.vietinbank.feature_maker.databinding.FragmentAccountListBinding
import com.vietinbank.feture_maker.maker_ui.adapter.AccountAdapter
import dagger.hilt.android.AndroidEntryPoint
import kotlin.String

@AndroidEntryPoint
class AccListBottomSheetFragment : BaseBottomSheetFragment() {
    private var _binding: FragmentAccountListBinding? = null
    private val binding get() = _binding!!
    private val accountAdapter = AccountAdapter()
    private var onItemListener: ((AccountDefaultDomain) -> Unit)? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        _binding = FragmentAccountListBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun initView() {
        binding.rcvAccount.adapter = accountAdapter
    }

    fun setData(accountDefault: MutableList<AccountDefaultDomain>) {
        val groupedByAccountType: Map<String?, List<AccountDefaultDomain>> =
            accountDefault.groupBy { it.accountType }
        val adapterData = mapGroupedAccountsToAdapterData(groupedByAccountType)
        accountAdapter.setData(adapterData)
    }

    fun mapGroupedAccountsToAdapterData(groupedByAccountType: Map<String?, List<AccountDefaultDomain>>): MutableList<AccountDefaultDomain> {
        val adapterData = mutableListOf<AccountDefaultDomain>()

        // Process each group
        groupedByAccountType.forEach { (accountType, accounts) ->
            // First add a title item for this group
            adapterData.add(
                AccountDefaultDomain(
                    group = AccountAdapter.TYPE_GROUP_TITLE,
                    accountType = accountType,
                    accountName = "",
                    accountNo = "",
                    accrueInterest = "",
                    accruedInterest = "",
                    acctNbr = "",
                    acctType = "",
                    aliasName = "",
                    availableBalance = "",
                    benBankName = "",
                    beneficiaryName = "",
                    branchId = "",
                    branchName = "",
                    cifNo = "",
                    closeDate = "",
                    closingOutstandingBalance = "",
                    contractNbr = "",
                    corpName = "",
                    creditCardLst = emptyList(),
                    creditLimit = "",
                    currency = "",
                    currentBalance = "",
                    depositCardSerialNumber = "",
                    depositContractNumber = "",
                    districtId = "",
                    districtName = "",
                    dueDate = "",
                    escrowAmt = "",
                    feeAcctNo = "",
                    feePlans = emptyList(),
                    fullPayLeft = "",
                    holdBalance = "",
                    interestAmount = "",
                    interestNotDueBilled = "",
                    interestPastDue = "",
                    interestRate = "",
                    interestTerm = "",
                    issuedDate = "",
                    lang = "",
                    lateCharge = "",
                    mainAccountId = "",
                    maturityDate = "",
                    minPayLeft = "",
                    minimumAmount = "",
                    nextPaymentDate = "",
                    openDate = "",
                    origIssueDt = "",
                    outstandingBalance = "",
                    payOffAmount = "",
                    penaltyAmount = "",
                    principalAmount = "",
                    principalNotDueBilled = "",
                    principalPastDue = "",
                    productId = "",
                    productName = "",
                    provinceId = "",
                    provinceName = "",
                    remainingLoanTotal = "",
                    settlementDate = "",
                    statementDate = "",
                    status = "",
                    statusName = "",
                    term = "",
                    totalCredit = "",
                    totalPayment = "",
                    cifName = "",
                ),
            )

            // Then add all accounts for this group, setting their type to content
            accounts.forEach { account ->
                adapterData.add(
                    account.copy(group = AccountAdapter.TYPE_GROUP_CONTENT),
                )
            }
        }

        return adapterData
    }

    override fun initData() {
    }

    override fun initListener() {
        accountAdapter.onClickItem = {
            onItemListener?.invoke(it)
            dismiss()
        }
        binding.ivBack.setThrottleClickListener {
            dismiss()
        }
    }

    fun setOnClickItemListener(action: ((AccountDefaultDomain) -> Unit)?) {
        onItemListener = action
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        onItemListener = null
    }
}