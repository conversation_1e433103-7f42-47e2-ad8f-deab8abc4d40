package com.vietinbank.feture_maker.maker_ui.result

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import androidx.viewbinding.ViewBinding
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.extensions.loadDrawable
import com.vietinbank.core_common.models.TransferObject
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.SpannableUtils
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.Utils.saveLayoutToGallery
import com.vietinbank.core_common.utils.Utils.shareLayoutAsImage
import com.vietinbank.core_domain.models.maker.CreateTransferDomain
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountTransferDomain
import com.vietinbank.core_domain.models.maker.ValidateNapasCardTransferDomains
import com.vietinbank.core_domain.models.maker.ValidatePaymentOrderTransferDomains
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.base.adapter.TransferAdapter
import com.vietinbank.core_ui.base.dialog.AssessmentQualityDialog
import com.vietinbank.core_ui.base.dialog.AssessmentSuccessDialog
import com.vietinbank.feature_maker.databinding.FragmentMakerResultTransferBinding
import com.vietinbank.feture_maker.maker_ui.viewmodel.ResultViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class MakerResultTransferFragment : BaseFragment<ResultViewModel>() {
    override val viewModel: ResultViewModel by viewModels()
    override val useCompose: Boolean = false
    private val transferAdapter = TransferAdapter()
    private var createTransferObject: CreateTransferDomain? = null
    private var createTransferObjectJson = ""
    private var amount = ""
    private var currency = ""
    private var jsonResultObject = ""
    private var isContactCreate = false

    @Inject
    override lateinit var appNavigator: IAppNavigator
    override fun inflateViewBinding(inflater: LayoutInflater, container: ViewGroup?): ViewBinding {
        return FragmentMakerResultTransferBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        createTransferObjectJson =
            arguments?.getString(Tags.TransferType.CREATE_TRANSFER_OBJECT, "").toString()
        isContactCreate = arguments?.getBoolean(
            Tags.TransferType.IS_CONTACT_CREATE, false,
        ) == false
        createTransferObject = createTransferObjectJson.let {
            val type = object : TypeToken<CreateTransferDomain>() {}.type
            Gson().fromJson(it, type)
        }
        viewModel.typeTransfer =
            arguments?.getString(Tags.TransferType.TYPE_TRANSFER, "").toString()
        when (viewModel.typeTransfer) {
            Tags.TransferType.TYPE_ACCOUNT, Tags.TransferType.TYPE_ACCOUNT_SPLIT -> {
                viewModel.validateNapasAccountTransferDomain = Utils.g().provideGson().fromJson(
                    arguments?.getString(
                        Tags.TransferType.CONFIRM_OBJECT, "",
                    ),
                    ValidateNapasAccountTransferDomain::class.java,
                )
                amount = viewModel.validateNapasAccountTransferDomain?.amount ?: ""
                currency = viewModel.validateNapasAccountTransferDomain?.currency ?: ""
                if (isContactCreate) {
                    viewModel.validateNapasAccountTransferDomain.let {
                        viewModel.contactAccountCreate(it)
                    }
                }
            }

            Tags.TransferType.TYPE_CARD -> {
                viewModel.validateNapasCardTransferDomains = Utils.g().provideGson().fromJson(
                    arguments?.getString(
                        Tags.TransferType.CONFIRM_OBJECT, "",
                    ),
                    ValidateNapasCardTransferDomains::class.java,
                )
                amount = viewModel.validateNapasCardTransferDomains?.amount ?: ""
                currency = viewModel.validateNapasCardTransferDomains?.currency ?: ""
                if (isContactCreate) {
                    viewModel.validateNapasCardTransferDomains.let {
                        viewModel.contactCardCreate(it)
                    }
                }
            }

            Tags.TransferType.TYPE_PAYMENT_ORDER -> {
                viewModel.validatePaymentOrderTransferDomains = Utils.g().provideGson().fromJson(
                    arguments?.getString(
                        Tags.TransferType.CONFIRM_OBJECT, "",
                    ),
                    ValidatePaymentOrderTransferDomains::class.java,
                )
                amount = viewModel.validatePaymentOrderTransferDomains?.amount ?: ""
                currency = viewModel.validatePaymentOrderTransferDomains?.currency ?: ""
                if (isContactCreate) {
                    viewModel.validatePaymentOrderTransferDomains.let {
                        // todo
                    }
                }
            }

            Tags.TransferType.TYPE_TRACE_PAYMENT -> {
            }

            else -> Unit
        }
        viewModel.configCSat(Tags.CSAT_FUNC_ID_KEY)
        initView()
        initData()
        initListener()
        viewModel.clearCacheAccount()
    }

    private fun initData() {
        (binding as FragmentMakerResultTransferBinding).apply {
            openCustomToolbar.tvTitleToolbar.text = "Kết quả giao dịch"
            openCustomToolbar.btnBack.visibility = View.GONE
            viewModel.createTemplateState.observe(viewLifecycleOwner) {
                showNoticeDialog(
                    message = "Quý khách lưu mẫu giao dịch thành công",
                    positiveAction = {
                        appNavigator.goToHome()
                    },
                )
            }
            viewModel.handleErrorCreateTemplateState.observe(viewLifecycleOwner) {
                showConfirmDialog(
                    message = it.message ?: "",
                    positiveButtonText = "Xác nhận",
                    negativeButtonText = "Từ chối",
                    positiveAction = {
                        when (viewModel.typeTransfer) {
                            Tags.TransferType.TYPE_ACCOUNT -> {
                                viewModel.validateNapasAccountTransferDomain.let { it1 ->
                                    viewModel.createTemplateAccount(confirm = "Y", it1)
                                }
                            }

                            Tags.TransferType.TYPE_CARD -> {
                                viewModel.validateNapasCardTransferDomains.let { it1 ->
                                    viewModel.createTemplateCard(confirm = "Y", it1)
                                }
                            }

                            else -> Unit
                        }
                    },
                    negativeAction = {
                        swCreateTemplate.isChecked = false
                        dismissNoticeDialog()
                    },
                )
            }

            viewModel.rateCSATStatus.observe(viewLifecycleOwner) {
                AssessmentSuccessDialog.newInstance().show(
                    childFragmentManager,
                    AssessmentSuccessDialog::class.java.name,
                )
                btnRate.visibility = View.GONE
            }

            viewModel.configCSat.observe(viewLifecycleOwner) { config ->
                if (config != null) {
                    if ("Y" == config.showButton) {
                        btnRate.isVisible = true
                        if ("Y" == config.showPopup) {
                            root.alpha = 0.7f
                            requireContext().let {
                                viewModel.showToolTipCSAT(
                                    btnCSAT = btnRate,
                                    timeShow = (config.timeShow ?: "0").toLong() * 1000,
                                    onItemClick = { ratingString ->
                                        val dialog = AssessmentQualityDialog.newInstance(ratingString)
                                        dialog.show(
                                            childFragmentManager,
                                            AssessmentQualityDialog::class.java.name,
                                        )
                                        dialog.setOnConfirmListener { point, comment ->
                                            viewModel.rateCSat(point = point, comment = comment)
                                        }
                                    },
                                    countDownTimerFinish = {
                                        root.alpha = 1f
                                    },
                                    context = it,
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    private fun initView() {
        setDataListResult()
        (binding as FragmentMakerResultTransferBinding).apply {
            openCustomToolbar.imgRight1.visibility = View.VISIBLE
            openCustomToolbar.imgRight2.visibility = View.VISIBLE
            openCustomToolbar.imgRight1.loadDrawable(com.vietinbank.core_common.R.drawable.ic_save)
            openCustomToolbar.imgRight2.loadDrawable(com.vietinbank.core_common.R.drawable.ic_share)
            SpannableUtils.makeClickableSpan(
                string = createTransferObject?.status?.message ?: "",
                specialString = createTransferObject?.mtId ?: "",
                textView = tvMessage,
            ) {
            }
            if (amount.isNullOrEmpty()) {
                lnAmount.visibility = View.GONE
                tvAmount.text = ""
                tvAmountReader.text = ""
            } else {
                lnAmount.visibility = View.VISIBLE
                tvAmount.text = Utils.g().getDotMoneyHasCcy(amount, currency)
                tvAmountReader.text = viewModel.readAmountInWord(amount, currency)
            }
        }
    }

    private fun initListener() {
        (binding as FragmentMakerResultTransferBinding).apply {
            btnResultMaker.setThrottleClickListener {
                gotoTransfer()
            }
            swCreateTemplate.setOnCheckedChangeListener { _, isChecked ->
                if (isChecked) {
                    when (viewModel.typeTransfer) {
                        Tags.TransferType.TYPE_ACCOUNT -> {
                            viewModel.validateNapasAccountTransferDomain.let { it1 ->
                                viewModel.createTemplateAccount(confirm = "N", it1)
                            }
                        }

                        Tags.TransferType.TYPE_CARD -> {
                            viewModel.validateNapasCardTransferDomains.let { it1 ->
                                viewModel.createTemplateCard(confirm = "N", it1)
                            }
                        }

                        else -> Unit
                    }
                }
            }

            tvManageTransaction.setThrottleClickListener {
                appNavigator.gotoFilterTransaction()
            }

            // danh gia dich vu
            btnRate.setThrottleClickListener {
                val dialog = AssessmentQualityDialog.newInstance()
                dialog.setOnConfirmListener { point, comment ->
                    viewModel.rateCSat(Tags.CSAT_FUNC_ID_KEY, point, comment)
                }
                dialog.show(childFragmentManager, AssessmentQualityDialog::class.java.name)
            }

            openCustomToolbar.imgRight1.setThrottleClickListener {
                requestPermissionExternalStore()
            }
            requestPermissionExternalStoreListener { allow ->
                if (allow) {
                    requireContext().let {
                        saveLayoutToGallery(rlResult, it)
                    }
                }
            }
            openCustomToolbar.imgRight2.setThrottleClickListener {
                requireContext().let {
                    shareLayoutAsImage(rlResult, it)
                }
            }
        }
    }

    private fun setDataListResult() {
        jsonResultObject = arguments?.getString(Tags.TransferType.LIST_RESULT_OBJECT, "").toString()
        val resultTransferObjectList: List<TransferObject> = jsonResultObject.let {
            val type = object : TypeToken<List<TransferObject>>() {}.type
            Gson().fromJson(it, type)
        } ?: emptyList()
        (binding as FragmentMakerResultTransferBinding).apply {
            rcvInfo.adapter = transferAdapter
        }
        transferAdapter.setData(resultTransferObjectList as MutableList<TransferObject>)
    }

    fun gotoTransfer() {
        when (viewModel.typeTransfer) {
            Tags.TransferType.TYPE_ACCOUNT, Tags.TransferType.TYPE_CARD -> {
//                appNavigator.goToViewPagerTransferFragment(bundleOf())
            }

            Tags.TransferType.TYPE_PAYMENT_ORDER -> {
                appNavigator.goToPaymentOrderFragment()
            }

            else -> {
                appNavigator.goToHome()
            }
        }
    }

    override fun onBackPressed(): Boolean {
        appNavigator.goToHome()
        return true
    }
}