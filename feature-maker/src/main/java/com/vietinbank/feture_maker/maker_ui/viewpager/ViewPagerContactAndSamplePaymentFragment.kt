package com.vietinbank.feture_maker.maker_ui.viewpager

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.viewpager2.widget.ViewPager2
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_ui.base.BaseBottomSheetFragment
import com.vietinbank.feature_maker.databinding.FragmentContactAndSamplePagerBinding
import com.vietinbank.feture_maker.maker_ui.adapter.ViewPagerContactAndSamplePaymentAdapter
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class ViewPagerContactAndSamplePaymentFragment : BaseBottomSheetFragment() {
    private var adapter: ViewPagerContactAndSamplePaymentAdapter? = null
    private var _binding: FragmentContactAndSamplePagerBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        _binding = FragmentContactAndSamplePagerBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun initView() {
        binding.apply {
            adapter = ViewPagerContactAndSamplePaymentAdapter(childFragmentManager, lifecycle)
            viewPager.offscreenPageLimit = 2
            viewPager.isSaveEnabled = false
            viewPager.adapter = adapter
            viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    super.onPageSelected(position)
                    when (position) {
                        0 -> {
                            lineContact.visibility = View.VISIBLE
                            lineShample.visibility = View.INVISIBLE
                        }

                        1 -> {
                            lineContact.visibility = View.INVISIBLE
                            lineShample.visibility = View.VISIBLE
                        }
                    }
                }
            })
            viewPager.isUserInputEnabled = false
        }
    }

    override fun initData() {
    }

    override fun initListener() {
        (binding).apply {
            tvGotoContact.setThrottleClickListener {
                viewPager.setCurrentItem(0, true)
            }
            tvGotoSample.setThrottleClickListener {
                viewPager.setCurrentItem(1, true)
            }
            ivBack.setThrottleClickListener {
                dismiss()
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}