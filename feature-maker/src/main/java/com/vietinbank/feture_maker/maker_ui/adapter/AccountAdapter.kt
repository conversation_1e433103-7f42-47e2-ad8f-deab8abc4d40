package com.vietinbank.feture_maker.maker_ui.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_domain.models.login.AccountDefaultDomain
import com.vietinbank.core_ui.databinding.ItemTitleConfirmMakerBinding
import com.vietinbank.feature_maker.databinding.ItemAccountBinding

class AccountAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    private var dataSet: MutableList<AccountDefaultDomain> = mutableListOf()
    var context: Context? = null

    @SuppressLint("NotifyDataSetChanged")
    var onClickItem: ((AccountDefaultDomain) -> Unit)? = null

    companion object {
        const val TYPE_GROUP_TITLE = 0
        const val TYPE_GROUP_CONTENT = 1
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setData(data: MutableList<AccountDefaultDomain>) {
        dataSet.clear()
        dataSet.addAll(data)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_GROUP_TITLE -> GroupTitleViewHolder(
                ItemTitleConfirmMakerBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false,
                ),
            )

            TYPE_GROUP_CONTENT -> GroupContentViewHolder(
                ItemAccountBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false,
                ),
            )

            else -> throw IllegalArgumentException("Unknown view type")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is GroupTitleViewHolder -> holder.bind(position)
            is GroupContentViewHolder -> holder.bind(position)
        }
    }

    override fun getItemCount(): Int = dataSet.size
    override fun getItemViewType(position: Int): Int {
        return when (dataSet[position].group) {
            TYPE_GROUP_TITLE -> TYPE_GROUP_TITLE
            TYPE_GROUP_CONTENT -> TYPE_GROUP_CONTENT
            else -> throw IllegalArgumentException("Unknown group")
        }
    }

    inner class GroupTitleViewHolder(private val binding: ItemTitleConfirmMakerBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(position: Int) {
            val item = dataSet[position]
            binding.tvTitle.text = when (item.accountType) {
                "D" -> "Tài khoản thanh toán"
                "T" -> "Tài khoản tiền gửi"
                else -> item.accountType ?: "Other Accounts"
            }
        }
    }

    inner class GroupContentViewHolder(private val binding: ItemAccountBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(position: Int) {
            val item = dataSet[position]
            binding.apply {
                context = binding.root.context
                tvAccountNumber.text =
                    item.accountNo.toString() + " - " + item.currency.toString() +
                    if (TextUtils.isEmpty(item.aliasName)) {
                        ""
                    } else {
                        " - " + item.aliasName.toString()
                    }
                root.setThrottleClickListener {
                    dataSet[position].let { it1 -> onClickItem?.invoke(it1) }
                }
            }
        }
    }
}
