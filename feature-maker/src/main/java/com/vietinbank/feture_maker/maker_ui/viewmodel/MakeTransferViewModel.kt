package com.vietinbank.feture_maker.maker_ui.viewmodel

import android.text.TextUtils
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.constants.Tags.TransferType.TYPE_NORMAL
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.extensions.dd_MM_yyyy_HH_mm_ss
import com.vietinbank.core_common.extensions.todayAsString
import com.vietinbank.core_common.livedata.SingleLiveEvent
import com.vietinbank.core_common.models.DataFillObject
import com.vietinbank.core_common.models.TransferObject
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.login.AccountDefaultDomain
import com.vietinbank.core_domain.models.login.AccountListDomain
import com.vietinbank.core_domain.models.login.AccountListParams
import com.vietinbank.core_domain.models.maker.ApproverDomains
import com.vietinbank.core_domain.models.maker.ContactCreateParams
import com.vietinbank.core_domain.models.maker.ContactDomains
import com.vietinbank.core_domain.models.maker.ContactListParams
import com.vietinbank.core_domain.models.maker.DataBankDomain
import com.vietinbank.core_domain.models.maker.GetPaymentTemplateListDomains
import com.vietinbank.core_domain.models.maker.GetPaymentTemplateListParams
import com.vietinbank.core_domain.models.maker.NapasBankListParams
import com.vietinbank.core_domain.models.maker.NapasTransferParams
import com.vietinbank.core_domain.models.maker.NextStatusTransactionByRuleDomains
import com.vietinbank.core_domain.models.maker.NextStatusTransactionByRuleParams
import com.vietinbank.core_domain.models.maker.TempTransactionDomains
import com.vietinbank.core_domain.models.maker.TempTransactionParams
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountDomain
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountParams
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountTransferDomain
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountTransferParams
import com.vietinbank.core_domain.models.maker.ValidateNapasCardDomains
import com.vietinbank.core_domain.models.maker.ValidateNapasCardParams
import com.vietinbank.core_domain.models.maker.ValidateNapasCardTransferDomains
import com.vietinbank.core_domain.models.maker.ValidateNapasCardTransferParams
import com.vietinbank.core_domain.models.qr.QrCodeDomain
import com.vietinbank.core_domain.repository.cache.ITransferCacheManager
import com.vietinbank.core_domain.usecase.login.LoginUseCase
import com.vietinbank.core_domain.usecase.transfer.TransferUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.adapter.TransferAdapter
import com.vietinbank.feture_maker.maker_ui.adapter.BankDefaultAdapter
import com.vietinbank.feture_maker.maker_ui.adapter.ContactAdapter
import com.vietinbank.feture_maker.maker_ui.adapter.ContactTypeAdapter
import com.vietinbank.feture_maker.maker_ui.adapter.ShampleTypeAdapter
import com.vietinbank.feture_maker.maker_ui.bottomSheet.AccListBottomSheetFragment
import com.vietinbank.feture_maker.maker_ui.bottomSheet.BankListBottomSheetFragment
import com.vietinbank.feture_maker.maker_ui.bottomSheet.DataFillBottomSheetFragment
import com.vietinbank.feture_maker.maker_ui.viewpager.ViewPagerContactAndSampleFragment
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

@HiltViewModel
class MakeTransferViewModel @Inject constructor(
    private val transferUseCase: TransferUseCase,
    private val loginUseCase: LoginUseCase,
    val transferCacheManager: ITransferCacheManager,
    private val moneyHelper: MoneyHelper,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
) : BaseViewModel() {
    private var currency: String = ""
    fun setCcy(currency: String?) {
        this.currency = currency ?: ""
    }

    fun readAmountInWord(amount: String? = null, ccy: String? = null) =
        moneyHelper.convertAmountToWords(amount ?: "", ccy ?: currency)

    var viewPagerContactAndSampleBottomSheet = ViewPagerContactAndSampleFragment()
    var listContact: MutableList<ContactDomains> = mutableListOf()
    var listDataBanks: MutableList<DataBankDomain> = mutableListOf()
    val contactAccountAdapter = ContactAdapter()
    val contactCardAdapter = ContactAdapter()
    var isTransferAccountTab = true
    private var dataTypeTransfer = mutableListOf<DataFillObject>()
    var chooseTransferTypeDialog = DataFillBottomSheetFragment()
    var currentTypeTransfer: DataFillObject? = null
    val chooseBankDialog = BankListBottomSheetFragment()
    val chooseAccountNoDialog = AccListBottomSheetFragment()
    var currentDataBankDomain: DataBankDomain? = null
    var currentAccountDefaultDomain: AccountDefaultDomain? = null
    var validateNapasAccountDomain: ValidateNapasAccountDomain? = null
    val contactAdapter = ContactAdapter()
    var contactTypeAdapter = ContactTypeAdapter()
    var listContactFilter: MutableList<ContactDomains> = mutableListOf()
    var listShampleFilter: MutableList<TempTransactionDomains> = mutableListOf()
    val chooseAccountNoCardDialog = AccListBottomSheetFragment()
    var currentCardDefaultDomain: AccountDefaultDomain? = null
    var currentTypeTransferCard: DataFillObject? = null
    private var dataTypeTransferCard = mutableListOf<DataFillObject>()
    private var dataTypeScheduleTime = mutableListOf<DataFillObject>()
    var currentTypeAccountSchedule: DataFillObject? = null
    var currentTypeCardSchedule: DataFillObject? = null
    val chooseTransferCardTypeDialog = DataFillBottomSheetFragment()
    var validateNapasCardDomains: ValidateNapasCardDomains? = null
    val shampleAdapter = ShampleTypeAdapter()
    var validateNapasAccountTransferDomain: ValidateNapasAccountTransferDomain? = null
    private var validateNapasCardTransferDomains: ValidateNapasCardTransferDomains? = null
    val bankDefaultAdapter = BankDefaultAdapter()
    var isSaveAccount = false
    var isSaveCard = false
    var listAccountApprovers = emptyList<ApproverDomains>()

    private val _accListState = SingleLiveEvent<Resource<AccountListDomain>>()
    val accListState: SingleLiveEvent<Resource<AccountListDomain>> get() = _accListState

    private val _accListCardState = SingleLiveEvent<Resource<AccountListDomain>>()
    val accListCardState: SingleLiveEvent<Resource<AccountListDomain>> get() = _accListCardState

    private val _validateNapasAccountState = SingleLiveEvent<Resource<ValidateNapasAccountDomain>>()
    val validateNapasAccountState: SingleLiveEvent<Resource<ValidateNapasAccountDomain>> get() = _validateNapasAccountState
    private val _handleErrorvalidateNapasAccountState = SingleLiveEvent<AppException>()
    val handleErrorvalidateNapasAccountState: SingleLiveEvent<AppException> get() = _handleErrorvalidateNapasAccountState

    private val _validateNapasAccountTransferState =
        SingleLiveEvent<ValidateNapasAccountTransferDomain>()
    val validateNapasAccountTransferState: SingleLiveEvent<ValidateNapasAccountTransferDomain> get() = _validateNapasAccountTransferState

    private val _validateNapasAccountTransferSplitState =
        SingleLiveEvent<ValidateNapasAccountTransferDomain>()
    val validateNapasAccountTransferSplitState: SingleLiveEvent<ValidateNapasAccountTransferDomain> get() = _validateNapasAccountTransferSplitState

    private val _validateNapasCardState = SingleLiveEvent<Resource<ValidateNapasCardDomains>>()
    val validateNapasCardState: SingleLiveEvent<Resource<ValidateNapasCardDomains>> get() = _validateNapasCardState
    private val _validateNapasCardTransferState =
        SingleLiveEvent<ValidateNapasCardTransferDomains>()
    val validateNapasCardTransferState: SingleLiveEvent<ValidateNapasCardTransferDomains> get() = _validateNapasCardTransferState

    private val _clickItemBankFromContactFragmentState = SingleLiveEvent<ContactDomains>()
    val clickItemBankFromContactFragmentState: SingleLiveEvent<ContactDomains> get() = _clickItemBankFromContactFragmentState

    private val _clickItemCardFromContactFragmentState = SingleLiveEvent<ContactDomains>()
    val clickItemCardFromContactFragmentState: SingleLiveEvent<ContactDomains> get() = _clickItemCardFromContactFragmentState

    private val _getNapasBankList = SingleLiveEvent<Any>()
    val getNapasBankList: SingleLiveEvent<Any> get() = _getNapasBankList

    private val _clickAccountShampleFromContactFragmentState =
        SingleLiveEvent<TempTransactionDomains>()
    val clickAccountShampleFromContactFragmentState: SingleLiveEvent<TempTransactionDomains> get() = _clickAccountShampleFromContactFragmentState

    private val _clickCardShampleFromContactFragmentState =
        SingleLiveEvent<TempTransactionDomains>()
    val clickCardShampleFromContactFragmentState: SingleLiveEvent<TempTransactionDomains> get() = _clickCardShampleFromContactFragmentState

    private val _getPaymentTemplateListState = SingleLiveEvent<GetPaymentTemplateListDomains>()
    val getPaymentTemplateListState: SingleLiveEvent<GetPaymentTemplateListDomains> get() = _getPaymentTemplateListState

    private val _nextStatusTransactionByRuleState =
        SingleLiveEvent<NextStatusTransactionByRuleDomains>()
    val nextStatusTransactionByRuleState: SingleLiveEvent<NextStatusTransactionByRuleDomains> get() = _nextStatusTransactionByRuleState

    private val _nextStatusCardTransactionByRuleState =
        SingleLiveEvent<NextStatusTransactionByRuleDomains>()
    val nextStatusCardTransactionByRuleState: SingleLiveEvent<NextStatusTransactionByRuleDomains> get() = _nextStatusCardTransactionByRuleState

    private val _handleDatePickerAccountState = SingleLiveEvent<String>()
    val handleDatePickerAccountState: SingleLiveEvent<String> get() = _handleDatePickerAccountState

    private val _handleDatePickerCardState = SingleLiveEvent<String>()
    val handleDatePickerCardState: SingleLiveEvent<String> get() = _handleDatePickerCardState

    private val _onSelectTabCardState = SingleLiveEvent<Any>()
    val onSelectTabCardState: SingleLiveEvent<Any> get() = _onSelectTabCardState

    private val _onSelectTabAccountState = SingleLiveEvent<Any>()
    val onSelectTabAccountState: SingleLiveEvent<Any> get() = _onSelectTabAccountState

    fun onSelectTabCardState() {
        _onSelectTabCardState.postValue("")
    }

    fun onSelectTabAccountState() {
        _onSelectTabAccountState.postValue("")
    }

    fun clickItemBankFromContactFragment(contactDomains: ContactDomains) {
        viewPagerContactAndSampleBottomSheet.dismiss()
        _clickItemBankFromContactFragmentState.postValue(contactDomains)
    }

    fun clickItemCardFromContactFragment(contactDomains: ContactDomains) {
        viewPagerContactAndSampleBottomSheet.dismiss()
        _clickItemCardFromContactFragmentState.postValue(contactDomains)
    }

    fun clickItemShampleFromContactFragmentState(tempTransactionDomains: TempTransactionDomains) {
        viewPagerContactAndSampleBottomSheet.dismiss()
        _clickAccountShampleFromContactFragmentState.postValue(tempTransactionDomains)
    }

    fun clickItemCardShampleFromContactFragmentState(tempTransactionDomains: TempTransactionDomains) {
        viewPagerContactAndSampleBottomSheet.dismiss()
        _clickCardShampleFromContactFragmentState.postValue(tempTransactionDomains)
    }

    fun getAccList() {
        val cachedAccounts = when {
            isTransferIn() -> transferCacheManager.getAccountsIN()
            isTransferOut() -> transferCacheManager.getAccountsOUT()
            isTransferNapas() -> transferCacheManager.getAccountsNP247()
            else -> null
        }

        if (cachedAccounts?.isNotEmpty() == true) {
            processCachedAccounts(cachedAccounts)
            return
        }

        fetchAccountsFromApi()
    }

    private fun processCachedAccounts(accounts: MutableList<AccountDefaultDomain>) {
        getDefaultAccount(accounts)?.let {
            currentAccountDefaultDomain = it
        }
        chooseAccountNoDialog.setData(accounts)
        val accountListDomain = AccountListDomain(accounts)
        _accListState.postValue(Resource.Success(accountListDomain))
    }

    private fun fetchAccountsFromApi() {
        launchJob(showLoading = true) {
            val serviceType = getServiceTypeForCurrentTransfer()
            val params = AccountListParams(
                accountType = "",
                currencySort = "",
                username = userProf.getUserName() ?: "",
                serviceType = serviceType,
            )

            val result = loginUseCase.accList(params)
            handleResource(result) { data ->
                val activeAccounts = data.accountDefault.filter { it.status == "0" }.toMutableList()
                when {
                    isTransferIn() -> transferCacheManager.saveAccountsIN(activeAccounts)
                    isTransferOut() -> transferCacheManager.saveAccountsOUT(activeAccounts)
                    isTransferNapas() -> transferCacheManager.saveAccountsNP247(activeAccounts)
                }

                getDefaultAccount(activeAccounts)?.let {
                    currentAccountDefaultDomain = it
                }
                chooseAccountNoDialog.setData(activeAccounts)
                _accListState.postValue(Resource.Success(data))
            }
        }
    }

    private fun getServiceTypeForCurrentTransfer(): String {
        return when {
            isTransferIn() -> Tags.TransferType.SERVICE_TYPE_TRANSFER_IN
            isTransferOut() -> Tags.TransferType.SERVICE_TYPE_TRANSFER_OUT
            else -> Tags.TransferType.SERVICE_TYPE_TRANSFER_NAPAS_CARD
        }
    }

    fun getNapasBankList() {
        if (transferCacheManager.getBankList()?.isNotEmpty() == true) {
            listDataBanks = transferCacheManager.getBankList()?.toMutableList() ?: mutableListOf()
            setListBankData()
            return
        }
        launchJob(showLoading = true) {
            val res = transferUseCase.getNapasBankList(
                NapasBankListParams(
                    username = userProf.getUserName() ?: "",
                    cifno = userProf.getCifNo() ?: "",
                ),
            )
            handleResource(res) { data ->
                listDataBanks = data.dataBanks
                transferCacheManager.saveBankList(listDataBanks)
                setListBankData()
            }
        }
    }

    private fun setListBankData() {
        chooseBankDialog.setData(listDataBanks)
        bankDefaultAdapter.setData(listDataBanks.toMutableList())
        if (napasTransferParams != null) {
            _getNapasBankList.postValue("")
        }
    }

    fun changeType247ToNormalListBank() {
        if (listDataBanks.isNotEmpty()) {
            listDataBanks = listDataBanks.map { dataBankDomain ->
                if (dataBankDomain.type == Tags.TransferType.TYPE_NAPAS) {
                    DataBankDomain(
                        type = TYPE_NORMAL,
                        binCode = dataBankDomain.binCode ?: "",
                        bankName = dataBankDomain.bankName ?: "",
                        shortName = dataBankDomain.shortName ?: "",
                        ebankCode = dataBankDomain.ebankCode ?: "",
                        icon = dataBankDomain.icon ?: "",
                        name = dataBankDomain.name ?: "",
                        status = dataBankDomain.status ?: "",
                    )
                } else {
                    dataBankDomain
                }
            }.toMutableList()
            chooseBankDialog.setData(listDataBanks)
            bankDefaultAdapter.setData(listDataBanks.toMutableList())
            currentDataBankDomain = listDataBanks.find {
                it.shortName == (
                    currentDataBankDomain?.shortName
                        ?: ""
                    ) && it.bankName == (currentDataBankDomain?.bankName ?: "")
            }
        }
    }

    fun validateNextStatusTransactionByRuleFields(
        amount: String,
        toAccountNo: String,
    ): String? {
        return when {
            currentAccountDefaultDomain == null -> "Chưa có tài khoản mặc định"
            toAccountNo.isBlank() -> "Nhập số tài khoản"
            amount.isBlank() -> "Nhập tiền chuyển"
            else -> null
        }
    }
    fun validateCreateContact(
        edtReceiveAccount: String,
        edtCustomerCode: String,
    ): String? {
        return when {
            edtReceiveAccount.isBlank() -> "Nhập số tài khoản"
            edtCustomerCode.isBlank() -> "Nhập tiền chuyển"
            else -> null
        }
    }

    fun nextStatusTransactionByRule(amount: String, toAccountNo: String) {
        launchJob(showLoading = true) {
            val serviceType = getServiceTypeForCurrentTransfer()
            val params = NextStatusTransactionByRuleParams(
                amount = amount,
                creator = userProf.getUserName() ?: "",
                currentStatus = "",
                currentUserGroup = "",
                currentUserLevel = "0",
                customerNumber = userProf.getCifNo() ?: "",
                fromAccountNo = currentAccountDefaultDomain?.accountNo ?: "",
                serviceCode = serviceType,
                toAccountNo = toAccountNo,
                username = userProf.getUserName() ?: "",
                mtId = "",
            )
            val res = transferUseCase.nextStatusTransactionByRule(params)
            handleResource(res) { data ->
                _nextStatusTransactionByRuleState.postValue(data)
            }
        }
    }
    fun contactAccountCreate(edtReceiveAccount: String, edtCustomerCode: String) {
        val params = ContactCreateParams(
            accountNo = edtReceiveAccount,
            confirm = "N",
            currency = validateNapasAccountDomain?.currency ?: "",
            customercode = edtCustomerCode,
            payeeName = validateNapasAccountDomain?.accountOwner ?: "",
            receiveBankName = Utils.g()
                .removeAccent(currentDataBankDomain?.bankName ?: ""),
            revBankCode = getReceiveBin(),
            serviceId = getServiceId(currentDataBankDomain?.type ?: ""),
            username = userProf.getUserName() ?: "",
            tranType = currentDataBankDomain?.type ?: "",
        )
        launchJob(showLoading = true) {
            val res = transferUseCase.contactCreate(params)
            handleResource(res) { data ->
                transferCacheManager.clearContactList()
            }
        }
    }
    private fun getServiceId(type: String): String {
        return when (type) {
            Tags.TransferType.TYPE_IN -> Tags.TransferType.TYPE_IN
            Tags.TransferType.TYPE_OUT -> Tags.TransferType.TYPE_OUT
            Tags.TransferType.TYPE_NAPAS -> Tags.TransferType.TYPE_NAPAS_ACCOUNT
            else -> ""
        }
    }
    fun validateNextStatusCardTransactionByRuleFields(
        amount: String,
        toAccountNo: String,
    ): String? {
        return when {
            currentCardDefaultDomain == null -> "Chưa có tài khoản mặc định"
            toAccountNo.isNullOrBlank() -> "Nhập số thẻ"
            amount.isNullOrBlank() -> "Nhập tiền chuyển"
            else -> null
        }
    }

    fun nextStatusCardTransactionByRule(amount: String, toAccountNo: String) {
        launchJob(showLoading = true) {
            val params = NextStatusTransactionByRuleParams(
                amount = amount,
                creator = userProf.getUserName() ?: "",
                currentStatus = "",
                currentUserGroup = "",
                currentUserLevel = "0",
                customerNumber = userProf.getCifNo() ?: "",
                fromAccountNo = currentCardDefaultDomain?.accountNo ?: "",
                serviceCode = Tags.TransferType.SERVICE_TYPE_TRANSFER_NAPAS_CARD,
                toAccountNo = toAccountNo,
                username = userProf.getUserName() ?: "",
                mtId = "",
            )
            val res = transferUseCase.nextStatusTransactionByRule(params)
            handleResource(res) { data ->
                _nextStatusCardTransactionByRuleState.postValue(data)
            }
        }
    }

    fun validateNapasAccount(receiveAccount: String) {
        when {
            currentDataBankDomain == null -> {
                return
            }

            isTransferNomal() || isTransferOut() -> {
                return
            }

            (isTransferIn() || isTransferNapas()) && currentAccountDefaultDomain != null -> {
                launchJob(showLoading = true) {
                    val params = ValidateNapasAccountParams(
                        receiveAccount = receiveAccount ?: "",
                        receiveBin = currentDataBankDomain?.binCode ?: "",
                        debitAccount = currentAccountDefaultDomain?.accountNo ?: "",
                        debitFullname = userProf.getFullName() ?: "",
                        cifno = userProf.getCifNo() ?: "",
                        currency = currentAccountDefaultDomain?.currency ?: "",
                        username = userProf.getUserName() ?: "",
                        tranType = currentDataBankDomain?.type ?: "",
                        receiveBankName = currentDataBankDomain?.bankName ?: "",
                    )

                    val res = transferUseCase.validateNapasAccount(params)
                    handleResource(res) { data ->
                        validateNapasAccountDomain = data
                        _validateNapasAccountState.postValue(Resource.Success(data))
                    }
                }
            }

            else -> getAccList()
        }
    }

    fun validateNapasAccountTransfer(
        amount: String,
        remark: String,
        toAcctNo: String,
        edtAccountOwner: String?,
        processDate: String?,
    ) {
        launchJob(showLoading = true) {
            val currentBankDomain = currentDataBankDomain ?: return@launchJob
            val currentAccount = currentAccountDefaultDomain ?: return@launchJob
            val currentTransferType = currentTypeTransfer ?: return@launchJob
            val isNapasOrInTransfer = isTransferIn() || isTransferNapas()
            val toAcctName = if (isNapasOrInTransfer && validateNapasAccountDomain != null) {
                validateNapasAccountDomain?.accountOwner ?: ""
            } else {
                edtAccountOwner ?: ""
            }
            val tranType = if (isNapasOrInTransfer) {
                currentBankDomain.type ?: ""
            } else {
                Tags.TransferType.TYPE_OUT
            }
            val receiveBin = when (currentBankDomain.type) {
                Tags.TransferType.TYPE_IN -> validateNapasAccountDomain?.receiveBin ?: ""
                Tags.TransferType.TYPE_NAPAS -> currentBankDomain.binCode ?: ""
                else -> currentBankDomain.ebankCode ?: ""
            }
            val params = ValidateNapasAccountTransferParams(
                amount = amount,
                currency = currentAccount.currency ?: "",
                feePayMethod = currentTransferType.id ?: "",
                fromAcctNo = currentAccount.accountNo ?: "",
                isQRTransfer = napasTransferParams?.qrTransfer ?: "",
                processDate = processDate ?: "",
                receiveBin = receiveBin,
                remark = remark,
                sendBank = "10698",
                toAcctName = toAcctName,
                toAcctNo = toAcctNo,
                toBankName = currentBankDomain.bankName ?: "",
                username = userProf.getUserName() ?: "",
                tranType = tranType,
                nextApprovers = listAccountApprovers,
            )
            val res = transferUseCase.validateNapasAccountTransfer(params)
            handleResource(res) { data ->
                validateNapasAccountTransferDomain = data
                _validateNapasAccountTransferState.postValue(data)
            }
        }
    }

    fun getReceiveBin(): String {
        val currentBankDomain = currentDataBankDomain
        return when (currentBankDomain?.type) {
            Tags.TransferType.TYPE_IN -> validateNapasAccountDomain?.receiveBin ?: ""
            Tags.TransferType.TYPE_NAPAS -> currentBankDomain.binCode ?: ""
            else -> currentBankDomain?.ebankCode ?: ""
        }
    }

    fun validateNapasAccountTransfer(
        amount: String,
        remark: String,
        toAcctNo: String,
        edtAccountOwner: String?,
        processDate: String?,
        confirm: String? = null,
    ) {
        launchJob(showLoading = true) {
            val currentBankDomain = currentDataBankDomain ?: return@launchJob
            val currentAccount = currentAccountDefaultDomain ?: return@launchJob
            val currentTransferType = currentTypeTransfer ?: return@launchJob
            val isNapasOrInTransfer = isTransferIn() || isTransferNapas()
            val toAcctName = if (isNapasOrInTransfer && validateNapasAccountDomain != null) {
                validateNapasAccountDomain?.accountOwner ?: ""
            } else {
                edtAccountOwner ?: ""
            }
            val tranType = if (isNapasOrInTransfer) {
                currentBankDomain.type ?: ""
            } else {
                Tags.TransferType.TYPE_OUT
            }

            val params = ValidateNapasAccountTransferParams(
                amount = amount,
                currency = currentAccount.currency ?: "",
                feePayMethod = currentTransferType.id ?: "",
                fromAcctNo = currentAccount.accountNo ?: "",
                isQRTransfer = napasTransferParams?.qrTransfer ?: "",
                processDate = processDate ?: "",
                receiveBin = getReceiveBin(),
                remark = remark,
                sendBank = "10698",
                toAcctName = toAcctName,
                toAcctNo = toAcctNo,
                toBankName = currentBankDomain.bankName ?: "",
                username = userProf.getUserName() ?: "",
                tranType = tranType,
                nextApprovers = listAccountApprovers,
                confirm = confirm ?: "",
            )
            val res = transferUseCase.validateNapasAccountTransfer(params)
            handleResource(res) { data ->
                validateNapasAccountTransferDomain = data
                _validateNapasAccountTransferSplitState.postValue(data)
            }
        }
    }

    fun validateNapasCard(cardNumber: String) {
        launchJob(showLoading = true) {
            if (currentCardDefaultDomain != null) {
                val params = ValidateNapasCardParams(
                    branch = "99998",
                    cardNumber = cardNumber,
                    currency = currentCardDefaultDomain?.currency ?: "",
                    debitAccount = currentCardDefaultDomain?.accountNo ?: "",
                    debitFullname = userProf.getFullName() ?: "",
                    username = userProf.getUserName() ?: "",
                )
                val res = transferUseCase.validateNapasCard(params)
                handleResource(res) { data ->
                    _validateNapasCardState.postValue(Resource.Success(data))
                }
            } else {
                getAccountCardList()
            }
        }
    }

    fun validNapasCardTransfer(
        amount: String,
        toCardNo: String,
        remark: String,
        processDate: String?,
    ) {
        launchJob(showLoading = true) {
            val params = ValidateNapasCardTransferParams(
                amount = amount,
                currency = currentCardDefaultDomain?.currency ?: "",
                feePayMethod = currentTypeTransferCard?.id ?: "",
                fromAcctNo = currentCardDefaultDomain?.accountNo ?: "",
                isQRTransfer = "",
                processDate = processDate ?: "0",
                remark = remark,
                sendBank = "12345",
                toBankName = validateNapasCardDomains?.bankName ?: "",
                toCardName = validateNapasCardDomains?.cardOwner ?: "",
                toCardNo = toCardNo,
                username = userProf.getUserName() ?: "",
                nextApprovers = listAccountApprovers,
            )
            val res = transferUseCase.validateNapasCardTransfer(params)
            handleResource(res) { data ->
                validateNapasCardTransferDomains = data
                _validateNapasCardTransferState.postValue(data)
            }
        }
    }

    var tempTransactionAccountList: MutableList<TempTransactionDomains> = mutableListOf()
    var tempTransactionCardList: MutableList<TempTransactionDomains> = mutableListOf()
    var alreadyAttemptedLoading = false
    fun getPaymentTemplateList() {
        val cachedTemplates = if (isTransferAccountTab) {
            tempTransactionAccountList
        } else {
            tempTransactionCardList
        }
        if (alreadyAttemptedLoading) {
            mapIconToListTemplate(cachedTemplates)
            return
        }

        launchJob(showLoading = true) {
            val type = if (isTransferAccountTab) {
                currentDataBankDomain?.type ?: "all"
            } else {
                Tags.TransferType.TYPE_NAPAS_CARD
            }
            val params = GetPaymentTemplateListParams(
                TempTransactionParams(tranType = type),
                username = userProf.getUserName() ?: "",
            )
            val res = transferUseCase.getPaymentTemplateList(params)
            handleResource(res) { data ->
                _getPaymentTemplateListState.postValue(data)
                alreadyAttemptedLoading = true
                if (isTransferAccountTab) {
                    tempTransactionAccountList = data.tempTransactionList
                } else {
                    tempTransactionCardList = data.tempTransactionList
                }
                mapIconToListTemplate(data.tempTransactionList)
            }
        }
    }

    fun contactList() {
        if (listContact.isNotEmpty()) {
            mapIconToListContact(
                contactDomains = listContact,
            )
            return
        } else if (transferCacheManager.getContactList()?.isNotEmpty() == true) {
            mapIconToListContact(
                contactDomains = transferCacheManager.getContactList()?.toMutableList()
                    ?: mutableListOf(),
            )
            return
        }
        launchJob(showLoading = true) {
            val params = ContactListParams(
                username = userProf.getUserName().toString(),
                serviceId = "",
            )
            val res = transferUseCase.contactList(params)
            handleResource(res) { data ->
                listContact = data.contacts
                transferCacheManager.saveContactist(listContact)
                mapIconToListContact(
                    contactDomains = listContact,
                )
            }
        }
    }

    fun getAccountCardList() {
        val cachedAccounts = transferCacheManager.getAccountsCARD()
        if (cachedAccounts?.isNotEmpty() == true) {
            chooseAccountNoCardDialog.setData(
                cachedAccounts.filter { it.status == "0" }.toMutableList(),
            )
            getDefaultAccount(cachedAccounts)?.let {
                currentCardDefaultDomain = it
            }
            val accountListDomain = AccountListDomain(cachedAccounts)
            _accListCardState.postValue(Resource.Success(accountListDomain))
            return
        }
        val accountListParams = AccountListParams(
            accountType = "",
            currencySort = "",
            username = userProf.getUserName() ?: "",
            serviceType = Tags.TransferType.SERVICE_TYPE_TRANSFER_NAPAS_CARD,
        )
        launchJob(showLoading = true) {
            val res = loginUseCase.accList(accountListParams)
            handleResource(res) { data ->
                if (data.accountDefault.isNotEmpty()) {
                    val activeAccounts =
                        data.accountDefault.filter { it.status == "0" }.toMutableList()
                    transferCacheManager.saveAccountsCARD(activeAccounts)
                    chooseAccountNoCardDialog.setData(activeAccounts)
                    getDefaultAccount(data.accountDefault)?.let {
                        currentCardDefaultDomain = it
                    }
                    _accListCardState.postValue(Resource.Success(data))
                }
            }
        }
    }

    fun confirmTransferAccountObjects(
        data: ValidateNapasAccountTransferDomain,
        currentBalance: String?,
    ): MutableList<TransferObject> {
        val transferObjects = mutableListOf<TransferObject>()
        transferObjects.addAll(
            mutableListOf(
                TransferObject(TransferAdapter.TYPE_GROUP_TITLE, "Thông tin tài khoản chuyển"),
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Từ tài khoản",
                    data.fromAcctNo + " - " + data.currency.toString() + " - " + (
                        data.username
                            ?: ""
                        ).toString(),
                ),
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Số dư khả dụng",
                    Utils.g().getDotMoneyHasCcy(currentBalance ?: "", data.currency ?: ""),
                ),
                TransferObject(
                    TransferAdapter.TYPE_GROUP_TITLE,
                    "Thông tin tài khoản thụ hưởng",
                ),
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Tới tài khoản",
                    data.toAcctNo + "\n" + data.toAcctName,
                ),
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Ngân hàng",
                    data.toBankName ?: "",
                ),
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Số tiền",
                    Utils.g().getDotMoneyHasCcy(
                        data.amount ?: "", data.currency ?: "",
                    ) + "\n" + moneyHelper.convertAmountToWords(
                        data.amount ?: "",
                        data.currency ?: "",
                    ),
                ),

                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Phí giao dịch",
                    Utils.g().getDotMoneyHasCcy(data.feeVat ?: "", data.currency ?: ""),
                ),
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Nội dung",
                    data.remark.toString(),
                ),
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Hình thức thu phí",
                    getFeeMethodName(data.feePayMethod ?: ""),
                ),
            ),
        )
        if (TextUtils.isEmpty(data.processDate)) {
            transferObjects.add(
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Thời gian chuyển",
                    todayAsString(dd_MM_yyyy_HH_mm_ss),
                ),
            )
        } else {
            transferObjects.add(
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Thời gian chuyển",
                    "Đặt lịch",
                ),
            )
            transferObjects.add(
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Ngày đặt lịch",
                    data.processDate.toString(),
                ),
            )
        }
        if (data.nextApprovers?.isNotEmpty() == true) {
            val groupedApprovers = data.nextApprovers?.groupBy { it.approverlevel }
            val sortedLevels = groupedApprovers?.keys?.sortedBy { it }
            if (sortedLevels != null) {
                for (level in sortedLevels) {
                    val approvers = groupedApprovers[level] ?: continue
                    val approverNames = approvers.map { it.username }
                    val approversString = approverNames.joinToString("\n")
                    transferObjects.add(
                        TransferObject(
                            TransferAdapter.TYPE_GROUP_CONTENT,
                            title = "Người phê duyệt cấp $level",
                            content = approversString,
                        ),
                    )
                }
            }
        }

        if (Tags.TransferType.TYPE_SPLIT_TRANSFER_YES == data.isSplit) {
            transferObjects.add(
                TransferObject(
                    TransferAdapter.TYPE_GROUP_TITLE,
                    title = "Thông tin tách lệnh giao dịch",
                ),
            )
            transferObjects.add(
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    title = (data.subTransactionCount ?: "") + " giao dịch",
                    content = Utils.g().getDotMoneyHasCcy(
                        data.subSplitTransAmount ?: "", data.currency ?: "",
                    ) + "\n" + moneyHelper.convertAmountToWords(
                        data.subSplitTransAmount ?: "",
                        data.currency ?: "",
                    ),
                ),
            )
            transferObjects.add(
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    title = (data.subTransactionsRemainderCount ?: "") + " giao dịch",
                    content = Utils.g().getDotMoneyHasCcy(
                        data.subTransactionsRemainderAmount ?: "", data.currency ?: "",
                    ) + "\n" + moneyHelper.convertAmountToWords(
                        data.subTransactionsRemainderAmount ?: "",
                        data.currency ?: "",
                    ),
                ),
            )
            try {
                val totalVAT = (data.feeSplitNonVat ?: "").toBigDecimal()
                    .add((data.feeSplitVat ?: "").toBigDecimal())
                transferObjects.add(
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        title = "Phí tách lệnh",
                        content = Utils.g()
                            .getDotMoneyHasCcy(totalVAT.toString(), data.currency ?: ""),
                    ),
                )
            } catch (e: Exception) {
            }

            transferObjects.add(
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    title = "Hình thức thu phí",
                    content = "Phí Ngoài",
                ),
            )
        }
        return transferObjects
    }

    fun confirmTransferCardObjects(
        data: ValidateNapasCardTransferDomains,
        currentBalance: String?,
    ): MutableList<TransferObject> {
        val transferObjects = mutableListOf<TransferObject>()
        transferObjects.addAll(
            mutableListOf(
                TransferObject(TransferAdapter.TYPE_GROUP_TITLE, "Thông tin tài khoản chuyển"),
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Từ tài khoản",
                    data.fromAcctNo + " - " + data.currency.toString() + " - " + (
                        data.username
                            ?: ""
                        ).toString(),
                ),
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Số dư khả dụng",
                    Utils.g().getDotMoneyHasCcy(currentBalance ?: "", data.currency ?: ""),
                ),
                TransferObject(
                    TransferAdapter.TYPE_GROUP_TITLE,
                    "Thông tin tài khoản thụ hưởng",
                ),
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Tới số thẻ",
                    data.toCardNo + "\n" + data.toCardName,
                ),
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Ngân hàng",
                    data.toBankName,
                ),
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Số tiền",
                    Utils.g().getDotMoneyHasCcy(
                        data.amount ?: "", data.currency ?: "",
                    ) + "\n" + moneyHelper.convertAmountToWords(
                        data.amount ?: "",
                        data.currency ?: "",
                    ),

                ),
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Phí giao dịch",
                    Utils.g().getDotMoneyHasCcy(data.feeVat ?: "", data.currency ?: ""),
                ),
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Nội dung",
                    data.remark.toString(),
                ),
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Hình thức thu phí",
                    getFeeMethodName(data.feePayMethod ?: ""),
                ),
            ),

        )
        transferObjects.add(
            TransferObject(
                TransferAdapter.TYPE_GROUP_CONTENT,
                "Thời gian chuyển",
                todayAsString(dd_MM_yyyy_HH_mm_ss),
            ),
        )
        if (data.nextApprovers?.isNotEmpty() == true) {
            val groupedApprovers = data.nextApprovers?.groupBy { it.approverlevel }
            val sortedLevels = groupedApprovers?.keys?.sortedBy { it }
            if (sortedLevels != null) {
                for (level in sortedLevels) {
                    val approvers = groupedApprovers[level] ?: continue
                    val approverNames = approvers.map { it.username }
                    val approversString = approverNames.joinToString("\n")
                    transferObjects.add(
                        TransferObject(
                            TransferAdapter.TYPE_GROUP_CONTENT,
                            title = "Người phê duyệt cấp $level",
                            content = approversString,
                        ),
                    )
                }
            }
        }
        return transferObjects
    }

    private fun mapIconToListTemplate(tempTransactionDomains: MutableList<TempTransactionDomains>) {
        val dataBankMap =
            listDataBanks.filterNot { !it.binCode.isNullOrEmpty() || !it.icon.isNullOrEmpty() || !it.ebankCode.isNullOrEmpty() }
                .flatMap { bank ->
                    listOf(
                        bank.binCode to bank.icon,
                        bank.ebankCode to bank.icon,
                    )
                }.toMap()
        val listTemplateWithIcons = tempTransactionDomains.map { tempTransactionItem ->
            dataBankMap[tempTransactionItem.toBankCode]?.let { newIcon ->
                tempTransactionItem.copy(icon = newIcon)
            } ?: tempTransactionItem
        }
        shampleAdapter.setData(listTemplateWithIcons.toMutableList())
        listShampleFilter = listTemplateWithIcons as MutableList<TempTransactionDomains>
    }

    private fun mapIconToListContact(contactDomains: MutableList<ContactDomains>) {
        val dataBankMap =
            listDataBanks.filter { !it.binCode.isNullOrEmpty() && !it.icon.isNullOrEmpty() && !it.ebankCode.isNullOrEmpty() }
                .flatMap { bank ->
                    listOf(
                        bank.binCode to Pair(bank.icon, bank.shortName),
                        bank.ebankCode to Pair(bank.icon, bank.shortName),
                    )
                }.toMap()

        listContact = contactDomains.map { contact ->
            dataBankMap[contact.bank]?.let { (newIcon, newBankName) ->
                contact.copy(icon = newIcon, bankName = newBankName)
            } ?: contact
        }.toMutableList()

        when {
            isTransferAccountTab -> {
                when (currentDataBankDomain?.type) {
                    Tags.TransferType.TYPE_IN -> listContact.filter { it.trantype == Tags.TransferType.TYPE_IN }
                    Tags.TransferType.TYPE_NAPAS -> listContact.filter {
                        it.trantype == Tags.TransferType.TYPE_NAPAS_ACCOUNT && it.bank == currentDataBankDomain?.binCode
                    }

                    Tags.TransferType.TYPE_OUT -> listContact.filter {
                        it.trantype == Tags.TransferType.TYPE_OUT && it.bank == currentDataBankDomain?.binCode
                    }

                    else -> listContact.filter { it.trantype != Tags.TransferType.TYPE_NAPAS_CARD && it.trantype != Tags.TransferType.TYPE_PAYMENT_ORDER_TRANSFER }
                }.map { it.copy() }.toMutableList().also { contacts ->
                    setDataContactAccAdapter(contacts)
                }
            }

            else -> {
                listContact.filter { it.trantype == Tags.TransferType.TYPE_NAPAS_CARD }
                    .map { it.copy() }.toMutableList().also { contacts ->
                        setDataContactCardAdapter(contacts)
                    }
            }
        }
    }

    fun setDataContactCardAdapter(contacts: MutableList<ContactDomains>) {
        contactCardAdapter.setData(contacts.take(5).toMutableList())
        listContactFilter = contacts
    }

    fun setDataContactAccAdapter(contacts: MutableList<ContactDomains>) {
        contactAccountAdapter.setData(contacts.take(5).toMutableList())
        listContactFilter = contacts
    }

    fun getNextApproversListString(): String {
        return if (listAccountApprovers.isNotEmpty()) {
            listAccountApprovers.joinToString(", ") { it.username ?: "" }
        } else {
            "Theo đăng ký với VietinBank"
        }
    }

    fun clearListApprover() {
        listAccountApprovers = emptyList()
    }

    fun isEnableSaveAccount(receiveAccount: String): Boolean {
        return if (isTransferAccountTab) {
            !listContact.any { it.account == receiveAccount }
        } else {
            !listContact.any { it.cardnumber == receiveAccount }
        }
    }

    fun findMatchingDataBankDomain(
        bank: String?,
        trantype: String?,
    ): DataBankDomain? = when (trantype) {
        Tags.TransferType.TYPE_IN -> listDataBanks.find { it.type == trantype }
        else -> listDataBanks.find { it.binCode == bank || it.ebankCode == bank }
    }

    fun getDefaultAccount(accountDefault: MutableList<AccountDefaultDomain>): AccountDefaultDomain? {
        return accountDefault.maxByOrNull {
            it.currentBalance?.toDoubleOrNull() ?: Double.MIN_VALUE
        }
    }

    fun setUpBranchDialog() {
        chooseBankDialog.showFullDialog = true
        viewPagerContactAndSampleBottomSheet.showFullDialog = true
    }

    fun setDataFeePaymentMethod() {
        dataTypeTransfer.clear()
        dataTypeTransfer.add(Tags.TransferType.dataSetFillDataOUR)
        dataTypeTransfer.add(Tags.TransferType.dataSetFillDataBEN)
        currentTypeTransfer = Tags.TransferType.dataSetFillDataOUR
        chooseTransferTypeDialog.setData(dataTypeTransfer)
    }

    fun chooseTypeFeeBEN() {
        currentTypeTransfer = Tags.TransferType.dataSetFillDataOUR
    }

    fun setDataTypeScheduleTime(chooseTimeScheduleTypeDialog: DataFillBottomSheetFragment) {
        dataTypeScheduleTime.clear()
        dataTypeScheduleTime.add(Tags.TransferType.dataSetFillDataTimeNow)
        if (isTransferAccountTab) {
            dataTypeScheduleTime.add(Tags.TransferType.dataSetFillDataTimeSchedule)
            currentTypeAccountSchedule = Tags.TransferType.dataSetFillDataTimeSchedule
        } else {
            currentTypeCardSchedule = Tags.TransferType.dataSetFillDataTimeNow
        }
        chooseTimeScheduleTypeDialog.setData(dataTypeScheduleTime)
    }

    fun setDefaultCcurrentTypeAccountSchedule() {
        currentTypeAccountSchedule = Tags.TransferType.dataSetFillDataTimeNow
    }

    fun setDataFeePaymentCardMethod() {
        dataTypeTransferCard.clear()
        dataTypeTransferCard.add(Tags.TransferType.dataSetFillDataOUR)
        dataTypeTransferCard.add(Tags.TransferType.dataSetFillDataBEN)
        currentTypeTransferCard = Tags.TransferType.dataSetFillDataOUR
        chooseTransferCardTypeDialog.setData(dataTypeTransferCard)
    }

    fun setEnableContactType() {
        with(Tags.TransferType) {
            listOf(contactTypeIn, contactTypeNPA, contactTypeNPC).forEach {
                it.isEnable = false
                it.isSelected = false
            }

            if (isTransferAccountTab) {
                contactTypeIn.apply {
                    isEnable = true
                    isSelected = true
                }
                contactTypeNPA.isEnable = true
            } else {
                contactTypeNPC.apply {
                    isEnable = true
                    isSelected = true
                }
            }
        }
    }

    fun canOpenTimeScheduleTypeDialog() =
        (currentDataBankDomain != null && (!isTransferNapas() || validateNapasAccountDomain == null))

    fun canOpenTransferTypeAccDialog() =
        !(isTransferIn() && currentTypeAccountSchedule == Tags.TransferType.dataSetFillDataTimeSchedule) || (isTransferNomal())

    override fun onDisplayErrorMessage(exception: AppException) {
        if (exception is AppException.ApiException && exception.requestPath == Constants.MB_VALIDATE_ACCOUNT) {
            if (Tags.TransferType.ERROR_CODE_NORMAL_TRANSFER == exception.code) {
                validateNapasAccountDomain = null
                _handleErrorvalidateNapasAccountState.postValue(exception)
            }
        } else if (exception is AppException.ApiException && exception.requestPath == Constants.MB_VALIDATE_NAPAS_CARD) {
            _validateNapasCardState.postValue(
                Resource.Error(
                    exception.message.toString(),
                    exception.code.toString(),
                    exception,
                ),
            )
        } else if (exception is AppException.ApiException && exception.requestPath == Constants.MB_ACCOUNT_LIST) {
            if (isTransferAccountTab) {
                _accListState.postValue(
                    Resource.Error(
                        exception.message.toString(),
                        exception.code.toString(),
                        exception,
                    ),
                )
            } else {
                _accListCardState.postValue(
                    Resource.Error(
                        exception.message.toString(),
                        exception.code.toString(),
                        exception,
                    ),
                )
            }
        }
        super.onDisplayErrorMessage(exception)
    }

    fun validateTransferFields(
        receiveAccount: String?,
        amount: String?,
        remark: String?,
        edtAccountOwner: String?,
    ): String? {
        return when {
            currentDataBankDomain == null -> "Chọn ngân hàng nhận"
            isTransferNomal() && edtAccountOwner.isNullOrBlank() -> "Chưa có tên người thụ hưởng"
            currentAccountDefaultDomain == null -> "Chọn từ tài khoản"
            receiveAccount.isNullOrBlank() -> "Nhập tới tài khoản"
            amount.isNullOrBlank() -> "Nhập số tiền"
            (
                amount.toString().replace(",", "").toBigDecimalOrNull()
                    ?: "0".toBigDecimal()
                ) <= 2000.toBigDecimal() && "VND" == (
                currentAccountDefaultDomain?.currency
                    ?: ""
                ) -> "Số tiền chuyển khoản thấp hơn hạn mức tối thiểu/giao dịch theo Quy định của Napas (tối thiểu 2,000VND/giao dịch)"

            remark.isNullOrBlank() -> "Nhập nội dung"
            else -> null
        }
    }

    fun validateTransferCardFields(
        amount: String?,
        toCardNo: String?,
        remark: String?,
    ): String? {
        return when {
            currentCardDefaultDomain == null -> "Chọn từ tài khoản"
            toCardNo.isNullOrBlank() -> "Nhập số thẻ"
            validateNapasCardDomains == null -> "Chưa có tên người thụ hưởng"
            amount.isNullOrBlank() -> "Nhập số tiền"
            (
                amount.toString().replace(",", "").toBigDecimalOrNull()
                    ?: "0".toBigDecimal()
                ) <= 2000.toBigDecimal() && "VND" == (
                currentAccountDefaultDomain?.currency
                    ?: ""
                ) && isTransferNapas() -> "Số tiền chuyển khoản thấp hơn hạn mức tối thiểu/giao dịch theo Quy định của Napas (tối thiểu 2,000VND/giao dịch)"

            remark.isNullOrBlank() -> "Nhập nội dung"
            else -> null
        }
    }

    fun isTransferIn() = Tags.TransferType.TYPE_IN == currentDataBankDomain?.type
    fun isTransferOut() = Tags.TransferType.TYPE_OUT == currentDataBankDomain?.type
    fun isTransferNapas() = Tags.TransferType.TYPE_NAPAS == currentDataBankDomain?.type
    fun isTransferCard() = Tags.TransferType.TYPE_NAPAS_CARD == currentDataBankDomain?.type
    fun isTransferNomal() = Tags.TransferType.TYPE_NORMAL == currentDataBankDomain?.type
    fun getFeeMethodName(id: String): String {
        return when (id) {
            "1" -> Tags.TransferType.dataSetFillDataOUR.name
            "0" -> Tags.TransferType.dataSetFillDataBEN.name
            else -> "Không xác định" // "Unknown" in Vietnamese
        }
    }

    // Trạng thái đang scan qr
    private val _isScanning = MutableStateFlow<Boolean>(false)
    fun resetScanning() {
        _qrScanResult.value = null
        _isScanning.value = false
    }

    private val _qrScanResult = MutableStateFlow<String?>(null)
    val qrScanResult = _qrScanResult.asStateFlow()
    fun submitQrResult(result: String) {
        if (_isScanning.value || result.isBlank() || result.isEmpty()) return
        _isScanning.value = true
        _qrScanResult.value = result
    }

    var napasTransferParams: NapasTransferParams? = null
    fun convertQrDomainToTransModel(domain: QrCodeDomain, qrDetected: String?) {
        napasTransferParams = NapasTransferParams().apply {
            receiveBank = domain.binCode
            receiveAcct = domain.accountNumber
            amount = domain.amount
            remark = domain.remark
            currency = domain.currency
            qrTransfer = qrDetected
            // case scan qr + amount != 0 -> not allow edit amount
            allowEditAmount = amount.isNullOrEmpty() || "0" == amount || qrTransfer.isNullOrEmpty()
        }
    }
}