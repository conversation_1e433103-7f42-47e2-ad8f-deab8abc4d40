package com.vietinbank.feture_maker.maker_ui.create

import android.Manifest
import android.app.Activity
import android.content.ContentValues
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.text.Editable
import android.text.InputFilter
import android.text.TextUtils
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.viewbinding.ViewBinding
import com.google.gson.Gson
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.extensions.getNextDateTime
import com.vietinbank.core_common.extensions.isFileSizeValid
import com.vietinbank.core_common.extensions.loadDrawable
import com.vietinbank.core_common.extensions.loadUrl
import com.vietinbank.core_common.extensions.onSearchTextChanged
import com.vietinbank.core_common.extensions.showDateAndTimePickerDialog
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.Utils.asciiOnlyFilter
import com.vietinbank.core_domain.models.login.AccountDefaultDomain
import com.vietinbank.core_domain.models.maker.ContactDomains
import com.vietinbank.core_domain.models.maker.DataBankDomain
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_maker.R
import com.vietinbank.feature_maker.databinding.FragmentMakerPaymentOrderBinding
import com.vietinbank.feture_maker.maker_ui.adapter.BankAdapter
import com.vietinbank.feture_maker.maker_ui.bottomSheet.BranchListBottomSheetFragment
import com.vietinbank.feture_maker.maker_ui.bottomSheet.NextApproverBottomSheetFragment
import com.vietinbank.feture_maker.maker_ui.viewmodel.PaymentOrderViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class PaymentOrderFragment : BaseFragment<PaymentOrderViewModel>() {
    override val viewModel: PaymentOrderViewModel by activityViewModels()
    override val useCompose: Boolean = false
    var nextApproverBottomSheetAccFragment = NextApproverBottomSheetFragment()
    val branchListBottomSheetFragment = BranchListBottomSheetFragment()

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override fun inflateViewBinding(inflater: LayoutInflater, container: ViewGroup?): ViewBinding {
        return FragmentMakerPaymentOrderBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initData()
        initListener()
//        viewModel.getNapasBankList()
        viewModel.contactList()
        viewModel.getAccList()
        viewModel.getBranch()
    }

    private fun initView() {
        (binding as FragmentMakerPaymentOrderBinding).apply {
            openCustomToolbar.tvTitleToolbar.text = "Lệnh chi"
            viewModel.setUpBranchDialog()
            viewModel.setDataFeePaymentMethod()
            viewModel.setDataTypeScheduleTime()
            viewModel.setDataTypeUpdate()
            imgUpload.loadDrawable(value = R.drawable.ic_upload, isCache = false)
            tvTypeFee.text = viewModel.currentTypeTransfer?.name ?: ""
            tvTimeSchedule.text = viewModel.currentTypePaymentOrderSchedule?.name ?: ""
            val layoutManager = LinearLayoutManager(context, GridLayoutManager.HORIZONTAL, false)
            rcvListBankDefault.layoutManager = layoutManager
            rcvListBankDefault.adapter = viewModel.bankDefaultAdapter
            viewModel.bankDefaultAdapter.setData(mutableListOf())
            rcvContact.adapter = viewModel.contactPaymentAdapter
            viewModel.contactPaymentAdapter.setData(mutableListOf())
            if (viewModel.imageBase64Object != null) {
                tvContentUpload.visibility = View.VISIBLE
                tvContentUpload.text = viewModel.imageBase64Object?.fileName ?: ""
                imgUpload.loadDrawable(
                    value = com.vietinbank.core_ui.R.drawable.ic_close,
                    isCache = false,
                )
            }
        }
    }

    private fun initData() {
        (binding as FragmentMakerPaymentOrderBinding).apply {
            viewModel.getBranchState.observe(viewLifecycleOwner) { state ->
                branchListBottomSheetFragment.setData(state.data)
            }
            viewModel.clickAccountShampleFromContactFragmentState.observe(viewLifecycleOwner) {
                it.let {
                    viewModel.findMatchingDataBankDomain(
                        bank = it.toBankCode,
                        trantype = it.tranType,
                    )?.let { setTextBankInfo(it) }
                    edtReceiveAccount.setText(it.toAccountNo.toString())
                    val formattedText = Utils.g().getDotMoney(it.amount ?: "")
                    edtAmount.setText(formattedText)
                    edtRemark.setText(it.content ?: "")
                }
            }

            viewModel.clickItemBankFromContactFragmentState.observe(viewLifecycleOwner) { contactDomains ->
                onItemContactClicked(contactDomains)
            }

            viewModel.accListState.observe(viewLifecycleOwner) { state ->
                when (state) {
                    is Resource.Success -> {
                        viewModel.getDefaultAccount(state.data.accountDefault)?.let {
                            clFromAccount.visibility = View.VISIBLE
                            setTextAccountInfo(viewModel.currentAccountDefaultDomain)
                        }
                    }

                    is Resource.Error -> {
                        clFromAccount.visibility = View.GONE
                    }
                }
            }

            viewModel.handleDatePickerPaymentOrderState.observe(viewLifecycleOwner) {
                tvDaySchedule.text = it
            }

            viewModel.paymentOrderTransferState.observe(viewLifecycleOwner) { state ->
                // listConfirm
                val listConfirmTransferObjects = viewModel.confirmPaymentOrderTransferObjects(
                    state,
                    viewModel.currentAccountDefaultDomain?.currentBalance,
                )
                val listConfirmJson = Gson().toJson(listConfirmTransferObjects)
                // create bundle
                val bundle = Bundle().apply {
                    putString(
                        Tags.TransferType.TYPE_TRANSFER,
                        Tags.TransferType.TYPE_PAYMENT_ORDER,
                    )
//                    putBoolean(
//                        Tags.TransferType.IS_CONTACT_CREATE,
//                        viewModel.isSave
//                    )
//                    state.customercode = (edtCustomerCode.text.toString())
                    putString(
                        Tags.TransferType.CONFIRM_OBJECT,
                        Utils.g().provideGson().toJson(state),
                    )
                    putString(
                        Tags.TransferType.LIST_CONFIRM_OBJECT,
                        listConfirmJson,
                    )
                }
                appNavigator.goToMakerConfirmTransferFragment(bundle)
            }

            viewModel.getFileUriState.observe(viewLifecycleOwner) { uri ->
                if (uri != null && uri != Uri.EMPTY) {
                    tvContentUpload.visibility = View.VISIBLE
                    tvContentUpload.text = viewModel.imageBase64Object?.fileName ?: ""
                    imgUpload.loadDrawable(
                        value = com.vietinbank.core_ui.R.drawable.ic_close,
                        isCache = false,
                    )
                } else {
                    viewModel.imageBase64Object = null
                    tvContentUpload.visibility = View.GONE
                    tvContentUpload.text = ""
                    imgUpload.loadDrawable(value = R.drawable.ic_upload, isCache = false)
                }
            }
            viewModel.nextStatusTransactionByRuleState.observe(viewLifecycleOwner) { data ->
                if (data.nextApprovers?.size == 0) {
                    showNoticeDialog("Hiện không có người phê duyệt tiếp theo")
                } else {
                    data.nextApprovers?.let { nextApproverBottomSheetAccFragment.setData(it) }
                    nextApproverBottomSheetAccFragment.show(
                        childFragmentManager,
                        "chooseTimeScheduleTypeDialog",
                    )
                }
            }
        }
    }

    private fun initListener() {
        (binding as FragmentMakerPaymentOrderBinding).apply {
            edtRemark.filters = arrayOf(asciiOnlyFilter(), InputFilter.LengthFilter(146))
            edtReceiveAccount.filters = arrayOf(asciiOnlyFilter())
            edtReceiveBank.filters = arrayOf(asciiOnlyFilter(), InputFilter.LengthFilter(146))
            edtAccountOwner.filters = arrayOf(asciiOnlyFilter())

            edtRemark.onSearchTextChanged { s ->
                tvRemarkCount.text = s.length.toString() + "/146"
            }
            openCustomToolbar.btnBack.setThrottleClickListener {
                onBackPressed()
            }

            clBank.setThrottleClickListener {
                viewModel.chooseBankDialog.show(parentFragmentManager, "choooseBank")
            }
            viewModel.bankDefaultAdapter.onClickItem = {
                setTextBankInfo(it)
            }
            viewModel.chooseBankDialog.setOnClickItemListener {
                setTextBankInfo(it)
            }
            clDaySchedule.setThrottleClickListener {
                context?.let {
                    it.showDateAndTimePickerDialog { selectedDate ->
                        viewModel.handleDatePickerPaymentOrderState.postValue(selectedDate)
                    }
                }
            }
            viewModel.chooseTimeScheduleTypeDialog.setOnClickItemListener {
                tvTimeSchedule.text = it.name
                viewModel.currentTypePaymentOrderSchedule = it
                if ("0" == it.id) {
                    clDaySchedule.visibility = View.GONE
                    tvDaySchedule.text = ""
                } else {
                    clDaySchedule.visibility = View.VISIBLE
                    tvDaySchedule.text = getNextDateTime()
                }
            }
            titleTypeFee.setThrottleClickListener {
                viewModel.choosePaymentOrderTypeDialog.show(
                    parentFragmentManager,
                    "choosePaymentOrderTypeDialog",
                )
            }
            titleTimeSchedule.setThrottleClickListener {
                viewModel.chooseTimeScheduleTypeDialog.show(
                    parentFragmentManager,
                    "chooseTimeScheduleTypeDialog",
                )
            }
            tvTypeFee.setThrottleClickListener {
                titleTypeFee.callOnClick()
            }
            tvTimeSchedule.setThrottleClickListener {
                titleTimeSchedule.callOnClick()
            }
            tvAccountNumber.setThrottleClickListener {
                viewModel.chooseAccountNoDialog.show(parentFragmentManager, "chooseAccountNo")
            }

            viewModel.chooseAccountNoDialog.setOnClickItemListener {
                viewModel.currentAccountDefaultDomain = it
                setTextAccountInfo(viewModel.currentAccountDefaultDomain)
            }
            clBranch.setThrottleClickListener {
                branchListBottomSheetFragment.show(
                    parentFragmentManager,
                    "branchListBottomSheetFragment",
                )
            }
            branchListBottomSheetFragment.setOnClickItemListener {
                viewModel.currentBranchDomains = it
                tvBranchContent.visibility = View.VISIBLE
                tvBranchContent.text =
                    "${(viewModel.currentBranchDomains?.branchId ?: "")} - ${(viewModel.currentBranchDomains?.branchName ?: "")} - ${(viewModel.currentBranchDomains?.provinceName ?: "")}"
            }
            viewModel.choosePaymentOrderTypeDialog.setOnClickItemListener {
                tvTypeFee.text = it.name
                viewModel.currentTypeTransfer = it
            }
            viewModel.chooseTimeScheduleTypeDialog.setOnClickItemListener {
                tvTimeSchedule.text = it.name
                viewModel.currentTypePaymentOrderSchedule = it
                if ("0" == it.id) {
                    clDaySchedule.visibility = View.GONE
                    tvDaySchedule.text = ""
                } else {
                    clDaySchedule.visibility = View.VISIBLE
                    tvDaySchedule.text = getNextDateTime()
                }
            }

            edtReceiveAccount.onSearchTextChanged { s ->
                clearListApprover()
                val newList = if (s.isEmpty()) {
                    viewModel.listContactFilter
                } else {
                    viewModel.listContactFilter.filter {
                        (it.account ?: "").lowercase().contains(
                            s.lowercase(),
                        ) || (it.placetoreceive ?: "").lowercase().contains(
                            s.lowercase(),
                        ) || (it.payeename ?: "").lowercase().contains(
                            s.lowercase(),
                        )
                    }.toMutableList()
                }
                viewModel.contactPaymentAdapter.setData(newList.take(5).toMutableList())
            }
            edtAmount.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                }

                override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                }

                override fun afterTextChanged(p0: Editable?) {
                    if (!TextUtils.isEmpty(p0)) {
                        val formattedText = Utils.g().getDotMoney((p0 ?: "").toString())
                        edtAmount.removeTextChangedListener(this)
                        if (edtAmount.text.toString().replace(",", "").length >= 16) {
                            edtAmount.setText(
                                Utils.g()
                                    .getDotMoney(formattedText.replace(",", "").substring(0, 16)),
                            )
                            edtAmount.setSelection(edtAmount.text.toString().length)
                        } else {
                            edtAmount.setText(formattedText)
                            tvAmountToWord.text =
                                viewModel.readAmountInWord(p0.toString().replace(",", ""))
                            edtAmount.setSelection(formattedText.length)
                        }

                        edtAmount.addTextChangedListener(this)
                    } else {
                        tvAmountToWord.text = ""
                    }
                }
            })
            clUpload.setThrottleClickListener {
                if (viewModel.imageBase64Object == null) {
                    viewModel.chooseUploadTypeDialog.show(
                        parentFragmentManager,
                        "chooseUploadTypeDialog",
                    )
                } else {
                    requireContext().let {
                        viewModel.getFileUriState(ctx = it, uri = Uri.EMPTY)
                    }
                }
            }
            viewModel.chooseUploadTypeDialog.setOnClickItemListener {
                viewModel.currentTypeUpdate = it
                when (it.id) {
                    "0" -> {
                        requestCameraPermission()
                    }

                    "1" -> {
                        openImagePicker()
                    }

                    "2" -> {
                        openFileChooser()
                    }

                    else -> Unit
                }
            }
            viewModel.contactPaymentAdapter.onClickItem = {
                onItemContactClicked(it)
            }
            btnCreateMaker.setThrottleClickListener {
                val errorMessage = viewModel.validateTransferFields(
                    edtReceiveBank.text?.toString(),
                    edtReceiveAccount.text?.toString(),
                    edtAmount.text?.toString(),
                    edtRemark.text?.toString(),
                    edtAccountOwner.text?.toString(),
                )

                if (errorMessage != null) {
                    showNoticeDialog(errorMessage)
                    return@setThrottleClickListener
                }

                viewModel.validatePaymentOrderTransfer(
                    amount = edtAmount.text.toString().replace(",", ""),
                    content = edtRemark.text.toString(),
                    toAccountNo = edtReceiveAccount.text.toString(),
                    processTime = tvDaySchedule.text.toString(),
                    toAccountName = edtReceiveAccount.text.toString(),
                    edtReceiveBank = edtReceiveBank.text.toString(),
                )
            }
            btnOpenContact.setThrottleClickListener {
                viewModel.viewPagerContactAndSamplePaymentFragment.show(
                    childFragmentManager,
                    "chooseContactAndSample",
                )
            }
            clNextApprover.setThrottleClickListener {
                val errorMessage = viewModel.validateNextStatusTransactionByRuleFields(
                    amount = edtAmount.text.toString().replace(",", ""),
                    toAccountNo = edtReceiveAccount.text.toString(),
                )

                if (errorMessage != null) {
                    showNoticeDialog(errorMessage)
                    return@setThrottleClickListener
                }
                viewModel.nextStatusTransactionByRule(
                    amount = edtAmount.text.toString().replace(",", ""),
                    toAccountNo = edtReceiveAccount.text.toString(),
                )
            }
            nextApproverBottomSheetAccFragment.setOnClickItemListener { it ->
                viewModel.listAccountApprovers = it
                contentNextApprover.text = viewModel.getNextApproversListString()
            }
        }
    }

    private fun FragmentMakerPaymentOrderBinding.clearListApprover() {
        viewModel.clearListApprover()
        nextApproverBottomSheetAccFragment.setData(mutableListOf())
        contentNextApprover.text = "Theo đăng ký với VietinBank"
    }

    private fun setTextBankInfo(currentBank: DataBankDomain) {
        (binding as FragmentMakerPaymentOrderBinding).apply {
            clearListApprover()
            currentBank.let {
                clBank.visibility = View.VISIBLE
                viewModel.currentDataBankDomain = currentBank
                edtReceiveAccount.setText("")
                tvRecieveBank.text = (viewModel.currentDataBankDomain?.shortName ?: "")
                tvRecieveBankName.text = (viewModel.currentDataBankDomain?.bankName ?: "")
                tvType.text = BankAdapter.getTypeName(
                    viewModel.currentDataBankDomain?.type ?: "",
                )
                imgIconBank.loadUrl(viewModel.currentDataBankDomain?.icon ?: "")
            }
        }
    }

    private fun setTextAccountInfo(it: AccountDefaultDomain?) {
        (binding as FragmentMakerPaymentOrderBinding).apply {
//            edtAmount.setText("")
            tvAccountNumber.text = it?.accountNo ?: ""
            tvCurrentBalnce.text =
                Utils.g().getDotMoneyHasCcy((it?.currentBalance ?: "0"), it?.currency ?: "")
            tvCurrentcyAmount.text = it?.currency.toString()
            viewModel.setCcy(it?.currency ?: "")
        }
        viewModel.sortListBranch()
    }

    private fun requestCameraPermission() {
        context?.let { ctx ->
            when {
                ContextCompat.checkSelfPermission(
                    ctx, Manifest.permission.CAMERA,
                ) == PackageManager.PERMISSION_GRANTED -> {
                    takePictureIntent()
                }

                shouldShowRequestPermissionRationale(Manifest.permission.CAMERA) -> {
                    Toast.makeText(
                        context,
                        "Cần cung cấp quyền để chụp ảnh",
                        Toast.LENGTH_LONG,
                    ).show()

                    requestCameraPermissionLauncher.launch(Manifest.permission.CAMERA)
                }

                else -> {
                    requestCameraPermissionLauncher.launch(Manifest.permission.CAMERA)
                }
            }
        }
    }

    private val requestCameraPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission(),
    ) { isGranted ->
        if (isGranted) {
            requestPermissionExternalStore()
            requestPermissionExternalStoreListener { allow ->
                if (allow) {
                    context?.let {
                        takePictureIntent()
                    }
                }
            }
        } else {
            Toast.makeText(
                context,
                "Đã từ chối quyền chụp ảnh",
                Toast.LENGTH_LONG,
            ).show()
        }
    }

    private fun takePictureIntent() {
        val contentResolver = activity?.contentResolver ?: return
        val values = ContentValues().apply {
            put(MediaStore.Images.Media.TITLE, "New Picture")
            put(MediaStore.Images.Media.DESCRIPTION, "From Camera")
        }
        viewModel.imageUri =
            contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values)
        viewModel.imageUri?.let { uri ->
            takePicture.launch(uri)
        }
    }

    private val takePicture =
        registerForActivityResult(ActivityResultContracts.TakePicture()) { success ->
            if (success) {
                viewModel.imageUri?.let { uri ->
                    requireContext().let {
                        viewModel.getFileUriState(ctx = it, uri = uri)
                    }
                }
            }
        }

    private fun openImagePicker() {
        val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
        imagePickerLauncher.launch(intent)
    }

    private val imagePickerLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult(),
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val imageUri = result.data?.data
            imageUri?.let { uri ->
                requireContext().let {
                    viewModel.getFileUriState(ctx = it, uri = uri)
                }
            }
        }
    }

    private fun openFileChooser() {
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
            addCategory(Intent.CATEGORY_OPENABLE)
            type = "*/*"
            putExtra(
                Intent.EXTRA_MIME_TYPES,
                arrayOf(
                    "application/pdf", // PDF
                    "application/zip", // ZIP
                    "application/vnd.ms-excel", // XLS
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // XLSX
                    "text/csv", // CSV
                    "application/msword", // DOC
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // DOCX
                ),
            )
        }
        filePickerLauncher.launch(intent)
    }

    private val filePickerLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult(),
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                requireContext().let {
                    if (isFileSizeValid(it, uri)) {
                        viewModel.getFileUriState(ctx = it, uri = uri)
                    } else {
                        Toast.makeText(
                            it,
                            "File bắt buộc nhỏ hơn 5MB",
                            Toast.LENGTH_SHORT,
                        ).show()
                    }
                }
            }
        }
    }

    private fun onItemContactClicked(contactDomains: ContactDomains) {
        (binding as FragmentMakerPaymentOrderBinding).apply {
            contactDomains.let {
                viewModel.findMatchingDataBankDomain(
                    bank = contactDomains.bank,
                    trantype = contactDomains.trantype,
                )?.let { setTextBankInfo(it) }
                edtReceiveAccount.setText(contactDomains.account.toString())
                edtAccountOwner.setText(contactDomains.payeename.toString())
            }
        }
    }

    override fun onBackPressed(): Boolean {
        appNavigator.goToHome()
        return true
    }
}