package com.vietinbank.feture_maker.maker_ui.bottomSheet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.extensions.loadDrawable
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountTransferDomain
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.BaseBottomSheetFragment
import com.vietinbank.feature_maker.databinding.FragmentSplitTransferBinding
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SplitTransferBottomSheetFragment : BaseBottomSheetFragment() {
    private var _binding: FragmentSplitTransferBinding? = null
    private val binding get() = _binding!!
    private var onItemListener: ((Boolean) -> Unit)? = null
    var validateNapasAccountTransferDomain: ValidateNapasAccountTransferDomain? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        _binding = FragmentSplitTransferBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun initView() {
    }

    fun setData(validateNapasAccountTransferDomain: ValidateNapasAccountTransferDomain) {
        this.validateNapasAccountTransferDomain = validateNapasAccountTransferDomain
    }

    override fun initData() {
        binding.apply {
            btnBack.background =
                ContextCompat.getDrawable(requireContext(), R.drawable.bg_button_disable)
            viewCollapse.loadDrawable(R.drawable.ic_drop_down)
            rbSplit.isChecked = true
            tvSubTransactionCount.text =
                (validateNapasAccountTransferDomain?.subTransactionCount ?: "") + " giao dịch"
            contentubTransactionCount.text = Utils.g().getDotMoneyHasCcy(
                validateNapasAccountTransferDomain?.subSplitTransAmount ?: "",
                (validateNapasAccountTransferDomain?.currency ?: ""),
            )
            tvSubTransactionsRemainderAmount.text =
                (
                    validateNapasAccountTransferDomain?.subTransactionsRemainderCount
                        ?: ""
                    ) + " giao dịch"
            contenSubTransactionsRemainderAmount.text =
                Utils.g().getDotMoneyHasCcy(
                    validateNapasAccountTransferDomain?.subTransactionsRemainderAmount ?: "",
                    (validateNapasAccountTransferDomain?.currency ?: ""),
                )
            contentTotalAmount.text =
                Utils.g().getDotMoneyHasCcy(
                    validateNapasAccountTransferDomain?.amount ?: "",
                    (validateNapasAccountTransferDomain?.currency ?: ""),
                )
            contentFeeSplit.text = Tags.TransferType.dataSetFillDataOUR.name
        }
    }

    var isCollapse = false
    override fun initListener() {
        binding.apply {
            clSplit.setThrottleClickListener {
                if (!rbSplit.isChecked) {
                    requireContext().let {
                        rbSplit.isChecked = true
                        rbNormal.isChecked = false
                        clSplit.background =
                            ContextCompat.getDrawable(it, R.drawable.boder_radius_5)
                        clNormal.background =
                            ContextCompat.getDrawable(
                                it,
                                R.drawable.boder_blue09_radius_5,
                            )
                    }
                }
            }
            clNormal.setThrottleClickListener {
                if (!rbNormal.isChecked) {
                    requireContext().let {
                        rbNormal.isChecked = true
                        rbSplit.isChecked = false
                        clNormal.background =
                            ContextCompat.getDrawable(it, R.drawable.boder_radius_5)
                        clSplit.background =
                            ContextCompat.getDrawable(
                                it,
                                R.drawable.boder_blue09_radius_5,
                            )
                    }
                }
            }

            btnBack.setThrottleClickListener {
                dismiss()
            }
            viewCollapse.setThrottleClickListener {
                isCollapse = !isCollapse
                if (isCollapse) {
                    viewCollapse.loadDrawable(R.drawable.ic_drop_down)
                    tvNoteSplit.maxLines = 3
                } else {
                    viewCollapse.loadDrawable(R.drawable.ic_drop_up)
                    tvNoteSplit.maxLines = 100
                }
            }
            btnContinue.setThrottleClickListener {
                dismiss()
                onItemListener?.invoke(rbSplit.isChecked)
            }
        }
    }

    fun setOnClickItemListener(action: ((Boolean) -> Unit)?) {
        onItemListener = action
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        onItemListener = null
    }
}