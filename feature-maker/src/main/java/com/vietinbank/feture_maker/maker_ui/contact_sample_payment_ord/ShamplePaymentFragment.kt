package com.vietinbank.feture_maker.maker_ui.contact_sample_payment_ord

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.viewbinding.ViewBinding
import com.vietinbank.core_common.extensions.onSearchTextChanged
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_maker.databinding.FragmentSampleBinding
import com.vietinbank.feture_maker.maker_ui.viewmodel.PaymentOrderViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class ShamplePaymentFragment : BaseFragment<PaymentOrderViewModel>() {
    override val viewModel: PaymentOrderViewModel by activityViewModels()
    override val useCompose: Boolean = false

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override fun inflateViewBinding(inflater: LayoutInflater, container: ViewGroup?): ViewBinding {
        return FragmentSampleBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initData()
        initView()
        initListener()
        viewModel.getPaymentTemplateList()
    }

    private fun initView() {
        (binding as FragmentSampleBinding).apply {
            rcvShample.adapter = viewModel.shampleAdapter
        }
        viewModel.shampleAdapter.setData(mutableListOf())
    }

    private fun initData() {
        (binding as FragmentSampleBinding).apply {
            etSearch.setText("")
            viewModel.getPaymentTemplateListState.observe(viewLifecycleOwner) { data ->
            }
        }
    }

    private fun initListener() {
        (binding as FragmentSampleBinding).apply {
            viewModel.shampleAdapter.onClickItem = {
                viewModel.clickItemShampleFromContactFragmentState(it)
            }
            etSearch.onSearchTextChanged { s ->
                val newList = if (s.isEmpty()) {
                    viewModel.listShampleFilter.toMutableList()
                } else {
                    viewModel.listShampleFilter.filter {
                        (it.toAccountName ?: "").lowercase().contains(
                            s.lowercase(),
                        ) || (it.toAccountNo ?: "").lowercase().contains(
                            s.lowercase(),
                        ) || (it.toBankName ?: "").lowercase().contains(
                            s.lowercase(),
                        ) || (it.content ?: "").lowercase().contains(
                            s.lowercase(),
                        )
                    }.toMutableList()
                }
                if (newList.isEmpty()) {
//                tạo 1 view khi null list
                } else {
                }
                viewModel.shampleAdapter.setData(newList)
            }
        }
    }
}
