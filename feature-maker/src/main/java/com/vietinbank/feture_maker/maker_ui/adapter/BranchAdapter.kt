package com.vietinbank.feture_maker.maker_ui.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_domain.models.maker.BranchDomains
import com.vietinbank.feature_maker.databinding.ItemAccountBinding

class BranchAdapter : RecyclerView.Adapter<BranchAdapter.BranchHolder>() {
    var dataSet: MutableList<BranchDomains> = mutableListOf()
    var context: Context? = null

    @SuppressLint("NotifyDataSetChanged")
    var onClickItem: ((BranchDomains) -> Unit)? = null

    @SuppressLint("NotifyDataSetChanged")
    fun setData(data: MutableList<BranchDomains>) {
        dataSet.clear()
        dataSet.addAll(data)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): BranchHolder {
        val binding = ItemAccountBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false,
        )
        return BranchHolder(binding)
    }

    inner class BranchHolder(var binding: ItemAccountBinding) : RecyclerView.ViewHolder(binding.root)

    override fun onBindViewHolder(holder: BranchHolder, position: Int) {
        val item = dataSet[position]
        with(holder.binding) {
            context = holder.itemView.context
            tvAccountNumber.text = "${(item.branchId ?: "")} - ${(item.branchName ?: "")} - ${(item.provinceName ?: "")}"
            root.setThrottleClickListener {
                dataSet[position].let { it1 -> onClickItem?.invoke(it1) }
            }
        }
    }

    override fun getItemCount(): Int = dataSet.size
}