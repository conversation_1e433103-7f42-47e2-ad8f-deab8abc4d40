package com.vietinbank.feture_maker.maker_ui.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.models.ContactType
import com.vietinbank.feature_maker.R
import com.vietinbank.feature_maker.databinding.ItemContactTypeBinding

class ContactTypeAdapter : RecyclerView.Adapter<ContactTypeAdapter.ContactTypeViewHolder>() {
    private var dataSet: MutableList<ContactType> = mutableListOf()
    var context: Context? = null

    @SuppressLint("NotifyDataSetChanged")
    var onClickItem: ((ContactType) -> Unit)? = null

    @SuppressLint("NotifyDataSetChanged")
    fun setData(data: MutableList<ContactType>) {
        dataSet.clear()
        dataSet.addAll(data)
        notifyDataSetChanged()
    }

    override fun getItemCount(): Int = dataSet.size

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): ContactTypeViewHolder {
        val binding =
            ItemContactTypeBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false,
            )
        return ContactTypeViewHolder(binding)
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onBindViewHolder(holder: ContactTypeViewHolder, position: Int) {
        val item = dataSet[position]
        with(holder.binding) {
            tvContactType.text = item.type

            if (item.isSelected) {
                tvContactType.setBackgroundResource(com.vietinbank.core_ui.R.drawable.bg_shape_text_selected)
                tvContactType.setTextColor(
                    ContextCompat.getColor(
                        root.context,
                        R.color.white,
                    ),
                )
            } else {
                tvContactType.setBackgroundResource(com.vietinbank.core_ui.R.drawable.bg_shape_text)
                tvContactType.setTextColor(
                    ContextCompat.getColor(
                        root.context,
                        R.color.text_blue_02,
                    ),
                )
            }
            if (item.isEnable) {
                root.setThrottleClickListener {
                    dataSet.forEach { it.isSelected = false }
                    item.isSelected = true
                    dataSet[position].let { it1 -> onClickItem?.invoke(it1) }
                    notifyDataSetChanged()
                }
            } else {
                tvContactType.setBackgroundResource(com.vietinbank.core_ui.R.drawable.bg_shape_text_disable)
                tvContactType.setTextColor(
                    ContextCompat.getColor(
                        root.context,
                        com.vietinbank.core_ui.R.color.text_hint,
                    ),
                )
            }
        }
    }

    inner class ContactTypeViewHolder(var binding: ItemContactTypeBinding) :
        RecyclerView.ViewHolder(binding.root)
}