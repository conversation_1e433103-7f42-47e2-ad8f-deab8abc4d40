package com.vietinbank.feture_maker.maker_ui.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_domain.models.maker.ApproverDomains
import com.vietinbank.feature_maker.databinding.ItemNextApproverBinding

class NextApproverAdapter : RecyclerView.Adapter<NextApproverAdapter.AccountHolder>() {
    private var dataSet: MutableList<ApproverDomains> = mutableListOf()
    var context: Context? = null

    @SuppressLint("NotifyDataSetChanged")
    var onClickItem: ((List<ApproverDomains>) -> Unit)? = null

    @SuppressLint("NotifyDataSetChanged")
    fun setData(data: MutableList<ApproverDomains>) {
        dataSet.clear()
        dataSet.addAll(data)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): AccountHolder {
        val binding = ItemNextApproverBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false,
        )
        return AccountHolder(binding)
    }

    inner class AccountHolder(var binding: ItemNextApproverBinding) :
        RecyclerView.ViewHolder(binding.root)

    @SuppressLint("NotifyDataSetChanged")
    override fun onBindViewHolder(holder: AccountHolder, position: Int) {
        val currentItem = dataSet[position]
        with(holder.binding) {
            context = holder.itemView.context
            tvUserName.text = currentItem.username ?: ""
            rbUserName.setOnCheckedChangeListener(null)
            rbUserName.isChecked = currentItem.isSelected

            root.setThrottleClickListener {
                currentItem.isSelected = !currentItem.isSelected
                rbUserName.isChecked = currentItem.isSelected
                onClickItem?.invoke(getSelectedItems())
            }
            rbUserName.setThrottleClickListener {
                currentItem.isSelected = !currentItem.isSelected
                rbUserName.isChecked = currentItem.isSelected
                onClickItem?.invoke(getSelectedItems())
            }
        }
    }

    fun getSelectedItems(): List<ApproverDomains> {
        return dataSet.filter { it.isSelected }
    }

    fun selectAll(select: Boolean) {
        dataSet.forEach { it.isSelected = select }
        notifyDataSetChanged()
        onClickItem?.invoke(getSelectedItems())
    }

    override fun getItemCount(): Int = dataSet.size
}