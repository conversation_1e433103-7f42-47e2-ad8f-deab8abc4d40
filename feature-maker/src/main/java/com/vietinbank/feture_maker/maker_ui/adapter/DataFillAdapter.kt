package com.vietinbank.feture_maker.maker_ui.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.models.DataFillObject
import com.vietinbank.feature_maker.databinding.ItemDataFillBinding

class DataFillAdapter : RecyclerView.Adapter<DataFillAdapter.DataFillHolder>() {
    private var dataSet: MutableList<DataFillObject> = mutableListOf()
    var context: Context? = null

    @SuppressLint("NotifyDataSetChanged")
    var onClickItem: ((DataFillObject) -> Unit)? = null

    @SuppressLint("NotifyDataSetChanged")
    fun setData(data: MutableList<DataFillObject>) {
        dataSet.clear()
        dataSet.addAll(data)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): DataFillHolder {
        val binding =
            ItemDataFillBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false,
            )
        return DataFillHolder(binding)
    }

    inner class DataFillHolder(var binding: ItemDataFillBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onBindViewHolder(holder: DataFillHolder, position: Int) {
        val item = dataSet[position]
        with(holder.binding) {
            context = holder.itemView.context
            tvNameDataFill.text = item.name
            root.setThrottleClickListener {
                dataSet[position].let { it1 -> onClickItem?.invoke(it1) }
            }
        }
    }

    override fun getItemCount(): Int = dataSet.size
}