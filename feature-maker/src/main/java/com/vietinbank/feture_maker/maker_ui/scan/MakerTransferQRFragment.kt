package com.vietinbank.feture_maker.maker_ui.scan

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.fragment.app.activityViewModels
import com.vietinbank.core_common.extensions.toBitmap
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.FlowUtils
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_data.qr.QrCodeManagerImpl
import com.vietinbank.core_domain.models.maker.ScanQrAction
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feture_maker.maker_ui.viewmodel.MakeTransferViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import kotlin.getValue

@AndroidEntryPoint
class MakerTransferQRFragment : BaseFragment<MakeTransferViewModel>() {
    override val viewModel: MakeTransferViewModel by activityViewModels()
    override val useCompose: Boolean = true

    @Inject
    lateinit var qrCodeManager: QrCodeManagerImpl

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Composable
    override fun ComposeScreen() {
        val actions = ScanQrAction(onBackClick = {
            appNavigator.popBackStack()
        }, onPictureClick = {
            openGallery()
        }, onError = {
            println("TUNA5: ${Utils.g().provideGson().toJson(it)}")
            showNoticeDialog("Mã QR không hợp lệ. Vui lòng kiểm tra lại") {
                viewModel.resetScanning()
            }
        })
        AppTheme {
            MakerQRScanScreen(viewModel, actions)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        // chon anh tu bo nho -> scan with zxing
        requestPermissionGalleryListener { uri ->
            uri.toBitmap(context)?.let { bitmap ->
                val detectedQr = qrCodeManager.scanWithBitmap(true, bitmap)
                qrCodeManager.handleQRCodeNapasInfo(detectedQr)?.let { info ->
                    if (info.binCode.isNullOrEmpty() || info.accountNumber.isNullOrEmpty()) {
                        showNoticeDialog("Mã QR không hợp lệ. Vui lòng kiểm tra lại")
                    } else {
                        viewModel.convertQrDomainToTransModel(info, detectedQr)
                        appNavigator.popBackStack()
                    }
                } ?: run {
                    showNoticeDialog("Mã QR không hợp lệ. Vui lòng kiểm tra lại")
                }
            }
        }

        FlowUtils.collectFlow(this@MakerTransferQRFragment, viewModel.qrScanResult) { qrResult ->
            qrResult?.let { detectedQr ->
                println("TuNA5 QR: $detectedQr")
                qrCodeManager.handleQRCodeNapasInfo(detectedQr)?.let { info ->
                    if (info.binCode.isNullOrEmpty() || info.accountNumber.isNullOrEmpty()) {
                        showNoticeDialog("Mã QR không hợp lệ. Vui lòng kiểm tra lại") {
                            viewModel.resetScanning()
                        }
                    } else {
                        viewModel.convertQrDomainToTransModel(info, detectedQr)
                        appNavigator.popBackStack()
                    }
                } ?: run {
                    showNoticeDialog("Mã QR không hợp lệ. Vui lòng kiểm tra lại") {
                        viewModel.resetScanning()
                    }
                }
            }
        }
    }
}