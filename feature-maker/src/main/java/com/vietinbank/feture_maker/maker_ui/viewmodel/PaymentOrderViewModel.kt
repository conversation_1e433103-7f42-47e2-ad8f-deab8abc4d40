package com.vietinbank.feture_maker.maker_ui.viewmodel

import android.content.Context
import android.net.Uri
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.extensions.convertFileToBase64
import com.vietinbank.core_common.extensions.dd_MM_yyyy_HH_mm_ss
import com.vietinbank.core_common.extensions.getFileNameFromUri
import com.vietinbank.core_common.extensions.getFileType
import com.vietinbank.core_common.extensions.resizeAndConvertUriToBase64
import com.vietinbank.core_common.extensions.todayAsString
import com.vietinbank.core_common.livedata.SingleLiveEvent
import com.vietinbank.core_common.models.DataFillObject
import com.vietinbank.core_common.models.ImageBase64Object
import com.vietinbank.core_common.models.TransferObject
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.login.AccountDefaultDomain
import com.vietinbank.core_domain.models.login.AccountListDomain
import com.vietinbank.core_domain.models.login.AccountListParams
import com.vietinbank.core_domain.models.maker.ApproverDomains
import com.vietinbank.core_domain.models.maker.BranchDomains
import com.vietinbank.core_domain.models.maker.BranchListDomains
import com.vietinbank.core_domain.models.maker.BranchParams
import com.vietinbank.core_domain.models.maker.ContactDomains
import com.vietinbank.core_domain.models.maker.ContactListParams
import com.vietinbank.core_domain.models.maker.DataBankDomain
import com.vietinbank.core_domain.models.maker.GetPaymentTemplateListDomains
import com.vietinbank.core_domain.models.maker.GetPaymentTemplateListParams
import com.vietinbank.core_domain.models.maker.NapasBankListParams
import com.vietinbank.core_domain.models.maker.NextStatusTransactionByRuleDomains
import com.vietinbank.core_domain.models.maker.NextStatusTransactionByRuleParams
import com.vietinbank.core_domain.models.maker.TempTransactionDomains
import com.vietinbank.core_domain.models.maker.TempTransactionParams
import com.vietinbank.core_domain.models.maker.ValidatePaymentOrderTransferDomains
import com.vietinbank.core_domain.models.maker.ValidatePaymentOrderTransferParams
import com.vietinbank.core_domain.repository.cache.ITransferCacheManager
import com.vietinbank.core_domain.usecase.login.LoginUseCase
import com.vietinbank.core_domain.usecase.transfer.TransferUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.adapter.TransferAdapter
import com.vietinbank.feture_maker.maker_ui.adapter.BankDefaultAdapter
import com.vietinbank.feture_maker.maker_ui.adapter.ContactAdapter
import com.vietinbank.feture_maker.maker_ui.adapter.ShampleTypeAdapter
import com.vietinbank.feture_maker.maker_ui.bottomSheet.AccListBottomSheetFragment
import com.vietinbank.feture_maker.maker_ui.bottomSheet.BankListBottomSheetFragment
import com.vietinbank.feture_maker.maker_ui.bottomSheet.DataFillBottomSheetFragment
import com.vietinbank.feture_maker.maker_ui.viewpager.ViewPagerContactAndSamplePaymentFragment
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class PaymentOrderViewModel @Inject constructor(
    private val transferUseCase: TransferUseCase,
    private val loginUseCase: LoginUseCase,
    val transferCacheManager: ITransferCacheManager,
    private val moneyHelper: MoneyHelper,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
) : BaseViewModel() {
    private var currency: String = ""
    fun setCcy(currency: String?) { this.currency = currency ?: "" }
    fun readAmountInWord(amount: String? = null, ccy: String? = null) = moneyHelper.convertAmountToWords(amount ?: "", ccy ?: currency)
    var viewPagerContactAndSamplePaymentFragment = ViewPagerContactAndSamplePaymentFragment()
    var listDataBanks: MutableList<DataBankDomain> = mutableListOf()
    val chooseBankDialog = BankListBottomSheetFragment()
    val bankDefaultAdapter = BankDefaultAdapter()
    var currentAccountDefaultDomain: AccountDefaultDomain? = null
    val chooseAccountNoDialog = AccListBottomSheetFragment()
    var currentDataBankDomain: DataBankDomain? = null
    var currentTypePaymentOrderSchedule: DataFillObject? = null
    var choosePaymentOrderTypeDialog = DataFillBottomSheetFragment()

    //    val choosePaymentOrderTypeDialog1 by lazy { bottomSheetFactory.createDataFillBottomSheet() } //lazy = tao obj khi thuc su can
    private var dataTypeTransfer = mutableListOf<DataFillObject>()
    var currentTypeTransfer: DataFillObject? = null
    val chooseTimeScheduleTypeDialog = DataFillBottomSheetFragment()
    private var dataTypeScheduleTime = mutableListOf<DataFillObject>()
    val chooseUploadTypeDialog = DataFillBottomSheetFragment()
    private var dataTypeUpload = mutableListOf<DataFillObject>()
    var currentTypeUpdate: DataFillObject? = null
    var imageUri: Uri? = null
    var imageBase64Object: ImageBase64Object? = null
    var listContact: MutableList<ContactDomains> = mutableListOf()
    var listContactFilter: MutableList<ContactDomains> = mutableListOf()
    val contactPaymentAdapter = ContactAdapter()
    val contactAdapter = ContactAdapter()
    val shampleAdapter = ShampleTypeAdapter()
    var listShampleFilter: MutableList<TempTransactionDomains> = mutableListOf()
    var listAccountApprovers = emptyList<ApproverDomains>()

    private val _accListState = SingleLiveEvent<Resource<AccountListDomain>>()
    val accListState: SingleLiveEvent<Resource<AccountListDomain>> get() = _accListState

    private val _handleDatePickerAccountState = SingleLiveEvent<String>()
    val handleDatePickerPaymentOrderState: SingleLiveEvent<String> get() = _handleDatePickerAccountState

    private val _paymentOrderTransferState = SingleLiveEvent<ValidatePaymentOrderTransferDomains>()
    val paymentOrderTransferState: SingleLiveEvent<ValidatePaymentOrderTransferDomains> get() = _paymentOrderTransferState

    private val _getFileUriState = SingleLiveEvent<Uri?>()

    private val _clickItemBankFromContactFragmentState = SingleLiveEvent<ContactDomains>()
    val clickItemBankFromContactFragmentState: SingleLiveEvent<ContactDomains> get() = _clickItemBankFromContactFragmentState

    val getFileUriState: SingleLiveEvent<Uri?> get() = _getFileUriState

    private val _getPaymentTemplateListState = SingleLiveEvent<GetPaymentTemplateListDomains>()
    val getPaymentTemplateListState: SingleLiveEvent<GetPaymentTemplateListDomains> get() = _getPaymentTemplateListState

    private val _clickAccountShampleFromContactFragmentState =
        SingleLiveEvent<TempTransactionDomains>()
    val clickAccountShampleFromContactFragmentState: SingleLiveEvent<TempTransactionDomains> get() = _clickAccountShampleFromContactFragmentState

    private val _nextStatusTransactionByRuleState =
        SingleLiveEvent<NextStatusTransactionByRuleDomains>()
    val nextStatusTransactionByRuleState: SingleLiveEvent<NextStatusTransactionByRuleDomains> get() = _nextStatusTransactionByRuleState

    private val _getBranchState =
        SingleLiveEvent<BranchListDomains>()
    val getBranchState: SingleLiveEvent<BranchListDomains> get() = _getBranchState
    var currentBranchDomains: BranchDomains? = null
    var branchList: MutableList<BranchDomains> = mutableListOf()
    var branchListSORTED: MutableList<BranchDomains> = mutableListOf()
    var tempTransactionPMList: MutableList<TempTransactionDomains> = mutableListOf()
    var alreadyAttemptedLoading = false

    fun getPaymentTemplateList() {
        if (alreadyAttemptedLoading) {
            mapIconToListTemplate(tempTransactionPMList)
            return
        }
        launchJob(showLoading = true) {
            val params = GetPaymentTemplateListParams(
                TempTransactionParams(
                    tranType = Tags.TransferType.TYPE_PAYMENT_ORDER_TRANSFER,
                ),
                username = userProf.getUserName() ?: "",
            )
            val res = transferUseCase.getPaymentTemplateList(params)
            handleResource(res) { data ->
                alreadyAttemptedLoading = true
                tempTransactionPMList = data.tempTransactionList
                _getPaymentTemplateListState.postValue(data)
                mapIconToListTemplate(data.tempTransactionList)
            }
        }
    }

    private fun mapIconToListTemplate(tempTransactionDomains: MutableList<TempTransactionDomains>) {
        val dataBankMap =
            listDataBanks.filterNot { !it.binCode.isNullOrEmpty() || !it.icon.isNullOrEmpty() || !it.ebankCode.isNullOrEmpty() }
                .flatMap { bank ->
                    listOf(
                        bank.binCode to bank.icon,
                        bank.ebankCode to bank.icon,
                    )
                }.toMap()
        val listTemplateWithIcons = tempTransactionDomains.map { tempTransactionItem ->
            dataBankMap[tempTransactionItem.toBankCode]?.let { newIcon ->
                tempTransactionItem.copy(icon = newIcon)
            } ?: tempTransactionItem
        }
        shampleAdapter.setData(listTemplateWithIcons.toMutableList())
        listShampleFilter = listTemplateWithIcons as MutableList<TempTransactionDomains>
    }

    fun getNapasBankList() {
        if (transferCacheManager.getBankList()?.isNotEmpty() == true) {
            listDataBanks = transferCacheManager.getBankList()?.toMutableList() ?: mutableListOf()
            setListBankData()
            return
        }
        launchJob(showLoading = true) {
            val res = transferUseCase.getNapasBankList(
                NapasBankListParams(
                    username = userProf.getUserName() ?: "",
                    cifno = userProf.getCifNo() ?: "",
                ),
            )
            handleResource(res) { data ->
                listDataBanks = data.dataBanks
                transferCacheManager.saveBankList(listDataBanks)
                setListBankData()
            }
        }
    }

    private fun setListBankData() {
        chooseBankDialog.setData(listDataBanks)
        bankDefaultAdapter.setData(listDataBanks.toMutableList())
    }

    fun contactList() {
        if (transferCacheManager.getContactList()?.isNotEmpty() == true) {
            mapIconToListContact(
                contactDomains = transferCacheManager.getContactList()?.toMutableList()
                    ?: mutableListOf(),
            )
            return
        }
        launchJob(showLoading = true) {
            val params = ContactListParams(
                username = userProf.getUserName().toString(),
                serviceId = "",
            )
            val res = transferUseCase.contactList(params)
            transferCacheManager.saveContactist(listContact)
            handleResource(res) { data ->
                listContact = data.contacts
                transferCacheManager.saveContactist(listContact)
                mapIconToListContact(
                    contactDomains = listContact.filter { it.trantype == Tags.TransferType.TYPE_PAYMENT_ORDER_TRANSFER }
                        .toMutableList(),
                )
            }
        }
    }

    fun getAccList() {
        val cachedAccounts = transferCacheManager.getAccountsPM()
        if (cachedAccounts?.isNotEmpty() == true) {
            val listAccount = cachedAccounts.filter { it.status == "0" }.toMutableList()
            getDefaultAccount(listAccount)?.let {
                currentAccountDefaultDomain = it
            }
            chooseAccountNoDialog.setData(listAccount)
            val accountListDomain = AccountListDomain(cachedAccounts)
            _accListState.postValue(Resource.Success(accountListDomain))
            return
        }
        launchJob(showLoading = true) {
            val params = AccountListParams(
                accountType = "",
                currencySort = "0",
                username = userProf.getUserName() ?: "",
                serviceType = Tags.TransferType.SERVICE_TYPE_PAYORD,
            )
            val res = loginUseCase.accList(params)
            handleResource(res) { data ->
                val activeAccounts =
                    data.accountDefault.filter { it.status == "0" }.toMutableList()
                transferCacheManager.saveAccountsPM(activeAccounts)
                getDefaultAccount(activeAccounts)?.let {
                    currentAccountDefaultDomain = it
                }
                chooseAccountNoDialog.setData(activeAccounts)
                _accListState.postValue(Resource.Success(data))
            }
        }
    }

    fun nextStatusTransactionByRule(amount: String, toAccountNo: String) {
        launchJob(showLoading = true) {
            val serviceType = Tags.TransferType.SERVICE_TYPE_PAYORD
            val params = NextStatusTransactionByRuleParams(
                amount = amount,
                creator = userProf.getUserName() ?: "",
                currentStatus = "",
                currentUserGroup = "",
                currentUserLevel = "0",
                customerNumber = userProf.getCifNo() ?: "",
                fromAccountNo = currentAccountDefaultDomain?.accountNo ?: "",
                serviceCode = serviceType,
                toAccountNo = toAccountNo,
                username = userProf.getUserName() ?: "",
                mtId = "",
            )
            val res = transferUseCase.nextStatusTransactionByRule(params)
            handleResource(res) { data ->
                _nextStatusTransactionByRuleState.postValue(data)
            }
        }
    }

    fun getBranch() {
        if (transferCacheManager.getBranchList()?.isNotEmpty() == true) {
            branchList = transferCacheManager.getBranchList() ?: mutableListOf()
            val branchListCache = BranchListDomains(branchList)
            _getBranchState.postValue(branchListCache)
            return
        }
        launchJob(showLoading = true) {
            val params = BranchParams(
                branchId = "",
                username = userProf.getUserName() ?: "",
            )
            val res = transferUseCase.getBranch(params)
            handleResource(res) { data ->
                transferCacheManager.saveBranch(data.data)
                branchList = data.data
                if (branchListSORTED.isEmpty() && branchList.isNotEmpty()) {
                    sortListBranch()
                }
//                _getBranchState.postValue(data)
            }
        }
    }

    fun validateNextStatusTransactionByRuleFields(
        amount: String,
        toAccountNo: String,
    ): String? {
        return when {
            currentAccountDefaultDomain == null -> "Chưa có tài khoản mặc định"
            toAccountNo.isBlank() -> "Nhập số tài khoản"
            amount.isBlank() -> "Nhập tiền chuyển"
            else -> null
        }
    }

    fun validatePaymentOrderTransfer(
        amount: String,
        content: String,
        toAccountNo: String,
        processTime: String,
        toAccountName: String,
        edtReceiveBank: String,
    ) {
        launchJob(showLoading = true) {
            val params = ValidatePaymentOrderTransferParams(
                amount = amount,
                content = content,
                feePayMethod = currentTypeTransfer?.id ?: "",
                fromAccountNo = currentAccountDefaultDomain?.accountNo ?: "",
                toAccountName = toAccountName,
                toAccountNo = toAccountNo,
                toBankName = edtReceiveBank,
                username = userProf.getUserName() ?: "",
                file = imageBase64Object?.imageBase64?.replace("\n", "") ?: "",
                processTime = processTime,
                fileName = imageBase64Object?.fileName ?: "",
                branchId = currentBranchDomains?.branchId ?: "",
                nextApprovers = listAccountApprovers,
            )
            val res = transferUseCase.validatePaymentOrderTransfer(params)
            handleResource(res) { data ->
                _paymentOrderTransferState.postValue(data)
            }
        }
    }

    override fun onDisplayErrorMessage(exception: AppException) {
        if (exception is AppException.ApiException && exception.requestPath == Constants.MB_ACCOUNT_LIST) {
            _accListState.postValue(
                Resource.Error(
                    exception.message.toString(),
                    exception.code.toString(),
                    exception,
                ),
            )
        }
        super.onDisplayErrorMessage(exception)
    }

    fun getFileUriState(ctx: Context, uri: Uri) {
        val fileType = getFileType(context = ctx, uri = uri)
        val isImage = fileType?.let { type ->
            type.contains("image") || type.contains("jpeg") || type.contains("jpg")
        } ?: false
        imageBase64Object = ImageBase64Object(
            imageBase64 = if (isImage) {
                resizeAndConvertUriToBase64(context = ctx, uri = uri)
            } else {
                convertFileToBase64(context = ctx, uri = uri)
            },
            fileName = getFileNameFromUri(context = ctx, uri = uri),
        )
        _getFileUriState.postValue(uri)
    }

    private fun mapIconToListContact(contactDomains: MutableList<ContactDomains>) {
        val dataBankMap = listDataBanks
            .filter { !it.binCode.isNullOrEmpty() && !it.icon.isNullOrEmpty() && !it.ebankCode.isNullOrEmpty() }
            .flatMap { bank ->
                listOf(
                    bank.binCode to Pair(bank.icon, bank.shortName),
                    bank.ebankCode to Pair(bank.icon, bank.shortName),
                )
            }.toMap()

        listContact = contactDomains.map { contact ->
            dataBankMap[contact.bank]?.let { (newIcon, newBankName) ->
                contact.copy(icon = newIcon, bankName = newBankName)
            } ?: contact
        }.toMutableList()

        listContact.filter { it.trantype == Tags.TransferType.TYPE_PAYMENT_ORDER_TRANSFER }
            .map { it.copy() }.toMutableList().also { contacts ->
                contactPaymentAdapter.setData(contacts.take(5).toMutableList())
                listContactFilter = contacts
            }
    }

    fun findMatchingDataBankDomain(
        bank: String?,
        trantype: String?,
    ): DataBankDomain? = when (trantype) {
        Tags.TransferType.TYPE_IN -> listDataBanks.find { it.type == trantype }
        else -> listDataBanks.find { it.binCode == bank || it.ebankCode == bank }
    }

    fun getDefaultAccount(accountDefault: MutableList<AccountDefaultDomain>): AccountDefaultDomain? {
        return accountDefault.maxByOrNull {
            it.currentBalance?.toDoubleOrNull() ?: Double.MIN_VALUE
        }
    }

    fun setUpBranchDialog() {
        chooseBankDialog.showFullDialog = true
        viewPagerContactAndSamplePaymentFragment.showFullDialog = true
    }

    fun setDataFeePaymentMethod() {
        dataTypeTransfer.clear()
        dataTypeTransfer.add(Tags.TransferType.dataSetFillDataOUR)
        dataTypeTransfer.add(Tags.TransferType.dataSetFillDataBEN)
        currentTypeTransfer = Tags.TransferType.dataSetFillDataOUR
        choosePaymentOrderTypeDialog.setData(dataTypeTransfer)
    }

    fun setDataTypeScheduleTime() {
        dataTypeScheduleTime.clear()
        dataTypeScheduleTime.add(Tags.TransferType.dataSetFillDataTimeNow)
        dataTypeScheduleTime.add(Tags.TransferType.dataSetFillDataTimeSchedule)
        currentTypePaymentOrderSchedule = Tags.TransferType.dataSetFillDataTimeNow
        chooseTimeScheduleTypeDialog.setData(dataTypeScheduleTime)
    }

    fun setDataTypeUpdate() {
        dataTypeUpload.clear()
        dataTypeUpload.add(Tags.TransferType.dataSetFillDataTakePicture)
        dataTypeUpload.add(Tags.TransferType.dataSetFillDataChoosePicture)
        dataTypeUpload.add(Tags.TransferType.dataSetFillDataChooseFile)
        chooseUploadTypeDialog.setData(dataTypeUpload)
    }

    fun confirmPaymentOrderTransferObjects(
        data: ValidatePaymentOrderTransferDomains,
        currentBalance: String?,
    ): MutableList<TransferObject> {
        return buildList {
            // Account information section
            add(TransferObject(TransferAdapter.TYPE_GROUP_TITLE, "Thông tin tài khoản chuyển"))
            add(
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Từ tài khoản",
                    data.fromAcctNo + " - " + (data.currency ?: "") + " - " + userProf.getFullName().toString(),
                ),
            )
            add(
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Số dư khả dụng",
                    Utils.g().getDotMoneyHasCcy(currentBalance ?: "", data.currency ?: ""),
                ),
            )

            // Branch information (conditional)
            data.branch?.let {
                add(
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "Tên chi nhánh xử lý",
                        "${(data.branch?.branchId ?: "")} - ${data.branch?.branchName}",
                    ),
                )
            }

            // Transaction information section
            add(TransferObject(TransferAdapter.TYPE_GROUP_TITLE, "Thông tin giao dịch"))
            add(
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Tới tài khoản",
                    data.toAcctNo ?: "",
                ),
            )
            add(
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Tên người thụ hưởng",
                    data.receiveName ?: "",
                ),
            )
            add(
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Ngân hàng",
                    data.receiveBankName ?: "",
                ),
            )
            add(
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Số tiền",
                    Utils.g().getDotMoneyHasCcy(data.amount ?: "", data.currency ?: "") + "\n" + moneyHelper.convertAmountToWords(data.amount ?: "", data.currency ?: ""),
                ),
            )
            add(
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Nội dung",
                    data.remark ?: "",
                ),
            )

            // Attachment file (conditional)
            imageBase64Object?.fileName?.let {
                add(
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "File đính kèm",
                        imageBase64Object?.fileName ?: "",
                    ),
                )
            }

            // Fee information
            add(
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Hình thức thu phí",
                    if ((data.feePayMethod ?: "") == "0") {
                        Tags.TransferType.dataSetFillDataBEN.name
                    } else {
                        Tags.TransferType.dataSetFillDataOUR.name
                    },
                ),
            )
            add(
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    "Phí giao dịch",
                    Utils.g().getDotMoneyHasCcy(data.feeVat ?: "", data.currency ?: ""),
                ),
            )

            // Transfer time (conditional)
            if (data.processDate.isNullOrEmpty()) {
                add(
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "Thời gian chuyển",
                        todayAsString(dd_MM_yyyy_HH_mm_ss),
                    ),
                )
            } else {
                add(
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "Thời gian chuyển",
                        "Đặt lịch",
                    ),
                )
                add(
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        "Ngày đặt lịch",
                        data.processDate ?: "",
                    ),
                )
            }

            // Approvers (conditional)
            if (data.nextApprovers?.isNotEmpty() == true) {
                val groupedApprovers = data.nextApprovers?.groupBy { it.approverlevel }
                val sortedLevels = groupedApprovers?.keys?.sortedBy { it }
                sortedLevels?.forEach { level ->
                    val approvers = groupedApprovers[level] ?: return@forEach
                    val approverNames = approvers.map { it.username }
                    val approversString = approverNames.joinToString("\n")
                    add(
                        TransferObject(
                            TransferAdapter.TYPE_GROUP_CONTENT,
                            title = "Người phê duyệt cấp $level",
                            content = approversString,
                        ),
                    )
                }
            }
        }.toMutableList()
    }

    fun getNextApproversListString(): String {
        return if (listAccountApprovers.isNotEmpty()) {
            listAccountApprovers.joinToString(", ") { it.username ?: "" }
        } else {
            "Theo đăng ký với VietinBank"
        }
    }

    fun clearListApprover() {
        listAccountApprovers = emptyList()
    }

    fun clickItemBankFromContactFragment(contactDomains: ContactDomains) {
        viewPagerContactAndSamplePaymentFragment.dismiss()
        _clickItemBankFromContactFragmentState.postValue(contactDomains)
    }

    fun clickItemShampleFromContactFragmentState(tempTransactionDomains: TempTransactionDomains) {
        viewPagerContactAndSamplePaymentFragment.dismiss()
        _clickAccountShampleFromContactFragmentState.postValue(tempTransactionDomains)
    }

    fun validateTransferFields(
        edtReceiveBank: String?,
        receiveAccount: String?,
        amount: String?,
        remark: String?,
        edtAccountOwner: String?,
    ): String? {
        return when {
            edtReceiveBank.isNullOrBlank() -> "Chưa nhập hàng nhận"
            edtAccountOwner.isNullOrBlank() -> "Chưa có tên người thụ hưởng"
            currentAccountDefaultDomain == null -> "Chọn từ tài khoản"
            currentBranchDomains == null -> "Chọn chi nhánh"
            receiveAccount.isNullOrBlank() -> "Nhập tới tài khoản"
            amount.isNullOrBlank() -> "Nhập số tiền"
            remark.isNullOrBlank() -> "Nhập nội dung"
//            imageBase64Object == null -> "Chưa có file đính kèm"
            else -> null
        }
    }

    fun sortListBranch() {
        if (currentAccountDefaultDomain != null) {
            val branchID = currentAccountDefaultDomain?.branchId ?: ""
            branchListSORTED = branchList.sortedWith { a, b ->
                when {
                    a.branchId == branchID && b.branchId != branchID -> -1
                    a.branchId != branchID && b.branchId == branchID -> 1
                    else -> 0
                }
            }.toMutableList()

            val branchListCache = BranchListDomains(branchListSORTED)
            _getBranchState.postValue(branchListCache)
        }
    }
}