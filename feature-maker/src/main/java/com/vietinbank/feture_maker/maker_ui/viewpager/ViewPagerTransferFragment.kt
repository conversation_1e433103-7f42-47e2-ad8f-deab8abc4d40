package com.vietinbank.feture_maker.maker_ui.viewpager

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.viewbinding.ViewBinding
import androidx.viewpager2.widget.ViewPager2
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.maker.NapasTransferParams
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_maker.databinding.FragmentMakerCreateTransferPagerBinding
import com.vietinbank.feture_maker.maker_ui.adapter.ViewPagerTransferAdapter
import com.vietinbank.feture_maker.maker_ui.viewmodel.MakeTransferViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class ViewPagerTransferFragment : BaseFragment<MakeTransferViewModel>() {
    private var adapter: ViewPagerTransferAdapter? = null
    override val viewModel: MakeTransferViewModel by activityViewModels()
    override val useCompose: Boolean = false

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override fun inflateViewBinding(inflater: LayoutInflater, container: ViewGroup?): ViewBinding {
        return FragmentMakerCreateTransferPagerBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initData()
        initView()
        initListener()
    }

    private fun initData() {
        try {
            // truong hop copy giao dich
            val napasInit = Utils.g().provideGson().fromJson(
                arguments?.getString(Tags.COPY_TRANSFER_OBJECT_BUNDLE, ""),
                NapasTransferParams::class.java,
            )
            napasInit?.let {
                viewModel.napasTransferParams = it
            }
        } catch (e: Exception) {
            printLog("Lỗi parse transaction JSON: ${e.message}")
            e.printStackTrace()
        }
    }

    private fun initView() {
        (binding as FragmentMakerCreateTransferPagerBinding).apply {
            adapter = ViewPagerTransferAdapter(childFragmentManager, lifecycle)
            viewPager.adapter = adapter
            viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    super.onPageSelected(position)
                    when (position) {
                        0 -> {
                            viewModel.isTransferAccountTab = true
                        }

                        1 -> {
                            viewModel.isTransferAccountTab = false
                        }
                    }
                }
            })
            viewPager.isUserInputEnabled = false
        }
    }

    private fun initListener() {
        (binding as FragmentMakerCreateTransferPagerBinding).apply {
            tvGotoAccountTransfer.setThrottleClickListener {
                viewPager.setCurrentItem(0, true)
                viewModel.onSelectTabAccountState()
                lineAccount.visibility = View.VISIBLE
                lineCard.visibility = View.GONE
            }
            tvGotoCardTransfer.setThrottleClickListener {
                viewPager.setCurrentItem(1, true)
                viewModel.onSelectTabCardState()
                openCustomToolbar.tvTitleToolbar.text = "Chuyển tiền"
                lineAccount.visibility = View.GONE
                lineCard.visibility = View.VISIBLE
            }
            openCustomToolbar.btnBack.setThrottleClickListener {
                onBackPressed()
            }
        }
    }
    override fun onBackPressed(): Boolean {
        appNavigator.goToHome()
        return true
    }
}