package com.vietinbank.feture_maker.maker_ui.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.extensions.loadUrl
import com.vietinbank.core_domain.models.maker.DataBankDomain
import com.vietinbank.feature_maker.databinding.ItemBankDefaultBinding

class BankDefaultAdapter : RecyclerView.Adapter<BankDefaultAdapter.BankHolder>() {
    var dataSet: MutableList<DataBankDomain> = mutableListOf()
    var context: Context? = null

    @SuppressLint("NotifyDataSetChanged")
    var onClickItem: ((DataBankDomain) -> Unit)? = null

    @SuppressLint("NotifyDataSetChanged")
    fun setData(data: MutableList<DataBankDomain>) {
        dataSet.clear()
        dataSet.addAll(data)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): BankHolder {
        val binding = ItemBankDefaultBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false,
        )
        return BankHolder(binding)
    }

    inner class BankHolder(var binding: ItemBankDefaultBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onBindViewHolder(holder: BankHolder, position: Int) {
        val item = dataSet[position]
        with(holder.binding) {
            context = holder.itemView.context
            root.setThrottleClickListener {
                dataSet[position].let { it1 -> onClickItem?.invoke(it1) }
            }
            imgIcon.loadUrl(item.icon ?: "")
            tvBankName.text = item.shortName
        }
    }

    override fun getItemCount(): Int = dataSet.size
}