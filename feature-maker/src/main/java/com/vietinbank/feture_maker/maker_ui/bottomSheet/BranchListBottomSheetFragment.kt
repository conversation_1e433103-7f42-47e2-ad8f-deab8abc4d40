package com.vietinbank.feture_maker.maker_ui.bottomSheet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_domain.models.maker.BranchDomains
import com.vietinbank.core_ui.base.BaseBottomSheetFragment
import com.vietinbank.feature_maker.databinding.FragmentBankListBinding
import com.vietinbank.feture_maker.maker_ui.adapter.BranchAdapter
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class BranchListBottomSheetFragment : BaseBottomSheetFragment() {
    private var _binding: FragmentBankListBinding? = null
    private val binding get() = _binding!!
    private val branchAdapter = BranchAdapter()
    private var onItemListener: ((BranchDomains) -> Unit)? = null
    private var dataSet: MutableList<BranchDomains> = mutableListOf()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        _binding = FragmentBankListBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun initView() {
        binding.tvType.text = "Chọn chi nhánh"
        binding.rcvBank.adapter = branchAdapter
    }

    fun setData(bankList: MutableList<BranchDomains>) {
        dataSet = bankList
        branchAdapter.setData(bankList)
    }

    override fun initData() {
        searchText()
    }

    private fun searchText() {
        binding.etSearch.searchText(0) { s ->
            val newList = if (s.isEmpty()) {
                dataSet.toList()
            } else {
                dataSet.filter {
                    (it.bankName ?: "").lowercase().contains(
                        s.lowercase(),
                    ) || (it.branchName ?: "").lowercase().contains(
                        s.lowercase(),
                    ) || (it.provinceName ?: "").lowercase().contains(
                        s.lowercase(),
                    )
                }
            }
            if (newList.isEmpty()) {
//                tạo 1 view khi null list
            } else {
            }
            branchAdapter.setData(newList as MutableList<BranchDomains>)
        }
    }

    override fun initListener() {
        branchAdapter.onClickItem = {
            onItemListener?.invoke(it)
            dismiss()
        }
        binding.ivBack.setThrottleClickListener {
            dismiss()
        }
    }

    fun setOnClickItemListener(action: ((BranchDomains) -> Unit)?) {
        onItemListener = action
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        onItemListener = null
    }
}