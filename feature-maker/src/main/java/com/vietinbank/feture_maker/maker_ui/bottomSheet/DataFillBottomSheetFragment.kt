package com.vietinbank.feture_maker.maker_ui.bottomSheet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.models.DataFillObject
import com.vietinbank.core_ui.base.BaseBottomSheetFragment
import com.vietinbank.feature_maker.databinding.FragmentDataFillBinding
import com.vietinbank.feture_maker.maker_ui.adapter.DataFillAdapter
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class DataFillBottomSheetFragment : BaseBottomSheetFragment() {
    private var _binding: FragmentDataFillBinding? = null
    private val binding get() = _binding!!
    private val dataFillAdapter = DataFillAdapter()
    private var onItemListener: ((DataFillObject) -> Unit)? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        _binding = FragmentDataFillBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun initView() {
        binding.rcvDataFill.adapter = dataFillAdapter
    }

    fun setData(accountDefault: MutableList<DataFillObject>) {
        dataFillAdapter.setData(accountDefault)
    }

    override fun initData() {
    }

    override fun initListener() {
        dataFillAdapter.onClickItem = {
            onItemListener?.invoke(it)
            dismiss()
        }
        binding.ivBack.setThrottleClickListener {
            dismiss()
        }
    }

    fun setOnClickItemListener(action: ((DataFillObject) -> Unit)?) {
        onItemListener = action
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        onItemListener = null
    }
}