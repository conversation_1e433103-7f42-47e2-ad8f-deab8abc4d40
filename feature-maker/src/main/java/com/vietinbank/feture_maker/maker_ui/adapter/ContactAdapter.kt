package com.vietinbank.feture_maker.maker_ui.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.extensions.loadDrawable
import com.vietinbank.core_common.extensions.loadUrl
import com.vietinbank.core_domain.models.maker.ContactDomains
import com.vietinbank.feature_maker.R
import com.vietinbank.feature_maker.databinding.ItemContactBinding

class ContactAdapter : RecyclerView.Adapter<ContactAdapter.ContactHolder>() {
    private var dataSet: MutableList<ContactDomains> = mutableListOf()
    var context: Context? = null

    @SuppressLint("NotifyDataSetChanged")
    var onClickItem: ((ContactDomains) -> Unit)? = null

    @SuppressLint("NotifyDataSetChanged")
    fun setData(data: MutableList<ContactDomains>) {
        dataSet.clear()
        dataSet.addAll(data)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): ContactHolder {
        val binding =
            ItemContactBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false,
            )
        return ContactHolder(binding)
    }

    inner class ContactHolder(var binding: ItemContactBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onBindViewHolder(holder: ContactHolder, position: Int) {
        val item = dataSet[position]
        with(holder.binding) {
            context = holder.itemView.context
            if (Tags.TransferType.TYPE_IN == item.trantype) {
                tvPayeeName.text = (item.payeename ?: "") + " - " +
                    ("VietinBank") + " - " + (item.account ?: "")
            } else if (Tags.TransferType.TYPE_NAPAS_ACCOUNT == item.trantype) {
                tvPayeeName.text = (item.payeename ?: "") + " - " +
                    (item.bankName ?: "") + " - " + (item.account ?: "")
            } else if (Tags.TransferType.TYPE_PAYMENT_ORDER_TRANSFER == item.trantype) {
                tvPayeeName.text =
                    "PM - ${item.bankName ?: ""} -  ${item.account ?: ""}"
            } else {
                tvPayeeName.text =
                    "Napas Card - ${item.bankName ?: ""} -  ${item.cardnumber ?: ""}"
            }

            if (Tags.TransferType.TYPE_IN == item.trantype) {
                icBank.visibility = View.VISIBLE
                icBank.loadDrawable(R.drawable.ic_vietinbank)
            } else if (Tags.TransferType.TYPE_PAYMENT_ORDER_TRANSFER == item.trantype) {
                icBank.visibility = View.GONE
            } else {
                icBank.visibility = View.VISIBLE
                icBank.loadUrl(item.icon ?: "")
            }

            root.setThrottleClickListener {
                dataSet[position].let { it1 -> onClickItem?.invoke(it1) }
            }
        }
    }

    override fun getItemCount(): Int = dataSet.size
    fun getCurrentList(): MutableList<ContactDomains> = dataSet
}