<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/maker_transfer_nav_graph"
    app:startDestination="@id/makerTransferFragment">

    <!--accountBalanceFragment -->
    <fragment
        android:id="@+id/makerTransferFragment"
        android:name="com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_dashboard.MakerTransferFragment"
        android:label="makerTransferFragment">
        <deepLink app:uri="maker://transfer/" />
    </fragment>

    <fragment
        android:id="@+id/makerTransferBankChoosedFragment"
        android:name="com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.MakerTransferBankChoosedFragment"
        android:label="makerTransferBankChoosedFragment">
        <deepLink app:uri="maker://makerTransferBankChoosedFragment/{KEY_TRANSFER_TYPE}" />
        <deepLink app:uri="maker://makerTransferBankChoosedFragment/{KEY_TRANSFER_TYPE}/{KEY_BANK_ITEM}" />
    </fragment>
    <fragment
        android:id="@+id/makerTransferConfirmFragment"
        android:name="com.vietinbank.feture_maker.transfer_new.screen.confirm.MakerTransferConfirmFragment"
        android:label="makerTransferConfirmFragment">
        <deepLink app:uri="maker://makerTransferConfirmFragment/{KEY_TRANSFER_TYPE}/{CONFIRM_TRANSFER_ITEM}" />
    </fragment>

    <fragment
        android:id="@+id/makerTransferResultFragment"
        android:name="com.vietinbank.feture_maker.transfer_new.screen.result.MakerTransferResultFragment"
        android:label="makerTransferResultFragment">
        <deepLink app:uri="maker://makerTransferResultFragment/{KEY_TRANSFER_TYPE}/{CONFIRM_TRANSFER_ITEM}/{RESULT_TRANSFER_ITEM}" />
    </fragment>



</navigation>