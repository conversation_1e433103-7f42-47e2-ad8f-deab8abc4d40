<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="40dp"
    android:height="40dp"
    android:viewportWidth="40"
    android:viewportHeight="40">
  <path
      android:pathData="M20,0.465C30.789,0.465 39.535,9.211 39.535,20C39.535,30.789 30.789,39.535 20,39.535C9.211,39.535 0.465,30.789 0.465,20C0.465,9.211 9.211,0.465 20,0.465Z"
      android:fillAlpha="0.5">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="0"
          android:startY="0"
          android:endX="40"
          android:endY="40"
          android:type="linear">
        <item android:offset="0" android:color="#FF72CEFF"/>
        <item android:offset="1" android:color="#FFF5FCFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M20,0.465C30.789,0.465 39.535,9.211 39.535,20C39.535,30.789 30.789,39.535 20,39.535C9.211,39.535 0.465,30.789 0.465,20C0.465,9.211 9.211,0.465 20,0.465Z"
      android:strokeWidth="0.929412"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="0"
          android:startY="0"
          android:endX="40"
          android:endY="40"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#FF8FD9FF"/>
      </gradient>
    </aapt:attr>
  </path>
  <group>
    <clip-path
        android:pathData="M8,8h24v24h-24z"/>
    <path
        android:pathData="M11,18H29M15,23H15.01M19,23H21M11,16C11,15.204 11.316,14.441 11.879,13.879C12.441,13.316 13.204,13 14,13H26C26.796,13 27.559,13.316 28.121,13.879C28.684,14.441 29,15.204 29,16V24C29,24.796 28.684,25.559 28.121,26.121C27.559,26.684 26.796,27 26,27H14C13.204,27 12.441,26.684 11.879,26.121C11.316,25.559 11,24.796 11,24V16Z"
        android:strokeLineJoin="round"
        android:strokeWidth="2"
        android:fillColor="#00000000"
        android:strokeColor="#036091"
        android:strokeLineCap="round"/>
  </group>
</vector>
