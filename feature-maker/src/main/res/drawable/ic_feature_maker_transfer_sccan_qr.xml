<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="18dp"
    android:height="18dp"
    android:viewportWidth="18"
    android:viewportHeight="18">
  <path
      android:pathData="M4,14V14.01M4,4V4.01M14,4V4.01M14,11H11V14M17,11V11.01M11,17H14M14,14H17V17M1,2C1,1.735 1.105,1.48 1.293,1.293C1.48,1.105 1.735,1 2,1H6C6.265,1 6.52,1.105 6.707,1.293C6.895,1.48 7,1.735 7,2V6C7,6.265 6.895,6.52 6.707,6.707C6.52,6.895 6.265,7 6,7H2C1.735,7 1.48,6.895 1.293,6.707C1.105,6.52 1,6.265 1,6V2<PERSON>M11,2C11,1.735 11.105,1.48 11.293,1.293C11.48,1.105 11.735,1 12,1H16C16.265,1 16.52,1.105 16.707,1.293C16.895,1.48 17,1.735 17,2V6C17,6.265 16.895,6.52 16.707,6.707C16.52,6.895 16.265,7 16,7H12C11.735,7 11.48,6.895 11.293,6.707C11.105,6.52 11,6.265 11,6V2ZM1,12C1,11.735 1.105,11.48 1.293,11.293C1.48,11.105 1.735,11 2,11H6C6.265,11 6.52,11.105 6.707,11.293C6.895,11.48 7,11.735 7,12V16C7,16.265 6.895,16.52 6.707,16.707C6.52,16.895 6.265,17 6,17H2C1.735,17 1.48,16.895 1.293,16.707C1.105,16.52 1,16.265 1,16V12Z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
</vector>
