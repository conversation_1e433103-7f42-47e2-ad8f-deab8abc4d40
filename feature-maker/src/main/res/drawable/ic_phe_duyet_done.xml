<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="43dp"
    android:height="43dp"
    android:viewportWidth="43"
    android:viewportHeight="43">
  <path
      android:pathData="M26.304,2.256C23.808,-0.752 19.192,-0.752 16.696,2.256C15.38,3.842 13.365,4.677 11.313,4.486C7.421,4.124 4.157,7.388 4.519,11.28C4.71,13.332 3.876,15.346 2.289,16.663C-0.719,19.159 -0.719,23.775 2.289,26.271C3.876,27.587 4.71,29.601 4.519,31.653C4.157,35.546 7.421,38.81 11.313,38.448C13.365,38.257 15.38,39.091 16.696,40.677C19.192,43.686 23.808,43.686 26.304,40.677C27.62,39.091 29.635,38.257 31.687,38.448C35.579,38.81 38.843,35.546 38.481,31.653C38.29,29.601 39.124,27.587 40.71,26.271C43.719,23.775 43.719,19.159 40.71,16.663C39.124,15.346 38.29,13.332 38.481,11.28C38.843,7.388 35.579,4.124 31.687,4.486C29.635,4.677 27.62,3.842 26.304,2.256ZM21.5,35.467C29.232,35.467 35.5,29.199 35.5,21.467C35.5,13.735 29.232,7.467 21.5,7.467C13.768,7.467 7.5,13.735 7.5,21.467C7.5,29.199 13.768,35.467 21.5,35.467Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="4.266"
          android:startY="42.934"
          android:endX="38.733"
          android:endY="-0"
          android:type="linear">
        <item android:offset="0.154" android:color="#FF58A0D2"/>
        <item android:offset="0.858" android:color="#FF3A7BB1"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M15.033,21L19.033,25L27.033,17"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#014F7F"
      android:strokeLineCap="square"/>
</vector>
