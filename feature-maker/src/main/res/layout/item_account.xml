<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:background="@color/white"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/tvAccountNumber"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp10"
        android:textColor="@color/text_blue_02"
        android:textSize="@dimen/sp15"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="00000" />
    <View
        android:layout_width="match_parent"
        android:background="@color/text_hint"
        app:layout_constraintTop_toBottomOf="@+id/tvAccountNumber"
        android:layout_height="1dp"/>
</androidx.constraintlayout.widget.ConstraintLayout>