<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="@dimen/dp10">

    <include
        android:id="@+id/openCustomToolbar"
        layout="@layout/custom_toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/tv1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp15"
        android:text="Thông tin tài khoản chuyển"
        android:textColor="#D6F4FF"
        android:textSize="@dimen/sp15"
        app:layout_constraintTop_toBottomOf="@+id/openCustomToolbar" />

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/tv2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp15"
        android:background="@color/white"
        android:paddingLeft="12dp"
        android:paddingTop="@dimen/sp10"
        android:text="Từ tài khoản"
        android:textColor="@color/color_hint"
        android:textSize="@dimen/sp14"
        app:layout_constraintTop_toBottomOf="@+id/tv1" />

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/tvAccountNumber"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:paddingLeft="12dp"
        android:paddingBottom="@dimen/sp10"
        android:textColor="#006594"
        android:textSize="@dimen/sp16"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv2"
        tools:text="00000" />

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/titleCurrentBalnce"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="#5687B3"
        android:paddingLeft="12dp"
        android:paddingTop="@dimen/dp12"
        android:paddingBottom="@dimen/dp12"
        android:text="Số dư khả dụng"
        android:textColor="@color/white"
        android:textSize="@dimen/sp12"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvAccountNumber" />

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/tvCurrentBalnce"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="#5687B3"
        android:gravity="end"
        android:paddingTop="@dimen/dp12"
        android:paddingRight="@dimen/dp12"
        android:paddingBottom="@dimen/dp12"
        android:textColor="@color/white"
        android:textSize="@dimen/sp12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/titleCurrentBalnce"
        app:layout_constraintTop_toBottomOf="@+id/tvAccountNumber"
        tools:text="00000" />

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/titleRecieveBank"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp14"
        android:text="Ngân hàng nhận"
        android:textColor="#D6F4FF"
        android:textSize="@dimen/sp15"
        app:layout_constraintTop_toBottomOf="@+id/tvCurrentBalnce" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_bank"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp15"
        android:background="@color/white"
        android:paddingLeft="@dimen/dp12"
        android:paddingTop="@dimen/dp8"
        android:paddingRight="@dimen/dp12"
        android:paddingBottom="@dimen/dp8"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titleRecieveBank">

        <com.vietinbank.core_ui.base.views.BaseTextView
            android:id="@+id/tvRecieveBank"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp15"
            android:clickable="false"
            android:focusable="false"
            android:textColor="#062A46"
            android:textSize="@dimen/sp15"
            app:layout_constraintStart_toEndOf="@+id/imgIconBank"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="00000" />

        <com.vietinbank.core_ui.base.views.BaseTextView
            android:id="@+id/tvRecieveBankName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp15"
            android:clickable="false"
            android:ellipsize="end"
            android:focusable="false"
            android:maxLines="2"
            android:textColor="#8294A2"
            android:textSize="@dimen/sp15"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/imgIconBank"
            app:layout_constraintTop_toBottomOf="@+id/tvRecieveBank"
            tools:text="000000000000000000000000000000000000000000000000000000000000000000000000000000000000" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgIconBank"
            android:layout_width="@dimen/dp48"
            android:layout_height="@dimen/dp48"
            android:layout_marginTop="@dimen/dp5"
            android:layout_marginBottom="@dimen/dp5"
            android:clickable="false"
            android:focusable="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgIconDrop"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/ic_drop_down"
            android:clickable="false"
            android:focusable="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.vietinbank.core_ui.base.views.BaseTextView
            android:id="@+id/tvType"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_shape_text"
            android:paddingLeft="@dimen/dp8"
            android:paddingTop="@dimen/dp3"
            android:layout_marginLeft="10dp"
            android:paddingRight="@dimen/dp8"
            android:paddingBottom="@dimen/dp3"
            android:textColor="#062A46"
            android:textSize="@dimen/sp10"
            app:layout_constraintStart_toEndOf="@+id/tvRecieveBank"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="00000" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_ReceiveAccount"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp15"
        android:background="@color/white"
        android:paddingLeft="@dimen/dp12"
        android:paddingTop="@dimen/dp8"
        android:paddingRight="@dimen/dp12"
        android:paddingBottom="@dimen/dp8"
        app:layout_constraintTop_toBottomOf="@+id/cl_bank">

        <EditText
            android:id="@+id/edtReceiveAccount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Tới tài khoản"
            android:textColor="@color/black"
            android:textColorHint="@color/color_hint"
            android:textSize="@dimen/sp16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.vietinbank.core_ui.base.views.BaseTextView
            android:id="@+id/tvReceiveAccountTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Tên người thụ hưởng"
            android:textColor="@color/text_hint"
            android:textSize="@dimen/sp14"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/edtReceiveAccount" />

        <com.vietinbank.core_ui.base.views.BaseTextView
            android:id="@+id/tvReceiveAccount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="#006594"
            android:textSize="@dimen/sp16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvReceiveAccountTitle"
            tools:text="00000" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_additionInfo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp15"
        android:background="@color/white"
        android:paddingLeft="@dimen/dp12"
        android:paddingTop="@dimen/dp8"
        android:paddingRight="@dimen/dp12"
        android:paddingBottom="@dimen/dp8"
        app:layout_constraintTop_toBottomOf="@+id/cl_ReceiveAccount">

        <EditText
            android:id="@+id/edtAmount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:hint="Số tiền"
            android:inputType="number"
            android:digits=".**********"
            android:textColor="#006594"
            android:textSize="@dimen/sp15"
            app:layout_constraintEnd_toStartOf="@+id/tvCurrentcyAmount"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.vietinbank.core_ui.base.views.BaseTextView
            android:id="@+id/tvCurrentcyAmount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/text_hint"
            android:textSize="@dimen/sp12"
            app:layout_constraintBottom_toBottomOf="@+id/edtAmount"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/edtAmount"
            tools:text="00000" />

        <EditText
            android:id="@+id/edtRemark"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp15"
            android:hint="Nội dung"
            android:textColor="#006594"
            android:textSize="@dimen/sp15"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/edtAmount" />

        <com.vietinbank.core_ui.base.views.BaseTextView
            android:id="@+id/titleTimeSchedule"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp15"
            android:text="Thời gian chuyển"
            android:textColor="@color/text_hint"
            android:textSize="@dimen/sp15"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/edtRemark" />

        <com.vietinbank.core_ui.base.views.BaseTextView
            android:id="@+id/tvTimeSchedule"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Chuyển Ngay"
            android:textColor="#006594"
            android:textSize="@dimen/sp15"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/titleTimeSchedule" />

        <com.vietinbank.core_ui.base.views.BaseTextView
            android:id="@+id/titleTypeFee"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp15"
            android:text="Hình thức thu phí"
            android:textColor="@color/text_hint"
            android:textSize="@dimen/sp15"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTimeSchedule" />

        <com.vietinbank.core_ui.base.views.BaseTextView
            android:id="@+id/tvTypeFee"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="OUR"
            android:textColor="#006594"
            android:textSize="@dimen/sp15"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/titleTypeFee" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <Button
        android:id="@+id/btnCreateMaker"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp10"
        android:text="Tiếp tục"
        android:textAllCaps="false"
        app:layout_constraintTop_toBottomOf="@+id/cl_additionInfo" />

</androidx.constraintlayout.widget.ConstraintLayout>