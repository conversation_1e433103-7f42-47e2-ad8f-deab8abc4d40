<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginTop="10dp"
    android:layout_marginRight="10dp"
    android:background="@drawable/bg_shape_text"
    android:orientation="vertical">

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/tvContactType"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_blue_02"
        android:padding="@dimen/dp10"
        android:textSize="@dimen/sp15"
        app:fontCus="medium"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="00000" />

</androidx.constraintlayout.widget.ConstraintLayout>