<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical">

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/tvUserName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/dp5"
        android:padding="@dimen/dp10"
        android:textColor="@color/text_blue_02"
        android:textSize="@dimen/sp15"
        app:fontCus="medium"
        app:layout_constraintEnd_toStartOf="@+id/rbUserName"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="00000" />

    <CheckBox
        android:id="@+id/rbUserName"
        android:button="@null"
        android:clickable="false"
        android:layout_width="@dimen/dp16"
        android:layout_height="@dimen/dp16"
        android:layout_marginRight="@dimen/dp15"
        android:background="@drawable/radio_background"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/text_hint"
        app:layout_constraintTop_toBottomOf="@+id/tvUserName" />
</androidx.constraintlayout.widget.ConstraintLayout>