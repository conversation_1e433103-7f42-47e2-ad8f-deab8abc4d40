<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rcv_contactType"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:itemCount="2"
        tools:listitem="@layout/item_contact_type" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_search"
        android:layout_marginTop="10dp"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp36"
        android:layout_marginHorizontal="@dimen/dp10"
        android:background="@drawable/bg_shape_search"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rcv_contactType">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_search"
            android:layout_width="@dimen/dp26"
            android:layout_height="@dimen/dp24"
            android:layout_marginStart="@dimen/dp6"
            android:src="@drawable/ic_search"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <EditText
            android:id="@+id/et_search"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp6"
            android:background="@color/transparent"
            android:hint="Tìm kiếm"
            android:textColor="#062A46"
            android:textColorHint="@color/text_hint"
            android:textSize="@dimen/sp14"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_search"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rcv_contact"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/dp10"
        android:layout_marginTop="10dp"
        android:orientation="vertical"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cl_search"
        tools:itemCount="5"
        tools:listitem="@layout/item_account" />
</androidx.constraintlayout.widget.ConstraintLayout>