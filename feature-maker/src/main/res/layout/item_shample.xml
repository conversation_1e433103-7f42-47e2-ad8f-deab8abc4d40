<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical"
   >

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/tvShampleTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp10"
        android:textColor="@color/text_blue_02"
        android:textSize="@dimen/sp15"
        app:layout_constraintEnd_toStartOf="@+id/tvShampleViewMore"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Tài khoản nội bộ" />

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/tvShampleViewMore"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp10"
        android:text="Thu gọn"
        android:textColor="@color/text_blue_02"
        android:textSize="@dimen/sp15"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rcvShampleType"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        android:padding="@dimen/dp10"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvShampleTitle" />
</androidx.constraintlayout.widget.ConstraintLayout>