<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/openCustomToolbar"
        layout="@layout/custom_toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/tvGotoAccountTransfer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:padding="@dimen/dp10"
        app:fontCus="semi_bold"
        android:text="Tới tài khoản"
        android:textColor="@color/white"
        android:textSize="15sp"
        app:layout_constraintEnd_toStartOf="@+id/tvGotoCardTransfer"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/openCustomToolbar" />

    <View
        android:id="@+id/lineAccount"
        android:layout_width="0dp"
        android:layout_height="1.5dp"
        android:background="@color/white"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@+id/tvGotoCardTransfer"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvGotoAccountTransfer" />

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/tvGotoCardTransfer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        app:fontCus="semi_bold"
        android:padding="@dimen/dp10"
        android:text="Qua thẻ"
        android:textColor="@color/white"
        android:textSize="15sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvGotoAccountTransfer"
        app:layout_constraintTop_toBottomOf="@+id/openCustomToolbar" />

    <View
        android:id="@+id/lineCard"
        android:layout_width="0dp"
        android:layout_height="1.5dp"
        android:background="@color/white"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvGotoAccountTransfer"
        app:layout_constraintTop_toBottomOf="@+id/tvGotoCardTransfer" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/lineAccount" />

</androidx.constraintlayout.widget.ConstraintLayout>