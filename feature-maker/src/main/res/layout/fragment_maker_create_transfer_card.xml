<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="@dimen/dp10">

    <ScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toTopOf="@+id/btnCreateMaker"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tv1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp5"
                android:text="Thông tin thụ hưởng"
                android:textColor="@color/text_blue_09"
                android:textSize="@dimen/sp14"
                app:fontCus="semi_bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="@drawable/bg_white_radius_2dp"
                android:padding="@dimen/dp10"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv1">


                <EditText
                    android:id="@+id/edtToCardNo"
                    android:digits="0123456789"
                    android:inputType="number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:hint="Nhập số thẻ"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/text_blue_02"
                    android:textColorHint="@color/color_hint"
                    android:textSize="15sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.vietinbank.core_ui.base.views.BaseTextView
                    android:id="@+id/tvToCardName"
                    android:layout_width="0dp"
                    android:visibility="gone"
                    android:layout_marginLeft="@dimen/dp6"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_blue_02"
                    android:textSize="16sp"
                    app:fontCus="medium"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/edtToCardNo"
                    tools:text="00000" />

                <com.vietinbank.core_ui.base.views.BaseTextView
                    android:id="@+id/tvToCardInfo"
                    android:layout_width="0dp"
                    android:visibility="gone"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp6"
                    android:textColor="@color/text_blue_07"
                    android:textSize="14sp"
                    app:fontCus="medium"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvToCardName"
                    tools:hint="Nhập số thẻ" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/btnOpenContact"
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:background="@drawable/ic_dangnhap_user"
                    app:layout_constraintBottom_toBottomOf="@+id/edtToCardNo"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/edtToCardNo" />

                <com.vietinbank.core_ui.base.views.BaseTextView
                    android:id="@+id/tvSaveAccount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingTop="5dp"
                    android:paddingBottom="5dp"
                    android:text="Lưu người thụ hưởng"
                    android:textColor="@color/text_blue_02"
                    android:visibility="gone"
                    app:fontCus="semi_bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvToCardInfo" />

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/sw_createContact"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingTop="5dp"
                    android:paddingBottom="5dp"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@+id/tvSaveAccount"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/tvSaveAccount" />

                <EditText
                    android:id="@+id/edt_CustomerCode"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Tên gợi nhớ"
                    android:textColor="@color/text_blue_02"
                    android:textColorHint="@color/text_hint"
                    android:textSize="@dimen/sp15"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvSaveAccount" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rcv_contact"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/edt_CustomerCode"
                    tools:itemCount="2"
                    tools:listitem="@layout/item_contact" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tv2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="Thông tin tài khoản chuyển"
                android:textColor="@color/text_blue_09"
                android:textSize="@dimen/sp14"
                app:fontCus="semi_bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/constraintLayout" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_fromAccount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="@drawable/bg_white_radius_2dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv2">

                <com.vietinbank.core_ui.base.views.BaseTextView
                    android:id="@+id/tv3"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/sp10"
                    android:text="Từ tài khoản"
                    android:textColor="@color/text_blue_07"
                    android:textSize="@dimen/sp14"
                    app:fontCus="semi_bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.vietinbank.core_ui.base.views.BaseTextView
                    android:id="@+id/tvAccountNumber"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:paddingLeft="12dp"
                    android:paddingBottom="@dimen/sp10"
                    android:textColor="@color/text_blue_02"
                    android:textSize="@dimen/sp16"
                    app:fontCus="semi_bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv3"
                    tools:text="00000" />

                <com.vietinbank.core_ui.base.views.BaseTextView
                    android:id="@+id/titleCurrentBalnce"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:background="#5687B3"
                    android:paddingLeft="12dp"
                    android:paddingTop="@dimen/dp12"
                    android:paddingBottom="@dimen/dp12"
                    android:text="Số dư khả dụng"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp12"
                    app:fontCus="medium"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvAccountNumber" />

                <com.vietinbank.core_ui.base.views.BaseTextView
                    android:id="@+id/tvCurrentBalnce"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:background="#5687B3"
                    android:gravity="end"
                    android:paddingTop="@dimen/dp12"
                    android:paddingRight="@dimen/dp12"
                    android:paddingBottom="@dimen/dp12"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp12"
                    app:fontCus="medium"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/titleCurrentBalnce"
                    app:layout_constraintTop_toBottomOf="@+id/tvAccountNumber"
                    tools:text="00000" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_additionInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp15"
                android:background="@drawable/bg_white_radius_2dp"
                android:paddingLeft="@dimen/dp12"
                android:paddingTop="@dimen/dp8"
                android:paddingRight="@dimen/dp12"
                android:paddingBottom="@dimen/dp8"
                app:layout_constraintTop_toBottomOf="@+id/cl_fromAccount">

                <com.vietinbank.core_ui.base.views.BaseEditText
                    android:id="@+id/edtAmount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:digits=".**********"
                    android:hint="Số tiền"
                    android:inputType="number"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/text_blue_02"
                    android:textSize="@dimen/sp15"
                    app:layout_constraintEnd_toStartOf="@+id/tvCurrentcyAmount"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:digits="**********" />

                <com.vietinbank.core_ui.base.views.BaseTextView
                    android:id="@+id/tvCurrentcyAmount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_blue_07"
                    android:textSize="@dimen/sp16"
                    app:fontCus="medium"
                    app:layout_constraintBottom_toBottomOf="@+id/edtAmount"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/edtAmount"
                    tools:text="00000" />

                <com.vietinbank.core_ui.base.views.BaseTextView
                    android:id="@+id/tvAmountToWord"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:textColor="@color/text_blue_02"
                    android:textSize="@dimen/sp12"
                    app:fontCus="medium"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/edtAmount"
                    tools:text="00000" />

                <com.vietinbank.core_ui.base.views.BaseEditText
                    android:id="@+id/edtRemark"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp15"
                    android:hint="Nội dung"
                    android:maxLines="1"
                    android:inputType="textNoSuggestions|textVisiblePassword"
                    android:textColor="@color/text_blue_02"
                    android:textSize="@dimen/sp15"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvAmountToWord" />
                <com.vietinbank.core_ui.base.views.BaseTextView
                    android:id="@+id/tvRemarkCount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:text="0/150"
                    android:textColor="#006594"
                    android:textSize="@dimen/sp12"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/edtRemark" />
                <com.vietinbank.core_ui.base.views.BaseTextView
                    android:id="@+id/titleTimeSchedule"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:text="Thời gian chuyển"
                    android:textColor="@color/text_blue_07"
                    android:textSize="@dimen/sp15"
                    app:fontCus="semi_bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvRemarkCount" />

                <com.vietinbank.core_ui.base.views.BaseTextView
                    android:id="@+id/tvTimeSchedule"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_blue_02"
                    android:textSize="@dimen/sp16"
                    app:fontCus="semi_bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/titleTimeSchedule"
                    tools:text="Chuyển Ngay" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_daySchedule"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvTimeSchedule">

                    <com.vietinbank.core_ui.base.views.BaseTextView
                        android:id="@+id/titleDaySchedule"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:clickable="false"
                        android:focusable="false"
                        android:text="Ngày đặt lịch"
                        android:textColor="@color/text_blue_07"
                        android:textSize="@dimen/sp16"
                        app:fontCus="semi_bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.vietinbank.core_ui.base.views.BaseTextView
                        android:id="@+id/tvDaySchedule"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:clickable="false"
                        android:focusable="false"
                        android:textColor="@color/text_blue_02"
                        android:textSize="@dimen/sp16"
                        app:fontCus="semi_bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/titleDaySchedule"
                        tools:text="01/01/1980" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/ic_right"
                        android:clickable="false"
                        android:focusable="false"
                        app:layout_constraintEnd_toEndOf="@+id/titleDaySchedule"
                        app:layout_constraintTop_toTopOf="@+id/tvDaySchedule" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <com.vietinbank.core_ui.base.views.BaseTextView
                    android:id="@+id/titleTypeFee"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp15"
                    android:text="Hình thức thu phí"
                    android:textColor="@color/text_blue_07"
                    android:textSize="@dimen/sp15"
                    app:fontCus="semi_bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/cl_daySchedule" />

                <com.vietinbank.core_ui.base.views.BaseTextView
                    android:id="@+id/tvTypeFee"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_blue_02"
                    android:textSize="@dimen/sp16"
                    app:fontCus="semi_bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/titleTypeFee"
                    tools:text="OUR" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/clNextApprover"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvTypeFee">

                    <com.vietinbank.core_ui.base.views.BaseTextView
                        android:id="@+id/titleNextApprover"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:clickable="false"
                        android:focusable="false"
                        android:text="Chọn người phê duyệt"
                        android:textColor="@color/text_blue_07"
                        android:textSize="@dimen/sp15"
                        app:fontCus="semi_bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.vietinbank.core_ui.base.views.BaseTextView
                        android:id="@+id/contentNextApprover"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="@dimen/dp5"
                        android:clickable="false"
                        android:focusable="false"
                        android:text="Theo đăng ký với VietinBank"
                        android:textColor="@color/text_blue_02"
                        android:textSize="@dimen/sp16"
                        app:fontCus="semi_bold"
                        app:layout_constraintEnd_toStartOf="@+id/imageView5"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/titleNextApprover" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/imageView5"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/ic_right"
                        android:clickable="false"

                        android:focusable="false"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

    <Button
        android:id="@+id/btnCreateMaker"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="Tiếp tục"
        android:textAllCaps="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>