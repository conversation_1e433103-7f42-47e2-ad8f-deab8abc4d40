<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_header"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp40"
        android:background="@drawable/bg_shape_drop_down"
        app:layout_constraintTop_toTopOf="parent">

        <com.vietinbank.core_ui.base.views.BaseTextView
            android:id="@+id/tv_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp15"
            android:text="<PERSON>h bạ và mẫu chuyển tiền"
            android:textColor="@color/white"
            android:textSize="@dimen/sp14"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_back"
            android:layout_width="@dimen/dp24"
            android:layout_height="@dimen/dp24"
            android:layout_marginEnd="@dimen/dp15"
            android:src="@drawable/ic_close"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/white" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cl_header">

        <com.vietinbank.core_ui.base.views.BaseTextView
            android:id="@+id/tvGotoContact"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:gravity="center"
            android:padding="@dimen/dp10"
            android:text="Danh bạ thụ hưởng"
            android:textColor="@color/text_blue_02"
            android:textSize="15sp"
            app:fontCus="semi_bold"
            app:layout_constraintEnd_toStartOf="@+id/tvGotoSample"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/lineContact"
            android:layout_width="0dp"
            android:layout_height="1.5dp"
            android:background="@color/text_blue_02"
            android:visibility="visible"
            app:layout_constraintEnd_toStartOf="@+id/tvGotoSample"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvGotoContact" />

        <com.vietinbank.core_ui.base.views.BaseTextView
            android:id="@+id/tvGotoSample"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:gravity="center"
            android:padding="@dimen/dp10"
            android:text="Mẫu chuyển tiền"
            android:textColor="@color/text_blue_02"
            android:textSize="15sp"
            app:fontCus="semi_bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/tvGotoContact"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/lineShample"
            android:layout_width="0dp"
            android:layout_height="1.5dp"
            android:background="@color/text_blue_02"
            android:visibility="invisible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/tvGotoContact"
            app:layout_constraintTop_toBottomOf="@+id/tvGotoSample" />

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@color/white"
            android:paddingTop="5dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/lineShample" />
    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>