<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"

>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imgIcon"
        android:layout_marginTop="@dimen/dp15"
        android:layout_marginBottom="@dimen/dp15"
        android:layout_width="@dimen/dp48"
        android:layout_height="@dimen/dp48"
        android:layout_marginLeft="@dimen/dp10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/tvBankName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp8"
        android:ellipsize="end"
        android:maxLines="1"
        app:fontCus="medium"
        android:singleLine="true"
        android:textColor="#062A46"
        android:textSize="@dimen/sp16"
        app:layout_constraintStart_toEndOf="@+id/imgIcon"
        app:layout_constraintTop_toTopOf="@+id/imgIcon"
        tools:text="00000" />

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/tvType"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp5"
        android:background="@drawable/bg_shape_text"
        android:paddingLeft="@dimen/dp8"
        android:paddingTop="@dimen/dp3"
        app:fontCus="medium"
        android:paddingRight="@dimen/dp8"
        android:paddingBottom="@dimen/dp3"
        android:textColor="#062A46"
        android:textSize="@dimen/sp14"
        app:layout_constraintStart_toEndOf="@+id/tvBankName"
        app:layout_constraintTop_toTopOf="@+id/tvBankName"
        tools:text="00000" />

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/tvName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp8"
        android:ellipsize="end"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="#8294a2"
        android:textSize="@dimen/sp14"
        app:layout_constraintBottom_toBottomOf="@+id/imgIcon"
        app:layout_constraintStart_toEndOf="@+id/imgIcon"
        tools:text="00000" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp1"
        android:background="@color/color_hint"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>