<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_white_radius_2dp"
        android:padding="@dimen/dp10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/AppCompatImageView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/ic_money_move"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.vietinbank.core_ui.base.views.BaseTextView
            android:id="@+id/tv1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp5"
            android:text="Chọn hình thức chuyển tiền"
            android:textColor="@color/text_blue_02"
            android:textSize="@dimen/sp14"
            app:fontCus="medium"
            app:layout_constraintBottom_toBottomOf="@+id/AppCompatImageView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/AppCompatImageView"
            app:layout_constraintTop_toTopOf="parent" />

        <com.vietinbank.core_ui.base.views.BaseTextView
            android:id="@+id/tvNote"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:background="@drawable/bg_yellow_radius_4dp"
            android:padding="8dp"
            android:text="Số tiền chuyển vượt quá hạn mức chuyển tiền nhanh (theo quy định của tổ chức Napas)"
            android:textColor="@color/text_gray_09"
            android:textSize="@dimen/sp12"
            app:fontCus="medium"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv1" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clSplit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:background="@drawable/boder_radius_5"
            android:clickable="true"
            android:descendantFocusability="blocksDescendants"
            android:focusable="true"
            android:padding="@dimen/dp8"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvNote">

            <CheckBox
                android:id="@+id/rbSplit"
                android:layout_width="@dimen/dp16"
                android:layout_height="@dimen/dp16"
                android:background="@drawable/radio_background"
                android:button="@null"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tvSplitTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp5"
                android:text="Chuyển nhanh - Tách giao dịch"
                android:textColor="@color/text_blue_02"
                android:textSize="@dimen/sp14"
                app:fontCus="semi_bold"
                app:layout_constraintBottom_toBottomOf="@+id/rbSplit"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/rbSplit"
                app:layout_constraintTop_toTopOf="@+id/rbSplit" />

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tvSplitContent"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp5"
                android:lineSpacingExtra="@dimen/dp3"
                android:text="VietinBank sẽ thực hiện tách thành các giao dịch có số tiền tối đa 490,000,000 VND/giao dịch"
                android:textColor="@color/text_gray_08"
                android:textSize="@dimen/sp12"
                app:fontCus="medium"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvSplitTitle" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:background="@drawable/bg_blue02_10percent_raidius_8dp"
                android:padding="@dimen/dp10"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvSplitContent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/clInfoSplit"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_white_radius_8dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.vietinbank.core_ui.base.views.BaseTextView
                        android:id="@+id/tvSubTransactionCount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingHorizontal="@dimen/dp12"
                        android:paddingVertical="@dimen/dp8"
                        android:textColor="@color/text_gray_09"
                        android:textSize="@dimen/sp13"
                        app:fontCus="semi_bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="01 Giao dịch" />

                    <com.vietinbank.core_ui.base.views.BaseTextView
                        android:id="@+id/contentubTransactionCount"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:paddingHorizontal="@dimen/dp12"
                        android:paddingVertical="@dimen/dp8"
                        android:textColor="@color/text_gray_09"
                        android:textSize="@dimen/sp14"
                        app:fontCus="medium"
                        app:layout_constraintBottom_toBottomOf="@+id/tvSubTransactionCount"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/tvSubTransactionCount"
                        app:layout_constraintTop_toTopOf="@+id/tvSubTransactionCount"
                        tools:text="490,000,000 VND" />

                    <com.vietinbank.core_ui.base.views.BaseTextView
                        android:id="@+id/tvSubTransactionsRemainderAmount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingHorizontal="@dimen/dp12"
                        android:paddingVertical="@dimen/dp8"
                        android:textColor="@color/text_gray_09"
                        android:textSize="@dimen/sp13"
                        app:fontCus="semi_bold"

                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvSubTransactionCount"
                        tools:text="01 Giao dịch" />

                    <com.vietinbank.core_ui.base.views.BaseTextView
                        android:id="@+id/contenSubTransactionsRemainderAmount"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:paddingHorizontal="@dimen/dp12"
                        android:paddingVertical="@dimen/dp8"
                        android:textColor="@color/text_gray_09"
                        android:textSize="@dimen/sp14"
                        app:fontCus="medium"
                        app:layout_constraintBottom_toBottomOf="@+id/tvSubTransactionsRemainderAmount"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/tvSubTransactionsRemainderAmount"
                        app:layout_constraintTop_toBottomOf="@+id/contentubTransactionCount"
                        tools:text="490,000,000 VND" />

                    <com.vietinbank.core_ui.base.views.BaseTextView
                        android:id="@+id/tvTotalAmount"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:paddingHorizontal="@dimen/dp12"
                        android:paddingVertical="@dimen/dp8"
                        android:text="Tổng tiền chuyển"
                        android:textColor="@color/text_blue_02"
                        android:textSize="@dimen/sp14"
                        app:fontCus="regular_bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvSubTransactionsRemainderAmount" />

                    <com.vietinbank.core_ui.base.views.BaseTextView
                        android:id="@+id/contentTotalAmount"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:paddingHorizontal="@dimen/dp12"
                        android:paddingVertical="@dimen/dp8"
                        android:textColor="@color/text_blue_02"
                        android:textSize="@dimen/sp14"
                        app:fontCus="regular_bold"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/tvTotalAmount"
                        app:layout_constraintTop_toBottomOf="@+id/tvSubTransactionsRemainderAmount"
                        tools:text="700,000,000 VND" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/clFeeSplit"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:background="@drawable/bg_white_radius_8dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/clInfoSplit">

                    <com.vietinbank.core_ui.base.views.BaseTextView
                        android:id="@+id/tvFeeSplit"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:paddingHorizontal="@dimen/dp12"
                        android:paddingVertical="@dimen/dp8"
                        android:text="Phí tách giao dịch"
                        android:textColor="@color/text_blue_02"
                        android:textSize="@dimen/sp14"
                        app:fontCus="regular_bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.vietinbank.core_ui.base.views.BaseTextView
                        android:id="@+id/contentFeeSplit"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:paddingHorizontal="@dimen/dp12"
                        android:paddingVertical="@dimen/dp8"
                        android:textColor="@color/text_blue_02"
                        android:textSize="@dimen/sp14"
                        app:fontCus="regular_bold"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/tvFeeSplit"
                        app:layout_constraintTop_toTopOf="@+id/tvFeeSplit"
                        tools:text="700,000,000 VND" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/clInfoDebt"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:background="@drawable/bg_white_radius_8dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/clFeeSplit">

                    <com.vietinbank.core_ui.base.views.BaseTextView
                        android:id="@+id/tvInfoDebt"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:paddingHorizontal="@dimen/dp12"
                        android:paddingVertical="@dimen/dp8"
                        android:text="Hình thức báo nợ"
                        android:textColor="@color/text_blue_02"
                        android:textSize="@dimen/sp14"
                        app:fontCus="regular_bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/viewCollapse"
                        android:layout_width="@dimen/dp25"
                        android:layout_height="@dimen/dp25"
                        android:layout_marginRight="@dimen/dp6"
                        android:clickable="true"
                        app:layout_constraintBottom_toBottomOf="@+id/tvInfoDebt"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@+id/tvInfoDebt" />

                    <com.vietinbank.core_ui.base.views.BaseTextView
                        android:id="@+id/tvNoteSplit"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:lineSpacingExtra="@dimen/dp3"
                        android:maxLines="3"
                        android:paddingHorizontal="@dimen/dp12"
                        android:paddingBottom="@dimen/dp5"
                        android:text="• Sao kê tài khoản (chuyển) của Quý khách ghi nợ số tiền tổng (tổng số tiền giao dịch của các giao dịch được tách lệnh).\n
• Tất cả các giao dịch tách lệnh của Quý khách chỉ áp dụng hình thức thu phí là phí ngoài (Phí được thu ngoài số tiền chuyển).\n
• Đối với tách lệnh giao dịch, người thụ hưởng sẽ nhận được số lần ghi có tương ứng với số lệnh giao dịch được tách."
                        android:textColor="@color/text_gray_08"
                        android:textSize="@dimen/sp12"
                        app:fontCus="medium"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvInfoDebt" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clNormal"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:background="@drawable/boder_blue09_radius_5"
            android:clickable="true"
            android:descendantFocusability="blocksDescendants"
            android:padding="@dimen/dp8"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/clSplit">

            <CheckBox
                android:id="@+id/rbNormal"
                android:layout_width="@dimen/dp16"
                android:layout_height="@dimen/dp16"
                android:background="@drawable/radio_background"
                android:button="@null"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tvNormalTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp5"
                android:text="Chuyển thường"
                android:textColor="@color/text_blue_02"
                android:textSize="@dimen/sp14"
                app:fontCus="semi_bold"
                app:layout_constraintBottom_toBottomOf="@+id/rbNormal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/rbNormal"
                app:layout_constraintTop_toTopOf="@+id/rbNormal" />

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tvNormalContent"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp5"
                android:lineSpacingExtra="@dimen/dp3"
                android:text="Giao dịch được xử lý trong giờ làm việc của VietinBank và Ngân hàng nhận. Giao dịch sẽ cần từ 1-2 ngày để chuyển đến người thụ hưởng."
                android:textColor="@color/text_gray_08"
                android:textSize="@dimen/sp12"
                app:fontCus="medium"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvNormalTitle" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/clNormal">

            <com.vietinbank.core_ui.base.views.BaseButton
                android:id="@+id/btnBack"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp10"
                android:layout_marginEnd="@dimen/dp5"
                android:text="Quay lại"
                android:textAllCaps="false"
                app:layout_constraintEnd_toStartOf="@+id/btnContinue"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.vietinbank.core_ui.base.views.BaseButton
                android:id="@+id/btnContinue"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp5"
                android:layout_marginEnd="@dimen/dp10"
                android:text="Tiếp tục"
                android:textAllCaps="false"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/btnBack"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>