<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_header"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp40"
        android:background="@drawable/bg_shape_drop_down"
        app:layout_constraintTop_toTopOf="parent">

        <com.vietinbank.core_ui.base.views.BaseTextView
            android:id="@+id/tv_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp15"
            android:text="Chọn ngân hàng nhận"
            android:textColor="@color/white"
            android:textSize="@dimen/sp14"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_back"
            android:layout_width="@dimen/dp24"
            android:layout_height="@dimen/dp24"
            android:layout_marginEnd="@dimen/dp15"
            android:src="@drawable/ic_close"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/white" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_search"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp62"
        android:background="@color/white"
        android:elevation="@dimen/dp5"
        app:layout_constraintTop_toBottomOf="@+id/cl_header">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="@dimen/dp36"
            android:layout_marginHorizontal="@dimen/sp17"
            android:background="@drawable/bg_shape_search"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_search"
                android:layout_width="@dimen/dp26"
                android:layout_height="@dimen/dp24"
                android:layout_marginStart="@dimen/dp6"
                android:src="@drawable/ic_search"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <EditText
                android:id="@+id/et_search"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp6"
                android:background="@color/transparent"
                android:hint="Tìm kiếm"
                android:textColor="#062A46"
                android:textColorHint="@color/text_hint"
                android:textSize="@dimen/sp14"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/iv_search"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rcv_bank"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/white"
        android:orientation="vertical"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cl_search"
        tools:itemCount="5"
        tools:listitem="@layout/item_bank" />

</androidx.constraintlayout.widget.ConstraintLayout>