<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include
        android:id="@+id/openCustomToolbar"
        layout="@layout/custom_toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/dp10"
        android:background="@drawable/bg_white_radius_2dp"
        android:fillViewport="true"
        android:layout_marginBottom="@dimen/dp10"
        app:layout_constraintBottom_toTopOf="@+id/btnResultMaker"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/openCustomToolbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/rlResult"
            android:background="@drawable/bg_white_radius_2dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp16">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/imageView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/ic_phe_duyet_done"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/imageView2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp15"
                android:background="@drawable/ic_logo_horizoltal"
                android:scaleX="1.3"
                android:scaleY="1.3"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/imageView" />

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tvMessage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dp10"
                android:gravity="center"
                android:textColor="@color/text_blue_02"
                app:fontCus="regular"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/imageView2"
                tools:text="Giao idhchhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhh" />

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tvManageTransaction"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp10"
                android:gravity="center"
                android:text="Quản lý giao dịch eFAST"
                android:textColor="@color/text_blue_02"
                app:fontCus="regular_bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvMessage" />

            <LinearLayout
                android:id="@+id/lnAmount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp10"
                android:background="@drawable/bg_shape_text"
                android:orientation="vertical"
                android:padding="@dimen/dp10"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvManageTransaction">

                <com.vietinbank.core_ui.base.views.BaseTextView
                    android:id="@+id/tvAmount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:textColor="#006594"
                    android:textSize="@dimen/sp16"
                    app:fontCus="regular_bold"
                    tools:text="a" />

                <com.vietinbank.core_ui.base.views.BaseTextView
                    android:id="@+id/tvAmountReader"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:textColor="#006594"
                    android:textSize="@dimen/sp12"
                    app:fontCus="medium"
                    tools:text="aaaaa" />
            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rcv_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:orientation="vertical"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/lnAmount"
                tools:itemCount="5" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/rl_CreateTemplate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/text_blue_02_50"
                android:padding="5dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/rcv_info">

                <com.vietinbank.core_ui.base.views.BaseTextView
                    android:id="@+id/tvCreateTemplate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Lưu mẫu giao dịch"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    app:fontCus="medium"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/sw_CreateTemplate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingTop="5dp"
                    android:paddingBottom="5dp"
                    app:layout_constraintBottom_toBottomOf="@+id/tvCreateTemplate"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/tvCreateTemplate" />

            </androidx.constraintlayout.widget.ConstraintLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

    <Button
        android:id="@+id/btnResultMaker"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp10"
        android:layout_marginBottom="@dimen/dp10"
        android:text="Tạo giao dịch mới"
        android:textAllCaps="false"
        app:layout_constraintBottom_toTopOf="@id/btnRate"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <com.vietinbank.core_ui.base.views.BaseButton
        android:id="@+id/btnRate"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp48"
        android:layout_marginHorizontal="@dimen/dp10"
        android:layout_marginBottom="@dimen/dp10"
        android:text="Đánh giá dịch vụ"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>