<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical"
  >

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/tvPayeeName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp10"
        android:maxLines="2"
        android:ellipsize="end"
        android:textColor="@color/text_gray_08"
        android:textSize="@dimen/sp15"
        app:fontCus="medium"
        app:layout_constraintBottom_toBottomOf="@+id/icBank"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/icBank"
        app:layout_constraintTop_toTopOf="@+id/icBank"
        tools:text="00000" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/icBank"
        android:layout_width="25dp"
        android:layout_height="25dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/text_hint"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>