<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imgIcon"
        android:layout_width="@dimen/dp35"
        android:layout_height="@dimen/dp35"
        android:layout_marginLeft="@dimen/dp10"
        app:layout_constraintBottom_toBottomOf="@+id/tvRemark"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvAccountName" />

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/tvAccountName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="10dp"
        android:layout_marginTop="@dimen/dp5"
        android:ellipsize="end"
        android:textColor="@color/text_blue_02"
        android:textSize="@dimen/sp14"
        app:fontCus="semi_bold"
        android:maxLines="2"
        app:layout_constraintEnd_toStartOf="@+id/tvAmount"
        app:layout_constraintStart_toEndOf="@+id/imgIcon"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="0000000000000000000000000000000000000000000000000000000000000000000000000000" />

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/tvRemark"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp10"
        android:layout_marginBottom="@dimen/dp5"
        android:textSize="@dimen/sp12"
        app:fontCus="medium"
        android:textColor="@color/color_hint"
        app:layout_constraintBottom_toBottomOf="@+id/view"
        app:layout_constraintEnd_toStartOf="@+id/tvAmount"
        app:layout_constraintStart_toEndOf="@+id/imgIcon"
        app:layout_constraintTop_toBottomOf="@+id/tvAccountName"
        tools:text="00000" />

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/tvAmount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/sp14"
        app:fontCus="semi_bold"
        android:layout_marginRight="@dimen/dp10"
        android:textColor="@color/text_blue_02"
        app:layout_constraintBottom_toBottomOf="@+id/imgIcon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/imgIcon"
        tools:text="00000" />

    <View
        android:id="@+id/view"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/text_hint"
        app:layout_constraintBottom_toBottomOf="@id/tvRemark"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>