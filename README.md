# VietinBank eFast

<div align="center">
  <img src="docs/logo.png" alt="VietinBank eFast" width="200"/>
  
  [![Android](https://img.shields.io/badge/Platform-Android-green.svg)](https://developer.android.com)
  [![Kotlin](https://img.shields.io/badge/Kotlin-2.0.21-blue.svg)](https://kotlinlang.org)
  [![Min SDK](https://img.shields.io/badge/Min%20SDK-24-brightgreen.svg)](https://developer.android.com/distribute/best-practices/develop/target-sdk)
  [![Target SDK](https://img.shields.io/badge/Target%20SDK-35-brightgreen.svg)](https://developer.android.com/distribute/best-practices/develop/target-sdk)
</div>

## 📋 Executive Summary

VietinBank eFast is a sophisticated, security-first Android banking application built with modern architecture principles and cutting-edge technologies. The application provides comprehensive banking services including account management, transactions, maker-checker workflows, and advanced security features.

### Key Highlights
- **🏗️ Architecture**: Multi-module Clean Architecture with MVVM pattern
- **🎨 UI Framework**: Jetpack Compose with Material3 and custom Foundation Design System (also
- XML support)
- **🔐 Security**: Native C++ security library with anti-tampering and key obfuscation
- **📦 Modules**: 16+ feature and core modules for maximum scalability
- **🚀 Performance**: Optimized with Kotlin Coroutines and efficient state management
- **📱 Modern Stack**: Latest Android SDK 35, Kotlin 2.0, and Compose BOM 2025

## 📚 Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Module Catalog](#module-catalog)
3. [Technology Stack](#technology-stack)
4. [Security Architecture](#security-architecture)
5. [Foundation Design System](#foundation-design-system)
6. [Getting Started](#getting-started)
7. [Development Workflow](#development-workflow)
8. [Build & Deployment](#build--deployment)
9. [Testing Strategy](#testing-strategy)
10. [Performance Guidelines](#performance-guidelines)
11. [Resource Naming Conventions](#resource-naming-conventions)
12. [Resource Architecture Guidelines](#resource-architecture-guidelines)
13. [Contributing](#contributing)
14. [Troubleshooting](#troubleshooting)
15. [Additional Resources](#additional-resources)

## 🏗️ Architecture Overview

The application follows **Clean Architecture** principles with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                     Presentation Layer                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐    │
│  │  Compose UI  │  │ ViewModels  │  │ State Management│    │
│  └─────────────┘  └─────────────┘  └─────────────────┘    │
├─────────────────────────────────────────────────────────────┤
│                      Domain Layer                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐    │
│  │  Use Cases  │  │   Models    │  │  Repositories   │    │
│  └─────────────┘  └─────────────┘  │  (Interfaces)   │    │
│                                     └─────────────────┘    │
├─────────────────────────────────────────────────────────────┤
│                       Data Layer                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐    │
│  │ Repository  │  │   Remote    │  │     Local       │    │
│  │    Impl     │  │ Data Source │  │  Data Source    │    │
│  └─────────────┘  └─────────────┘  └─────────────────┘    │
└─────────────────────────────────────────────────────────────┘
```

### Key Architectural Decisions
- **Single Activity Architecture**: One activity hosts all screens via Navigation Component
- **MVVM Pattern**: ViewModels manage UI state with StateFlow/SharedFlow
- **Dependency Injection**: Hilt provides compile-time safe DI
- **Modularization**: Feature modules for scalability and build performance

## 📦 Module Catalog

### Core Modules

| Module | Purpose                                                     |
|--------|-------------------------------------------------------------|
| **core-common** | Shared utilities, constants, extensions, session management |
| **core-data** | Repository implementations, encryption service, API clients |
| **core-domain** | Business logic, use cases, repository interfaces, models    |
| **core-ui** | Foundation Design System, base UI components, themes        |
| **core-ott** | One-Time Token notification system and handlers             |
| **core-keyboard** | ....                                                        |

### Feature Modules

| Module | Purpose |
|--------|---------|
| **feature-login** | Authentication, biometric login, session initialization |
| **feature-home** | Dashboard, quick actions, account summary |
| **feature-account** | Account details, balance inquiry, statements |
| **feature-transaction-manage** | Transaction history, search, filters |
| **feature-maker** | Transaction creation (Maker-Checker pattern) |
| **feature-checker** | Transaction approval (Maker-Checker pattern) |
| **feature-ekyc** | Electronic KYC with document scanning and biometrics |
| **feature-smartCA** | Smart Certificate Authority for digital signatures |
| **feature-soft** | Soft token generation and management |
| **feature-list-type-ott** | OTT transaction listing and management |
| **feature_trace_payment** | Payment tracking and status updates |
| **feature_report** | Reports, analytics, and data visualization |

### Application Module

| Module | Purpose |
|--------|---------|
| **app** | Application entry point, navigation host, DI setup |

## 🛠️ Technology Stack

### Core Technologies
```kotlin
// Build Configuration
Android Gradle Plugin: 8.5.2
Kotlin: 2.0.21
Java: 11
Gradle: 8.7

// Android SDK
Compile SDK: 35
Target SDK: 35
Min SDK: 24
```

### Key Dependencies
```kotlin
// UI Framework
Jetpack Compose BOM: 2025.02.00
Compose Compiler: 1.5.15
Material3: Latest from BOM
Coil: 3.1.0

// Architecture Components
Hilt: 2.56.1
Navigation Component: 2.7.7
Lifecycle: 2.6.1
ViewModel Compose: 2.8.5

// Networking
Retrofit: 2.9.0
OkHttp: 4.9.0
Gson: 2.10.1

// Async
Coroutines: 1.8.0

// Security
Security Crypto: 1.1.0-alpha06
Biometric: 1.1.0

// Firebase
Firebase BOM: 33.9.0
- Crashlytics
- Cloud Messaging

// Camera (for eKYC)
CameraX: 1.3.4
ML Kit Face Detection: 16.1.5
```

## 🔐 Security Architecture

### Multi-Layer Security Model

1. **Native Security Library**
   - C++ implementation for critical operations
   - Key obfuscation with XOR encryption
   - Anti-debugging and anti-tampering protection
   - Signature generation and validation
   - Performance-optimized with caching

2. **Application Security**
   - EncryptedSharedPreferences for sensitive data
   - Biometric authentication support
   - Certificate pinning for network security
   - ProGuard/R8 obfuscation
   - Single instance enforcement

3. **Session Management**
   - Secure session tokens
   - Automatic session timeout
   - Complete data cleanup on logout
   - Protected against replay attacks

### Security Best Practices
- All sensitive operations handled in native layer
- No hardcoded keys in Java/Kotlin code
- Environment-specific key management
- Comprehensive audit logging

## 🎨 Foundation Design System

The application uses a custom Foundation Design System built on top of Material3, providing:

### Components
- **FoundationButtonLight**: Primary action buttons with gradient effects
- **FoundationAppBar**: Custom app bar with glass morphism effects
- **FoundationTabs**: Tabbed navigation with animations
- **CircularIconButton**: Glass morphism circular buttons

### Design Tokens
```kotlin
// Spacing
foundation_spacing_xs: 4dp
foundation_spacing_sm: 8dp
foundation_spacing_md: 16dp
foundation_spacing_lg: 24dp
foundation_spacing_xl: 32dp

// Radius
foundation_radius_xs: 4dp
foundation_radius_sm: 8dp
foundation_radius_default: 12dp
foundation_radius_lg: 16dp
foundation_radius_xl: 24dp

// Elevation
elevationXs: 2dp
elevationSm: 4dp
elevationMd: 8dp
elevationLg: 16dp
```

### 🎨 Foundation Design System (FDS) - The Only Way

**Quick Setup**: Import once, use everywhere
```kotlin
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS
```

#### Colors - Đừng hardcode, dùng FDS!
```kotlin
// ✅ ĐÚNG - Semantic colors cho common UI
FDS.Colors.primary          // Brand chính (#006FBD)
FDS.Colors.error           // Error states  
FDS.Colors.success         // Success states

// ✅ ĐÚNG - Text colors (match Figma exactly)
FDS.Colors.textPrimary     // Text chính
FDS.Colors.textSecondary   // Text phụ
FDS.Colors.textOnPrimary   // Text trên nền primary

// ❌ SAI - Đừng làm thế này
Color(0xFF006FBD)          // NEVER hardcode!
appColor.primary           // Old way, don't use
```

#### Typography - Text styles có sẵn, xài thôi
```kotlin
// Headings - dùng cho titles
FDS.Typography.headingH1    // 32sp - Screen titles
FDS.Typography.headingH2    // 24sp - Section headers
FDS.Typography.headingH3    // 20sp - Card titles

// Body text - cho content
FDS.Typography.bodyB1       // 16sp - Normal text
FDS.Typography.bodyB2       // 14sp - Secondary text

// Buttons & Links
FDS.Typography.interactionButton  // Button text
FDS.Typography.interactionLink    // Clickable links

// Real example từ LoginScreen:
FoundationText(
    text = "Đăng nhập",
    style = FDS.Typography.headingH2,
    color = FDS.Colors.textPrimary
)
```

#### Spacing & Dimensions - Consistent everywhere
```kotlin
// Padding - dùng cho internal spacing
FDS.Sizer.Padding.padding8   // Small gaps
FDS.Sizer.Padding.padding16  // Standard spacing (most common)
FDS.Sizer.Padding.padding24  // Large sections

// Gaps - spacing between elements
FDS.Sizer.Gap.gap8          // Between related items
FDS.Sizer.Gap.gap16         // Between sections

// Border Radius
FDS.Sizer.Radius.radius8    // Cards, inputs
FDS.Sizer.Radius.radius32   // Buttons (default)
FDS.Sizer.Radius.radiusFull // Circular (999dp)

// Icon sizes
FDS.Sizer.Icon.icon24       // Standard icons
FDS.Sizer.Icon.icon32       // Large icons
```

#### Quick Tips 💡
1. **Designer nói gì, dùng đó**: "Use blue500" → `FDS.Colors.blue500`
2. **Không tìm thấy?** Check `FoundationDesignSystem.kt` trước khi hardcode
3. **Elevation?** `FDS.Effects.elevationSm` cho cards, `elevationMd` cho dialogs

### 🎯 UI Components - Use Foundation, Not Default!

#### Available Foundation Components
```kotlin
// ✅ USE THESE (from core-ui)
FoundationButton      // Thay cho Button
FoundationText        // Thay cho Text  
FoundationFieldType    // Thay cho TextField
FoundationDialog      // Thay cho AlertDialog
FoundationAppBar      // Thay cho TopAppBar
CircularIconButton    // Glass morphism buttons

// ❌ DON'T USE default Compose (unless no Foundation equivalent)
Button()             // ❌ Use FoundationButton
Text()              // ❌ Use FoundationText
TextField()         // ❌ Use FoundationFieldType
```

#### Critical: Banking App Requirements
```kotlin
// ⚠️ ALWAYS use safeClickable for ALL clickable elements
Modifier.safeClickable { onClick() }  // ✅ Prevents double-click
Modifier.clickable { onClick() }       // ❌ Risk duplicate transactions!

// Use Channel for one-time events (navigation, dialogs)
private val _events = Channel<LoginEvent>(Channel.BUFFERED)
val events = _events.receiveAsFlow()
// NEVER use LiveData for navigation in banking apps!
```

### 💬 Dialog & Bottom Sheet System - Hiểu Rõ Để Chọn Đúng

#### 🤔 BaseBottomSheet vs BaseDialog - Bản Chất Thực Sự

Nhiều dev hay bị confuse giữa 2 thằng này vì cả 2 đều có thể show content từ bottom lên. Nhưng thực ra chúng khác nhau về **bản chất kiến trúc** chứ không phải chỉ khác về UI.

##### BaseBottomSheet - Thuần Compose, Nhẹ Nhàng
BaseBottomSheet được build trên **ModalBottomSheet** của Material3. Nó sống trong thế giới Compose, không phải là Fragment. Data flow qua lambda callbacks, giống như mọi Composable khác.

**Điều này có nghĩa gì?**
- Khi màn hình rotate hoặc app bị system kill → sheet biến mất, state mất (trừ khi bạn tự handle)
- Muốn truyền data? Dùng lambda callback, không có "Fragment result" gì cả
- Animation mượt mà native của Material3, swipe gesture xịn sò
- Code gọn, không cần tạo class riêng, viết ngay trong Composable

**Khi nào nó phù hợp?**
Hãy nghĩ đến những thao tác "tạm thời", "không critical":
- User chọn 1 option rồi sheet đóng → done, không cần nhớ gì
- Hiển thị info nhanh, user đọc xong swipe đóng
- Các filter/sort mà apply ngay vào UI hiện tại

##### BaseDialog - DialogFragment Mạnh Mẽ  
BaseDialog là **DialogFragment** với Compose UI bên trong. Đây là citizen hạng nhất trong Android, có lifecycle riêng, được system quản lý.

**Điều này có nghĩa gì?**
- App bị kill? Không sao, Android tự restore dialog khi user quay lại
- Data truyền qua Fragment Result API - chuẩn Android, test dễ
- Có thể set window flags riêng (FLAG_SECURE chỉ cho dialog, không ảnh hưởng Activity)
- Phức tạp hơn một chút: cần tạo class, implement abstract methods

**Khi nào nó phù hợp?**
Những thứ "quan trọng", "cần đảm bảo":
- User đang nhập form dài → app crash → mở lại vẫn còn dialog với data
- Cần theo dõi kết quả qua Fragment Result API (dễ test, debug)
- Security: chỉ muốn dialog này không bị screenshot, còn lại app vẫn bình thường

#### 📦 Những Gì Đã Được Làm Sẵn (Đừng Code Lại!)

##### BaseBottomSheet đã làm sẵn cho bạn:
```kotlin
// Edge-to-edge: Scrim phủ cả status bar, không bị "lủng" ở trên
// FLAG_SECURE: Tự động bật/tắt khi sheet show/hide
secureFlag = true

// IME handling: Keyboard đẩy sheet lên, tự động padding
// Không cần làm gì thêm, nó just works

// Duplicate result prevention: Đang animate thì không fire result
// Tránh case user tap nhanh 2 lần → 2 results

// Navigation bar: Tự động padding để content không bị che
// Tablet: maxWidthDp để sheet không quá rộng trên màn lớn

// Touch dismiss control: 
allowTouchDismiss = false  // Banking app: không cho dismiss tùy tiện
```

##### BaseDialog đã làm sẵn cho bạn:
```kotlin
// Process death recovery: Tự động restore sau khi app bị kill
// Không cần SavedStateHandle hay gì cả

// Fragment Result API: Type-safe, không cast loạn xạ
@Parcelize data class Result(...) : Parcelable
// Parent Fragment tự listen, dialog không cần biết parent là ai

// IME smart back: Nhấn back → đóng keyboard trước → sau đó mới đóng dialog
// User experience tốt hơn nhiều

// Dialog stack: Multi-dialog support, quản lý thứ tự đóng/mở
// Animation state sync: Exit animation luôn chạy hết mới dismiss

// System bars: Tự động set dark icons khi scrim show
// Window isolation: FLAG_SECURE chỉ ảnh hưởng dialog
```

#### 💡 Tư Duy Thực Tế Khi Chọn

**Đừng nghĩ "bottom sheet UI = BaseBottomSheet"**. Cả BaseDialog cũng có thể show từ bottom lên (DialogLayout.Bottom).

**Hãy tự hỏi:**

1. **"Nếu user đang dùng cái này mà app crash, có sao không?"**
   - Không sao → BaseBottomSheet có thể OK
   - Mất data/state quan trọng → BaseDialog an toàn hơn

2. **"Code này cần test/debug thế nào?"**
   - Simple UI, test Compose là đủ → BaseBottomSheet 
   - Complex flow, cần mock/verify results → BaseDialog với Fragment Result API

3. **"Security requirement?"**
   - Toàn app không cho screenshot → Cả 2 đều OK
   - Chỉ riêng popup này sensitive → BaseDialog (window flags isolation)

4. **"Team quen với pattern nào?"**
   - Team Compose-first → BaseBottomSheet dễ maintain
   - Team quen Fragment architecture → BaseDialog familiar hơn

#### 🎯 Code Examples (Chỉ Để Demo Capability!)

##### BaseBottomSheet - Show vài options nhanh
```kotlin
// Inline ngay trong Composable, không cần tạo class
@Composable
fun MyScreen() {
    var showOptions by remember { mutableStateOf(false) }
    
    // Sheet nhẹ nhàng, đóng/mở thoải mái
    BaseBottomSheet(
        visible = showOptions,
        onDismissRequest = { showOptions = false }
    ) {
        // Vài options để user chọn
        OptionItem("Edit") { /* do edit */ }
        OptionItem("Share") { /* do share */ }
        OptionItem("Delete") { /* do delete */ }
    }
}
```

##### BaseDialog - Form phức tạp cần đảm bảo
```kotlin
// Tạo class riêng, rõ ràng lifecycle
class ComplexFormDialog : BaseDialog<FormData>() {
    override val resultKey = "form_result"
    override val requiresSecureFlag = true  // Sensitive data
    
    @Composable
    override fun DialogContent(...) {
        // Form với validation, multiple steps
        // System đảm bảo không mất data khi rotate/kill
    }
}

// Parent Fragment handle result một cách chuẩn mực
childFragmentManager.setFragmentResultListener(
    "form_result",
    viewLifecycleOwner
) { _, bundle ->
    // Type-safe result handling
}
```

#### ⚠️ Lưu Ý

Banking app có requirements khắt khe về security và reliability. Một số nguyên tắc:

1. **Transaction flows** - Tendency về phía an toàn: BaseDialog
2. **Quick actions** - Nếu không ảnh hưởng data: BaseBottomSheet cũng OK  
3. **Sensitive data input** - BaseDialog với FLAG_SECURE
4. **Info display** - Tùy: quan trọng thì Dialog, thông tin phụ thì Sheet

Nhưng đây **không phải rules cứng**. Mỗi feature có context riêng, hãy cân nhắc kỹ.

#### 🔧 Migration Path

Nếu đang dùng custom bottom sheet cũ:
1. Xem nó có cần state recovery không?
2. Check xem có Fragment Result API không?
3. Nếu cả 2 đều không → BaseBottomSheet phù hợp
4. Nếu cần 1 trong 2 → BaseDialog an toàn hơn

**Remember**: Không có "đúng" hay "sai" tuyệt đối. Chỉ có "phù hợp" với use case cụ thể.

### Theme Structure
- **Foundation Design System (FDS)** is the single source of truth
- All design tokens centralized in `FoundationDesignSystem.kt`
- Automatic Material3 theming integration
- Dark theme support (coming soon)

## 🚀 Getting Started

### Prerequisites
- Android Studio Ladybug (2024.2.1) or newer
- JDK 11 or higher
- Android SDK with API 35
- Git

### Clone and Setup
```bash
# Clone the repository
git clone http://10.2.139.145/a200_mbc/app/efast_android.git

# Navigate to project directory
cd efast_android

# Copy local properties template
cp local.properties.template local.properties

# Edit local.properties with your SDK path and signing configs
```

### Build Instructions
```bash
# Build all modules
./gradlew build

# Build specific flavor
./gradlew assembleUatDebug    # UAT environment
./gradlew assemblePpeRelease  # PPE environment  
./gradlew assembleLiveRelease # Production

# Run tests
./gradlew test

# Run lint checks
./gradlew lint
```

### Native Library Setup
```bash
# Copy native libraries from security project
cp /path/to/securitylib/build/intermediates/cmake/debug/obj/* \
   app/src/main/jniLibs/

# Verify library structure
app/src/main/jniLibs/
├── arm64-v8a/
│   └── libefastsecurity.so
├── armeabi-v7a/
│   └── libefastsecurity.so
└── x86_64/
    └── libefastsecurity.so
```

## 💻 Development Workflow

### Git Hooks
Pre-push hooks are automatically installed to:
- Run ktlint formatting
- Execute unit tests
- Check for hardcoded secrets

## 🏗️ Build & Deployment

### Build Variants

| Flavor | Purpose | Server | Version Code |
|--------|---------|--------|--------------|
| **uat** | Testing | UAT Server | 30000 + base |
| **ppe** | Pre-production | PPE Server | 30000 + base |
| **live** | Production | Live Server | 30000 + base |

### ProGuard Configuration
```
✓ Code obfuscation enabled for release
✓ Resource shrinking active
✓ Native library symbols preserved
✓ Crashlytics mapping uploaded
```

### Firebase App Distribution
UAT builds are automatically distributed via Firebase:
```gradle
firebaseAppDistribution {
    apkPath = "build/outputs/apk/uat/release/app-uat-release.apk"
    groups = "eFast-testers"
}
```

## 🧪 Testing Strategy

### Testing Pyramid
```
         ╱╲
        ╱  ╲      UI Tests (Compose)
       ╱────╲     - User flows
      ╱      ╲    - Screenshot tests
     ╱────────╲   
    ╱          ╲  Integration Tests
   ╱────────────╲ - API integration
  ╱              ╲- Database tests
 ╱────────────────╲
╱                  ╲ Unit Tests
────────────────────  - ViewModels, Use Cases, Utils
```

## ⚡ Performance Guidelines

### Compose Performance
- Use `remember` for expensive computations
- Implement `key` parameter in lists
- Avoid inline lambdas in composables
- Use `ImmutableList` for stable parameters

### Coroutines Best Practices
```kotlin
// Use SupervisorJob for error isolation
private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

// Always handle cancellation
override fun onCleared() {
    scope.cancel()
    super.onCleared()
}

// Prefer Flow over LiveData
val uiState: StateFlow<UiState> = _uiState.asStateFlow()
```

### Memory Management
- Clear references in `onCleared()`
- Use `WeakReference` for callbacks
- Implement proper image caching
- Monitor with LeakCanary in debug

### Network Optimization
- Implement request caching
- Use pagination for lists
- Compress request/response data
- Monitor with network profiler

## 📁 Resource Naming Conventions

### Overview
Consistent resource naming is crucial for maintainability and scalability in our multi-module architecture. All resources must follow these conventions.

### 1. Icons (Drawables)
**Pattern**: `ic_{module}_{description}_{size}`

Examples:
- `ic_common_close.xml`
- `ic_login_password.xml`
- `ic_transfer_success_24dp.xml`

### 2. Backgrounds & Shapes
**Pattern**: `bg_{module}_{component}_{state/variant}`

Examples:
- `bg_common_button_primary_enabled.xml`
- `bg_common_button_primary_disabled.xml`
- `bg_login_input_field_default.xml`

### 3. Selectors (State Lists)
**Pattern**: `selector_{module}_{component}_{purpose}`

Examples:
- `selector_common_button_primary.xml`
- `selector_login_checkbox.xml`
- `selector_common_radio_button.xml`

### 4. Borders
**Pattern**: `border_{module}_{component}_{variant}`

Examples:
- `border_common_input_default.xml`
- `border_common_input_error.xml`
- `border_transfer_amount_focused.xml`

### 5. Strings
**Pattern**: `{feature}_{screen/component}_{description}` (centralized in core-ui)

Examples:
```xml
<string name="login_username_label">Tên đăng nhập</string>
<string name="login_password_error">Mật khẩu không hợp lệ</string>
<string name="transfer_confirmation_title">Xác nhận chuyển tiền</string>
<string name="common_button_confirm">Xác nhận</string>
<string name="common_error_network">Lỗi kết nối mạng</string>
```

> 📍 **Note**: All strings are centralized in `core-ui` module. See [Resource Architecture Guidelines](#resource-architecture-guidelines) for details.

### 6. Colors
**Pattern**: Use Foundation Design System naming

```xml
<!-- Semantic colors (preferred) -->
<color name="foundation_primary">#006FBD</color>
<color name="foundation_text_primary">#1A1A1A</color>
<color name="foundation_background">#FFFFFF</color>

<!-- Component-specific colors -->
<color name="foundation_button_gradient_primary">#FFBBE8FF</color>
<color name="foundation_input_border_error">#FFE53935</color>
```

### 7. Dimensions
**Pattern**: Use Foundation Design System tokens

```xml
<!-- Spacing (use semantic names) -->
<dimen name="foundation_spacing_xs">4dp</dimen>
<dimen name="foundation_spacing_sm">8dp</dimen>
<dimen name="foundation_spacing_md">16dp</dimen>
<dimen name="foundation_spacing_lg">24dp</dimen>
<dimen name="foundation_spacing_xl">32dp</dimen>

<!-- Component-specific -->
<dimen name="foundation_button_height_medium">48dp</dimen>
<dimen name="foundation_input_height">56dp</dimen>
<dimen name="foundation_radius_default">8dp</dimen>
```

### 8. Menus
**Pattern**: `menu_{module}_{screen/purpose}`

Examples:
- `menu_home_bottom_navigation.xml`
- `menu_transfer_options.xml`
- `menu_common_overflow.xml`

### 9. Animations
**Pattern**: `anim_{module}_{transition}_{direction/type}`

Examples:
- `anim_common_fade_in.xml`
- `anim_common_fade_out.xml`
- `anim_login_slide_in_right.xml`
- `anim_transfer_success_scale_up.xml`

### State Suffixes
- `_normal` - Default state
- `_pressed` - Pressed/active state
- `_focused` - Focused state
- `_disabled` - Disabled state
- `_selected` - Selected state
- `_error` - Error state
- `_loading` - Loading state

## 🏛️ Resource Architecture Guidelines

### Overview
Resources are organized following the principle: **SHARED/REUSABLE → core-ui**, **FEATURE-SPECIFIC → feature module**. This architecture ensures consistency while maintaining module independence.

### Resource Organization Strategy

#### 1. Strings → ✅ CENTRALIZED in core-ui
All strings are centralized for consistency and localization:
```
core-ui/res/values/
├── strings_common.xml      // Shared strings (OK, Cancel, Error)
├── strings_login.xml       // Login feature strings
├── strings_transfer.xml    // Transfer feature strings
└── strings_account.xml     // Account feature strings
```

**Pattern**: `{feature}_{screen/component}_{description}` (no module prefix needed)
```xml
<string name="login_username_label">Tên đăng nhập</string>
<string name="transfer_confirm_title">Xác nhận chuyển tiền</string>
<string name="common_button_ok">Đồng ý</string>
```

#### 2. Colors → ✅ CENTRALIZED in core-ui
- All Foundation Design System colors
- Semantic colors (primary, error, success)
- No feature-specific colors allowed

#### 3. Dimensions → ✅ CENTRALIZED in core-ui
- Foundation spacing system (xs, sm, md, lg, xl)
- Component sizes (button heights, input heights)
- Consistent radius values

#### 4. Icons (Drawables) → 🔄 HYBRID Approach
```
core-ui/res/drawable/
├── ic_common_back.xml       // Shared icons
├── ic_common_close.xml
├── ic_common_error.xml
└── ic_common_success.xml

feature-transfer/res/drawable/
├── ic_transfer_bank.xml     // Feature-specific icons
└── ic_transfer_qr.xml
```

#### 5. Backgrounds/Shapes → 🔄 HYBRID Approach
```
core-ui/res/drawable/
├── bg_common_button_primary.xml   // Design system components
├── bg_common_card.xml
└── shape_rounded_corner_8dp.xml

feature-ekyc/res/drawable/
├── bg_ekyc_camera_overlay.xml     // Feature-unique backgrounds
└── shape_face_detection_frame.xml
```

#### 6. Animations → 🔄 HYBRID Approach
```
core-ui/res/anim/
├── anim_common_fade_in.xml        // System animations
└── anim_common_slide_up.xml

feature-login/res/anim/
└── anim_login_fingerprint_pulse.xml  // Feature-specific animations
```

### Resource Decision Matrix

| Resource Type | Location | Rationale |
|--------------|----------|-----------|
| **Strings** | core-ui | Consistency, localization |
| **Colors** | core-ui | Brand consistency |
| **Dimensions** | core-ui | Design system tokens |
| **Common Icons** | core-ui | Reusability (back, close) |
| **Feature Icons** | feature-* | Module encapsulation |
| **Common Shapes** | core-ui | Design system components |
| **Feature Shapes** | feature-* | Unique UI needs |
| **System Animations** | core-ui | Consistent transitions |
| **Feature Animations** | feature-* | Specific interactions |

### Architecture Benefits
- **Module Independence**: Features can be developed/tested in isolation
- **Design System Enforcement**: Common UI patterns centralized
- **Build Performance**: Optimized with `android.nonTransitiveRClass=true`
- **Team Scalability**: Clear ownership boundaries, less merge conflicts
- **Maintainability**: Single source of truth for shared resources

## 🤝 Contributing

### Code Style
- Follow [Kotlin official style guide](https://kotlinlang.org/docs/coding-conventions.html)
- Use ktlint for formatting
- Maximum line length: 120 characters

### Commit Message Format
```
type(scope): subject

body

footer
```

Types: feat, fix, docs, style, refactor, test, chore

### Documentation Requirements
- Update README for architectural changes
- Document new modules in module catalog
- Add KDoc for public APIs
- Include examples for complex features

## 🔧 Troubleshooting

### Common Issues

#### Build Failures
```bash
# Clean build cache
./gradlew clean
rm -rf ~/.gradle/caches/

# Invalid cache
./gradlew --refresh-dependencies
```

#### Native Library Issues
```
UnsatisfiedLinkError: Couldn't load efastsecurity
Solution: Verify .so files are in correct jniLibs structure
```

#### Dependency Conflicts
```bash
# View dependency tree
./gradlew app:dependencies

# Find specific dependency
./gradlew app:dependencyInsight --dependency retrofit
```

#### Memory Issues
```
# Increase Gradle heap
org.gradle.jvmargs=-Xmx4g -XX:+HeapDumpOnOutOfMemoryError
```

### Debug Tools
- Layout Inspector for Compose
- Network Profiler for API calls
- Memory Profiler for leaks
- Firebase Crashlytics dashboard

## 📚 Additional Resources

### External Resources
- [Android Developers](https://developer.android.com)
- [Jetpack Compose Documentation](https://developer.android.com/jetpack/compose)
- [Kotlin Documentation](https://kotlinlang.org/docs)
- [Material Design 3](https://m3.material.io)

---

<div align="center">
  <p>Built with ❤️ by VietinBank eFAST Team</p>
  <p>© 2024 VietinBank. All rights reserved.</p>
</div>