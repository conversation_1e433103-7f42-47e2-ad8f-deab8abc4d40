[versions]
# SDK versions
activityCompose = "1.10.0"
androidx-material = "1.7.8"
biometric = "1.1.0"
composeBom = "2025.02.00" # BOM Jetpack Compose
constraintlayoutCompose = "1.1.1"
lifecycleViewmodelCompose = "2.8.5"
material3WindowSizeClass = "1.2.0"
minSdk = "24"
targetSdk = "35"
compileSdk = "35"

mapstruct = "1.6.3"

compose-compiler = "1.5.15"      # Compose Compiler khớp với BOM
navigation-compose = "2.7.7"
google-services = "4.4.2"
firebase-bom = "33.9.0"
glide-ver = "4.16.0"
zxing-ver = "1.9.8"
viewpager-ver = "1.0.0"
flexbox-ver = "3.0.0"
#lottie
lottie = "6.1.0"

ktlint = "0.50.0"
ktlint-gradle = "11.6.1"



# Kotlin and build tools
kotlin = "2.0.21"
agp = "8.5.2"

# Hilt
hilt = "2.56.1"
hilt-navigation = "1.2.0"

# AndroidX and Google
core-ktx = "1.12.0"
appcompat = "1.6.1"
material = "1.11.0"
navigation = "2.7.7"
safe-args = "2.3.0"
lifecycle = "2.6.1"
coil = "3.1.0"
security-crypto = "1.1.0-alpha06"

# Networking
retrofit = "2.9.0"
okhttp = "4.9.0"
gson = "2.10.1"

# Dependency Injection
koin = "3.4.0"
koin-core = "3.4.0"

# Coroutines
coroutines = "1.8.0"
coroutines-stdlib = "2.0.21"

# Testing
junit = "4.13.2"
androidx-test-ext = "1.1.5"
espresso = "3.5.1"
activity = "1.9.3"
constraintlayout = "2.1.4"
room = "2.7.1"
webkit = "1.12.1"
legacySupportV4 = "1.0.0"
fragmentKtx = "1.8.0"
runtimeLivedata = "1.7.8"
uiUnitAndroid = "1.7.8"
pdfView = "3.2.0-beta.3"
firebaseCrashlyticsKtx = "19.4.3"

#core_ekyc
camerax_version= "1.3.4"
odml_image="1.0.0-beta1"
face-detection="16.1.5"
guava="27.0.1-android"
rxjava = "2.2.6"
rxandroid = "2.1.1"
retrofit2_rxjava2_adapter = "1.0.0"
androidx_annotation = "1.6.0"
androidx_window = "1.0.0"
lifecycle_runtime_ktx = "2.4.0"
commons_codec = "1.16.0"
androidx_exifinterface = "1.3.6"
tensorflow_lite = "2.14.0"
tensorflow_lite_support = "0.4.4"
[libraries]
#compose
androidx-biometric = { module = "androidx.biometric:biometric", version.ref = "biometric" }
androidx-constraintlayout-compose = { module = "androidx.constraintlayout:constraintlayout-compose", version.ref = "constraintlayoutCompose" }
androidx-material = { module = "androidx.compose.material:material", version.ref = "androidx-material" }
androidx-material3-window-size-class1 = { module = "androidx.compose.material3:material3-window-size-class", version.ref = "material3WindowSizeClass" }
compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
compose-ui = { group = "androidx.compose.ui", name = "ui" }
compose-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
compose-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-material3 = { module = "androidx.compose.material3:material3" }
androidx-ui-test-junit4 = { module = "androidx.compose.ui:ui-test-junit4" }
androidx-ui-test-manifest = { module = "androidx.compose.ui:ui-test-manifest" }
pdf-view= { module = "com.github.mhiew:android-pdf-viewer", version.ref = "pdfView" }

compose-material = { group = "androidx.compose.material", name = "material" }
compose-foundation = { group = "androidx.compose.foundation", name = "foundation" }
compose-runtime-livedata = { group = "androidx.compose.runtime", name = "runtime-livedata" }
navigation-compose = { group = "androidx.navigation", name = "navigation-compose", version.ref = "navigation-compose" }

#ktlint
ktlint = { group = "com.pinterest", name = "ktlint", version.ref = "ktlint" }

# AndroidX
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "core-ktx" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
androidx-appcompat-resources = { group = "androidx.appcompat", name = "appcompat-resources", version.ref = "appcompat" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
androidx-activity-compose = { module = "androidx.activity:activity-compose", version.ref = "activityCompose" }
androidx-lifecycle-viewmodel-compose = { module = "androidx.lifecycle:lifecycle-viewmodel-compose", version.ref = "lifecycleViewmodelCompose" }


# Navigation
navigation-fragment = { group = "androidx.navigation", name = "navigation-fragment-ktx", version.ref = "navigation" }
navigation-ui = { group = "androidx.navigation", name = "navigation-ui-ktx", version.ref = "navigation" }

# Lifecycle
lifecycle-viewmodel = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-ktx", version.ref = "lifecycle" }
lifecycle-livedata = { group = "androidx.lifecycle", name = "lifecycle-livedata-ktx", version.ref = "lifecycle" }

# Networking
retrofit = { group = "com.squareup.retrofit2", name = "retrofit", version.ref = "retrofit" }
converter-gson = { group = "com.squareup.retrofit2", name = "converter-gson", version.ref = "retrofit" }
okhttp = { group = "com.squareup.okhttp3", name = "okhttp", version.ref = "okhttp" }
logging-interceptor = { group = "com.squareup.okhttp3", name = "logging-interceptor", version.ref = "okhttp" }
gson = { group = "com.google.code.gson", name = "gson", version.ref = "gson" }

# Dependency Injection
koin-android = { group = "io.insert-koin", name = "koin-android", version.ref = "koin" }
koin-core = { group = "io.insert-koin", name = "koin-core", version.ref = "koin-core" }

# Hilt
hilt-android = { group = "com.google.dagger", name = "hilt-android", version.ref = "hilt" }
hilt-compiler = { group = "com.google.dagger", name = "hilt-compiler", version.ref = "hilt" }
hilt-navigation = { group = "androidx.hilt", name = "hilt-navigation-fragment", version.ref = "hilt-navigation" }

# Room
androidx-room = { group = "androidx.room", name = "room-runtime", version.ref = "room" }
androidx-room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "room" }
androidx-room-ktx = { group = "androidx.room", name = "room-ktx", version.ref = "room" }

# Coroutines
coroutines-core = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-core", version.ref = "coroutines" }
coroutines-android = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-android", version.ref = "coroutines" }
coroutines-stdlib = { group = "org.jetbrains.kotlin", name = "kotlin-stdlib", version.ref = "coroutines-stdlib" }

# Security
security-crypto = { group = "androidx.security", name = "security-crypto", version.ref = "security-crypto" }

# Testing
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-test-ext = { group = "androidx.test.ext", name = "junit", version.ref = "androidx-test-ext" }
espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espresso" }

mapstruct = { group = "org.mapstruct", name = "mapstruct", version.ref = "mapstruct" }
mapstruct-processor = { group = "org.mapstruct", name = "mapstruct-processor", version.ref = "mapstruct" }
androidx-activity = { group = "androidx.activity", name = "activity", version.ref = "activity" }
androidx-constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
androidx-webkit = { group = "androidx.webkit", name = "webkit", version.ref = "webkit" }


#lottie
lottie = { group = "com.airbnb.android", name = "lottie", version.ref = "lottie" }
androidx-legacy-support-v4 = { group = "androidx.legacy", name = "legacy-support-v4", version.ref = "legacySupportV4" }
androidx-fragment-ktx = { group = "androidx.fragment", name = "fragment-ktx", version.ref = "fragmentKtx" }

#google
google-services = { group = "com.google.gms", name = "google-services", version.ref = "google-services" }

#firebase
firebase-bom = { group = "com.google.firebase", name = "firebase-bom", version.ref = "firebase-bom" }
firebase-messaging = { group = "com.google.firebase", name = "firebase-messaging-ktx" }
firebase-crashlytics = { group = "com.google.firebase", name = "firebase-crashlytics-ktx" }



androidx-runtime-livedata = { group = "androidx.compose.runtime", name = "runtime-livedata", version.ref = "runtimeLivedata" }
glide = { group = "com.github.bumptech.glide", name = "glide", version.ref = "glide-ver" }
zxing = { group = "me.dm7.barcodescanner", name = "zxing", version.ref = "zxing-ver" }
view-pager = { group = "androidx.viewpager2", name = "viewpager2", version.ref = "viewpager-ver" }
flexbox = { group = "com.google.android.flexbox", name = "flexbox", version.ref = "flexbox-ver" }

#coil
coil-compose = { group = "io.coil-kt.coil3", name = "coil-compose", version.ref = "coil" }
coil-network = { group = "io.coil-kt.coil3", name = "coil-network-okhttp", version.ref = "coil" }
androidx-ui-unit-android = { group = "androidx.compose.ui", name = "ui-unit-android", version.ref = "uiUnitAndroid" }

#core_ekyc
camera-core = { module = "androidx.camera:camera-core", version.ref = "camerax_version" }
camera-camera2 = { module = "androidx.camera:camera-camera2", version.ref = "camerax_version" }
camera-lifecycle = { module = "androidx.camera:camera-lifecycle", version.ref = "camerax_version" }
camera-video = { module = "androidx.camera:camera-video", version.ref = "camerax_version" }
camera-view = { module = "androidx.camera:camera-view", version.ref = "camerax_version" }
camera-extensions = { module = "androidx.camera:camera-extensions", version.ref = "camerax_version" }
odml-image = { module = "com.google.android.odml:image", version.ref = "odml_image" }
face-detection = { module = "com.google.mlkit:face-detection", version.ref = "face-detection" }
guava = { module = "com.google.guava:guava", version.ref = "guava" }
rxjava = { module = "io.reactivex.rxjava2:rxjava", version.ref = "rxjava" }
rxandroid = { module = "io.reactivex.rxjava2:rxandroid", version.ref = "rxandroid" }
retrofit2-rxjava2-adapter = { module = "com.jakewharton.retrofit:retrofit2-rxjava2-adapter", version.ref = "retrofit2_rxjava2_adapter" }
androidx-annotation = { module = "androidx.annotation:annotation", version.ref = "androidx_annotation" }
androidx-window = { module = "androidx.window:window", version.ref = "androidx_window" }
lifecycle-runtime-ktx = { module = "androidx.lifecycle:lifecycle-runtime-ktx", version.ref = "lifecycle_runtime_ktx" }
commons-codec = { module = "commons-codec:commons-codec", version.ref = "commons_codec" }
androidx-exifinterface = { module = "androidx.exifinterface:exifinterface", version.ref = "androidx_exifinterface" }
tensorflow-lite = { module = "org.tensorflow:tensorflow-lite", version.ref = "tensorflow_lite" }
tensorflow-lite-support = { module = "org.tensorflow:tensorflow-lite-support", version.ref = "tensorflow_lite_support" }
[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
android-library = { id = "com.android.library", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
hilt-android = { id = "com.google.dagger.hilt.android", version.ref = "hilt" }
safe-args = { id = "androidx.navigation.safeargs", version.ref = "navigation" }
ktlint = { id = "org.jlleitschuh.gradle.ktlint", version.ref = "ktlint-gradle" }


