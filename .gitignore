# --------------------------
# Android/Gradle
# --------------------------

# Th<PERSON> mụ<PERSON> build của Gradle
build/
.gradle/
**/build/
**/captures/
**/generated/

# Tệp lock củ<PERSON> Gradle
gradle-app.setting
gradle.xml
gradle-app.iml
# Nếu bạn dùng Gradle wrapper, KHÔNG ignore các file sau (cần đ<PERSON>y lên repo):
# gradlew
# gradlew.bat
# gradle-wrapper.jar
# gradle-wrapper.properties

# --------------------------
# Android Studio / IntelliJ
# --------------------------

# Th<PERSON> mục cấu hình IDEA
.idea
*.iws
*.iml
*.ipr

# Tệp cấu hình cục bộ của Android Studio (chứa đường dẫn SDK, v.v.)
local.properties

# Navigation Editor protobufs
.navigation/


# --------------------------
# Khác
# --------------------------

# Tệp APK, AAB (Android App Bundle)
*.apk
*.ap_
*.aab

# Tệp IPA (trường hợp cross-platform, ví dụ Flutter)
*.ipa

# Tệp Dalvik VM
*.dex

# Java class file
*.class

# Log, debug info
*.log
*.logcat

# Các file tạm, cache
*.tmp
*.temp
*~
*.swp
*.swo
*.orig
*.rej
*#

# File hệ thống trên macOS
.DS_Store

# File hệ thống trên Windows
Thumbs.db
Desktop.ini

# Nếu có dùng Python/Script
*.pyc
__pycache__/

# Nếu có dùng ccache (tối ưu build C/C++)
/ccache/

# Fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/test_output
fastlane/screenshots

# Secrets
.env
.env.local
firebase-service-account.json
*-service-account.json
*.keystore
*.jks
*.p12
*.pem
*.key
*.pfx
*.crt
*.der

# Build outputs
notes.txt