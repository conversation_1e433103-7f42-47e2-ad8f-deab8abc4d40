<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="32dp"
    android:height="32dp"
    android:viewportWidth="32"
    android:viewportHeight="32">
  <path
      android:pathData="M16,0.465C24.58,0.465 31.535,7.42 31.535,16C31.535,24.58 24.58,31.535 16,31.535C7.42,31.535 0.465,24.58 0.465,16C0.465,7.42 7.42,0.465 16,0.465Z"
      android:fillAlpha="0.5">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="0"
          android:startY="0"
          android:endX="32"
          android:endY="32"
          android:type="linear">
        <item android:offset="0" android:color="#FF72CEFF"/>
        <item android:offset="1" android:color="#FFF5FCFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M16,0.465C24.58,0.465 31.535,7.42 31.535,16C31.535,24.58 24.58,31.535 16,31.535C7.42,31.535 0.465,24.58 0.465,16C0.465,7.42 7.42,0.465 16,0.465Z"
      android:strokeWidth="0.929412"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="0"
          android:startY="0"
          android:endX="32"
          android:endY="32"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#FF8FD9FF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M17.336,10V12.667C17.336,12.844 17.406,13.013 17.531,13.138C17.656,13.263 17.826,13.333 18.003,13.333H20.669M17.336,10H12.669C12.316,10 11.976,10.141 11.726,10.391C11.476,10.641 11.336,10.98 11.336,11.333V20.667C11.336,21.02 11.476,21.359 11.726,21.61C11.976,21.86 12.316,22 12.669,22H19.336C19.69,22 20.029,21.86 20.279,21.61C20.529,21.359 20.669,21.02 20.669,20.667V13.333M17.336,10L20.669,13.333M16.003,19.333V15.333M16.003,19.333L14.336,17.667M16.003,19.333L17.669,17.667"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#0F4C7A"
      android:strokeLineCap="round"/>
</vector>
