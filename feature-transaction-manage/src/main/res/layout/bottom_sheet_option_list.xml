<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/cl_header"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp40"
        android:background="@drawable/bg_shape_drop_down"
        android:backgroundTint="@color/white"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp16"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp8"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/m_ic_filter" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/text_blue_02"
            android:textSize="@dimen/sp14"
            tools:text="Chọn tài khoản" />

    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/dp16">

        <RadioGroup
            android:id="@+id/tabAccountBank"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginVertical="@dimen/dp16"
            android:orientation="horizontal"
            android:visibility="gone"
            tools:visibility="visible">

            <androidx.appcompat.widget.AppCompatRadioButton
                android:id="@+id/rbInBank"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_selector_radio"
                android:button="@null"
                android:checked="true"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/dp8"
                android:paddingVertical="@dimen/dp10"
                tools:text="Trong VietinBank (1000)"
                android:textColor="@color/color_text_selector_radio"
                android:textSize="@dimen/sp10" />

            <androidx.appcompat.widget.AppCompatRadioButton
                android:id="@+id/rbOutBank"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp8"
                android:background="@drawable/bg_selector_radio"
                android:button="@null"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/dp8"
                android:paddingVertical="@dimen/dp10"
                tools:text="Liên ngân hàng qua tài khoản (1000)"
                android:textColor="@color/color_text_selector_radio"
                android:textSize="@dimen/sp10" />

        </RadioGroup>

        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/llSearch"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/m_bg_search"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/dp5"
            android:paddingVertical="@dimen/dp6"
            android:visibility="gone"
            tools:visibility="visible">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                app:srcCompat="@drawable/m_ic_search" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/edtSearch"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/dp8"
                android:layout_weight="1"
                android:background="@null"
                android:gravity="center_vertical"
                android:hint="Tìm kiếm"
                android:imeOptions="actionDone"
                android:importantForAutofill="no"
                android:maxLines="1"
                android:textColor="@color/text_blue_02"
                android:textColorHint="@color/text_blue_07"
                android:textSize="@dimen/sp14" />

            <!--            <androidx.appcompat.widget.AppCompatImageView-->
            <!--                android:id="@+id/vtb_iv_clear"-->
            <!--                android:layout_width="wrap_content"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                app:srcCompat="@drawable/vtb_ic_clear"-->
            <!--                android:visibility="gone"-->
            <!--                app:layout_constraintBottom_toBottomOf="@id/vtb_iv_search"-->
            <!--                app:layout_constraintEnd_toEndOf="parent"-->
            <!--                app:layout_constraintTop_toTopOf="@id/vtb_iv_search"-->
            <!--                tools:visibility="visible" />-->
        </androidx.appcompat.widget.LinearLayoutCompat>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rcv_account"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp4"
            android:orientation="vertical"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="5" />

    </androidx.appcompat.widget.LinearLayoutCompat>

</androidx.appcompat.widget.LinearLayoutCompat>