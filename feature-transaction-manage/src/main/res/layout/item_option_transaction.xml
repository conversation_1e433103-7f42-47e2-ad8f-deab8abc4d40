<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/dp4"
    android:orientation="vertical">

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp12"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivLogo"
            android:layout_width="@dimen/dp32"
            android:layout_height="@dimen/dp32"
            android:layout_gravity="center"
            android:layout_marginEnd="@dimen/dp16"
            android:scaleType="fitXY"
            android:visibility="gone"
            tools:srcCompat="@drawable/m_ic_radio_check"
            tools:visibility="visible" />

        <com.vietinbank.core_ui.base.views.BaseTextView
            android:id="@+id/tvValue"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_toStartOf="@id/ivStatus"
            android:layout_weight="1"
            android:textColor="@color/text_blue_07"
            android:textSize="@dimen/sp14"
            app:fontCus="medium"
            tools:text="****************" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp16"
            app:srcCompat="@drawable/m_ic_radio_uncheck" />

    </androidx.appcompat.widget.LinearLayoutCompat>


    <View
        android:id="@+id/vLine"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp1"
        android:layout_marginTop="@dimen/dp12"
        android:background="@color/text_blue_09" />

</androidx.appcompat.widget.LinearLayoutCompat>