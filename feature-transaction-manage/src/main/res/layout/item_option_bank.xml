<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/dp4"
    android:orientation="vertical">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivBank"
        android:layout_width="@dimen/dp48"
        android:layout_height="@dimen/dp48"
        android:layout_marginTop="@dimen/dp14" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/dp8"
        android:layout_toEndOf="@id/ivBank">

        <com.vietinbank.core_ui.base.views.BaseTextView
            android:id="@+id/tvName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxWidth="@dimen/dp150"
            android:maxLines="1"
            android:textColor="@color/text_blue_01"
            android:textSize="@dimen/sp16"
            app:fontCus="medium"
            app:layout_constraintEnd_toStartOf="@id/tvDescription"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="123456789123456789123456789123456789123456789123456789" />

        <com.vietinbank.core_ui.base.views.BaseTextView
            android:id="@+id/tvDescription"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp4"
            android:background="@drawable/boder_grey_radius_12"
            android:paddingHorizontal="@dimen/dp8"
            android:paddingVertical="@dimen/dp3"
            android:textColor="@color/text_blue_01"
            android:textSize="@dimen/sp10"
            app:fontCus="regular_bold"
            app:layout_constraintBottom_toBottomOf="@id/tvName"
            app:layout_constraintStart_toEndOf="@id/tvName"
            app:layout_constraintTop_toTopOf="@id/tvName"
            tools:text="Cùng hệ thống" />

        <com.vietinbank.core_ui.base.views.BaseTextView
            android:id="@+id/tvFullName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/text_gray_description"
            android:textSize="@dimen/sp14"
            app:fontCus="medium"
            app:layout_constraintStart_toStartOf="@id/tvName"
            app:layout_constraintTop_toBottomOf="@id/tvName"
            tools:text="1515123518535255151512351853525515151235185352551515123518535255151512351853525515151235185352551515123518535255" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/vLine"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp1"
        android:layout_below="@id/ivBank"
        android:layout_marginTop="@dimen/dp14"
        android:background="@color/color_grey_faint" />

</RelativeLayout>