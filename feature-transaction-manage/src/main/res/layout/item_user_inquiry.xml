<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:background="@color/white"
    android:layout_margin="10dp"
    android:layout_height="wrap_content"
    android:padding="@dimen/dp10">

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/tvUserName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/dp10"
        android:textColor="@color/text_blue_02"
        android:textSize="@dimen/sp16"
        app:fontCus="semi_bold"
        app:layout_constraintEnd_toStartOf="@+id/tvApprovalLevel"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="minhpt6" />

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/tvApprovalLevel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#AC7103"
        android:textSize="@dimen/sp13"
        app:fontCus="semi_bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Phê duyệt cấp 1" />

    <View
        android:id="@+id/view"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="10dp"
        android:background="@color/color_hint"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvUserName" />

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/tvTransLimit"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="Hạn mức còn lại/GD"
        android:textColor="@color/text_gray_08"
        android:textSize="@dimen/sp14"
        app:fontCus="medium"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/view"
        app:layout_constraintWidth_percent="0.5" />

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/contentTransLimit"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="end"
        android:textColor="@color/text_gray_09"
        android:textSize="@dimen/sp14"
        app:fontCus="medium"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvTransLimit"
        app:layout_constraintTop_toBottomOf="@+id/view"
        tools:text="60,000,000" />


    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/tvDailyLimit"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp5"
        android:text="Hạn mức còn lại/ngày"
        android:textColor="@color/text_gray_08"
        android:textSize="@dimen/sp14"
        app:fontCus="medium"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTransLimit"
        app:layout_constraintWidth_percent="0.5" />

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/contentDailyLimit"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp5"
        android:gravity="end"
        android:textColor="@color/text_gray_09"
        android:textSize="@dimen/sp14"
        app:fontCus="medium"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvDailyLimit"
        app:layout_constraintTop_toBottomOf="@+id/tvTransLimit"
        tools:text="60,000,000" />

</androidx.constraintlayout.widget.ConstraintLayout>