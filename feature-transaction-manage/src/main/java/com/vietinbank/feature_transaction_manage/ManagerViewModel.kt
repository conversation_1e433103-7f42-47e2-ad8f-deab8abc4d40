package com.vietinbank.feature_transaction_manage

import android.text.TextUtils
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.snapshotFlow
import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.dd_MM_yyyy_1
import com.vietinbank.core_common.extensions.getAmountServer
import com.vietinbank.core_common.extensions.getDayAgo
import com.vietinbank.core_common.extensions.toTimeInMillis
import com.vietinbank.core_common.extensions.todayAsString
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_domain.models.checker.CountPendingParams
import com.vietinbank.core_domain.models.checker.SubTranTypeListDomain
import com.vietinbank.core_domain.models.maker.ContactDomains
import com.vietinbank.core_domain.models.maker.ContactListParams
import com.vietinbank.core_domain.models.maker.DataBankDomain
import com.vietinbank.core_domain.models.maker.NapasBankListParams
import com.vietinbank.core_domain.models.maker.NapasTransferParams
import com.vietinbank.core_domain.models.manage.DECREASES
import com.vietinbank.core_domain.models.manage.FIELD_AMOUNT
import com.vietinbank.core_domain.models.manage.FIELD_TIME
import com.vietinbank.core_domain.models.manage.FillDataDomain
import com.vietinbank.core_domain.models.manage.FilterDomain
import com.vietinbank.core_domain.models.manage.FilterReportParams
import com.vietinbank.core_domain.models.manage.IManageData
import com.vietinbank.core_domain.models.manage.INCREASES
import com.vietinbank.core_domain.models.manage.ListReportParams
import com.vietinbank.core_domain.models.manage.ManagerDomain
import com.vietinbank.core_domain.models.manage.TAccountsDomain
import com.vietinbank.core_domain.models.manage.TGroupTypesDomain
import com.vietinbank.core_domain.models.manage.TSortDomain
import com.vietinbank.core_domain.models.manage.TStatusDomain
import com.vietinbank.core_domain.models.manage.TransDetailDomain
import com.vietinbank.core_domain.models.manage.TransRsUIModel
import com.vietinbank.core_domain.models.manage.toUIModel
import com.vietinbank.core_domain.repository.cache.ITransferCacheManager
import com.vietinbank.core_domain.usecase.home.HomeUseCase
import com.vietinbank.core_domain.usecase.transaction_manage.TransactionManageUseCase
import com.vietinbank.core_domain.usecase.transfer.TransferUseCase
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.ALL_GROUP
import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.FILL_BANK
import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.FILL_CONTACT
import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.FILL_FROM_ACCOUNT
import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.FILL_SORT_OPTION
import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.FILL_STATUS
import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.FILL_TRANSACTION
import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.INPUT_ACCOUNT_NAME
import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.INPUT_ACCOUNT_NO
import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.INPUT_END_AMOUNT
import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.INPUT_START_AMOUNT
import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.SELECTOR_DATE_TYPE
import com.vietinbank.feature_transaction_manage.fragment.render.IRenderManager
import com.vietinbank.feature_transaction_manage.fragment.render.RenderManagerFactory
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ManagerViewModel
@Inject constructor(
    private val manageUseCase: TransactionManageUseCase,
    private val homeUseCase: HomeUseCase,
    private val transferUseCase: TransferUseCase,
    private val transferCacheManager: ITransferCacheManager,
    val renderManager: RenderManagerFactory,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
) : BaseViewModel() {

    companion object {
        // ngay tim kiem
        const val DATE_INIT = "0"
        const val DATE_APPROVED = "1"

        // action for one transaction
        const val CLICK_DETAIL = "CLICK_DETAIL"
        const val CLICK_COPY = "CLICK_COPY"
        const val CLICK_ROLE_APPROVE = "CLICK_ROLE_APPROVE"
        const val CLICK_DELETE = "CLICK_DELETE"
        const val CLICK_TRACE = "CLICK_TRACE"
        const val CLICK_UNC = "CLICK_UNC"
        const val CLICK_UNC_CKS = "CLICK_UNC_CKS"
    }

    private val _filterModel = MutableStateFlow<ManagerDomain>(
        ManagerDomain(isChecker = userProf.isChecker()),
    )
    val filterModel = _filterModel.asStateFlow()

    // tab focus
    fun onTabChange(tab: Int) {
        _filterModel.value = _filterModel.value.copy(tabManager = tab)
        // gọi api khi click vào tab
        if (tab == 0 && !_filterModel.value.isCounterFinal) {
            countPending()
        }
        if (tab != 0) {
            getListFilterTransaction()
        }
    }

    // yeu cau cua toi
    fun countPending() {
        if (_filterModel.value.isCounterFinal) return // chi goi 1 lan thui
        launchJob {
            val res = homeUseCase.countPending(
                CountPendingParams(
                    cifNo = userProf.getCifNo() ?: "",
                    userName = userProf.getUserName() ?: "",
                    role = userProf.getRoleId() ?: "",
                ),
            )
            handleResource(res) { data ->
                // Cập nhật pending transactions trong state
                _filterModel.value = _filterModel.value.copy(
                    countList = data.countTransactionList ?: emptyList(),
                    isCounterFinal = true,
                )
            }
        }
    }

    private val _renderer = MutableStateFlow<IRenderManager?>(null)
    val renderer = _renderer.asStateFlow()
    fun renderUI(tranType: String? = null): IRenderManager? {
        return renderManager.render(tranType)
    }

    // xử lý load more
    val lazyDetailState = LazyListState()
    fun observeListScroll() {
        viewModelScope.launch {
            snapshotFlow {
                val layoutInfo = lazyDetailState.layoutInfo
                val totalItems = layoutInfo.totalItemsCount
                val lastVisible = layoutInfo.visibleItemsInfo.lastOrNull()?.index ?: 0
                lastVisible >= totalItems - 3 && _filterModel.value.canLoadMore && !_filterModel.value.isLoadingMore && _filterModel.value.tabManager == 1
            }.distinctUntilChanged().collect { shouldLoad ->
                if (shouldLoad) {
                    getListFilterTransaction()
                }
            }
        }
    }

    fun getListWithNewFilter() {
        _filterModel.value = _filterModel.value.copy(
            isShowFilter = false,
            tabManager = 1,
            currentPage = "0",
            canLoadMore = false,
            isLoadingMore = false,
            reportList = null,
            filterOrigin = _filterModel.value.filterChange,
        )
        getListFilterTransaction()
    }

    fun getListFilterTransaction() {
        if (_filterModel.value.isLoadingMore) return
        _filterModel.value = _filterModel.value.copy(isLoadingMore = true)
        launchJob(showLoading = ("0" == _filterModel.value.currentPage)) {
            try {
                val params = _filterModel.value.filterOrigin?.let {
                    ListReportParams(
                        status = it.status?.statusCode ?: "",
                        trantype = it.group?.groupType ?: "",
                        dateType = it.dateType ?: "",
                        fromdate = it.startDate ?: "",
                        todate = it.endDate ?: "",
                        toAccountNo = it.toAccount?.account ?: "",
                        creditname = it.toAccount?.payeename ?: "",
                        creditacc = it.fromAccount?.accountNo ?: "",
                        receiveBankName = it.toBank?.ebankCode ?: "",
                        minAmt = it.startAmount?.getAmountServer() ?: "",
                        maxAmt = it.endAmount?.getAmountServer() ?: "",
                        orderBy = it.sortField?.orderBy ?: "",
                        orderDirect = it.sortField?.orderDirect ?: "",
                        username = userProf.getUserName() ?: "",
                        fromPage = _filterModel.value.currentPage,
                        toPage = (
                            _filterModel.value.currentPage.toIntOrNull()?.plus(1)
                                ?: 1
                            ).toString(),
                    )
                } ?: ListReportParams(
                    status = "000",
                    trantype = "ALL",
                    dateType = "1",
                    fromdate = getDayAgo(dayAgo = Tags.DAY_7 - 1),
                    todate = todayAsString(),
                    username = userProf.getUserName() ?: "",
                    fromPage = _filterModel.value.currentPage,
                    toPage = (
                        _filterModel.value.currentPage.toIntOrNull()?.plus(1)
                            ?: 1
                        ).toString(),
                )
                val res = manageUseCase.getListFilterTransaction(params)
                handleResource(res) { data ->
                    val reportListFinal = _filterModel.value.reportList ?: mutableListOf()
                    // Chuyển đổi danh sách TransactionListDomain thành TransRsUIModelState
                    reportListFinal.addAll(
                        (data.btxTranItemList.orEmpty() + data.transListRs.orEmpty()).map { it ->
                            it.toUIModel()
                        },
                    )
                    _filterModel.value = _filterModel.value.copy(
                        reportList = reportListFinal,
                        isLoadingMore = false,
                        currentPage = (
                            _filterModel.value.currentPage.toIntOrNull()?.plus(1)
                                ?: 1
                            ).toString(),
                        canLoadMore = !data.transListRs.isNullOrEmpty() || !data.btxTranItemList.isNullOrEmpty(),
                    )
                }
            } catch (_: Exception) {
            } finally {
                _filterModel.value = _filterModel.value.copy(isLoadingMore = false)
            }
        }
    }

    fun convertToNapasTransfer(domain: Any): NapasTransferParams? {
        return when (domain) {
            is TransDetailDomain -> {
                NapasTransferParams().apply {
                    receiveBank = domain.receiveBank
                    receiveAcct = domain.toAccountNo
                    amount = domain.amount
                    currency = domain.currency
                    remark = domain.remark
                    tranType = domain.tranType
                }
            }

            is TransRsUIModel -> {
                NapasTransferParams().apply {
                    receiveBank = domain.receiveBank
                    receiveAcct = domain.toAccountNo
                    amount = domain.amount
                    currency = domain.currency
                    remark = domain.remark
                    tranType = domain.tranType
                }
            }

            else -> null
        }
    }

    // danh sach filter
    // filter option sort
    private val lstSortOption = listOf(
        TSortDomain(
            resourceProvider.getString(R.string.manager_sort_time_decreases),
            FIELD_TIME,
            DECREASES,
        ),
        TSortDomain(
            resourceProvider.getString(R.string.manager_sort_time_increases),
            FIELD_TIME,
            INCREASES,
        ),
        TSortDomain(
            resourceProvider.getString(R.string.manager_sort_amount_increases),
            FIELD_AMOUNT,
            INCREASES,
        ),
        TSortDomain(
            resourceProvider.getString(R.string.manager_sort_amount_decreases),
            FIELD_AMOUNT,
            DECREASES,
        ),
    )
    private var lstGroupsStatus: MutableList<TStatusDomain> = mutableListOf()
    private var lstGroupTypes: MutableList<TGroupTypesDomain> = mutableListOf()
    private var lstFromAccount: MutableList<TAccountsDomain> = mutableListOf()
    private var lstToAccount: MutableList<TAccountsDomain> = mutableListOf()

    // danh sach ngan hang
    private var lstBanksCache: List<DataBankDomain>? = null
    private var lstContactsCache: List<ContactDomains>? = null

    fun onValidateApplyFilter(): String? {
        return _filterModel.value.filterChange?.let {
            var fromDateTime = it.startDate.toTimeInMillis(dd_MM_yyyy_1)
            var toDateTime = it.endDate.toTimeInMillis(dd_MM_yyyy_1)
            val dateRange = toDateTime.minus(fromDateTime)
            val amountValidate = (
                it.endAmount?.getAmountServer()?.toLongOrNull()
                    ?: 0
                ).minus(it.startAmount?.getAmountServer()?.toLongOrNull() ?: 0) >= 0
            if (it.group == null) {
                resourceProvider.getString(com.vietinbank.core_ui.R.string.manager_filter_group_validate)
            } else if (it.status == null) {
                resourceProvider.getString(com.vietinbank.core_ui.R.string.manager_filter_status_validate)
            } else if (toDateTime == 0L || fromDateTime == 0L) {
                resourceProvider.getString(com.vietinbank.core_ui.R.string.manager_filter_date_empty_validate)
            } else if (it.group?.groupType == ALL_GROUP && dateRange > Tags.SS_30_DAY) {
                resourceProvider.getString(com.vietinbank.core_ui.R.string.manager_filter_date_over_30_validate)
            } else if (it.group?.groupType != ALL_GROUP && dateRange > Tags.SS_90_DAY) {
                resourceProvider.getString(com.vietinbank.core_ui.R.string.manager_filter_date_over_90_validate)
            } else if (!amountValidate) {
                resourceProvider.getString(com.vietinbank.core_ui.R.string.manager_filter_amount_validate)
            } else {
                null
            }
        }
    }

    // show filter sheet
    fun onShowFilterSheet(isShow: Boolean) {
        if (_filterModel.value.isFilterFinal) {
            _filterModel.value = _filterModel.value.copy(isShowFilter = isShow)
        } else {
            // call get filter
            getPreFilterTransaction()
        }
    }

    // reset filter sheet to origin
    fun onResetFilter() {
        _filterModel.value = _filterModel.value.copy(
            filterChange = _filterModel.value.filterOrigin?.copy(),
        )
    }

    // show date picker
    fun onShowDatePicker(isShow: Boolean) {
        _filterModel.value = _filterModel.value.copy(isShowPicker = isShow)
    }

    // show optional sheet
    fun onChangeSheet(fillType: String? = null) {
        _filterModel.value = when (fillType) {
            FILL_TRANSACTION -> {
                _filterModel.value.copy(
                    fillSheet = FillDataDomain(
                        FILL_TRANSACTION,
                        lstGroupTypes,
                        _filterModel.value.filterChange?.group,
                        false, // lstGroupTypes.size > Tags.SIZE_OPTION_SEARCH,
                    ),
                )
            }

            FILL_STATUS -> {
                _filterModel.value.copy(
                    fillSheet = FillDataDomain(
                        FILL_STATUS,
                        lstGroupsStatus,
                        _filterModel.value.filterChange?.status,
                        false, // lstGroupsStatus.size > Tags.SIZE_OPTION_SEARCH,
                    ),
                )
            }

            FILL_FROM_ACCOUNT -> {
                _filterModel.value.copy(
                    fillSheet = FillDataDomain(
                        FILL_FROM_ACCOUNT,
                        lstFromAccount,
                        _filterModel.value.filterChange?.fromAccount,
                        lstFromAccount.size >= Tags.SIZE_OPTION_SEARCH,
                    ),
                )
            }

            FILL_BANK -> {
                if (null == lstBanksCache) {
                    getNapasBankList()
                    return
                }
                _filterModel.value.copy(
                    fillSheet = FillDataDomain(
                        FILL_BANK,
                        lstBanksCache,
                        _filterModel.value.filterChange?.toBank,
                        (lstBanksCache?.size ?: 0) >= Tags.SIZE_OPTION_SEARCH,
                    ),
                )
            }

            FILL_CONTACT -> {
                if (null == lstContactsCache) {
                    getContactList()
                    return
                }
                _filterModel.value.copy(
                    fillSheet = FillDataDomain(
                        FILL_CONTACT,
                        lstContactsCache,
                        _filterModel.value.filterChange?.toAccount,
                        (lstContactsCache?.size ?: 0) >= Tags.SIZE_OPTION_SEARCH,
                    ),
                )
            }

            FILL_SORT_OPTION -> {
                _filterModel.value.copy(
                    fillSheet = FillDataDomain(
                        FILL_SORT_OPTION,
                        lstSortOption,
                        _filterModel.value.filterChange?.sortField,
                    ),
                )
            }

            else -> {
                _filterModel.value.copy(fillSheet = null)
            }
        }
    }

    // lay danh ba thu huong
    private fun getContactList() {
        // lay cache danh ba thu huong
        if (true == transferCacheManager.getContactList()?.isNotEmpty()) {
            mapContactAccount(transferCacheManager.getContactList())
            return
        }
        launchJob(showLoading = true) {
            val params = ContactListParams(
                username = userProf.getUserName().toString(),
                serviceId = "",
            )
            val res = transferUseCase.contactList(params)
            handleResource(res) { data ->
                transferCacheManager.saveContactist(data.contacts)
                mapContactAccount(data.contacts)
            }
        }
    }

    private fun mapContactAccount(lstOrigin: List<ContactDomains>?) {
        lstContactsCache = lstOrigin?.filter {
            when (it.trantype) {
                Tags.TransferType.TYPE_IN -> true
                Tags.TransferType.TYPE_NAPAS_ACCOUNT, Tags.TransferType.TYPE_OUT -> !it.bank.isNullOrEmpty()
                else -> false
            }
        } ?: emptyList()
        onChangeSheet(FILL_CONTACT)
    }

    // lay danh sach ngan hang
    private fun getNapasBankList() {
        if (true == transferCacheManager.getBankList()?.isNotEmpty()) {
            mapBankSelector(transferCacheManager.getBankList() ?: listOf())
            return
        }
        launchJob {
            val res = transferUseCase.getNapasBankList(
                NapasBankListParams(
                    username = userProf.getUserName() ?: "",
                    cifno = userProf.getCifNo() ?: "",
                ),
            )
            handleResource(res) { data ->
                transferCacheManager.saveBankList(data.dataBanks)
                mapBankSelector(data.dataBanks)
            }
        }
    }

    private fun mapBankSelector(lstOrigin: List<DataBankDomain>) {
        lstBanksCache = lstOrigin
        // truong hop chuyen tien noi bo
        if (Tags.TransferType.TYPE_IN == _filterModel.value.filterChange?.toAccount?.trantype) {
            lstBanksCache?.firstOrNull { itemBank ->
                Tags.TransferType.TYPE_IN.equals(itemBank.type, true)
            }?.let { bank ->
                onChangeFillModel(bank)
            }
        } else if (!TextUtils.isEmpty(_filterModel.value.filterChange?.toAccount?.bank)) {
            lstBanksCache?.firstOrNull { itemBank ->
                itemBank.ebankCode.equals(_filterModel.value.filterChange?.toAccount?.bank, true)
            }?.let { bank ->
                onChangeFillModel(bank)
            }
        } else {
            onChangeSheet(FILL_BANK)
        }
    }

    // chon item trong selector sheet
    fun onChangeFillModel(filterUpdate: Any?) {
        when (filterUpdate) {
            is TGroupTypesDomain -> {
                lstGroupsStatus.clear()
                lstGroupsStatus.addAll(filterUpdate.statusGroups ?: emptyList())
                _filterModel.value = _filterModel.value.copy().apply {
                    fillSheet = null
                    filterChange = _filterModel.value.filterChange?.copy(
                        group = filterUpdate,
                        status = null,
                        dateOverError = if (ALL_GROUP == filterUpdate.groupType) {
                            30
                        } else {
                            90
                        },
                    )
                }
            }

            is TStatusDomain -> {
                _filterModel.value = _filterModel.value.copy().apply {
                    fillSheet = null
                    filterChange = _filterModel.value.filterChange?.copy(status = filterUpdate)
                }
            }

            is TAccountsDomain -> {
                _filterModel.value = _filterModel.value.copy().apply {
                    fillSheet = null
                    filterChange = _filterModel.value.filterChange?.copy(fromAccount = filterUpdate)
                }
            }

            is ContactDomains -> {
                if (null == lstBanksCache) {
                    _filterModel.value = _filterModel.value.copy().apply {
                        fillSheet = null
                        filterChange =
                            _filterModel.value.filterChange?.copy(toAccount = filterUpdate)
                    }
                    getNapasBankList()
                    return
                }
                _filterModel.value = _filterModel.value.copy().apply {
                    fillSheet = null
                    filterChange = _filterModel.value.filterChange?.copy(
                        toAccount = filterUpdate,
                        toBank = lstBanksCache?.firstOrNull { item -> item.ebankCode == filterUpdate.bank },
                    )
                }
            }

            is DataBankDomain -> {
                _filterModel.value = _filterModel.value.copy().apply {
                    fillSheet = null
                    filterChange = _filterModel.value.filterChange?.copy(toBank = filterUpdate)
                }
            }

            is TSortDomain -> {
                _filterModel.value = _filterModel.value.copy().apply {
                    fillSheet = null
                    filterChange = _filterModel.value.filterChange?.copy(sortField = filterUpdate)
                }
            }

            else -> {
                _filterModel.value = _filterModel.value.copy(fillSheet = null)
            }
        }
    }

    // thay doi ngay => selector tab
    fun onChangeDate(type: String, value: String) {
        _filterModel.value.filterChange?.copy()?.apply {
            when {
                type == SELECTOR_DATE_TYPE -> {
                    dateType = value
                }
                // SELECTOR_DATE_RANGE
                else -> {
                    dateRange = value
                    startDate = getDayAgo(dayAgo = value.toInt() - 1)
                    endDate = todayAsString()
                }
            }
        }?.let {
            _filterModel.value = _filterModel.value.copy(filterChange = it)
        }
    }

    // thay doi key in
    fun onChangeInput(inputType: String, inputValue: String) {
        _filterModel.value.filterChange?.copy()?.apply {
            when (inputType) {
                INPUT_START_AMOUNT -> startAmount = inputValue
                INPUT_END_AMOUNT -> endAmount = inputValue

                INPUT_ACCOUNT_NO -> {
                    toAccount = toAccount?.copy(id = null, account = inputValue) ?: ContactDomains(
                        id = null, account = inputValue,
                    )
                }

                INPUT_ACCOUNT_NAME -> {
                    toAccount = toAccount?.copy(id = null, payeename = inputValue)
                        ?: ContactDomains(id = null, payeename = inputValue)
                }

                else -> {}
            }
        }?.let {
            _filterModel.value = _filterModel.value.copy(filterChange = it)
        }
    }

    // chon range date
    fun onChangeRangeDate(start: String?, end: String?) {
        _filterModel.value.filterChange?.copy()?.apply {
            startDate = start
            endDate = end
            dateRange = null
        }?.let {
            _filterModel.value = _filterModel.value.copy(filterChange = it)
        }
    }

    // thay doi trang thai filter nang cao/ thu gon
    fun onChangeFilterAdvance() {
        _filterModel.value.filterChange?.copy()?.apply {
            isFilterAdvance = !isFilterAdvance
        }?.let {
            _filterModel.value = _filterModel.value.copy(filterChange = it)
        }
        getPreFilterTransaction()
    }

    // lay dieu kien để filter
    fun getPreFilterTransaction() {
        if ((_filterModel.value.isFilterFinal && false == _filterModel.value.filterChange?.isFilterAdvance) || (_filterModel.value.isFilterAdvanceFinal && true == _filterModel.value.filterChange?.isFilterAdvance)) return

        launchJob {
            val res = manageUseCase.getPreFilterTransaction(
                FilterReportParams(
                    userName = userProf.getUserName() ?: "",
                    searchAdvance = if (true == _filterModel.value.filterChange?.isFilterAdvance) "1" else "0",
                ),
            )
            handleResource(res) { data ->
                lstGroupTypes.clear()
                lstFromAccount.clear()
                lstToAccount.clear()
                lstGroupTypes.addAll(data.groupTypes ?: listOf())
                lstFromAccount.addAll(data.fromAccounts ?: listOf())
                lstToAccount.addAll(data.toAccounts ?: listOf())
                data.groupTypes?.firstOrNull()?.let { item ->
                    lstGroupsStatus.clear()
                    lstGroupsStatus.addAll(item.statusGroups ?: listOf())
                    _filterModel.value = _filterModel.value.copy().apply {
                        isShowFilter = true
                        isFilterFinal = true
                        isFilterAdvanceFinal =
                            _filterModel.value.isFilterAdvance || true == _filterModel.value.filterChange?.isFilterAdvance
                        if (null == filterOrigin) {
                            val filterDomain = FilterDomain(
                                dateType = if (userProf.isChecker()) {
                                    FilterManagerViewModel.Companion.DATE_APPROVED
                                } else {
                                    FilterManagerViewModel.Companion.DATE_INIT
                                },
                                dateRange = Tags.DAY_7.toString(),
                                startDate = getDayAgo(dayAgo = Tags.DAY_7 - 1),
                                endDate = todayAsString(),
                                group = item,
                                status = item.statusGroups?.firstOrNull(),
                                sortField = lstSortOption.firstOrNull(),
                                dateOverError = if (ALL_GROUP == item.groupType) {
                                    30
                                } else {
                                    90
                                },
                            )
                            filterChange = filterDomain
                            filterOrigin = filterDomain
                        }
                    }
                }
            }
        }
    }
}

interface IManage
sealed class ManagerAction : IManage {
    data object OnBackPress : ManagerAction()
    data class OnTabChanged(val tabIndex: Int) : ManagerAction() // thay doi tab
    data class OnTransaction(val action: String, val data: TransRsUIModel) : ManagerAction()
    data class OnMultipleApproval(val group: SubTranTypeListDomain) : ManagerAction()
}

sealed class FilterAction : IManage {
    data class TapShowFilter(val isShow: Boolean) : FilterAction()
    data object TapReset : FilterAction()
    data object TapApply : FilterAction()
    data object TapChangeFilter : FilterAction()
    data class TapDatePicker(val isShow: Boolean) : FilterAction()
    data class TapFillSheet(val sheetType: String?) : FilterAction()
    data class TapOnFillData(val data: IManageData?) : FilterAction()
    data class TapChip(val chipType: String, val chipValue: String) : FilterAction()
    data class TapInput(val inputType: String, val inputValue: String) : FilterAction()
    data class TapRangeDate(val startDate: String?, val endDate: String?) : FilterAction()
}