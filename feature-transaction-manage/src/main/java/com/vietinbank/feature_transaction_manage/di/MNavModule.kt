package com.vietinbank.feature_transaction_manage.di

import androidx.fragment.app.FragmentManager
import androidx.navigation.NavController
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.FragmentComponent

/**
 * Created by vandz on 18/12/24.
 */

@Module
@InstallIn(FragmentComponent::class)
object MNavModule {
    @Provides
    fun provideMTransactionNavigator(
        navController: NavController,
        fragmentManager: FragmentManager,
    ): IMTransactionNavigator {
        return MTransactionNavigatorImpl(navController, fragmentManager)
    }
}