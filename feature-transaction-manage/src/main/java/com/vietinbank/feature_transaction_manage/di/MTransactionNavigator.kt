package com.vietinbank.feature_transaction_manage.di

import android.os.Bundle
import android.os.Parcelable
import androidx.fragment.app.FragmentManager
import androidx.navigation.NavController
import androidx.navigation.NavOptions
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.feature_transaction_manage.R
import java.io.Serializable

class MTransactionNavigatorImpl(
    private val navController: NavController,
    private val fragmentManager: FragmentManager,
) : IMTransactionNavigator {

    override fun popBackStack() {
        navController.popBackStack()
    }

    override fun setFragmentResult(requestKey: String, result: Bundle) {
        // Lấy NavHostFragment
        fragmentManager.setFragmentResult(requestKey, result)
    }

    override fun goToDetailsTransaction(vararg pair: Pair<String, Any?>) {
        navController.navigate(
            R.id.mTransactionDetailFragment,
            createBundle(*pair),
            createSlideNavOptions(),
        )
    }

    override fun goToListTransaction(vararg pair: Pair<String, Any?>) {
        navController.navigate(
            R.id.mTransactionListFragment,
            createBundle(*pair),
            createSlideNavOptions(),
        )
    }

    /**
     * Tạo NavOptions cho màn forward:
     * - enterAnim, exitAnim, popEnterAnim, popExitAnim
     */
    private fun createSlideNavOptions(popUpStartFragment: Boolean = false): NavOptions {
        val builder = NavOptions.Builder()
            .setEnterAnim(com.vietinbank.core_common.R.anim.core_slide_in_right)
            .setExitAnim(com.vietinbank.core_common.R.anim.core_slide_out_left)
            .setPopEnterAnim(com.vietinbank.core_common.R.anim.core_slide_in_left)
            .setPopExitAnim(com.vietinbank.core_common.R.anim.core_slide_out_right)
        return builder.build()
    }

    private fun createBundle(vararg pair: Pair<String, Any?>): Bundle {
        val bundle = Bundle()
        pair.forEach {
            val first = it.first
            val second = it.second
            second?.let {
                when (second) {
                    is Boolean -> bundle.putBoolean(first, second)
                    is Byte -> bundle.putByte(first, second)
                    is Short -> bundle.putShort(first, second)
                    is Int -> bundle.putInt(first, second)
                    is Long -> bundle.putLong(first, second)
                    is Float -> bundle.putFloat(first, second)
                    is Double -> bundle.putDouble(first, second)
                    is Char -> bundle.putChar(first, second)
                    is String -> bundle.putString(first, second)
                    is CharSequence -> bundle.putCharSequence(first, second)
                    is Parcelable -> bundle.putParcelable(first, second)
                    is Serializable -> bundle.putSerializable(first, second)
                    else -> bundle.putString(first, Utils.g().provideGson().toJson(second))
                }
            }
        }
        return bundle
    }
}