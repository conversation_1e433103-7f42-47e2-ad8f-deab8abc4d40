// package com.vietinbank.feature_transaction_manage.screen
//
// import androidx.compose.animation.AnimatedVisibility
// import androidx.compose.animation.expandVertically
// import androidx.compose.animation.fadeIn
// import androidx.compose.animation.fadeOut
// import androidx.compose.animation.shrinkVertically
// import androidx.compose.foundation.Image
// import androidx.compose.foundation.background
// import androidx.compose.foundation.layout.Arrangement
// import androidx.compose.foundation.layout.Column
// import androidx.compose.foundation.layout.Row
// import androidx.compose.foundation.layout.fillMaxSize
// import androidx.compose.foundation.layout.fillMaxWidth
// import androidx.compose.foundation.layout.imePadding
// import androidx.compose.foundation.layout.padding
// import androidx.compose.foundation.layout.systemBarsPadding
// import androidx.compose.foundation.lazy.LazyColumn
// import androidx.compose.foundation.lazy.LazyRow
// import androidx.compose.foundation.lazy.items
// import androidx.compose.foundation.shape.RoundedCornerShape
// import androidx.compose.foundation.text.KeyboardActions
// import androidx.compose.material3.Scaffold
// import androidx.compose.runtime.Composable
// import androidx.compose.runtime.LaunchedEffect
// import androidx.compose.runtime.remember
// import androidx.compose.ui.Alignment
// import androidx.compose.ui.Modifier
// import androidx.compose.ui.draw.clip
// import androidx.compose.ui.graphics.Color
// import androidx.compose.ui.platform.LocalFocusManager
// import androidx.compose.ui.platform.LocalSoftwareKeyboardController
// import androidx.compose.ui.res.painterResource
// import androidx.compose.ui.res.stringResource
// import androidx.compose.ui.text.input.ImeAction
// import com.vietinbank.core_common.constants.Tags
// import com.vietinbank.core_common.models.AppBarAction
// import com.vietinbank.core_domain.models.manage.FilterManagerDomain
// import com.vietinbank.core_ui.R
// import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
// import com.vietinbank.core_ui.components.FoundationAppBar
// import com.vietinbank.core_ui.components.FoundationButton
// import com.vietinbank.core_ui.components.FoundationCategorySection
// import com.vietinbank.core_ui.components.FoundationChips
// import com.vietinbank.core_ui.components.FoundationDivider
// import com.vietinbank.core_ui.components.date.FoundationDateRangePicker
// import com.vietinbank.core_ui.components.foundation.textfield.FoundationEditText
// import com.vietinbank.core_ui.components.foundation.textfield.InputType
// import com.vietinbank.core_ui.components.text.foundation.FoundationIconText
// import com.vietinbank.core_ui.components.text.foundation.FoundationRichText
// import com.vietinbank.core_ui.components.text.foundation.FoundationText
// import com.vietinbank.core_ui.components.text.foundation.appendBold
// import com.vietinbank.core_ui.components.text.foundation.appendColored
// import com.vietinbank.core_ui.models.IconConfig
// import com.vietinbank.core_ui.models.IconPosition
// import com.vietinbank.core_ui.utils.dismissKeyboardOnClickOutside
// import com.vietinbank.core_ui.utils.eFastBackground
// import com.vietinbank.core_ui.utils.safeClickable
// import com.vietinbank.feature_transaction_manage.FilterAction
// import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.FILL_BANK
// import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.FILL_CONTACT
// import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.FILL_FROM_ACCOUNT
// import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.FILL_STATUS
// import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.FILL_TRANSACTION
// import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.INPUT_ACCOUNT_NAME
// import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.INPUT_ACCOUNT_NO
// import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.INPUT_END_AMOUNT
// import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.INPUT_START_AMOUNT
// import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.SELECTOR_DATE_RANGE
// import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.SELECTOR_DATE_TYPE
// import com.vietinbank.feature_transaction_manage.ManagerViewModel.Companion.DATE_APPROVED
// import com.vietinbank.feature_transaction_manage.ManagerViewModel.Companion.DATE_INIT
// import com.vietinbank.feature_transaction_manage.bottomSheet.FillListDataBottomSheet
// import java.math.BigDecimal
// import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS
//
// @Composable
// fun FilterScreen(
//    filterManager: FilterManagerDomain?,
//    imageLoader: CoilImageLoader,
//    onAction: ((FilterAction) -> Unit),
// ) {
//    val dateItems = remember {
//        listOf(
//            R.string.manager_transaction_date_initialization to DATE_INIT,
//            R.string.manager_transaction_date_approve to DATE_APPROVED,
//        )
//    }
//
//    val dateRangeItems = remember {
//        listOf(
//            R.string.account_detail_7_days to Tags.DAY_7,
//            R.string.account_detail_15_days to Tags.DAY_15,
//            R.string.account_detail_30_days to Tags.DAY_30,
//        )
//    }
//
//    FillListDataBottomSheet(
//        visible = filterManager?.fillSheet != null,
//        sheet = filterManager?.fillSheet,
//        imageLoader = imageLoader,
//        onDismissRequest = { onAction.invoke(FilterAction.TapFillSheet(null)) },
//        onResult = { onAction.invoke(FilterAction.TapOnFillData(it)) },
//    )
//
//    if (true == filterManager?.isShowPicker) {
//        FoundationDateRangePicker(
//            startDate = filterManager.startDate,
//            endDate = filterManager.endDate,
//            onDismiss = { onAction.invoke(FilterAction.TapDatePicker(false)) },
//            onSelected = { startDate, endDate ->
//                onAction.invoke(FilterAction.TapRangeDate(startDate, endDate))
//            },
//        )
//    }
//
//    val focusManager = LocalFocusManager.current
//    val keyboardController = LocalSoftwareKeyboardController.current
//    // Hide keyboard when dialog/sheet opens
//    LaunchedEffect(filterManager) {
//        if (null != filterManager?.fillSheet || true == filterManager?.isShowPicker) {
//            keyboardController?.hide()
//            focusManager.clearFocus()
//        }
//    }
//
//    Scaffold(
//        modifier = Modifier
//            .fillMaxSize()
//            .eFastBackground()
//            .systemBarsPadding()
//            .dismissKeyboardOnClickOutside(),
//        containerColor = Color.Transparent,
//        topBar = {
//            Column(
//                modifier = Modifier
//                    .fillMaxWidth()
//                    .padding(bottom = FDS.Sizer.Gap.gap16),
//            ) {
//                FoundationAppBar(
//                    title = stringResource(R.string.common_search_hint),
//                    isLightIcon = false,
//                    onNavigationClick = { onAction(FilterAction.TapBack) },
//                    actions = listOf(
//                        AppBarAction(
//                            icon = R.drawable.ic_common_filter_reset_24,
//                            contentDescription = "reset",
//                            onClick = { onAction(FilterAction.TapReset) },
//                        ),
//                    ),
//                )
//            }
//        },
//    ) { paddingValues ->
//        filterManager?.let {
//            Column(
//                modifier = Modifier
//                    .fillMaxSize()
//                    .padding(paddingValues),
//            ) {
//                Column(
//                    modifier = Modifier
//                        .weight(1f)
//                        .imePadding(),
//                ) {
//                    LazyColumn(
//                        modifier = Modifier
//                            .fillMaxWidth()
//                            .weight(1f, false)
//                            .clip(
//                                RoundedCornerShape(
//                                    topStart = FDS.Sizer.Radius.radius32,
//                                    topEnd = FDS.Sizer.Radius.radius32,
//                                ),
//                            )
//                            .background(FDS.Colors.backgroundBgContainer)
//                            .padding(top = FDS.Sizer.Gap.gap24)
//                            .padding(horizontal = FDS.Sizer.Gap.gap24),
//                    ) {
//                        item {
//                            FoundationCategorySection(
//                                title = stringResource(R.string.account_detail_transaction_type),
//                                value = filterManager?.group?.groupName,
//                                isShowLine = true,
//                                icon = com.vietinbank.feature_transaction_manage.R.drawable.ic_manager_dropdown_24,
//                            ) { onAction(FilterAction.TapFillSheet(FILL_TRANSACTION)) }
//
//                            FoundationCategorySection(
//                                modifier = Modifier.padding(top = FDS.Sizer.Gap.gap16),
//                                title = stringResource(R.string.manager_transaction_status),
//                                value = filterManager?.status?.statusName,
//                                isShowLine = true,
//                                icon = com.vietinbank.feature_transaction_manage.R.drawable.ic_manager_dropdown_24,
//                            ) { onAction(FilterAction.TapFillSheet(FILL_STATUS)) }
//
//                            FoundationText(
//                                modifier = Modifier.padding(top = FDS.Sizer.Gap.gap16),
//                                text = stringResource(R.string.manager_filter_date_search),
//                                style = FDS.Typography.bodyB2Emphasized,
//                                color = FDS.Colors.characterSecondary,
//                            )
//
//                            LazyRow(
//                                modifier = Modifier
//                                    .fillMaxWidth()
//                                    .padding(top = FDS.Sizer.Gap.gap8),
//                                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
//                            ) {
//                                items(dateItems) { (dateId, dateType) ->
//                                    FoundationChips(
//                                        text = stringResource(dateId),
//                                        isSelector = filterManager?.dateType == dateType,
//                                    ) {
//                                        onAction(
//                                            FilterAction.TapChip(
//                                                SELECTOR_DATE_TYPE,
//                                                dateType,
//                                            ),
//                                        )
//                                    }
//                                }
//                            }
//
//                            FoundationText(
//                                modifier = Modifier.padding(top = FDS.Sizer.Gap.gap16),
//                                text = stringResource(R.string.common_select_date),
//                                style = FDS.Typography.bodyB2Emphasized,
//                                color = FDS.Colors.characterSecondary,
//                            )
//
//                            LazyRow(
//                                modifier = Modifier
//                                    .fillMaxWidth()
//                                    .padding(top = FDS.Sizer.Gap.gap8),
//                                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
//                            ) {
//                                items(dateRangeItems) { (rangeId, rangeType) ->
//                                    FoundationChips(
//                                        text = stringResource(rangeId),
//                                        isSelector = filterManager?.dateRange == rangeType.toString(),
//                                    ) {
//                                        onAction(
//                                            FilterAction.TapChip(
//                                                SELECTOR_DATE_RANGE,
//                                                rangeType.toString(),
//                                            ),
//                                        )
//                                    }
//                                }
//                            }
//
//                            Row(
//                                modifier = Modifier
//                                    .fillMaxWidth()
//                                    .padding(top = FDS.Sizer.Gap.gap16),
//                                verticalAlignment = Alignment.CenterVertically,
//                                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
//                            ) {
//                                FoundationCategorySection(
//                                    modifier = Modifier.weight(1f),
//                                    title = stringResource(R.string.account_detail_start_date),
//                                    value = filterManager?.startDate,
//                                    isShowLine = true,
//                                ) { onAction(FilterAction.TapDatePicker(true)) }
//
//                                FoundationCategorySection(
//                                    modifier = Modifier.weight(1f),
//                                    title = stringResource(R.string.account_detail_end_date),
//                                    value = filterManager?.endDate,
//                                    isShowLine = true,
//                                ) { onAction(FilterAction.TapDatePicker(true)) }
//                            }
//
//                            if (filterManager != null && filterManager.dateOverError != 0) {
//                                val colorOver = FDS.Colors.red300
//                                val dateDescription =
//                                    stringResource(R.string.manager_filter_date_not_over)
//                                val dateOver = stringResource(
//                                    R.string.manager_filter_date_over,
//                                    filterManager.dateOverError,
//                                )
//                                FoundationRichText(
//                                    modifier = Modifier
//                                        .fillMaxWidth()
//                                        .padding(top = FDS.Sizer.Gap.gap16),
//                                    style = FDS.Typography.interactionLink,
//                                ) {
//                                    appendColored(dateDescription, colorOver)
//                                    appendBold(" ")
//                                    appendBold(dateOver, colorOver)
//                                }
//                            }
//
//                            AnimatedVisibility(
//                                visible = (true == filterManager?.isFilterAdvance),
//                                enter = fadeIn() + expandVertically(),
//                                exit = fadeOut() + shrinkVertically(),
//                            ) {
//                                Column {
//                                    FoundationCategorySection(
//                                        modifier = Modifier.padding(top = FDS.Sizer.Gap.gap16),
//                                        title = stringResource(R.string.manager_filter_from_account),
//                                        value = filterManager?.fromAccount?.accountNo,
//                                        isShowLine = true,
//                                        icon = com.vietinbank.feature_transaction_manage.R.drawable.ic_manager_dropdown_24,
//                                    ) { onAction(FilterAction.TapFillSheet(FILL_FROM_ACCOUNT)) }
//
//                                    FoundationCategorySection(
//                                        modifier = Modifier.padding(top = FDS.Sizer.Gap.gap16),
//                                        title = stringResource(R.string.manager_filter_beneficiary_bank),
//                                        value = filterManager?.toBank?.bankName,
//                                        isShowLine = true,
//                                        icon = com.vietinbank.feature_transaction_manage.R.drawable.ic_manager_dropdown_24,
//                                    ) { onAction(FilterAction.TapFillSheet(FILL_BANK)) }
//
//                                    FoundationEditText(
//                                        modifier = Modifier.padding(top = FDS.Sizer.Gap.gap16),
//                                        value = filterManager?.toAccount?.account ?: "",
//                                        onValueChange = {
//                                            onAction(FilterAction.TapInput(INPUT_ACCOUNT_NO, it))
//                                        },
//                                        placeholder = stringResource(R.string.manager_filter_to_account),
//                                        showBottomBorder = true,
//                                        showCharacterCounter = false,
//                                        inputType = InputType.NUMBER,
//                                        imeAction = ImeAction.Done,
//                                        keyboardActions = KeyboardActions(
//                                            onDone = { focusManager.clearFocus() },
//                                        ),
//                                        trailingIcon = {
//                                            Image(
//                                                modifier = Modifier
//                                                    .padding(start = FDS.Sizer.Gap.gap4)
//                                                    .safeClickable {
//                                                        onAction(
//                                                            FilterAction.TapFillSheet(FILL_CONTACT),
//                                                        )
//                                                    },
//                                                painter = painterResource(com.vietinbank.feature_transaction_manage.R.drawable.ic_manager_contact_24),
//                                                contentDescription = "contact",
//                                            )
//                                        },
//                                    )
//
//                                    FoundationEditText(
//                                        modifier = Modifier.padding(top = FDS.Sizer.Gap.gap16),
//                                        value = filterManager?.toAccount?.payeename ?: "",
//                                        onValueChange = {
//                                            onAction(FilterAction.TapInput(INPUT_ACCOUNT_NAME, it))
//                                        },
//                                        placeholder = stringResource(R.string.manager_filter_beneficiary_name),
//                                        showBottomBorder = true,
//                                        showCharacterCounter = false,
//                                        imeAction = ImeAction.Done,
//                                        keyboardActions = KeyboardActions(
//                                            onDone = { focusManager.clearFocus() },
//                                        ),
//                                    )
//
//                                    Row(
//                                        modifier = Modifier
//                                            .fillMaxWidth()
//                                            .padding(top = FDS.Sizer.Gap.gap16),
//                                        horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
//                                    ) {
//                                        FoundationEditText(
//                                            modifier = Modifier.weight(1f),
//                                            value = filterManager?.startAmount ?: "",
//                                            onValueChange = {
//                                                onAction(
//                                                    FilterAction.TapInput(
//                                                        INPUT_START_AMOUNT,
//                                                        it,
//                                                    ),
//                                                )
//                                            },
//                                            inputType = InputType.AMOUNT,
//                                            placeholder = stringResource(R.string.account_filter_min_amount),
//                                            showBottomBorder = true,
//                                            showCharacterCounter = false,
//                                            maxLength = 19,
//                                            imeAction = ImeAction.Done,
//                                            keyboardActions = KeyboardActions(
//                                                onDone = { focusManager.clearFocus() },
//                                            ),
//                                        )
//
//                                        FoundationEditText(
//                                            modifier = Modifier.weight(1f),
//                                            value = filterManager?.endAmount ?: "",
//                                            onValueChange = {
//                                                onAction(
//                                                    FilterAction.TapInput(
//                                                        INPUT_END_AMOUNT,
//                                                        it,
//                                                    ),
//                                                )
//                                            },
//                                            inputType = InputType.AMOUNT,
//                                            placeholder = stringResource(R.string.account_filter_max_amount),
//                                            showBottomBorder = true,
//                                            showCharacterCounter = false,
//                                            maxLength = 19,
//                                            imeAction = ImeAction.Done,
//                                            keyboardActions = KeyboardActions(
//                                                onDone = { focusManager.clearFocus() },
//                                            ),
//                                        )
//                                    }
//
//                                    if ((
//                                            filterManager?.startAmount?.toBigDecimalOrNull()
//                                                ?: BigDecimal(
//                                                    0,
//                                                )
//                                            ) > (
//                                            filterManager?.endAmount?.toBigDecimalOrNull()
//                                                ?: BigDecimal(0)
//                                            )
//                                    ) {
//                                        FoundationText(
//                                            modifier = Modifier
//                                                .fillMaxWidth()
//                                                .padding(vertical = FDS.Sizer.Gap.gap16),
//                                            text = stringResource(R.string.account_filter_validate_amount),
//                                            style = FDS.Typography.captionCaptionL,
//                                            color = FDS.Colors.stateError,
//                                        )
//                                    }
//                                }
//                            }
//                        }
//                    }
//
//                    Column(
//                        modifier = Modifier
//                            .fillMaxWidth()
//                            .clip(
//                                RoundedCornerShape(
//                                    bottomStart = FDS.Sizer.Radius.radius32,
//                                    bottomEnd = FDS.Sizer.Radius.radius32,
//                                ),
//                            )
//                            .background(FDS.Colors.backgroundBgContainer)
//                            .padding(top = FDS.Sizer.Gap.gap16, bottom = FDS.Sizer.Gap.gap24),
//                        horizontalAlignment = Alignment.CenterHorizontally,
//                    ) {
//                        FoundationDivider()
//
//                        FoundationIconText(
//                            modifier = Modifier
//                                .padding(top = FDS.Sizer.Gap.gap20)
//                                .safeClickable { onAction(FilterAction.TapChangeFilter) },
//                            text = if (filterManager?.isFilterAdvance == false) {
//                                stringResource(R.string.manager_filter_expand)
//                            } else {
//                                stringResource(R.string.manager_filter_collapse)
//                            },
//                            style = FDS.Typography.interactionSmallButton,
//                            color = FDS.Colors.characterHighlighted,
//                            icons = mapOf(
//                                IconPosition.LEFT to IconConfig(
//                                    icon = if (filterManager?.isFilterAdvance == false) {
//                                        com.vietinbank.feature_transaction_manage.R.drawable.ic_manager_expand_24
//                                    } else {
//                                        com.vietinbank.feature_transaction_manage.R.drawable.ic_manager_collapse_24
//                                    },
//                                    size = FDS.Sizer.Icon.icon24,
//                                ),
//                            ),
//                        )
//                    }
//                }
//
//                FoundationButton(
//                    modifier = Modifier
//                        .fillMaxWidth()
//                        .padding(top = FDS.Sizer.Gap.gap16)
//                        .padding(horizontal = FDS.Sizer.Gap.gap24),
//                    text = stringResource(R.string.common_apply),
//                    onClick = { onAction.invoke(FilterAction.TapApply) },
//                )
//            }
//        }
//    }
// }