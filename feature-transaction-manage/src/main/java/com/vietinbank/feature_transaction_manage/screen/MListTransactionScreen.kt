package com.vietinbank.feature_transaction_manage.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_domain.models.manage.TransactionReportActions
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_transaction_manage.DetailManagerViewModel

@Composable
fun MListTransactionScreen(
    viewModel: DetailManagerViewModel,
    actions: TransactionReportActions?,
) {
//    val listTransaction by viewModel.listTranState.collectAsState()
//    val renderer by viewModel.renderer.collectAsState()
//    val isLoadingMore by viewModel.isLoadingMore.collectAsState()
//    Column(
//        modifier = Modifier
//            .fillMaxSize()
//            .background(AppColors.itemBackground),
//    ) {
//        // AppBar
//        val appBarActions = mutableListOf<AppBarAction>()
//        appBarActions.add(
//            AppBarAction(
//                icon = com.vietinbank.core_ui.R.drawable.ic_filter,
//                contentDescription = "SORT",
//                tint = Color.White,
//                onClick = {},
//            ),
//        )
//        // Normal AppBar
//        BaseAppBar(
//            title = "Quản lý giao dịch eFAST",
//            onBackClick = actions?.onBackClick ?: {},
//        )
//
//        listTransaction?.let { lst ->
//            if (lst.isEmpty()) {
//                Column(
//                    modifier = Modifier
//                        .fillMaxWidth()
//                        .padding(top = 50.dp, start = 32.dp, end = 32.dp),
//                    horizontalAlignment = Alignment.CenterHorizontally,
//                ) {
//                    Icon(
//                        painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_empty),
//                        contentDescription = "No transactions",
//                        tint = Color.White,
//                    )
//
//                    Spacer(modifier = Modifier.height(16.dp))
//
//                    Text(
//                        text = "Quý khách không có giao dịch trong thời gian tìm kiếm. Vui lòng kiểm tra lại.",
//                        style = getComposeFont(4, 14.sp, Color.White),
//                        textAlign = TextAlign.Center,
//                    )
//                }
//            } else {
//                LazyColumn(
//                    modifier = Modifier
//                        .fillMaxWidth()
//                        .weight(1f)
//                        .padding(horizontal = 16.dp, vertical = 8.dp),
//                ) {
//                    itemsIndexed(
//                        items = lst,
//                        key = { index, item -> "$index" },
//                    ) { index, item ->
//                        viewModel.renderUI(item.tranType)?.let { render ->
//                            BaseSwipeable(
//                                isRevealed = false,
//                                actions = {
//                                    if (viewModel.isHasUNCCKS(item.tranType, item.statusCode)) {
//                                        SwipeItem(
//                                            "Tải UNC có CKS của NHCT",
//                                            AppColors.primary4,
//                                        ) {
//                                            actions?.onClick?.invoke("UNC_CKS", item)
//                                        }
//                                    }
//                                    if (viewModel.isHasUNC(item.tranType, item.statusCode)) {
//                                        SwipeItem("Tải UNC", AppColors.primary6) {
//                                            actions?.onClick?.invoke("UNC", item)
//                                        }
//                                    }
//                                    if (viewModel.isHasCopy(item.tranType, item.statusCode)) {
//                                        SwipeItem("Sao chép", AppColors.primary8) {
//                                            actions?.onClick?.invoke("COPY", item)
//                                        }
//                                    }
//                                    if (viewModel.isHasTrace(item.tranType, item.statusCode)) {
//                                        SwipeItem(
//                                            "Tra soát",
//                                            AppColors.primary10,
//                                        ) {
//                                            actions?.onClick?.invoke("TRACE", item)
//                                        }
//                                    }
//                                    if (viewModel.isHasDelete(item.tranType, item.statusCode)) {
//                                        SwipeItem("Xóa", AppColors.primary4) {
//                                            actions?.onClick?.invoke("DELETE", item)
//                                        }
//                                    }
//                                },
//                                modifier = Modifier
//                                    .fillMaxWidth()
//                                    .padding(bottom = 8.dp)
//                                    .clip(RoundedCornerShape(2.dp))
//                                    .background(Color.White),
//                                content = {
//                                    render.RenderViewItemManager(item) { actionName ->
//                                        actions?.onClick?.invoke(actionName, item)
//                                    }
//                                },
//                            )
//
//                            if (index >= lst.size - 3) {
//                                LaunchedEffect(key1 = index) {
//                                    actions?.onLoadMore?.invoke()
//                                }
//                            }
//                        }
//                    }
//
//                    if (isLoadingMore) {
//                        item {
//                            Box(
//                                modifier = Modifier
//                                    .fillMaxWidth()
//                                    .padding(vertical = 16.dp),
//                                contentAlignment = Alignment.Center,
//                            ) {
//                                CircularProgressIndicator(
//                                    modifier = Modifier.size(24.dp),
//                                    color = AppColors.gradientButtonStart,
//                                    strokeWidth = 2.dp,
//                                )
//                            }
//                        }
//                    }
//                }
//            }
//        }
//    }
}

@Composable
fun SwipeItem(name: String? = null, color: Color? = null, onClick: (() -> Unit)? = null) {
    Box(
        modifier = Modifier
            .background(color ?: AppColors.blue08)
            .fillMaxHeight()
            .width(70.dp)
            .padding(12.dp)
            .safeClickable {
                onClick?.invoke()
            },
        contentAlignment = Alignment.Center,
    ) {
        Text(
            text = name ?: "",
            textAlign = TextAlign.Center,
            style = getComposeFont(4, 14.sp, AppColors.white),
        )
    }
}