package com.vietinbank.feature_transaction_manage.bottomSheet

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.feature_transaction_manage.databinding.ItemTransactionDownloadBinding

class TransactionAdapter(
    private val items: List<String>,
    private val onItemClick: (String) -> Unit,
) : RecyclerView.Adapter<TransactionAdapter.TransactionViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TransactionViewHolder {
        val binding = ItemTransactionDownloadBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false,
        )
        return TransactionViewHolder(binding)
    }

    override fun onBindViewHolder(holder: TransactionViewHolder, position: Int) {
        holder.bind(position + 1, items[position])
    }

    override fun getItemCount(): Int = items.size

    inner class TransactionViewHolder(
        private val binding: ItemTransactionDownloadBinding,
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(index: Int, transactionCode: String) {
            binding.tvTransaction.text = "$index - $transactionCode"

            binding.ivDownload.setThrottleClickListener {
                onItemClick(transactionCode)
            }

            // Optional: click cả dòng cũng được
            binding.root.setThrottleClickListener {
                onItemClick(transactionCode)
            }
        }
    }
}
