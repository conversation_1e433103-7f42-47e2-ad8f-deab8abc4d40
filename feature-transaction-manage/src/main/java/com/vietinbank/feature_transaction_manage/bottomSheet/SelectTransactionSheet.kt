package com.vietinbank.feature_transaction_manage.bottomSheet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.feature_transaction_manage.databinding.SheetSelectTransactionBinding

class SelectTransactionSheet(
    private val transactionList: List<String>,
    private val title: String,
    private val onTransactionSelected: (String) -> Unit,
) : BottomSheetDialogFragment() {

    private var _binding: SheetSelectTransactionBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        _binding = SheetSelectTransactionBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.rcTransactionList.layoutManager = LinearLayoutManager(requireContext())
        // Gán text động
        val message = "Vui lòng chọn giao dịch bạn cần tải file %s".format(title)
        binding.tvSubtitle.text = message
        binding.rcTransactionList.adapter = TransactionAdapter(transactionList) { selected ->
//            dismiss()
            onTransactionSelected.invoke(selected)
        }
        binding.btnClose.setThrottleClickListener {
            dismiss() //  Chỉ nút X mới được phép đóng
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
