package com.vietinbank.feature_transaction_manage

import android.content.Context
import android.content.Intent
import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_common.constants.DataSourceProperties
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.extensions.saveBase64File
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.checker.FileTransactionDomain
import com.vietinbank.core_domain.models.checker.GetDownloadBase64FileParams
import com.vietinbank.core_domain.models.checker.GetDownloadFileIDDomain
import com.vietinbank.core_domain.models.checker.GetDownloadFileIDParams
import com.vietinbank.core_domain.models.checker.SubTransactionDetailDomain
import com.vietinbank.core_domain.models.checker.SubTransactionParams
import com.vietinbank.core_domain.models.maker.NapasTransferParams
import com.vietinbank.core_domain.models.manage.DetailReportParams
import com.vietinbank.core_domain.models.manage.SubTranItemListDomain
import com.vietinbank.core_domain.models.manage.TransDetailDomain
import com.vietinbank.core_domain.models.manage.TransRsUIModel
import com.vietinbank.core_domain.usecase.checker.CheckerUserCase
import com.vietinbank.core_domain.usecase.transaction_manage.TransactionManageUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.feature_transaction_manage.fragment.render.IRenderManager
import com.vietinbank.feature_transaction_manage.fragment.render.RenderManagerFactory
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import kotlin.String

@HiltViewModel
class DetailManagerViewModel
@Inject constructor(
    private val useCase: CheckerUserCase,
    private val manageUseCase: TransactionManageUseCase,
    val dataSourceProperties: DataSourceProperties,
    val renderManager: RenderManagerFactory,
    val imageLoader: CoilImageLoader,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
) : BaseViewModel() {
    var transRsUICopyModel: TransRsUIModel? = null

    // UI State để hiển thị - sử dụng StateFlow
    private var detailTrans: TransDetailDomain? = null
    private val _detailTransState = MutableStateFlow<TransDetailDomain?>(null)
    val detailTransState: StateFlow<TransDetailDomain?> = _detailTransState.asStateFlow()

    fun getDetailTransaction() {
        val req = DetailReportParams(
            transRsUICopyModel?.mtId ?: "",
            transRsUICopyModel?.serviceType ?: "",
            transRsUICopyModel?.tranType ?: "",
            userProf.getUserName() ?: "",
            transRsUICopyModel?.mtIdList,
        )

        if (transRsUICopyModel?.tranType == "sn") {
            transRsUICopyModel?.mtId?.let {
                getSubTransactionDetail(it)
            }
        }

        launchJob {
            val res = manageUseCase.getReportDetail(req)
            handleResource(res) { data ->
                _renderer.value = renderUI(data?.tranType) // ?: data.btxTranItem?.tranType)
                detailTrans = data // .transaction ?: data.btxTranItem
                updateDetailTransState()
            }
        }
    }

    private val _renderer = MutableStateFlow<IRenderManager?>(null)
    val renderer = _renderer.asStateFlow()
    fun renderUI(tranType: String? = null): IRenderManager? {
        return renderManager.render(tranType)
    }

    // SharedFlow để thông báo khi URL sẵn sàng
    private val _fileUrlReady = MutableSharedFlow<String>()
    val fileUrlReady = _fileUrlReady.asSharedFlow()

    private val _openFileIntent = MutableSharedFlow<Intent>()
    val openFileIntent = _openFileIntent.asSharedFlow()

    private val _errorMessage = MutableSharedFlow<String>()
    val errorMessage = _errorMessage.asSharedFlow()

    private val _getDownloadFileID = MutableSharedFlow<Resource<GetDownloadFileIDDomain>?>()
    val getDownloadFileID = _getDownloadFileID.asSharedFlow()
    fun getDownloadFileID(file: FileTransactionDomain? = null) {
        launchJob {
            val res = useCase.getDownloadFileID(
                GetDownloadFileIDParams(
                    username = userProf.getUserName(),
                    fileId = getFileIDFollowTranType(
                        transRsUICopyModel?.tranType,
                        file,
                    ), // tam thoi dung mtid
                    mtID = getMtIdFollowTranType(transRsUICopyModel, file),
                    tranType = transRsUICopyModel?.tranType ?: "",
                ),
            )
            handleResource(res) { data ->
                _getDownloadFileID.emit(Resource.Success(data))

                data.downloadFileId?.let { fileId ->
                    if (fileId.isNotEmpty()) {
                        val baseUrl = dataSourceProperties.getUrl()
                        val downloadUrl = baseUrl + Constants.MB_DOWNLOAD_FILE.replace(
                            "{encryptStr}",
                            fileId,
                        )
                        printLog("download url: $downloadUrl")
                        _fileUrlReady.emit(downloadUrl)
                    }
                }
            }
        }
    }

    sealed class FileDownloadEvent {
        data class ShowSelectTransactionSheet(
            val mtIdList: List<String>,
            val type: String,
            val signType: String,
            val title: String,
        ) : FileDownloadEvent()

        data class DownloadDirect(val mtId: String, val type: String, val signType: String) :
            FileDownloadEvent()
    }

    private val _fileDownloadEvent = MutableSharedFlow<FileDownloadEvent>()
    val fileDownloadEvent: SharedFlow<FileDownloadEvent> = _fileDownloadEvent

    fun getAllMtIds(subTranItemList: List<SubTranItemListDomain>): List<String> {
        return subTranItemList.mapNotNull { it.mtId?.toString() }
    }

    fun handleDownloadAction(
        mtId: String,
        tranType: String,
        type: String,
        signType: String,
    ) {
        val title = when {
            type == Tags.DOWNLOAD_FILE_TYPE_BM && signType == Tags.DOWNLOAD_FILE_SIGN_TYPE_BM -> Tags.DOWNLOAD_FILE_BM01
            type == Tags.DOWNLOAD_FILE_TYPE_CM && signType == Tags.DOWNLOAD_FILE_SIGN_TYPE_BM -> Tags.DOWNLOAD_FILE_C102
            type == Tags.DOWNLOAD_FILE_TYPE_CM && signType == Tags.DOWNLOAD_FILE_SIGN_TYPE_CM -> Tags.DOWNLOAD_FILE_C102CKS
            else -> return //  Không hỗ trợ -> thoát luôn
        }

        viewModelScope.launch {
            val itemList =
                detailTrans?.subTranItemList?.let { getAllMtIds(it) }.orEmpty()
            if (tranType == "btx" && itemList.isNotEmpty()) {
                _fileDownloadEvent.emit(
                    FileDownloadEvent.ShowSelectTransactionSheet(
                        itemList,
                        type,
                        signType,
                        title,
                    ),
                )
            } else {
                _fileDownloadEvent.emit(
                    FileDownloadEvent.DownloadDirect(
                        mtId,
                        type,
                        signType,
                    ),
                )
            }
        }
    }

    fun getDownloadBase64File(context: Context, mtId: String?, type: String?, signType: String?) {
        launchJob {
            val res = useCase.getDownloadBase64(
                GetDownloadBase64FileParams(
                    username = userProf.getUserName(),
                    type = type,
                    mtId = mtId,
                    signType = signType,
                ),
            )
            handleResource(res) { data ->
                viewModelScope.launch {
                    if (data.file.isNullOrEmpty()) {
                        _errorMessage.emit("Không tìm thấy file đính kèm hoặc file không khả dụng")
                        return@launch
                    } else {
                        try {
                            saveBase64File(
                                context,
                                data.file.orEmpty(),
                                data.fileName.orEmpty(),
                            ).onSuccess { file ->
                                val uri = Utils.getFileUri(context, file)
                                val mimeType = Utils.getMimeType(file)

                                val intent = Intent(Intent.ACTION_VIEW).apply {
                                    setDataAndType(uri, mimeType)
                                    flags = Intent.FLAG_GRANT_READ_URI_PERMISSION
                                }

                                if (intent.resolveActivity(context.packageManager) != null) {
                                    _openFileIntent.emit(intent)
                                } else {
                                    _errorMessage.emit("Không có ứng dụng mở file phù hợp")
                                }
                            }.onFailure { e ->
                                _errorMessage.emit(
                                    e.message ?: "Lỗi không xác định",
                                )
                            }
                        } catch (e: Exception) {
                            _errorMessage.emit("Lỗi khi lưu file: ${e.message}")
                        }
                    }
                }
            }
        }
    }

    // giao dich chuyen tien tach lenh
    private var subTransList: List<SubTransactionDetailDomain>? = null
    fun getSubTransactionDetail(hostMtId: String? = null) = launchJob(showLoading = true) {
        val res =
            useCase.getSubTransactionDetail(SubTransactionParams(hostMtId, userProf.getUserName()))
        handleResource(res) { data ->
            subTransList = data.data ?: emptyList()
            updateDetailTransState()
        }
    }

    // truong hop la giao dich tach lenh -> doi 2 api detail + sub thanh cong -> update trang thai
    private fun updateDetailTransState() {
        detailTrans?.let {
            if (it.tranType == "sn" && subTransList != null) {
                it.splitTransaction = subTransList
                _detailTransState.value = it
            } else if (it.tranType != "sn") {
                _detailTransState.value = it
            }
        }
    }

    // show action for transaction
    fun isHasUNC(tranType: String? = null, statusCode: String? = null): Boolean =
        when (tranType?.uppercase() ?: "") {
            // chuyen tien don
            // maker -> all
            // checker -> success
            Tags.TYPE_GROUP_TRANSFER_NAPAS, Tags.TYPE_GROUP_TRANSFER_OUT,
            Tags.TYPE_GROUP_TRANSFER_IN,
            -> !userProf.isChecker() || Tags.STATUS_SUCCESS == statusCode

            else -> false
        }

    fun isHasBM01C102(tranType: String? = null): Boolean {
        // Định nghĩa danh sách các loại tranType cần kiểm tra
        val supportedTranTypes = listOf(
            Tags.TYPE_GROUP_INFRASTRUCTURE,
            Tags.TYPE_GROUP_FILENSNN,
            Tags.TYPE_GROUP_CUSTOMSINLAND,
        )

        // Kiểm tra nếu tranType thuộc danh sách cho phép và statusCode hợp lệ
        val normalizedTranType = tranType?.uppercase() ?: ""
        return normalizedTranType in supportedTranTypes
    }

    fun isHasC102COCKS(tranType: String? = null, statusCode: String? = null): Boolean =
        when (tranType?.uppercase() ?: "") {
            // nộp NSNN, nộp thuế theo file, nộp thuế hạ tầng
            // success
            Tags.TYPE_GROUP_INFRASTRUCTURE, Tags.TYPE_GROUP_FILENSNN,
            Tags.TYPE_GROUP_CUSTOMSINLAND,
            -> Tags.STATUS_SUCCESS == statusCode

            else -> false
        }

    fun isHasUNCCKS(tranType: String? = null, statusCode: String? = null): Boolean =
        when (tranType?.uppercase() ?: "") {
            // chuyen tien don
            // success -> all maker + checker
            Tags.TYPE_GROUP_TRANSFER_IN, Tags.TYPE_GROUP_TRANSFER_OUT,
            Tags.TYPE_GROUP_TRANSFER_NAPAS,
            -> Tags.STATUS_SUCCESS == statusCode

            else -> false
        }

    fun isHasCopy(tranType: String? = null, statusCode: String? = null): Boolean =
        !userProf.isChecker() && when (tranType?.uppercase() ?: "") {
            // chuyen tien don + lenh chi + giai ngan online + dich vu bao lanh
            // -> all giao dich
            Tags.TYPE_GROUP_TRANSFER_IN, Tags.TYPE_GROUP_TRANSFER_OUT,
            Tags.TYPE_GROUP_TRANSFER_NAPAS, Tags.TYPE_GROUP_PAYMENT,
            Tags.TYPE_DISBURSEMENT_ONLINE, Tags.TYPE_GUARANTEE,
            -> true

            else -> false
        }

    fun isHasTrace(tranType: String? = null, statusCode: String? = null): Boolean =
        !userProf.isChecker() && when (tranType?.uppercase() ?: "") {
            // chuyen tien don
            // chi luong tu dong
            // -> thanh cong
            Tags.TYPE_GROUP_TRANSFER_IN, Tags.TYPE_GROUP_TRANSFER_OUT, Tags.TYPE_GROUP_TRANSFER_NAPAS,
            Tags.TYPE_GROUP_SALARY_AUTO,
            -> Tags.STATUS_SUCCESS == statusCode
            // lenh chi
            // cl qua ngan hang + cl ngoai te
            // -> Thành công + Chờ ngân hàng xử lý + Ngân hàng đang xử lý
            Tags.TYPE_GROUP_PAYMENT,
            Tags.TYPE_GROUP_SALARY_FOREIGN, Tags.TYPE_GROUP_SALARY,
            -> Tags.STATUS_SUCCESS == statusCode || Tags.STATUS_BANK_WAITING == statusCode || Tags.STATUS_BANK_PROCESSING == statusCode
            // NSNN thế nội địa, hải quan
            // Thuế hạ tầng
            // -> Thành công + Chờ ngân hàng xử lý + Ngân hàng đang xử lý
            Tags.TYPE_GROUP_CUSTOMSINLAND,
            Tags.TYPE_GROUP_INFRASTRUCTURE,
            -> Tags.STATUS_SUCCESS == statusCode || Tags.STATUS_BANK_WAITING == statusCode || Tags.STATUS_BANK_PROCESSING == statusCode

            else -> false
        }

    fun isHasDelete(tranType: String? = null, statusCode: String? = null): Boolean =
        userProf.isChecker() && when (tranType?.uppercase() ?: "") {
            // giai ngan online + dich vu bao lanh
            // -> ktt/ctk từ chối
            Tags.TYPE_DISBURSEMENT_ONLINE, Tags.TYPE_GUARANTEE -> Tags.STATUS_USER_REJECT == statusCode
            else -> false
        }

    private fun getFileIDFollowTranType(tranType: String?, file: FileTransactionDomain?): String? {
        return when (tranType) {
            "sl", "sx" -> {
                "SL_${file?.mtId}_${file?.id}"
            }

            "slo" -> {
                file?.mtId
            }

            "do", "go", "goc", "gor" -> {
                file?.objectId
            }

            else -> file?.mtId // gor
        }
    }

    private fun getMtIdFollowTranType(
        transaction: TransRsUIModel?,
        file: FileTransactionDomain?,
    ): String? {
        return when (transaction?.tranType ?: "") {
            "do", "go", "goc", "gor" -> {
                transaction?.mtId
            }

            else -> {
                file?.mtId ?: transaction?.mtId
            }
        }
    }

    fun convertToNapasTransfer(domain: Any): NapasTransferParams? {
        return when (domain) {
            is TransDetailDomain -> {
                NapasTransferParams().apply {
                    receiveBank = domain.receiveBank
                    receiveAcct = domain.toAccountNo
                    amount = domain.amount
                    currency = domain.currency
                    remark = domain.remark
                    tranType = domain.tranType
                }
            }

            is TransRsUIModel -> {
                NapasTransferParams().apply {
                    receiveBank = domain.receiveBank
                    receiveAcct = domain.toAccountNo
                    amount = domain.amount
                    currency = domain.currency
                    remark = domain.remark
                    tranType = domain.tranType
                }
            }

            else -> null
        }
    }
}
