package com.vietinbank.feature_transaction_manage.view

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_data.models.response.DisplayModelUI
import com.vietinbank.core_domain.models.manage.TransRsUIModel
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_transaction_manage.R

@Composable
fun MTransactionItem(
    transaction: TransRsUIModel,
    isSelectionMode: Boolean = false,
    onSelectedClick: ((Boolean) -> Unit)? = null,
    onDetailClick: ((TransRsUIModel) -> Unit)? = null,
) {
    var isChecked by remember { mutableStateOf(transaction.isSelected) }
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White)
            .padding(12.dp)
            .safeClickable {
                onDetailClick?.invoke(transaction)
            },
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
        ) {
            if (isSelectionMode) {
                Image(
                    painter = painterResource(
                        id = if (isChecked) {
                            com.vietinbank.core_ui.R.drawable.ic_radio_check
                        } else {
                            com.vietinbank.core_ui.R.drawable.ic_radio_uncheck
                        },
                    ),
                    contentDescription = "Selected",
                    modifier = Modifier.safeClickable {
                        isChecked = !isChecked
                        onSelectedClick?.invoke(isChecked)
                    },
                )

                Spacer(modifier = Modifier.width(8.dp))
            }
            BaseText(
                text = transaction.tranTypeName ?: "",
                fontCus = 4,
                color = AppColors.blue02,
                textSize = 16.sp,
            )

            Spacer(modifier = Modifier.weight(1f))

            BaseText(
                modifier = Modifier.padding(start = 16.dp),
                text = Utils.g().getDotMoneyHasCcy(
                    transaction.amount ?: "",
                    transaction.currency ?: "",
                ),
                fontCus = 5,
                color = AppColors.primaryRed,
                textSize = 14.sp,
                rightDrawable = R.drawable.m_ic_more,
                rightDrawableSize = 24.dp,
            )
        }

        Spacer(
            modifier = Modifier
                .padding(top = 8.dp)
                .height(1.dp)
                .fillMaxWidth()
                .background(AppColors.blue09),
        )

        MHorizontalInfoView(
            modifier = Modifier.padding(top = 4.dp),
            displayModelUI = DisplayModelUI(
                title = transaction.corpName ?: "",
                value = transaction.toAccountNo,
            ),
        )

        MHorizontalInfoView(
            modifier = Modifier.padding(top = 16.dp),
            displayModelUI = DisplayModelUI(
                title = "Số giao dịch",
                value = transaction.mtId ?: "",
            ),
        )

        MHorizontalInfoView(
            modifier = Modifier.padding(top = 16.dp),
            displayModelUI = DisplayModelUI(
                title = "Nội dung",
                value = transaction.tranDesc ?: "",
            ),
        )

        MHorizontalInfoView(
            modifier = Modifier.padding(top = 16.dp),
            displayModelUI = DisplayModelUI(
                title = "Trạng thái",
                value = transaction.statusName ?: "",
                valueColor = AppColors.blue02,
            ),
        )
    }
}
