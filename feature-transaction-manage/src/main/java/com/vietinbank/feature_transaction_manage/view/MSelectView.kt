package com.vietinbank.feature_transaction_manage.view

import android.text.TextUtils
import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.core_ui.base.compose.getFontFamily
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_transaction_manage.R

@Preview
@Composable
fun MSelectView(
    title: String? = null,
    titleFont: Int = 2,
    titleColor: Color = AppColors.blue07,
    titleSize: TextUnit = 14.sp,
    value: String? = null,
    valueFont: Int = 2,
    valueColor: Color = AppColors.blue02,
    valueSize: TextUnit = 16.sp,
    @DrawableRes icon: Int? = R.drawable.m_ic_more,
    iconSize: Dp = 24.dp,
    isRequire: Boolean = false,
    isShowLine: Boolean = true,
    modifier: Modifier = Modifier,
    onClick: (() -> Unit)? = null,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .selectableGroup()
            .safeClickable {
                onClick?.invoke()
            },
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = buildAnnotatedString {
                        withStyle(
                            SpanStyle(
                                color = titleColor,
                                fontSize = titleSize,
                                fontFamily = getFontFamily(titleFont),
                            ),
                        ) { append(title) }
                        if (isRequire) {
                            append(" ")
                            withStyle(style = SpanStyle(color = AppColors.primaryRed)) {
                                append("*")
                            }
                        }
                    },
                )

                if (!TextUtils.isEmpty(value)) {
                    Text(
                        text = value ?: "",
                        style = getComposeFont(valueFont, valueSize, valueColor),
                        modifier = Modifier.padding(top = 4.dp),
                    )
                }
            }

            icon?.let {
                Image(
                    painter = painterResource(id = it),
                    contentDescription = null,
                    modifier = Modifier
                        .width(iconSize)
                        .height(iconSize),
                )
            }
        }

        if (isShowLine) {
            Spacer(
                modifier = Modifier
                    .height(1.dp)
                    .fillMaxWidth()
                    .background(AppColors.grey200),
            )
        }
    }
}