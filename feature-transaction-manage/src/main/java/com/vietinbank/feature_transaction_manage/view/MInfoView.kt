package com.vietinbank.feature_transaction_manage.view

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_data.models.response.DisplayModelUI
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.core_ui.theme.AppColors

@Preview
@Composable
fun MHorizontalInfoView(
    modifier: Modifier = Modifier,
    displayModelUI: DisplayModelUI? = null,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(Color.White),
        verticalAlignment = Alignment.Top,
    ) {
        Text(
            text = displayModelUI?.title ?: "",
            style = getComposeFont(
                displayModelUI?.titleFont ?: 4,
                displayModelUI?.titleSize ?: 14.sp,
                displayModelUI?.titleColor ?: AppColors.grey08,
            ),
        )

        Spacer(modifier = Modifier.weight(1f))

        Column(
            modifier = Modifier.padding(start = 16.dp),
            horizontalAlignment = Alignment.End,
        ) {
            Text(
                text = displayModelUI?.value ?: "",
                style = getComposeFont(
                    displayModelUI?.valueFont ?: 2,
                    displayModelUI?.valueSize ?: 14.sp,
                    displayModelUI?.valueColor ?: AppColors.grey09,
                ),
                textAlign = TextAlign.End,
            )

            displayModelUI?.valueDescription?.let {
                Text(
                    text = it,
                    style = getComposeFont(4, 14.sp, AppColors.blue02),
                    textAlign = TextAlign.End,
                    modifier = Modifier.padding(top = 4.dp),
                )
            }
        }
    }
}