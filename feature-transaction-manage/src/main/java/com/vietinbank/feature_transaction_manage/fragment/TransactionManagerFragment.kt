package com.vietinbank.feature_transaction_manage.fragment

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.fragment.app.setFragmentResultListener
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.manage.ListReportParams
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.components.dialog.DialogUtils.getResult
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_transaction_manage.FilterAction
import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.FILTER_RESULT_BUNDLE
import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.FILTER_RESULT_KEY
import com.vietinbank.feature_transaction_manage.ManagerAction
import com.vietinbank.feature_transaction_manage.ManagerViewModel
import com.vietinbank.feature_transaction_manage.di.IMTransactionNavigator
import com.vietinbank.feature_transaction_manage.screen.ManagerScreenFinal
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class TransactionManagerFragment : BaseFragment<ManagerViewModel>() {

    override val viewModel: ManagerViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var transNavigator: IMTransactionNavigator

    @Inject
    lateinit var imageLoader: CoilImageLoader

    @Composable
    override fun ComposeScreen() {
        AppTheme {
            ManagerScreenFinal(viewModel, imageLoader) { action ->
                when (action) {
                    is ManagerAction.OnBackPress -> {
                        transNavigator.popBackStack()
                    }

                    is ManagerAction.OnTabChanged -> {
                        viewModel.onTabChange(action.tabIndex)
                    }

                    is ManagerAction.OnTransaction -> {
                        when (action.action) {
                            ManagerViewModel.CLICK_COPY -> {
                                requireActivity().viewModelStore.clear()
                                val bundle = Bundle()
                                viewModel.convertToNapasTransfer(action.data)?.let { napasParam ->
                                    bundle.putString(
                                        Tags.COPY_TRANSFER_OBJECT_BUNDLE,
                                        Utils.g().provideGson().toJson(napasParam),
                                    )
                                }

                                when (action.data.tranType) {
                                    Tags.TransferType.TYPE_PAYMENT_ORDER_TRANSFER -> {
                                    }

                                    else -> {
                                        appNavigator.goToViewPagerTransferFragment(bundle)
                                    }
                                }
                            }

                            ManagerViewModel.CLICK_ROLE_APPROVE -> {
                                val bundle = Bundle()
                                bundle.putString(Tags.TRANSACTION_BUNDLE, action.data.mtId ?: "")
                                bundle.putString(
                                    Tags.ACCOUNT_NUMBER_BUNDLE,
                                    action.data.fromAccountNo ?: "",
                                )
                                bundle.putString(Tags.SERVICE_ID_BUNDLE, action.data.tranType ?: "")
                                bundle.putString(Tags.CURRENCY_BUNDLE, action.data.currency ?: "")
                                appNavigator.goToinquiryApproverListFragment(bundle)
                            }

                            ManagerViewModel.CLICK_UNC -> {
                            }

                            ManagerViewModel.CLICK_UNC_CKS -> {
                            }

                            ManagerViewModel.CLICK_TRACE -> {
                                val bundle = Bundle()
                                bundle.putString(Tags.MTID_STRING_BUNDLE, action.data.mtId)
                                appNavigator.goToTracePaymentFragment(bundle)
                            }

                            ManagerViewModel.CLICK_DELETE -> {
                                // xoa dien
//                                showConfirmDialog("")
                            }

                            // chi tiet
                            else -> {
                                transNavigator.goToDetailsTransaction(
                                    Tags.TRANSFER_OBJECT_BUNDLE to Utils.g().provideGson()
                                        .toJson(action.data),
                                )
                            }
                        }
                    }

                    is ManagerAction.OnMultipleApproval -> {
                        appNavigator.goToMultipleApprovalList(
                            action.group.tranType ?: "",
                            action.group.groupType ?: "",
                            action.group.servicetype ?: "",
                        )
                    }

                    // filter
                    is FilterAction.TapShowFilter -> {
                        viewModel.onShowFilterSheet(action.isShow)
                    }

                    is FilterAction.TapReset -> {
                        viewModel.onResetFilter()
                    }

                    is FilterAction.TapDatePicker -> {
                        viewModel.onShowDatePicker(action.isShow)
                    }

                    is FilterAction.TapFillSheet -> {
                        viewModel.onChangeSheet(action.sheetType)
                    }

                    is FilterAction.TapOnFillData -> {
                        viewModel.onChangeFillModel(action.data)
                    }

                    is FilterAction.TapChip -> {
                        viewModel.onChangeDate(action.chipType, action.chipValue)
                    }

                    is FilterAction.TapInput -> {
                        viewModel.onChangeInput(action.inputType, action.inputValue)
                    }

                    is FilterAction.TapRangeDate -> {
                        viewModel.onChangeRangeDate(action.startDate, action.endDate)
                    }

                    is FilterAction.TapChangeFilter -> {
                        viewModel.onChangeFilterAdvance()
                    }
                    is FilterAction.TapApply -> {
                        viewModel.onValidateApplyFilter()?.let {
                            showNoticeDialog(it)
                            return@let
                        }
                        viewModel.getListWithNewFilter()
                    }
                    else -> {
                    }
                }
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (!viewModel.isKeepCallInit()) {
            viewModel.onTabChange(0)
            viewModel.setKeepCallInit()
        }
        setupFragmentResultListener()
        initObserver()
    }

    private fun initObserver() {
    }

    private fun setupFragmentResultListener() {
        viewLifecycleOwner.lifecycleScope.launch {
            setFragmentResultListener(FILTER_RESULT_KEY) { _, bundle ->
                bundle.getResult<ListReportParams>(FILTER_RESULT_BUNDLE)?.let {
                    // call api
                }
            }
        }
    }
}