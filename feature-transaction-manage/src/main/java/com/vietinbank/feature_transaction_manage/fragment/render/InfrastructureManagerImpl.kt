package com.vietinbank.feature_transaction_manage.fragment.render

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.manage.TransDetailDomain
import com.vietinbank.core_domain.models.manage.TransRsUIModel
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.base.compose.InfoHorizontalView
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_transaction_manage.ManagerAction
import com.vietinbank.feature_transaction_manage.ManagerViewModel
import javax.inject.Inject

class InfrastructureManagerImpl @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : IRenderManager {
    @Composable
    override fun RenderViewDetail(
        transaction: TransDetailDomain?,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        if (transaction != null) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                    .padding(16.dp),
            ) {
                if ("975" != transaction.providerCode) {
                    // Cảng Hài Phòng
                    Text(
                        text = "Thông tin chung",
                        style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    )

                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(top = 12.dp)
                            .clip(RoundedCornerShape(4.dp))
                            .background(Color.White)
                            .padding(vertical = 20.dp, horizontal = 16.dp),
                    ) {
                        InfoHorizontalView(title = "Loại giao dich", value = transaction.tranTypeName)
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Số giao dịch",
                            value = transaction.mtId ?: "",
                        )
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Trạng thái",
                            value = transaction.statusName,
                            valueColor = AppColors.blue02,
                        )
                        transaction.activityLogs?.createdBy?.let {
                            InfoHorizontalView(
                                modifier = Modifier.padding(top = 14.dp),
                                title = "Người khởi tạo",
                                value = "${it.username} - ${it.processDate}",
                            )
                        }

                        var verifyUser = ""
                        transaction.activityLogs?.verifiedBy?.forEachIndexed { index, item ->
                            verifyUser += "${item.username ?: ""} - ${item.processDate}"
                            if (index < (transaction.activityLogs?.verifiedBy?.size ?: 0) - 1) {
                                verifyUser += "\n"
                            }
                        }
                        if (!verifyUser.isEmpty()) {
                            InfoHorizontalView(
                                modifier = Modifier.padding(top = 14.dp),
                                title = "Người phê duyệt",
                                value = verifyUser,
                            )
                        }
                    }
                } else {
                    // Cảng Hồ chí mình
                    Text(
                        text = "Thông tin đơn vị thu phí",
                        style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    )

                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(top = 12.dp)
                            .clip(RoundedCornerShape(4.dp))
                            .background(Color.White)
                            .padding(vertical = 20.dp, horizontal = 16.dp),
                    ) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Mã đơn vị thu phí",
                            value = (transaction.providerCode ?: "") + (transaction.providerName ?: ""),
                            valueColor = AppColors.blue02,
                        )
                    }
                }

                if ("01" == transaction.status) {
                    Text(
                        text = "Nội dung từ chối",
                        style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                        modifier = Modifier.padding(vertical = 12.dp),
                    )

                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .clip(RoundedCornerShape(4.dp))
                            .background(Color.White)
                            .padding(vertical = 20.dp, horizontal = 16.dp),
                    ) {
                        Text(
                            text = transaction.rejectReason ?: "",
                            style = getComposeFont(
                                2,
                                14.sp,
                                AppColors.blue07,
                            ),
                            modifier = Modifier.padding(vertical = 14.dp),
                        )
                    }
                }
                // Cảng Hài Phòng và HCM
                Text(
                    text = "Thông tin người nộp thuế",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    modifier = Modifier.padding(vertical = 12.dp),
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        title = "Hình thức",
                        value = transaction.taxMethodName ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Tên đơn vị nộp",
                        value = transaction.payname ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Mã số thuế",
                        value = transaction.paycode ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Địa chỉ",
                        value = transaction.payadd ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Từ tài khoản",
                        value = "${transaction.fromAccountNo} - VND - ${transaction.fromAccountName}",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Ngân hàng",
                        value = transaction.branchName ?: "",
                    )
                }
                if ("975" != transaction.providerCode) {
                    // Cảng Hài Phòng
                    Text(
                        text = "Thông tin cơ quan thu",
                        style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                        modifier = Modifier.padding(vertical = 12.dp),
                    )

                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .clip(RoundedCornerShape(4.dp))
                            .background(Color.White)
                            .padding(vertical = 20.dp, horizontal = 16.dp),
                    ) {
                        InfoHorizontalView(
                            title = "Tỉnh/Thành phố",
                            value = "${transaction.provinceCode ?: ""} - ${transaction.provinceName ?: ""}",
                        )

                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Địa bàn hành chính",
                            value = "${transaction.districtCode ?: ""} - ${transaction.districtName ?: ""}",
                        )

                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Mã kho bạc nhà nước",
                            value = "${transaction.treasuryCode ?: ""} - ${transaction.treasuryName ?: ""}",
                        )

                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Tên đơn vị thu phí",
                            value = "${transaction.feeUnitCode ?: ""} - ${transaction.feeUnitName ?: ""}",
                        )

                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Tài khoản ghi thu",
                            value = transaction.collectionAccountNo ?: "",
                        )

                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Mã cơ quan quản lý thu",
                            value = "${transaction.collectAgencyCode ?: ""} - ${transaction.collectAgencyName ?: ""}",
                        )

                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Số tờ khai/QĐ/thông báo",
                            value = transaction.declareNumber ?: "",
                        )
                    }
                }
                // Cảng Hài Phòng và HCM
                Text(
                    text = "Thông tin chi tiết nộp phí hạ tầng",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    modifier = Modifier.padding(vertical = 12.dp),
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "ID chứng từ",
                        value = if ("975" == transaction.providerCode) {
                            transaction.invoiceId ?: ""
                        } else {
                            transaction.docId ?: ""
                        },
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số chứng từ",
                        value = if ("975" == transaction.providerCode) {
                            transaction.voucherNumber ?: ""
                        } else {
                            transaction.docNum ?: ""
                        },
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Ký hiệu chứng từ",
                        value = if ("975" == transaction.providerCode) {
                            transaction.voucherSymbol ?: ""
                        } else {
                            transaction.docSign ?: ""
                        },
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Ngày chứng từ",
                        value = if ("975" == transaction.providerCode) {
                            transaction.voucherDate ?: ""
                        } else {
                            transaction.docDate ?: ""
                        },
                    )

                    if ("975" != transaction.providerCode) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Mã chương",
                            value = transaction.chapCode ?: "",
                        )

                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Mã tiểu mục",
                            value = transaction.subsect ?: "",
                        )
                    }

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số tiền giao dịch",
                        value = Utils.g().getDotMoneyHasCcy(
                            transaction.amount ?: "",
                            transaction.currency ?: "",
                        ),
                        valueFont = 5,
                        valueColor = AppColors.blue02,
                        valueTwo = moneyHelper.convertAmountToWords(
                            transaction.amount ?: "",
                            transaction.currency,
                        ),
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Phí giao dịch",
                        value = Utils.g().getDotMoneyHasCcy(
                            transaction.feeAmount ?: "",
                            transaction.currency ?: "",
                        ),
                    )
                }
            }
        }
    }

    @Composable
    override fun RenderViewItemManager(
        transaction: TransRsUIModel,
        onAction: (ManagerAction) -> Unit,
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White)
                .padding(12.dp)
                .safeClickable {
                    onAction.invoke(
                        ManagerAction.OnTransaction(
                            ManagerViewModel.CLICK_DETAIL,
                            transaction,
                        ),
                    )
                },
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
            ) {
                BaseText(
                    text = transaction.tranTypeName ?: "",
                    fontCus = 4,
                    color = AppColors.blue02,
                    textSize = 16.sp,
                    modifier = Modifier.weight(1f),
                )

                BaseText(
                    modifier = Modifier.padding(start = 16.dp),
                    text = Utils.getDotMoneyHasCcy(
                        transaction.amount ?: "",
                        transaction.currency ?: "",
                    ),
                    fontCus = 5,
                    color = AppColors.primaryRed,
                    textSize = 14.sp,
                    rightDrawable = R.drawable.ic_more,
                    rightDrawableSize = 24.dp,
                )
            }

            Spacer(
                modifier = Modifier
                    .padding(vertical = 16.dp)
                    .height(1.dp)
                    .fillMaxWidth()
                    .background(AppColors.blue09),
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Số tờ khai/QĐ/TB",
                value = transaction.treasuryCode.orEmpty(),
            )

            InfoHorizontalView(
                title = "Số giao dịch",
                value = transaction.mtId.orEmpty(),
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Đơn vị nộp thuế",
                value = transaction.payname.orEmpty(),
            )
            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "ID chứng từ",
                value = if ("975" == transaction.providerCode) {
                    transaction.invoiceId ?: ""
                } else {
                    transaction.docId ?: ""
                },
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Ngày tạo",
                value = transaction.createdDate.orEmpty(),
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Trạng thái",
                value = transaction.statusName.orEmpty(),
                valueColor = AppColors.blue02,
            )
        }
    }
}