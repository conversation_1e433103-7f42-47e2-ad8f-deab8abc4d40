package com.vietinbank.feature_transaction_manage.fragment.render

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_common.extensions.getAmountServer
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.manage.TransDetailDomain
import com.vietinbank.core_domain.models.manage.TransRsUIModel
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.base.compose.InfoHorizontalView
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_transaction_manage.ManagerAction
import com.vietinbank.feature_transaction_manage.ManagerViewModel
import javax.inject.Inject

/**
 * Render phat hành bảo lanh
 * */
class GuaranteeIssueManagerImpl @Inject constructor(
    private val moneyHelper: MoneyHelper,
    private val resourceProvider: IResourceProvider,
) : IRenderManager {
    @Composable
    override fun RenderViewDetail(
        transaction: TransDetailDomain?,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        if (transaction != null) {
            val amountDesc = StringBuilder()
            try {
                transaction.amount?.getAmountServer()?.let {
                    if (it.isNotEmpty()) {
                        amountDesc.append(
                            "${
                                Utils.g().getDotMoneyHasCcy(
                                    transaction.amount ?: "",
                                    transaction.currency ?: "",
                                )
                            }\n${
                                moneyHelper.convertAmountToWords(
                                    transaction.amount ?: "",
                                    transaction.currency ?: "",
                                )
                            }",
                        )
                    }
                }
                transaction.amount2?.getAmountServer()?.let {
                    if (it.isNotEmpty() && "0" != it) {
                        amountDesc.append(
                            "\n${
                                Utils.g().getDotMoneyHasCcy(
                                    transaction.amount2 ?: "",
                                    transaction.currency2 ?: "",
                                )
                            }\n${
                                moneyHelper.convertAmountToWords(
                                    transaction.amount2 ?: "",
                                    transaction.currency2 ?: "",
                                )
                            }",
                        )
                    }
                }

                transaction.amount3?.getAmountServer()?.let {
                    if (it.isNotEmpty() && "0" != it) {
                        amountDesc.append(
                            "\n${
                                Utils.g().getDotMoneyHasCcy(
                                    transaction.amount3 ?: "",
                                    transaction.currency3 ?: "",
                                )
                            }\n${
                                moneyHelper.convertAmountToWords(
                                    transaction.amount3 ?: "",
                                    transaction.currency3 ?: "",
                                )
                            }",
                        )
                    }
                }
            } catch (_: Exception) {
            }
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                    .padding(16.dp),
            ) {
                Text(
                    modifier = Modifier.padding(vertical = 8.dp),
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    text = "Thông tin chung",
                )
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        title = "Loại giao dịch",
                        value = transaction.tranTypeName ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số giao dịch",
                        value = transaction.mtId ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Trạng thái",
                        value = transaction.statusName ?: "",
                    )

                    transaction.activityLogs?.createdBy?.let {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Người tạo",
                            value = "${it.username} - ${it.processDate}",
                        )
                    }
                    var verifyUser = ""
                    transaction.activityLogs?.verifiedBy?.forEachIndexed { index, item ->
                        verifyUser += "${item.username ?: ""} - ${item.processDate}"
                        if (index < (transaction.activityLogs?.verifiedBy?.size ?: 0) - 1) {
                            verifyUser += "\n"
                        }
                    }
                    if (!verifyUser.isEmpty()) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Người phê duyệt",
                            value = verifyUser,
                        )
                    }
                    transaction.rejectReason?.let {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Lý do",
                            value = it,
                        )
                    }
                }
                Text(
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    text = "Thông tin khách hàng",
                )
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        title = "Tên công ty",
                        value = transaction.companyName ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số ĐKKD/MST/QĐ thành lập",
                        value = transaction.guaranteeCode ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Tên người đại diện/Ủy quyền",
                        value = transaction.representativeName ?: "",
                    )
                    transaction.authority?.let {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Ủy quyền số",
                            value = transaction.authority ?: "",
                        )
                    }
                }
                Text(
                    modifier = Modifier.padding(vertical = 8.dp),
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    text = "Thông tin hợp đồng",
                )
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        title = "Số hợp đồng",
                        value = transaction.contractNo ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Ngày hợp đồng",
                        value = transaction.contractDate ?: "",
                    )
                }
                Text(
                    modifier = Modifier.padding(vertical = 8.dp),
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    text = "Thông tin đề nghị phát hành bảo lãnh",
                )
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        title = "Loại bảo lãnh",
                        value = transaction.documentTypeName ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Chi nhánh phát hành bảo lãnh",
                        value = "${transaction.branch ?: ""} - ${transaction.branchName ?: ""}",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số tiền đề nghị phát hành bảo lãnh",
                        value = amountDesc.toString(),
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Mục đích bảo lãnh",
                        value = transaction.guaranteePurpose ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Thời gian hiệu lực",
                        value = "${transaction.effectiveStartDate ?: ""} - ${transaction.effectiveEndDate ?: ""}",
                    )
                }
                Text(
                    modifier = Modifier.padding(vertical = 8.dp),
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    text = "Thông tin bên nhận bảo lãnh",
                )
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        title = "Bên nhận bảo lãnh",
                        value = transaction.beneficiaryName ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Trụ sở giao dịch",
                        value = transaction.beneficiaryAddress ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Đăng ký kinh doanh",
                        value = transaction.beneficiaryCode ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Nơi cấp",
                        value = transaction.receiverIssuedBy,
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Ngày cấp",
                        value = transaction.receiverDateRange,
                    )
                }
                Text(
                    modifier = Modifier.padding(vertical = 8.dp),
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    text = "Thông tin phát hành bảo lãnh",
                )
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        title = "Cách thức phát hành",
                        value = if (!transaction.messageTypeDes.isNullOrEmpty()) {
                            transaction.messageTypeDes
                        } else {
                            when (transaction.messageType) {
                                "1" -> "Bản giấy"
                                "2" -> "Bản điện tử ký số"
                                "3" -> "Swift"
                                else -> ""
                            }
                        },
                    )
                    if ("3" == transaction.messageType) {
                        // swift
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Bin code",
                            value = transaction.biCodeInfo?.bicCode ?: "",
                        )
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Tên",
                            value = transaction.biCodeInfo?.bankName ?: "",
                        )
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Địa chỉ",
                            value = transaction.biCodeInfo?.bankAddr ?: "",
                        )
                    }
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Hình thức phát hành",
                        value = if (!transaction.issueTypeDesc.isNullOrEmpty()) {
                            transaction.issueTypeDesc
                        } else {
                            when (transaction.issueType) {
                                "1" -> "Thư bảo lãnh"
                                "2" -> "Hợp đồng bảo lãnh"
                                "3" -> "Thư tín dụng dự phòng"
                                else -> "Khác"
                            }
                        },
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Ngôn ngữ sử dụng",
                        value = transaction.language ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Điều kiện NHCT thực hiện nghĩa vụ BL",
                        value = transaction.conditionPerform ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Điều kiện giảm trừ nghĩa vụ BL",
                        value = transaction.conditionReduction ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Cách thức gửi cam kết BL",
                        value = transaction.sendTypeDesc ?: "",
                    )

                    if ("1" == transaction.messageType) {
                        // ban giay
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "NHCT giao cam kết bảo lãnh gốc cho ông/bà",
                            value = transaction.sendTypeCmnd ?: "",
                        )
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "CCCD/Hộ chiếu",
                            value = transaction.sendTypeNo ?: "",
                        )
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Ngày cấp",
                            value = transaction.sendTypeDate ?: "",
                        )
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Nơi cấp",
                            value = transaction.sendTypeBy ?: "",
                        )
                    }

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Phí BL",
                        value = if (!transaction.feeTimeDesc.isNullOrEmpty()) {
                            transaction.feeTimeDesc
                        } else {
                            when (transaction.feeTime) {
                                "1" -> "Tại thời điểm phát hành"
                                "2" -> "Thỏa thuận"
                                else -> ""
                            }
                        }, // feeTimeDesc
                    )
                    val measureDesc = StringBuilder()
                    if (true == transaction.measure?.contains("1")) {
                        measureDesc.append("\nKý quỹ")
                    }
                    if (true == transaction.measure?.contains("2")) {
                        measureDesc.append("\nCầm cố/thế chấp bằng tài sản khác của khách hàng bên thứ ba")
                    }
                    if (true == transaction.measure?.contains("3")) {
                        measureDesc.append("\nBiện pháp đảm bảo khác")
                    }
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Biện pháp đảm bảo của BL",
                        value = measureDesc.replaceFirst("\n".toRegex(), ""),
                    )
                    if (true == transaction.measure?.contains("1")) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Trích từ tài khoản số",
                            value = transaction.measureAcct,
                        )
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Số tiền ký quỹ",
                            value = Utils.g().getDotMoneyHasCcy(
                                transaction.measureAmount ?: "",
                                transaction.measureCurrency ?: "",
                            ),
                        )
                    }

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Mô tả chi tiết biện pháp đảm bảo",
                        value = transaction.measureDesc ?: "",
                    )
                }

                if (!transaction.listFilesGO.isNullOrEmpty()) {
                    Text(
                        modifier = Modifier.padding(vertical = 8.dp),
                        style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                        text = "Thông tin hồ sơ đính kèm",
                    )
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(top = 12.dp)
                            .clip(RoundedCornerShape(4.dp))
                            .background(Color.White)
                            .padding(vertical = 20.dp, horizontal = 16.dp),
                    ) {
                        // listFiles
                        transaction.listFilesGO?.forEachIndexed { index, file ->
                            InfoHorizontalView(
                                modifier = Modifier.padding(
                                    top = if (index == 0) {
                                        0.dp
                                    } else {
                                        14.dp
                                    },
                                ),
                                title = file.typeName ?: "",
                                value = file.fileName ?: "",
                            ) {
                                onFieldClick(TransactionFieldEvent.FileAttachmentClick(file))
                            }
                        }
                    }
                }
            }
        }
    }

    @Composable
    override fun RenderViewItemManager(
        transaction: TransRsUIModel,
        onAction: (ManagerAction) -> Unit,
    ) {
        val amountDesc = StringBuilder()
        try {
            transaction.amount?.getAmountServer()?.let {
                if (it.isNotEmpty()) {
                    amountDesc.append(
                        Utils.g()
                            .getDotMoneyHasCcy(
                                transaction.amount ?: "",
                                transaction.currency ?: "",
                            ),
                    )
                }
            }
            transaction.amount2?.getAmountServer()?.let {
                if (it.isNotEmpty() && "0" != it) {
                    amountDesc.append(
                        "\n${
                            Utils.g().getDotMoneyHasCcy(
                                transaction.amount2 ?: "",
                                transaction.currency2 ?: "",
                            )
                        }",
                    )
                }
            }
            transaction.amount3?.getAmountServer()?.let {
                if (it.isNotEmpty() && "0" != it) {
                    amountDesc.append(
                        "\n${
                            Utils.g().getDotMoneyHasCcy(
                                transaction.amount3 ?: "",
                                transaction.currency3 ?: "",
                            )
                        }",
                    )
                }
            }
        } catch (_: Exception) {
        }
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White)
                .padding(12.dp)
                .safeClickable {
                    onAction.invoke(
                        ManagerAction.OnTransaction(
                            ManagerViewModel.CLICK_DETAIL,
                            transaction,
                        ),
                    )
                },
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
            ) {
                BaseText(
                    text = transaction.serviceName ?: "",
                    fontCus = 4,
                    color = AppColors.blue02,
                    textSize = 16.sp,
                    modifier = Modifier.weight(1f),
                )

                BaseText(
                    modifier = Modifier.padding(start = 16.dp),
                    text = amountDesc.toString(),
                    fontCus = 5,
                    color = AppColors.primaryRed,
                    textSize = 14.sp,
                    rightDrawable = R.drawable.ic_more,
                    rightDrawableSize = 24.dp,
                    textAlign = TextAlign.End,
                )
            }

            Spacer(
                modifier = Modifier
                    .padding(vertical = 16.dp)
                    .height(1.dp)
                    .fillMaxWidth()
                    .background(AppColors.blue09),
            )

            InfoHorizontalView(
                title = "Số giao dịch",
                value = transaction.mtId ?: "",
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Loại bảo lãnh",
                value = when (transaction.type) {
                    "1" -> "Bảo lãnh tiền ứng trước"
                    "2" -> "Bảo lãnh vay vốn"
                    "3" -> "Bảo lãnh thanh toán"
                    "4" -> "Bảo lãnh dự thầu"
                    "5" -> "Bảo lãnh thực hiện hợp đồng"
                    "6" -> "Bảo lãnh bảo đảm chất lượng sản phẩm"
                    "7" -> "Bảo lãnh trả tiền ứng trước"
                    "8" -> "Bảo lãnh khác"
                    else -> ""
                },
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Bên nhận bảo lãnh",
                value = transaction.addressOfReceiver ?: "",
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Người tạo",
                value = transaction.creator ?: "",
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Trạng thái",
                value = transaction.statusName ?: "",
                valueColor = AppColors.blue02,
            )
        }
    }
}