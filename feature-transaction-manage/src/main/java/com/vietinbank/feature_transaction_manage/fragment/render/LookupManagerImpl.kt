package com.vietinbank.feature_transaction_manage.fragment.render

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.manage.TransDetailDomain
import com.vietinbank.core_domain.models.manage.TransRsUIModel
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.base.compose.InfoHorizontalView
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_transaction_manage.ManagerAction
import com.vietinbank.feature_transaction_manage.ManagerViewModel
import javax.inject.Inject

class LookupManagerImpl @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : IRenderManager {
    @Composable
    override fun RenderViewDetail(
        detailDomain: TransDetailDomain?,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        detailDomain?.let { transaction ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                    .padding(16.dp),
            ) {
                Text(
                    text = "Thông tin giao dịch tra soát",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        title = "Loại giao dich",
                        value = transaction.tranTypeName,
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số giao dịch",
                        value = transaction.mtId ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Trạng thái",
                        value = transaction.statusName,
                        valueColor = AppColors.blue02,
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Nội dung tra soát",
                        value = transaction.remark ?: "",
                    )
                    if (transaction.feeAmount.isNullOrEmpty() || transaction.feeAmount == "0") {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Phí tra soát",
                            value = "Được tính bởi chi nhánh xử lý giao dịch",
                        )
                    } else {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Phí tra soát",
                            value = Utils.g().getDotMoneyHasCcy(
                                transaction.feeAmount ?: "",
                                transaction.currency ?: "",
                            ),
                        )
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Tài khoản thu phí",
                            value = transaction.feeAccountNo ?: "",
                        )
                    }

                    transaction.activityLogs?.let {
                        it.createdBy?.let { user ->
                            InfoHorizontalView(
                                modifier = Modifier.padding(top = 14.dp),
                                title = "Người khởi tạo",
                                value = "${user.username} - ${user.processDate}",
                            )
                        }

                        var verifyUser = ""
                        it.verifiedBy?.forEachIndexed { index, user ->
                            verifyUser += "${user.username ?: ""} - ${user.processDate}"
                            if (index < (it.verifiedBy?.size ?: 0) - 1) {
                                verifyUser += "\n"
                            }
                        }
                        if (!verifyUser.isEmpty()) {
                            InfoHorizontalView(
                                modifier = Modifier.padding(top = 14.dp),
                                title = "Người phê duyệt",
                                value = verifyUser,
                            )
                        }
                    }

                    transaction.rejectReason?.let {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Lý do",
                            value = it,
                        )
                    }
                }

                Text(
                    text = "Thông tin giao dịch gốc",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    modifier = Modifier.padding(vertical = 12.dp),
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        title = "Loại giao dịch",
                        value = transaction.pref?.tranTypeName,
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số giao dịch",
                        value = transaction.pref?.mtId,
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số tham chiếu",
                        value = transaction.referenceNo,
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Trạng thái",
                        value = transaction.pref?.statusName ?: "",
                        valueColor = AppColors.blue02,
                    )

                    transaction.pref?.activityLogs?.let {
                        it.createdBy?.let { user ->
                            InfoHorizontalView(
                                modifier = Modifier.padding(top = 14.dp),
                                title = "Người khởi tạo",
                                value = "${user.username} - ${user.processDate}",
                            )
                        }
                        var verifyUser = ""
                        it.verifiedBy?.forEachIndexed { index, user ->
                            verifyUser += "${user.username ?: ""} - ${user.processDate}"
                            if (index < (it.verifiedBy?.size ?: 0) - 1) {
                                verifyUser += "\n"
                            }
                        }
                        if (!verifyUser.isEmpty()) {
                            InfoHorizontalView(
                                modifier = Modifier.padding(top = 14.dp),
                                title = "Người phê duyệt",
                                value = verifyUser,
                            )
                        }
                    }
                }

                transaction.pref?.let { pref ->
                    Text(
                        text = "Thông tin chi tiết",
                        style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                        modifier = Modifier.padding(vertical = 12.dp),
                    )
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .clip(RoundedCornerShape(4.dp))
                            .background(Color.White)
                            .padding(vertical = 20.dp, horizontal = 16.dp),
                    ) {
                        when (pref.tranType) {
                            "in", "ou", "np", "pm" -> TransferDetail(pref, onFieldClick)
                            "sx", "sl", "slo" -> SalaryDetail(pref, onFieldClick)
                            "hu", "ba" -> BulkDetail(pref, onFieldClick)
                            else -> DefaultDetail(pref, onFieldClick)
                        }
                    }
                }
            }
        }
    }

    @Composable
    override fun RenderViewItemManager(
        transaction: TransRsUIModel,
        onAction: (ManagerAction) -> Unit,
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White)
                .padding(12.dp)
                .safeClickable {
                    onAction.invoke(
                        ManagerAction.OnTransaction(
                            ManagerViewModel.CLICK_DETAIL,
                            transaction,
                        ),
                    )
                },
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
            ) {
                Text(
                    text = "Số giao dịch gốc",
                    style = getComposeFont(4, 16.sp, AppColors.blue02),
                    modifier = Modifier.weight(1f),
                )

                BaseText(
                    modifier = Modifier.padding(start = 16.dp),
                    text = transaction.tracerMtId ?: "",
                    fontCus = 5,
                    color = AppColors.primaryRed,
                    textSize = 14.sp,
                    rightDrawable = R.drawable.ic_more,
                    rightDrawableSize = 24.dp,
                )
            }

            Spacer(
                modifier = Modifier
                    .padding(top = 16.dp)
                    .height(1.dp)
                    .fillMaxWidth()
                    .background(AppColors.blue09),
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Số giao dịch",
                value = transaction.mtId,
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Nội dung",
                value = transaction.tracerContent,
            )
            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Ngày tạo",
                value = transaction.activityLogs?.createdBy?.processDate,
            )
        }
    }

    @Composable
    private fun TransferDetail(
        transaction: TransDetailDomain,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        InfoHorizontalView(
            title = "Từ tài khoản",
            value = "${transaction.fromAccountNo ?: ""} - ${transaction.currency} - ${transaction.fromAccountName ?: ""}",
        )
        InfoHorizontalView(
            modifier = Modifier.padding(top = 14.dp),
            title = "Tới tài khoản",
            value = "${transaction.toAccountNo ?: ""}\n${transaction.receiveName ?: ""}",
        )
        InfoHorizontalView(
            modifier = Modifier.padding(top = 14.dp),
            title = "Ngân hàng",
            value = transaction.receiveBankName ?: "",
        )

        InfoHorizontalView(
            modifier = Modifier.padding(top = 14.dp),
            title = "Số tiền",
            value = Utils.g().getDotMoneyHasCcy(
                transaction.amount ?: "",
                transaction.currency ?: "",
            ),
            valueTwo = moneyHelper.convertAmountToWords(
                transaction.amount ?: "",
                transaction.currency ?: "",
            ),
        )
        InfoHorizontalView(
            modifier = Modifier.padding(top = 14.dp),
            title = "Phí giao dịch",
            value = Utils.g().getDotMoneyHasCcy(
                transaction.feeAmount ?: "",
                transaction.currency ?: "",
            ),
        )

        InfoHorizontalView(
            modifier = Modifier.padding(top = 14.dp),
            title = "Hình thức thu phí",
            value = transaction.feePayMethodDesc ?: "",
        )

        InfoHorizontalView(
            modifier = Modifier.padding(top = 14.dp),
            title = "Nội dung",
            value = transaction.remark ?: "",
        )

        transaction.listFile?.firstOrNull()?.let {
            InfoHorizontalView(
                modifier = Modifier.padding(top = 14.dp),
                title = "File đính kèm",
                value = it.fileName ?: "",
                valueColor = AppColors.blue02,
            ) {
                onFieldClick(TransactionFieldEvent.FileAttachmentClick(it))
            }
        }

        InfoHorizontalView(
            modifier = Modifier.padding(top = 14.dp),
            title = "Thời gian giao dịch",
            value = transaction.createdDate ?: "",
        )

        InfoHorizontalView(
            modifier = Modifier.padding(top = 14.dp),
            title = "Thời gian chuyển",
            value = if (transaction.process_time.isNullOrEmpty()) "Chuyển ngay" else "Đặt lịch",
        )
        if (!transaction.process_time.isNullOrEmpty()) {
            InfoHorizontalView(
                modifier = Modifier.padding(top = 14.dp),
                title = "Ngày đặt lịch",
                value = transaction.process_time ?: "",
            )
        }
    }

    // chi luong tu dong + chi luong qua ngan hang
    @Composable
    private fun SalaryDetail(
        transaction: TransDetailDomain,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        InfoHorizontalView(
            title = "Số tiền",
            value = Utils.g().getDotMoneyHasCcy(
                transaction.amount ?: "",
                transaction.currency ?: "",
            ),
            valueTwo = moneyHelper.convertAmountToWords(
                transaction.amount ?: "",
                transaction.currency ?: "",
            ),
        )

        if (transaction.tranType == "sx") {
            transaction.exchangeRate?.let {
                InfoHorizontalView(
                    modifier = Modifier.padding(top = 14.dp),
                    title = "Tỷ giá (quy đổi tham khảo)",
                    value = Utils.g().getDotMoneyHasCcy(it, transaction.debitCurrency ?: ""),
                )
            }
            transaction.debitAmount?.let {
                InfoHorizontalView(
                    modifier = Modifier.padding(top = 14.dp),
                    title = "Số tiền trích nợ (tham khảo)",
                    value = Utils.g().getDotMoneyHasCcy(it, transaction.debitCurrency ?: ""),
                )
            }
        }

        InfoHorizontalView(
            modifier = Modifier.padding(top = 14.dp),
            title = "Phí giao dịch",
            value = Utils.g().getDotMoneyHasCcy(
                transaction.feeAmount ?: "",
                transaction.currency ?: "",
            ),
        )

        InfoHorizontalView(
            modifier = Modifier.padding(top = 14.dp),
            title = "Nội dung",
            value = transaction.remark ?: "",
        )

        transaction.listFile?.firstOrNull()?.let {
            InfoHorizontalView(
                modifier = Modifier.padding(top = 14.dp),
                title = "File đính kèm",
                value = it.fileName ?: "",
                valueColor = AppColors.blue02,
            ) {
                onFieldClick(TransactionFieldEvent.FileAttachmentClick(it))
            }
        }

        transaction.listFile2?.firstOrNull()?.let {
            InfoHorizontalView(
                modifier = Modifier.padding(top = 14.dp),
                title = "Hồ sơ đính kèm",
                value = it.fileName ?: "",
                valueColor = AppColors.blue02,
            ) {
                onFieldClick(TransactionFieldEvent.FileAttachmentClick(it))
            }
        }

        InfoHorizontalView(
            modifier = Modifier.padding(top = 14.dp),
            title = "Thời gian giao dịch",
            value = transaction.createdDate ?: "",
        )

        InfoHorizontalView(
            modifier = Modifier.padding(top = 14.dp),
            title = "Thời gian chuyển",
            value = if (transaction.process_time.isNullOrEmpty()) "Chuyển ngay" else "Đặt lịch",
        )
        if (!transaction.process_time.isNullOrEmpty()) {
            InfoHorizontalView(
                modifier = Modifier.padding(top = 14.dp),
                title = "Ngày đặt lịch",
                value = transaction.process_time ?: "",
            )
        }
    }

    // chuyen tien theo file
    @Composable
    private fun BulkDetail(
        transaction: TransDetailDomain,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        InfoHorizontalView(
            title = "Số tiền",
            value = Utils.g().getDotMoneyHasCcy(
                transaction.amount ?: "",
                transaction.currency ?: "",
            ),
            valueTwo = moneyHelper.convertAmountToWords(
                transaction.amount ?: "",
                transaction.currency ?: "",
            ),
        )

        if (transaction.tranType == "sx") {
            transaction.exchangeRate?.let {
                InfoHorizontalView(
                    modifier = Modifier.padding(top = 14.dp),
                    title = "Tỷ giá (quy đổi tham khảo)",
                    value = Utils.g().getDotMoneyHasCcy(it, transaction.debitCurrency ?: ""),
                )
            }
            transaction.debitAmount?.let {
                InfoHorizontalView(
                    modifier = Modifier.padding(top = 14.dp),
                    title = "Số tiền trích nợ (tham khảo)",
                    value = Utils.g().getDotMoneyHasCcy(it, transaction.debitCurrency ?: ""),
                )
            }
        }
        InfoHorizontalView(
            modifier = Modifier.padding(top = 14.dp),
            title = "Phí giao dịch",
            value = Utils.g().getDotMoneyHasCcy(
                transaction.feeAmount ?: "",
                transaction.currency ?: "",
            ),
        )

        InfoHorizontalView(
            modifier = Modifier.padding(top = 14.dp),
            title = "Nội dung",
            value = transaction.remark ?: "",
        )

        transaction.listFile?.firstOrNull()?.let {
            InfoHorizontalView(
                modifier = Modifier.padding(top = 14.dp),
                title = "File đính kèm",
                value = it.fileName ?: "",
                valueColor = AppColors.blue02,
            ) {
                onFieldClick(TransactionFieldEvent.FileAttachmentClick(it))
            }
        }

        transaction.listFile2?.firstOrNull()?.let {
            InfoHorizontalView(
                modifier = Modifier.padding(top = 14.dp),
                title = "Hồ sơ đính kèm",
                value = it.fileName ?: "",
                valueColor = AppColors.blue02,
            ) {
                onFieldClick(TransactionFieldEvent.FileAttachmentClick(it))
            }
        }

        InfoHorizontalView(
            modifier = Modifier.padding(top = 14.dp),
            title = "Thời gian giao dịch",
            value = transaction.createdDate ?: "",
        )

        InfoHorizontalView(
            modifier = Modifier.padding(top = 14.dp),
            title = "Thời gian chuyển",
            value = if (transaction.process_time.isNullOrEmpty()) "Chuyển ngay" else "Đặt lịch",
        )
        if (!transaction.process_time.isNullOrEmpty()) {
            InfoHorizontalView(
                modifier = Modifier.padding(top = 14.dp),
                title = "Ngày đặt lịch",
                value = transaction.process_time ?: "",
            )
        }
    }

    // chuyen tien tach lenh
    @Composable
    private fun DefaultDetail(
        transaction: TransDetailDomain,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        InfoHorizontalView(
            title = "Từ tài khoản",
            value = "${transaction.fromAccountNo ?: ""}\n${transaction.fromAccountName ?: ""}",
        )
        InfoHorizontalView(
            modifier = Modifier.padding(top = 14.dp),
            title = "Tới tài khoản",
            value = "${transaction.toAccountNo ?: ""}\n${transaction.receiveName ?: ""}",
        )
        InfoHorizontalView(
            modifier = Modifier.padding(top = 14.dp),
            title = "Ngân hàng",
            value = transaction.receiveBankName ?: "",
        )
        InfoHorizontalView(
            modifier = Modifier.padding(top = 14.dp),
            title = "Số tiền",
            value = Utils.g().getDotMoneyHasCcy(
                transaction.amount ?: "",
                transaction.currency ?: "",
            ),
            valueTwo = moneyHelper.convertAmountToWords(
                transaction.amount ?: "",
                transaction.currency ?: "",
            ),
        )
        InfoHorizontalView(
            modifier = Modifier.padding(top = 14.dp),
            title = "Phí giao dịch",
            value = Utils.g().getDotMoneyHasCcy(
                transaction.feeAmount ?: "",
                transaction.currency ?: "",
            ),
        )
        InfoHorizontalView(
            modifier = Modifier.padding(top = 14.dp),
            title = "Hình thức thu phí",
            value = transaction.feePayMethodDesc ?: "",
        )

        InfoHorizontalView(
            modifier = Modifier.padding(top = 14.dp),
            title = "Nội dung",
            value = transaction.remark ?: "",
        )
        InfoHorizontalView(
            modifier = Modifier.padding(top = 14.dp),
            title = "Thời gian giao dịch",
            value = transaction.createdDate ?: "",
        )
    }
}