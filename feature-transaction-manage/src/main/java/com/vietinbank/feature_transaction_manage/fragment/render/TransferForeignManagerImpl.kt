package com.vietinbank.feature_transaction_manage.fragment.render

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.manage.TransDetailDomain
import com.vietinbank.core_domain.models.manage.TransRsUIModel
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.base.compose.InfoHorizontalView
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_transaction_manage.ManagerAction
import com.vietinbank.feature_transaction_manage.ManagerViewModel
import javax.inject.Inject

class TransferForeignManagerImpl @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : IRenderManager {
    @Composable
    override fun RenderViewDetail(
        transaction: TransDetailDomain?,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        if (transaction != null) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                    .padding(16.dp),
            ) {
                Text(
                    text = "Thông tin chung",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(title = "Loại giao dich", value = transaction.tranTypeName)
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số giao dịch",
                        value = transaction.mtId ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Trạng thái",
                        value = transaction.statusName,
                        valueColor = AppColors.blue02,
                    )
                    transaction.activityLogs?.createdBy?.let {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Người khởi tạo",
                            value = "${it.username} - ${it.processDate}",
                        )
                    }

                    var verifyUser = ""
                    transaction.activityLogs?.verifiedBy?.forEachIndexed { index, item ->
                        verifyUser += "${item.username ?: ""} - ${item.processDate}"
                        if (index < (transaction.activityLogs?.verifiedBy?.size ?: 0) - 1) {
                            verifyUser += "\n"
                        }
                    }
                    if (!verifyUser.isEmpty()) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Người phê duyệt",
                            value = verifyUser,
                        )
                    }
                }

                Text(
                    text = "Thông tin người chuyển",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    modifier = Modifier.padding(vertical = 12.dp),
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Tên đơn vị mua/chuyển tiền",
                        value = transaction.corpName ?: "",
                    )

                    InfoHorizontalView(
                        title = "Hình thức",
                        value = if (transaction.remitterName?.isEmpty() == false) {
                            "Nguời mua, chuyển tiền là người chuyển thực tế"
                        } else {
                            "Nguời mua, chuyển tiền Không là Người chuyển thực tế"
                        },
                    )

                    if (transaction.remitterName?.isEmpty() == false) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Tên người chuyển tiền thực tế",
                            value = transaction.remitterName ?: "",
                        )

                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Quốc gia",
                            value = transaction.remitterCountry ?: "",
                        )

                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Tỉnh/Thành Phố",
                            value = transaction.remitterDistrict ?: "",
                        )

                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Phuường/Xã",
                            value = transaction.remitterWard ?: "",
                        )

                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Đường",
                            value = transaction.remitterStreet ?: "",
                        )
                    }
                }

                Text(
                    text = "Thông tin chi tiết",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    modifier = Modifier.padding(vertical = 12.dp),
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Cán bộ giới thiệu",
                        value = transaction.introducer ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Tên chi nhánh xử lý",
                        value = transaction.branchName ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số tiền chuyển",
                        value = Utils.g().getDotMoneyHasCcy(
                            transaction.amount ?: "",
                            transaction.currency ?: "",
                        ),
                        valueTwo = moneyHelper.convertAmountToWords(
                            transaction.amount ?: "",
                            transaction.currency,
                        ),
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Nội dung giao dịch",
                        value = transaction.remark ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Ghi chú",
                        value = transaction.note ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Ngày giá trị",
                        value = transaction.valueDate ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Mục đích sử dụng ngoại tệ",
                        value = transaction.purposeTransferName ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Ngày hoàn chứng từ",
                        value = if (transaction.lcReturnDate?.isEmpty() == false) {
                            "Thanh toán sau khi nhận hàng"
                        } else {
                            "Thanh toán trước khi nhận hàng"
                        },
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Ngày",
                        value = transaction.lcReturnDate ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Tổng số tiền phí",
                        value = Utils.g().getDotMoneyHasCcy(
                            transaction.feeAmount ?: "",
                            transaction.currency ?: "",
                        ),
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Loại phí",
                        value = transaction.feeType ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Tài khoản trích nợ phí",
                        value = transaction.feeAccountNo ?: "",
                    )
                }

                Text(
                    text = "Tài khoản trích nợ 1",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    modifier = Modifier.padding(vertical = 12.dp),
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số tài khoản",
                        value = transaction.fromAccountNo ?: "",
                        valueTwo = transaction.fromAccountName ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Tỷ giá",
                        value = transaction.debitAmt1 ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số tiền trích nợ tạm tính",
                        value = Utils.g().getDotMoneyHasCcy(
                            transaction.debitAmtByRate1 ?: "",
                            transaction.debitCurrency1 ?: "",
                        ),
                        valueTwo = moneyHelper.convertAmountToWords(
                            transaction.debitAmtByRate1 ?: "",
                            transaction.debitCurrency1,
                        ),
                    )
                }

                Text(
                    text = "Tài khoản trích nợ 2",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    modifier = Modifier.padding(vertical = 12.dp),
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số tài khoản",
                        value = transaction.debitAccountNo2 ?: "",
                        valueTwo = transaction.fromAccountName2 ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Tỷ giá",
                        value = transaction.debitAmt2 ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số tiền trích nợ tạm tính",
                        value = Utils.g().getDotMoneyHasCcy(
                            transaction.debitAmtByRate2 ?: "",
                            transaction.debitCurrency2 ?: "",
                        ),
                        valueTwo = moneyHelper.convertAmountToWords(
                            transaction.debitAmtByRate2 ?: "",
                            transaction.debitCurrency2,
                        ),
                    )
                }

                Text(
                    text = "Thông tin người hưởng",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    modifier = Modifier.padding(vertical = 12.dp),
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số tài khoản/IBAN người hưởng",
                        value = transaction.beneficiaryAccount ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Tên",
                        value = transaction.benName ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Quốc gia",
                        value = transaction.benCountry ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Tỉnh/Thành Phố",
                        value = transaction.benDistrict ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Phuường/Xã",
                        value = transaction.benWard ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Đường",
                        value = transaction.benStreet ?: "",
                    )

                    InfoHorizontalView(
                        title = "Người hưởng là người hưởng cuối cùng",
                        value = if (transaction.remitterName?.isEmpty() == false) {
                            "Có"
                        } else {
                            "Không"
                        },
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Tên người hưởng cuối cùng",
                        value = transaction.finalBenName ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Quốc gia",
                        value = transaction.finalBenCountry ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Tỉnh/Thành Phố",
                        value = transaction.finalBenDistrict ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Phuường/Xã",
                        value = transaction.finalBenWard ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Đường",
                        value = transaction.finalBenStreet ?: "",
                    )
                }

                Text(
                    text = "Ngân hàng hưởng",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    modifier = Modifier.padding(vertical = 12.dp),
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Bic code",
                        value = transaction.receiveBank ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Tên/Địa chỉ",
                        value = transaction.benBankName ?: "",
                    )
                    if (!transaction.benBankCountry.isNullOrEmpty()) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Quốc gia",
                            value = transaction.benBankCountry ?: "",
                        )
                    }
                    if (!transaction.benBankDistrict.isNullOrEmpty()) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Tỉnh/Thành Phố",
                            value = transaction.benBankDistrict ?: "",
                        )
                    }
                    if (!transaction.benBankWard.isNullOrEmpty()) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Phuường/Xã",
                            value = transaction.benBankWard ?: "",
                        )
                    }
                    if (!transaction.benBankStreet.isNullOrEmpty()) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Đường",
                            value = transaction.benBankStreet ?: "",
                        )
                    }
                }

                Text(
                    text = "Ngân hàng trung gian",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    modifier = Modifier.padding(vertical = 12.dp),
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Bic code",
                        value = transaction.midBankCode ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Tên/Địa chỉ",
                        value = transaction.midBankName2 ?: "",
                    )
                    if (!transaction.midBankCountry.isNullOrEmpty()) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Quốc gia",
                            value = transaction.midBankCountry ?: "",
                        )
                    }
                    if (!transaction.midBankDistrict.isNullOrEmpty()) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Tỉnh/Thành Phố",
                            value = transaction.midBankDistrict ?: "",
                        )
                    }
                    if (!transaction.midBankWard.isNullOrEmpty()) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Phuường/Xã",
                            value = transaction.midBankWard ?: "",
                        )
                    }
                    if (!transaction.midBankStreet.isNullOrEmpty()) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Đường",
                            value = transaction.midBankStreet ?: "",
                        )
                    }
                }

                if (!transaction.listFile.isNullOrEmpty()) {
                    Text(
                        text = "File đính kèm",
                        style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                        modifier = Modifier.padding(vertical = 12.dp),
                    )
                    transaction.listFile?.firstOrNull()?.let {
                        Column(
                            modifier = Modifier
                                .fillMaxSize()
                                .clip(RoundedCornerShape(4.dp))
                                .background(Color.White)
                                .padding(vertical = 20.dp, horizontal = 16.dp),
                        ) {
                            InfoHorizontalView(
                                modifier = Modifier.padding(top = 14.dp),
                                title = "Hồ sơ CMNĐSDNT",
                                value = it.fileName ?: "",
                                valueColor = AppColors.blue02,
                            ) {
                                onFieldClick(TransactionFieldEvent.FileAttachmentClick(it))
                            }
                        }
                    }
                }
                Text(
                    text = "Mẫu chứng từ",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    modifier = Modifier.padding(vertical = 12.dp),
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "File đính kèm",
                        value = transaction.midBankName2 ?: "", // 0
                    )
                }
            }
        }
    }

    @Composable
    override fun RenderViewItemManager(
        transaction: TransRsUIModel,
        onAction: (ManagerAction) -> Unit,
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White)
                .padding(12.dp)
                .safeClickable {
                    onAction.invoke(
                        ManagerAction.OnTransaction(
                            ManagerViewModel.CLICK_DETAIL,
                            transaction,
                        ),
                    )
                },
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
            ) {
                BaseText(
                    text = transaction.tranTypeName ?: "",
                    fontCus = 4,
                    color = AppColors.blue02,
                    textSize = 16.sp,
                    modifier = Modifier.weight(1f),
                )

                BaseText(
                    modifier = Modifier.padding(start = 16.dp),
                    text = Utils.getDotMoneyHasCcy(
                        transaction.amount ?: "",
                        transaction.currency ?: "",
                    ),
                    fontCus = 5,
                    color = AppColors.primaryRed,
                    textSize = 14.sp,
                    rightDrawable = R.drawable.ic_more,
                    rightDrawableSize = 24.dp,
                )
            }

            Spacer(
                modifier = Modifier
                    .padding(vertical = 16.dp)
                    .height(1.dp)
                    .fillMaxWidth()
                    .background(AppColors.blue09),
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Số tài khoản/IBAN người hưởng",
                value = transaction.toAccountNo.orEmpty(),
            )

            InfoHorizontalView(
                title = "Số giao dịch",
                value = transaction.mtId.orEmpty(),
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Nội dung",
                value = transaction.remark.orEmpty(),
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Ngày tạo",
                value = transaction.createdDate.orEmpty(),
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Trạng thái",
                value = transaction.statusName.orEmpty(),
                valueColor = AppColors.blue02,
            )
        }
    }
}