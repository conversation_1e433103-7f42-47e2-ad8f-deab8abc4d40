package com.vietinbank.feature_transaction_manage.fragment.render

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_common.extensions.getAmountServer
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.manage.TransDetailDomain
import com.vietinbank.core_domain.models.manage.TransRsUIModel
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.base.compose.InfoHorizontalView
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_transaction_manage.ManagerAction
import com.vietinbank.feature_transaction_manage.ManagerViewModel
import javax.inject.Inject

class GuaranteeAmendManagerImpl @Inject constructor(
    private val moneyHelper: MoneyHelper,
    private val resourceProvider: IResourceProvider,
) : IRenderManager {

    @Composable
    override fun RenderViewDetail(
        transaction: TransDetailDomain?,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        if (transaction != null) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                    .padding(16.dp),
            ) {
                Text(
                    modifier = Modifier.padding(vertical = 8.dp),
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    text = "Thông tin chung",
                )
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        title = "Loại giao dịch",
                        value = transaction.tranTypeName,
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số giao dịch",
                        value = transaction.mtId ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Trạng thái",
                        value = transaction.statusName ?: "",
                    )
                    transaction.activityLogs?.createdBy?.let {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Người tạo",
                            value = "${it.username} - ${it.processDate}",
                        )
                    }
                    var verifyUser = ""
                    transaction.activityLogs?.verifiedBy?.forEachIndexed { index, item ->
                        verifyUser += "${item.username ?: ""} - ${item.processDate}"
                        if (index < (transaction.activityLogs?.verifiedBy?.size ?: 0) - 1) {
                            verifyUser += "\n"
                        }
                    }
                    if (!verifyUser.isEmpty()) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Người phê duyệt",
                            value = verifyUser,
                        )
                    }
                    transaction.rejectReason?.let {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Lý do",
                            value = it,
                        )
                    }
                }
                Text(
                    modifier = Modifier.padding(vertical = 8.dp),
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    text = "Thông tin khoản bảo lãnh",
                )
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        title = "Số bảo lãnh",
                        value = transaction.host_mtid ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số HĐ đã ký kết với Ngân hàng",
                        value = transaction.contractNo ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Ngày hợp đồng",
                        value = transaction.contractDate ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Chi nhánh Vietinbank",
                        value = "${transaction.branch ?: ""} - ${transaction.branchName ?: ""}",
                    )
                }
                Text(
                    modifier = Modifier.padding(vertical = 8.dp),
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    text = "Thông tin khách hàng",
                )
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        title = "Tên công ty",
                        value = transaction.cifName ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số ĐKKD/MST/QĐ thành lập",
                        value = transaction.guaranteeCode ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Ngày cấp",
                        value = transaction.dateRange ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Nơi cấp",
                        value = transaction.issuesBy ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Trụ sở",
                        value = transaction.headQuarters ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Tên người đại diện/Ủy quyền",
                        value = transaction.representativeName ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Ủy quyền số",
                        value = transaction.authority ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Ngày ủy quyền",
                        value = transaction.authorizationDate ?: "",
                    )
                }
                val typeChangeDesc = StringBuilder()
                if (true == transaction.typeChange?.contains("1")) {
                    typeChangeDesc.append("\nThời gian hiệu lực của bảo lãnh")
                }
                if (true == transaction.typeChange?.contains("2")) {
                    typeChangeDesc.append("\nSố tiền bảo lãnh")
                }
                if (true == transaction.typeChange?.contains("3")) {
                    typeChangeDesc.append("\nCác nội dung khác")
                }
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    modifier = Modifier.padding(vertical = 8.dp),
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    text = "Nội dung sửa đổi bảo lãnh",
                )
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        title = "Loại sửa đổi",
                        value = typeChangeDesc.replaceFirst("\n".toRegex(), ""),
                    )
                }
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    modifier = Modifier.padding(vertical = 8.dp),
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    text = "Chi tiết sửa đổi",
                )
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    if (true == transaction.typeChange?.contains("1")) {
                        Text(
                            text = "Thời gian hiệu lực bảo lãnh",
                            style = getComposeFont(2, 16.sp, AppColors.blue02),
                        )
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Ngày/sự kiện bắt đầu có hiệu lực",
                            value = transaction.effectiveStartDate ?: "",
                        )
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Đề nghị sửa đổi",
                            value = transaction.effectiveStartDateChange ?: "",
                        )
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Ngày/sự kiện kết thúc hiệu lực",
                            value = transaction.effectiveEndDate ?: "",
                        )
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Đề nghị sửa đổi",
                            value = transaction.effectiveEndDateChange ?: "",
                        )
                    }
                    if (true == transaction.typeChange?.contains("2")) {
                        Text(
                            text = "Số tiền bảo lãnh",
                            style = getComposeFont(2, 16.sp, AppColors.blue02),
                        )
                        val amountDesc = StringBuilder()
                        transaction.amount?.getAmountServer()?.let {
                            if (it.isNotEmpty()) {
                                amountDesc.append(
                                    "${
                                        Utils.g().getDotMoneyHasCcy(
                                            transaction.amount ?: "",
                                            transaction.currency ?: "",
                                        )
                                    }\n${
                                        moneyHelper.convertAmountToWords(
                                            transaction.amount,
                                            transaction.currency,
                                        )
                                    }",
                                )
                            }
                        }

                        transaction.amount2?.getAmountServer()?.let {
                            if (it.isNotEmpty() && "0" != it) {
                                amountDesc.append(
                                    "\n${
                                        Utils.g().getDotMoneyHasCcy(
                                            transaction.amount2 ?: "",
                                            transaction.currency2 ?: "",
                                        )
                                    }\n${
                                        moneyHelper.convertAmountToWords(
                                            transaction.amount2,
                                            transaction.currency2,
                                        )
                                    }",
                                )
                            }
                        }
                        transaction.amount3?.getAmountServer()?.let {
                            if (it.isNotEmpty() && "0" != it) {
                                amountDesc.append(
                                    "\n${
                                        Utils.g().getDotMoneyHasCcy(
                                            transaction.amount3 ?: "",
                                            transaction.currency3 ?: "",
                                        )
                                    }\n${
                                        moneyHelper.convertAmountToWords(
                                            transaction.amount3,
                                            transaction.currency3,
                                        )
                                    }",
                                )
                            }
                        }

                        val amountChangeDesc = StringBuilder()
                        transaction.amountChange?.getAmountServer()?.let {
                            if (it.isNotEmpty()) {
                                amountChangeDesc.append(
                                    "${
                                        Utils.g().getDotMoneyHasCcy(
                                            transaction.amountChange ?: "",
                                            transaction.currency ?: "",
                                        )
                                    }\n${
                                        moneyHelper.convertAmountToWords(
                                            transaction.amountChange,
                                            transaction.currency,
                                        )
                                    }",
                                )
                            }
                        }
                        transaction.amountChange2?.getAmountServer()?.let {
                            if (it.isNotEmpty() && "0" != it) {
                                amountChangeDesc.append(
                                    "\n${
                                        Utils.g().getDotMoneyHasCcy(
                                            transaction.amountChange2 ?: "",
                                            transaction.currency2 ?: "",
                                        )
                                    }\n${
                                        moneyHelper.convertAmountToWords(
                                            transaction.amountChange2,
                                            transaction.currency2,
                                        )
                                    }",
                                )
                            }
                        }
                        transaction.amountChange3?.getAmountServer()?.let {
                            if (it.isNotEmpty() && "0" != it) {
                                amountChangeDesc.append(
                                    "\n${
                                        Utils.g().getDotMoneyHasCcy(
                                            transaction.amountChange3 ?: "",
                                            transaction.currency3 ?: "",
                                        )
                                    }\n${
                                        moneyHelper.convertAmountToWords(
                                            transaction.amountChange3,
                                            transaction.currency3,
                                        )
                                    }",
                                )
                            }
                        }
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Số tiền cam kết bảo lãnh",
                            value = amountDesc.toString(),
                        )
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Số tiền sửa đổi",
                            value = amountChangeDesc.toString(),
                        )
                    }
                    if (true == transaction.typeChange?.contains("3")) {
                        Text(
                            text = "Các nội dung khác",
                            style = getComposeFont(2, 16.sp, AppColors.blue02),
                        )
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Nội dung theo cam kết bảo lãnh",
                            value = transaction.guaranteeCommitContent ?: "",
                        )
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Đề nghị sửa đổi/bổ sung thành",
                            value = transaction.guaranteeCommitContentChange ?: "",
                        )
                    }
                }
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    modifier = Modifier.padding(vertical = 8.dp),
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    text = "Thông tin khác",
                )
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        title = "Lý do sửa đổi",
                        value = transaction.reasonChange,
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Mức phí",
                        value = transaction.feeDesc ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Cách thức sửa đổi cam kết bảo lãnh",
                        value = if (!transaction.message.isNullOrEmpty()) {
                            transaction.message
                        } else {
                            when (transaction.messageType) {
                                "1" -> "Bản giấy"
                                "2" -> "Bản điện tử ký số"
                                "3" -> "SWIFT"
                                else -> ""
                            }
                        },
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Cách thức gửi cam kết bảo lãnh",
                        value = if (!transaction.sendTypeDesc.isNullOrEmpty()) {
                            transaction.sendTypeDesc
                        } else {
                            when (transaction.sendType) {
                                "1" -> "Chúng tôi nhận và chuyển cho bên nhận bảo lãnh"
                                "2" -> "Ngân hàng gửi cho bên nhận bảo lãnh"
                                "3" -> "Chuyển thông qua mạng SWIFT"
                                else -> ""
                            }
                        },
                    )
                    when (transaction.messageType) {
                        "1" -> {
                            // ban giay
                            InfoHorizontalView(
                                modifier = Modifier.padding(top = 14.dp),
                                title = "NHCT giao Cam kết bảo lãnh gốc cho ông/bà",
                                value = transaction.sendTypeCmnd ?: "",
                            )
                            InfoHorizontalView(
                                modifier = Modifier.padding(top = 14.dp),
                                title = "CCCD/Hộ chiếu",
                                value = transaction.sendTypeNo ?: "",
                            )
                            InfoHorizontalView(
                                modifier = Modifier.padding(top = 14.dp),
                                title = "Ngày cấp",
                                value = transaction.sendTypeDate ?: "",
                            )
                            InfoHorizontalView(
                                modifier = Modifier.padding(top = 14.dp),
                                title = "Nơi cấp",
                                value = transaction.sendTypeBy ?: "",
                            )
                        }

                        "3" -> {
                            // swift
                            InfoHorizontalView(
                                modifier = Modifier.padding(top = 14.dp),
                                title = "Bin code",
                                value = transaction.biCodeInfo?.bicCode ?: "",
                            )
                            InfoHorizontalView(
                                modifier = Modifier.padding(top = 14.dp),
                                title = "Tên",
                                value = transaction.biCodeInfo?.bankName ?: "",
                            )
                            InfoHorizontalView(
                                modifier = Modifier.padding(top = 14.dp),
                                title = "Địa chỉ",
                                value = transaction.biCodeInfo?.bankAddr ?: "",
                            )
                        }

                        else -> {
                        }
                    }

                    val measureDesc = StringBuilder()
                    if (true == transaction.measure?.contains("1")) {
                        measureDesc.append("\nKý quỹ tăng thêm")
                    }
                    if (true == transaction.measure?.contains("2")) {
                        measureDesc.append("\nTSĐB khác bổ sung")
                    }
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "TSĐB hoàn ký quỹ",
                        value = measureDesc.replaceFirst("\n".toRegex(), ""),
                    )
                    if (true == transaction.measure?.contains("1")) {
                        // ky quy tang them
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Tài khoản trích ký quỹ",
                            value = transaction.measureAcct ?: "",
                        )
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Số tiền ký quỹ tăng thêm",
                            value = Utils.g().getDotMoneyHasCcy(
                                transaction.measureAmount ?: "",
                                transaction.measureCurrency ?: "",
                            ),
                        )
                    }
                    if (true == transaction.measure?.contains("2")) {
                        // TSĐB bo sung khac
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Chi tiết tài sản đảm bảo bổ sung khác",
                            value = transaction.measureDesc ?: "",
                        )
                    }
                }
                Text(
                    modifier = Modifier.padding(vertical = 8.dp),
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    text = "Thông tin hồ sơ đính kèm",
                )
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    // listFiles
                    transaction.listFilesGO?.forEachIndexed { index, file ->
                        InfoHorizontalView(
                            modifier = Modifier.padding(
                                top = if (index == 0) {
                                    0.dp
                                } else {
                                    14.dp
                                },
                            ),
                            title = when (file.attachmentType) {
                                "GDN" -> "Giấy đề nghị giải tỏa/giảm trừ bảo lãnh"
                                "13" -> "Biên bản chấp thuận/Thông báo chấp nhận hình thức bảo đảm khác thay cho bảo lãnh khác"
                                "11" -> "Biên bản thanh lý hợp đồng/Biên bản làm việc"
                                else -> ""
                            },
                            value = file.fileName ?: "",
                        ) {
                            onFieldClick(TransactionFieldEvent.FileAttachmentClick(file))
                        }
                    }
                }
            }
        }
    }

    @Composable
    override fun RenderViewItemManager(
        transaction: TransRsUIModel,
        onAction: (ManagerAction) -> Unit,
    ) {
        val amountDesc = StringBuilder()
        try {
            transaction.amount?.getAmountServer()?.let {
                if (it.isNotEmpty()) {
                    amountDesc.append(
                        Utils.g()
                            .getDotMoneyHasCcy(transaction.amount ?: "", transaction.currency ?: ""),
                    )
                }
            }
            transaction.amount2?.getAmountServer()?.let {
                if (it.isNotEmpty() && "0" != it) {
                    amountDesc.append(
                        "\n${
                            Utils.g().getDotMoneyHasCcy(
                                transaction.amount2 ?: "",
                                transaction.currency2 ?: "",
                            )
                        }",
                    )
                }
            }

            transaction.amount3?.getAmountServer()?.let {
                if (it.isNotEmpty() && "0" != it) {
                    amountDesc.append(
                        "\n${
                            Utils.g().getDotMoneyHasCcy(
                                transaction.amount3 ?: "",
                                transaction.currency3 ?: "",
                            )
                        }",
                    )
                }
            }
        } catch (_: Exception) {
        }

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White)
                .padding(12.dp)
                .safeClickable {
                    onAction.invoke(
                        ManagerAction.OnTransaction(
                            ManagerViewModel.CLICK_DETAIL,
                            transaction,
                        ),
                    )
                },
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
            ) {
                BaseText(
                    text = transaction.serviceName ?: "",
                    fontCus = 4,
                    color = AppColors.blue02,
                    textSize = 16.sp,
                    modifier = Modifier.weight(1f),
                )

                BaseText(
                    modifier = Modifier.padding(start = 16.dp),
                    text = amountDesc.toString(),
                    fontCus = 5,
                    color = AppColors.primaryRed,
                    textSize = 14.sp,
                    rightDrawable = R.drawable.ic_more,
                    rightDrawableSize = 24.dp,
                    textAlign = TextAlign.End,
                )
            }

            Spacer(
                modifier = Modifier
                    .padding(vertical = 16.dp)
                    .height(1.dp)
                    .fillMaxWidth()
                    .background(AppColors.blue09),
            )

            InfoHorizontalView(
                title = "Số giao dịch",
                value = transaction.mtId ?: "",
            )

            val typeChangeDesc = StringBuilder()
            if (true == transaction.typeChange?.contains("1")) {
                typeChangeDesc.append("\nThời gian hiệu lực của bảo lãnh")
            }
            if (true == transaction.typeChange?.contains("2")) {
                typeChangeDesc.append("\nSố tiền bảo lãnh")
            }
            if (true == transaction.typeChange?.contains("3")) {
                typeChangeDesc.append("\nCác nội dung khác")
            }
            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Loại sửa đổi",
                value = typeChangeDesc.toString().replaceFirst("\n", ""),
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Bên nhận bảo lãnh",
                value = transaction.addressOfReceiver ?: "",
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Người tạo",
                value = transaction.creator ?: "",
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Trạng thái",
                value = transaction.statusName ?: "",
                valueColor = AppColors.blue02,
            )
        }
    }
}