package com.vietinbank.feature_transaction_manage.fragment.inquiry_approver_list

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.manage.UsersListDomains
import com.vietinbank.feature_transaction_manage.databinding.ItemUserInquiryBinding

class InquiryApproverListAdapter : RecyclerView.Adapter<InquiryApproverListAdapter.Holder>() {
    private var dataSet: MutableList<UsersListDomains> = mutableListOf()
    var context: Context? = null
    var currency: String? = "VND"

    @SuppressLint("NotifyDataSetChanged")
    var onClickItem: ((UsersListDomains) -> Unit)? = null

    @SuppressLint("NotifyDataSetChanged")
    fun setData(data: MutableList<UsersListDomains>) {
        dataSet.clear()
        dataSet.addAll(data)
        notifyDataSetChanged()
    }

    fun setCCY(currency: String) {
        this.currency = currency
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): Holder {
        val binding = ItemUserInquiryBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false,
        )
        return Holder(binding)
    }

    inner class Holder(var binding: ItemUserInquiryBinding) : RecyclerView.ViewHolder(binding.root)

    override fun onBindViewHolder(holder: Holder, position: Int) {
        val item = dataSet[position]
        with(holder.binding) {
            tvUserName.text = item.userName ?: ""
            tvApprovalLevel.text = "Phê duyệt cấp " + (item.approvalLevel ?: "")
            contentTransLimit.text = Utils.g().getDotMoneyHasCcy(item.transLimit ?: "", currency ?: "")
            contentDailyLimit.text = Utils.g().getDotMoneyHasCcy(item.dailyLimit ?: "", currency ?: "")
            root.setThrottleClickListener {
                dataSet[position].let { it1 -> onClickItem?.invoke(it1) }
            }
        }
    }

    override fun getItemCount(): Int = dataSet.size
}
