package com.vietinbank.feature_transaction_manage.fragment.render

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.manage.TransDetailDomain
import com.vietinbank.core_domain.models.manage.TransRsUIModel
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.base.compose.InfoHorizontalView
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_transaction_manage.ManagerAction
import com.vietinbank.feature_transaction_manage.ManagerViewModel
import javax.inject.Inject

class FileNSNNManagerImpl @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : IRenderManager {
    @Composable
    override fun RenderViewDetail(
        transaction: TransDetailDomain?,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        if (transaction != null) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                    .padding(16.dp),
            ) {
                Text(
                    text = "Thông tin chung",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số file ID",
                        value = transaction.mtId ?: "",
                    )
                    transaction.activityLogs?.createdBy?.let {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Người khởi tạo",
                            value = "${it.username} - ${it.processDate}",
                        )
                    }
                    if (transaction.countSuccessTransaction != 0) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Giao dịch thành công",
                            value = transaction.countSuccessTransaction.toString() ?: "",
                        )
                    }
                    if (transaction.countPendingTransaction != 0) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Giao dịch chờ duyệt",
                            value = transaction.countPendingTransaction.toString() ?: "",
                        )
                    }
                    if (transaction.countErrTransaction != 0) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Giao dịch từ chối",
                            value = transaction.countErrTransaction.toString() ?: "",
                        )
                    }
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "File đính kèm",
                        value = transaction.fileName ?: "",
                    )
                }

                Text(
                    text = "Tổng số giao dịch: " + transaction.countTransaction.toString(), // + transaction.countTransaction,
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    modifier = Modifier.padding(vertical = 12.dp),
                )

                // Display child transactions if they exist
                if (transaction.subTranItemList?.isNotEmpty() == true) {
                    // Display each child transaction with circular indicator
                    transaction.subTranItemList!!.forEachIndexed { index, subTransaction ->

                        Column(
                            modifier = Modifier
                                .fillMaxSize()
                                .clip(RoundedCornerShape(4.dp))
                                .background(Color.White)
                                .padding(vertical = 20.dp, horizontal = 16.dp),
                        ) {
                            InfoHorizontalView(
                                title = "Số giao dịch",
                                value = subTransaction.mtId ?: "",
                            )

                            InfoHorizontalView(
                                modifier = Modifier.padding(top = 14.dp),
                                title = "Loại giao dịch",
                                value = subTransaction.tranTypeName ?: "",
                            )

                            InfoHorizontalView(
                                modifier = Modifier.padding(top = 14.dp),
                                title = "Trạng thái",
                                value = subTransaction.statusName ?: "",
                            )

                            InfoHorizontalView(
                                modifier = Modifier.padding(top = 14.dp),
                                title = "Số tiền giao dịch",
                                value = Utils.g().getDotMoneyHasCcy(
                                    subTransaction.amount ?: "",
                                    transaction.currency ?: "",
                                ),
                                valueFont = 5,
                                valueColor = AppColors.blue02,
                                valueTwo = moneyHelper.convertAmountToWords(
                                    subTransaction.amount ?: "",
                                    transaction.currency,
                                ),
                            )

                            InfoHorizontalView(
                                modifier = Modifier.padding(top = 14.dp),
                                title = "Tài khoản chuyển",
                                value = (
                                    subTransaction.fromAccountNo
                                        ?: ""
                                    ) + " - " + (subTransaction.fromAccountName ?: ""),
                            )

                            InfoHorizontalView(
                                modifier = Modifier.padding(top = 14.dp),
                                title = "Đơn vị nộp thuế",
                                value = (
                                    subTransaction.payName
                                        ?: ""
                                    ) + " - " + (subTransaction.payCode ?: ""),
                            )
                        }
                        if (index < transaction.subTranItemList!!.size - 1) {
                            Spacer(modifier = Modifier.height(16.dp))
                        }
                    }
                }
            }
        }
    }

    @Composable
    override fun RenderViewItemManager(
        transaction: TransRsUIModel,
        onAction: (ManagerAction) -> Unit,
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White)
                .padding(12.dp)
                .safeClickable {
                    onAction.invoke(
                        ManagerAction.OnTransaction(
                            ManagerViewModel.CLICK_DETAIL,
                            transaction,
                        ),
                    )
                },
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
            ) {
                BaseText(
                    text = transaction.fileName ?: "",
                    fontCus = 4,
                    color = AppColors.blue02,
                    textSize = 16.sp,
                    modifier = Modifier.weight(1f),
                )

                BaseText(
                    modifier = Modifier.padding(start = 16.dp),
                    text = Utils.getDotMoneyHasCcy(
                        transaction.total ?: "",
                        transaction.currency ?: "",
                    ),
                    fontCus = 5,
                    color = AppColors.primaryRed,
                    textSize = 14.sp,
                    rightDrawable = R.drawable.ic_more,
                    rightDrawableSize = 24.dp,
                )
            }

            Spacer(
                modifier = Modifier
                    .padding(vertical = 16.dp)
                    .height(1.dp)
                    .fillMaxWidth()
                    .background(AppColors.blue09),
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Số file ID",
                value = transaction.mtId.orEmpty(),
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Ngày tạo",
                value = transaction.createdDate.orEmpty(),
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Số lượng giao dịch",
                value = transaction.countTransaction.toString().orEmpty(),
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Trạng thái",
                value = transaction.statusName.orEmpty(),
                valueColor = AppColors.blue02,
            )
        }
    }
}