package com.vietinbank.feature_transaction_manage.fragment

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.core.net.toUri
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.manage.TransRsUIModel
import com.vietinbank.core_domain.models.manage.TransactionDetailActions
import com.vietinbank.core_domain.navigation.IConfirmCheckerLauncher
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_transaction_manage.DetailManagerViewModel
import com.vietinbank.feature_transaction_manage.bottomSheet.SelectTransactionSheet
import com.vietinbank.feature_transaction_manage.di.IMTransactionNavigator
import com.vietinbank.feature_transaction_manage.screen.MDetailScreen
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class MTransactionDetailFragment : BaseFragment<DetailManagerViewModel>() {

    override val viewModel: DetailManagerViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    lateinit var confirmLauncher: IConfirmCheckerLauncher

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var transNavigator: IMTransactionNavigator

    @Composable
    override fun ComposeScreen() {
        val actions = TransactionDetailActions(
            onHomeClick = { appNavigator.goToHome() },
            onBackClick = { transNavigator.popBackStack() },
            onCheckApprovedClick = {
                // kiem tra nguoi phe duyet
                val bundle = Bundle()
                if (viewModel.transRsUICopyModel != null) {
                    viewModel.transRsUICopyModel.let { item ->
                        bundle.putString(Tags.TRANSACTION_BUNDLE, item?.mtId ?: "")
                        bundle.putString(Tags.ACCOUNT_NUMBER_BUNDLE, item?.fromAccountNo ?: "")
                        bundle.putString(Tags.SERVICE_ID_BUNDLE, item?.tranType ?: "")
                        bundle.putString(Tags.CURRENCY_BUNDLE, item?.currency ?: "")
                    }
                    appNavigator.goToinquiryApproverListFragment(bundle)
                }
            },
            onLookupClick = { mtId ->
                // tra soat giao dich - maker
                val bundle = Bundle()
                bundle.putString(Tags.MTID_STRING_BUNDLE, mtId)
                appNavigator.goToTracePaymentFragment(bundle)
            },
            onCopyClick = { item ->
                // Sao chep giao dich
                requireActivity().viewModelStore.clear()
                val bundle = Bundle()
                viewModel.convertToNapasTransfer(item)?.let { napasParam ->
                    bundle.putString(
                        Tags.COPY_TRANSFER_OBJECT_BUNDLE,
                        Utils.g().provideGson().toJson(napasParam),
                    )
                }

                when (item.tranType) {
                    Tags.TransferType.TYPE_PAYMENT_ORDER_TRANSFER -> {
                    }

                    else -> {
                        appNavigator.goToViewPagerTransferFragment(bundle)
                    }
                }
            },
            onDeleteClick = {
                // xoa  điện
            },
            onUncClick = {
                // tai unc
            },
            onUncCKSClick = {
                // tai unc có cks
            },
            onBM01Click = { item ->
                viewModel.handleDownloadAction(
                    mtId = item.mtId.toString(),
                    tranType = item.tranType.toString(),
                    type = Tags.DOWNLOAD_FILE_TYPE_BM,
                    signType = Tags.DOWNLOAD_FILE_SIGN_TYPE_BM,
                )
            },
            onC102Click = { item ->
                viewModel.handleDownloadAction(
                    mtId = item.mtId.toString(),
                    tranType = item.tranType.toString(),
                    type = Tags.DOWNLOAD_FILE_TYPE_CM,
                    signType = Tags.DOWNLOAD_FILE_SIGN_TYPE_BM,
                )
            },
            onC102COCKSClick = { item ->
                viewModel.handleDownloadAction(
                    mtId = item.mtId.toString(),
                    tranType = item.tranType.toString(),
                    type = Tags.DOWNLOAD_FILE_TYPE_CM,
                    signType = Tags.DOWNLOAD_FILE_SIGN_TYPE_CM,
                )
            },
        )

        AppTheme { MDetailScreen(viewModel, actions) }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        // Lấy dữ liệu transaction từ Bundle
        try {
            viewModel.transRsUICopyModel = Utils.g().provideGson().fromJson(
                arguments?.getString(
                    Tags.TRANSFER_OBJECT_BUNDLE, "",
                ),
                TransRsUIModel::class.java,
            )
            if (viewModel.transRsUICopyModel != null) {
                viewModel.getDetailTransaction()
            }
        } catch (e: Exception) {
            printLog("Lỗi parse transaction JSON: ${e.message}")
            e.printStackTrace()
        }

        initObserver()
    }

    private fun initObserver() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.apply {
                    launch {
                        getDownloadFileID.collect { resource ->
                            if (resource != null && resource is Resource.Success) {
                                // Lấy downloadFileId từ kết quả
                                if (resource.data.downloadFileId.isNullOrEmpty()) {
                                    showNoticeDialog("Không tìm thấy file đính kèm hoặc file không khả dụng")
                                }
                            } else if (resource is Resource.Error) {
                                showNoticeDialog(resource.message ?: "Không thể tải file đính kèm")
                            }
                        }
                    }

                    launch {
                        fileUrlReady.collect { url ->
                            try {
                                // Mở URL trong trình duyệt
                                val intent = Intent(Intent.ACTION_VIEW, url.toUri())
                                startActivity(intent)
                            } catch (e: Exception) {
                                showNoticeDialog("Không thể mở file: ${e.message}")
                            }
                        }
                    }
                    launch {
                        openFileIntent.collect { intent ->
                            startActivity(intent)
                        }
                    }
                    launch {
                        errorMessage.collect { message ->
                            showNoticeDialog(message)
                        }
                    }
                    launch {
                        fileDownloadEvent.collect { event ->
                            when (event) {
                                is DetailManagerViewModel.FileDownloadEvent.ShowSelectTransactionSheet -> {
                                    SelectTransactionSheet(event.mtIdList, event.title) { selectedTran ->
                                        viewModel.getDownloadBase64File(
                                            requireContext(),
                                            selectedTran,
                                            event.type,
                                            event.signType,
                                        )
                                    }.show(childFragmentManager, "SelectTransactionSheet")
                                }

                                is DetailManagerViewModel.FileDownloadEvent.DownloadDirect -> {
                                    viewModel.getDownloadBase64File(
                                        requireContext(),
                                        event.mtId,
                                        event.type,
                                        event.signType,
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}