package com.vietinbank.feature_transaction_manage.fragment.render

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.manage.ItemSubTransactionDetailMangerDomain
import com.vietinbank.core_domain.models.manage.TransDetailDomain
import com.vietinbank.core_domain.models.manage.TransRsUIModel
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.base.compose.InfoHorizontalView
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_transaction_manage.ManagerAction
import com.vietinbank.feature_transaction_manage.ManagerViewModel
import javax.inject.Inject

class CustomsInlandManagerImpl @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : IRenderManager {
    @Composable
    override fun RenderViewDetail(
        transaction: TransDetailDomain?,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        if (transaction != null) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                    .padding(16.dp),
            ) {
                Text(
                    text = "Thông tin chung",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(title = "Loại giao dich", value = transaction.tranTypeName)
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số giao dịch",
                        value = transaction.mtId ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Trạng thái",
                        value = transaction.statusName,
                        valueColor = AppColors.blue02,
                    )
                    transaction.activityLogs?.createdBy?.let {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Người khởi tạo",
                            value = "${it.username} - ${it.processDate}",
                        )
                    }

                    var verifyUser = ""
                    transaction.activityLogs?.verifiedBy?.forEachIndexed { index, item ->
                        verifyUser += "${item.username ?: ""} - ${item.processDate}"
                        if (index < (transaction.activityLogs?.verifiedBy?.size ?: 0) - 1) {
                            verifyUser += "\n"
                        }
                    }
                    if (!verifyUser.isEmpty()) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Người phê duyệt",
                            value = verifyUser,
                        )
                    }
                }

                if ("01" == transaction.status) {
                    Text(
                        text = "Nội dung từ chối",
                        style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                        modifier = Modifier.padding(vertical = 12.dp),
                    )

                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .clip(RoundedCornerShape(4.dp))
                            .background(Color.White)
                            .padding(vertical = 20.dp, horizontal = 16.dp),
                    ) {
                        Text(
                            text = transaction.rejectReason ?: "",
                            style = getComposeFont(
                                2,
                                14.sp,
                                AppColors.blue07,
                            ),
                            modifier = Modifier.padding(vertical = 14.dp),
                        )
                    }
                }

                Text(
                    text = "Thông tin người nộp thuế",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    modifier = Modifier.padding(vertical = 12.dp),
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        title = "Hình thức",
                        value = transaction.taxMethodName ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Tên đơn vị nộp",
                        value = transaction.payname ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Mã số thuế",
                        value = transaction.paycode ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Địa chỉ",
                        value = transaction.payadd ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Từ tài khoản",
                        value = transaction.fromAccountNo,
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Ngân hàng",
                        value = transaction.branchName ?: "",
                    )
                }

                Text(
                    text = "Thông tin cơ quan thu",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    modifier = Modifier.padding(vertical = 12.dp),
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        title = "Tỉnh/Thành phố",
                        value = "${transaction.provinceCode ?: ""} - ${transaction.provinceName ?: ""}",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Địa bàn hành chính",
                        value = "${transaction.areaCode ?: ""} - ${transaction.areaName ?: ""}",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Mã kho bạc nhà nước",
                        value = "${transaction.treasuryCode ?: ""} - ${transaction.treasuryName ?: ""}",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Tài khoản ghi thu",
                        value = "${transaction.collectionAccountNo ?: ""} - ${transaction.collectionAccountName ?: ""}",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Mã cơ quan quản lý thu",
                        value = "${transaction.collectAgencyCode ?: ""} - ${transaction.collectAgencyName ?: ""}",
                    )

                    if ("04" == transaction.taxType) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Mã chi cục HQ",
                            value = "${transaction.bucode ?: ""} - ${transaction.buname ?: ""}",
                        )

                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Mã HQ phát hành",
                            value = "${transaction.oficode ?: ""} - ${transaction.ofiname ?: ""}",
                        )
                    }

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Mã chương",
                        value = "${transaction.chapCode ?: ""} - ${transaction.chapName ?: ""}",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số tờ khai/QĐ/thông báo",
                        value = transaction.declareNumber ?: "",
                    )

                    if ("04" == transaction.taxType) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Ngày tờ khai",
                            value = transaction.declareDate ?: "",
                        )

                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Loại hình SNK",
                            value = "${transaction.ieType ?: ""} - ${transaction.ieName ?: ""}",
                        )
                    }
                }

                Text(
                    text = "Thông tin chi tiết nộp NSNN",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    modifier = Modifier.padding(vertical = 12.dp),
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Tổng số tiền thực nộp",
                        value = Utils.g().getDotMoneyHasCcy(
                            transaction.amount ?: "",
                            transaction.currency ?: "",
                        ),
                        valueFont = 5,
                        valueColor = AppColors.blue02,
                        valueTwo = moneyHelper.convertAmountToWords(
                            transaction.amount ?: "",
                            transaction.currency,
                        ),
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Phí giao dịch",
                        value = Utils.g().getDotMoneyHasCcy(
                            transaction.feeAmount ?: "",
                            transaction.currency ?: "",
                        ),
                    )

                    if (!transaction.items.isNullOrEmpty()) {
                        if (transaction.items?.size!! > 1) {
                            Spacer(modifier = Modifier.height(16.dp))
                            DashedDividerQLGD()
                            Spacer(modifier = Modifier.height(16.dp))
                        }

                        // Duyệt danh sách động từ API
                        // Loop qua danh sách chi tiết
                        transaction.items?.forEachIndexed { index, item ->
                            TaxDetailSection(item)

                            // Chỉ hiển thị Divider giữa các item nếu có nhiều hơn 1 item
                            if (transaction.items?.size!! > 1 && index < transaction.items?.lastIndex!!) {
                                Spacer(modifier = Modifier.height(16.dp))
                                DashedDividerQLGD()
                                Spacer(modifier = Modifier.height(16.dp))
                            }
                        }
                    }
                }
            }
        }
    }

    @Composable
    fun TaxDetailSection(item: ItemSubTransactionDetailMangerDomain) {
        InfoHorizontalView(
            modifier = Modifier.padding(top = 14.dp),
            title = "Nội dung nộp thuế",
            value = item.content.orEmpty(),
            valueColor = AppColors.blue02,
        )
        InfoHorizontalView(
            modifier = Modifier.padding(top = 14.dp),
            title = "Mã NDKT (TM)",
            value = item.businessCode.orEmpty(),
            valueColor = AppColors.blue02,
        )
        InfoHorizontalView(
            modifier = Modifier.padding(top = 14.dp),
            title = "Kỳ thuế",
            value = item.taxPeriod.orEmpty(),
            valueColor = AppColors.blue02,
        )
        InfoHorizontalView(
            modifier = Modifier.padding(top = 14.dp),
            title = "Số tiền",
            value = Utils.g().getDotMoneyHasCcy(
                item.amount ?: "",
                item.currency ?: "",
            ),
            valueColor = AppColors.blue02,
        )
    }

    @Composable
    override fun RenderViewItemManager(
        transaction: TransRsUIModel,
        onAction: (ManagerAction) -> Unit,
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White)
                .padding(12.dp)
                .safeClickable {
                    onAction.invoke(
                        ManagerAction.OnTransaction(
                            ManagerViewModel.CLICK_DETAIL,
                            transaction,
                        ),
                    )
                },
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
            ) {
                BaseText(
                    text = transaction.tranTypeName ?: "",
                    fontCus = 4,
                    color = AppColors.blue02,
                    textSize = 16.sp,
                    modifier = Modifier.weight(1f),
                )

                BaseText(
                    modifier = Modifier.padding(start = 16.dp),
                    text = Utils.getDotMoneyHasCcy(
                        transaction.amount ?: "",
                        transaction.currency ?: "",
                    ),
                    fontCus = 5,
                    color = AppColors.primaryRed,
                    textSize = 14.sp,
                    rightDrawable = R.drawable.ic_more,
                    rightDrawableSize = 24.dp,
                )
            }

            Spacer(
                modifier = Modifier
                    .padding(vertical = 16.dp)
                    .height(1.dp)
                    .fillMaxWidth()
                    .background(AppColors.blue09),
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Số tờ khai/QĐ/TB",
                value = transaction.treasuryCode.orEmpty(),
            )

            InfoHorizontalView(
                title = "Số giao dịch",
                value = transaction.mtId.orEmpty(),
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Đơn vị nộp thuế",
                value = transaction.payname.orEmpty(),
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Mã KBNN",
                value = transaction.treasuryCode.orEmpty() + "-" + transaction.tranTypeName.orEmpty(),
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Ngày tạo",
                value = transaction.createdDate.orEmpty(),
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Trạng thái",
                value = transaction.statusName.orEmpty(),
                valueColor = AppColors.blue02,
            )
        }
    }

    @Composable
    fun DashedDividerQLGD(
        color: Color = Color.Gray,
        thickness: Dp = 1.dp,
        dashWidth: Dp = 6.dp,
        gapWidth: Dp = 4.dp,
    ) {
        // Lấy Density từ Composable scope
        val density = LocalDensity.current

        // Dùng remember để cache giá trị px
        val dashPx = remember(dashWidth, density) { with(density) { dashWidth.toPx() } }
        val gapPx = remember(gapWidth, density) { with(density) { gapWidth.toPx() } }
        val thicknessPx = remember(thickness, density) { with(density) { thickness.toPx() } }

        Canvas(
            modifier = Modifier
                .fillMaxWidth()
                .height(thickness),
        ) {
            val totalWidth = size.width
            var currentX = 0f

            while (currentX < totalWidth) {
                drawLine(
                    color = color,
                    start = Offset(currentX, 0f),
                    end = Offset(currentX + dashPx, 0f),
                    strokeWidth = thicknessPx,
                )
                currentX += dashPx + gapPx
            }
        }
    }
}