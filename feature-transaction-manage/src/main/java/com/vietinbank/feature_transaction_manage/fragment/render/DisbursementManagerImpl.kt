package com.vietinbank.feature_transaction_manage.fragment.render

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_common.extensions.getAmountServer
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.manage.TransDetailDomain
import com.vietinbank.core_domain.models.manage.TransRsUIModel
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.base.compose.InfoHorizontalView
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_transaction_manage.ManagerAction
import com.vietinbank.feature_transaction_manage.ManagerViewModel
import javax.inject.Inject

class DisbursementManagerImpl @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : IRenderManager {
    @Composable
    override fun RenderViewDetail(
        transaction: TransDetailDomain?,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        if (transaction != null) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                    .padding(16.dp),
            ) {
                Text(
                    text = "Thông tin chung",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                )
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        title = "Loại giao dịch",
                        value = transaction.tranTypeName ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Mục đích giải ngân",
                        value = when (transaction.disbursementPurposeCode) {
                            "1" -> "Thanh toán trong nước VNĐ"
                            "2" -> "Thanh toán chi lương"
                            "3" -> "Thanh toán bù đắp cho TK khách hàng"
                            "4" -> "Thanh toán Ngân sách nhà nước/ nghĩa vụ khác"
                            "5" -> "Chuyển tiền biên mậu CNY/Chuyển tiền ngoại tệ trong Vietinbank"
                            "6" -> "Thanh toán TT chuyển tiền ngoại tệ ngoài VTB"
                            else -> ""
                        }, // transaction.disbursementPurposeName ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số giao dịch",
                        value = transaction.mtId ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Trạng thái",
                        value = transaction.statusNameBill ?: "",
                    )
                    transaction.activityLogs?.createdBy?.let {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Người tạo",
                            value = "${it.username} - ${it.processDate}",
                        )
                    }
                    var verifyUser = ""
                    transaction.activityLogs?.verifiedBy?.forEachIndexed { index, item ->
                        verifyUser += "${item.username ?: ""} - ${item.processDate}"
                        if (index < (transaction.activityLogs?.verifiedBy?.size ?: 0) - 1) {
                            verifyUser += "\n"
                        }
                    }
                    if (!verifyUser.isEmpty()) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Người phê duyệt",
                            value = verifyUser,
                        )
                    }
                    transaction.rejectReason?.let {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Lý do",
                            value = it,
                        )
                    }
                }
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "Thông tin khách hàng",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                )
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        title = when (transaction.subType) {
                            "EXT" -> "Tên doanh nghiệp"
                            else -> "Tên công ty"
                        },
                        value = transaction.companyName ?: "",
                    )
                    if ("EXT" == transaction.subType) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Tên, địa chỉ (Tiếng Anh)",
                            value = transaction.companyAddress ?: "",
                        )
                    }

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Mã số thuế doanh nghiệp",
                        value = transaction.companyCode ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Tên người đại diện/Ủy quyền",
                        value = transaction.representativeName ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Ủy quyền",
                        value = if ("Y" == transaction.authority) "Có" else "Không",
                    )
                    if ("Y" == transaction.authority) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Thông tin ủy quyền",
                            value = transaction.authorityNumber ?: "",
                        )
                    }
                }
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "Thông tin hợp đồng vay",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                )
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        title = "Số tiền cam kết/hạn mức",
                        value = Utils.g().getDotMoneyHasCcy(
                            transaction.loanLimit ?: "",
                            transaction.currencyCode ?: "",
                        ),
                        valueTwo = moneyHelper.convertAmountToWords(
                            transaction.loanLimit?.getAmountServer() ?: "",
                            transaction.currencyCode,
                        ),
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = when (transaction.subType) {
                            "EXT" -> "Số tiền đã giải ngân - Dư nợ gốc"
                            else -> "Dư nợ gốc"
                        },
                        value = Utils.g().getDotMoneyHasCcy(
                            transaction.disbursedAmount ?: "",
                            transaction.currencyCode ?: "",
                        ),
                        valueTwo = moneyHelper.convertAmountToWords(
                            transaction.disbursedAmount?.getAmountServer() ?: "",
                            transaction.currencyCode,
                        ),
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số hợp đồng đã ký",
                        value = transaction.contractNo ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Ngày hợp đồng",
                        value = transaction.contractDate ?: "",
                    )
                }

                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "Thông tin đề nghị vay vốn",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                )
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        title = "Chi nhánh giải ngân",
                        value = "${transaction.disbursedBranchCode ?: ""} - ${transaction.disbursedBranchName ?: ""}",
                    )
                    val amountLoan = when (transaction.subType) {
                        "EXT" -> transaction.transferLoanAmountCal ?: ""
                        else -> transaction.amountProposed ?: ""
                    }
                    val currencyLoan = when (transaction.subType) {
                        "EXT" -> transaction.transferLoanCurType ?: ""
                        else -> transaction.currencyCode ?: ""
                    }
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = when (transaction.subType) {
                            "EXT" -> "Số tiền vay"
                            else -> "Số tiền đề nghị giải ngân"
                        },
                        value = Utils.g().getDotMoneyHasCcy(amountLoan, currencyLoan),
                        valueTwo = moneyHelper.convertAmountToWords(
                            amountLoan.getAmountServer(),
                            currencyLoan,
                        ),
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Lãi suất vay",
                        value = (transaction.loanRate ?: "").plus("%/năm"),
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Ngày giải ngân",
                        value = transaction.disbursementDate ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Thời hạn trả nợ",
                        value = transaction.loanTerm ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Mục địch sử dụng khoản nợ",
                        value = transaction.purpose ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Ngày trả nợ",
                        value = transaction.dueDate ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Trả nợ tự động",
                        value = if ("N" == transaction.autoRepayment) "Không" else "Có",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số TK thanh toán",
                        value = transaction.accountId ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Ngày trả nợ đầu tiên",
                        value = transaction.firstPaymentDate ?: "",
                    )
                }

                when (transaction.subType) {
                    "EXT" -> {
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "Thông tin chuyển tiền ngoại tệ",
                            style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                        )
                        Column(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(top = 12.dp)
                                .clip(RoundedCornerShape(4.dp))
                                .background(Color.White)
                                .padding(vertical = 20.dp, horizontal = 16.dp),
                        ) {
                            InfoHorizontalView(
                                title = "Số tiền chuyển",
                                value = Utils.g().getDotMoneyHasCcy(
                                    transaction.transferAmount ?: "",
                                    transaction.transferCurType ?: "",
                                ),
                                valueTwo = moneyHelper.convertAmountToWords(
                                    transaction.transferAmount?.getAmountServer() ?: "",
                                    transaction.transferCurType ?: "",
                                ),
                            )
                            InfoHorizontalView(
                                modifier = Modifier.padding(top = 14.dp),
                                title = "Số tiền vay theo loại chuyển tiền",
                                value = Utils.g().getDotMoneyHasCcy(
                                    transaction.transferLoanAmount ?: "",
                                    transaction.transferLoanCurType ?: "",
                                ),
                                valueTwo = moneyHelper.convertAmountToWords(
                                    transaction.transferLoanAmount?.getAmountServer() ?: "",
                                    transaction.transferLoanCurType ?: "",
                                ),
                            )
                            if (!transaction.transferCurType.isNullOrEmpty() && transaction.transferCurType != transaction.transferLoanCurType) {
                                // phat sinh mua ban nt
                                InfoHorizontalView(
                                    modifier = Modifier.padding(top = 14.dp),
                                    title = "Loại tỷ giá",
                                    value = transaction.exchangeRateTypeDesc ?: "",
                                )

                                InfoHorizontalView(
                                    modifier = Modifier.padding(top = 14.dp),
                                    title = "Tỷ giá",
                                    value = Utils.g().getDotMoney(transaction.exchangeRate ?: ""),
                                )
                            }

                            InfoHorizontalView(
                                modifier = Modifier.padding(top = 14.dp),
                                title = "Tài khoản thu phí",
                                value = transaction.feeAccount ?: "",
                            )

                            if (1 == transaction.dataFx?.size) {
                                InfoHorizontalView(
                                    modifier = Modifier.padding(top = 14.dp),
                                    title = "Loại phí chuyển tiền",
                                    value = transaction.dataFx?.first()?.chargeMethod ?: "",
                                )
                                InfoHorizontalView(
                                    modifier = Modifier.padding(top = 14.dp),
                                    title = "Ngày hoàn chứng từ",
                                    value = transaction.dataFx?.first()?.lcReturnDate ?: "",
                                )
                            }
                        }
                        if (!transaction.dataFx.isNullOrEmpty()) {
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(
                                text = "Thông tin thụ hưởng",
                                style = getComposeFont(
                                    5,
                                    14.sp,
                                    AppColors.trenBgr,
                                ),
                            )
                            Column(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(top = 12.dp)
                                    .clip(RoundedCornerShape(4.dp))
                                    .background(Color.White)
                                    .padding(vertical = 20.dp, horizontal = 16.dp),
                            ) {
                                if (1 == transaction.dataFx?.size) {
                                    InfoHorizontalView(
                                        title = "Đơn vị thụ hưởng",
                                        value = "Một đơn vị thụ hưởng",
                                    )
                                    InfoHorizontalView(
                                        modifier = Modifier.padding(top = 14.dp),
                                        title = "Số tài khoản/IBAN người hưởng",
                                        value = transaction.dataFx?.first()?.receiveAccount ?: "",
                                    )
                                    InfoHorizontalView(
                                        modifier = Modifier.padding(top = 14.dp),
                                        title = "Tên/địa chỉ người hưởng",
                                        value = transaction.dataFx?.first()?.receiveInfo ?: "",
                                    )
                                    transaction.dataFx?.first()?.receiveBank?.let { item ->
                                        InfoHorizontalView(
                                            modifier = Modifier.padding(top = 14.dp),
                                            title = "Bin code NH hưởng",
                                            value = item,
                                        )
                                    }
                                    transaction.dataFx?.first()?.receiveBankName?.let { item ->
                                        InfoHorizontalView(
                                            modifier = Modifier.padding(top = 14.dp),
                                            title = "Tên/địa chỉ NH hưởng",
                                            value = item,
                                        )
                                    }
                                } else {
                                    InfoHorizontalView(
                                        title = "Đơn vị thụ hưởng",
                                        value = "Nhiều đơn vị thụ hưởng",
                                    )
                                    transaction.transferListFile?.forEach { file ->
                                        InfoHorizontalView(
                                            modifier = Modifier.padding(top = 14.dp),
                                            title = "Bảng kê chuyển tiền",
                                            value = "Xem chi tiết",
                                        ) {
                                            onFieldClick(
                                                TransactionFieldEvent.FileAttachmentClick(
                                                    file,
                                                ),
                                            )
                                        }
                                    }
                                }
                            }
                        }

                        if (!transaction.fileGNN.isNullOrEmpty()) {
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(
                                text = "Hồ sơ ký số",
                                style = getComposeFont(
                                    5,
                                    14.sp,
                                    AppColors.trenBgr,
                                ),
                            )
                            Column(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(top = 12.dp)
                                    .clip(RoundedCornerShape(4.dp))
                                    .background(Color.White)
                                    .padding(
                                        top = 6.dp,
                                        bottom = 20.dp,
                                        start = 16.dp,
                                        end = 16.dp,
                                    ),
                            ) {
                                transaction.fileGNN?.forEach { file ->
                                    InfoHorizontalView(
                                        modifier = Modifier.padding(top = 14.dp),
                                        title = when (file.attachmentType) {
                                            "P1" -> "Đề nghị kiêm mua CTNT"
                                            "D1" -> "Giấy nhận nợ"
                                            else -> ""
                                        },
                                        value = file.fileName ?: "",
                                        valueColor = AppColors.blue02,
                                    ) {
                                        onFieldClick(TransactionFieldEvent.FileAttachmentClick(file))
                                    }
                                }
                            }
                        }
                    }

                    "INT" -> {
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "Thông tin hồ sơ đính kèm",
                            style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                        )
                        Column(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(top = 12.dp)
                                .clip(RoundedCornerShape(4.dp))
                                .background(Color.White)
                                .padding(start = 16.dp, top = 6.dp, end = 16.dp, bottom = 20.dp),
                        ) {
                            transaction.transferListFileVND?.let { file ->
                                InfoHorizontalView(
                                    modifier = Modifier.padding(top = 14.dp),
                                    title = "Bảng kê hóa đơn/Hợp đồng",
                                    value = file.fileName ?: "",
                                    valueColor = AppColors.blue02,
                                ) { onFieldClick(TransactionFieldEvent.FileAttachmentClick(file)) }
                            }

                            if (!transaction.listDocumentFileVND.isNullOrEmpty()) {
                                InfoHorizontalView(
                                    modifier = Modifier.padding(top = 14.dp),
                                    title = "Hồ sơ CMMĐSDV",
                                    value = transaction.listDocumentFileVND?.first()?.fileName
                                        ?: "",
                                    valueColor = AppColors.blue02,
                                ) {
                                    onFieldClick(
                                        TransactionFieldEvent.FileAttachmentClick(
                                            transaction.listDocumentFileVND?.first()!!,
                                        ),
                                    )
                                }
                            }

                            transaction.fileGNN?.forEach { file ->
                                InfoHorizontalView(
                                    modifier = Modifier.padding(top = 14.dp),
                                    title = when (file.attachmentType) {
                                        "P1" -> "Đề nghị kiêm mua CTNT"
                                        "D1" -> "Giấy nhận nợ"
                                        else -> ""
                                    },
                                    value = file.fileName ?: "",
                                    valueColor = AppColors.blue02,
                                ) {
                                    onFieldClick(TransactionFieldEvent.FileAttachmentClick(file))
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    @Composable
    override fun RenderViewItemManager(
        transaction: TransRsUIModel,
        onAction: (ManagerAction) -> Unit,
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White)
                .padding(12.dp)
                .safeClickable {
                    onAction.invoke(
                        ManagerAction.OnTransaction(
                            ManagerViewModel.CLICK_DETAIL,
                            transaction,
                        ),
                    )
                },
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
            ) {
                BaseText(
                    text = transaction.tranTypeName ?: "",
                    fontCus = 4,
                    color = AppColors.blue02,
                    textSize = 16.sp,
                    modifier = Modifier.weight(1f),
                )

                BaseText(
                    modifier = Modifier.padding(start = 16.dp),
                    text = when (transaction.subType) {
                        "EXT" -> {
                            Utils.getDotMoneyHasCcy(
                                transaction.transferLoanAmountCal ?: "",
                                transaction.transferLoanCurType ?: "",
                            )
                        }

                        else -> {
                            Utils.getDotMoneyHasCcy(
                                transaction.amountProposed ?: "",
                                transaction.currencyCode ?: "",
                            )
                        }
                    },
                    fontCus = 5,
                    color = AppColors.primaryRed,
                    textSize = 14.sp,
                    rightDrawable = R.drawable.ic_more,
                    rightDrawableSize = 24.dp,
                )
            }

            Spacer(
                modifier = Modifier
                    .padding(vertical = 16.dp)
                    .height(1.dp)
                    .fillMaxWidth()
                    .background(AppColors.blue09),
            )

            InfoHorizontalView(
                title = "Số giao dịch",
                value = transaction.mtId ?: "",
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Ngày đề nghị",
                value = transaction.disbursementDate ?: "",
            )
            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Lãi suất vay",
                value = transaction.loanRate?.plus("%/năm"),
            )
            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Thời hạn trả nợ",
                value = transaction.loanTerm,
            )
            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Ngày tạo",
                value = transaction.createdDate,
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Trạng thái",
                value = transaction.statusNameBill ?: "",
                valueColor = AppColors.blue02,
            )
        }
    }
}
