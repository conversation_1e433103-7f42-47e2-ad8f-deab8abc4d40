package com.vietinbank.feature_transaction_manage.fragment.render

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.manage.TransDetailDomain
import com.vietinbank.core_domain.models.manage.TransRsUIModel
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.base.compose.InfoHorizontalView
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_transaction_manage.ManagerAction
import com.vietinbank.feature_transaction_manage.ManagerViewModel
import javax.inject.Inject

class SalaryManagerImpl @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : IRenderManager {
    @Composable
    override fun RenderViewDetail(
        detailModel: TransDetailDomain?,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        detailModel?.let { transaction ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                    .padding(16.dp),
            ) {
                Text(
                    text = "Thông tin chung",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(title = "Loại giao dich", value = transaction.tranTypeName)
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số giao dịch",
                        value = transaction.mtId ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Trạng thái",
                        value = transaction.statusName,
                        valueColor = AppColors.blue02,
                    )
                    transaction.activityLogs?.createdBy?.let {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Người khởi tạo",
                            value = "${it.username} - ${it.processDate}",
                        )
                    }

                    var verifyUser = ""
                    transaction.activityLogs?.verifiedBy?.forEachIndexed { index, item ->
                        verifyUser += "${item.username ?: ""} - ${item.processDate}"
                        if (index < (transaction.activityLogs?.verifiedBy?.size ?: 0) - 1) {
                            verifyUser += "\n"
                        }
                    }
                    if (!verifyUser.isEmpty()) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Người phê duyệt",
                            value = verifyUser,
                        )
                    }

                    transaction.rejectReason?.let {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Lý do",
                            value = it,
                        )
                    }
                }

                Text(
                    text = "Thông tin tài khoản chuyển",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    modifier = Modifier.padding(vertical = 12.dp),
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        title = "Từ tài khoản",
                        value = "${transaction.fromAccountNo} - VND - ${transaction.fromAccountName}",
                    )
                }

                Text(
                    text = "Thông tin chi tiết",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    modifier = Modifier.padding(vertical = 12.dp),
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số tiền",
                        value = Utils.g().getDotMoneyHasCcy(
                            transaction.amount ?: "",
                            transaction.debitCurrency ?: "",
                        ),
                        valueFont = 5,
                        valueColor = AppColors.blue02,
                        valueTwo = moneyHelper.convertAmountToWords(
                            transaction.amount ?: "",
                            transaction.debitCurrency,
                        ),
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Phí giao dịch",
                        value = Utils.g().getDotMoneyHasCcy(
                            transaction.feeAmount ?: "",
                            transaction.debitCurrency ?: "",
                        ),
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Nội dung",
                        value = transaction.remark ?: "",
                    )

                    transaction.listFile?.firstOrNull()?.let { file ->
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "File đính kèm",
                            value = file.fileName,
                            valueColor = AppColors.blue02,
                        ) {
                            onFieldClick(TransactionFieldEvent.FileAttachmentClick(file))
                        }
                    }

                    transaction.listFile2?.firstOrNull()?.let { file ->
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Hồ sơ đính kèm",
                            value = file.fileName,
                            valueColor = AppColors.blue02,
                        ) {
                            onFieldClick(TransactionFieldEvent.FileAttachmentClick(file))
                        }
                    }

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Thời gian chuyển",
                        value = transaction.createdDate ?: "",
                    )
                }
            }
        }
    }

    @Composable
    override fun RenderViewItemManager(
        transaction: TransRsUIModel,
        onAction: (ManagerAction) -> Unit,
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White)
                .padding(12.dp)
                .safeClickable {
                    onAction.invoke(
                        ManagerAction.OnTransaction(
                            ManagerViewModel.CLICK_DETAIL,
                            transaction,
                        ),
                    )
                },
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
            ) {
                BaseText(
                    text = transaction.tranTypeName ?: "",
                    fontCus = 4,
                    color = AppColors.blue02,
                    textSize = 16.sp,
                    modifier = Modifier.weight(1f),
                )

                BaseText(
                    modifier = Modifier.padding(start = 16.dp),
                    text = Utils.getDotMoneyHasCcy(
                        transaction.amount ?: "",
                        transaction.currency ?: "",
                    ),
                    fontCus = 5,
                    color = AppColors.primaryRed,
                    textSize = 14.sp,
                    rightDrawable = R.drawable.ic_more,
                    rightDrawableSize = 24.dp,
                )
            }

            Spacer(
                modifier = Modifier
                    .padding(vertical = 16.dp)
                    .height(1.dp)
                    .fillMaxWidth()
                    .background(AppColors.blue09),
            )

            InfoHorizontalView(
                title = "Số giao dịch",
                value = transaction.mtId,
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Nội dung",
                value = transaction.remark,
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Ngày tạo",
                value = transaction.createdDate,
            )

            InfoHorizontalView(
                modifier = Modifier.padding(top = 16.dp),
                title = "Trạng thái",
                value = transaction.statusName,
                valueColor = AppColors.blue02,
            )
        }
    }
}