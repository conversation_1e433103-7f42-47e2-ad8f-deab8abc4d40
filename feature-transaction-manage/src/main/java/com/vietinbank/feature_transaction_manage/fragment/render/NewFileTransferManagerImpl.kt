package com.vietinbank.feature_transaction_manage.fragment.render

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.manage.TransDetailDomain
import com.vietinbank.core_domain.models.manage.TransRsUIModel
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.text.foundation.FoundationIconText
import com.vietinbank.core_ui.components.text.foundation.FoundationRichText
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.components.text.foundation.appendBold
import com.vietinbank.core_ui.components.text.foundation.appendColored
import com.vietinbank.core_ui.models.IconConfig
import com.vietinbank.core_ui.models.IconPosition
import com.vietinbank.core_ui.utils.getColorStatus
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_transaction_manage.ManagerAction
import com.vietinbank.feature_transaction_manage.ManagerViewModel
import javax.inject.Inject
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

class NewFileTransferManagerImpl @Inject constructor(
    private val moneyHelper: MoneyHelper,
    private val resourceProvider: IResourceProvider,
    private val imageLoader: CoilImageLoader,
) : IRenderManager {
    @Composable
    override fun RenderViewDetail(
        transaction: TransDetailDomain?,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
    }

    @Composable
    override fun RenderViewItemManager(
        transaction: TransRsUIModel,
        onAction: (ManagerAction) -> Unit,
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FDS.Sizer.Gap.gap16)
                .safeClickable {
                    onAction.invoke(
                        ManagerAction.OnTransaction(
                            ManagerViewModel.CLICK_DETAIL,
                            transaction,
                        ),
                    )
                },
        ) {
            FoundationRichText(
                modifier = Modifier.weight(1f),
                style = FDS.Typography.captionCaptionL,
            ) {
                appendColored(
                    resourceProvider.getString(R.string.approve_file_id),
                    resourceProvider.getComposeColor(R.color.foundation_character_secondary),
                )
                append(" ")
                appendBold(
                    transaction.mtId ?: "",
                    resourceProvider.getComposeColor(R.color.foundation_character_primary),
                )
            }

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = FDS.Sizer.Gap.gap8)
                    .padding(horizontal = FDS.Sizer.Gap.gap24),
                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                FoundationIconText(
                    modifier = Modifier.weight(1f),
                    text = stringResource(R.string.approve_file_300),
                    icons = mapOf(
                        IconPosition.LEFT to IconConfig(
                            icon = R.drawable.ic_common_file_24,
                            size = FDS.Sizer.Icon.icon24,
                        ),
                    ),
                    style = FDS.Typography.captionCaptionLBold,
                    color = FDS.Colors.characterPrimary,
                )

                FoundationText(
                    text = "200,000,000 vnd",
                    style = FDS.Typography.captionCaptionLBold,
                    color = FDS.Colors.characterPrimary,
                )
            }

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FDS.Sizer.Gap.gap8),
                title = stringResource(R.string.approve_file_name),
                titleStyle = FDS.Typography.captionCaptionL,
                value = "abc.pdf",
                isValueUnderline = true,
                valueStyle = FDS.Typography.captionLSemibold,
                onClick = {
                    onAction.invoke(
                        ManagerAction.OnTransaction(
                            ManagerViewModel.CLICK_FILE,
                            transaction,
                        ),
                    )
                },
            )

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FDS.Sizer.Gap.gap8),
                title = stringResource(R.string.transaction_label_fee_method),
                value = "phí ngoài",
                titleStyle = FDS.Typography.captionCaptionL,
                valueStyle = FDS.Typography.captionLSemibold,
            )

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FDS.Sizer.Gap.gap8),
                title = stringResource(R.string.transaction_label_transfer_type),
                value = if (transaction.process_time.isNullOrEmpty()) {
                    stringResource(R.string.maker_transfer_dashboard_now)
                } else {
                    stringResource(R.string.maker_transfer_dashboard_schedule)
                },
                titleStyle = FDS.Typography.captionCaptionL,
                valueStyle = FDS.Typography.captionLSemibold,
            )

            if (!transaction.process_time.isNullOrEmpty()) {
                FoundationInfoHorizontal(
                    modifier = Modifier.padding(top = FDS.Sizer.Gap.gap8),
                    title = stringResource(R.string.manager_detail_appointment_date),
                    value = transaction.process_time ?: "",
                    titleStyle = FDS.Typography.captionCaptionL,
                    valueStyle = FDS.Typography.captionLSemibold,
                )
            }

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FDS.Sizer.Gap.gap8),
                title = stringResource(R.string.manager_detail_status),
                value = transaction.statusName ?: "",
                titleStyle = FDS.Typography.captionCaptionL,
                valueStyle = FDS.Typography.captionLSemibold,
                valueColor = transaction.statusCode.getColorStatus(),
            )
        }
    }
}