package com.vietinbank.feature_transaction_manage.fragment

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_transaction_manage.FilterManagerViewModel
import com.vietinbank.feature_transaction_manage.di.IMTransactionNavigator
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class MTransactionFilterFragment : BaseFragment<FilterManagerViewModel>() {

    override val viewModel: FilterManagerViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var transNavigator: IMTransactionNavigator

    @Inject
    lateinit var imageLoader: CoilImageLoader

    @Composable
    override fun ComposeScreen() {
        val filterState by viewModel.filterState.collectAsState()
        AppTheme {
//            FilterScreen(filterState, imageLoader) { action ->
//                when (action) {
//                    // filter
//                    is FilterAction.TapBack -> {
//                        appNavigator.popBackStack()
//                    }
//
//                    is FilterAction.TapReset -> {
//                        viewModel.onResetFilter()
//                    }
//
//                    is FilterAction.TapDatePicker -> {
//                        viewModel.onShowDatePicker(action.isShow)
//                    }
//
//                    is FilterAction.TapApply -> {
//                        viewModel.onValidateApplyFilter()?.let {
//                            showNoticeDialog(it)
//                            return@FilterScreen
//                        }
//                        // back ve man hinh truoc do voi filter moi
//                        printLog(
//                            "Tuna5",
//                            "${viewModel.getResultFilter()}",
//                        )
//                        appNavigator.setFragmentResult(FILTER_RESULT_KEY, viewModel.getResultFilter())
//                        appNavigator.popBackStack()
//                    }
//
//                    is FilterAction.TapFillSheet -> {
//                        viewModel.onChangeSheet(action.sheetType)
//                    }
//
//                    is FilterAction.TapOnFillData -> {
//                        viewModel.onChangeFillModel(action.data)
//                    }
//
//                    is FilterAction.TapChip -> {
//                        viewModel.onChangeDate(action.chipType, action.chipValue)
//                    }
//
//                    is FilterAction.TapInput -> {
//                        viewModel.onChangeInput(action.inputType, action.inputValue)
//                    }
//
//                    is FilterAction.TapRangeDate -> {
//                        viewModel.onChangeRangeDate(action.startDate, action.endDate)
//                    }
//
//                    is FilterAction.TapChangeFilter -> {
//                        viewModel.onChangeFilterAdvance()
//                    }
//
//                    else -> {
//                    }
//                }
//            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.getPreFilterTransaction()
        initObserver()
    }

    private fun initObserver() {
    }
}