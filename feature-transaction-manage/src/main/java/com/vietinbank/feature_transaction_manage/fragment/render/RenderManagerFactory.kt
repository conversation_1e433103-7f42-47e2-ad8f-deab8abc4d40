package com.vietinbank.feature_transaction_manage.fragment.render

import com.vietinbank.core_common.constants.Tags
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class RenderManagerFactory @Inject constructor(
    private val defaultManagerImpl: DefaultManagerImpl,
    private val disbursementManagerImpl: DisbursementManagerImpl,
    private val lookupManagerImpl: LookupManagerImpl,
    private val salaryManagerImpl: SalaryManagerImpl,
    private val guaranteeManagerImpl: GuaranteeManagerImpl,
    private val guaranteeAmendManagerImpl: GuaranteeAmendManagerImpl,
    private val guaranteeIssueManagerImpl: GuaranteeIssueManagerImpl,
    private val customsInlandManagerImpl: CustomsInlandManagerImpl,
    private val fileNSNNManagerImpl: FileNSNNManagerImpl,
    private val infrastructureManagerImpl: InfrastructureManagerImpl,
    private val transferForeignManagerImpl: TransferForeignManagerImpl,
    private val unLockUserManagerImpl: UnLockUserManagerImpl,
) {
    fun render(tranType: String?): IRenderManager = when (tranType?.uppercase() ?: "") {
        "IN", "OU", "NP", "SN" -> defaultManagerImpl
        Tags.TYPE_GROUP_LOOKUP -> lookupManagerImpl
        Tags.TYPE_DISBURSEMENT_ONLINE -> disbursementManagerImpl
        "SL", "SLO", "SX" -> salaryManagerImpl
        "GOR" -> guaranteeManagerImpl
        "GOC" -> guaranteeAmendManagerImpl
        "GO" -> guaranteeIssueManagerImpl
        "TX" -> customsInlandManagerImpl
        "BTX" -> fileNSNNManagerImpl
        "IF" -> infrastructureManagerImpl
        "FX", "FXR" -> transferForeignManagerImpl
        Tags.TYPE_GROUP_RESET_PASSWORD, Tags.TYPE_GROUP_UNLOCK_USER -> unLockUserManagerImpl
        else -> defaultManagerImpl
    }
}