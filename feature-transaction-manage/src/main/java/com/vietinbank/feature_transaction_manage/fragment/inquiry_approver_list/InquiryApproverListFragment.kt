package com.vietinbank.feature_transaction_manage.fragment.inquiry_approver_list

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.viewbinding.ViewBinding
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_transaction_manage.databinding.InquiryApproverFragmentBinding
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class InquiryApproverListFragment : BaseFragment<InquiryApproverListViewModel>() {
    override val viewModel: InquiryApproverListViewModel by viewModels()
    override val useCompose: Boolean = false

    @Inject
    override lateinit var appNavigator: IAppNavigator
    override fun inflateViewBinding(inflater: LayoutInflater, container: ViewGroup?): ViewBinding {
        return InquiryApproverFragmentBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initData()
        initListener()
    }

    private fun initData() {
        (binding as InquiryApproverFragmentBinding).apply {
            toolbar.tvTitleToolbar.text = "Danh sách cấp phê duyệt"
            viewModel.inquiryApproverState.observe(viewLifecycleOwner) { data ->
            }
        }
    }

    private fun initListener() {
        (binding as InquiryApproverFragmentBinding).apply {
            toolbar.btnBack.setThrottleClickListener {
                appNavigator.popBackStack()
            }
        }
    }

    private fun initView() {
        (binding as InquiryApproverFragmentBinding).apply {
            rcvInquiry.adapter = viewModel.inquiryApproverListAdapter
            viewModel.inquiryApproverListAdapter.setData(mutableListOf())
            viewModel.inquiryApproverListAdapter.setCCY(
                arguments?.getString(Tags.CURRENCY_BUNDLE) ?: "",
            )
            viewModel.getInquiryApproverState(
                mtId = arguments?.getString(Tags.TRANSACTION_BUNDLE) ?: "",
                accountNumber = arguments?.getString(Tags.ACCOUNT_NUMBER_BUNDLE) ?: "",
                serviceId = viewModel.getServiceID(
                    arguments?.getString(Tags.SERVICE_ID_BUNDLE) ?: "",
                ),
            )
        }
    }
}