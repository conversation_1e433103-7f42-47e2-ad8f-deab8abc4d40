pluginManagement {
    repositories {
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
    resolutionStrategy {
        eachPlugin {
            if (requested.id.id == "org.jetbrains.kotlin.android" ||
                    requested.id.id == "org.jetbrains.kotlin.jvm" ||
                    requested.id.id == "kotlin-android" ||
                    requested.id.id == "kotlin-kapt") {
                useVersion("2.0.21")
            }
        }
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        // Thêm local Maven repository từ thư mục maven-repo ở root project
        maven { url uri("${rootDir}/maven-repo") }
        // <PERSON><PERSON><PERSON> hình cũ - giữ lại để tương thích
        maven { url uri("${rootProject.projectDir}/local-repo/build/repo") }
        maven { url 'https://jitpack.io' }

    }
}

rootProject.name = "newcore"
include ':app'
include ':core-common'
include ':core-data'
include ':core-ott'
include ':feature-login'
include ':core-ui'
include ':core-domain'
include ':feature-home'
include ':local-repo'
include ':feature-maker'
include ':feature-checker'
include ':feature-soft'
include ':feature-transaction-manage'
include ':feature-account'
include ':feature_trace_payment'
include ':feature-smartCA'
include ':feature_report'
include ':feature-list-type-ott'
include ':feature-ekyc'
include ':idcard_document_capture_libs'
include ':liveness_libs'
include ':nfc_libs'
