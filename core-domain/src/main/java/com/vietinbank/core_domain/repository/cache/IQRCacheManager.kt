package com.vietinbank.core_domain.repository.cache

import com.vietinbank.core_domain.models.account.AccountSaveQR
import kotlinx.coroutines.flow.StateFlow

/**
 * Theo pattern ITransferCacheManager:
 * - <PERSON><PERSON> StateFlow để observe.
 * - <PERSON><PERSON> các hàm save/get/clear và clearAll.
 * - hasCachedData() tương tự Transfer.
 */
interface IQRCacheManager {
    /** Map<accountNo, AccountSaveQR> */
    val qrMapFlow: StateFlow<Map<String, AccountSaveQR>>

    fun saveQR(accountNo: String, data: AccountSaveQR)
    fun getQR(accountNo: String): AccountSaveQR?
    fun getAllQR(): Map<String, AccountSaveQR>

    fun clearQR(accountNo: String)
    fun clearAllQR()

    suspend fun warmUp()

    /** Kiểm tra trong lớp CacheManager có sẵn dữ liệu hay không (tương tự Transfer). */
    suspend fun hasCachedData(): <PERSON><PERSON><PERSON>
}