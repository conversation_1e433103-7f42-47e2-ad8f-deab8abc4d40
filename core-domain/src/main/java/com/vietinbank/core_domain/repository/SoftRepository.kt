package com.vietinbank.core_domain.repository

import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.soft.KeypassParams
import com.vietinbank.core_domain.models.soft.VBlockSoftOtpParams
import com.vietinbank.core_domain.models.soft.VGetSoftOtpActivationCodeParams
import com.vietinbank.core_domain.models.soft.VSoftResponseParams

interface SoftRepository {
    suspend fun getActivationCode(params: VGetSoftOtpActivationCodeParams): Resource<VSoftResponseParams>
    suspend fun blockToken(params: VBlockSoftOtpParams): Resource<VSoftResponseParams>
    suspend fun forgotKeypassPin(params: KeypassParams): Resource<VSoftResponseParams>
}