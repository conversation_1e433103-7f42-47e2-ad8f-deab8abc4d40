package com.vietinbank.core_domain.repository

import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.smartCA.SmartCABranchDomain
import com.vietinbank.core_domain.models.smartCA.SmartCACertVNPTPDomain
import com.vietinbank.core_domain.models.smartCA.SmartCACertVNPTParams
import com.vietinbank.core_domain.models.smartCA.SmartCADetailDomain
import com.vietinbank.core_domain.models.smartCA.SmartCADetailParams
import com.vietinbank.core_domain.models.smartCA.SmartCAGetParamDomain
import com.vietinbank.core_domain.models.smartCA.SmartCAGetParams
import com.vietinbank.core_domain.models.smartCA.SmartCAListDomain
import com.vietinbank.core_domain.models.smartCA.SmartCAParams
import com.vietinbank.core_domain.models.smartCA.SmartCARegisterDomain
import com.vietinbank.core_domain.models.smartCA.SmartCARegisterParams
import com.vietinbank.core_domain.models.smartCA.SmartCAUpdateDomain

interface SmartCARepository {
    suspend fun getListCKS(params: SmartCAParams): Resource<SmartCAListDomain>
    suspend fun getDetailCKS(params: SmartCADetailParams): Resource<SmartCADetailDomain>
    suspend fun registerCKS(params: SmartCARegisterParams): Resource<SmartCARegisterDomain>
    suspend fun getBranchCKS(params: SmartCAParams): Resource<SmartCABranchDomain>
    suspend fun updateCKS(params: SmartCADetailParams): Resource<SmartCAUpdateDomain>
    suspend fun getParamsCKS(params: SmartCAGetParams): Resource<SmartCAGetParamDomain>
    suspend fun getCertVNPT(params: SmartCACertVNPTParams): Resource<SmartCACertVNPTPDomain>
}