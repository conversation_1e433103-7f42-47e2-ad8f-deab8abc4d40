package com.vietinbank.core_domain.repository

import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.ekyc_feature.EkycCompareImageRequestParams
import com.vietinbank.core_domain.models.ekyc_feature.EkycConfirmInputDomains
import com.vietinbank.core_domain.models.ekyc_feature.EkycConfirmInputRequestParams
import com.vietinbank.core_domain.models.ekyc_feature.EkycNFCDomains
import com.vietinbank.core_domain.models.ekyc_feature.EkycNFCRequestParams
import com.vietinbank.core_domain.models.ekyc_feature.EkycOcrDomains
import com.vietinbank.core_domain.models.ekyc_feature.EkycOcrRequestParams
import com.vietinbank.core_domain.models.home.VerifyOtpEkycDomain

interface EkycFeatureRepository {
    suspend fun ekycOcr(params: EkycOcrRequestParams): Resource<EkycOcrDomains>
    suspend fun ekycNFC(params: EkycNFCRequestParams): Resource<EkycNFCDomains>
    suspend fun ekycConfirmInput(params: EkycConfirmInputRequestParams): Resource<EkycConfirmInputDomains>
    suspend fun ekycCompareImage(params: EkycCompareImageRequestParams): Resource<VerifyOtpEkycDomain>
}