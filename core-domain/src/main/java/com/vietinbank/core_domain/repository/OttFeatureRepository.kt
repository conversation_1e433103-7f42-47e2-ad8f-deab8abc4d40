package com.vietinbank.core_domain.repository

import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.ott_feature.CancelVacctOttDomain
import com.vietinbank.core_domain.models.ott_feature.CancelVacctOttParams
import com.vietinbank.core_domain.models.ott_feature.CheckRegDomains
import com.vietinbank.core_domain.models.ott_feature.CheckRegRequestParams
import com.vietinbank.core_domain.models.ott_feature.ConfirmVacctParams
import com.vietinbank.core_domain.models.ott_feature.ListRegDomains
import com.vietinbank.core_domain.models.ott_feature.ListRegRequestParams
import com.vietinbank.core_domain.models.ott_feature.OttRegisterDomains
import com.vietinbank.core_domain.models.ott_feature.OttRegisterRequestParams
import com.vietinbank.core_domain.models.ott_feature.OttStatusListDomain
import com.vietinbank.core_domain.models.ott_feature.OttStatusUpdateDomain
import com.vietinbank.core_domain.models.ott_feature.OttStatusUpdateParams
import com.vietinbank.core_domain.models.ott_feature.SmsOtpCreateRequestDomains
import com.vietinbank.core_domain.models.ott_feature.SmsOtpCreateRequestParams
import com.vietinbank.core_domain.models.ott_feature.SmsOtpVerifyRequestDomain
import com.vietinbank.core_domain.models.ott_feature.SmsOtpVerifyRequestParams

interface OttFeatureRepository {
    // check register OTT or not
    suspend fun checkRegister(params: CheckRegRequestParams): Resource<CheckRegDomains>

    // get list register
    suspend fun listReg(params: ListRegRequestParams): Resource<ListRegDomains>

    // create SMS OTP
    suspend fun smsOtpCreate(params: SmsOtpCreateRequestParams): Resource<SmsOtpCreateRequestDomains>

    // create SMS Verify
    suspend fun smsOtpVerify(params: SmsOtpVerifyRequestParams): Resource<SmsOtpVerifyRequestDomain>

    // registerOTT
    suspend fun registerOTT(params: OttRegisterRequestParams): Resource<OttRegisterDomains>

    suspend fun ottStatusUpdate(params: OttStatusUpdateParams): Resource<OttStatusUpdateDomain>

    suspend fun ottStatusList(params: String): Resource<OttStatusListDomain>

    suspend fun confirmVacct(params: ConfirmVacctParams): Resource<CancelVacctOttDomain>

    suspend fun cancelVacct(params: CancelVacctOttParams): Resource<CancelVacctOttDomain>
}