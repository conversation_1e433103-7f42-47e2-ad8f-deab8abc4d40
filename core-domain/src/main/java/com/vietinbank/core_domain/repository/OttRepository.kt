package com.vietinbank.core_domain.repository

import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.ott.OttGetAllMessageDomain
import com.vietinbank.core_domain.models.ott.OttGetAllMessageParams
import com.vietinbank.core_domain.models.ott.OttRegisterDeviceDomain
import com.vietinbank.core_domain.models.ott.OttRegisterDeviceParams

/**
 * Created by vandz on 15/4/25.
 */
interface OttRepository {
    /**
     * Register device token with OTT server
     * @return A Result containing a Boolean indicating success or failure
     */
    suspend fun registerToken(
        params: OttRegisterDeviceParams,
    ): Resource<OttRegisterDeviceDomain>

    suspend fun registerSocket(
        params: OttRegisterDeviceParams,
    ): Resource<OttRegisterDeviceDomain>

    suspend fun getAllMessage(params: OttGetAllMessageParams): Resource<OttGetAllMessageDomain>
}