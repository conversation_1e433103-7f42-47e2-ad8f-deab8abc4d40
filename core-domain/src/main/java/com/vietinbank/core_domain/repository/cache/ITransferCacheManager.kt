package com.vietinbank.core_domain.repository.cache

import com.vietinbank.core_domain.models.home.ListLatestDomain
import com.vietinbank.core_domain.models.home.TransactionInfo
import com.vietinbank.core_domain.models.login.AccountDefaultDomain
import com.vietinbank.core_domain.models.login.AccountListDomain
import com.vietinbank.core_domain.models.maker.BranchDomains
import com.vietinbank.core_domain.models.maker.BranchListDomains
import com.vietinbank.core_domain.models.maker.ContactDomains
import com.vietinbank.core_domain.models.maker.ContactListDomains
import com.vietinbank.core_domain.models.maker.DataBankDomain
import com.vietinbank.core_domain.models.maker.GetPaymentTemplateListDomains
import com.vietinbank.core_domain.models.maker.NapasBankListDomain
import com.vietinbank.core_domain.models.maker.TempTransactionDomains
import kotlinx.coroutines.flow.StateFlow

interface ITransferCacheManager {
    val banksCacheFlow: StateFlow<NapasBankListDomain>
    val contactsCacheFlow: StateFlow<ContactListDomains>
    val tempCacheFlow: StateFlow<GetPaymentTemplateListDomains>
    val latestListCacheFlow: StateFlow<ListLatestDomain>
    val accountsINCacheFlow: StateFlow<AccountListDomain>
    val accountsOUTCacheFlow: StateFlow<AccountListDomain>
    val accountsNP247CacheFlow: StateFlow<AccountListDomain>
    val accountsCARDCacheFlow: StateFlow<AccountListDomain>
    val accountsPaymentOrderCacheFlow: StateFlow<AccountListDomain>
    val branchListCacheFlow: StateFlow<BranchListDomains>

    fun saveBankList(dataBanks: MutableList<DataBankDomain>)
    fun saveContactist(dataContacts: MutableList<ContactDomains>)
    fun saveTempList(dataTemps: MutableList<TempTransactionDomains>)
    fun saveLatestList(dataLatestList: MutableList<TransactionInfo>)
    fun saveAccountsIN(dataAccounts: MutableList<AccountDefaultDomain>)
    fun saveAccountsOUT(dataAccounts: MutableList<AccountDefaultDomain>)
    fun saveAccountsNP247(dataAccounts: MutableList<AccountDefaultDomain>)
    fun saveAccountsCARD(dataAccounts: MutableList<AccountDefaultDomain>)
    fun saveAccountsPM(dataAccounts: MutableList<AccountDefaultDomain>)
    fun saveBranch(dataBranch: MutableList<BranchDomains>)

    fun getBankList(): List<DataBankDomain>?
    fun getContactList(): List<ContactDomains>?
    fun getTempList(): List<TempTransactionDomains>?
    fun getLatTestList(): List<TransactionInfo>?
    fun getAccountsIN(): MutableList<AccountDefaultDomain>?
    fun getAccountsOUT(): MutableList<AccountDefaultDomain>?
    fun getAccountsNP247(): MutableList<AccountDefaultDomain>?
    fun getAccountsCARD(): MutableList<AccountDefaultDomain>?
    fun getAccountsPM(): MutableList<AccountDefaultDomain>?
    fun getBranchList(): MutableList<BranchDomains>?

    fun clearBankList()
    fun clearContactList()
    fun clearLatestList()
    fun clearTempList()
    fun clearAccountsIN()
    fun clearAccountsOUT()
    fun clearAccountsNP247()
    fun clearAccountsCARD()
    fun clearAccountsPM()
    fun clearTransferALL()
    fun clearBranch()
    suspend fun hasCachedData(): Boolean
}