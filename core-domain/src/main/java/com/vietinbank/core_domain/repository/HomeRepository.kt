package com.vietinbank.core_domain.repository

import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.home.CountTransGroupDomain
import com.vietinbank.core_domain.models.home.CountTransGroupParams
import com.vietinbank.core_domain.models.home.HomeAccountListDomain
import com.vietinbank.core_domain.models.home.HomeAccountListParams
import com.vietinbank.core_domain.models.home.ListFunctionDomain
import com.vietinbank.core_domain.models.home.ListFunctionParams
import com.vietinbank.core_domain.models.home.ListKeyDomain
import com.vietinbank.core_domain.models.home.ListKeyParams
import com.vietinbank.core_domain.models.home.ListLatestDomain
import com.vietinbank.core_domain.models.home.ListLatestParams
import com.vietinbank.core_domain.models.home.RegisterTouchIDDomain
import com.vietinbank.core_domain.models.home.RegisterTouchIDParams
import com.vietinbank.core_domain.models.home.UpdateFunctionParams

/**
 * Created by vandz on 18/12/24.
 */
interface HomeRepository {
    suspend fun registerTouchID(params: RegisterTouchIDParams): Resource<RegisterTouchIDDomain>

    suspend fun countTransGroup(params: CountTransGroupParams): Resource<CountTransGroupDomain>

    suspend fun listLatest(params: ListLatestParams): Resource<ListLatestDomain>

    suspend fun homeAccountList(params: HomeAccountListParams): Resource<HomeAccountListDomain>

    suspend fun listKey(params: ListKeyParams): Resource<ListKeyDomain>

    suspend fun listFunction(params: ListFunctionParams): Resource<ListFunctionDomain>

    suspend fun listFavoriteFunction(params: ListFunctionParams): Resource<ListFunctionDomain>

    suspend fun updateFavoriteFunction(params: UpdateFunctionParams): Resource<String>
}