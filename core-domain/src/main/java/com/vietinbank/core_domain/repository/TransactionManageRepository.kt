package com.vietinbank.core_domain.repository

import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.maker.CreateTransferDomain
import com.vietinbank.core_domain.models.manage.DetailReportParams
import com.vietinbank.core_domain.models.manage.FilterReportDomain
import com.vietinbank.core_domain.models.manage.FilterReportParams
import com.vietinbank.core_domain.models.manage.InquiryApproverDomains
import com.vietinbank.core_domain.models.manage.InquiryApproverParams
import com.vietinbank.core_domain.models.manage.ListReportDomain
import com.vietinbank.core_domain.models.manage.ListReportParams
import com.vietinbank.core_domain.models.manage.TransDetailDomain
import com.vietinbank.core_domain.models.trace_payment.ReportDetailsDomains
import com.vietinbank.core_domain.models.trace_payment.ReportDetailsParams
import com.vietinbank.core_domain.models.trace_payment.TraceCreateParams
import com.vietinbank.core_domain.models.trace_payment.TraceInquiryDomains
import com.vietinbank.core_domain.models.trace_payment.TraceInquiryParams

interface TransactionManageRepository {
    suspend fun getPreFilterTransaction(params: FilterReportParams): Resource<FilterReportDomain>
    suspend fun getListFilterTransaction(params: ListReportParams): Resource<ListReportDomain>
    suspend fun getInquiryApprover(params: InquiryApproverParams): Resource<InquiryApproverDomains>
    suspend fun traceInquiry(params: TraceInquiryParams): Resource<TraceInquiryDomains>
    suspend fun reportDetail(params: ReportDetailsParams): Resource<ReportDetailsDomains>
    suspend fun traceCreate(params: TraceCreateParams): Resource<CreateTransferDomain>
    suspend fun getReportDetail(params: DetailReportParams): Resource<TransDetailDomain?>
}