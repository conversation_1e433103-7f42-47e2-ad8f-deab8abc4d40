package com.vietinbank.core_domain.repository

import com.vietinbank.core_common.models.ForceUpdateDomain
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.login.AccountListDomain
import com.vietinbank.core_domain.models.login.AccountListParams
import com.vietinbank.core_domain.models.login.AccountLockDomain
import com.vietinbank.core_domain.models.login.AccountLockFaceParams
import com.vietinbank.core_domain.models.login.AccountLockOTPDomain
import com.vietinbank.core_domain.models.login.AccountLockOTPParams
import com.vietinbank.core_domain.models.login.AccountLockParams
import com.vietinbank.core_domain.models.login.ChangePasswordDomain
import com.vietinbank.core_domain.models.login.ChangePasswordParams
import com.vietinbank.core_domain.models.login.ForceUpdateParams
import com.vietinbank.core_domain.models.login.GenOTPDomain
import com.vietinbank.core_domain.models.login.GenOTPParams
import com.vietinbank.core_domain.models.login.LoginDomain
import com.vietinbank.core_domain.models.login.LoginParams
import com.vietinbank.core_domain.models.login.VerifyOTPDomain
import com.vietinbank.core_domain.models.login.VerifyOTPParams

/**
 * Created by vandz on 18/12/24.
 */
interface LoginRepository {
    suspend fun login(params: LoginParams): Resource<LoginDomain>
    suspend fun changePassword(params: ChangePasswordParams): Resource<ChangePasswordDomain>
    suspend fun forceUpdate(params: ForceUpdateParams): Resource<ForceUpdateDomain>
    suspend fun accountList(params: AccountListParams): Resource<AccountListDomain>
    suspend fun genOTP(params: GenOTPParams): Resource<GenOTPDomain>
    suspend fun verifyOTP(params: VerifyOTPParams): Resource<VerifyOTPDomain>

    // lock account
    suspend fun checkInformation(params: AccountLockParams): Resource<AccountLockDomain>
    suspend fun sendOTPLockAccount(params: AccountLockOTPParams): Resource<AccountLockOTPDomain>
    suspend fun compareFaceLockAccount(params: AccountLockFaceParams): Resource<AccountLockOTPDomain>
}