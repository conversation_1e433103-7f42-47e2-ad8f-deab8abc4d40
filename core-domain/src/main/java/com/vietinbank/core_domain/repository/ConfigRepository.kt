package com.vietinbank.core_domain.repository

/**
 * Created by vand<PERSON> on 18/12/24.
 * Repository để quản lý cấu hình ứng dụng
 */
interface ConfigRepository {
    /**
     * Lấy URL cơ sở cho Socket.IO
     * @return URL cơ sở, ví dụ: "https://webdemo.vietinbank.vn"
     */
    fun getSocketBaseUrl(): String

    /**
     * Lấy path cho Socket.IO
     * @return Path, ví dụ: "/socket-decode/socket.io"
     */
    fun getSocketPath(): String

    /**
     * Cập nhật cấu hình từ server
     * @param socketBaseUrl URL cơ sở mới (null nếu không thay đổi)
     * @param socketPath Path mới (null nếu không thay đổi)
     */
    fun updateConfigFromServer(socketBaseUrl: String?, socketPath: String?)
}