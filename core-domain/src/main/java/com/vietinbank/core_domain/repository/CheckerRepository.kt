package com.vietinbank.core_domain.repository

import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.checker.ApproveDomain
import com.vietinbank.core_domain.models.checker.ApproveParams
import com.vietinbank.core_domain.models.checker.CountPendingDomain
import com.vietinbank.core_domain.models.checker.CountPendingParams
import com.vietinbank.core_domain.models.checker.GenKeyPassChallengeCodeDomain
import com.vietinbank.core_domain.models.checker.GenKeyPassChallengeCodeParams
import com.vietinbank.core_domain.models.checker.GenSOTPTransCodeDomain
import com.vietinbank.core_domain.models.checker.GenSOTPTransCodeParams
import com.vietinbank.core_domain.models.checker.GetBatchTransactionListDomain
import com.vietinbank.core_domain.models.checker.GetBatchTransactionListParams
import com.vietinbank.core_domain.models.checker.GetDownloadBase64FileDomain
import com.vietinbank.core_domain.models.checker.GetDownloadBase64FileParams
import com.vietinbank.core_domain.models.checker.GetDownloadFileIDDomain
import com.vietinbank.core_domain.models.checker.GetDownloadFileIDParams
import com.vietinbank.core_domain.models.checker.GetExportNSNNFileTemplateParams
import com.vietinbank.core_domain.models.checker.GetTransactionDetailDomain
import com.vietinbank.core_domain.models.checker.GetTransactionDetailParams
import com.vietinbank.core_domain.models.checker.GetTransactionListDomain
import com.vietinbank.core_domain.models.checker.GetTransactionListParams
import com.vietinbank.core_domain.models.checker.MobileConfigLastDomain
import com.vietinbank.core_domain.models.checker.MobileConfigLastParams
import com.vietinbank.core_domain.models.checker.PreApproveDomain
import com.vietinbank.core_domain.models.checker.PreApproveParams
import com.vietinbank.core_domain.models.checker.PreRejectParams
import com.vietinbank.core_domain.models.checker.RejectDomain
import com.vietinbank.core_domain.models.checker.RejectParams
import com.vietinbank.core_domain.models.checker.SubTransactionDomain
import com.vietinbank.core_domain.models.checker.SubTransactionParams
import java.io.File

/**
 * Created by vandz on 18/12/24.
 */
interface CheckerRepository {
    suspend fun getTransactionList(params: GetTransactionListParams): Resource<GetTransactionListDomain>
    suspend fun getTranList(params: GetTransactionListParams): Resource<GetTransactionListDomain>
    suspend fun getTransactionDetail(params: GetTransactionDetailParams): Resource<GetTransactionDetailDomain>
    suspend fun countPending(params: CountPendingParams): Resource<CountPendingDomain>
    suspend fun mobileConfigLast(params: MobileConfigLastParams): Resource<MobileConfigLastDomain>
    suspend fun preApprove(params: PreApproveParams): Resource<PreApproveDomain>
    suspend fun doApprove(params: ApproveParams): Resource<ApproveDomain>
    suspend fun preReject(params: PreRejectParams): Resource<PreApproveDomain>
    suspend fun doReject(params: RejectParams): Resource<RejectDomain>
    suspend fun genSOTPTransCode(params: GenSOTPTransCodeParams): Resource<GenSOTPTransCodeDomain>
    suspend fun genKeypassChallengeCode(params: GenKeyPassChallengeCodeParams): Resource<GenKeyPassChallengeCodeDomain>
    suspend fun getBathTransactionList(params: GetBatchTransactionListParams): Resource<GetBatchTransactionListDomain>
    suspend fun getDownloadFileID(params: GetDownloadFileIDParams): Resource<GetDownloadFileIDDomain>
    suspend fun getDownloadBase64File(params: GetDownloadBase64FileParams): Resource<GetDownloadBase64FileDomain>
    suspend fun getExportNSNNFileTemplateParams(params: GetExportNSNNFileTemplateParams): Resource<GetDownloadBase64FileDomain>
    suspend fun getSubTransactionDetail(params: SubTransactionParams): Resource<SubTransactionDomain>
    suspend fun downloadPdfFile(url: String): Resource<File>
}