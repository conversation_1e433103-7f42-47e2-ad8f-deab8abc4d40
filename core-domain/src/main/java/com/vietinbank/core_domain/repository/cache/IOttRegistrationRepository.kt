package com.vietinbank.core_domain.repository.cache

import com.vietinbank.core_domain.models.ott_feature.ListRegDomains
import kotlinx.coroutines.flow.StateFlow

/**
 * Repository interface for OTT registration data
 * This interface provides business-focused methods for managing OTT registrations
 */
interface IOttRegistrationRepository {

    val registrationFlow: StateFlow<ListRegDomains>
    val countTimeShowRegisterOTTFlow: StateFlow<Int>

    fun getCachedRegistrations(): ListRegDomains?
    fun saveRegistrations(data: ListRegDomains)
    fun incrementCountTimeShowRegisterOTT()
    fun getCahceCountTimeShowRegisterOTT(): Int
    fun clearRegistrations()
    fun clearAllOttCache(needClearCahceCountTimeShowRegisterOTT: Boolean = false)
    fun clearCahceCountTimeShowRegisterOTT()
    suspend fun hasCachedData(): Boolean
}