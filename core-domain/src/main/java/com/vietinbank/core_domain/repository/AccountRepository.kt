package com.vietinbank.core_domain.repository

import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.account.AccountDetailDomain
import com.vietinbank.core_domain.models.account.AccountDetailParams
import com.vietinbank.core_domain.models.account.AccountFileSavingDomain
import com.vietinbank.core_domain.models.account.AccountFileSavingParams
import com.vietinbank.core_domain.models.account.AccountHistoryDetailDomain
import com.vietinbank.core_domain.models.account.AccountHistoryDetailParams
import com.vietinbank.core_domain.models.account.AccountHistoryListDomain
import com.vietinbank.core_domain.models.account.AccountHistoryListParams
import com.vietinbank.core_domain.models.account.AccountImageQRDomain
import com.vietinbank.core_domain.models.account.AccountImagesQRParams
import com.vietinbank.core_domain.models.account.AccountListNewDomain
import com.vietinbank.core_domain.models.account.AccountLstParams
import com.vietinbank.core_domain.models.account.AccountSaveQRDomain
import com.vietinbank.core_domain.models.account.AccountSaveQRParams
import com.vietinbank.core_domain.models.account.AliasAccountParams
import com.vietinbank.core_domain.models.account.SetAliasAccountDomain
import com.vietinbank.core_domain.models.account.TransactionStatusDomain
import com.vietinbank.core_domain.models.account.TransactionStatusParams

interface AccountRepository {
    suspend fun getAccountList(params: AccountLstParams): Resource<AccountListNewDomain>
    suspend fun getAccountDetail(params: AccountDetailParams): Resource<AccountDetailDomain>
    suspend fun getTransactionDetail(params: AccountHistoryDetailParams): Resource<AccountHistoryDetailDomain>
    suspend fun getTransactionList(params: AccountHistoryListParams): Resource<AccountHistoryListDomain>
    suspend fun setAliasAccount(params: AliasAccountParams): Resource<SetAliasAccountDomain>
    suspend fun setQRAccount(params: AccountSaveQRParams): Resource<AccountSaveQRDomain>
    suspend fun getImageQRAccount(params: AccountImagesQRParams): Resource<AccountImageQRDomain>
    suspend fun getTransactionStatus(params: TransactionStatusParams): Resource<TransactionStatusDomain>
    suspend fun exportFile(params: AccountFileSavingParams): Resource<AccountFileSavingDomain>
}