package com.vietinbank.core_domain.repository

import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.home.CheckUserEkycDomain
import com.vietinbank.core_domain.models.home.CheckUserEkycParams
import com.vietinbank.core_domain.models.home.GenerateOtpDomain
import com.vietinbank.core_domain.models.home.GenerateOtpParam
import com.vietinbank.core_domain.models.home.GetBiometricFaceDomain
import com.vietinbank.core_domain.models.home.GetBiometricFaceParams
import com.vietinbank.core_domain.models.home.UpdateEkycDomain
import com.vietinbank.core_domain.models.home.UpdateEkycParams
import com.vietinbank.core_domain.models.home.VerifyOtpEkycDomain
import com.vietinbank.core_domain.models.home.VerifyOtpEkycParam

interface UpdateEkycRepository {
    suspend fun checkUserEkyc(params: CheckUserEkycParams): Resource<CheckUserEkycDomain>
    suspend fun generateOtpRetail(params: GenerateOtpParam): Resource<GenerateOtpDomain>
    suspend fun verifyOtpRetail(params: VerifyOtpEkycParam): Resource<VerifyOtpEkycDomain>
    suspend fun updateEkyc(params: UpdateEkycParams): Resource<UpdateEkycDomain>
    suspend fun getBiometricFace(params: GetBiometricFaceParams): Resource<GetBiometricFaceDomain>
}