package com.vietinbank.core_domain.repository

import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.maker.BranchListDomains
import com.vietinbank.core_domain.models.maker.BranchParams
import com.vietinbank.core_domain.models.maker.ContactCreateDomains
import com.vietinbank.core_domain.models.maker.ContactCreateParams
import com.vietinbank.core_domain.models.maker.ContactListDomains
import com.vietinbank.core_domain.models.maker.ContactListParams
import com.vietinbank.core_domain.models.maker.CreateTemplateDomains
import com.vietinbank.core_domain.models.maker.CreateTemplateParams
import com.vietinbank.core_domain.models.maker.CreateTransferDomain
import com.vietinbank.core_domain.models.maker.CreateTransferParams
import com.vietinbank.core_domain.models.maker.GetPaymentTemplateListDomains
import com.vietinbank.core_domain.models.maker.GetPaymentTemplateListParams
import com.vietinbank.core_domain.models.maker.NapasBankListDomain
import com.vietinbank.core_domain.models.maker.NapasBankListParams
import com.vietinbank.core_domain.models.maker.NextStatusTransactionByRuleDomains
import com.vietinbank.core_domain.models.maker.NextStatusTransactionByRuleParams
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountDomain
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountParams
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountTransferDomain
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountTransferParams
import com.vietinbank.core_domain.models.maker.ValidateNapasCardDomains
import com.vietinbank.core_domain.models.maker.ValidateNapasCardParams
import com.vietinbank.core_domain.models.maker.ValidateNapasCardTransferDomains
import com.vietinbank.core_domain.models.maker.ValidateNapasCardTransferParams
import com.vietinbank.core_domain.models.maker.ValidatePaymentOrderTransferDomains
import com.vietinbank.core_domain.models.maker.ValidatePaymentOrderTransferParams

interface TransferRepository {
    suspend fun getNapasBankList(params: NapasBankListParams): Resource<NapasBankListDomain>
    suspend fun validateNapasAccount(params: ValidateNapasAccountParams): Resource<ValidateNapasAccountDomain>
    suspend fun createNapasAccountTransfer(params: CreateTransferParams): Resource<CreateTransferDomain>
    suspend fun validateNapasAccountTransfer(params: ValidateNapasAccountTransferParams): Resource<ValidateNapasAccountTransferDomain>
    suspend fun validateNapasCard(params: ValidateNapasCardParams): Resource<ValidateNapasCardDomains>
    suspend fun validateNapasCardTransfer(params: ValidateNapasCardTransferParams): Resource<ValidateNapasCardTransferDomains>
    suspend fun contactList(params: ContactListParams): Resource<ContactListDomains>
    suspend fun getPaymentTemplateList(params: GetPaymentTemplateListParams): Resource<GetPaymentTemplateListDomains>
    suspend fun contactCreate(params: ContactCreateParams): Resource<ContactCreateDomains>
    suspend fun createTemplate(params: CreateTemplateParams): Resource<CreateTemplateDomains>
    suspend fun validatePaymentOrderTransfer(params: ValidatePaymentOrderTransferParams): Resource<ValidatePaymentOrderTransferDomains>
    suspend fun nextStatusTransactionByRule(params: NextStatusTransactionByRuleParams): Resource<NextStatusTransactionByRuleDomains>
    suspend fun getBranch(params: BranchParams): Resource<BranchListDomains>
}