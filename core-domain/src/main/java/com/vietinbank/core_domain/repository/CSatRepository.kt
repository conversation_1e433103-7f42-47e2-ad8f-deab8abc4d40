package com.vietinbank.core_domain.repository

import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.csat.CSatConfigDomain
import com.vietinbank.core_domain.models.csat.CSatConfigParams
import com.vietinbank.core_domain.models.csat.CSatRateDomain
import com.vietinbank.core_domain.models.csat.CSatRateParams

interface CSatRepository {
    suspend fun rateCSAT(params: CSatRateParams): Resource<CSatRateDomain>
    suspend fun configCSAT(params: CSatConfigParams): Resource<CSatConfigDomain>
}