package com.vietinbank.core_domain.models.soft

enum class SoftSDKErrorCode(val code: Int, val message: String) {
    SUCCESS(0, "Thành công"),
    OTHER_SUCCESS(1, "Thành công"),
    FAIL(93, "<PERSON>hông thành công"),
    WRONG_ACTIVATION_5_TIMES(89, "Kích hoạt sai 5 lần liên tiếp"),
    WRONG_ACTIVATION_CODE(91, "Sai mã kích hoạt"),
    ACTIVATION_CODE_EXPIRED(90, "Mã kích hoạt hết hạn"),
    WRONG_PIN(21, "Sai mã PIN"),
    CONNECTION_TIMEOUT(28, "Lỗi kết nối: Kết nối đến server bị timeout"),
    CONNECTION_FAILED_1(7, "Lỗi kết nối: Không thể kết nối đến server"),
    CONNECTION_FAILED_2(56, "Lỗi kết nối: <PERSON>h<PERSON>ng thể kết nối đến server"),
    SYNC_LIMIT_EXCEEDED(205, "Đồng bộ quá số lần cho phép trong 1 timestep"),
    ROOTED_DEVICE(94, "Thiết bị Root/Jailbreak"),
    INVALID_URL(92, "URL không hợp lệ"),
    NOT_ACTIVATED(206, "Ứng dụng chưa được kích hoạt"),
    INVALID_DATA(3, "Dữ liệu không hợp lệ"),
    DB_ERROR(11, "Lỗi cơ sở dữ liệu"),
    TRANSACTION_TIMEOUT(31, "Lỗi giao dịch hết hạn xử lý"),
    TRANSACTION_UPDATE_FAIL(72, "Cập nhật trạng thái giao dịch không thành công"),
    CHALLENGE_CODE_USED(97, "Challenge code cho giao dịch này đã được sử dụng"),
    TRANSACTION_NOT_FOUND(99, "Giao dịch không tồn tại"),
    SYSTEM_ERROR(100, "Lỗi hệ thống"),
    CALLBACK_FAILED(102, "Gọi lại không thành công. Kiểm tra kết nối với API"),
    ;

    companion object {
        fun fromCode(code: Int): SoftSDKErrorCode =
            SoftSDKErrorCode.entries.find { it.code == code } ?: FAIL
    }
}