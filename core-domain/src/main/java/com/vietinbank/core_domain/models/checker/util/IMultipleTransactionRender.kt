package com.vietinbank.core_domain.models.checker.util

import androidx.compose.runtime.Composable
import com.vietinbank.core_domain.models.checker.MultipleResultDomain
import com.vietinbank.core_domain.models.checker.maping.confirm.MultipleConfirmUIModel

interface IMultipleTransactionRender {
    @Composable
    fun RenderConfirmContent(transaction: MultipleConfirmUIModel?)

    @Composable
    fun RenderResultContent(isSuccess: <PERSON>olean, transaction: MultipleResultDomain?)
}