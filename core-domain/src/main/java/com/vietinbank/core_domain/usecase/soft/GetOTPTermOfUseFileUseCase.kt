package com.vietinbank.core_domain.usecase.soft

import android.content.Context
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Tags.LANGUAGE_CN
import com.vietinbank.core_common.constants.Tags.LANGUAGE_EN
import com.vietinbank.core_common.constants.Tags.LANGUAGE_JP
import com.vietinbank.core_common.constants.Tags.LANGUAGE_KR
import com.vietinbank.core_common.constants.Tags.LANGUAGE_VI
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.utils.Resource
import dagger.hilt.android.qualifiers.ApplicationContext
import jakarta.inject.Inject
import java.io.File
import java.io.FileOutputStream

class GetOTPTermOfUseFileUseCase @Inject constructor(
    @ApplicationContext private val context: Context,
    private val appConfig: IAppConfigManager,
) {
    operator fun invoke(): Resource<File> = runCatching {
        val supportedLanguage = listOf(
            LANGUAGE_CN,
            LANGUAGE_EN,
            LANGUAGE_JP,
            LANGUAGE_KR,
            LANGUAGE_VI,
        )

        val lang = appConfig.getLanguage()
            .takeIf { it in supportedLanguage }
            ?: LANGUAGE_EN

        val fileName = "soft_otp_term_$lang.pdf"

        val outFile = File(context.cacheDir, fileName)
        context.assets.open(fileName).use { input ->
            FileOutputStream(outFile).use { output ->
                input.copyTo(output)
            }
        }
        Resource.Success(outFile)
    }.getOrElse {
        Resource.Error(
            code = "999",
            message = "File not exist",
            exception = AppException.UnknownException(
                message = it.message ?: "File not exist",
                cause = it.cause,
            ),
        )
    }
}