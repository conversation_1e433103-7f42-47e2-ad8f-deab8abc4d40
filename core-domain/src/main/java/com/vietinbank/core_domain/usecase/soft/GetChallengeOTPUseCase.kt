package com.vietinbank.core_domain.usecase.soft

import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.soft.GetChallengeOTPParams
import com.vietinbank.core_domain.repository.SoftRepository
import javax.inject.Inject

class GetChallengeOTPUseCase @Inject constructor(
    private val softRepository: SoftRepository,
    private val getTransactionInfoUseCase: GetTransactionInfoUseCase,
) {
    suspend operator fun invoke(
        transactionId: String,
        messageId: String = "",
    ): Resource<String> {
        return when (val response = getTransactionInfoUseCase(transactionId, messageId)) {
            is Resource.Error -> response
            is Resource.Success -> {
                val transactionInfo = response.data.softEntity
                softRepository.getChallengeResponseOTP(
                    GetChallengeOTPParams(
                        transactionInfo.transactionData,
                        transactionInfo.challenge,
                    ),
                )
            }
        }
    }
}