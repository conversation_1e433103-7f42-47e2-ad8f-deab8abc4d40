package com.vietinbank.core_domain.usecase.soft

import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.repository.SoftRepository
import javax.inject.Inject

/**
Step 5.1 -> 5.5 MK Doc 2.2.1
Call đến SDK.checkActiveToken
Nếu response code = 0/1 chuyển bước 6 (doSyncTime) (Đã map sang Resource.Success)
Nếu response code kết quả khác thì thông báo lỗi (show mã lỗi) và dừng flow
 */
class CheckOTPActiveSessionUseCase @Inject constructor(
    private val softRepository: SoftRepository,
) {
    suspend operator fun invoke(): Resource<Unit> {
        return softRepository.checkActiveToken()
    }
}