package com.vietinbank.core_domain.usecase.soft

import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.soft.SetSelectUserIdParams
import com.vietinbank.core_domain.repository.SoftRepository
import com.vietinbank.core_domain.softotp.ISoftManager
import javax.inject.Inject

class ToggleSoftUseCase @Inject constructor(
    private val softRepository: SoftRepository,
    private val softManager: ISoftManager,
    private val appConfig: IAppConfigManager,
) {
    suspend operator fun invoke(
        userId: String,
        pushToken: String,
    ): Resource<Unit> {
        val response = softRepository.deleteAllTokensSmartOTP()
        return when (response) {
            is Resource.Success -> {
                appConfig.updateStatusSoft(false)
                softManager.isAppActive = false
                softManager.isUserActive = false
                softRepository.setSelectedUserId(SetSelectUserIdParams(""))
                Resource.Success(Unit)
            }

            is Resource.Error -> Resource.Error(response.message, response.code)
        }
    }
}