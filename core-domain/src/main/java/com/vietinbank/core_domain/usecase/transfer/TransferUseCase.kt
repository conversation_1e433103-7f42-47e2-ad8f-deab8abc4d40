package com.vietinbank.core_domain.usecase.transfer

import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.maker.BranchListDomains
import com.vietinbank.core_domain.models.maker.BranchParams
import com.vietinbank.core_domain.models.maker.ContactCreateDomains
import com.vietinbank.core_domain.models.maker.ContactCreateParams
import com.vietinbank.core_domain.models.maker.ContactListDomains
import com.vietinbank.core_domain.models.maker.ContactListParams
import com.vietinbank.core_domain.models.maker.CreateTemplateDomains
import com.vietinbank.core_domain.models.maker.CreateTemplateParams
import com.vietinbank.core_domain.models.maker.CreateTransferDomain
import com.vietinbank.core_domain.models.maker.CreateTransferParams
import com.vietinbank.core_domain.models.maker.GetPaymentTemplateListDomains
import com.vietinbank.core_domain.models.maker.GetPaymentTemplateListParams
import com.vietinbank.core_domain.models.maker.NapasBankListDomain
import com.vietinbank.core_domain.models.maker.NapasBankListParams
import com.vietinbank.core_domain.models.maker.NextStatusTransactionByRuleDomains
import com.vietinbank.core_domain.models.maker.NextStatusTransactionByRuleParams
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountDomain
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountParams
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountTransferDomain
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountTransferParams
import com.vietinbank.core_domain.models.maker.ValidateNapasCardDomains
import com.vietinbank.core_domain.models.maker.ValidateNapasCardParams
import com.vietinbank.core_domain.models.maker.ValidateNapasCardTransferDomains
import com.vietinbank.core_domain.models.maker.ValidateNapasCardTransferParams
import com.vietinbank.core_domain.models.maker.ValidatePaymentOrderTransferDomains
import com.vietinbank.core_domain.models.maker.ValidatePaymentOrderTransferParams
import com.vietinbank.core_domain.repository.TransferRepository
import javax.inject.Inject

class TransferUseCase @Inject constructor(
    private val transferRepository: TransferRepository,
) {
    suspend fun getNapasBankList(params: NapasBankListParams): Resource<NapasBankListDomain> {
        if (params.username.isBlank()) {
            return Resource.Error(
                "Empty username ",
                "133",
                AppException.UnknownException("Empty Empty username "),
            )
        }
        return transferRepository.getNapasBankList(params)
    }

    suspend fun validateNapasAccount(params: ValidateNapasAccountParams): Resource<ValidateNapasAccountDomain> {
        val blankParams = mutableListOf<String>()
        if (params.receiveAccount.isBlank()) blankParams.add("receiveAccount")
        if (params.debitAccount.isBlank()) blankParams.add("debitAccount")
        if (params.debitFullname.isBlank()) blankParams.add("debitFullname")
        if (params.cifno.isBlank()) blankParams.add("cifno")
        if (params.currency.isBlank()) blankParams.add("currency")
        if (params.username.isBlank()) blankParams.add("username")

        if (blankParams.isNotEmpty()) {
            return Resource.Error(
                "Empty params: ${blankParams.joinToString(", ")} are blank",
                "133",
                AppException.UnknownException("Empty params: ${blankParams.joinToString(", ")} are blank"),
            )
        }
        return transferRepository.validateNapasAccount(params)
    }

    suspend fun createNapasAccountTransfer(params: CreateTransferParams): Resource<CreateTransferDomain> {
        val blankParams = mutableListOf<String>()
        if (params.username.isBlank()) blankParams.add("username")
        if (blankParams.isNotEmpty()) {
            return Resource.Error(
                "Empty params: ${blankParams.joinToString(", ")} are blank",
                "133",
                AppException.UnknownException("Empty params: ${blankParams.joinToString(", ")} are blank"),
            )
        }
        return transferRepository.createNapasAccountTransfer(params)
    }

    suspend fun validateNapasAccountTransfer(params: ValidateNapasAccountTransferParams): Resource<ValidateNapasAccountTransferDomain> {
        val blankParams = mutableListOf<String>()

        if (params.amount.isBlank()) blankParams.add("amount")
        if (params.currency.isBlank()) blankParams.add("currency")
        if (params.feePayMethod.isBlank()) blankParams.add("feePayMethod")
        if (params.fromAcctNo.isBlank()) blankParams.add("fromAcctNo")
//        if (params.receiveBin.isBlank()) blankParams.add("receiveBin")
        if (params.remark.isBlank()) blankParams.add("remark")
        if (params.sendBank.isBlank()) blankParams.add("sendBank")
        if (params.toAcctName.isBlank()) blankParams.add("toAcctName")
        if (params.toAcctNo.isBlank()) blankParams.add("toAcctNo")
        if (params.username.isBlank()) blankParams.add("username")

        if (blankParams.isNotEmpty()) {
            return Resource.Error(
                "Empty params: ${blankParams.joinToString(", ")} are blank",
                "133",
                AppException.UnknownException("Empty params: ${blankParams.joinToString(", ")} are blank"),
            )
        }
        return transferRepository.validateNapasAccountTransfer(params)
    }

    suspend fun validateNapasCard(params: ValidateNapasCardParams): Resource<ValidateNapasCardDomains> {
        val blankParams = mutableListOf<String>()
        if (params.branch.isBlank()) blankParams.add("branch")
        if (params.cardNumber.isBlank()) blankParams.add("cardNumber")
        if (params.currency.isBlank()) blankParams.add("currency")
        if (params.debitAccount.isBlank()) blankParams.add("debitAccount")
        if (params.debitFullname.isBlank()) blankParams.add("debitFullname")
        if (params.username.isBlank()) blankParams.add("username")

        if (blankParams.isNotEmpty()) {
            return Resource.Error(
                "Empty params: ${blankParams.joinToString(", ")} are blank",
                "133",
                AppException.UnknownException("Empty params: ${blankParams.joinToString(", ")} are blank"),
            )
        }
        return transferRepository.validateNapasCard(params)
    }

    suspend fun validateNapasCardTransfer(params: ValidateNapasCardTransferParams): Resource<ValidateNapasCardTransferDomains> {
        val blankParams = mutableListOf<String>()
        if (params.amount.isBlank()) blankParams.add("amount")
        if (params.currency.isBlank()) blankParams.add("currency")
        if (params.feePayMethod.isBlank()) blankParams.add("feePayMethod")
        if (params.fromAcctNo.isBlank()) blankParams.add("fromAcctNo")
        if (params.remark.isBlank()) blankParams.add("remark")
        if (params.sendBank.isBlank()) blankParams.add("sendBank")
        if (params.toBankName.isBlank()) blankParams.add("toBankName")
        if (params.toCardName.isBlank()) blankParams.add("toCardName")
        if (params.toCardNo.isBlank()) blankParams.add("toCardNo")
        if (params.username.isBlank()) blankParams.add("username")
        if (blankParams.isNotEmpty()) {
            return Resource.Error(
                "Empty params: ${blankParams.joinToString(", ")} are blank",
                "133",
                AppException.UnknownException("Empty params: ${blankParams.joinToString(", ")} are blank"),
            )
        }
        return transferRepository.validateNapasCardTransfer(params)
    }

    suspend fun contactList(params: ContactListParams): Resource<ContactListDomains> {
        val blankParams = mutableListOf<String>()
        if (params.username.isBlank()) blankParams.add("username")
        if (blankParams.isNotEmpty()) {
            return Resource.Error(
                "Empty params: ${blankParams.joinToString(", ")} are blank",
                "133",
                AppException.UnknownException("Empty params: ${blankParams.joinToString(", ")} are blank"),
            )
        }
        return transferRepository.contactList(params)
    }

    suspend fun getPaymentTemplateList(params: GetPaymentTemplateListParams): Resource<GetPaymentTemplateListDomains> {
        val blankParams = mutableListOf<String>()
        if (params.tempTransaction.tranType.isBlank()) blankParams.add("tranType")
        if (blankParams.isNotEmpty()) {
            return Resource.Error(
                "Empty params: ${blankParams.joinToString(", ")} are blank",
                "133",
                AppException.UnknownException("Empty params: ${blankParams.joinToString(", ")} are blank"),
            )
        }
        return transferRepository.getPaymentTemplateList(params)
    }

    suspend fun contactCreate(params: ContactCreateParams): Resource<ContactCreateDomains> {
        val blankParams = mutableListOf<String>()
        if (params.customercode.isBlank()) blankParams.add("tranType")
        if (blankParams.isNotEmpty()) {
            return Resource.Error(
                "Empty params: ${blankParams.joinToString(", ")} are blank",
                "133",
                AppException.UnknownException("Empty params: ${blankParams.joinToString(", ")} are blank"),
            )
        }
        return transferRepository.contactCreate(params)
    }

    suspend fun createTemplate(params: CreateTemplateParams): Resource<CreateTemplateDomains> {
        return transferRepository.createTemplate(params)
    }

    suspend fun validatePaymentOrderTransfer(params: ValidatePaymentOrderTransferParams): Resource<ValidatePaymentOrderTransferDomains> {
        val blankParams = mutableListOf<String>()
        if (params.amount.isBlank()) blankParams.add("amount")
//        if (params.file.isBlank()) blankParams.add("file")
        if (params.content.isBlank()) blankParams.add("content")
        if (params.feePayMethod.isBlank()) blankParams.add("feePayMethod")
        if (params.fromAccountNo.isBlank()) blankParams.add("fromAccountNo")
        if (params.toAccountName.isBlank()) blankParams.add("toAccountName")
        if (params.toAccountNo.isBlank()) blankParams.add("toAccountNo")
        if (params.toBankName.isBlank()) blankParams.add("toBankName")
        if (params.username.isBlank()) blankParams.add("username")
//        if (params.fileName.isBlank()) blankParams.add("fileName")
        if (blankParams.isNotEmpty()) {
            return Resource.Error(
                "Empty params: ${blankParams.joinToString(", ")} are blank",
                "133",
                AppException.UnknownException("Empty params: ${blankParams.joinToString(", ")} are blank"),
            )
        }
        return transferRepository.validatePaymentOrderTransfer(params)
    }

    suspend fun nextStatusTransactionByRule(params: NextStatusTransactionByRuleParams): Resource<NextStatusTransactionByRuleDomains> {
        val blankParams = mutableListOf<String>()
        if (params.amount.isBlank()) blankParams.add("amount")
        if (params.creator.isBlank()) blankParams.add("creator")
        if (params.currentUserLevel.isBlank()) blankParams.add("currentUserLevel")
        if (params.customerNumber.isBlank()) blankParams.add("customerNumber")
        if (params.fromAccountNo.isBlank()) blankParams.add("fromAccountNo")
        if (params.serviceCode.isBlank()) blankParams.add("serviceCode")
        if (params.toAccountNo.isBlank()) blankParams.add("toAccountNo")

        if (blankParams.isNotEmpty()) {
            return Resource.Error(
                "Empty params: ${blankParams.joinToString(", ")} are blank",
                "133",
                AppException.UnknownException("Empty params: ${blankParams.joinToString(", ")} are blank"),
            )
        }
        return transferRepository.nextStatusTransactionByRule(params)
    }
    suspend fun getBranch(params: BranchParams): Resource<BranchListDomains> {
        val blankParams = mutableListOf<String>()
        if (params.username.isBlank()) blankParams.add("username")
        if (blankParams.isNotEmpty()) {
            return Resource.Error(
                "Empty params: ${blankParams.joinToString(", ")} are blank",
                "133",
                AppException.UnknownException("Empty params: ${blankParams.joinToString(", ")} are blank"),
            )
        }
        return transferRepository.getBranch(params)
    }
}