package com.vietinbank.core_domain.usecase.soft

import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.soft.InstallSoftOTPParams
import com.vietinbank.core_domain.models.soft.InstallSoftOTPResponse
import com.vietinbank.core_domain.repository.SoftRepository
import com.vietinbank.core_domain.softotp.ISoftManager
import javax.inject.Inject

class InstallOTPUseCase @Inject constructor(
    private val softRepository: SoftRepository,
    private val softManager: ISoftManager,
    private val appConfig: IAppConfigManager,
) {
    suspend operator fun invoke(
        pin: String,
        userId: String?,
    ): Resource<InstallSoftOTPResponse> {
        return softRepository.installSoftOTP(
            InstallSoftOTPParams(
                pin = pin,
                userId = userId,
            ),
        ).also { response ->
            when (response) {
                is Resource.Success -> {
                    appConfig.updateStatusSoft(true)
                    softManager.isAppActive = true
                    softManager.isUserActive = true
                }

                is Resource.Error -> {
                    // suppress
                }
            }
        }
    }
}