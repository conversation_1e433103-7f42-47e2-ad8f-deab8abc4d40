package com.vietinbank.core_domain.usecase.soft

import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.soft.SetSelectUserIdParams
import com.vietinbank.core_domain.models.soft.VBlockSoftOtpParams
import com.vietinbank.core_domain.repository.SoftRepository
import com.vietinbank.core_domain.softotp.ISoftManager
import javax.inject.Inject

class DeleteSoftOTPTokenUseCase @Inject constructor(
    private val softRepository: SoftRepository,
    private val appConfig: IAppConfigManager,
    private val softManager: ISoftManager,
) {
    suspend operator fun invoke(token: String): Resource<Unit> {
        // Tạm thời không call đến blockToken do BE đang lỗi
        return run {
            appConfig.setSoftOTPToken(null)
            appConfig.updateStatusSoft(false)
            softManager.isAppActive = false
            softManager.isUserActive = false
            softRepository.deleteAllTokensSmartOTP()
            softRepository.setSelectedUserId(SetSelectUserIdParams(""))
        }

        return when (
            val blockTokenResponse = softRepository.blockToken(
                VBlockSoftOtpParams(listOf(token)),
            )
        ) {
            is Resource.Success -> {
                /*Xóa token lưu ở DB nếu code = 0 hoặc 14
                Response code # khả năng là có lỗi kết nối hoặc lỗi liên quan đến service của
                Keypass OTP.
                Lúc này hiển thị thông báo lỗi và dừng quy trình
                 */
                if (blockTokenResponse.data.code == "0" || blockTokenResponse.data.code == "14") {
                    appConfig.setSoftOTPToken(null)
                    /*
                    LƯU Ý: User B đăng nhập eFast MB app sau đó gạt ON "Xác thực giao dịch bằng Soft
                    OTP" nhưng không thực hiện hết flow kích hoạt thì thông tin Soft OTP của user A cũng sẽ bị
                    xóa hết
                     */
                    appConfig.updateStatusSoft(false)
                    softManager.isAppActive = false
                    softManager.isUserActive = false
                    softRepository.deleteAllTokensSmartOTP()
                    softRepository.setSelectedUserId(SetSelectUserIdParams(""))
                } else {
                    Resource.Error(
                        code = blockTokenResponse.data.code ?: "999",
                        message = blockTokenResponse.data.message ?: "Service Error",
                    )
                }
            }
            is Resource.Error -> blockTokenResponse
        }
    }
}