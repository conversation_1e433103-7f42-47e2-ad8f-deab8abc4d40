package com.vietinbank.core_domain.usecase.soft

import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.soft.CheckUserExistsParams
import com.vietinbank.core_domain.repository.SoftRepository
import javax.inject.Inject

class CheckUserExistsUseCase @Inject constructor(
    private val softRepository: SoftRepository,
) {
    suspend operator fun invoke(id: String): Resource<Boolean> {
        return softRepository.checkUserExistsById(
            CheckUserExistsParams(id),
        )
    }
}