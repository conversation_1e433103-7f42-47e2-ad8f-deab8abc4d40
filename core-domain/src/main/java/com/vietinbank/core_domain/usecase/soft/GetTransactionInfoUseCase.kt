package com.vietinbank.core_domain.usecase.soft

import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.soft.GetTransactionInfoParams
import com.vietinbank.core_domain.models.soft.GetTransactionInfoResponse
import com.vietinbank.core_domain.repository.SoftRepository
import javax.inject.Inject

class GetTransactionInfoUseCase @Inject constructor(
    private val softRepository: SoftRepository,
) {
    suspend operator fun invoke(
        transactionId: String,
        messageId: String = "",
    ): Resource<GetTransactionInfoResponse> {
        return softRepository.getTransactionInfo(
            GetTransactionInfoParams(
                transactionId = transactionId,
                messageId = messageId,
            ),
        )
    }
}