package com.vietinbank.core_domain.usecase.soft

import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.soft.VerifyPinParam
import com.vietinbank.core_domain.repository.SoftRepository
import javax.inject.Inject

class VerifyPinUseCase @Inject constructor(
    private val softRepository: SoftRepository,
) {
    suspend operator fun invoke(otp: String): Resource<Unit> {
        return softRepository.verifyPin(
            VerifyPinParam(otp),
        )
    }
}