package com.vietinbank.core_domain.usecase.soft

import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.soft.SetSelectUserIdParams
import com.vietinbank.core_domain.repository.SoftRepository
import javax.inject.Inject

class SetCurrentUserUseCase @Inject constructor(
    private val softRepository: SoftRepository,
) {
    suspend operator fun invoke(id: String): Resource<Unit> {
        return softRepository.setSelectedUserId(SetSelectUserIdParams(id))
    }
}