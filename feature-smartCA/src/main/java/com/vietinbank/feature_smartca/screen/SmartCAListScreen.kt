package com.vietinbank.feature_smartca.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_common.models.AppBarAction
import com.vietinbank.core_domain.models.smartCA.SmartCAEsignDomain
import com.vietinbank.core_domain.models.smartCA.SmartCAListActions
import com.vietinbank.core_ui.base.compose.BaseAppBar
import com.vietinbank.core_ui.base.compose.BaseButton
import com.vietinbank.core_ui.base.compose.InfoHorizontalView
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_smartca.SmartCAViewModel

@Composable
fun SmartCAListScreen(
    viewModel: SmartCAViewModel,
    actions: SmartCAListActions?,
) {
    val lstCKS by viewModel.lstCKS.collectAsState()
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(AppColors.itemBackground),
    ) {
        val appBarActions = mutableListOf<AppBarAction>()
        appBarActions.add(
            AppBarAction(
                icon = com.vietinbank.core_ui.R.drawable.ic_home,
                contentDescription = "HOME",
                tint = Color.White,
                onClick = actions?.onHomeClick ?: {},
            ),
        )
        // AppBar
        BaseAppBar(
            title = "Thông tin chi tiết",
            onBackClick = actions?.onBackClick ?: {},
            actions = appBarActions,
        )

        Row(
            modifier = Modifier.padding(horizontal = 16.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(text = "Danh sách chữ ký số", style = getComposeFont(4, 16.sp, Color.White))

            Spacer(modifier = Modifier.weight(1f))

            Box(
                modifier = Modifier
                    .clip(RoundedCornerShape(2.dp))
                    .background(Color.White)
                    .padding(horizontal = 16.dp, vertical = 6.dp)
                    .safeClickable { actions?.onRegisterClick?.invoke() },
            ) {
                Text(
                    text = "+ Đăng ký",
                    style = getComposeFont(6, 12.sp, AppColors.blue02),
                )
            }
        }
        // Content
        lstCKS?.let {
            if (it.isNotEmpty()) {
                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 16.dp, top = 10.dp, end = 16.dp, bottom = 16.dp),
                ) {
                    items(it) {
                        SmartCAItemView(model = it, actions = actions)
                    }
                }
            } else {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 50.dp, start = 32.dp, end = 32.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    Icon(
                        painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_empty),
                        contentDescription = "No transactions",
                        tint = Color.White,
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Text(
                        text = "Danh sách chữ ký số trống",
                        style = getComposeFont(4, 14.sp, Color.White),
                        textAlign = TextAlign.Center,
                    )
                }
            }
        }
    }
}

@Preview
@Composable
fun SmartCAItemView(
    modifier: Modifier = Modifier,
    model: SmartCAEsignDomain? = null,
    actions: SmartCAListActions? = null,
) {
    Box(
        modifier = modifier
            .padding(bottom = 10.dp)
            .clip(RoundedCornerShape(2.dp))
            .background(Color.White)
            .padding(start = 14.dp, top = 20.dp, end = 14.dp, bottom = 10.dp),
    ) {
        Column {
            InfoHorizontalView(
                title = "Tên user",
                titleColor = AppColors.grey08,
                value = model?.userName,
                valueFont = 4,
                valueColor = AppColors.grey09,
            )

            InfoHorizontalView(
                title = "Số serial CKS",
                titleColor = AppColors.grey08,
                value = model?.serial,
                valueFont = 4,
                valueColor = AppColors.grey09,
                modifier = Modifier.padding(top = 20.dp),
            )

            InfoHorizontalView(
                title = "Loại CKS",
                titleColor = AppColors.grey08,
                value = when (model?.esignType) {
                    "1" -> "Cá nhân"
                    "2" -> "Tổ chức"
                    else -> ""
                },
                valueFont = 4,
                valueColor = AppColors.grey09,
                modifier = Modifier.padding(top = 20.dp),
            )

            InfoHorizontalView(
                title = "Vai trò CKS",
                titleColor = AppColors.grey08,
                value = model?.roleDesc?.firstOrNull(),
                valueFont = 4,
                valueColor = AppColors.grey09,
                modifier = Modifier.padding(top = 20.dp),
            )

            InfoHorizontalView(
                title = "Thời gian đăng ký",
                titleColor = AppColors.grey08,
                value = model?.createDate,
                valueFont = 4,
                valueColor = AppColors.grey09,
                modifier = Modifier.padding(top = 20.dp),
            )

            InfoHorizontalView(
                title = "Thời hạn hiệu lực của CKS",
                titleColor = AppColors.grey08,
                value = model?.endtDate,
                valueFont = 4,
                valueColor = AppColors.grey09,
                modifier = Modifier.padding(top = 20.dp),
            )

            InfoHorizontalView(
                title = "Tên tổ chức cấp CKS",
                titleColor = AppColors.grey08,
                value = model?.esignFrom,
                valueFont = 4,
                valueColor = AppColors.grey09,
                modifier = Modifier.padding(top = 20.dp),
            )

            InfoHorizontalView(
                title = "Trạng thái đăng ký với NH",
                titleColor = AppColors.grey08,
                value = model?.statusName,
                valueFont = 4,
                valueColor = AppColors.grey09,
                modifier = Modifier.padding(top = 20.dp),
            )

            // Xác nhận button
            Row(
                modifier = Modifier
                    .padding(top = 20.dp)
                    .fillMaxWidth(),
            ) {
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .height(48.dp)
                        .clip(RoundedCornerShape(2.dp))
                        .background(AppColors.grey200)
                        .safeClickable { actions?.onDetailClick?.invoke(model?.mtId ?: "") },
                    contentAlignment = Alignment.Center,
                ) {
                    Text(
                        text = "Xem chi tiết",
                        style = getComposeFont(5, 14.sp, AppColors.blue02),
                    )
                }

                when (model?.statusCode) {
                    "05" -> {
                        BaseButton(
                            modifier = Modifier
                                .padding(start = 8.dp)
                                .weight(1f)
                                .safeClickable {
                                    actions?.onCancelClick?.invoke(model.mtId ?: "")
                                },
                            text = "Đóng",
                        )
                    }

                    "06" -> {
                        BaseButton(
                            modifier = Modifier
                                .padding(start = 8.dp)
                                .weight(1f)
                                .safeClickable {
                                    actions?.onDeleteClick?.invoke(model.mtId ?: "")
                                },
                            text = "Xóa",
                        )
                    }

                    else -> {}
                }
            }
        }
    }
}