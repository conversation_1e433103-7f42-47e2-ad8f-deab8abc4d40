package com.vietinbank.feature_smartca.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.models.AppBarAction
import com.vietinbank.core_domain.models.smartCA.SmartCADetailActions
import com.vietinbank.core_ui.base.compose.BaseAppBar
import com.vietinbank.core_ui.base.compose.InfoHorizontalView
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.feature_smartca.SmartCAViewModel

@Composable
fun SmartCADetailScreen(
    viewModel: SmartCAViewModel,
    actions: SmartCADetailActions? = null,
) {
    val detailCKS by viewModel.detailCKS.collectAsState()
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(AppColors.itemBackground),
    ) {
        val appBarActions = mutableListOf<AppBarAction>()
        appBarActions.add(
            AppBarAction(
                icon = com.vietinbank.core_ui.R.drawable.ic_home,
                contentDescription = "HOME",
                tint = Color.White,
                onClick = actions?.onHomeClick ?: {},
            ),
        )
        // AppBar
        BaseAppBar(
            title = "Chi tiết đăng ký chữ ký số",
            onBackClick = actions?.onBackClick ?: {},
            actions = appBarActions,
        )

        // Content
        Column(
            modifier = Modifier
                .fillMaxSize()
                .weight(1f)
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
        ) {
            viewModel.getDisplayGeneral(detailCKS?.data)?.let { lst ->
                Text(text = "Thông tin chung", style = getComposeFont(4, 16.sp, Color.White))

                Column(
                    modifier = Modifier
                        .padding(top = 10.dp)
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(2.dp))
                        .background(Color.White)
                        .padding(start = 14.dp, top = 0.dp, end = 14.dp, bottom = 20.dp),
                ) {
                    lst.forEach {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 20.dp),
                            title = it.title,
                            titleColor = it.titleColor ?: AppColors.grey08,
                            value = it.value,
                            valueColor = it.valueColor ?: AppColors.grey09,
                        )
                    }
                }
            }

            viewModel.getDisplayInfoCKS(detailCKS?.data)?.let { lst ->
                Text(
                    text = "Thông tin chữ ký số",
                    style = getComposeFont(4, 16.sp, Color.White),
                    modifier = Modifier.padding(vertical = 10.dp),
                )
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(2.dp))
                        .background(Color.White)
                        .padding(start = 14.dp, top = 0.dp, end = 14.dp, bottom = 20.dp),
                ) {
                    lst.forEach {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 20.dp),
                            title = it.title,
                            titleColor = it.titleColor ?: AppColors.grey08,
                            value = it.value,
                            valueColor = it.valueColor ?: AppColors.grey09,
                            onClick = {
                                when (it.clickDirect) {
                                    Tags.TYPE_FILE -> {
                                        actions?.onDownloadClick?.invoke(
                                            "${detailCKS?.data?.mtId}_${detailCKS?.data?.files?.firstOrNull()?.id}",
                                        )
                                    }

                                    else -> null
                                }
                            },
                        )
                    }
                }
            }
        }
    }
}