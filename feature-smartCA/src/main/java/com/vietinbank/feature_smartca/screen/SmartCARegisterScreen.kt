package com.vietinbank.feature_smartca.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_common.models.AppBarAction
import com.vietinbank.core_domain.models.smartCA.SmartCARegisterActions
import com.vietinbank.core_ui.base.compose.BaseAppBar
import com.vietinbank.core_ui.base.compose.BaseButton
import com.vietinbank.core_ui.base.compose.InfoHorizontalView
import com.vietinbank.core_ui.base.compose.InfoVerticalView
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_smartca.SmartCAViewModel

@Composable
fun SmartCARegisterScreen(
    viewModel: SmartCAViewModel,
    actions: SmartCARegisterActions? = null,
) {
    val certModel by viewModel.certSelected.collectAsState()
    val userModel by viewModel.userSelected.collectAsState()
    val serviceModel by viewModel.serviceSelected.collectAsState()
    val roleModel by viewModel.roleSelected.collectAsState()
    val branchModel by viewModel.branchSelected.collectAsState()
    var isShowMore by remember { mutableStateOf(false) }
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(AppColors.itemBackground),
    ) {
        val appBarActions = mutableListOf<AppBarAction>()
        appBarActions.add(
            AppBarAction(
                icon = com.vietinbank.core_ui.R.drawable.ic_home,
                contentDescription = "HOME",
                tint = Color.White,
                onClick = actions?.onHomeClick ?: {},
            ),
        )
        // AppBar
        BaseAppBar(
            title = "Chi tiết đăng ký chữ ký số",
            onBackClick = actions?.onBackClick ?: {},
            actions = appBarActions,
        )
        // Content
        Column(
            modifier = Modifier
                .fillMaxSize()
                .weight(1f)
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
        ) {
            Text(
                text = "Thông tin khách hàng",
                style = getComposeFont(4, 16.sp, Color.White),
            )

            Column(
                modifier = Modifier
                    .padding(top = 16.dp)
                    .clip(RoundedCornerShape(4.dp))
                    .background(Color.White)
                    .padding(16.dp),
            ) {
                InfoVerticalView(
                    title = "Tài khoản đăng ký CKS",
                    value = userModel?.username,
                ) {
                    actions?.onUserClick?.invoke()
                }

                InfoVerticalView(
                    modifier = Modifier.padding(top = 20.dp),
                    title = "CMND/CCCD",
                    value = userModel?.idNumber,
                    icon = null,
                )

                InfoHorizontalView(
                    modifier = Modifier.padding(top = 20.dp),
                    title = "Họ tên",
                    value = userModel?.fullName,
                    valueColor = AppColors.grey07,
                )

                InfoHorizontalView(
                    modifier = Modifier.padding(top = 12.dp),
                    title = "Chức vụ",
                    value = userModel?.titleName,
                    valueColor = AppColors.grey07,
                )

                InfoVerticalView(
                    title = "Dịch vụ sử dụng CKS",
                    value = serviceModel?.value,
                    modifier = Modifier.padding(top = 20.dp),
                ) {
                    actions?.onSeviceClick?.invoke()
                }

                InfoVerticalView(
                    title = "Vai trò CKS",
                    value = roleModel?.value,
                    modifier = Modifier.padding(top = 20.dp),
                ) {
                    actions?.onRoleClick?.invoke()
                }
            }

            Box(
                modifier = Modifier
                    .padding(top = 8.dp)
                    .clip(RoundedCornerShape(4.dp))
                    .background(Color.White)
                    .padding(16.dp),
            ) {
                InfoVerticalView(
                    title = "Chi nhánh xử lý",
                    value = branchModel?.branchName,
                ) {
                    actions?.onBranchClick?.invoke()
                }
            }

            certModel?.let {
                Text(
                    text = "Thông tin CKS",
                    style = getComposeFont(4, 16.sp, Color.White),
                    modifier = Modifier.padding(top = 8.dp),
                )

                Column(
                    modifier = Modifier
                        .padding(top = 8.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(16.dp),
                ) {
                    InfoHorizontalView(
                        title = "Họ tên",
                        value = certModel?.name,
                        valueColor = AppColors.grey07,
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 16.dp),
                        title = "Số serial",
                        value = certModel?.serial,
                        valueColor = AppColors.grey07,
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 16.dp),
                        title = "Ngày hết hạn",
                        value = certModel?.endDate,
                        valueColor = AppColors.grey07,
                    )

                    if (isShowMore) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 16.dp),
                            title = "Loại CKS",
                            value = certModel?.esignType,
                            valueColor = AppColors.grey07,
                        )

                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 16.dp),
                            title = "Nhà cung cấp CKS",
                            value = certModel?.esignFrom,
                            valueColor = AppColors.grey07,
                        )

                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 16.dp),
                            title = "ID chủ sở hữu CKS",
                            value = certModel?.idEsign,
                            valueColor = AppColors.grey07,
                        )
                    }

                    Text(
                        text = if (isShowMore) {
                            "Thu gọn"
                        } else {
                            "Xem thêm"
                        },
                        style = getComposeFont(5, 12.sp, AppColors.blue02),
                        modifier = Modifier
                            .padding(top = 16.dp)
                            .safeClickable { isShowMore = !isShowMore },
                    )
                }
            }
        }

        BaseButton(
            Modifier
                .padding(16.dp)
                .safeClickable {
                    if (certModel == null) {
                        actions?.onCertClick?.invoke()
                    } else {
                        actions?.onNextClick?.invoke()
                    }
                },
            if (certModel == null) {
                "Lấy thông tin"
            } else {
                "Tiếp tục"
            },
        )
    }
}