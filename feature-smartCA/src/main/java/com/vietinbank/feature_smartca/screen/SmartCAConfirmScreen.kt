package com.vietinbank.feature_smartca.screen

import android.text.TextUtils
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_domain.models.smartCA.SmartCAConfirmActions
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.compose.BaseAppBar
import com.vietinbank.core_ui.base.compose.BaseButton
import com.vietinbank.core_ui.base.compose.InfoHorizontalView
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_smartca.SmartCAViewModel

@Composable
fun SmartCAConfirmScreen(
    viewModel: SmartCAViewModel,
    actions: SmartCAConfirmActions?,
) {
    var isCheckTerm by remember { mutableStateOf(false) }
    val registerCKS by viewModel.registerCKSState.collectAsState(null)

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(AppColors.itemBackground),
    ) {
        // AppBar
        BaseAppBar(
            title = "Xác nhận",
            onBackClick = actions?.onBackClick ?: {},
        )

        Box(
            modifier = Modifier
                .weight(1f)
                .padding(16.dp),
        ) {
            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(2.dp))
                    .background(Color.White)
                    .padding(horizontal = 14.dp, vertical = 20.dp),
            ) {
                viewModel.getRegisterConfirm(registerCKS)?.let { lst ->
                    itemsIndexed(lst) { index, it ->
                        if (index != 0) {
                            Spacer(modifier = Modifier.height(20.dp))
                        }
                        InfoHorizontalView(
                            title = it.title,
                            titleColor = it.titleColor ?: AppColors.grey08,
                            value = it.value,
                            valueColor = it.valueColor ?: AppColors.grey09,
                            onClick = {
                                if (!TextUtils.isEmpty(it.clickDirect)) {
                                    actions?.onFileClick?.invoke(registerCKS?.toaDo?.fileId)
                                } else {
                                    null
                                }
                            },
                        )
                    }
                }
            }
        }

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White, RoundedCornerShape(topEnd = 2.dp, topStart = 2.dp))
                .padding(16.dp),
        ) {
            Row(
                Modifier
                    .fillMaxWidth()
                    .safeClickable {
                        isCheckTerm = !isCheckTerm
                    },
            ) {
                Image(
                    painter = painterResource(
                        id = if (isCheckTerm) {
                            R.drawable.ic_checkbox
                        } else {
                            R.drawable.ic_uncheckbox
                        },
                    ),
                    contentDescription = null,
                )

                Text(
                    text = "Xác nhận và đồng ý điều khoản điều kiện trên Giấy đăng ký kiêm cam kết sử dụng chữ ký số",
                    style = getComposeFont(4, 14.sp, AppColors.contentSecondary),
                    modifier = Modifier.padding(start = 8.dp),
                )
            }

            BaseButton(
                Modifier
                    .padding(top = 16.dp)
                    .safeClickable { actions?.onNextClick?.invoke(isCheckTerm) },
                "Ký số",
            )
        }
    }
}
