package com.vietinbank.feature_smartca.screen

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.boundsInRoot
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.models.AppBarAction
import com.vietinbank.core_domain.models.smartCA.SmartCAResultActions
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.compose.BaseAppBar
import com.vietinbank.core_ui.base.compose.BaseButton
import com.vietinbank.core_ui.base.compose.InfoHorizontalView
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.ScreenshotUtil
import com.vietinbank.core_ui.utils.ScreenshotUtil.trackBounds
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_smartca.SmartCAViewModel
import kotlinx.coroutines.launch

@Composable
fun SmartCAResultScreen(
    viewModel: SmartCAViewModel,
    actions: SmartCAResultActions?,
) {
    val registerCKSState by viewModel.registerCKSState.collectAsState()
    val coroutineScope = rememberCoroutineScope()
    val rootView = LocalView.current
    val context = LocalContext.current

    // State to track the bounds of the content area for screenshot
    var contentBounds by remember { mutableStateOf<Rect?>(null) }
    // Custom actions with screenshot logic
    val downloadAction: () -> Unit = {
        coroutineScope.launch {
            contentBounds?.let { bounds ->
                val bitmap = ScreenshotUtil.captureScreenshot(rootView, bounds, context)
                bitmap?.let { capturedBitmap ->
                    val uri = ScreenshotUtil.saveBitmapToStorage(context, capturedBitmap)
                    uri?.let {
                        // Notify success or pass URI back for further processing
                        actions?.onDownloadClick?.invoke("Đã lưu hình ảnh thành công")
                    } ?: actions?.onDownloadClick?.invoke("Không thể lưu hình ảnh giao dịch")
                } ?: actions?.onDownloadClick?.invoke("Không thể chụp ảnh giao dịch")
            }
        }
    }

    val shareAction: () -> Unit = {
        coroutineScope.launch {
            contentBounds?.let { bounds ->
                val bitmap = ScreenshotUtil.captureScreenshot(rootView, bounds, context)
                bitmap?.let { capturedBitmap ->
                    ScreenshotUtil.shareBitmap(context, capturedBitmap)
                } ?: actions?.onDownloadClick?.invoke("Không thể chụp ảnh giao dịch")
            }
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(AppColors.itemBackground),
    ) {
        val appBarActions = mutableListOf<AppBarAction>()
        appBarActions.add(
            AppBarAction(
                icon = R.drawable.ic_download,
                contentDescription = "Download",
                onClick = { downloadAction() },
                tint = Color.Unspecified,
            ),
        )
        appBarActions.add(
            AppBarAction(
                icon = com.vietinbank.core_ui.R.drawable.ic_share,
                contentDescription = "SHARE",
                onClick = { shareAction() },
                tint = Color.Unspecified,
            ),
        )
        BaseAppBar(
            title = "Kết quả",
            onBackClick = actions?.onHomeClick ?: {},
            isBackHome = true,
            actions = appBarActions,
        )

        LazyColumn(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
                .padding(16.dp)
                .clip(RoundedCornerShape(2.dp))
                .background(Color.White)
                .padding(24.dp)
                .then(
                    Modifier.trackBounds { coordinates ->
                        contentBounds = coordinates.boundsInRoot()
                    },
                ),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            item {
                Image(painter = painterResource(R.drawable.ic_approved), contentDescription = null)

                Spacer(modifier = Modifier.height(16.dp))

                Image(
                    painter = painterResource(R.drawable.ic_logo_horizontal),
                    contentDescription = null,
                )

                Spacer(modifier = Modifier.height(10.dp))

                Text(
                    text = registerCKSState?.status?.message
                        ?: "Giao dịch số ${registerCKSState?.esignRegister?.mtId ?: ""} được đẩy đến ngân hàng thành công.",
                    style = getComposeFont(4, 12.sp, AppColors.grey09),
                    fontStyle = FontStyle.Italic,
                    textAlign = TextAlign.Center,
                )
            }
            viewModel.getRegisterResult(registerCKSState)?.let { lst ->
                items(lst) {
                    InfoHorizontalView(
                        title = it.title,
                        titleColor = it.titleColor ?: AppColors.grey08,
                        value = it.value,
                        valueColor = it.valueColor ?: AppColors.grey09,
                        modifier = Modifier
                            .padding(top = 20.dp)
                            .safeClickable {
                                if (it.clickDirect == Tags.TYPE_FILE) {
                                    // tai file
                                    actions?.onFileClick?.invoke(registerCKSState?.toaDo?.fileId ?: "")
                                }
                            },
                    )
                }
            }
        }

        BaseButton(
            Modifier
                .padding(16.dp)
                .safeClickable { actions?.onHomeClick?.invoke() },
            "Về trang chủ",
        )
    }
}
