package com.vietinbank.feature_smartca.di

import android.os.Bundle
import android.os.Parcelable
import androidx.navigation.NavController
import androidx.navigation.NavOptions
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.feature_smartca.R
import java.io.Serializable

class ISmartCANavigatorImpl(
    private val navController: NavController,
) : ISmartCANavigator {

    override fun popBackStack() {
        navController.popBackStack()
    }

    override fun toDetailCKS(vararg pair: Pair<String, Any?>) {
        navController.navigate(
            R.id.smartCADetailFragment,
            createBundle(*pair),
            createSlideNavOptions(),
        )
    }

    override fun toRegisterCKS(vararg pair: Pair<String, Any?>) {
        navController.navigate(
            R.id.smartCARegisterFragment,
            createBundle(*pair),
            createSlideNavOptions(),
        )
    }

    override fun toConfirmCKS(vararg pair: Pair<String, Any?>) {
        navController.navigate(
            R.id.smartCAConfirmFragment,
            createBundle(*pair),
            createSlideNavOptions(),
        )
    }

    override fun toResultCKS(vararg pair: Pair<String, Any?>) {
        navController.navigate(
            R.id.smartCAResultFragment,
            createBundle(*pair),
            createSlideNavOptions(),
        )
    }

    override fun popBackGraph() {
        navController.popBackStack(R.id.smartca_nav, false)
    }

    /**
     * Tạo NavOptions cho màn forward:
     * - enterAnim, exitAnim, popEnterAnim, popExitAnim
     */
    private fun createSlideNavOptions(): NavOptions {
        val builder = NavOptions.Builder()
            .setEnterAnim(com.vietinbank.core_common.R.anim.core_slide_in_right)
            .setExitAnim(com.vietinbank.core_common.R.anim.core_slide_out_left)
            .setPopEnterAnim(com.vietinbank.core_common.R.anim.core_slide_in_left)
            .setPopExitAnim(com.vietinbank.core_common.R.anim.core_slide_out_right)
        return builder.build()
    }

    private fun createBundle(vararg pair: Pair<String, Any?>): Bundle {
        val bundle = Bundle()
        pair.forEach {
            val first = it.first
            val second = it.second
            second?.let {
                when (second) {
                    is Boolean -> bundle.putBoolean(first, second)
                    is Byte -> bundle.putByte(first, second)
                    is Short -> bundle.putShort(first, second)
                    is Int -> bundle.putInt(first, second)
                    is Long -> bundle.putLong(first, second)
                    is Float -> bundle.putFloat(first, second)
                    is Double -> bundle.putDouble(first, second)
                    is Char -> bundle.putChar(first, second)
                    is String -> bundle.putString(first, second)
                    is CharSequence -> bundle.putCharSequence(first, second)
                    is Parcelable -> bundle.putParcelable(first, second)
                    is Serializable -> bundle.putSerializable(first, second)
                    else -> bundle.putString(first, Utils.g().provideGson().toJson(second))
                }
            }
        }
        return bundle
    }
}