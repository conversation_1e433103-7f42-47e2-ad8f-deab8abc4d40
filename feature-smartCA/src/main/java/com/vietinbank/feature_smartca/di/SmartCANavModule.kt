package com.vietinbank.feature_smartca.di

import androidx.navigation.NavController
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.FragmentComponent

@Module
@InstallIn(FragmentComponent::class)
object SmartCANavModule {
    @Provides
    fun provideSmartCANavigator(
        navController: NavController,
    ): ISmartCANavigator {
        return ISmartCANavigatorImpl(navController)
    }
}
