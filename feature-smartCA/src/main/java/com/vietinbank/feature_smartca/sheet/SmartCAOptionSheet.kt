package com.vietinbank.feature_smartca.sheet

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import com.vietinbank.core_ui.base.BaseBottomSheetFragment
import com.vietinbank.feature_smartca.R
import com.vietinbank.feature_smartca.adapter.SmartCAAdapter
import com.vietinbank.feature_smartca.databinding.SheetSmartCaOptionBinding

class SmartCAOptionSheet(
    val screenType: Int = 0,
    val lstDefault: List<Any> = mutableListOf(),
) : BaseBottomSheetFragment() {
    private var _binding: SheetSmartCaOptionBinding? = null
    private val binding get() = _binding!!
    private val adapter: SmartCAAdapter by lazy { SmartCAAdapter() }

    private var onItemClick: ((Any) -> Unit)? = null

    companion object {
        const val SERVICE = 0
        const val ROLE = 1
        const val CERT = 2
        const val BRANCH = 3
        const val USER = 4
    }

    fun setOnItemClick(listener: ((Any) -> Unit)?) {
        onItemClick = listener
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        _binding = SheetSmartCaOptionBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun initView() {
        with(binding) {
            when (screenType) {
                USER -> {
                    ivTitle.setImageResource(R.drawable.s_ic_user)
                    tvTitle.text = "Tài khoản đăng ký CKS"
                }
                SERVICE -> {
                    ivTitle.setImageResource(R.drawable.s_ic_user)
                    tvTitle.text = "Dịch vụ sử dụng CKS"
                }

                ROLE -> {
                    ivTitle.setImageResource(R.drawable.s_ic_user)
                    tvTitle.text = "Vai trò CKS"
                }

                CERT -> {
                    ivTitle.setImageResource(R.drawable.s_ic_document)
                    tvTitle.text = "Danh sách CKS"
                }

                else -> {
                    ivTitle.setImageResource(R.drawable.s_ic_locate)
                    tvTitle.text = "Chi nhánh"
                }
            }
            adapter.apply {
                setData(lstDefault)
                setOnItemClick {
                    onItemClick?.invoke(it)
                    edtSearch.setText("")
                    dismiss()
                }
            }

            llSearch.isVisible = lstDefault.size > 10
            rcvAccount.adapter = adapter
        }
    }

    override fun initData() {
    }

    override fun initListener() {
        binding.edtSearch.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                adapter.filter.filter(s.toString())
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        onItemClick = null
    }
}