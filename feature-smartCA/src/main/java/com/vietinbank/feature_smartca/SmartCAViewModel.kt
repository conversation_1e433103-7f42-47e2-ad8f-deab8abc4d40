package com.vietinbank.feature_smartca

import android.text.TextUtils
import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_common.constants.DataSourceProperties
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_data.models.response.DisplayModelUI
import com.vietinbank.core_domain.models.checker.GetDownloadFileIDDomain
import com.vietinbank.core_domain.models.checker.GetDownloadFileIDParams
import com.vietinbank.core_domain.models.smartCA.BranchDomain
import com.vietinbank.core_domain.models.smartCA.SignCallState
import com.vietinbank.core_domain.models.smartCA.SmartCABranchDomain
import com.vietinbank.core_domain.models.smartCA.SmartCACertVNPTPDomain
import com.vietinbank.core_domain.models.smartCA.SmartCACertVNPTParams
import com.vietinbank.core_domain.models.smartCA.SmartCADataDomain
import com.vietinbank.core_domain.models.smartCA.SmartCADetailDomain
import com.vietinbank.core_domain.models.smartCA.SmartCADetailParams
import com.vietinbank.core_domain.models.smartCA.SmartCAEsignDomain
import com.vietinbank.core_domain.models.smartCA.SmartCAEsignRegisterDomain
import com.vietinbank.core_domain.models.smartCA.SmartCAGetParamDomain
import com.vietinbank.core_domain.models.smartCA.SmartCAGetParams
import com.vietinbank.core_domain.models.smartCA.SmartCAParams
import com.vietinbank.core_domain.models.smartCA.SmartCARegisterDomain
import com.vietinbank.core_domain.models.smartCA.SmartCARegisterParams
import com.vietinbank.core_domain.models.smartCA.SmartCARoleDomain
import com.vietinbank.core_domain.models.smartCA.SmartCAServiceDomain
import com.vietinbank.core_domain.models.smartCA.SmartCAUpdateDomain
import com.vietinbank.core_domain.models.smartCA.SmartCAUserDomain
import com.vietinbank.core_domain.usecase.checker.CheckerUserCase
import com.vietinbank.core_domain.usecase.smartca.SmartCAUseCase
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SmartCAViewModel
@Inject constructor(
    private val checkerUserCase: CheckerUserCase,
    private val useCase: SmartCAUseCase,
    private val moneyHelper: MoneyHelper,
    private val dataSourceProperties: DataSourceProperties,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
) : BaseViewModel() {
    private var isChecking = false
    fun isChecking() = isChecking

    // danh sach cks
    private val _lstCKS = MutableStateFlow<List<SmartCAEsignDomain>?>(null)
    val lstCKS = _lstCKS.asStateFlow()

    fun getListCKS() = launchJob {
        val params = SmartCAParams(userProf.getUserName(), userProf.getRoleId())
        val res = useCase.getListCKS(params)
        handleResource(res) { data ->
            _lstCKS.value = data.signInfoList ?: emptyList()
        }
    }

    // chi tiet cks
    private val _detailCKS = MutableStateFlow<SmartCADetailDomain?>(null)
    val detailCKS: StateFlow<SmartCADetailDomain?> = _detailCKS.asStateFlow()

    fun getDetailCKS(mtId: String) = launchJob {
        val params =
            SmartCADetailParams("SCA", mtId, userProf.getUserName(), "", userProf.getRoleId())
        val res = useCase.getDetailCKS(params)
        handleResource(res) { data ->
            _detailCKS.value = data
        }
    }

    // dang ky
    private val _registerCKS = MutableSharedFlow<SmartCARegisterDomain?>()
    val registerCKS = _registerCKS.asSharedFlow()

    private var mtIdSign = ""
    private val _resultMessage = MutableStateFlow<String?>(null)
    val resultMessage = _resultMessage.asStateFlow()
    fun onResultMessage(message: String?) {
        _resultMessage.value = message
    }

    private val _registerCKSState = MutableStateFlow<SmartCARegisterDomain?>(null)
    val registerCKSState = _registerCKSState.asStateFlow()
    fun onChangeRegisterCKS(model: SmartCARegisterDomain?) {
        _registerCKSState.value = model
    }

    fun registerCKS(typeRegister: String? = null) = launchJob {
        isChecking = typeRegister == "CHECK"
        val params = SmartCARegisterParams(
            mtId = mtIdSign,
            username = userProf.getUserName(),
            userRegister = registerCKSState.value?.esignRegister?.creator
                ?: userSelected.value?.username,
            serviceType = registerCKSState.value?.esignRegister?.serviceType
                ?: serviceSelected.value?.key,
            roleList = roleSelected.value?.key,
            fullName = registerCKSState.value?.esignRegister?.name ?: userSelected.value?.fullName,
            no = registerCKSState.value?.esignRegister?.no ?: userSelected.value?.idNumber,
            esignType = "1",
            serialNo = registerCKSState.value?.esignRegister?.serial ?: certSelected.value?.serial,
            startDate = registerCKSState.value?.esignRegister?.startDate
                ?: certSelected.value?.startDate,
            endDate = registerCKSState.value?.esignRegister?.endDate ?: certSelected.value?.endDate,
            esignFrom = registerCKSState.value?.esignRegister?.esignFrom
                ?: certSelected.value?.esignFrom,
            esignName = registerCKSState.value?.esignRegister?.nameEsign
                ?: certSelected.value?.nameEsign,
            idEsign = registerCKSState.value?.esignRegister?.idEsign ?: certSelected.value?.idEsign,
            branch = registerCKSState.value?.esignRegister?.branch
                ?: branchSelected.value?.branchId,
            role = userProf.getRoleId(),
            ou = "",
            type = typeRegister,
        )
        val res = useCase.registerCKS(params)
        handleResource(res) { data ->
            data.esignRegister?.mtId?.let {
                mtIdSign = it
            }
            _registerCKS.emit(data)
        }
    }

    fun registerCKSSilent(typeRegister: String? = null, onError: ((AppException) -> Unit)?) =
        launchJobSilent {
            isChecking = typeRegister == "CHECK"
            val params = SmartCARegisterParams(
                mtId = mtIdSign,
                username = userProf.getUserName(),
                userRegister = registerCKSState.value?.esignRegister?.creator
                    ?: userSelected.value?.username,
                serviceType = registerCKSState.value?.esignRegister?.serviceType
                    ?: serviceSelected.value?.key,
                roleList = roleSelected.value?.key,
                fullName = registerCKSState.value?.esignRegister?.name
                    ?: userSelected.value?.fullName,
                no = registerCKSState.value?.esignRegister?.no ?: userSelected.value?.idNumber,
                esignType = "1",
                serialNo = registerCKSState.value?.esignRegister?.serial
                    ?: certSelected.value?.serial,
                startDate = registerCKSState.value?.esignRegister?.startDate
                    ?: certSelected.value?.startDate,
                endDate = registerCKSState.value?.esignRegister?.endDate
                    ?: certSelected.value?.endDate,
                esignFrom = registerCKSState.value?.esignRegister?.esignFrom
                    ?: certSelected.value?.esignFrom,
                esignName = registerCKSState.value?.esignRegister?.nameEsign
                    ?: certSelected.value?.nameEsign,
                idEsign = registerCKSState.value?.esignRegister?.idEsign
                    ?: certSelected.value?.idEsign,
                branch = registerCKSState.value?.esignRegister?.branch
                    ?: branchSelected.value?.branchId,
                role = userProf.getRoleId(),
                ou = "",
                type = typeRegister,
            )
            val res = useCase.registerCKS(params)
            handleResourceSilent(
                resource = res,
                onSuccess = { data ->
                    data.esignRegister?.mtId?.let {
                        mtIdSign = it
                    }
                    _registerCKS.emit(data)
                },
                onError = onError,
            )
        }

    // update CKS
    private val _actionCKS = MutableSharedFlow<SmartCAUpdateDomain>()
    val actionCKS: SharedFlow<SmartCAUpdateDomain> = _actionCKS.asSharedFlow()

    fun updateCKS(
        mtId: String,
        action: String,
    ) = launchJob {
        val params = SmartCADetailParams(
            "SCA",
            mtId,
            userProf.getUserName(),
            action,
            userProf.getRoleId(),
        )
        val res = useCase.updateCKS(params)
        handleResource(res) { data ->
            _actionCKS.emit(data)
        }
    }

    // get param
    // role selected
    private val _roleSelected = MutableStateFlow<SmartCARoleDomain?>(null)
    val roleSelected: StateFlow<SmartCARoleDomain?> = _roleSelected.asStateFlow()

    fun onChangeRole(item: SmartCARoleDomain) {
        _roleSelected.value = item
    }

    // cert selected
    private val _certSelected = MutableStateFlow<SmartCAEsignRegisterDomain?>(null)
    val certSelected: StateFlow<SmartCAEsignRegisterDomain?> = _certSelected.asStateFlow()

    fun onChangeCert(item: SmartCAEsignRegisterDomain) {
        _certSelected.value = item
    }

    // service selected
    private val _serviceSelected = MutableStateFlow<SmartCAServiceDomain?>(null)
    val serviceSelected: StateFlow<SmartCAServiceDomain?> = _serviceSelected.asStateFlow()

    fun onChangeService(item: SmartCAServiceDomain) {
        _serviceSelected.value = item
        // update vai tro CKS
        lstRole.clear()
        lstRole.addAll(item.roleList ?: emptyList())

        // update chi nhanh tuong ung voi dich vu
        lstBranch.clear()
        lstBranch.addAll(
            if ("2" == item.key) {
                branchDomain?.listLoans
            } else {
                branchDomain?.listSavings
            } ?: emptyList(),
        )
    }

    // user selected
    private val _userSelected = MutableStateFlow<SmartCAUserDomain?>(null)
    val userSelected: StateFlow<SmartCAUserDomain?> = _userSelected.asStateFlow()

    fun onChangeUser(item: SmartCAUserDomain?) {
        _userSelected.value = item
    }

    // branch selected
    private val _branchSelected = MutableStateFlow<BranchDomain?>(null)
    val branchSelected: StateFlow<BranchDomain?> = _branchSelected.asStateFlow()

    fun onChangeBranch(item: BranchDomain) {
        _branchSelected.value = item
    }

    private val _paramsCKS = MutableSharedFlow<SmartCAGetParamDomain?>()
    val paramsCKS = _paramsCKS.asSharedFlow()
    var lstService = mutableListOf<SmartCAServiceDomain>()
    var lstRole = mutableListOf<SmartCARoleDomain>()
    var lstUser = mutableListOf<SmartCAUserDomain>()

    fun getParamsCKS(type: String? = null) = launchJob {
        val params = SmartCAGetParams(userProf.getRoleId(), type ?: "", userProf.getUserName())
        val res = useCase.getParamCKS(params)
        handleResource(res) { data ->
            _paramsCKS.emit(data)
            lstService.clear()
            lstService.addAll(data.serviceTypeListFull ?: emptyList())
            lstUser.clear()
            lstUser.addAll(data.userList ?: emptyList())
            onChangeUser(
                data.userList?.firstOrNull { user ->
                    !TextUtils.isEmpty(userProf.getUserName()) && user.username == userProf.getUserName()
                },
            )
        }
    }

    // chi nhanh xu ly
    var lstBranch = mutableListOf<BranchDomain>()
    var branchDomain: SmartCABranchDomain? = null
    private val _branchCKS = MutableSharedFlow<SmartCABranchDomain?>()
    val branchCKS = _branchCKS.asSharedFlow()

    fun getBranch() = launchJob {
        val params = SmartCAParams(userProf.getUserName(), userProf.getRoleId())
        val res = useCase.getBranchCKS(params)
        handleResource(res) { data ->
            branchDomain = data
            lstBranch.clear()
            lstBranch.addAll(
                if ("2" == serviceSelected.value?.key) {
                    branchDomain?.listLoans
                } else {
                    branchDomain?.listSavings
                } ?: emptyList(),
            )
            _branchCKS.emit(data)
        }
    }

    // cert from vnpt
    var lstCertVNPT = mutableListOf<SmartCAEsignRegisterDomain>()
    private val _certVNPT = MutableSharedFlow<SmartCACertVNPTPDomain?>()
    val certVNPT = _certVNPT.asSharedFlow()

    fun getCertVNPT() = launchJob {
        val params = SmartCACertVNPTParams(
            userSelected.value?.idNumber,
            userProf.getRoleId(),
            userProf.getUserName(),
        )
        val res = useCase.getCertVNPT(params)
        handleResource(res) { data ->
            lstCertVNPT.clear()
            lstCertVNPT.addAll(data.esignRegisterList ?: emptyList())
            _certVNPT.emit(data)
        }
    }

    // show display
    fun getDisplayGeneral(model: SmartCADataDomain? = null): List<DisplayModelUI>? {
        if (model == null) return null
        return listOf(
            DisplayModelUI(title = "Số giao dịch", value = model.mtId ?: ""),
            DisplayModelUI(
                title = "Trạng thái",
                value = model.statusName ?: "",
                valueColor = resourceProvider.getComposeColor(
                    R.color.text_blue_02,
                ),
            ),
        )
    }

    fun getDisplayInfoCKS(model: SmartCADataDomain? = null): List<DisplayModelUI>? {
        if (model == null) return null
        val lstDisplay = mutableListOf(
            DisplayModelUI(title = "User đăng ký CKS", value = model.creator ?: ""),
            DisplayModelUI(title = "Loại CKS", value = model.esignTypeName ?: ""),
            DisplayModelUI(title = "Dịch vụ sử dụng CKS", value = model.serviceTypeName ?: ""),
            DisplayModelUI(title = "Vai trò CKS", value = model.roleName ?: ""),
            DisplayModelUI(title = "Họ và tên", value = model.name ?: ""),
            DisplayModelUI(title = "CMND/CCCD", value = model.no ?: ""),
            DisplayModelUI(title = "Số serial", value = model.serial ?: ""),
            DisplayModelUI(title = "Ngày hết hạn", value = model.endDate ?: ""),
            DisplayModelUI(title = "Nhà cung cấp CKS", value = model.esignFrom ?: ""),
            DisplayModelUI(title = "Tên chủ sở hữu CKS", value = model.nameEsign ?: ""),
            DisplayModelUI(title = "ID chủ sở hữu CKS", value = model.idEsign ?: ""),
            DisplayModelUI(title = "Chi nhánh xử lý", value = model.branchName ?: ""),
        )
        if (!TextUtils.isEmpty(model.files?.firstOrNull()?.fileName)) {
            lstDisplay.add(
                DisplayModelUI(
                    title = "File đã ký",
                    value = model.files?.firstOrNull()?.fileName,
                    valueColor = resourceProvider.getComposeColor(
                        R.color.text_blue_02,
                    ),
                    clickDirect = Tags.TYPE_FILE,
                ),
            )
        }
        return lstDisplay
    }

    fun getRegisterConfirm(model: SmartCARegisterDomain?): List<DisplayModelUI>? {
        if (model == null) return null
        return listOf(
            DisplayModelUI(
                title = "User đăng ký CKS",
                value = model.esignRegister?.creator ?: "",
            ),
            DisplayModelUI(
                title = "Loại CKS",
                value = model.esignRegister?.esignTypeName ?: "",
            ),
            DisplayModelUI(
                title = "Dịch vụ sử dụng CKS",
                value = model.esignRegister?.serviceTypeName ?: "",
            ),
            DisplayModelUI(
                title = "Vai trò CKS",
                value = model.esignRegister?.roleName ?: "",
            ),
            DisplayModelUI(
                title = "Họ và tên",
                value = model.esignRegister?.name ?: "",
            ),
            DisplayModelUI(title = "CMND/CCCD", value = model.esignRegister?.no ?: ""),
            DisplayModelUI(
                title = "Số serial",
                value = model.esignRegister?.serial ?: "",
            ),
            DisplayModelUI(
                title = "Ngày hết hạn",
                value = model.esignRegister?.endDate ?: "",
            ),
            DisplayModelUI(
                title = "Nhà cung cấp CKS",
                value = model.esignRegister?.esignFrom ?: "",
            ),
            DisplayModelUI(
                title = "Tên chủ sở hữu CKS",
                value = model.esignRegister?.nameEsign ?: "",
            ),
            DisplayModelUI(
                title = "ID chủ sở hữu CKS",
                value = model.esignRegister?.idEsign ?: "",
            ),
            DisplayModelUI(
                title = "Chi nhánh xử lý",
                value = model.esignRegister?.branchName ?: "",
            ),
            DisplayModelUI(
                title = "File chưa ký",
                value = model.toaDo?.fileName ?: "",
                valueColor = resourceProvider.getComposeColor(R.color.text_blue_02),
                clickDirect = Tags.TYPE_FILE,
            ),
        )
    }

    fun getRegisterResult(model: SmartCARegisterDomain?): List<DisplayModelUI>? {
        if (model == null) return null
        return listOf(
            DisplayModelUI(
                title = "User đăng ký CKS",
                value = model.esignRegister?.creator ?: "",
            ),
            DisplayModelUI(
                title = "Họ và tên",
                value = model.esignRegister?.name ?: "",
            ),
            DisplayModelUI(title = "CMND/CCCD", value = model.esignRegister?.no ?: ""),
            DisplayModelUI(
                title = "Dịch vụ sử dụng CKS",
                value = model.esignRegister?.serviceTypeName ?: "",
            ),
            DisplayModelUI(
                title = "Vai trò CKS",
                value = model.esignRegister?.roleName ?: "",
            ),
            DisplayModelUI(
                title = "Số serial",
                value = model.esignRegister?.serial ?: "",
            ),
            DisplayModelUI(
                title = "Ngày hết hạn",
                value = model.esignRegister?.endDate ?: "",
            ),
            DisplayModelUI(
                title = "Nhà cung cấp CKS",
                value = model.esignRegister?.esignFrom ?: "",
            ),
            DisplayModelUI(
                title = "Tên chủ sở hữu CKS",
                value = model.esignRegister?.nameEsign ?: "",
            ),
            DisplayModelUI(
                title = "ID chủ sở hữu CKS",
                value = model.esignRegister?.idEsign ?: "",
            ),
            DisplayModelUI(
                title = "Chi nhánh xử lý",
                value = model.esignRegister?.branchName ?: "",
            ),
            DisplayModelUI(
                title = "File đã ký",
                value = model.toaDo?.fileName ?: "",
                valueColor = resourceProvider.getComposeColor(R.color.text_blue_02),
                clickDirect = Tags.TYPE_FILE,
            ),
        )
    }

    private val _signFlowState = MutableSharedFlow<SignCallState>()
    var signFlowState = _signFlowState.asSharedFlow()
    fun changeSignState(state: SignCallState) {
        viewModelScope.launch {
            _signFlowState.emit(state)
        }
    }

    // SharedFlow để thông báo khi URL sẵn sàng
    private val _fileUrlReady = MutableSharedFlow<String>()
    val fileUrlReady = _fileUrlReady.asSharedFlow()
    private val _getDownloadFileID = MutableSharedFlow<Resource<GetDownloadFileIDDomain>?>()
    val getDownloadFileID = _getDownloadFileID.asSharedFlow()
    fun getDownloadFileID(fileId: String?) {
        launchJob(showLoading = true) {
            val res = checkerUserCase.getDownloadFileID(
                GetDownloadFileIDParams(
                    username = userProf.getUserName(),
                    fileId = fileId,
                    mtID = fileId,
                    tranType = null,
                ),
            )
            handleResource(res) { data ->
                _getDownloadFileID.emit(Resource.Success(data))

                data.downloadFileId?.let { fileId ->
                    if (fileId.isNotEmpty()) {
                        val baseUrl = dataSourceProperties.getUrl()
                        val downloadUrl = baseUrl + Constants.MB_DOWNLOAD_FILE.replace(
                            "{encryptStr}",
                            fileId,
                        )
                        printLog("download url: $downloadUrl")
                        _fileUrlReady.emit(downloadUrl)
                    }
                }
            }
        }
    }
}
