package com.vietinbank.feature_smartca.fragment

import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import android.view.View
import androidx.compose.runtime.Composable
import androidx.core.net.toUri
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.extensions.isInstalledApp
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.FlowUtils
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.smartCA.SignCallState
import com.vietinbank.core_domain.models.smartCA.SmartCAConfirmActions
import com.vietinbank.core_domain.models.smartCA.SmartCARegisterDomain
import com.vietinbank.core_domain.models.smartCA.SmartCARoleDomain
import com.vietinbank.core_domain.smartca.ISignResult
import com.vietinbank.core_domain.smartca.ISmartCAManager
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_smartca.SmartCAViewModel
import com.vietinbank.feature_smartca.di.ISmartCANavigator
import com.vietinbank.feature_smartca.screen.SmartCAConfirmScreen
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class SmartCAConfirmFragment : BaseFragment<SmartCAViewModel>(), ISignResult {
    override val viewModel: SmartCAViewModel by viewModels()

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override val useCompose: Boolean = true

    @Inject
    lateinit var smartCANavigator: ISmartCANavigator
    private var countDownTimer: CountDownTimer? = null

    @Inject
    lateinit var smartCAManager: ISmartCAManager

    @Composable
    override fun ComposeScreen() {
        val actions = SmartCAConfirmActions(onBackClick = {
            appNavigator.popBackStack()
        }, onFileClick = { fileId ->
            // download file
            if (fileId.isNullOrEmpty()) {
                showNoticeDialog("Không tìm thấy file đính kèm hoặc file không khả dụng")
            } else {
                viewModel.getDownloadFileID(fileId)
            }
        }, onNextClick = { isTerm ->
            if (isTerm) {
                showConfirmDialog(
                    "Quý khách sẽ được chuyển đến ứng dụng Smart CA để tiếp tục thực hiện duyệt giao dịch bằng phương thức ký số. Quý khách có muốn tiếp tục không?",
                    "Đồng ý",
                    "Đóng",
                    positiveAction = {
                        viewModel.registerCKS("SIGN")
                    },
                )
            } else {
                showConfirmDialog(
                    "Quý khách vui lòng đồng ý với điều khoản điều kiện trên Giấy dăng ký",
                    "Đồng ý",
                    "Đóng",
                )
            }
        })

        AppTheme {
            SmartCAConfirmScreen(viewModel, actions)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        // lang nghe ket qua ky so
        smartCAManager.setSignListener(this)
        // gọi get params
//        viewModel.getParamsCKS("VIEW_FILE")
        try {
            arguments?.getString(Tags.TRANSACTION_BUNDLE)?.let {
                viewModel.onChangeRegisterCKS(
                    Utils.g().provideGson().fromJson(it, SmartCARegisterDomain::class.java),
                )
            }

            arguments?.getString(Tags.DATA_TYPE)?.let {
                viewModel.onChangeRole(
                    Utils.g().provideGson().fromJson(it, SmartCARoleDomain::class.java),
                )
            }
        } catch (_: Exception) {
        }

        viewModel.apply {
            FlowUtils.collectFlow(this@SmartCAConfirmFragment, registerCKS) { resource ->
                if (isChecking()) {
                    // dieu huong sang man ket qua
                    countDownTimer?.cancel()
                    countDownTimer = null
                    smartCANavigator.toResultCKS(
                        Tags.TRANSACTION_BUNDLE to Utils.g().provideGson().toJson(resource ?: ""),
                    )
                } else if (!activity.isInstalledApp("com.vnptit.vnpt_kyso_v2")) {
                    // kiem tra device da co app vnpt smart ca hay chua
                    // chua co app
                    showConfirmDialog(
                        "Quý khách chưa cài đặt ứng dụng VNPT SmartCA. Quý khách có thể sử dụng thiết bị đã được cài đặt ứng dụng VNPT SmartCA để ký số, quá trình sẽ được xử lý tiếp tục.",
                        "Đồng ý",
                        "Đóng",
                        positiveAction = {
                            changeSignState(SignCallState.Loading)
                        },
                    )
                } else {
                    // điều hướng sang vnpt để ký số
                    activity?.let {
                        changeSignState(SignCallState.Loading)
                        smartCAManager.signTransaction(
                            it,
                            resource?.androidKey ?: "",
                            resource?.data?.addInfoDetail?.firstOrNull()?.tran_code ?: "",
                        )
                    }
                }
            }

            FlowUtils.collectFlow(this@SmartCAConfirmFragment, signFlowState) { state ->
                when (state) {
                    is SignCallState.Error -> {
                        forceAllHideLoading()
                        countDownTimer?.cancel()
                        countDownTimer = null
                        state.exception?.let {
                            onError(it)
                        } ?: run {
                            showNoticeDialog(state.message ?: "") { appNavigator.popBackStack() }
                        }
                    }

                    // trang thai ky thanh cong + case ky tren device khac
                    is SignCallState.Success, is SignCallState.Loading -> {
                        showLoading()
                        if (countDownTimer == null) {
                            countDownTimer = object : CountDownTimer(300 * 1000L, 1000) {
                                override fun onTick(timeLeft: Long) {
                                    if ((timeLeft / 1000) % 5 == 0L) {
                                        // 5s call lai 1 lan nha
                                        registerCKSSilent("CHECK") { appError ->
                                            /**
                                             * 1 thành công, ký xong -> case thanh cong -> khong can xu ly
                                             * 0 lỗi
                                             * 3 pending sign chờ ký số, check tiếp
                                             * */
                                            if (appError is AppException.ApiException && appError.code == "3") {
                                                // tiep tuc goi ky so theo count time -> khong can xu ly gi
                                            } else {
                                                // show thong bao loi -> dung luong recall api check
                                                changeSignState(SignCallState.Error(exception = appError))
                                            }
                                        }
                                    }
                                }

                                override fun onFinish() {
                                    changeSignState(SignCallState.Error(message = "Thời gian ký số đã kết thúc. Quý khách vui lòng thực hiện lại"))
                                }
                            }.start()
                        }
                    }

                    else -> {}
                }
            }

            FlowUtils.collectFlow(this@SmartCAConfirmFragment, getDownloadFileID) { resource ->
                if (resource != null && resource is Resource.Success) {
                    // Lấy downloadFileId từ kết quả
                    if (resource.data.downloadFileId.isNullOrEmpty()) {
                        showNoticeDialog("Không tìm thấy file đính kèm hoặc file không khả dụng")
                    }
                } else if (resource is Resource.Error) {
                    showNoticeDialog(resource.message ?: "Không thể tải file đính kèm")
                }
            }

            FlowUtils.collectFlow(this@SmartCAConfirmFragment, fileUrlReady) { url ->
                try {
                    // Mở URL trong trình duyệt
                    val intent = Intent(Intent.ACTION_VIEW, url.toUri())
                    startActivity(intent)
                } catch (e: Exception) {
                    showNoticeDialog("Không thể mở file: ${e.message}")
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        countDownTimer?.cancel()
        countDownTimer = null
    }

    override fun onResult(statusCode: Int, statusMessage: String) {
        if (statusCode == 0) {
            viewModel.changeSignState(SignCallState.Success)
        } else if ((statusCode != 3 && statusCode != -1)) {
            viewModel.changeSignState(SignCallState.Error(message = statusMessage))
        } else {
            viewModel.changeSignState(SignCallState.Loading)
        }
    }
}