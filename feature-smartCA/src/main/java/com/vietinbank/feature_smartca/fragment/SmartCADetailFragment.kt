package com.vietinbank.feature_smartca.fragment

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.core.net.toUri
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.FlowUtils
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.smartCA.SmartCADetailActions
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_smartca.SmartCAViewModel
import com.vietinbank.feature_smartca.di.ISmartCANavigator
import com.vietinbank.feature_smartca.screen.SmartCADetailScreen
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class SmartCADetailFragment : BaseFragment<SmartCAViewModel>() {
    override val viewModel: SmartCAViewModel by viewModels()

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override val useCompose: Boolean = true

    @Inject
    lateinit var smartCANavigator: ISmartCANavigator

    @Composable
    override fun ComposeScreen() {
        val actions = SmartCADetailActions(onHomeClick = {
            smartCANavigator.popBackGraph()
        }, onBackClick = {
            smartCANavigator.popBackStack()
        }, onDownloadClick = { fileId ->
            if (fileId.isNullOrEmpty()) {
                showNoticeDialog("Không tìm thấy file đính kèm hoặc file không khả dụng")
            } else {
                viewModel.getDownloadFileID(fileId)
            }
        })
        AppTheme {
            SmartCADetailScreen(viewModel, actions)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        arguments?.getString(Tags.TRANSACTION_BUNDLE)?.let {
            viewModel.getDetailCKS(it)
        }

        initObservers()
    }

    private fun initObservers() {
        viewModel.apply {
            FlowUtils.collectFlow(this@SmartCADetailFragment, getDownloadFileID) { resource ->
                if (resource != null && resource is Resource.Success) {
                    // Lấy downloadFileId từ kết quả
                    if (resource.data.downloadFileId.isNullOrEmpty()) {
                        showNoticeDialog("Không tìm thấy file đính kèm hoặc file không khả dụng")
                    }
                } else if (resource is Resource.Error) {
                    showNoticeDialog(resource.message ?: "Không thể tải file đính kèm")
                }
            }

            FlowUtils.collectFlow(this@SmartCADetailFragment, fileUrlReady) { url ->
                try {
                    // Mở URL trong trình duyệt
                    val intent = Intent(Intent.ACTION_VIEW, url.toUri())
                    startActivity(intent)
                } catch (e: Exception) {
                    showNoticeDialog("Không thể mở file: ${e.message}")
                }
            }
        }
    }
}