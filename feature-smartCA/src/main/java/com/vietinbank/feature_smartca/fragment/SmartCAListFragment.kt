package com.vietinbank.feature_smartca.fragment

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_domain.models.smartCA.SmartCAListActions
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_smartca.SmartCAViewModel
import com.vietinbank.feature_smartca.di.ISmartCANavigator
import com.vietinbank.feature_smartca.screen.SmartCAListScreen
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class SmartCAListFragment : BaseFragment<SmartCAViewModel>() {
    override val viewModel: SmartCAViewModel by viewModels()

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override val useCompose: Boolean = true

    @Inject
    lateinit var smartCANavigator: ISmartCANavigator

    @Composable
    override fun ComposeScreen() {
        val actions = SmartCAListActions(
            onBackClick = { appNavigator.popBackStack() },
            onHomeClick = { smartCANavigator.popBackGraph() },
            onRegisterClick = { smartCANavigator.toRegisterCKS() },
            onDetailClick = { smartCANavigator.toDetailCKS(Tags.TRANSACTION_BUNDLE to it) },
            onDeleteClick = {
                showConfirmDialog(
                    "Quý khách có chắc chắn muốn xóa chữ ký số này không?",
                    "Tiếp tục",
                    "Đóng",
                    positiveAction = { viewModel.updateCKS(it, "D") },
                )
            },
            onCancelClick = {
                showConfirmDialog(
                    "Quý khách có chắc chắn muốn đóng chữ ký số này không?",
                    "Tiếp tục",
                    "Đóng",
                    positiveAction = { viewModel.updateCKS(it, "C") },
                )
            },
        )

        AppTheme {
            SmartCAListScreen(viewModel, actions)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.getListCKS()

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.apply {
                    launch {
                        actionCKS.collect {
                            getListCKS()
                        }
                    }
                }
            }
        }
    }
}