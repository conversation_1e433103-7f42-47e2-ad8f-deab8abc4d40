package com.vietinbank.feature_smartca.fragment

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.smartCA.BranchDomain
import com.vietinbank.core_domain.models.smartCA.SmartCAEsignRegisterDomain
import com.vietinbank.core_domain.models.smartCA.SmartCARegisterActions
import com.vietinbank.core_domain.models.smartCA.SmartCARoleDomain
import com.vietinbank.core_domain.models.smartCA.SmartCAServiceDomain
import com.vietinbank.core_domain.models.smartCA.SmartCAUserDomain
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_smartca.SmartCAViewModel
import com.vietinbank.feature_smartca.di.ISmartCANavigator
import com.vietinbank.feature_smartca.screen.SmartCARegisterScreen
import com.vietinbank.feature_smartca.sheet.SmartCAOptionSheet
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class SmartCARegisterFragment : BaseFragment<SmartCAViewModel>() {
    override val viewModel: SmartCAViewModel by viewModels()

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override val useCompose: Boolean = true

    @Inject
    lateinit var smartCANavigator: ISmartCANavigator

    private var certSheet: SmartCAOptionSheet? = null
    private var userSheet: SmartCAOptionSheet? = null
    private var serviceSheet: SmartCAOptionSheet? = null
    private var roleSheet: SmartCAOptionSheet? = null
    private var branchSheet: SmartCAOptionSheet? = null

    @Composable
    override fun ComposeScreen() {
        val actions =
            SmartCARegisterActions(onBackClick = { appNavigator.popBackStack() }, onHomeClick = {
                smartCANavigator.popBackGraph()
            }, onUserClick = {
                if (userSheet == null && viewModel.lstUser.size > 1) {
                    userSheet = SmartCAOptionSheet(
                        SmartCAOptionSheet.USER, viewModel.lstUser,
                    )
                }
                userSheet?.setOnItemClick {
                    if (it is SmartCAUserDomain) {
                        viewModel.onChangeUser(it)
                    }
                }
                userSheet?.show(childFragmentManager, SmartCAOptionSheet::class.java.name)
            }, onSeviceClick = {
                if (serviceSheet == null && viewModel.lstService.isNotEmpty()) {
                    serviceSheet = SmartCAOptionSheet(
                        SmartCAOptionSheet.SERVICE, viewModel.lstService,
                    )
                }
                serviceSheet?.setOnItemClick {
                    if (it is SmartCAServiceDomain) {
                        viewModel.onChangeService(it)
                    }
                }
                serviceSheet?.show(childFragmentManager, SmartCAOptionSheet::class.java.name)
            }, onRoleClick = {
                if (null == viewModel.serviceSelected.value) {
                    showNoticeDialog("Quý khách vui lòng chọn \"Dịch vụ sử dung CKS\"")
                    return@SmartCARegisterActions
                }
                if (roleSheet == null && viewModel.lstRole.isNotEmpty()) {
                    roleSheet = SmartCAOptionSheet(
                        SmartCAOptionSheet.ROLE, viewModel.lstRole,
                    )
                }
                roleSheet?.setOnItemClick {
                    if (it is SmartCARoleDomain) {
                        viewModel.onChangeRole(it)
                    }
                }
                roleSheet?.show(childFragmentManager, SmartCAOptionSheet::class.java.name)
            }, onBranchClick = {
                if (null == viewModel.serviceSelected.value) {
                    showNoticeDialog("Quý khách vui lòng chọn \"Dịch vụ sử dung CKS\"")
                    return@SmartCARegisterActions
                }
                if (viewModel.lstBranch.isEmpty()) {
                    viewModel.getBranch()
                    return@SmartCARegisterActions
                }
                branchSheet = SmartCAOptionSheet(
                    SmartCAOptionSheet.BRANCH, viewModel.lstBranch,
                )
                branchSheet?.setOnItemClick {
                    if (it is BranchDomain) {
                        viewModel.onChangeBranch(it)
                    }
                }
                branchSheet?.show(childFragmentManager, SmartCAOptionSheet::class.java.name)
            }, onCertClick = {
                viewModel.getCertVNPT()
            }, onNextClick = {
                if (null == viewModel.userSelected.value) {
                    showNoticeDialog("Vui lòng  chọn \"Tài khoản đăng ký CKS\" trước khi \"Tiếp tục\"")
                    return@SmartCARegisterActions
                } else if (null == viewModel.serviceSelected.value) {
                    showNoticeDialog("Vui lòng  chọn \"Dịch vụ sử dụng CKS\" trước khi \"Tiếp tục\"")
                    return@SmartCARegisterActions
                } else if (null == viewModel.roleSelected.value) {
                    showNoticeDialog("Vui lòng  chọn \"Vai trò CKS\" trước khi \"Tiếp tục\"")
                    return@SmartCARegisterActions
                } else if (null == viewModel.branchSelected.value) {
                    showNoticeDialog("Vui lòng  chọn \"Chi nhánh xử lý\" trước khi \"Tiếp tục\"")
                    return@SmartCARegisterActions
                }
                // validate thành công => sang màn confirm
                viewModel.registerCKS("VALIDATE")
            })

        AppTheme {
            SmartCARegisterScreen(viewModel, actions)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (!viewModel.isKeepCallInit()) {
            viewModel.getParamsCKS("PARAMS")
            viewModel.setKeepCallInit()
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.apply {
                    launch {
                        branchCKS.collect { resource ->
                            branchSheet = SmartCAOptionSheet(
                                SmartCAOptionSheet.BRANCH, viewModel.lstBranch,
                            )
                            branchSheet?.setOnItemClick {
                                if (it is BranchDomain) {
                                    viewModel.onChangeBranch(it)
                                }
                            }
                            branchSheet?.show(
                                childFragmentManager,
                                SmartCAOptionSheet::class.java.name,
                            )
                        }
                    }

                    launch {
                        certVNPT.collect {
                            certSheet = SmartCAOptionSheet(
                                SmartCAOptionSheet.CERT, viewModel.lstCertVNPT,
                            )
                            certSheet?.setOnItemClick {
                                if (it is SmartCAEsignRegisterDomain) {
                                    viewModel.onChangeCert(it)

                                    // goi gen file - chua ky so
//                                    viewModel.getParamsCKS("VIEW_FILE")
                                }
                            }
                            certSheet?.show(
                                childFragmentManager,
                                SmartCAOptionSheet::class.java.name,
                            )
                        }
                    }

                    launch {
                        registerCKS.collect {
                            smartCANavigator.toConfirmCKS(
                                Tags.TRANSACTION_BUNDLE to Utils.g().provideGson().toJson(it),
                                Tags.DATA_TYPE to Utils.g().provideGson()
                                    .toJson(roleSelected.value),
                            )
                        }
                    }
                }
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        serviceSheet = null
        roleSheet = null
        branchSheet = null
    }
}