package com.vietinbank.feature_smartca.adapter

import android.text.TextUtils
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.Filter
import android.widget.Filterable
import androidx.core.view.isVisible
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.extensions.removeVietNam
import com.vietinbank.core_domain.models.smartCA.BranchDomain
import com.vietinbank.core_domain.models.smartCA.SmartCAEsignRegisterDomain
import com.vietinbank.core_domain.models.smartCA.SmartCARoleDomain
import com.vietinbank.core_domain.models.smartCA.SmartCAServiceDomain
import com.vietinbank.core_domain.models.smartCA.SmartCAUserDomain
import com.vietinbank.core_ui.R
import com.vietinbank.feature_smartca.databinding.ItemCertOptionBinding
import com.vietinbank.feature_smartca.databinding.ItemSmartOptionBinding

class SmartCAAdapter : ListAdapter<Any, RecyclerView.ViewHolder>(DIFF_CALLBACK), Filterable {
    private var dataSet: MutableList<Any> = mutableListOf()
    private var onItemClick: ((Any) -> Unit)? = null
    private var itemSelect: String? = null

    fun setOnItemClick(listener: ((Any) -> Unit)? = null) {
        onItemClick = listener
    }

    fun setItemSelect(item: String? = null) {
        this.itemSelect = item
    }

    fun setData(data: List<Any>) {
        dataSet.clear()
        dataSet.addAll(data)
        submitList(dataSet)
    }

    override fun getItemViewType(position: Int): Int {
        return when (currentList.get(position)) {
            is SmartCAEsignRegisterDomain -> TYPE_CERT
            else -> TYPE_DEFAULT
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_CERT -> CertVH(
                ItemCertOptionBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false,
                ),
            )

            else -> OptionVH(
                ItemSmartOptionBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false,
                ),
            )
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is CertVH -> holder.bindData(getItem(position))
            else -> (holder as OptionVH).bindData(getItem(position))
        }
    }

    inner class OptionVH(val binding: ItemSmartOptionBinding) :
        RecyclerView.ViewHolder(binding.root) {

        init {
            binding.root.setThrottleClickListener {
                try {
                    itemSelect = when (currentList[adapterPosition]) {
                        is SmartCAUserDomain -> (currentList[adapterPosition] as SmartCAUserDomain).username
                        is SmartCAServiceDomain -> (currentList[adapterPosition] as SmartCAServiceDomain).key
                        is SmartCARoleDomain -> (currentList[adapterPosition] as SmartCARoleDomain).key
                        is BranchDomain -> (currentList[adapterPosition] as BranchDomain).branchId
                        else -> ""
                    }
                    onItemClick?.invoke(currentList[adapterPosition])
                } catch (_: Exception) {
                }
            }
        }

        fun bindData(item: Any) {
            with(binding) {
                when (item) {
                    is SmartCAUserDomain -> {
                        tvValue.text = item.username ?: ""
                        ivStatus.setImageResource(
                            if (!TextUtils.isEmpty(itemSelect) &&
                                TextUtils.equals(item.username, itemSelect)
                            ) {
                                R.drawable.ic_radio_check
                            } else {
                                R.drawable.ic_radio_uncheck
                            },
                        )
                    }

                    is SmartCAServiceDomain -> {
                        tvValue.text = item.value ?: ""
                        ivStatus.setImageResource(
                            if (!TextUtils.isEmpty(itemSelect) &&
                                TextUtils.equals(item.key, itemSelect)
                            ) {
                                R.drawable.ic_radio_check
                            } else {
                                R.drawable.ic_radio_uncheck
                            },
                        )
                    }

                    is SmartCARoleDomain -> {
                        tvValue.text = item.value ?: ""
                        ivStatus.setImageResource(
                            if (!TextUtils.isEmpty(itemSelect) &&
                                TextUtils.equals(item.key, itemSelect)
                            ) {
                                R.drawable.ic_radio_check
                            } else {
                                R.drawable.ic_radio_uncheck
                            },
                        )
                    }

                    is BranchDomain -> {
                        tvValue.text = item.branchName ?: ""
                        ivStatus.setImageResource(
                            if (!TextUtils.isEmpty(itemSelect) &&
                                TextUtils.equals(item.branchId, itemSelect)
                            ) {
                                R.drawable.ic_radio_check
                            } else {
                                R.drawable.ic_radio_uncheck
                            },
                        )
                    }
                }
            }
        }
    }

    inner class CertVH(val binding: ItemCertOptionBinding) : RecyclerView.ViewHolder(binding.root) {

        init {
            binding.ivStatus.setThrottleClickListener {
                try {
                    itemSelect = when (currentList[adapterPosition]) {
                        is SmartCAEsignRegisterDomain -> (currentList[adapterPosition] as SmartCAEsignRegisterDomain).serial
                        else -> ""
                    }
                    onItemClick?.invoke(currentList[adapterPosition])
                } catch (_: Exception) {
                }
            }

            binding.tvMore.setThrottleClickListener {
                var isShow = binding.llService.isVisible
                binding.llService.isVisible = !isShow
                binding.llEsignFrom.isVisible = !isShow
                binding.llEsignId.isVisible = !isShow
                binding.tvMore.text = if (isShow) {
                    "Xem thêm"
                } else {
                    "Thu gọn"
                }
            }
        }

        fun bindData(item: Any) {
            with(binding) {
                if (item is SmartCAEsignRegisterDomain) {
                    tvName.text = item.name ?: ""
                    tvSerial.text = item.serial ?: ""
                    tvExpireDate.text = item.endDate ?: ""
                    tvService.text = when (item.serviceType) {
                        "2" -> "CKS tổ chức"
                        else -> "CKS cá nhân"
                    }
                    tvEsignFrom.text = item.esignFrom ?: ""
                    tvEsignId.text = item.idEsign ?: ""
                    ivStatus.setImageResource(
                        if (!TextUtils.isEmpty(itemSelect) &&
                            TextUtils.equals(item.serial, itemSelect)
                        ) {
                            R.drawable.ic_radio_check
                        } else {
                            R.drawable.ic_radio_uncheck
                        },
                    )
                }
            }
        }
    }

    companion object {
        const val TYPE_DEFAULT = 0
        const val TYPE_CERT = 1
        val DIFF_CALLBACK: DiffUtil.ItemCallback<Any> = object : DiffUtil.ItemCallback<Any>() {
            override fun areItemsTheSame(oldItem: Any, newItem: Any): Boolean {
                return oldItem === newItem
            }

            override fun areContentsTheSame(oldItem: Any, newItem: Any): Boolean {
                return if (oldItem is SmartCARoleDomain && newItem is SmartCARoleDomain) {
                    return oldItem.key == newItem.key
                } else if (oldItem is SmartCAUserDomain && newItem is SmartCAUserDomain) {
                    return oldItem.username == newItem.username
                } else if (oldItem is SmartCAServiceDomain && newItem is SmartCAServiceDomain) {
                    return oldItem.key == newItem.key
                } else if (oldItem is BranchDomain && newItem is BranchDomain) {
                    return oldItem.branchId == newItem.branchId
                } else if (oldItem is SmartCAEsignRegisterDomain && newItem is SmartCAEsignRegisterDomain) {
                    return oldItem.mtId == newItem.mtId
                } else {
                    false
                }
            }
        }
    }

    override fun getFilter(): Filter {
        return object : Filter() {
            override fun performFiltering(constraintFilter: CharSequence?): FilterResults {
                val filter = FilterResults()
                if (constraintFilter.isNullOrEmpty()) {
                    filter.values = dataSet
                } else {
                    val constraint = constraintFilter.toString().removeVietNam(true)
                    val lstFilter = mutableListOf<Any>()
                    lstFilter.addAll(
                        dataSet.filter { item ->
                            when (item) {
                                is SmartCARoleDomain -> true == item.value.removeVietNam(true)
                                    .contains(constraint)

                                is SmartCAUserDomain -> true == item.fullName.removeVietNam(true)
                                    .contains(constraint) || true == item.idNumber?.contains(
                                    constraint,
                                )

                                is SmartCAServiceDomain -> true == item.value.removeVietNam(true)
                                    .contains(constraint)

                                is BranchDomain -> true == item.branchName.removeVietNam(true)
                                    .contains(constraint) || true == item.branchId?.contains(
                                    constraint,
                                )

                                is SmartCAEsignRegisterDomain -> true == item.nameEsign.removeVietNam(
                                    true,
                                ).contains(constraint) || true == item.name.removeVietNam(true)
                                    .contains(constraint) || true == item.no?.contains(
                                    constraint,
                                )

                                else -> false
                            }
                        },
                    )
                    filter.values = lstFilter
                }
                return filter
            }

            override fun publishResults(constraint: CharSequence?, results: FilterResults?) {
                if (results != null) {
                    submitList(results.values as MutableList<*>)
                }
            }
        }
    }
}