package com.vietinbank.feature_new_smart_ca.constance

object SmartCAConstants {
    object Bundle {
        const val KEY_MT_ID = "KEY_MT_ID"
        const val TRANSACTION_BUNDLE = "TRANSACTION_BUNDLE"
        const val DATA_TYPE = "DATA_TYPE"
        const val UTF8 = "UTF-8"
    }
    object DashBoardSmartCA {
        const val TAB_ACTIVE = 0
        const val TAB_INACTIVE = 1
        const val STATUS_CODE_SUCCESS = "05"
        const val STATUS_CODE_PENDING = "04"
        const val STATUS_CODE_ACCOUNTANT_REJECT = "01"
        const val STATUS_CODE_BANK_REJECT = "06"
        const val STATUS_CODE_DELETED = "12"
        const val CERT_TYPE_KEY = "SCA"
        const val TYPE_CLOSE_CKS = "C"
        const val TYPE_DELETE_CKS = "D"
        const val ESIGN_TYPE_PERSONAL = "1"
        const val ESIGN_TYPE_BUSINESS = "2"
    }
    object TypeRegisterChoose {
        const val SERVICE = 0
        const val ROLE = 1
        const val CERT = 2
        const val BRANCH = 3
        const val USER = 4
    }
}
