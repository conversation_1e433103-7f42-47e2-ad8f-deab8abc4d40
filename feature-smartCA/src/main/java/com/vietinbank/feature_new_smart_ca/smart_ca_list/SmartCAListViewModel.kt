package com.vietinbank.feature_new_smart_ca.smart_ca_list

import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_domain.models.smartCA.SmartCAEsignDomain
import com.vietinbank.core_domain.models.smartCA.SmartCAParams
import com.vietinbank.core_domain.usecase.smartca.SmartCAUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.feature_new_smart_ca.constance.SmartCAConstants.DashBoardSmartCA.STATUS_CODE_PENDING
import com.vietinbank.feature_new_smart_ca.constance.SmartCAConstants.DashBoardSmartCA.STATUS_CODE_SUCCESS
import com.vietinbank.feature_new_smart_ca.constance.SmartCAConstants.DashBoardSmartCA.TAB_ACTIVE
import com.vietinbank.feature_new_smart_ca.constance.SmartCAConstants.DashBoardSmartCA.TAB_INACTIVE
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SmartCAListViewModel @Inject constructor(
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
    private val smartCAUseCase: SmartCAUseCase,

) : BaseViewModel() {
    private val _state = MutableStateFlow(SmartCAListState())
    val state: StateFlow<SmartCAListState> = _state.asStateFlow()

    private val _events = Channel<SmartCAListEvent>(Channel.BUFFERED)
    val events = _events.receiveAsFlow()

    init {
        getListCKS()
    }

    private fun getListCKS() = launchJob(showLoading = true) {
        updateIsLoadingListCKS(true)
        val params = SmartCAParams(userProf.getUserName(), userProf.getRoleId())
        val res = smartCAUseCase.getListCKS(params)
        handleResource(res) { data ->
            val signInfoList = data.signInfoList.orEmpty()

            val activeList =
                signInfoList.filter { STATUS_CODE_SUCCESS == it.statusCode || STATUS_CODE_PENDING == it.statusCode }
            val inActiveList =
                signInfoList.filterNot { STATUS_CODE_SUCCESS == it.statusCode || STATUS_CODE_PENDING == it.statusCode }

            updateSignInfoListActive(activeList.toMutableList())
            updateSignInfoListInActive(inActiveList.toMutableList())
            updateIsLoadingListCKS(false)
        }
    }

    private fun onSelectedIndexChange(value: Int) {
        _state.update { currentState ->
            currentState.copy(
                selectedIndex = value,
            )
        }
    }

    private fun updateSignInfoListActive(value: MutableList<SmartCAEsignDomain>) {
        _state.update { currentState ->
            currentState.copy(
                signInfoListActive = value,
            )
        }
    }

    private fun updateSignInfoListInActive(value: MutableList<SmartCAEsignDomain>) {
        _state.update { currentState ->
            currentState.copy(
                signInfoListInActive = value,
            )
        }
    }

    private fun updateIsLoadingListCKS(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                isLoadingListCKS = value,
            )
        }
    }

    override fun onDisplayErrorMessage(exception: AppException) {
        if (exception is AppException.ApiException) {
            when (exception.requestPath) {
                Constants.MB_SMART_CA_LIST -> {
                    updateSignInfoListActive(mutableListOf())
                    updateSignInfoListInActive(mutableListOf())
                    updateIsLoadingListCKS(false)
                }
            }
        }
        super.onDisplayErrorMessage(exception)
    }

    fun onAction(action: SmartCAListAction) {
        when (action) {
            is SmartCAListAction.OnBackPressed -> {
                viewModelScope.launch {
                    _events.send(SmartCAListEvent.NavigateBack)
                }
            }

            is SmartCAListAction.OnSelectedIndex -> {
                onSelectedIndexChange(action.selectedIndex)
                if (TAB_ACTIVE == action.selectedIndex) {
                } else if (TAB_INACTIVE == action.selectedIndex) {
                }
            }

            is SmartCAListAction.OnDetailClick -> {
                viewModelScope.launch {
                    _events.send(SmartCAListEvent.OnDetailClick(action.model))
                }
            }

            is SmartCAListAction.OnGoToRegisterSmartCA -> {
                viewModelScope.launch {
                    _events.send(SmartCAListEvent.OnGoToRegisterSmartCA)
                }
            }
        }
    }

    sealed class SmartCAListEvent {
        data object NavigateBack : SmartCAListEvent()
        data class OnDetailClick(val model: SmartCAEsignDomain) : SmartCAListEvent()
        data object OnGoToRegisterSmartCA : SmartCAListEvent()
    }

    sealed class SmartCAListAction {
        data object OnBackPressed : SmartCAListAction()
        data class OnSelectedIndex(val selectedIndex: Int) : SmartCAListAction()
        data class OnDetailClick(val model: SmartCAEsignDomain) : SmartCAListAction()
        data object OnGoToRegisterSmartCA : SmartCAListAction()
    }

    data class SmartCAListState(
        val selectedIndex: Int = 0,
        val signInfoListActive: MutableList<SmartCAEsignDomain> = mutableListOf(),
        val signInfoListInActive: MutableList<SmartCAEsignDomain> = mutableListOf(),
        val isLoadingListCKS: Boolean = true,

    )
}