package com.vietinbank.feature_new_smart_ca.bottomSheet

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.vietinbank.core_domain.models.smartCA.SmartCAServiceDomain
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

data class ServiceTypeBottomSheetBottomSheetUiState(
    var items: List<SmartCAServiceDomain> = emptyList(),
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ServiceTypeBottomSheet(
    isVisible: Boolean,
    title: String,
    onItemSelected: (SmartCAServiceDomain) -> Unit,
    onDismiss: () -> Unit,
    state: ServiceTypeBottomSheetBottomSheetUiState,
) {
    if (isVisible) {
        BaseBottomSheet(
            visible = true,
            onDismissRequest = onDismiss,
            containerColor = FoundationDesignSystem.Colors.backgroundBgContainer,
            containerPadding = PaddingValues(
                horizontal = 0.dp,
                vertical = 0.dp,
            ),
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                // Title
                FoundationText(
                    text = title,
                    style = FDS.Typography.headingH3,
                    color = FDS.Colors.characterHighlighted,
                    modifier = Modifier.padding(vertical = FDS.Sizer.Gap.gap24),
                )

                HorizontalDivider(
                    color = FDS.Colors.divider,
                    thickness = FDS.Sizer.Stroke.stroke1,
                    modifier = Modifier.padding(bottom = FDS.Sizer.Gap.gap8),
                )
                // Items list
                state.items.forEachIndexed { index, item ->
                    RadioButtonServiceTypeItem(
                        item = item,
                        onClick = { onItemSelected(item) },
                    )
                }
                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
            }
        }
    }
}

@Composable
private fun RadioButtonServiceTypeItem(
    item: SmartCAServiceDomain,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(horizontal = FDS.Sizer.Gap.gap24),
    ) {
        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
        FoundationText(
            text = item.value ?: "",
            style = FDS.Typography.bodyB1,
            color = FDS.Colors.characterPrimary,
        )
        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
        HorizontalDivider(
            color = FDS.Colors.divider,
            thickness = FDS.Sizer.Stroke.stroke1,
        )
    }
}