package com.vietinbank.feature_new_smart_ca.bottomSheet

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.vietinbank.core_domain.models.smartCA.SmartCAEsignRegisterDomain
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.FoundationSelector
import com.vietinbank.core_ui.components.SelectorType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

data class CertBottomSheetUiState(
    var items: MutableList<SmartCAEsignRegisterDomain> = mutableListOf(),
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CertBottomSheet(
    isVisible: Boolean,
    title: String,
    onItemSelected: (SmartCAEsignRegisterDomain) -> Unit,
    onConfirmItemSelected: () -> Unit,
    onDismiss: () -> Unit,
    state: CertBottomSheetUiState,
) {
    var isEnableButton by remember { mutableStateOf(false) }

    if (isVisible) {
        BaseBottomSheet(
            visible = true,
            onDismissRequest = onDismiss,
            containerPadding = PaddingValues(
                horizontal = 0.dp,
                vertical = 0.dp,
            ),
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(0.9f)
                    .systemBarsPadding()
                    .clip(RoundedCornerShape(AppSizer.Radius.radius32))
                    .background(FDS.Colors.backgroundBgContainer),

            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center,
                ) {
                    FoundationText(
                        text = title,
                        style = FDS.Typography.headingH3,
                        color = FDS.Colors.characterHighlighted,
                        modifier = Modifier.padding(vertical = FDS.Sizer.Gap.gap24),
                    )
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .verticalScroll(rememberScrollState())
                            .padding(bottom = FoundationDesignSystem.Sizer.Gap.gap80),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center,
                    ) {
                        HorizontalDivider(
                            color = FDS.Colors.divider,
                            thickness = FDS.Sizer.Stroke.stroke1,
                            modifier = Modifier.padding(bottom = FDS.Sizer.Gap.gap8),
                        )
                        // Items list
                        state.items.forEachIndexed { index, item ->
                            CertItem(
                                item = item,
                                onClick = {
                                    isEnableButton = true
                                    onItemSelected(item)
                                },
                            )
                        }
                    }
                }

                Box(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .fillMaxWidth()
                        .background(
                            Brush.verticalGradient(
                                colors = listOf(
                                    FoundationDesignSystem.Colors.dialogBackground.copy(alpha = 0f),
                                    FoundationDesignSystem.Colors.dialogBackground.copy(alpha = 0.5f),
                                    FoundationDesignSystem.Colors.dialogBackground.copy(alpha = 1f),
                                ),
                                startY = 0f,
                                endY = Float.POSITIVE_INFINITY,
                            ),
                        )
                        .padding(
                            horizontal = FoundationDesignSystem.Sizer.Gap.gap24,
                            vertical = FoundationDesignSystem.Sizer.Gap.gap8,
                        ),
                ) {
                    FoundationButton(
                        isLightButton = true,
                        text = stringResource(com.vietinbank.core_ui.R.string.common_continue),
                        onClick = { onConfirmItemSelected() },
                        enabled = isEnableButton,
                        modifier = Modifier.fillMaxWidth(),
                    )
                }
            }
        }
    }
}

@Composable
private fun CertItem(
    item: SmartCAEsignRegisterDomain,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(horizontal = FDS.Sizer.Gap.gap24)
            .padding(top = FDS.Sizer.Gap.gap16),
    ) {
        FoundationSelector(
            boxType = SelectorType.Radio,
            isSelected = item.isSelected,
            modifier = Modifier.size(FDS.Sizer.Icon.icon24),
            isClickable = false,
        )

        Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap8))
        Column(modifier = Modifier.weight(1f)) {
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_bottom_sheet_cert_nameEsign),
                value = item.nameEsign.orEmpty(),
                titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
            )
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_bottom_sheet_cert_serial),
                value = item.serial.orEmpty(),
                titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
            )
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))

            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_bottom_sheet_cert_name),
                value = item.name.orEmpty(),
                titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
            )
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))

            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_bottom_sheet_endDate),
                value = item.endDate.orEmpty(),
                titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
            )
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_bottom_sheet_serviceType),
                value = item.serviceTypeName.orEmpty(),
                titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
            )
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_bottom_sheet_idEsign),
                value = item.idEsign.orEmpty(),
                titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
            )
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))

            HorizontalDivider(
                color = FDS.Colors.divider,
                thickness = FDS.Sizer.Stroke.stroke1,
            )
        }
    }
}