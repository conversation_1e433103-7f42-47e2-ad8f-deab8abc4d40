package com.vietinbank.feature_new_smart_ca.smart_ca_detail_screen

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.core.net.toUri
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.FlowUtils
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class SmartCADetailFragment : BaseFragment<SmartCADetailViewModel>() {
    override val viewModel: SmartCADetailViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        arguments?.let { bundle ->
            viewModel.handleArguments(bundle)
        }
        observeEvents()
    }

    private fun observeEvents() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.events.collect { event ->
                when (event) {
                    SmartCADetailViewModel.SmartCADetailEvent.NavigateBack -> {
                        appNavigator.popBackStack()
                    }

                    is SmartCADetailViewModel.SmartCADetailEvent.OnClickConfirmButton -> {
                        showConfirmDialog(
                            message = event.text,
                            negativeButtonText = resources.getString(R.string.common_back),
                            positiveButtonText = resources.getString(R.string.common_confirm),
                            positiveAction = { viewModel.updateCKS() },
                        )
                    }

                    is SmartCADetailViewModel.SmartCADetailEvent.OnUpdateCKSSuccess -> {
                        showNoticeDialog(
                            message = event.text,
                            positiveAction = {
                                appNavigator.goToListSmartCA()
                            },
                        )
                    }
                }
            }
        }

        viewModel.apply {
            FlowUtils.collectFlow(this@SmartCADetailFragment, getDownloadFileID) { resource ->
                if (resource != null && resource is Resource.Success) {
                    // Lấy downloadFileId từ kết quả
                    if (resource.data.downloadFileId.isNullOrEmpty()) {
                        showNoticeDialog(resources.getString(com.vietinbank.core_ui.R.string.feature_smart_ca_confirm_error_find_file))
                    }
                } else if (resource is Resource.Error) {
                    showNoticeDialog(resource.message)
                }
            }

            FlowUtils.collectFlow(this@SmartCADetailFragment, fileUrlReady) { url ->
                try {
                    // Mở URL trong trình duyệt
                    val intent = Intent(Intent.ACTION_VIEW, url.toUri())
                    startActivity(intent)
                } catch (e: Exception) {
                    showNoticeDialog(resources.getString(com.vietinbank.core_ui.R.string.feature_smart_ca_confirm_cant_open_file, e.message))
                }
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.state.collectAsState()
        AppTheme {
            SmartCAConfirmScreen(
                state = uiState,
                onAction = viewModel::onAction,
            )
        }
    }
}