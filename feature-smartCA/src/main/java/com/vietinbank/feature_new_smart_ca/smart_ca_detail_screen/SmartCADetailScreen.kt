package com.vietinbank.feature_new_smart_ca.smart_ca_detail_screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_data.models.response.DisplayModelUI
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_new_smart_ca.smart_ca_detail_screen.SmartCADetailViewModel.SmartCADetailAction

@Composable
fun SmartCAConfirmScreen(
    state: SmartCADetailViewModel.SmartCADetailState,
    onAction: ((SmartCADetailViewModel.SmartCADetailAction) -> Unit),
) {
    val scrollState = rememberScrollState()

    Box(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding(),
    ) {
        Column {
            FoundationAppBar(
                modifier = Modifier.padding(vertical = FoundationDesignSystem.Sizer.Gap.gap16),
                isLightIcon = false,
                isSingleLineAppBar = true,
                title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_dashboard_list),
                onNavigationClick = { onAction.invoke(SmartCADetailViewModel.SmartCADetailAction.OnBackPressed) },
            )
            if (state.listDisplayInfoCKS.isNotEmpty()) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = FoundationDesignSystem.Sizer.Gap.gap8)
                        .imePadding()
                        .verticalScroll(scrollState)
                        .padding(vertical = FoundationDesignSystem.Sizer.Gap.gap16)
                        .padding(bottom = FoundationDesignSystem.Sizer.Gap.gap80), // Chiều cao của button + padding
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clip(
                                RoundedCornerShape(
                                    AppSizer.Radius.radius32,
                                ),
                            )
                            .background(FoundationDesignSystem.Colors.backgroundBgContainer)
                            .padding(horizontal = FoundationDesignSystem.Sizer.Gap.gap24),
                    ) {
                        FoundationText(
                            text = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_dashboard_info_item_cks),
                            style = FoundationDesignSystem.Typography.headingH3,
                            color = FoundationDesignSystem.Colors.characterHighlighted,
                            modifier = Modifier
                                .padding(
                                    vertical = FoundationDesignSystem.Sizer.Gap.gap24,
                                )
                                .align(Alignment.CenterHorizontally),
                        )

                        HorizontalDivider(
                            color = FoundationDesignSystem.Colors.divider,
                            thickness = FoundationDesignSystem.Sizer.Stroke.stroke1,
                        )
                        state.listDisplayInfoCKS.forEachIndexed { index, item ->
                            SmartCAItemDetailView(
                                model = item,
                                isFirst = index == 0,
                                isLast = index == state.listDisplayInfoCKS.size - 1,
                                actions = { onAction.invoke(SmartCADetailAction.OnDownloadClick) },
                                modifier = Modifier,
                            )
                        }
                    }
                }
            }
        }
        Box(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth()
                .background(
                    Brush.verticalGradient(
                        colors = listOf(
                            FoundationDesignSystem.Colors.dialogBackground.copy(alpha = 0f),
                            FoundationDesignSystem.Colors.dialogBackground.copy(alpha = 0.5f),
                            FoundationDesignSystem.Colors.dialogBackground.copy(alpha = 1f),
                        ),
                        startY = 0f,
                        endY = Float.POSITIVE_INFINITY,
                    ),
                )
                .padding(
                    horizontal = FoundationDesignSystem.Sizer.Gap.gap24,
                    vertical = FoundationDesignSystem.Sizer.Gap.gap8,
                ),
        ) {
            FoundationButton(
                isLightButton = true,
                leadingIcon = if (state.iconResButton != 0) { painterResource(state.iconResButton) } else { null },
                text = state.textButton,
                onClick = { onAction.invoke(SmartCADetailAction.OnClickConfirmButton) },
                enabled = true,
                modifier = Modifier.fillMaxWidth(),
            )
        }
    }
}

@Composable
fun SmartCAItemDetailView(
    model: DisplayModelUI,
    actions: () -> Unit,
    isFirst: Boolean,
    isLast: Boolean,
    modifier: Modifier,
) {
    Box(
        modifier = modifier.safeClickable {
            when (model.clickDirect) {
                Tags.TYPE_FILE -> {
                    actions.invoke()
                }

                else -> null
            }
        },
    ) {
        Column {
            if (isFirst) {
                Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap24))
            } else {
                Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap16))
            }
            FoundationInfoHorizontal(
                title = model.title,
                value = model.value,
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = model.valueColor,
            )
            if (isLast) {
                Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap24))
            }
        }
    }
}
