<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include
        android:id="@+id/openCustomToolbar"
        layout="@layout/custom_toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="@dimen/dp15"
        android:fillViewport="true"
        android:padding="@dimen/dp10"
        app:layout_constraintBottom_toTopOf="@+id/btnCreateMaker"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/openCustomToolbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rcv_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:itemCount="5" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp10"
                android:layout_marginTop="@dimen/dp15"
                android:background="@drawable/bg_white_radius_2dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/rcv_info">

                <com.vietinbank.core_ui.base.views.BaseEditText
                    android:id="@+id/edtMsgTrace"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:digits="0123456789abcdefghijklmnopqrstuvwxyz  ABCDEFGHIJKLMNOPQRSTUVWXYZ"
                    android:inputType="textNoSuggestions|textVisiblePassword"
                    android:hint="Nội dung tra soát"
                    android:maxLines="1"
                    android:textColor="@color/text_blue_02"
                    android:textColorHint="@color/color_hint"
                    android:textSize="@dimen/sp15"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <!-- Dòng mới được thêm vào đây -->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_extraInfo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:background="@drawable/bg_white_radius_2dp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/edtMsgTrace">

                    <com.vietinbank.core_ui.base.views.BaseTextView
                        android:id="@+id/tvExtraLabel"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="Nội dung"
                        android:textColor="@color/text_blue_07"
                        android:textSize="@dimen/sp15"
                        app:fontCus="semi_bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/tvExtraValue"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintHorizontal_weight="1" />

                    <com.vietinbank.core_ui.base.views.BaseTextView
                        android:id="@+id/tvExtraValue"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text=""
                        android:textColor="@color/text_blue_02"
                        android:textSize="@dimen/sp16"
                        android:gravity="end"
                        app:fontCus="semi_bold"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/tvExtraLabel"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintHorizontal_weight="1" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_fromAccount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:background="@drawable/bg_white_radius_2dp"
                    android:visibility="visible"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/cl_extraInfo">

                    <com.vietinbank.core_ui.base.views.BaseTextView
                        android:id="@+id/tvFromAccount"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="Tài khoản thu phí"
                        android:textColor="@color/text_blue_07"
                        android:textSize="@dimen/sp15"
                        app:fontCus="semi_bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/contentFromAccount"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintHorizontal_weight="1" />

                    <com.vietinbank.core_ui.base.views.BaseTextView
                        android:id="@+id/contentFromAccount"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:textColor="@color/text_blue_02"
                        android:textSize="@dimen/sp16"
                        android:gravity="end"
                        app:fontCus="semi_bold"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/tvFromAccount"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintHorizontal_weight="1"
                        tools:text="33,000 VND" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <com.vietinbank.core_ui.base.views.BaseTextView
                    android:id="@+id/titleTotalFee"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:text="Phí tra soát"
                    android:textColor="@color/text_blue_07"
                    android:textSize="@dimen/sp15"
                    app:fontCus="semi_bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/cl_fromAccount" />

                <com.vietinbank.core_ui.base.views.BaseTextView
                    android:id="@+id/tvTotalFee"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:textColor="@color/text_blue_02"
                    android:textSize="@dimen/sp16"
                    app:fontCus="semi_bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/cl_fromAccount"
                    tools:text="33,000 VND" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

    <Button
        android:id="@+id/btnCreateMaker"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp10"
        android:layout_marginBottom="@dimen/dp10"
        android:text="Tiếp tục"
        android:textAllCaps="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>