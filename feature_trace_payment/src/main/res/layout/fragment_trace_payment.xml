<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/toolbar"
        layout="@layout/custom_toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.vietinbank.core_ui.base.views.BaseEditText
        android:id="@+id/edtCodeTrace"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:digits="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
        android:inputType="textNoSuggestions|textVisiblePassword"
        android:layout_marginHorizontal="@dimen/dp10"
        android:hint="Số giao dịch/mã tham chiếu"
        android:textColorHint="@color/color_hint"
        android:textSize="@dimen/sp15"
        android:textColor="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar" />

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/tv1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:lineSpacingExtra="@dimen/dp3"
        android:text="Tra soát giao dịch là dịch vụ khách hàng gửi yêu cầu tới VietinBank để kiểm tra các giao dịch đã thực hiện. Quý khách có thể yêu cầu điều chỉnh thông tin giao dịch, hủy/hoàn trả và một số yêu cầu tra soát khác tùy theo nhu cầu.\n\nSau khi nhận được yêu cầu tra soát, VietinBank sẽ xử lý và phản hồi trong thời gian sớm nhất. Nếu bạn chưa có số giao dịch/mã tham chiếu, vui lòng vấn tin tại đây"
        android:textColor="@color/text_informative_medium"
        android:textSize="@dimen/sp14"
        app:fontCus="medium"
        app:layout_constraintTop_toBottomOf="@+id/edtCodeTrace" />
</androidx.constraintlayout.widget.ConstraintLayout>