package com.vietinbank.feature_trace_payment.comfirm

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.models.TransferObject
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.trace_payment.ReportDetailsDomains
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_trace_payment.TracePaymentViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class TracePaymentComfirmFragment : BaseFragment<TracePaymentViewModel>() {
    override val viewModel: TracePaymentViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.uiState.collectAsState()

        TracePaymentConfirmScreen(
            state = uiState,
            onBackClick = { appNavigator.popBackStack() },
            onHomeClick = { appNavigator.goToHome() },
            onSubmitClick = {
                val error = viewModel.validateTracePaymentFields(uiState.extraContent)
                if (error != null) {
                    showNoticeDialog(error)
                    return@TracePaymentConfirmScreen
                }
                viewModel.traceCreate(uiState.extraContent)
            },
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupArguments()
        observeData()
    }

    private fun setupArguments() {
        val args = arguments ?: return
        val gson = Utils.g().provideGson()

        viewModel.totalFee = args.getString(Tags.TransferType.FEE_STRING_BUNDLE, "") ?: ""

        val feeTrace = args.getString(Tags.COMFIRM_FREETRACE, "") ?: ""

        viewModel.reportDetailsDomains = gson.fromJson(
            args.getString(Tags.TransferType.DETAIL_OBJECT_CONFIRM, ""),
            ReportDetailsDomains::class.java,
        )

        val listJson = args.getString(Tags.TransferType.DETAIL_OBJECT)
        listJson?.let {
            val type = object : TypeToken<List<TransferObject>>() {}.type
            val list: List<TransferObject> = gson.fromJson(it, type)
            viewModel.setTransferList(list, feeTrace, args.getString(Tags.COMFIRM_EDTMSGTRACE, "") ?: "", args.getString(Tags.COMFIRM_ACCOUNTFREE, "") ?: "")
        }

        viewModel.getAccList()
    }

    private fun observeData() {
        viewModel.traceCreate.observe(viewLifecycleOwner) { state ->
            val gson = Utils.g().provideGson()
            val bundle = Bundle().apply {
                putString(Tags.TransferType.TYPE_TRANSFER, Tags.TransferType.TYPE_TRACE_PAYMENT)
                putBoolean(Tags.TransferType.IS_CONTACT_CREATE, false)
                putString(Tags.TransferType.CONFIRM_OBJECT, gson.toJson(viewModel.reportDetailsDomains))
                putString(Tags.TransferType.CREATE_TRANSFER_OBJECT, gson.toJson(state))
                putString(Tags.TransferType.LIST_RESULT_OBJECT, gson.toJson(viewModel.createListResulTracePayment()))
            }
            appNavigator.goToMakerResultTransferFragment(bundle)
        }
    }
}
