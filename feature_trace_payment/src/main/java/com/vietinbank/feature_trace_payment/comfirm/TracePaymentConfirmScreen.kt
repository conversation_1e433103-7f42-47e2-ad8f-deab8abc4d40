package com.vietinbank.feature_trace_payment.comfirm

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_common.models.AppBarAction
import com.vietinbank.core_common.models.TransferObject
import com.vietinbank.core_ui.base.adapter.TransferAdapter
import com.vietinbank.core_ui.base.compose.BaseAppBar
import com.vietinbank.core_ui.base.compose.BaseButton
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.utils.safeClickable

@Composable
fun TracePaymentConfirmScreen(
    state: TracePaymentUIState,
    onBackClick: () -> Unit,
    onHomeClick: () -> Unit,
    onSubmitClick: () -> Unit,
) {
    Column(modifier = Modifier.fillMaxSize()) {
        CustomToolbar(
            title = "Xác nhận",
            onBackClick = onBackClick,
            onHomeClick = onHomeClick,
        )

        LazyColumn(
            modifier = Modifier.weight(1f),
            contentPadding = PaddingValues(horizontal = 10.dp), // Hiệu suất scroll
        ) {
            items(state.transferList) { item ->
                if (item.group == TransferAdapter.TYPE_GROUP_TITLE) {
                    SectionHeader(title = item.title.orEmpty())
                } else {
                    TransferItemView(item)
                }
            }
        }

        BaseButton(
            modifier = Modifier
                .fillMaxWidth()
                .padding(10.dp)
                .safeClickable { onSubmitClick() },
            text = "Chuyển kiểm soát",
        )
    }
}

@Composable
fun CustomToolbar(
    title: String,
    onBackClick: () -> Unit,
    onHomeClick: () -> Unit,
) {
    val appBarActions = mutableListOf<AppBarAction>()
    appBarActions.add(
        AppBarAction(
            icon = com.vietinbank.core_ui.R.drawable.ic_home,
            contentDescription = "HOME",
            tint = Color.White,
            onClick = onHomeClick,
        ),
    )

    BaseAppBar(
        title = title,
        onBackClick = onBackClick,
        actions = appBarActions,
    )
}

@Composable
fun SectionHeader(
    title: String,
    backgroundColor: Color = Color.Transparent,
    textColor: Color = Color.White,
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .background(backgroundColor)
            .padding(vertical = 8.dp),
    ) {
        BaseText(
            text = title,
            color = textColor,
            textSize = 16.sp,
            fontWeight = FontWeight.Medium,
        )
    }
}

@Composable
fun TransferItemView(item: TransferObject) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White)
            .padding(vertical = 8.dp, horizontal = 12.dp),
        horizontalArrangement = Arrangement.SpaceBetween, // Căn các phần tử ra 2 đầu (trái – phải)
        verticalAlignment = Alignment.CenterVertically, // Căn giữa theo chiều dọc
    ) {
        BaseText(
            text = item.title.orEmpty(),
            color = Color.Gray,
            textSize = 14.sp,
            textAlign = TextAlign.Start,
            modifier = Modifier.widthIn(max = 200.dp),
        )

        BaseText(
            text = item.content.orEmpty(),
            color = Color.Gray,
            textSize = 14.sp,
            fontWeight = FontWeight.Normal,
            textAlign = TextAlign.End,
        )
    }
}
