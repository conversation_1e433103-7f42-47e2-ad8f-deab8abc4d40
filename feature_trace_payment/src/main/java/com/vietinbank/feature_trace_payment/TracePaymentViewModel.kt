package com.vietinbank.feature_trace_payment

import android.text.TextUtils
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.livedata.SingleLiveEvent
import com.vietinbank.core_common.models.TransferObject
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.login.AccountDefaultDomain
import com.vietinbank.core_domain.models.login.AccountListDomain
import com.vietinbank.core_domain.models.login.AccountListParams
import com.vietinbank.core_domain.models.maker.CreateTransferDomain
import com.vietinbank.core_domain.models.trace_payment.ReportDetailsDomains
import com.vietinbank.core_domain.models.trace_payment.ReportDetailsParams
import com.vietinbank.core_domain.models.trace_payment.TraceCreateParams
import com.vietinbank.core_domain.models.trace_payment.TraceInquiryDomains
import com.vietinbank.core_domain.models.trace_payment.TraceInquiryParams
import com.vietinbank.core_domain.models.trace_payment.TransactionDomains
import com.vietinbank.core_domain.usecase.login.LoginUseCase
import com.vietinbank.core_domain.usecase.transaction_manage.TransactionManageUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.adapter.TransferAdapter
import com.vietinbank.feature_trace_payment.comfirm.TracePaymentUIState
import com.vietinbank.feture_maker.maker_ui.bottomSheet.AccListBottomSheetFragment
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class TracePaymentViewModel @Inject constructor(
    private val transferUseCase: TransactionManageUseCase,
    private val moneyHelper: MoneyHelper,
    private val loginUseCase: LoginUseCase,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
) : BaseViewModel() {
    var traceInquiryDomains: TraceInquiryDomains? = null
    val transferAdapter = TransferAdapter()
    var reportDetailsDomains: ReportDetailsDomains? = null
    var createTransferDomain: CreateTransferDomain? = null
    val chooseAccountNoDialog = AccListBottomSheetFragment()
    var totalFee = ""
    var currentAccountDefaultDomain: AccountDefaultDomain? = null

    private val _accListState = SingleLiveEvent<Resource<AccountListDomain>>()
    val accListState: SingleLiveEvent<Resource<AccountListDomain>> get() = _accListState

    val _traceInquiryState = SingleLiveEvent<TraceInquiryDomains?>()
    val traceInquiryState: SingleLiveEvent<TraceInquiryDomains?> get() = _traceInquiryState

    val _reportDetail = SingleLiveEvent<ReportDetailsDomains?>()
    val reportDetail: SingleLiveEvent<ReportDetailsDomains?> get() = _reportDetail

    val _traceCreate = SingleLiveEvent<CreateTransferDomain?>()
    val traceCreate: SingleLiveEvent<CreateTransferDomain?> get() = _traceCreate

    private val _uiState = MutableStateFlow(TracePaymentUIState())
    val uiState = _uiState.asStateFlow()

    fun setTransferList(list: List<TransferObject>, feeInfs: String?, contentIns: String?, accountInfs: String?) {
        val extendedList = list + listOf( // Nội dung tra soát
            TransferObject(TransferAdapter.TYPE_GROUP_TITLE, "Nội dung tra soát"),
            TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Nội dung", contentIns.orEmpty()),
            TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Tài khoản thu phí", accountInfs.orEmpty()),
            TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Phí tra soát", feeInfs.orEmpty()),
        )
        _uiState.update { it.copy(transferList = extendedList, extraContent = contentIns.toString()) }
    }

    fun getAccList() {
        launchJob(showLoading = true) {
            val serviceType = when (reportDetailsDomains?.transaction?.tranType ?: "") {
                Tags.TransferType.TYPE_IN -> Tags.TransferType.SERVICE_TYPE_TRANSFER_IN
                Tags.TransferType.TYPE_OUT -> Tags.TransferType.SERVICE_TYPE_TRANSFER_OUT
                else -> Tags.TransferType.SERVICE_TYPE_TRANSFER_NAPAS_CARD
            }
            val params = AccountListParams(
                accountType = "",
                currencySort = "",
                username = userProf.getUserName() ?: "",
                serviceType = serviceType,
            )
            val res = loginUseCase.accList(params)
            handleResource(res) { data ->
                val listAccount = data.accountDefault.filter { it.status == "0" }.toMutableList()
                getDefaultAccount(listAccount)?.let {
                    currentAccountDefaultDomain = it
                }
                chooseAccountNoDialog.setData(listAccount)
                _accListState.postValue(Resource.Success(data))
            }
        }
    }

    fun traceInquiry(mtId: String) {
        launchJob(showLoading = true) {
            val params = TraceInquiryParams(
                username = userProf.getUserName() ?: "",
                mtId = mtId,
            )
            val res = transferUseCase.traceInquiry(params)
            handleResource(res) { data ->
                traceInquiryDomains = data
                reportDetail()
                _traceInquiryState.postValue(data)
            }
        }
    }

    private fun reportDetail() {
        launchJob(showLoading = true) {
            val params = ReportDetailsParams(
                username = userProf.getUserName() ?: "",
                mtId = traceInquiryDomains?.mtId ?: "",
                serviceType = traceInquiryDomains?.serviceType ?: "",
                signType = "",
                trantype = traceInquiryDomains?.tranType ?: "",
                type = "",
            )
            val res = transferUseCase.reportDetail(params)
            handleResource(res) { data ->
                _reportDetail.postValue(data)
            }
        }
    }

    fun traceCreate(traceMsg: String) {
        launchJob(showLoading = true) {
            val params = TraceCreateParams(
                feeAccountNo = currentAccountDefaultDomain?.accountNo ?: "",
                mtId = reportDetailsDomains?.transaction?.mtId ?: "",
                traceMsg = traceMsg,
                username = userProf.getUserName() ?: "",
            )
            val res = transferUseCase.traceCreate(params)
            handleResource(res) { data ->
                createTransferDomain = data
                _traceCreate.postValue(data)
            }
        }
    }

    fun validateTracePaymentFields(
        edtMsgTrace: String?,
    ): String? {
        return when {
            edtMsgTrace.isNullOrBlank() -> "Nhập nội dung tra soát"
            else -> null
        }
    }

    fun createListResulTracePayment(): MutableList<TransferObject> {
        val transferObjects = mutableListOf<TransferObject>()
        if (reportDetailsDomains?.transaction != null) {
            val item = reportDetailsDomains?.transaction
            transferObjects.addAll(
                mutableListOf(
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        title = "Giao dịch gốc",
                        content = item?.tranTypeName ?: "",
                    ),
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        title = "Số giao dịch gốc",
                        content = item?.mtId ?: "",
                    ),
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        title = "Nội dung tra soát",
                        content = _uiState.value.extraContent,
                    ),
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        title = "Phí tra soát",
                        content = getTotalFeeString(),
                    ),
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        title = "Tài khoản thu phí",
                        content = "${currentAccountDefaultDomain?.accountNo ?: ""} - ${currentAccountDefaultDomain?.accountName ?: ""} - ${getMoneyDefaultAccountString()} ",
                    ),
                    TransferObject(
                        TransferAdapter.TYPE_GROUP_CONTENT,
                        title = "Thời gian giao dịch",
                        content = createTransferDomain?.createdDate ?: "",
                    ),
                ),
            )
        }
        return transferObjects
    }

    fun createListConfirmInquiry(): MutableList<TransferObject> {
        val transferObjects = mutableListOf<TransferObject>()
        val item = reportDetailsDomains?.transaction ?: return transferObjects
        // Thêm các thông tin chung của giao dịch (VD: loại giao dịch, trạng thái, mã giao dịch...)
        transferObjects += buildGeneralInfo(item)
        // Xử lý tuỳ theo loại giao dịch
        when (item.tranType) {
            "tx", "if" -> { // Nếu là giao dịch nộp thuế ("tx" là NSNN nội địa/hai quan, "if" là thuế hạ tầng)
                transferObjects += buildTaxPayerInfo(item) // Thêm thông tin người nộp thuế (VD: tên, mã số thuế, địa chỉ)
                transferObjects += buildTaxAgencyInfo(item) // Thêm thông tin cơ quan thuế (VD: tên kho bạc, mã CQ thuế)

                if (item.tranType == "tx") {
                    transferObjects += buildTxSpecificInfo(item) // Nếu là tNSNN nội địa/hai quan: thêm chi tiết các mã chương, tiểu mục
                } else {
                    transferObjects += buildOtherTaxDetails(item) // Nếu là thuế hạ tầng: thêm thông tin về tờ khai, số tờ khai
                }
            }

            else -> {
                transferObjects += buildAccountInfo(item) // Với các loại giao dịch khác (chuyển tiền, thanh toán...), thêm info người gửi
                transferObjects += buildReceiverInfo(item) // Thêm thông tin người nhận, số tiền, nội dung, phí, ngày chuyển
            }
        }

        return transferObjects
    }

    // Thông tin chung
    private fun buildGeneralInfo(item: TransactionDomains): List<TransferObject> {
        val list = mutableListOf<TransferObject>()
        list += TransferObject(TransferAdapter.TYPE_GROUP_TITLE, title = "Thông tin chung")
        list += TransferObject(
            TransferAdapter.TYPE_GROUP_CONTENT,
            "Loại giao dịch",
            item.tranTypeName.orEmpty(),
        )
        list += TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Số giao dịch", item.mtId.orEmpty())
        list += TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Trạng thái", item.statusName.orEmpty())
        item.activityLogs?.let {
            it.createdBy?.let { user ->
                list += listOf(
                    TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Người khởi tạo", "${user.username} - ${user.processDate}"),
                )
            }

            var verifyUser = ""
            it.verifiedBy?.forEachIndexed { index, user ->
                verifyUser += "${user.username ?: ""} - ${user.processDate}"
                if (index < (it.verifiedBy?.size ?: 0) - 1) {
                    verifyUser += "\n"
                }
            }
            if (!verifyUser.isEmpty()) {
                list += listOf(
                    TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Người phê duyệt", verifyUser),
                )
            }
        }
        return list
    }

    // Người nộp thuế
    private fun buildTaxPayerInfo(item: TransactionDomains): List<TransferObject> = listOf(
        TransferObject(TransferAdapter.TYPE_GROUP_TITLE, "Thông tin người nộp thuế"),
        TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Hình thức", if (item.payMethod == "0") "Nộp cho chính đơn vị" else "Nộp thay cho đơn vị khác"),
        TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Tên đơn vị nộp", item.payname.orEmpty()),
        TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Mã số thuế", item.paycode.orEmpty()),
        TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Địa chỉ", item.payadd.orEmpty()),
        TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Từ tài khoản", "${item.fromAccountNo} - ${item.currency} - ${item.fromAccountName}"),
        TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Ngân hàng", item.branchName.orEmpty()),
    )

    // Cơ quan thu
    private fun buildTaxAgencyInfo(item: TransactionDomains): List<TransferObject> = listOf(
        TransferObject(TransferAdapter.TYPE_GROUP_TITLE, "Thông tin cơ quan thu"),
        TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Tỉnh/Thành phố", "${item.provinceCode} - ${item.provinceName}"),
        TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Địa bàn hành chính", "${item.areaCode} - ${item.areaName}"),
        TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Mã kho bạc nhà nước", "${item.treasuryCode} - ${item.treasuryName}"),
        TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Tài khoản ghi thu", "${item.collectionAccountNo} - ${item.collectionAccountName}"),
        TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Mã cơ quan quản lý thu", "${item.collectAgencyCode} - ${item.collectAgencyName}"),
    )

    // Chi tiết giao dịch "tx"
    private fun buildTxSpecificInfo(item: TransactionDomains): List<TransferObject> {
        val list = mutableListOf<TransferObject>()

        if (item.taxType == "04") {
            list += TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Mã chi cục HQ", "${item.bucode} - ${item.buName}")
            list += TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Mã HQ phát hành", "${item.oficode} - ${item.ofiName}")
        }

        list += TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Mã chương", "${item.chapCode} - ${item.chapName}")

        item.declareNumber?.takeIf { it.isNotEmpty() }?.let {
            list += TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Số tờ khai QĐ/thông báo", it)
        }

        if (item.taxType == "04") {
            list += TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Ngày tờ khai", item.declareDate.orEmpty())
            list += TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Loại hình XNK", "${item.ieType} - ${item.ieName}")
            item.uid?.takeIf { it.isNotEmpty() }?.let {
                list += TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "UID", it)
            }
        }

        if (!item.assetCode.isNullOrEmpty() || !item.machineNo.isNullOrEmpty() ||
            !item.machineFeatures.isNullOrEmpty() || !item.propertyAdd.isNullOrEmpty()
        ) {
            list += TransferObject(TransferAdapter.TYPE_GROUP_TITLE, "Thông tin khác")

            item.assetCode?.takeIf { it.isNotEmpty() }?.let {
                list += TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Số khung/ Số tài sản", it)
            }
            item.machineNo?.takeIf { it.isNotEmpty() }?.let {
                list += TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Số máy", it)
            }
            item.machineFeatures?.takeIf { it.isNotEmpty() }?.let {
                list += TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Đặc điểm phương tiện", it)
            }
            item.propertyAdd?.takeIf { it.isNotEmpty() }?.let {
                list += TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Địa chỉ tài sản", it)
            }
        }

        list += TransferObject(TransferAdapter.TYPE_GROUP_TITLE, "Thông tin chi tiết nộp NSNN")

        list += TransferObject(
            TransferAdapter.TYPE_GROUP_CONTENT,
            "Tổng số tiền thực nộp",
            Utils.g().getDotMoneyHasCcy(item.amount.orEmpty(), item.currency.orEmpty()) +
                "\n" + moneyHelper.convertAmountToWords(item.amount.orEmpty(), item.currency.orEmpty()),
        )
        list += TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Phí giao dịch", item.feeAmount.orEmpty())

        item.items?.forEach { file ->
            list += listOf(
                TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Nội dung nộp thuế", file.content.orEmpty()),
                TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Mã NDKT (TM)", file.businessCode.orEmpty()),
                TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Kỳ thuế", file.taxPeriod.orEmpty()),
                TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Số tiền", Utils.g().getDotMoneyHasCcy(file.amount.orEmpty(), file.currency.orEmpty())),
            )
        }

        return list
    }

    // Chi tiết khác (nếu không phải “tx”)
    private fun buildOtherTaxDetails(item: TransactionDomains): List<TransferObject> {
        val list = mutableListOf<TransferObject>()

        list += TransferObject(TransferAdapter.TYPE_GROUP_TITLE, "Thông tin chi tiết khoản nộp")

        val isProvider975 = item.providerCode == "975"

        list += TransferObject(
            TransferAdapter.TYPE_GROUP_CONTENT,
            "ID chứng từ",
            if (isProvider975) item.invoiceId.orEmpty() else item.docId.orEmpty(),
        )
        list += TransferObject(
            TransferAdapter.TYPE_GROUP_CONTENT,
            "Số chứng từ",
            if (isProvider975) item.voucherNumber.orEmpty() else item.docNum.orEmpty(),
        )
        list += TransferObject(
            TransferAdapter.TYPE_GROUP_CONTENT,
            "Ký hiệu chứng từ",
            if (isProvider975) item.voucherSymbol.orEmpty() else item.docSign.orEmpty(),
        )
        list += TransferObject(
            TransferAdapter.TYPE_GROUP_CONTENT,
            "Ngày chứng từ",
            if (isProvider975) item.voucherDate.orEmpty() else item.docDate.orEmpty(),
        )

        if (!isProvider975) {
            list += TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Mã chương", "${item.chapCode} - ${item.chapName}")
            list += TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Mã tiểu mục", item.subsect.orEmpty())
        }

        list += TransferObject(
            TransferAdapter.TYPE_GROUP_CONTENT,
            "Số tiền giao dich",
            Utils.g().getDotMoneyHasCcy(item.amount.orEmpty(), item.currency.orEmpty()),
        )
        list += TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Phí giao dịch", item.feeAmount.orEmpty())

        return list
    }

    // Thông tin tài khoản (nội bộ)
    private fun buildAccountInfo(item: TransactionDomains): List<TransferObject> = listOf(
        TransferObject(TransferAdapter.TYPE_GROUP_TITLE, "Thông tin tài khoản"),
        TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Từ tài khoản", "${item.fromAccountNo} - ${item.currency} - ${item.fromAccountName}"),
    )

    // Tới tài khoản + phí + nội dung
    private fun buildReceiverInfo(item: TransactionDomains): List<TransferObject> {
        val list = mutableListOf<TransferObject>()

        list += TransferObject(TransferAdapter.TYPE_GROUP_TITLE, "Thông tin tài khoản")
        list += TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Tới tài khoản", "${item.toAccountNo}  - ${item.receiveName}")
        list += TransferObject(TransferAdapter.TYPE_GROUP_CONTENT, "Ngân hàng", item.receiveBankName.orEmpty())
        list += TransferObject(
            TransferAdapter.TYPE_GROUP_CONTENT,
            "Số tiền",
            Utils.g().getDotMoneyHasCcy(item.amount.orEmpty(), item.currency.orEmpty()) +
                "\n" + moneyHelper.convertAmountToWords(item.amount.orEmpty(), item.currency.orEmpty()),
        )
        list += TransferObject(
            TransferAdapter.TYPE_GROUP_CONTENT,
            "Phí giao dịch",
            Utils.g().getDotMoneyHasCcy(item.feeAmount.orEmpty(), item.currency.orEmpty()),
        )
        list += TransferObject(
            TransferAdapter.TYPE_GROUP_CONTENT,
            "Hình thức thu phí",
            getFeeMethodName(item.feePayMethod.orEmpty()),
        )
        list += TransferObject(
            TransferAdapter.TYPE_GROUP_CONTENT,
            "Nội dung",
            item.remark.orEmpty(),
        )

        // Thời gian chuyển
        list += if (TextUtils.isEmpty(item.processTime)) {
            listOf(
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    title = "Thời gian chuyển",
                    content = "Chuyển ngay",
                ),
            )
        } else {
            listOf(
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    title = "Thời gian chuyển",
                    content = "Đặt lịch",
                ),
                TransferObject(
                    TransferAdapter.TYPE_GROUP_CONTENT,
                    title = "Ngày đặt lịch",
                    content = item.processTimeDesc.orEmpty(),
                ),
            )
        }

        return list
    }

    fun getFeeMethodName(id: String): String {
        return when (id) {
            "1" -> Tags.TransferType.dataSetFillDataOUR.name
            "0" -> Tags.TransferType.dataSetFillDataBEN.name
            else -> "Không xác định" // "Unknown" in Vietnamese
        }
    }

    fun getDefaultAccount(accountDefault: MutableList<AccountDefaultDomain>): AccountDefaultDomain? {
        return accountDefault.maxByOrNull {
            it.currentBalance?.toDoubleOrNull() ?: Double.MIN_VALUE
        }
    }

    fun getMoneyDefaultAccountString(): String {
        return Utils.g().getDotMoneyHasCcy(
            currentAccountDefaultDomain?.currentBalance ?: "0",
            currentAccountDefaultDomain?.currency ?: "",
        )
    }

    override fun onDisplayErrorMessage(exception: AppException) {
        if (exception is AppException.ApiException && exception.requestPath == Constants.MB_ACCOUNT_LIST) {
            _accListState.postValue(
                Resource.Error(
                    exception.message.toString(),
                    exception.code.toString(),
                    exception,
                ),
            )
        }
        super.onDisplayErrorMessage(exception)
    }

    fun getTotalFeeString(): String {
        return if (TextUtils.isEmpty(totalFee)) {
            "Được tính bởi chi nhánh xử lý giao dịch"
        } else {
            Utils.g().getDotMoneyHasCcy(totalFee, reportDetailsDomains?.transaction?.currency ?: "")
        }
    }
}