package com.vietinbank.feature_trace_payment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.viewbinding.ViewBinding
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.extensions.loadDrawable
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.Utils.asciiOnlyFilter
import com.vietinbank.core_domain.models.login.AccountDefaultDomain
import com.vietinbank.core_domain.models.trace_payment.ReportDetailsDomains
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_trace_payment.databinding.FragmentTracePaymentDetailBinding
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class TracePaymentDetailFragment : BaseFragment<TracePaymentViewModel>() {
    override val viewModel: TracePaymentViewModel by viewModels()
    override val useCompose: Boolean = false

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override fun inflateViewBinding(inflater: LayoutInflater, container: ViewGroup?): ViewBinding {
        return FragmentTracePaymentDetailBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initData()
        initListener()
    }

    private fun initView() {
        (binding as FragmentTracePaymentDetailBinding).apply {
            openCustomToolbar.tvTitleToolbar.text = "Tra soát giao dịch"
            rcvInfo.adapter = viewModel.transferAdapter
            edtMsgTrace.filters = arrayOf(asciiOnlyFilter())
        }
    }

    private fun initData() {
        (binding as FragmentTracePaymentDetailBinding).apply {
            viewModel.reportDetailsDomains = Utils.g().provideGson().fromJson(
                arguments?.getString(
                    Tags.TransferType.DETAIL_OBJECT, "",
                ),
                ReportDetailsDomains::class.java,
            )
            viewModel.totalFee = arguments?.getString(Tags.TransferType.FEE_STRING_BUNDLE, "") ?: ""
            tvTotalFee.setText(viewModel.getTotalFeeString())
            viewModel.getAccList()
            viewModel.transferAdapter.setData(viewModel.createListConfirmInquiry())

            viewModel.accListState.observe(viewLifecycleOwner) { state ->
                when (state) {
                    is Resource.Success -> {
                        viewModel.getDefaultAccount(state.data.accountDefault)?.let {
                            clFromAccount.visibility = View.VISIBLE
                            setTextAccountInfo(viewModel.currentAccountDefaultDomain)
                        }
                    }

                    is Resource.Error -> {
                        clFromAccount.visibility = View.GONE
                    }
                }
            }
        }
    }

    private fun setTextAccountInfo(it: AccountDefaultDomain?) {
        (binding as FragmentTracePaymentDetailBinding).apply {
            contentFromAccount.text =
                "${viewModel.currentAccountDefaultDomain?.accountNo ?: ""} - ${viewModel.currentAccountDefaultDomain?.accountName ?: ""} - ${viewModel.getMoneyDefaultAccountString()} "
        }
    }

    private fun initListener() {
        (binding as FragmentTracePaymentDetailBinding).apply {
            openCustomToolbar.btnBack.setThrottleClickListener {
                appNavigator.popBackStack()
            }
            openCustomToolbar.imgRight2.visibility = View.VISIBLE
            openCustomToolbar.imgRight2.loadDrawable(com.vietinbank.core_ui.R.drawable.ic_home_white)
            openCustomToolbar.imgRight2.setThrottleClickListener {
                appNavigator.goToHome()
            }
            btnCreateMaker.setThrottleClickListener {
                val errorMessage = viewModel.validateTracePaymentFields(
                    edtMsgTrace.text.toString(),
                )
                if (errorMessage != null) {
                    showNoticeDialog(errorMessage)
                    return@setThrottleClickListener
                }

                val bundle = Bundle().apply {
                    putString(
                        Tags.TransferType.DETAIL_OBJECT_CONFIRM,
                        Utils.g().provideGson().toJson(viewModel.reportDetailsDomains),
                    )
                    putString(
                        Tags.TransferType.FEE_STRING_BUNDLE,
                        viewModel.traceInquiryDomains?.totalFee ?: "",
                    )
                    putString(
                        Tags.TransferType.DETAIL_OBJECT,
                        Utils.g().provideGson().toJson(viewModel.createListConfirmInquiry()),
                    )
                    putString(
                        Tags.COMFIRM_EDTMSGTRACE,
                        edtMsgTrace.text.toString(),
                    )
                    putString(
                        Tags.COMFIRM_ACCOUNTFREE,
                        contentFromAccount.text.toString(),
                    )
                    putString(
                        Tags.COMFIRM_FREETRACE,
                        tvTotalFee.text.toString(),
                    )
                }
                appNavigator.goToTracePaymentComfirmFragment(bundle)
            }
            clFromAccount.setThrottleClickListener {
                viewModel.chooseAccountNoDialog.show(parentFragmentManager, "chooseAccountNo")
            }
            viewModel.chooseAccountNoDialog.setOnClickItemListener {
                viewModel.currentAccountDefaultDomain = it
                setTextAccountInfo(viewModel.currentAccountDefaultDomain)
            }
        }
    }
}