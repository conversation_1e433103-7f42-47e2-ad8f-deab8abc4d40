package com.vietinbank.feature_trace_payment

import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.viewbinding.ViewBinding
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.SpannableUtils
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.Utils.asciiOnlyFilter
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_trace_payment.databinding.FragmentTracePaymentBinding
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class TracePaymentFragment : BaseFragment<TracePaymentViewModel>() {
    override val viewModel: TracePaymentViewModel by viewModels()
    override val useCompose: Boolean = false

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override fun inflateViewBinding(inflater: LayoutInflater, container: ViewGroup?): ViewBinding {
        return FragmentTracePaymentBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initData()
        initListener()
    }

    private fun initView() {
        (binding as FragmentTracePaymentBinding).apply {
            toolbar.tvTitleToolbar.text = "Tra soát giao dịch"
            edtCodeTrace.setText("1030325C12122701")
            edtCodeTrace.filters = arrayOf(asciiOnlyFilter())
            edtCodeTrace.setOnFocusChangeListener { _, hasFocus ->
                if (!hasFocus && !TextUtils.isEmpty(edtCodeTrace.text.toString())) {
                    viewModel.traceInquiry(mtId = edtCodeTrace.text.toString())
                }
            }
        }
    }

    private fun initData() {
        (binding as FragmentTracePaymentBinding).apply {
            val mtID = arguments?.getString(Tags.MTID_STRING_BUNDLE) ?: ""
            if (!TextUtils.isEmpty(mtID)) {
                edtCodeTrace.setText(mtID)
                viewModel.traceInquiry(mtId = edtCodeTrace.text.toString())
            }
            viewModel.reportDetail.observe(viewLifecycleOwner) { item ->
                val bundle = Bundle()
                bundle.putString(
                    Tags.TransferType.DETAIL_OBJECT,
                    Utils.g().provideGson().toJson(item),
                )
                bundle.putString(
                    Tags.TransferType.FEE_STRING_BUNDLE,
                    viewModel.traceInquiryDomains?.totalFee ?: "",
                )
                appNavigator.goToTracePaymentDetailFragment(bundle)
            }
        }
    }

    private fun initListener() {
        (binding as FragmentTracePaymentBinding).apply {
            toolbar.btnBack.setThrottleClickListener {
                appNavigator.popBackStack()
            }
            SpannableUtils.makeClickableSpan(
                string = "Tra soát giao dịch là dịch vụ khách hàng gửi yêu cầu tới VietinBank để kiểm tra các giao dịch đã thực hiện. Quý khách có thể yêu cầu điều chỉnh thông tin giao dịch, hủy/hoàn trả và một số yêu cầu tra soát khác tùy theo nhu cầu.\nnSau khi nhận được yêu cầu tra soát, VietinBank sẽ xử lý và phản hồi trong thời gian sớm nhất. Nếu bạn chưa có số giao dịch/mã tham chiếu, vui lòng vấn tin tại đây",
                specialString = "tại đây",
                textView = tv1,
                isUnderlineText = true,
                color = com.vietinbank.core_ui.R.color.secondary,
            ) {
                appNavigator.gotoFilterTransaction()
            }
        }
    }
}