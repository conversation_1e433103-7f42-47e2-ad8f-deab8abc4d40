package com.vietinbank.core_ekyc.update_ekyc.confirm_update_ekyc

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ekyc.update_ekyc.view.BaseVerifyView
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.feature_ekyc.R

@Composable
fun ConfirmUpdateEkycScreen(
    onHomeClick: () -> Unit,
) {
    BaseVerifyView(
        appBarTitle = "Kết quả",
        secondButtonText = "Trang chủ",
        isBackHome = true,
        onBackClick = {
            onHomeClick()
        },
        onSecondButtonClick = {
            onHomeClick()
        },
        content = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .padding(horizontal = 16.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 20.dp, vertical = 12.dp),
                    shape = RoundedCornerShape(2.dp),
                    colors = CardDefaults.cardColors(containerColor = Color.White),
                ) {
                    Column(
                        modifier = Modifier.padding(20.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.spacedBy(15.dp, Alignment.CenterVertically),
                    ) {
                        Image(
                            modifier = Modifier
                                .size(43.dp),
                            painter = painterResource(R.drawable.ic_verify_done),
                            contentDescription = "",
                        )
                        Image(
                            modifier = Modifier
                                .size(width = 106.dp, height = 12.dp),
                            painter = painterResource(R.drawable.ic_logo_efast),
                            contentDescription = "",
                        )
                        Text(
                            textAlign = TextAlign.Center,
                            style = getComposeFont(1, fontColor = Color(0xFF353C4D)),
                            text = "Quý khách đã cập nhật thông tin sinh trắc học thành công",
                        )
                    }
                }
            }
        },
    )
}