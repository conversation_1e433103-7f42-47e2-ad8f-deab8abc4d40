package com.vietinbank.core_ekyc.update_ekyc.update_biometric

import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ekyc.UpdateBiometricUiState
import com.vietinbank.core_ekyc.update_ekyc.view.BaseVerifyView
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.feature_ekyc.R

@Composable
fun UpdateBiometricScreen(
    updateBiometricUiState: UpdateBiometricUiState,
    imageLoader: CoilImageLoader,
    guideText: AnnotatedString,
    onFirstButtonClick: () -> Unit,
    onSecondButtonClick: () -> Unit,
    onBackClick: () -> Unit,
    onHomeClick: () -> Unit,
) {
    BaseVerifyView(
        appBarTitle = "Cập nhật sinh trắc học",
        firstButtonText = "Đăng ký lại sinh trắc học",
        secondButtonText = "Đồng ý",
        onBackClick = { onBackClick() },
        onFirstButtonClick = { onFirstButtonClick() },
        onSecondButtonClick = { onSecondButtonClick() },
        onHomeClick = { onHomeClick() },
        isShowHomeTopAppBar = true,
        content = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .padding(horizontal = 20.dp),
                verticalArrangement = Arrangement.Top,
                horizontalAlignment = Alignment.Start,
            ) {
                BaseText(
                    modifier = Modifier.padding(bottom = 24.dp, top = 12.dp),
                    fontWeight = FontWeight(600),
                    text = "Quý khách đã đăng ký xác thực sinh trắc học",
                    color = Color.White,
                )
                Card(
                    modifier = Modifier
                        .fillMaxWidth(),
                    shape = RoundedCornerShape(2.dp),
                    colors = CardDefaults.cardColors(containerColor = Color.White),
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        Box(
                            modifier = Modifier
                                .padding(24.dp)
                                .size(height = 150.dp, width = 136.dp),
                        ) {
                            imageLoader.LoadByteArray(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .border(1.dp, Color(0xFFC6C6C6), RoundedCornerShape(8.dp))
                                    .clip(RoundedCornerShape(8.dp)),
                                byteArray = updateBiometricUiState.image,
                                placeholderRes = R.drawable.white_background,
                                errorRes = R.drawable.white_background,
                                isCache = false,
                            )
                            Image(
                                modifier = Modifier
                                    .padding(5.dp)
                                    .size(18.dp)
                                    .align(Alignment.TopEnd),
                                painter = painterResource(R.drawable.ic_verified_user),
                                contentDescription = "",
                            )
                        }
                        Text(
                            modifier = Modifier
                                .padding(horizontal = 16.dp)
                                .padding(bottom = 24.dp),
                            style = getComposeFont(1, fontColor = Color(0xFF353C4D)),
                            text = guideText,
                        )
                    }
                }
            }
        },
    )
}