package com.vietinbank.core_ekyc.update_ekyc.pre_approve

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.fragment.app.setFragmentResultListener
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.UserInfoDataHolder
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.softotp.ISoftManager
import com.vietinbank.core_domain.softotp.ISoftResult
import com.vietinbank.core_ekyc.ApiEvent
import com.vietinbank.core_ekyc.EkycViewModel
import com.vietinbank.core_ekyc.update_ekyc.navigation.EkycNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_home.ui.home.update_ekyc.pre_approve.PreApproveEkycScreen
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class PreApproveEkycFragment : BaseFragment<EkycViewModel>(), ISoftResult {
    override val viewModel: EkycViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var ekycNavigator: EkycNavigator

    @Inject
    lateinit var softManager: ISoftManager

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.updateMtid(arguments?.getString(Tags.MT_ID) ?: "")
        arguments?.getString(Tags.LIST_METHOD)?.let { data ->
            try {
                val type = object : TypeToken<List<String>>() {}.type
                viewModel.updateListMethod(Utils.g().provideGson().fromJson(data, type))
            } catch (e: Exception) {
                printLog("Lỗi parse JSON list method: ${e.message}", "pre approve Ekyc")
                e.printStackTrace()
            }
        }
        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.apiEvent.collect {
                    when (it) {
                        is ApiEvent.Success -> {
                            when (it.data) {
                                Tags.SOFT_OTP -> {
                                    appNavigator.goToEnterPIN(
                                        VSoftConstants.VtpOTPFlowType.CONFIRM_IN_EFAST.name,
                                    )
                                }
                                Tags.KEY_PASS -> {
                                    navigateToConfirmKeypass()
                                }
                            }
                        }
                        is ApiEvent.ShowError -> {}
                    }
                }
            }
        }
        setupPinResultListener()
        softManager.setSoftResultListener(this)
    }

    private fun navigateToConfirmKeypass() {
        val isFromRegisterScreen = UserInfoDataHolder.isFromRegisterScreen()
        val preApproveState = viewModel.preApproveUiState.value

        appNavigator.goToConfirmKeypass(
            challengeCode = preApproveState.challengeCode,
            confirmType = if (isFromRegisterScreen) Tags.REGISTER_EKYC else Tags.UPDATE,
            transaction = "",
            mtId = preApproveState.transactionMtId,
            originalTransaction = "",
            nextApprover = "",
            ekycId = if (isFromRegisterScreen) UserInfoDataHolder.getEkycID() else "",
        )
    }
    private fun setupPinResultListener() {
        viewLifecycleOwner.lifecycleScope.launch {
            setFragmentResultListener(VSoftConstants.RESULT_VERIFY_PIN) { _, bundle ->
                handlePinVerificationResult(bundle)
            }
        }
    }

    private fun handlePinVerificationResult(bundle: Bundle) {
        try {
            if (bundle.getBoolean(VSoftConstants.Bundle.KEY_DATA_1, false)) {
                softManager.genSoftOtp(
                    userId = viewModel.getKeypassProfile() ?: "",
                    transactionId = viewModel.preApproveUiState.value.transactionId ?: "",
                    messageId = "",
                )
            }
        } catch (_: Exception) {
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState = viewModel.preApproveUiState.collectAsState()
        AppTheme {
            PreApproveEkycScreen(
                uiState = uiState.value,
                onBackClick = { ekycNavigator.popBackStack() },
                onSecondButtonClick = { authMethod ->
                    when (authMethod) {
                        Tags.SOFT_OTP -> {
                            if (!softManager.isAllowSoft()) {
                                showNoticeDialog("Quý khách chưa đăng ký phương thức xác thực giao dịch. Quý khách vui lòng liên hệ CN/ PGD VietinBank gần nhất để được hỗ trợ")
                            } else if (softManager.getStatusLockSoft() != null) {
                                // da kich hoat soft nhung bị khóa do nhập sai 5 lần
                                showNoticeDialog(softManager.getStatusLockSoft() ?: "")
                            } else if (softManager.isUserActive) {
                                // user da duoc kich hoat tren thiet bi
                                viewModel.genSOTPTransCode()
                            } else {
                                showConfirmDialog(
                                    "Quý khách vui lòng kích hoạt Soft OTP để tiếp tục thực hiện giao dịch",
                                    positiveAction = {
                                        if (softManager.isAppActive) {
                                            // kich hoat lan thu 2
                                            appNavigator.goToEnterPIN(VSoftConstants.VtpOTPFlowType.ACTIVE.name)
                                        } else {
                                            appNavigator.gotoActivSoft()
                                        }
                                    },
                                    negativeAction = {},
                                )
                            }
                        }

                        Tags.KEY_PASS -> {
                            viewModel.genKeyPassChallengeCode()
                        }

                        else -> {
                            // ky so
                        }
                    }
                },
            )
        }
    }

    override fun onSuccess(otpCode: String?, timeCount: Int?) {
        printLog("OTP from Confirm Checker: $otpCode")
        val isFromRegisterScreen = UserInfoDataHolder.isFromRegisterScreen()
        val preApproveState = viewModel.preApproveUiState.value
        appNavigator.goToConfirmOTP(
            transaction = "",
            transactionID = preApproveState.transactionId,
            token = otpCode ?: "",
            confirmType = if (isFromRegisterScreen) Tags.REGISTER_EKYC else Tags.UPDATE,
            step = timeCount ?: 0,
            nextApprover = "",
            originalTransaction = viewModel.getOriginalTransaction().let {
                Utils.g().provideGson().toJson(it)
            } ?: "",
            mtId = preApproveState.transactionMtId,
            ekycId = if (isFromRegisterScreen) UserInfoDataHolder.getEkycID() else "",
        )
    }

    override fun isActive(isActive: Boolean?) {
    }

    override fun onError(message: String?, code: Int?) {
        message?.let {
            showNoticeDialog(it)
        }
    }
}