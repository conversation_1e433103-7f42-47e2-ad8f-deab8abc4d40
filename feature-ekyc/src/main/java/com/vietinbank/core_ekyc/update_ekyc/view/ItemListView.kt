package com.vietinbank.core_ekyc.update_ekyc.view

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.theme.AppColors

@Composable
fun ItemListView(
    modifier: Modifier = Modifier,
    shape: RoundedCornerShape,
    showTopBorder: Boolean = false,
    leadingFontWeight: FontWeight = FontWeight(500),
    leadingText: String = "",
    trailingText: String = "",
    leadingTextColor: Color = Color(0xFF697F90),
    trailingTextColor: Color? = null,
) {
    Box(
        modifier = modifier
            .clip(shape)
            .background(Color.White),
    ) {
        // Viền phía trên để ngăn cách các item (trừ item đầu tiên va item thứ hai)
        if (showTopBorder) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(0.5.dp)
                    .background(AppColors.borderColor)
                    .align(Alignment.TopCenter),
            )
        }

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween,
        ) {
            BaseText(
                text = leadingText,
                color = leadingTextColor,
                fontWeight = leadingFontWeight,
            )
            BaseText(
                modifier = Modifier.widthIn(max = 165.dp),
                text = trailingText,
                color = trailingTextColor ?: Color(0xFF062A46),
                fontWeight = FontWeight(600),
            )
        }
    }
}