package com.vietinbank.core_ekyc.update_ekyc.view

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.models.AppBarAction
import com.vietinbank.core_ui.base.compose.BaseAppBar
import com.vietinbank.core_ui.base.compose.BaseButton
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_ekyc.R

@Composable
fun BaseVerifyView(
    appBarTitle: String = "",
    onBackClick: () -> Unit = {},
    onHomeClick: () -> Unit = {},
    onFirstButtonClick: () -> Unit = {},
    onSecondButtonClick: () -> Unit = {},
    firstButtonText: String? = null,
    secondButtonText: String? = null,
    isEnableSecondButton: Boolean = true,
    isShowHomeTopAppBar: Boolean = false,
    isBackHome: Boolean = false,
    content: @Composable ColumnScope.() -> Unit = {},
    bottomBarContent: @Composable ColumnScope.() -> Unit = {},
) {
    Column(
        modifier = Modifier.fillMaxSize(),
    ) {
        BaseAppBar(
            title = appBarTitle,
            isBackHome = isBackHome,
            onBackClick = onBackClick,
            actions = if (isShowHomeTopAppBar) {
                listOf(
                    AppBarAction(
                        icon = R.drawable.ic_home,
                        contentDescription = "ic_home",
                        onClick = { onHomeClick() },
                        tint = Color.White,
                    ),
                )
            } else {
                emptyList()
            },
        )
        content()
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(topStart = 10.dp, topEnd = 10.dp))
                .background(Color.White)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp),
        ) {
            bottomBarContent()
            Row(
                modifier = Modifier
                    .height(52.dp)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(16.dp),
            ) {
                if (firstButtonText != null) {
                    BaseButton(
                        modifier = Modifier
                            .weight(1f)
                            .safeClickable {
                                onFirstButtonClick()
                            },
                        text = firstButtonText,
                        isCancelButton = true,
                    )
                }
                BaseButton(
                    modifier = Modifier
                        .weight(1f)
                        .then(
                            if (isEnableSecondButton) {
                                Modifier.safeClickable {
                                    onSecondButtonClick()
                                }
                            } else {
                                Modifier
                            },
                        ),
                    text = secondButtonText,
                    isCancelButton = !isEnableSecondButton,
                )
            }
        }
    }
}
