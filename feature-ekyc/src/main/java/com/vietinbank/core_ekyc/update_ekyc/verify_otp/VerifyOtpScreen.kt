package com.vietinbank.core_ekyc.update_ekyc.verify_otp

import android.annotation.SuppressLint
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ekyc.VerifyOtpUiState
import com.vietinbank.core_ekyc.update_ekyc.view.BaseVerifyView
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_ekyc.R

@SuppressLint("DefaultLocale")
@Composable
fun VerifyOtpScreen(
    uiState: VerifyOtpUiState,
    guideText: AnnotatedString,
    onVerifyOtpClick: (String) -> Unit,
    onBackClick: () -> Unit,
    onClickSendOtp: () -> Unit,
    onHomeClick: () -> Unit,
) {
    var otp by remember { mutableStateOf("") }
    var otpComplete by remember { mutableStateOf(false) }

    BaseVerifyView(
        appBarTitle = "Nhập OTP",
        secondButtonText = "Xác nhận",
        onBackClick = { onBackClick() },
        onSecondButtonClick = {
            onVerifyOtpClick(otp)
        },
        isEnableSecondButton = otpComplete,
        isShowHomeTopAppBar = true,
        onHomeClick = { onHomeClick() },
        content = {
            Column(modifier = Modifier.weight(1f)) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 24.dp)
                        .clip(RoundedCornerShape(2.dp))
                        .background(Color.White)
                        .padding(24.dp),
                    verticalArrangement = Arrangement.spacedBy(15.dp),
                ) {
                    BaseText(
                        text = "Vui lòng kiểm tra kỹ thông tin trước khi xác nhận",
                        backgroundColor = Color(0xFFFFFAE4),
                        textSize = 14.sp,
                        padding = PaddingValues(7.dp),
                        color = Color(0xFF4B6477),
                        leftDrawable = R.drawable.ic_guide_warning,
                        fontWeight = FontWeight(500),
                    )
                    Text(
                        text = guideText,
                        style = getComposeFont(1, fontColor = Color(0xFF353C4D)),
                    )
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        BaseText(
                            text = "Thời gian hiệu lực OTP còn ",
                            color = Color(0xFF353C4D),
                            textSize = 14.sp,
                            fontCus = 0,
                        )
                        BaseText(
                            text = uiState.countDownOtpTime,
                            color = Color(0xFF2183DB),
                            textSize = 14.sp,
                            fontCus = 2, // Semi-bold
                        )
                    }

                    // OTP Input fields
                    OtpInput(
                        onOtpComplete = { number, isOtpComplete ->
                            otp = number
                            otpComplete = isOtpComplete
                        },
                        isOtpFail = uiState.isFailOtp,
                    )
                    if (uiState.isFailOtp) {
                        BaseText(
                            text = "Mã OTP không chính xác",
                            color = Color(0xFFD71249),
                        )
                    }
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .then(
                                if (uiState.isTimeOut1minute) {
                                    Modifier.safeClickable {
                                        onClickSendOtp()
                                    }
                                } else {
                                    Modifier
                                },
                            ),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center,
                    ) {
                        BaseText(
                            text = "Gửi lại mã OTP ",
                            color = if (uiState.isTimeOut1minute) Color(0xFF2A74C8) else Color(0xFFB8C0C7),
                            textSize = 16.sp,
                            fontCus = 2, // Semi-bold
                        )
                        if (!uiState.isTimeOut1minute) {
                            BaseText(
                                text = "(${uiState.countDownClickOtp})",
                                color = Color(0xFFB8C0C7),
                                textSize = 16.sp,
                                fontCus = 2, // Semi-bold
                            )
                        }
                    }
                }
            }
        },
    )
}

@Composable
fun OtpInput(
    otpLength: Int = 6,
    isOtpFail: Boolean = false,
    onOtpComplete: (String, Boolean) -> Unit,
) {
    val focusRequester = remember { FocusRequester() }
    val keyboardController = LocalSoftwareKeyboardController.current

    var otpValue by remember { mutableStateOf("") }
    val otpChars = if (otpValue.length <= otpLength) {
        otpValue.padEnd(otpLength, ' ')
    } else {
        otpValue.substring(0, otpLength)
    }
    LaunchedEffect(Unit) {
        focusRequester.requestFocus()
    }
    Box {
        Row(
            horizontalArrangement = Arrangement.spacedBy(16.dp, Alignment.CenterHorizontally),
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
                .border(
                    1.dp,
                    Color(if (!isOtpFail) 0xFFADD8E6 else 0xFFD71249),
                    RoundedCornerShape(8.dp),
                )
                .padding(16.dp)
                .safeClickable {
                    focusRequester.requestFocus()
                },

        ) {
            otpChars.forEach { char ->
                Box(
                    contentAlignment = Alignment.Center,
                    modifier = Modifier
                        .size(24.dp, 36.dp)
                        .drawBehind {
                            drawLine(
                                color = Color.LightGray,
                                start = Offset(0f, size.height),
                                end = Offset(size.width, size.height),
                                strokeWidth = 2.dp.toPx(),
                            )
                        },
                ) {
                    BaseText(
                        text = if (char != ' ') char.toString() else "",
                        color = Color(0xFF4285F4),
                        textSize = 20.sp,
                        fontCus = 2,
                    )
                }
            }
        }
        // Hidden text field to capture input
        BasicTextField(
            value = otpValue,
            onValueChange = {
                if (it.length <= otpLength && it.all { ch -> ch.isDigit() }) {
                    otpValue = it
                    if (it.length == otpLength) {
                        keyboardController?.hide()
                        onOtpComplete(it, true)
                    } else {
                        onOtpComplete("", false)
                    }
                }
            },
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.NumberPassword,
            ),
            modifier = Modifier
                .fillMaxWidth()
                .height(0.dp)
                .alpha(0f)
                .focusRequester(focusRequester),
        )
    }
}