package com.vietinbank.core_ekyc.update_ekyc.navigation

import android.os.Bundle
import androidx.fragment.app.FragmentManager
import androidx.navigation.NavController
import androidx.navigation.NavOptions
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.feature_ekyc.R

class EkycNavigatorImpl(
    private val navController: NavController,
    private val fragmentManager: FragmentManager,
) : EkycNavigator {

    /**
     * Tạo NavOptions cho màn forward:
     * - enterAnim, exitAnim, popEnterAnim, popExitAnim
     */
    private fun createSlideNavOptions(): NavOptions {
        val builder = NavOptions.Builder()
            .setEnterAnim(R.anim.slide_in_right)
            .setExitAnim(R.anim.slide_out_left)
            .setPopEnterAnim(R.anim.slide_in_left)
            .setPopExitAnim(R.anim.slide_out_right)

        return builder.build()
    }

    override fun setFragmentResult(requestKey: String, result: Bundle) {
        // Lấy NavHostFragment
        fragmentManager.setFragmentResult(requestKey, result)
    }

    override fun goToUpdateBiometric() {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.updateBiometricFragment, null, navOptions)
    }

    override fun goToVerifyOtpFromUpdateBiometric(genOtpTime: Long) {
        val navOptions = createSlideNavOptions()
        val bundle = Bundle().apply {
            putLong(Tags.GEN_OTP_TINE, genOtpTime)
        }
        navController.navigate(R.id.verifyOtpFragment, bundle, navOptions)
    }

    override fun goToUserInfoFromVerifyOtp() {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.userInfoFragment, null, navOptions)
    }

    override fun goToShowContractFromUserInfo() {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.showContractFragment, null, navOptions)
    }

    override fun goToShowDetailContract() {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.detailContractFragment, null, navOptions)
    }

    override fun popToBioMetricScreen() {
        val navOptions = NavOptions.Builder()
            .setEnterAnim(R.anim.slide_in_right)
            .setExitAnim(R.anim.slide_out_left)
            .setPopEnterAnim(R.anim.slide_in_left)
            .setPopExitAnim(R.anim.slide_out_right)
            .setPopUpTo(R.id.updateBiometricFragment, true) // xóa toàn bộ back stack
            .build()
        navController.navigate(R.id.updateBiometricFragment, null, navOptions)
    }

    override fun popToContractScreen() {
        val navOptions = NavOptions.Builder()
            .setEnterAnim(R.anim.slide_in_right)
            .setExitAnim(R.anim.slide_out_left)
            .setPopEnterAnim(R.anim.slide_in_left)
            .setPopExitAnim(R.anim.slide_out_right)
            .setPopUpTo(R.id.updateBiometricFragment, true) // xóa toàn bộ back stack
            .build()
        navController.navigate(R.id.showContractFragment, null, navOptions)
    }

    override fun goToPreApproveEkyc(mtId: String, listMethod: String) {
        val navOptions = createSlideNavOptions()
        val bundle = Bundle().apply {
            putString(Tags.MT_ID, mtId)
            putString(Tags.LIST_METHOD, listMethod)
        }
        navController.navigate(R.id.preApproveEkycFragment, bundle, navOptions)
    }

    override fun popBackStack() {
        navController.popBackStack()
    }

    override fun popBackGraph() {
        navController.popBackStack(R.id.update_ekyc_nav_graph, false)
    }
}