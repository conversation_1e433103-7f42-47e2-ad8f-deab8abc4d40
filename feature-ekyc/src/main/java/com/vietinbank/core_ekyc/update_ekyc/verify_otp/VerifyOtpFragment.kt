package com.vietinbank.core_ekyc.update_ekyc.verify_otp

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ekyc.ApiEvent
import com.vietinbank.core_ekyc.EkycViewModel
import com.vietinbank.core_ekyc.GenOtpEvent
import com.vietinbank.core_ekyc.update_ekyc.navigation.EkycNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class VerifyOtpFragment : BaseFragment<EkycViewModel>() {
    override val viewModel: EkycViewModel by viewModels()
    override val useCompose: Boolean = true

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        getBundleData()
        viewModel.clearFileHolder()
        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                launch {
                    viewModel.apiEvent.collect {
                        when (it) {
                            is ApiEvent.Success -> {
                                ekycNavigator.goToUserInfoFromVerifyOtp()
                            }

                            is ApiEvent.ShowError -> {
                            }
                        }
                    }
                }
                launch {
                    viewModel.genOtpEvent.collect {
                        when (it) {
                            is GenOtpEvent.GenerateSuccess -> {
                                viewModel.verifyOtpCountDown(it.currentTime)
                            }

                            is GenOtpEvent.ShowError -> {}
                        }
                    }
                }
            }
        }
    }

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var ekycNavigator: EkycNavigator

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.verifyUiState.collectAsState()
        val text =
            "Mã xác thực OTP đã được gửi đến số điện thoại ${viewModel.maskPhone()} và email ${viewModel.maskEmail()}. " +
                "Mã xác thực có thời hạn sử dụng trong 5 phút. Quý khách vui lòng nhập mã xác thực vào ô bên dưới."
        val listBoldText = listOf(
            viewModel.maskPhone(),
            viewModel.maskEmail(),
        )
        AppTheme {
            VerifyOtpScreen(
                uiState = uiState,
                guideText = viewModel.buildBoldText(text, listBoldText),
                onBackClick = { ekycNavigator.popBackStack() },
                onVerifyOtpClick = {
                    viewModel.verifyOtpEkycRetail(it)
                },
                onClickSendOtp = {
                    viewModel.generateOtpRetail()
                },
                onHomeClick = { appNavigator.goToHome() },
            )
        }
    }

    private fun getBundleData() {
        arguments?.getString(Tags.ADDFIELD1_BUNDLE)?.let { data ->
            viewModel.addField1 = data
        }
        arguments?.getString(Tags.EFAST_ID_BUNDLE)?.let { data ->
            viewModel.eFastID = data
        }
        arguments?.getString(Tags.CIFNO_BUNDLE)?.let { data ->
            viewModel.currentCifNo = data
        }
        arguments?.getLong(Tags.GEN_OTP_TINE)?.let {
            viewModel.verifyOtpCountDown(it)
        }
    }
}