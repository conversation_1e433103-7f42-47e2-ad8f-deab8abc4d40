package com.vietinbank.core_ekyc.update_ekyc.detail_contract

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.base.compose.BaseAppBar
import com.vietinbank.feature_home.ui.home.update_ekyc.show_contract.PdfViewerFromBytes
import kotlin.io.encoding.ExperimentalEncodingApi

@OptIn(ExperimentalEncodingApi::class)
@Composable
fun DetailContractScreen(
    onBackClick: () -> Unit,
    fileContract: ByteArray?,
) {
    Column(
        modifier = Modifier.fillMaxSize(),
    ) {
        BaseAppBar(
            title = "Chi tiết giấy đề nghị",
            onBackClick = onBackClick,
        )

        Column(
            modifier = Modifier.weight(1f)
                .padding(horizontal = 20.dp)
                .padding(top = 26.dp),
        ) {
            PdfViewerFromBytes(pdfBytes = fileContract, isPreventTouchEvent = false)
        }
    }
}