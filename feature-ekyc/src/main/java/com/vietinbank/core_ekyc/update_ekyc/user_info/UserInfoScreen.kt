package com.vietinbank.core_ekyc.update_ekyc.user_info

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ekyc.UserInfoUiState
import com.vietinbank.core_ekyc.update_ekyc.view.BaseVerifyView
import com.vietinbank.core_ekyc.update_ekyc.view.ItemListView
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.feature_ekyc.R

@Composable
fun UserInfoScreen(
    imageLoader: CoilImageLoader,
    uiState: UserInfoUiState,
    onButtonClick: () -> Unit,
    onBackClick: () -> Unit,
) {
    val scrollState = rememberScrollState()
    val isHaveDiffData = uiState.userInfo.any { it.third }
    val listDataNotBlank = uiState.userInfo.filter { it.second.isNotBlank() }

    BaseVerifyView(
        appBarTitle = if (uiState.isTypeIdCard) "Thông tin CCCD" else "Thông tin Hộ chiếu",
        secondButtonText = "Tiếp tục",
        onBackClick = { onBackClick() },
        onSecondButtonClick = { onButtonClick() },
        content = {
            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(horizontal = 16.dp)
                    .verticalScroll(scrollState),
                verticalArrangement = Arrangement.spacedBy(10.dp),
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(87.dp)
                        .padding(vertical = 10.dp),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                ) {
                    imageLoader.LoadByteArray(
                        modifier = Modifier
                            .size(87.dp)
                            .clip(RoundedCornerShape(2.dp)),
                        byteArray = uiState.biometricImage,
                        placeholderRes = R.drawable.white_background,
                        errorRes = R.drawable.white_background,
                        isCache = false,
                    )

                    uiState.listImage.forEachIndexed { _, byteImage ->
                        imageLoader.LoadByteArray(
                            modifier = Modifier
                                .widthIn(min = 87.dp, max = 134.dp),
                            byteArray = byteImage,
                            placeholderRes = R.drawable.white_background,
                            errorRes = R.drawable.white_background,
                            isCache = false,
                        )
                    }
                }
                if (isHaveDiffData) {
                    BaseText(
                        color = Color.White,
                        text = "Thông tin được đánh dấu đỏ là thông tin có thay đổi so với hồ sơ Quý khách đã đăng ký trước đây với VietinBank",
                    )
                }
                Column(
                    modifier = Modifier.padding(bottom = 20.dp),
                ) {
                    ItemListView(
                        leadingText = if (uiState.isTypeIdCard) "Thông tin Căn cước Công dân" else "Thông tin Hộ chiếu",
                        shape = RoundedCornerShape(topStart = 10.dp, topEnd = 10.dp),
                        leadingFontWeight = FontWeight(600),
                        leadingTextColor = Color(0xFF062A46),
                    )

                    listDataNotBlank.forEachIndexed { index, item ->
                        val isLastItem = index == listDataNotBlank.size - 1

                        // Bo góc dựa vào vị trí
                        val cornerShape = when {
                            isLastItem -> RoundedCornerShape(bottomStart = 10.dp, bottomEnd = 10.dp)
                            else -> RoundedCornerShape(0.dp)
                        }
                        val trailingColor =
                            if (item.third) Color(0xFFD71249) else null
                        ItemListView(
                            leadingText = item.first,
                            trailingText = item.second,
                            shape = cornerShape,
                            showTopBorder = index != 0,
                            trailingTextColor = trailingColor,
                        )
                    }
                }
            }
        },
    )
}
