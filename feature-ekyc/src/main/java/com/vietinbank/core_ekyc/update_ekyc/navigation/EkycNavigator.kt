package com.vietinbank.core_ekyc.update_ekyc.navigation

import android.os.Bundle

interface EkycNavigator {
    fun setFragmentResult(requestKey: String, result: Bundle)
    fun goToUpdateBiometric()
    fun goToVerifyOtpFromUpdateBiometric(genOtpTime: Long)
    fun goToUserInfoFromVerifyOtp()
    fun goToShowContractFromUserInfo()
    fun goToShowDetailContract()
    fun popToBioMetricScreen()
    fun popToContractScreen()
    fun goToPreApproveEkyc(mtId: String, listMethod: String)
    fun popBackStack()
    fun popBackGraph()
}