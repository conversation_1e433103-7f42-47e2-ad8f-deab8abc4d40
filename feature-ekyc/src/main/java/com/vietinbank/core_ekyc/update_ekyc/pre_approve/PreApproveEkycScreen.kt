package com.vietinbank.feature_home.ui.home.update_ekyc.pre_approve

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.RadioButton
import androidx.compose.material3.RadioButtonDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ekyc.PreApproveEkycUiState
import com.vietinbank.core_ekyc.update_ekyc.view.BaseVerifyView
import com.vietinbank.core_ekyc.update_ekyc.view.ItemListView
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable

@Composable
fun PreApproveEkycScreen(
    uiState: PreApproveEkycUiState,
    onBackClick: () -> Unit,
    onSecondButtonClick: (String) -> Unit,
) {
    var selectedOption by remember { mutableStateOf("Soft OTP") }
    val list = listOf(
        "Dịch vụ" to "Đăng ký sinh trắc học",
        "Thông tin đăng ký" to "Khuôn mặt",
        "Kênh đăng ký" to "eFast Mobile App",
    )
    val options = listOf(
        "Soft OTP",
        "Key pass",
    )
    BaseVerifyView(
        appBarTitle = "Xác nhận",
        secondButtonText = "Tiếp tục",
        onBackClick = { onBackClick() },
        onSecondButtonClick = { onSecondButtonClick(selectedOption) },
        content = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .padding(horizontal = 20.dp),
                verticalArrangement = Arrangement.spacedBy(10.dp, Alignment.Top),
                horizontalAlignment = Alignment.Start,
            ) {
                BaseText(
                    text = "Thông tin giao dịch",
                    color = AppColors.textPrimary,
                )
                Column {
                    list.forEachIndexed { index, item ->
                        // Bo góc dựa vào vị trí
                        val cornerShape = when {
                            (index == 0) -> RoundedCornerShape(topStart = 2.dp, topEnd = 2.dp)
                            (index == list.size - 1) -> RoundedCornerShape(bottomStart = 2.dp, bottomEnd = 2.dp)
                            else -> RoundedCornerShape(0.dp)
                        }
                        ItemListView(
                            leadingText = item.first,
                            trailingText = item.second,
                            shape = cornerShape,
                            trailingTextColor = Color(0xFF697F90),
                        )
                    }
                }
                BaseText(
                    text = "Thông tin giao dịch",
                    color = AppColors.textPrimary,
                )

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(2.dp))
                        .background(Color.White),
                    horizontalArrangement = Arrangement.Start,
                ) {
                    uiState.listOptions.forEach { option ->
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier
                                .padding(horizontal = 8.dp)
                                .safeClickable {
                                    selectedOption = option
                                },
                        ) {
                            RadioButton(
                                selected = (selectedOption == option),
                                onClick = { selectedOption = option },
                                colors = RadioButtonDefaults.colors(
                                    selectedColor = Color(0xFF3A6EA5),
                                    unselectedColor = Color(0xFFEDEDED),
                                ),
                            )
                            BaseText(text = option, modifier = Modifier.padding(start = 4.dp))
                        }
                    }
                }
            }
        },
    )
}