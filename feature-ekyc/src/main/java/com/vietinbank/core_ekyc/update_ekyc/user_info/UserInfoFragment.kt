package com.vietinbank.core_ekyc.update_ekyc.user_info

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.UserInfoDataHolder
import com.vietinbank.core_ekyc.EkycViewModel
import com.vietinbank.core_ekyc.update_ekyc.navigation.EkycNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.theme.AppTheme
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class UserInfoFragment : BaseFragment<EkycViewModel>() {
    override val viewModel: EkycViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var ekycNavigator: EkycNavigator

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.updateUserData()
    }

    @Inject
    lateinit var imageLoader: CoilImageLoader

    override fun onBackPressed(): Boolean {
        if (UserInfoDataHolder.isFromRegisterScreen())ekycNavigator.popBackStack() else ekycNavigator.popToBioMetricScreen()
        return true
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.infoUiState.collectAsStateWithLifecycle()
        AppTheme {
            UserInfoScreen(
                imageLoader = imageLoader,
                uiState = uiState,
                onButtonClick = { ekycNavigator.goToShowContractFromUserInfo() },
                onBackClick = { if (UserInfoDataHolder.isFromRegisterScreen())ekycNavigator.popBackStack() else ekycNavigator.popToBioMetricScreen() },
            )
        }
    }
}