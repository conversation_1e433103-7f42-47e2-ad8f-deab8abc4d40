package com.vietinbank.core_ekyc.update_ekyc.confirm_update_ekyc

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ekyc.EkycViewModel
import com.vietinbank.core_ekyc.update_ekyc.navigation.EkycNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class ConfirmUpdateEkycFragment : BaseFragment<EkycViewModel>() {
    override val viewModel: EkycViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var ekycNavigator: EkycNavigator

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.clearFileHolder()
        viewModel.clearedBiometricImage()
        viewModel.clearedEkycID()
    }

    @Composable
    override fun ComposeScreen() {
        AppTheme {
            ConfirmUpdateEkycScreen(
                onHomeClick = { appNavigator.goToHome() },
            )
        }
    }
}