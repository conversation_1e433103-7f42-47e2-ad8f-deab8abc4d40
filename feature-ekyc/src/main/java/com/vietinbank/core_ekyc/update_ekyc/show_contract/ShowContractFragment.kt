package com.vietinbank.core_ekyc.update_ekyc.show_contract

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_ekyc.ApiEvent
import com.vietinbank.core_ekyc.EkycViewModel
import com.vietinbank.core_ekyc.update_ekyc.navigation.EkycNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_home.ui.home.update_ekyc.show_contract.ShowContractScreen
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class ShowContractFragment : BaseFragment<EkycViewModel>() {
    override val viewModel: EkycViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var ekycNavigator: EkycNavigator

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.updateFileContract()
        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.apiEvent.collect {
                    when (it) {
                        is ApiEvent.Success -> {
                            ekycNavigator.goToPreApproveEkyc(
                                mtId = viewModel.preApproveUiState.value.transactionMtId,
                                listMethod = Utils.g()
                                    .provideGson()
                                    .toJson(viewModel.preApproveUiState.value.listOptions),
                            )
                        }
                        is ApiEvent.ShowError -> {}
                    }
                }
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.infoUiState.collectAsStateWithLifecycle()

        AppTheme {
            ShowContractScreen(
                fileContract = uiState.fileContract,
                onClickShowDetailContract = {
                    ekycNavigator.goToShowDetailContract()
                },
                onBackClick = { ekycNavigator.popBackStack() },
                onClickFirstButton = {
                    // navigate to home
                    showConfirmDialog(
                        message = "Yêu cầu đăng ký & thông tin thu thập Sinh trắc học sẽ bị Hủy bỏ, Quý khách vui lòng Xác nhận việc từ chối cập nhật.",
                        positiveButtonText = "Từ chối cập nhật",
                        negativeButtonText = "Đóng",
                        positiveAction = {
                            ekycNavigator.popBackGraph()
                        },
                    )
                },
                onClickSecondButton = {
                    viewModel.preApproveUpdateEkyc()
                },
            )
        }
    }
}