package com.vietinbank.core_ekyc.update_ekyc.update_biometric

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ekyc.EkycViewModel
import com.vietinbank.core_ekyc.GenOtpEvent
import com.vietinbank.core_ekyc.update_ekyc.navigation.EkycNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.theme.AppTheme
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class UpdateBiometricFragment : BaseFragment<EkycViewModel>() {
    override val viewModel: EkycViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var ekycNavigator: EkycNavigator

    @Inject
    lateinit var imageLoader: CoilImageLoader

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.updateBiometricFace()
        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.genOtpEvent.collect {
                    when (it) {
                        is GenOtpEvent.GenerateSuccess -> {
                            ekycNavigator.goToVerifyOtpFromUpdateBiometric(it.currentTime)
                        }
                        is GenOtpEvent.ShowError -> {}
                    }
                }
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.updateBiometricUiState.collectAsState()
        val guideText = "VietinBank đã tìm thấy 01 bản ghi sinh trắc học đã được thu thập và xác minh trong hệ thống VietinBank trùng khớp tên và số ID của Quý khách. " +
            "Quý khách ấn nút Đồng ý để sử dụng lại thông tin sinh trắc học, hoặc Từ chối để thu thập sinh trắc học mới"
        val listBoldText = listOf(
            "01 bản ghi sinh trắc học",
            "Đồng ý",
            "Từ chối",
        )
        AppTheme {
            UpdateBiometricScreen(
                updateBiometricUiState = uiState,
                guideText = viewModel.buildBoldText(
                    fullText = guideText,
                    boldWords = listBoldText,
                ),
                onBackClick = { ekycNavigator.popBackStack() },
                onFirstButtonClick = {},
                onSecondButtonClick = {
                    viewModel.generateOtpRetail()
                },
                onHomeClick = { appNavigator.goToHome() },
                imageLoader = imageLoader,
            )
        }
    }
}