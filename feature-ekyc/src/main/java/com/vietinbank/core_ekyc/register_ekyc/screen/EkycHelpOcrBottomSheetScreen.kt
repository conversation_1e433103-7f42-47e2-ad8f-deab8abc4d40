package com.vietinbank.core_ekyc.register_ekyc.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.compose.BaseButton
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable

@Composable
fun EkycHelpOcrBottomSheetScreen(onClick: (Int) -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(AppColors.white),
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 10.dp)
                .padding(vertical = 10.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            BaseText(
                modifier = Modifier.weight(1f),
                text = "Hướng dẫn chụp ảnh CCCD điện tử",
                textSize = 18.sp,
                fontCus = 5,
                color = AppColors.blue01,
            )
            Icon(
                painter = painterResource(id = R.drawable.ic_close),
                contentDescription = "Menu",
                tint = Color.Unspecified,

                modifier = Modifier
                    .size(24.dp)
                    .clickable { onClick.invoke(1) },
            )
        }
        BaseText(
            text = "Vui lòng sử dụng giấy tờ tùy thân bản gốc và còn hiệu lực",
            textSize = 16.sp,
            fontCus = 4,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 10.dp),
            color = AppColors.blue01,
        )
    }
    Spacer(modifier = Modifier.width(10.dp))
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 10.dp)
            .padding(horizontal = 15.dp, vertical = 15.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceEvenly,
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.width(98.dp),
        ) {
            Icon(
                painter = painterResource(id = com.vietinbank.feature_ekyc.R.drawable.ekyc_help_1),
                contentDescription = "Home",
                tint = Color.Unspecified,
                modifier = Modifier.size(width = 98.dp, height = 60.dp),
            )
            BaseText(
                text = "Không chụp\nquá mờ",
                textSize = 14.sp,
                fontCus = 2,
                color = AppColors.blue01,
            )
        }

        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.width(98.dp),
        ) {
            Icon(
                painter = painterResource(id = com.vietinbank.feature_ekyc.R.drawable.ekyc_help_2),
                contentDescription = "Search",
                tint = Color.Unspecified,
                modifier = Modifier.size(width = 98.dp, height = 60.dp),
            )

            BaseText(
                text = "Không chụp\nmất góc",
                textSize = 14.sp,
                fontCus = 2,
                color = AppColors.blue01,
            )
        }

        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.width(98.dp),
        ) {
            Icon(
                painter = painterResource(id = com.vietinbank.feature_ekyc.R.drawable.ekyc_help_3),
                contentDescription = "Settings",
                tint = Color.Unspecified,
                modifier = Modifier.size(width = 98.dp, height = 60.dp),
            )
            BaseText(
                text = "Không chụp \nlóa sáng",
                textSize = 14.sp,
                fontCus = 2,
                color = AppColors.blue01,
            )
        }
    }
    BaseButton(
        Modifier
            .padding(16.dp)
            .safeClickable { onClick.invoke(2) },
        "Chụp CCCD",
    )
}