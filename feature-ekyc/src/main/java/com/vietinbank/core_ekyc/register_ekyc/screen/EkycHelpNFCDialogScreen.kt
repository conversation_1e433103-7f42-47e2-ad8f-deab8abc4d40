package com.vietinbank.core_ekyc.register_ekyc.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ui.base.compose.BaseButton
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable

@Composable
fun EkycHelpNFCDialogScreen(onClick: (Int) -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(AppColors.white),
    ) {
        Icon(
            painter = painterResource(id = com.vietinbank.feature_ekyc.R.drawable.ekyc_help_nfc),
            contentDescription = "Menu",
            tint = Color.Unspecified,
            modifier = Modifier.fillMaxWidth(),
        )
        Spacer(modifier = Modifier.width(10.dp))
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 10.dp)
                .padding(vertical = 10.dp),
            verticalAlignment = Alignment.Top,
        ) {
            BaseText(
                modifier = Modifier
                    .background(
                        color = AppColors.primaryBlueVietinLight,
                        shape = RoundedCornerShape(10.dp),
                    )
                    .clip(RoundedCornerShape(10.dp))
                    .border(
                        width = 0.5.dp,
                        color = AppColors.primaryBlueVietinLight,
                        shape = RoundedCornerShape(10.dp),
                    )
                    .padding(5.dp),
                text = "1 >",
                textSize = 16.sp,
                fontCus = 2,
                color = AppColors.white,
            )
            Spacer(modifier = Modifier.width(5.dp))
            BaseText(
                modifier = Modifier.weight(1f),
                text = "Đưa 2 vị trí khoanh vùng đỏ lại gần nhau (xem hình hướng dẫn: đưa phần đầu điện thoại lại gần vị trí chính giữa thẻ căn cước có biểu tượng chip xanh).",
                textSize = 14.sp,
                fontCus = 4,
                color = Color(0xFF4B6477),
            )
        }
        Spacer(modifier = Modifier.width(10.dp))
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 10.dp)
                .padding(vertical = 10.dp),
            verticalAlignment = Alignment.Top,
        ) {
            BaseText(
                modifier = Modifier
                    .background(
                        color = AppColors.primaryBlueVietinLight,
                        shape = RoundedCornerShape(10.dp),
                    )
                    .clip(RoundedCornerShape(10.dp))
                    .border(
                        width = 0.5.dp,
                        color = AppColors.primaryBlueVietinLight,
                        shape = RoundedCornerShape(10.dp),
                    )
                    .padding(5.dp),
                text = "2 >",
                textSize = 16.sp,
                fontCus = 2,
                color = AppColors.white,
            )
            Spacer(modifier = Modifier.width(5.dp))
            BaseText(
                modifier = Modifier.weight(1f),
                text = "Giữ nguyên thẻ căn cước & điện thoại khi màn hình thông báo đang đọc thông tin.",
                textSize = 14.sp,
                fontCus = 4,
                color = Color(0xFF4B6477),
            )
        }
        Spacer(modifier = Modifier.width(10.dp))

        BaseButton(
            Modifier
                .padding(16.dp)
                .safeClickable { onClick.invoke(1) },
            "Tôi đã hiểu",
        )
    }
}