package com.vietinbank.core_ekyc.register_ekyc.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_common.models.AppBarAction
import com.vietinbank.core_domain.models.ekyc_feature.EkycRegisterActions
import com.vietinbank.core_ekyc.EkycViewModel
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.compose.BaseAppBar
import com.vietinbank.core_ui.base.compose.BaseButton
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable

@Composable
fun EkycStepScreen(viewModel: EkycViewModel, actions: EkycRegisterActions) {
    var showSheet by remember { mutableStateOf(false) }
    val showDialogNFC by viewModel.showDialogNFC.collectAsState()
    val ekycStep1 by viewModel.ekycStep1.collectAsState()
    val ekycStep2 by viewModel.ekycStep2.collectAsState()
    val ekycStep3 by viewModel.ekycStep3.collectAsState()

    Column(
        modifier = Modifier.fillMaxSize(),
    ) {
        BaseAppBar(
            title = "Đăng ký sinh trắc học",
            onBackClick = actions.onBackClick,
            actions = listOf(
                AppBarAction(
                    icon = R.drawable.ic_home,
                    contentDescription = "ic_home",
                    onClick = { actions.onHomeClick.invoke() },
                    tint = Color.White,
                ),
            ),
        )
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 10.dp)
                .background(AppColors.white)
                .padding(horizontal = 15.dp, vertical = 15.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            BaseText(
                text = "Các bước thực hiện",
                textSize = 18.sp,
                fontCus = 2,
                color = AppColors.blue01,
            )

            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.Top,
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    Icon(
                        painter = if (ekycStep1) {
                            painterResource(id = R.drawable.ic_radio_check)
                        } else {
                            painterResource(
                                id = R.drawable.ic_radio_uncheck,
                            )
                        },
                        contentDescription = "radio1",
                        modifier = Modifier.size(20.dp),
                    )
                    Box(
                        modifier = Modifier
                            .width(1.5.dp)
                            .padding(top = 10.dp)
                            .height(15.dp)
                            .background(AppColors.blue07),
                    )
                }

                BaseText(
                    text = "Bước 1: ",
                    textSize = 14.sp,
                    fontCus = 2,
                    modifier = Modifier.padding(
                        start = 10.dp,
                        top = 4.dp,
                    ),
                    color = AppColors.blue07,
                )
                BaseText(
                    text = "Chụp ảnh CCCD",
                    textSize = 14.sp,
                    modifier = Modifier.padding(top = 4.dp),
                    color = AppColors.blue07,
                )
            }
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.Top,
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    Icon(
                        painter = if (ekycStep2) {
                            painterResource(id = R.drawable.ic_radio_check)
                        } else {
                            painterResource(
                                id = R.drawable.ic_radio_uncheck,
                            )
                        },
                        contentDescription = "radio1",
                        modifier = Modifier.size(20.dp),
                    )
                    Box(
                        modifier = Modifier
                            .width(1.5.dp)
                            .padding(top = 10.dp)
                            .height(15.dp)
                            .background(AppColors.blue07),
                    )
                }

                BaseText(
                    text = "Bước 2: ",
                    textSize = 14.sp,
                    fontCus = 2,
                    modifier = Modifier.padding(
                        start = 10.dp,
                        top = 4.dp,
                    ),
                    color = AppColors.blue07,
                )
                BaseText(
                    text = "Thực hiện NFC CCCD",
                    textSize = 14.sp,
                    modifier = Modifier.padding(top = 4.dp),
                    color = AppColors.blue07,
                )
            }
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.Top,
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    Icon(
                        painter = if (ekycStep3) {
                            painterResource(id = R.drawable.ic_radio_check)
                        } else {
                            painterResource(
                                id = R.drawable.ic_radio_uncheck,
                            )
                        },
                        contentDescription = "radio1",
                        modifier = Modifier.size(20.dp),
                    )
                }

                BaseText(
                    text = "Bước 3: ",
                    textSize = 14.sp,
                    fontCus = 2,
                    modifier = Modifier.padding(
                        start = 10.dp,
                        top = 4.dp,
                    ),
                    color = AppColors.blue07,
                )
                BaseText(
                    text = "Chụp ảnh chân dung",
                    textSize = 14.sp,
                    modifier = Modifier.padding(top = 4.dp),
                    color = AppColors.blue07,
                )
            }
        }
        Spacer(modifier = Modifier.weight(1f))
        BaseButton(
            Modifier
                .padding(16.dp)
                .safeClickable { showSheet = true },
            "Bắt đầu đăng ký",
        )
    }

    EkycHelpOcrBottomSheet(
        showSheet = showSheet,
        onDismiss = { showSheet = false },
        onClickItem = { it ->
            when (it) {
                1 -> {
                    showSheet = false
                }

                2 -> {
                    showSheet = false
                    actions.onOcrCCCDClick.invoke()
                }
            }
        },
    )

    EkycHelpNFCDialog(
        showDialog = showDialogNFC,
        onDismiss = { showDialogNFC },
        onClickItem = { it ->
            when (it) {
                1 -> {
                    showDialogNFC
                    actions.onNFCClick.invoke()
                }
            }
        },
    )
}