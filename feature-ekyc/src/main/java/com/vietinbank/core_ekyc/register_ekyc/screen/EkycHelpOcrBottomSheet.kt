package com.vietinbank.core_ekyc.register_ekyc.screen

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EkycHelpOcrBottomSheet(
    showSheet: Boolean,
    onDismiss: () -> Unit,
    onClickItem: (Int) -> Unit,
) {
    val sheetState = rememberModalBottomSheetState()

    if (showSheet) {
        ModalBottomSheet(
            onDismissRequest = { onDismiss() },
            sheetState = sheetState,
            containerColor = Color.White,
            shape = RoundedCornerShape(topStart = 10.dp, topEnd = 10.dp),
            dragHandle = {},
        ) {
            EkycHelpOcrBottomSheetScreen(
                onClick = {
                    onClickItem(it)
                },
            )
        }
    }
}
