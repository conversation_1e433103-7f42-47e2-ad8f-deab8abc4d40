package com.vietinbank.core_ekyc.register_ekyc.fragment

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import android.content.res.Configuration
import android.nfc.NfcManager
import android.os.Bundle
import android.provider.Settings
import android.view.View
import androidx.compose.runtime.Composable
import androidx.core.content.ContextCompat
import androidx.fragment.app.viewModels
import com.fis.ekyc.liveness.activity.LivenessActivity
import com.fis.ekyc.liveness.preference.ErrorCode
import com.fis.ekyc.liveness.preference.LivenessConfigManager
import com.fis.ekyc.liveness.preference.LivenessListener
import com.fis.ekyc.nfc.stepNfc.NFCConfigManager
import com.fis.ekyc.nfc.stepNfc.NFCListener
import com.fis.ekyc.ocr.preference.OCRListener
import com.fis.ekyc.ocr.preference.OcrConfigManager
import com.vietinbank.core_common.extensions.dd_MM_yyyy_1
import com.vietinbank.core_common.extensions.getDateToFormat
import com.vietinbank.core_common.extensions.yy_MM_dd
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_domain.models.ekyc_feature.EkycImageLivenessItemParam
import com.vietinbank.core_domain.models.ekyc_feature.EkycRegisterActions
import com.vietinbank.core_ekyc.EkycViewModel
import com.vietinbank.core_ekyc.register_ekyc.screen.EkycStepScreen
import com.vietinbank.core_ekyc.update_ekyc.navigation.EkycNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import dagger.hilt.android.AndroidEntryPoint
import java.util.Locale
import javax.inject.Inject

@AndroidEntryPoint
class EkycStepFragment : BaseFragment<EkycViewModel>() {
    override val viewModel: EkycViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var ekycNavigator: EkycNavigator

    @Composable
    override fun ComposeScreen() {
        val actions = EkycRegisterActions(
            onBackClick = { appNavigator.popBackStack() },
            onHomeClick = { appNavigator.goToHome() },
            onOcrCCCDClick = {
                viewModel.getCurrentStep()
            },
            onNFCClick = {
                startScanNFC()
            },
        )
        AppTheme {
            EkycStepScreen(
                viewModel = viewModel,
                actions = actions,
            )
        }
    }

    private fun startScanNFC() {
        viewModel.showDialogNFC(false)
        viewModel.ekycOcrDomains.let { it ->
            NFCConfigManager.config(
                it?.data?.identityNumber ?: "",
                birthDate = (it?.data?.birthDay ?: "").getDateToFormat(
                    formatFrom = dd_MM_yyyy_1,
                    formatTo = yy_MM_dd,
                ) ?: "",
                expiredDate = (it?.data?.expireDate ?: "").getDateToFormat(
                    formatFrom = dd_MM_yyyy_1,
                    formatTo = yy_MM_dd,
                ) ?: "",
                issueDate = (it?.data?.provideDate ?: "").getDateToFormat(
                    formatFrom = dd_MM_yyyy_1,
                    formatTo = yy_MM_dd,
                ) ?: "",
                object : NFCListener {
                    override fun onError(errorCode: String) {
                    }

                    override fun onSuccess(result: String) {
                        viewModel.ekycNFC(result = result)
                    }
                },
            ).start(requireContext())
        }
    }

    private fun startOcrCCCD() {
        val errorMessage = checkEnableNFC()
        if (errorMessage != null) {
            showNoticeDialog(errorMessage)
            return
        }
        OcrConfigManager.startOCR(
            false,
            requireContext(),
            object : OCRListener {
                override fun onSuccess(
                    frontImageBase64: String,
                    rearImageBase64: String,
                    activity: Activity,
                ) {
                    viewModel.ekycOcr(
                        imageBack = rearImageBase64,
                        imageFront = frontImageBase64,
                    )
                    activity.finish()
                }

                override fun onError(
                    error: String,
                    activity: Activity,
                ) {
                }
            },
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setAppLanguage()
        observe()
        requestAccessPermissionListener { allow ->
            if (allow) {
                startLiveNess()
            }
        }
    }

    private fun observe() {
        viewModel.ekycNFCState.observe(viewLifecycleOwner) { it ->
            requestCameraPermission()
        }
        viewModel.ekycCompareImageState.observe(viewLifecycleOwner) { it ->
            appNavigator.gotoUpdateEkycUserInfo()
        }
        viewModel.step0State.observe(viewLifecycleOwner) { it ->
            startOcrCCCD()
        }
        viewModel.step1State.observe(viewLifecycleOwner) { it ->
            startScanNFC()
        }
        viewModel.step2State.observe(viewLifecycleOwner) { it ->
            requestCameraPermission()
        }
        viewModel.step3State.observe(viewLifecycleOwner) { it ->
        }
    }

    private fun startLiveNess() {
        context?.let {
            LivenessConfigManager.startLiveness(
                it,
                30L,
                object : LivenessListener {
                    override fun onSuccess(
                        faceList: ArrayList<String>,
                        activity: LivenessActivity,
                    ) {
                        viewModel.listImages.clear()
                        if (faceList.isNotEmpty()) {
                            for (i in 0 until faceList.size) {
                                viewModel.listImages.add(EkycImageLivenessItemParam(faceList[i]))
                            }
                            viewModel.saveBiometricImage(viewModel.listImages[0])
                            viewModel.confirmInput()
                        }
                        activity.finish()
                    }

                    override fun onError(
                        error: ErrorCode,
                        faceList: ArrayList<String>,
                        activity: LivenessActivity,
                    ) {
                        when (error) {
                            ErrorCode.LIVENESS_TIMEOUT -> {
                            }

                            ErrorCode.USER_REFUSE_PERMISSION -> {
                            }

                            ErrorCode.DEVICE_NOT_SATISFIED -> {
                            }

                            ErrorCode.OUTPUT_NOT_MEET_REQUIREMENT -> {
                            }

                            else -> Unit
                        }
                    }
                },
            )
        }
    }

    private fun requestCameraPermission() {
        requestAccessPermission(Manifest.permission.CAMERA)
    }

    private fun setAppLanguage() {
        val locale = Locale("vi", "VN")
        Locale.setDefault(locale)
        val config = Configuration()
        config.setLocale(locale)
        activity?.resources?.updateConfiguration(config, activity?.resources?.displayMetrics)
    }

    fun checkEnableNFC(): String? {
        val manager = context?.getSystemService(Context.NFC_SERVICE) as? NfcManager
        val adapter = manager?.defaultAdapter

        return when {
            context == null -> "Có lỗi xẩy ra"
            adapter == null -> "Thiết bị của khác hàng không hợp lệ để thu thập sinh trắc học. Quý khách vui lòng đổi thiết bị hoặc liên hệ với Chi nhánh/PGD gần nhất."
            !adapter.isEnabled -> "NFC đang tắt. Vui lòng bật ở trong Cài đặt"
            ContextCompat.checkSelfPermission(
                requireContext(),
                Manifest.permission.NFC,
            ) != PackageManager.PERMISSION_GRANTED ->
                "Thiết bị của khác hàng không hợp lệ để thu thập sinh trắc học. Quý khách vui lòng đổi thiết bị hoặc liên hệ với Chi nhánh/PGD gần nhất."

            Settings.Global.getInt(
                requireContext().contentResolver,
                Settings.Global.AIRPLANE_MODE_ON,
                0,
            ) != 0 ->
                "Chế độ máy bay có thể gây nhiễu khi quét NFC"

            else -> null
        }
    }
}