package com.vietinbank.core_ekyc

import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.livedata.SingleLiveEvent
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.UserInfoDataHolder
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.checker.GenKeyPassChallengeCodeParams
import com.vietinbank.core_domain.models.checker.GenSOTPTransCodeParams
import com.vietinbank.core_domain.models.checker.PreApproveParams
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.ekyc_feature.EkycCompareImageRequestParams
import com.vietinbank.core_domain.models.ekyc_feature.EkycConfirmInputRequestParams
import com.vietinbank.core_domain.models.ekyc_feature.EkycImageLivenessItemParam
import com.vietinbank.core_domain.models.ekyc_feature.EkycNFCDomains
import com.vietinbank.core_domain.models.ekyc_feature.EkycNFCRequestParams
import com.vietinbank.core_domain.models.ekyc_feature.EkycOcrDomains
import com.vietinbank.core_domain.models.ekyc_feature.EkycOcrRequestParams
import com.vietinbank.core_domain.models.ekyc_feature.ScanNFCEntity
import com.vietinbank.core_domain.models.home.DataRetail
import com.vietinbank.core_domain.models.home.DifferentData
import com.vietinbank.core_domain.models.home.GenerateOtpParam
import com.vietinbank.core_domain.models.home.ImageUpdate
import com.vietinbank.core_domain.models.home.VerifyOtpEkycDomain
import com.vietinbank.core_domain.models.home.VerifyOtpEkycParam
import com.vietinbank.core_domain.models.home.toUserInfo
import com.vietinbank.core_domain.models.maker.Status
import com.vietinbank.core_domain.usecase.checker.CheckerUserCase
import com.vietinbank.core_domain.usecase.ekyc_feature.EkycFeatureUseCase
import com.vietinbank.core_domain.usecase.home.HomeUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject
import kotlin.io.encoding.Base64
import kotlin.io.encoding.ExperimentalEncodingApi

@HiltViewModel
class EkycViewModel @Inject constructor(
    private val ekycFeatureUseCase: EkycFeatureUseCase,
    private val homeUseCase: HomeUseCase,
    private val checkerUserCase: CheckerUserCase,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
) : BaseViewModel() {

    // Common UI States
    private val _verifyUiState = MutableStateFlow(VerifyOtpUiState())
    val verifyUiState = _verifyUiState.asStateFlow()

    private val _apiEvent = Channel<ApiEvent<String>>(Channel.BUFFERED)
    val apiEvent = _apiEvent.receiveAsFlow()

    // Registration specific states
    private val _showDialogNFC = MutableStateFlow(false)
    val showDialogNFC = _showDialogNFC.asStateFlow()

    private val _ekycStep1 = MutableStateFlow(false)
    val ekycStep1 = _ekycStep1.asStateFlow()

    private val _ekycStep2 = MutableStateFlow(false)
    val ekycStep2 = _ekycStep2.asStateFlow()

    private val _ekycStep3 = MutableStateFlow(false)
    val ekycStep3 = _ekycStep3.asStateFlow()

    private val _ekycNFCState = SingleLiveEvent<EkycNFCDomains>()
    val ekycNFCState: SingleLiveEvent<EkycNFCDomains> get() = _ekycNFCState

    private val _ekycCompareImageState = SingleLiveEvent<VerifyOtpEkycDomain>()
    val ekycCompareImageState: SingleLiveEvent<VerifyOtpEkycDomain> get() = _ekycCompareImageState

    // Update specific states
    private val _updateBiometricUiState = MutableStateFlow(UpdateBiometricUiState())
    val updateBiometricUiState = _updateBiometricUiState.asStateFlow()

    private val _infoUiState = MutableStateFlow(UserInfoUiState())
    val infoUiState: StateFlow<UserInfoUiState> = _infoUiState.asStateFlow()

    private val _step0State = SingleLiveEvent<String>()
    val step0State: SingleLiveEvent<String> get() = _step0State

    private val _step1State = SingleLiveEvent<String>()
    val step1State: SingleLiveEvent<String> get() = _step1State

    private val _step2State = SingleLiveEvent<String>()
    val step2State: SingleLiveEvent<String> get() = _step2State

    private val _step3State = SingleLiveEvent<String>()
    val step3State: SingleLiveEvent<String> get() = _step3State

    private val _preApproveUiState = MutableStateFlow(PreApproveEkycUiState())
    val preApproveUiState: StateFlow<PreApproveEkycUiState> = _preApproveUiState.asStateFlow()

    private val _genOtpEvent = Channel<GenOtpEvent>(Channel.BUFFERED)
    val genOtpEvent = _genOtpEvent.receiveAsFlow()

    // Common properties
    var ekycOcrDomains: EkycOcrDomains? = null
    var imageCCCDFront: String? = null
    var imageCCCDBack: String? = null
    val listImages: MutableList<EkycImageLivenessItemParam> = mutableListOf()

    // Update specific properties
    var addField1: String? = null
    var eFastID: String? = null
    var currentCifNo: String? = null
    var originalTransactions: List<TransactionListDomain> = emptyList()
        private set

    private var verifyOtpJob: Job? = null

    init {
        viewModelScope.launch {
            _verifyUiState.update {
                it.copy(
                    phoneNum = userProf.getPhoneNo().toString(),
                    email = userProf.getEmail().toString(),
                )
            }
        }
    }

    fun compareImage() {
        launchJob(showLoading = true) {
            val params = EkycCompareImageRequestParams(
                username = userProf.getUserName() ?: "",
                eKycId = ekycOcrDomains?.data?.ekycId ?: "",
                image = listImages[0].image ?: "",
            )
            val res = ekycFeatureUseCase.ekycCompareImage(params)
            handleResource(res) { data ->

                val imageUpdate = mutableListOf<ImageUpdate?>()
                imageUpdate.add(
                    ImageUpdate(
                        file = imageCCCDFront,
                        fileName = "",
                        status = Status("", ""),
                    ),
                )
                imageUpdate.add(
                    ImageUpdate(
                        file = imageCCCDBack,
                        fileName = "",
                        status = Status("", ""),
                    ),
                )
                UserInfoDataHolder.saveEkycID(ekycOcrDomains?.data?.ekycId ?: "")
                handleDataProcessing(
                    differentData = data.differentData,
                    imageUpdate = imageUpdate,
                    fileGTDTT = data.fileGTDTT?.file,
                    dataRetail = data.dataRetail,
                    isTypeIdCard = "05" == data.dataRetail?.idType,
                    isFromRegisterScreen = true,
                )
                _ekycStep3.value = true
                _ekycCompareImageState.postValue(data)
            }
        }
    }

    @OptIn(ExperimentalEncodingApi::class)
    fun saveBiometricImage(param: EkycImageLivenessItemParam) {
        viewModelScope.launch(Dispatchers.Default) {
            UserInfoDataHolder.saveBiometricImage(
                Base64.Default.decode(param.image ?: ""),
            )
        }
    }

    fun confirmInput() {
        launchJob(showLoading = true) {
            val params = EkycConfirmInputRequestParams(
                username = userProf.getUserName() ?: "",
                eKycId = ekycOcrDomains?.data?.ekycId ?: "",
                images = listImages,
            )
            val res = ekycFeatureUseCase.ekycConfirmInput(params)
            handleResource(res) { compareImage() }
        }
    }

    fun ekycNFC(result: String) {
        val scanNFCEntity = scanNFCEntity(result)
        if (scanNFCEntity != null) {
            launchJob(showLoading = true) {
                val params = createNFCRequestParams(scanNFCEntity)
                val res = ekycFeatureUseCase.ekycNFC(params)
                handleResource(res) { data ->
                    _ekycNFCState.postValue(data)
                    _ekycStep2.value = true
                }
            }
        }
    }

    private fun scanNFCEntity(result: String): ScanNFCEntity? {
        try {
            return Utils.g().provideGson().fromJson(
                String(result.toByteArray(Charsets.ISO_8859_1)).replace("\\n", ""),
                ScanNFCEntity::class.java,
            )
        } catch (e: Exception) {
            return null
            e.printStackTrace()
        }
    }

    fun ekycOcr(imageBack: String, imageFront: String) {
        imageCCCDFront = imageFront
        imageCCCDBack = imageBack
        launchJob(showLoading = true) {
            val params = EkycOcrRequestParams(
                imageBack = imageBack,
                imageFront = imageFront,
                username = userProf.getUserName() ?: "",
                type = "cccd",
                userType = "1",
            )
            val res = ekycFeatureUseCase.ekycOcr(params)
            handleResource(res) { data ->
                ekycOcrDomains = data
                showDialogNFC(true)
                _ekycStep1.value = true
            }
        }
    }

    // UPDATE METHODS
    fun preApproveUpdateEkyc() {
        launchJob {
            val res = checkerUserCase.preApprove(
                PreApproveParams(
                    userName = userProf.getUserName().toString(),
                    pereAppTp = arrayListOf(),
                    serviceId = "",
                    serviceType = if (UserInfoDataHolder.isFromRegisterScreen()) "NEW_STH" else "UTH",
                    tranType = if (UserInfoDataHolder.isFromRegisterScreen()) "NEW_STH" else "",
                    transactions = if (UserInfoDataHolder.isFromRegisterScreen()) {
                        arrayListOf(
                            UserInfoDataHolder.getEkycID(),
                        )
                    } else {
                        arrayListOf()
                    },
                ),
            )
            printLog("preApproveUpdateEkyc $res")
            handleResource(res) { data ->
                _preApproveUiState.update { state ->
                    state.copy(
                        listOptions = data.methodList?.map {
                            when (it.method) {
                                "SoftOtp" -> Tags.SOFT_OTP
                                "Keypass" -> Tags.KEY_PASS
                                else -> Tags.VNPT_SMARTCA
                            }
                        } ?: emptyList(),
                        transactionMtId = data.tranSTHId ?: "",
                    )
                }
                _apiEvent.send(ApiEvent.Success())
            }
        }
    }

    fun genSOTPTransCode() {
        launchJob {
            val res = checkerUserCase.genSOTPTransCode(
                GenSOTPTransCodeParams(
                    cifno = userProf.getCifNo(),
                    userName = userProf.getUserName().toString(),
                    actionId = "approve",
                    groupType = "",
                    mtIds = _preApproveUiState.value.transactionMtId,
                    reason = "",
                    transactionData = "1",
                    trxType = "",
                ),
            )
            handleResource(res) { responseData ->
                _preApproveUiState.update {
                    it.copy(transactionId = responseData.transactionId ?: "")
                }
                _apiEvent.send(ApiEvent.Success(Tags.SOFT_OTP))
            }
        }
    }

    fun genKeyPassChallengeCode() {
        launchJob(showLoading = true) {
            val res = checkerUserCase.genKeyPassChallengeCode(
                GenKeyPassChallengeCodeParams(username = userProf.getUserName()),
            )
            handleResource(res) { data ->
                _preApproveUiState.update {
                    it.copy(challengeCode = data.challengeCode ?: "")
                }
                _apiEvent.send(ApiEvent.Success(Tags.KEY_PASS))
            }
        }
    }

    // COMMON OTP METHODS
    fun generateOtpRetail() {
        launchJob {
            val res = homeUseCase.generateOtpRetail(
                GenerateOtpParam(
                    userProf.getUserName().toString(),
                    userProf.getPhoneNo().toString(),
                ),
            )
            printLog("ekyc generateOtpRetail: $res", "get phoneNum: ${userProf.getPhoneNo()}")
            handleResource(res) {
                _genOtpEvent.send(GenOtpEvent.GenerateSuccess(System.currentTimeMillis()))
                _verifyUiState.update { it.copy(isFailOtp = false) }
            }
        }
    }

    @OptIn(ExperimentalEncodingApi::class)
    fun verifyOtpEkycRetail(otp: String) {
        launchJob {
            val res = homeUseCase.verifyOtpRetail(
                VerifyOtpEkycParam(
                    username = userProf.getUserName().toString(),
                    addition1 = addField1.toString(),
                    cifno = currentCifNo,
                    efastId = eFastID.toString(),
                    otp = otp,
                ),
            )
            printLog("ekyc verifyOtpEkycRetail: $res")
            handleResource(res) { data ->
                handleDataProcessing(
                    differentData = data.differentData,
                    imageUpdate = data.imageUpdate,
                    fileGTDTT = data.fileGTDTT?.file,
                    dataRetail = data.dataRetail,
                    isTypeIdCard = "05" == data.dataRetail?.idType,
                    isFromRegisterScreen = false,
                )
            }
        }
    }

    fun showDialogNFC(isShow: Boolean) {
        _showDialogNFC.value = isShow
    }

    fun getCurrentStep() {
        return when {
            _ekycStep3.value -> {
                _step3State.postValue("")
            }

            _ekycStep2.value -> {
                _step2State.postValue("")
            }

            _ekycStep1.value -> {
                _step1State.postValue("")
            }

            else -> _step0State.postValue("")
        }
    }

    fun updateBiometricFace() = _updateBiometricUiState.update {
        it.copy(image = UserInfoDataHolder.getBiometricImage())
    }

    fun updateUserData() {
        _infoUiState.update {
            it.copy(
                userInfo = UserInfoDataHolder.getUserInfo(),
                listImage = UserInfoDataHolder.getListImage(),
                isTypeIdCard = UserInfoDataHolder.getTypeIdCard() != false,
                biometricImage = UserInfoDataHolder.getBiometricImage(),
            )
        }
    }

    fun updateFileContract() {
        _infoUiState.update {
            it.copy(fileContract = UserInfoDataHolder.getFile())
        }
    }

    fun clearFileHolder() = UserInfoDataHolder.clearedUserInfo()
    fun clearedBiometricImage() = UserInfoDataHolder.clearedBiometricImage()
    fun clearedEkycID() = UserInfoDataHolder.clearedEkycID()

    fun updateMtid(data: String) {
        _preApproveUiState.update { it.copy(transactionMtId = data) }
    }

    fun updateListMethod(data: List<String>) {
        _preApproveUiState.update { it.copy(listOptions = data) }
    }

    fun maskEmail(): String {
        val email = userProf.getEmail() ?: ""
        val parts = email.split("@")
        if (parts.size != 2 || parts[0].length < 2) return "*@${parts.getOrNull(1) ?: "unknown"}"
        val prefix = parts[0].take(1)
        val masked = "*".repeat(parts[0].length - 1)
        return "$prefix$masked@${parts[1]}"
    }

    fun maskPhone(): String {
        val phone = userProf.getPhoneNo() ?: ""

        return if (phone.length >= 7) {
            val prefix = phone.take(3)
            val masked = "*".repeat(4)
            val suffix = phone.drop(7)
            "$prefix$masked$suffix"
        } else {
            "*".repeat(phone.length) // fallback cho SĐT quá ngắn
        }
    }

    fun buildBoldText(fullText: String, boldWords: List<String>): AnnotatedString {
        return buildAnnotatedString {
            append(fullText)
            boldWords.forEach { word ->
                val start = fullText.indexOf(word)
                if (start >= 0) {
                    addStyle(
                        style = SpanStyle(fontWeight = FontWeight.Bold),
                        start = start,
                        end = start + word.length,
                    )
                }
            }
        }
    }

    fun verifyOtpCountDown(timeGenOtp: Long) {
        verifyOtpJob?.cancel()
        verifyOtpJob = viewModelScope.launch {
            val totalTime = (System.currentTimeMillis() - timeGenOtp) / 1000
            if ((60 - totalTime.toInt()) > 0) {
                _verifyUiState.update { it.copy(isTimeOut1minute = false) }
            }
            launch {
                countDownTimer(
                    totalTime = 60 - totalTime.toInt(),
                    onTick = { remaining ->
                        _verifyUiState.update { it.copy(countDownClickOtp = "${remaining}s") }
                        if (remaining == 0) {
                            _verifyUiState.update { it.copy(isTimeOut1minute = true) }
                        }
                    },
                )
            }
            launch {
                countDownTimer(
                    totalTime = 300 - totalTime.toInt(),
                    onTick = { remaining ->
                        val minutes = remaining / 60
                        val seconds = remaining % 60
                        _verifyUiState.update { it.copy(countDownOtpTime = "${minutes}p${seconds}s") }
                    },
                )
            }
        }
    }

    // HELPER METHODS
    fun getOriginalTransaction(): TransactionListDomain = TransactionListDomain().copy()
    fun getKeypassProfile(): String? = userProf.getKeypassProfile()
    fun getOriginalTransactionByMTId(mtID: String): TransactionListDomain? =
        originalTransactions.find { it.mtId == mtID }

    override fun onDisplayErrorMessage(exception: AppException) {
        if (exception is AppException.ApiException &&
            exception.requestPath == Constants.MB_VERIFY_OTP_EKYC &&
            exception.code == "3"
        ) {
            _verifyUiState.update { it.copy(isFailOtp = true) }
            viewModelScope.launch {
                _apiEvent.send(ApiEvent.ShowError(""))
            }
        }
        super.onDisplayErrorMessage(exception)
    }

    // PRIVATE HELPER METHODS
    private fun createNFCRequestParams(scanNFCEntity: ScanNFCEntity) = EkycNFCRequestParams(
        userType = "",
        aAResult = scanNFCEntity.aAResult ?: "",
        challenge = scanNFCEntity.challenge ?: "",
        dg1 = scanNFCEntity.dg1 ?: "",
        dg2 = scanNFCEntity.dg2 ?: "",
        dg3 = scanNFCEntity.dg3 ?: "",
        dg4 = scanNFCEntity.dg4 ?: "",
        dg5 = scanNFCEntity.dg5 ?: "",
        dg6 = scanNFCEntity.dg6 ?: "",
        dg7 = scanNFCEntity.dg7 ?: "",
        dg8 = scanNFCEntity.dg8 ?: "",
        dg9 = scanNFCEntity.dg9 ?: "",
        dg10 = scanNFCEntity.dg10 ?: "",
        dg11 = scanNFCEntity.dg11 ?: "",
        dg12 = scanNFCEntity.dg12 ?: "",
        dg13 = scanNFCEntity.dg13 ?: "",
        dg14 = scanNFCEntity.dg14 ?: "",
        dg15 = scanNFCEntity.dg15 ?: "",
        dg16 = scanNFCEntity.dg16 ?: "",
        eACCAResult = scanNFCEntity.eACCAResult ?: "",
        sod = scanNFCEntity.sod ?: "",
        username = userProf.getUserName() ?: "",
        eKycId = ekycOcrDomains?.data?.ekycId ?: "",
        type = "cccd",
    )

    @OptIn(ExperimentalEncodingApi::class)
    private suspend fun handleDataProcessing(
        differentData: DifferentData?,
        imageUpdate: List<ImageUpdate?>?,
        fileGTDTT: String?,
        dataRetail: DataRetail?,
        isTypeIdCard: Boolean,
        isFromRegisterScreen: Boolean = false,
    ) {
        val diff = differentData ?: DifferentData.empty()
        withContext(Dispatchers.Default) {
            val listImageDeferred = vmScope.async {
                imageUpdate?.mapNotNull { image ->
                    image?.file?.let { Base64.Default.decode(it) }
                }?.toList() ?: emptyList()
            }
            UserInfoDataHolder.saveUserInfoByteArrayData(
                listImageDeferred.await(),
                Base64.Default.decode(fileGTDTT ?: ""),
            )
            UserInfoDataHolder.setFromRegisterScreen(isFromRegisterScreen)
        }
        UserInfoDataHolder.saveUserInfoData(
            newList = dataRetail?.toUserInfo(diff) ?: emptyList(),
            newTypeIdCard = isTypeIdCard,
        )
        if (!isFromRegisterScreen) {
            _verifyUiState.update { it.copy(isFailOtp = false) }
            UserInfoDataHolder.isCalculating.collect {
                if (!it) {
                    _apiEvent.send(ApiEvent.Success())
                }
            }
        }
    }

    private suspend fun countDownTimer(
        totalTime: Int,
        onTick: (remainingTime: Int) -> Unit,
    ) {
        var time = totalTime
        while (time > 0) {
            delay(1000)
            time--
            onTick(time)
        }
    }
}

data class VerifyOtpUiState(
    val countDownOtpTime: String = "0p0s",
    val countDownClickOtp: String = "0s",
    val isTimeOut1minute: Boolean = false,
    val isFailOtp: Boolean = false,
    val phoneNum: String = "",
    val email: String = "",
)

data class PreApproveEkycUiState(
    val listOptions: List<String> = emptyList(),
    val selectedOption: String = "",
    val transactionMtId: String = "",
    val transactionId: String = "",
    val challengeCode: String = "",
)

data class UpdateBiometricUiState(
    val image: ByteArray? = null,
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        other as UpdateBiometricUiState
        if (image != null) {
            if (other.image == null) return false
            if (!image.contentEquals(other.image)) return false
        } else if (other.image != null) return false
        return true
    }

    override fun hashCode(): Int {
        return image?.contentHashCode() ?: 0
    }
}

data class UserInfoUiState(
    val isTypeIdCard: Boolean = true,
    val userInfo: List<Triple<String, String, Boolean>> = listOf(),
    val listImage: List<ByteArray> = listOf(),
    val fileContract: ByteArray? = null,
    val biometricImage: ByteArray? = null,
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        other as UserInfoUiState
        if (isTypeIdCard != other.isTypeIdCard) return false
        if (userInfo != other.userInfo) return false
        if (listImage != other.listImage) return false
        if (fileContract != null) {
            if (other.fileContract == null) return false
            if (!fileContract.contentEquals(other.fileContract)) return false
        } else if (other.fileContract != null) return false
        if (biometricImage != null) {
            if (other.biometricImage == null) return false
            if (!biometricImage.contentEquals(other.biometricImage)) return false
        } else if (other.biometricImage != null) return false
        return true
    }

    override fun hashCode(): Int {
        var result = isTypeIdCard.hashCode()
        result = 31 * result + userInfo.hashCode()
        result = 31 * result + listImage.hashCode()
        result = 31 * result + (fileContract?.contentHashCode() ?: 0)
        result = 31 * result + (biometricImage?.contentHashCode() ?: 0)
        return result
    }
}

sealed class ApiEvent<out T> {
    data class Success<T>(val data: T? = null) : ApiEvent<T>()
    data class ShowError(val message: String) : ApiEvent<Nothing>()
}

sealed class GenOtpEvent {
    data class GenerateSuccess(val currentTime: Long) : GenOtpEvent()
    data class ShowError(val message: String) : GenOtpEvent()
}