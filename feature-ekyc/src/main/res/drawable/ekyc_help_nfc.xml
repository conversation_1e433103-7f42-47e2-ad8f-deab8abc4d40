<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="280dp"
    android:height="280dp"
    android:viewportWidth="280"
    android:viewportHeight="280">
  <group>
    <clip-path
        android:pathData="M70,11.2h140v84h-140z"/>
    <group>
      <clip-path
          android:pathData="M76,11.2L204,11.2A6,6 0,0 1,210 17.2L210,89.2A6,6 0,0 1,204 95.2L76,95.2A6,6 0,0 1,70 89.2L70,17.2A6,6 0,0 1,76 11.2z"/>
      <path
          android:pathData="M70,11.2h140v84h-140z"
          android:fillColor="#D3E3F6"/>
      <path
          android:strokeWidth="1"
          android:pathData="M70.5,11.7h139v83h-139z"
          android:strokeColor="#D9E1E7">
        <aapt:attr name="android:fillColor">
          <gradient 
              android:startX="71.87"
              android:startY="52.27"
              android:endX="208.13"
              android:endY="52.27"
              android:type="linear">
            <item android:offset="0.18" android:color="#FFCFF1F3"/>
            <item android:offset="0.51" android:color="#FFFFF7F7"/>
            <item android:offset="0.85" android:color="#FFCFF1F3"/>
          </gradient>
        </aapt:attr>
      </path>
      <path
          android:pathData="M163.33,54.13m-20.53,0a20.53,20.53 0,1 1,41.07 0a20.53,20.53 0,1 1,-41.07 0"
          android:strokeAlpha="0.17"
          android:fillColor="#78CEFD"
          android:fillAlpha="0.17"/>
      <path
          android:pathData="M171.59,51.39C171.47,51.03 171.15,50.75 170.76,50.7L166.31,50.05L164.32,46.02C164.14,45.67 163.79,45.45 163.4,45.45C163.01,45.45 162.66,45.67 162.48,46.02L160.49,50.05L156.04,50.71C155.65,50.76 155.33,51.03 155.21,51.4C155.09,51.77 155.19,52.18 155.47,52.46L158.69,55.6L157.92,60.04C157.86,60.43 158.01,60.82 158.33,61.04C158.51,61.17 158.72,61.24 158.93,61.24C159.1,61.24 159.26,61.2 159.41,61.12L163.39,59.03L167.37,61.12C167.72,61.3 168.14,61.27 168.45,61.04C168.76,60.81 168.92,60.42 168.86,60.04L168.09,55.6L171.32,52.46C171.61,52.17 171.71,51.77 171.59,51.39Z"
          android:strokeAlpha="0.17"
          android:fillColor="#ffffff"
          android:fillType="evenOdd"
          android:fillAlpha="0.17"/>
      <path
          android:pathData="M85.18,31.03a10.96,10.5 0,1 0,21.91 0a10.96,10.5 0,1 0,-21.91 0z"
          android:strokeAlpha="0.8"
          android:fillColor="#9CBCD0"
          android:fillAlpha="0.4"/>
      <path
          android:pathData="M91.26,36.87a4.87,4.67 0,1 0,9.74 0a4.87,4.67 0,1 0,-9.74 0z"
          android:strokeAlpha="0.8"
          android:fillColor="#ffffff"
          android:fillAlpha="0.8"/>
      <path
          android:pathData="M86.39,40.37L96.13,39.2L105.87,40.37"
          android:strokeAlpha="0.4"
          android:strokeWidth="2.5"
          android:fillColor="#00000000"
          android:strokeColor="#9CBCD0"
          android:fillAlpha="0.8"/>
      <path
          android:pathData="M99.76,25.5C99.71,25.34 99.57,25.22 99.39,25.19L97.42,24.91L96.54,23.12C96.46,22.96 96.3,22.87 96.13,22.87C95.96,22.87 95.8,22.96 95.73,23.12L94.84,24.91L92.87,25.2C92.7,25.22 92.56,25.34 92.5,25.51C92.45,25.67 92.49,25.85 92.62,25.97L94.04,27.37L93.7,29.34C93.68,29.51 93.74,29.68 93.89,29.78C93.96,29.84 94.06,29.87 94.15,29.87C94.22,29.87 94.3,29.85 94.36,29.81L96.13,28.88L97.89,29.81C98.05,29.89 98.23,29.88 98.37,29.78C98.51,29.67 98.58,29.5 98.55,29.34L98.21,27.37L99.64,25.97C99.77,25.85 99.82,25.67 99.76,25.5Z"
          android:strokeAlpha="0.8"
          android:fillColor="#ffffff"
          android:fillType="evenOdd"
          android:fillAlpha="0.8"/>
      <path
          android:pathData="M81.33,47.13L110.93,47.13A2,2 0,0 1,112.93 49.13L112.93,86.2A2,2 0,0 1,110.93 88.2L81.33,88.2A2,2 0,0 1,79.33 86.2L79.33,49.13A2,2 0,0 1,81.33 47.13z"
          android:fillColor="#F2F6F9"/>
      <path
          android:pathData="M81.33,47.38L110.93,47.38A1.75,1.75 0,0 1,112.68 49.13L112.68,86.2A1.75,1.75 0,0 1,110.93 87.95L81.33,87.95A1.75,1.75 0,0 1,79.58 86.2L79.58,49.13A1.75,1.75 0,0 1,81.33 47.38z"
          android:strokeAlpha="0.3"
          android:strokeWidth="0.5"
          android:fillColor="#00000000"
          android:strokeColor="#9CBCD0"/>
      <path
          android:pathData="M103.24,78.15H89.43C89.43,78.15 89.91,76.44 91.4,75.52C92.66,74.74 94.28,74.76 94.82,74.42C94.92,74.36 94.96,74.08 94.96,73.49C94.97,72.48 94.89,71.18 94.89,71.18H97.78C97.78,71.18 97.69,72.56 97.71,73.57C97.72,74.13 97.75,74.36 97.85,74.42C98.39,74.76 100.01,74.74 101.26,75.52C102.75,76.44 103.24,78.15 103.24,78.15Z"
          android:fillColor="#EBCEB3"/>
      <path
          android:pathData="M104.29,80.98H88.36C88.36,80.98 89.74,76.71 90.68,75.93C92.22,74.66 94.3,74.69 94.69,74.38L96.32,75.88L97.95,74.38C98.34,74.69 100.42,74.66 101.96,75.93C102.91,76.71 104.29,80.98 104.29,80.98Z"
          android:fillColor="#ffffff"/>
      <path
          android:pathData="M94.57,75.05C94.61,74.83 94.65,74.6 94.7,74.38L96.34,75.88L97.96,74.38C98.01,74.6 98.05,74.83 98.09,75.05L98.09,75.05L98.22,75.71L98.34,76.38C98.36,76.49 98.38,76.6 98.4,76.71C98.42,76.82 98.44,76.93 98.46,77.04L98.47,77.13L98.4,77.08C98.06,76.89 97.71,76.69 97.36,76.5C97.02,76.3 96.67,76.1 96.34,75.88L95.31,76.5C95.06,76.64 94.81,76.78 94.56,76.92L94.56,76.92L94.56,76.92C94.46,76.97 94.36,77.03 94.27,77.08L94.19,77.13L94.21,77.04C94.24,76.82 94.28,76.6 94.33,76.38L94.44,75.71L94.57,75.05Z"
          android:fillColor="#ffffff"/>
      <path
          android:pathData="M100.26,68.42C100.2,69.1 100.17,69.85 99.87,70.56C99.49,71.46 98.49,72.69 96.38,72.69C94.23,72.69 93.18,71.46 92.89,70.56C92.66,69.83 92.56,69.1 92.5,68.42C92.45,67.85 91.95,65.91 92.67,64.24C93.21,62.98 94.83,62.19 96.38,62.21C97.93,62.19 99.54,62.98 100.08,64.24C100.81,65.92 100.31,67.86 100.26,68.42Z"
          android:fillColor="#FAE2CC"/>
      <path
          android:pathData="M92.5,68.42C92.28,68.8 91.6,65.63 91.94,64.38C92.2,63.44 92.97,63.02 92.97,63.02C92.97,63.02 93.24,61.86 94.64,61.44C96.62,60.85 99.02,62.52 99.6,62.61C100.17,62.69 101.13,62.44 101.43,62.74C101.74,63.03 101.17,63.9 100.74,64.24C100.74,64.24 101.18,64.93 101.18,65.48C101.18,66.03 100.88,68.43 100.38,68.43C100.06,68.43 99.79,67.26 99.94,65.75C99.94,65.75 98.25,66.6 96.38,66.61C95.33,66.61 94.23,66.42 93.36,65.76C93.36,65.75 93.07,67.45 92.5,68.42Z"
          android:fillColor="#4B4845"/>
      <path
          android:pathData="M99.75,62.86C98.59,62.05 97.1,61.32 95.7,61.15C94.54,61.02 93.25,61.62 93,62.96C93,62.97 93.01,62.98 93.01,62.97C94.22,59.83 97.8,61.72 99.73,62.91C99.75,62.92 99.78,62.88 99.75,62.86Z"
          android:fillColor="#4B4845"/>
      <path
          android:pathData="M93.01,63.08C93.03,63.07 93.02,63.04 93.01,63.05C92.12,63.41 91.58,64.36 91.56,65.28C91.52,66.4 91.92,67.55 92.54,68.48C92.55,68.5 92.6,68.48 92.58,68.45C91.66,66.75 90.89,64.24 93.01,63.08Z"
          android:fillColor="#4B4845"/>
      <path
          android:pathData="M100.72,64.21C100.7,64.21 100.71,64.21 100.72,64.21C101.65,65.4 101.14,66.8 100.63,68.05C100.62,68.07 100.66,68.09 100.67,68.07C101.25,66.81 101.7,65.38 100.72,64.21Z"
          android:fillColor="#4B4845"/>
      <path
          android:pathData="M100.51,68.5C100.51,68.5 100.51,68.51 100.51,68.5C100.52,68.5 100.52,68.51 100.51,68.5Z"
          android:fillColor="#D69487"/>
      <path
          android:pathData="M92.62,68.21C92.62,68.21 92.03,67.59 91.32,67.78C90.61,67.97 90.82,69.15 91.61,69.58C92.35,69.98 92.82,69.66 92.82,69.66L92.62,68.21Z"
          android:fillColor="#FAE2CC"/>
      <path
          android:pathData="M91.25,68.33C91.24,68.33 91.25,68.34 91.25,68.34C91.86,68.27 92.19,68.86 92.24,68.95C92.21,68.93 92.17,68.92 92.14,68.9C91.94,68.85 91.76,68.9 91.59,69C91.58,69.01 91.59,69.02 91.6,69.01C91.75,68.93 91.93,68.95 92.09,69.01C92.23,69.06 92.37,69.14 92.46,69.25C92.48,69.26 92.23,68.02 91.25,68.33Z"
          android:fillColor="#D1AE90"/>
      <path
          android:pathData="M100.14,68.22C100.14,68.22 100.73,67.59 101.44,67.78C102.15,67.97 101.93,69.15 101.15,69.58C100.41,69.98 99.94,69.66 99.94,69.66L100.14,68.22Z"
          android:fillColor="#FAE2CC"/>
      <path
          android:pathData="M101.52,68.33C101.53,68.33 101.52,68.34 101.52,68.34C100.9,68.27 100.58,68.86 100.53,68.95C100.56,68.93 100.6,68.92 100.63,68.9C100.83,68.85 101,68.9 101.18,69C101.18,69.01 101.18,69.02 101.17,69.01C101.01,68.93 100.83,68.95 100.68,69.01C100.54,69.06 100.4,69.14 100.31,69.25C100.29,69.26 100.54,68.02 101.52,68.33Z"
          android:fillColor="#D1AE90"/>
      <path
          android:pathData="M127.68,21.65L174.72,21.65"
          android:strokeAlpha="0.5"
          android:strokeLineJoin="round"
          android:strokeWidth="1.5"
          android:fillColor="#00000000"
          android:strokeColor="#9CBCD0"
          android:strokeLineCap="round"/>
      <path
          android:pathData="M127.68,30.98L174.72,30.98"
          android:strokeAlpha="0.5"
          android:strokeLineJoin="round"
          android:strokeWidth="1.5"
          android:fillColor="#00000000"
          android:strokeColor="#9CBCD0"
          android:strokeLineCap="round"/>
      <path
          android:strokeWidth="1"
          android:pathData="M194.76,34.84l-3.11,0l-0,3.11l3.11,0z"
          android:strokeAlpha="0.5"
          android:strokeLineJoin="round"
          android:fillColor="#00000000"
          android:strokeColor="#9CBCD0"
          android:strokeLineCap="round"/>
      <path
          android:strokeWidth="1"
          android:pathData="M199.73,39.82l-3.12,0l-0,3.12l3.12,0z"
          android:strokeAlpha="0.5"
          android:strokeLineJoin="round"
          android:fillColor="#00000000"
          android:strokeColor="#9CBCD0"
          android:strokeLineCap="round"/>
      <path
          android:strokeWidth="1"
          android:pathData="M199.73,34.84H197.24"
          android:strokeAlpha="0.5"
          android:strokeLineJoin="round"
          android:fillColor="#00000000"
          android:strokeColor="#9CBCD0"
          android:strokeLineCap="round"/>
      <path
          android:strokeWidth="1"
          android:pathData="M192.27,42.93V40.44"
          android:strokeAlpha="0.5"
          android:strokeLineJoin="round"
          android:fillColor="#00000000"
          android:strokeColor="#9CBCD0"
          android:strokeLineCap="round"/>
      <path
          android:strokeWidth="1"
          android:pathData="M188.15,24.27L182.07,24.27A1,1 0,0 0,181.07 25.27L181.07,31.36A1,1 0,0 0,182.07 32.36L188.15,32.36A1,1 0,0 0,189.15 31.36L189.15,25.27A1,1 0,0 0,188.15 24.27z"
          android:strokeAlpha="0.5"
          android:strokeLineJoin="round"
          android:fillColor="#00000000"
          android:strokeColor="#9CBCD0"
          android:strokeLineCap="round"/>
      <path
          android:strokeWidth="1"
          android:pathData="M188.15,34.84L182.07,34.84A1,1 0,0 0,181.07 35.84L181.07,41.93A1,1 0,0 0,182.07 42.93L188.15,42.93A1,1 0,0 0,189.15 41.93L189.15,35.84A1,1 0,0 0,188.15 34.84z"
          android:strokeAlpha="0.5"
          android:strokeLineJoin="round"
          android:fillColor="#00000000"
          android:strokeColor="#9CBCD0"
          android:strokeLineCap="round"/>
      <path
          android:strokeWidth="1"
          android:pathData="M198.73,24.27L192.64,24.27A1,1 0,0 0,191.64 25.27L191.64,31.36A1,1 0,0 0,192.64 32.36L198.73,32.36A1,1 0,0 0,199.73 31.36L199.73,25.27A1,1 0,0 0,198.73 24.27z"
          android:strokeAlpha="0.5"
          android:strokeLineJoin="round"
          android:fillColor="#00000000"
          android:strokeColor="#9CBCD0"
          android:strokeLineCap="round"/>
      <path
          android:strokeWidth="1"
          android:pathData="M186.67,26.76l-3.11,0l-0,3.11l3.11,0z"
          android:strokeAlpha="0.5"
          android:strokeLineJoin="round"
          android:fillColor="#00000000"
          android:strokeColor="#9CBCD0"
          android:strokeLineCap="round"/>
      <path
          android:strokeWidth="1"
          android:pathData="M186.67,37.33l-3.11,0l-0,3.11l3.11,0z"
          android:strokeAlpha="0.5"
          android:strokeLineJoin="round"
          android:fillColor="#00000000"
          android:strokeColor="#9CBCD0"
          android:strokeLineCap="round"/>
      <path
          android:strokeWidth="1"
          android:pathData="M197.24,26.76l-3.11,0l-0,3.11l3.11,0z"
          android:strokeAlpha="0.5"
          android:strokeLineJoin="round"
          android:fillColor="#00000000"
          android:strokeColor="#9CBCD0"
          android:strokeLineCap="round"/>
      <path
          android:pathData="M127.68,57.12L200.85,57.12"
          android:strokeAlpha="0.5"
          android:strokeLineJoin="round"
          android:strokeWidth="1.5"
          android:fillColor="#00000000"
          android:strokeColor="#9CBCD0"
          android:strokeLineCap="round"/>
      <path
          android:pathData="M127.68,66.45L200.85,66.45"
          android:strokeAlpha="0.5"
          android:strokeLineJoin="round"
          android:strokeWidth="1.5"
          android:fillColor="#00000000"
          android:strokeColor="#9CBCD0"
          android:strokeLineCap="round"/>
      <path
          android:pathData="M127.68,75.78L157.18,75.78"
          android:strokeAlpha="0.5"
          android:strokeLineJoin="round"
          android:strokeWidth="1.5"
          android:fillColor="#00000000"
          android:strokeColor="#9CBCD0"
          android:strokeLineCap="round"/>
      <path
          android:pathData="M171.36,75.78L200.85,75.78"
          android:strokeAlpha="0.5"
          android:strokeLineJoin="round"
          android:strokeWidth="1.5"
          android:fillColor="#00000000"
          android:strokeColor="#9CBCD0"
          android:strokeLineCap="round"/>
      <path
          android:pathData="M127.68,85.12L200.85,85.12"
          android:strokeAlpha="0.5"
          android:strokeLineJoin="round"
          android:strokeWidth="1.5"
          android:fillColor="#00000000"
          android:strokeColor="#9CBCD0"
          android:strokeLineCap="round"/>
    </group>
    <path
        android:strokeWidth="1"
        android:pathData="M76,11.7L204,11.7A5.5,5.5 0,0 1,209.5 17.2L209.5,89.2A5.5,5.5 0,0 1,204 94.7L76,94.7A5.5,5.5 0,0 1,70.5 89.2L70.5,17.2A5.5,5.5 0,0 1,76 11.7z"
        android:fillColor="#00000000"
        android:strokeColor="#D9E1E7"/>
    <group>
      <clip-path
          android:pathData="M141,35.47L163.27,35.47A1,1 0,0 1,164.27 36.47L164.27,51.27A1,1 0,0 1,163.27 52.27L141,52.27A1,1 0,0 1,140 51.27L140,36.47A1,1 0,0 1,141 35.47z"/>
      <path
          android:pathData="M140,35.47h24.27v16.8h-24.27z"
          android:fillColor="#005993"/>
      <path
          android:pathData="M140,42.47h24.27v2.8h-24.27z"
          android:fillColor="#ffffff"/>
      <path
          android:pathData="M146.53,43.87a5.6,5.6 0,1 0,11.2 0a5.6,5.6 0,1 0,-11.2 0z"
          android:fillColor="#ffffff"/>
      <path
          android:pathData="M148.4,43.87a3.73,3.73 0,1 0,7.47 0a3.73,3.73 0,1 0,-7.47 0z"
          android:fillColor="#005993"/>
    </group>
  </group>
  <path
      android:pathData="M173.8,99.19C180.43,99.19 185.8,104.56 185.8,111.19V136.62C186.81,136.72 187.6,137.57 187.6,138.61V157.06C187.6,158.1 186.81,158.95 185.8,159.05V253.59C185.8,260.22 180.43,265.59 173.8,265.59H108.03C101.4,265.59 96.03,260.22 96.03,253.59V164.03C95.04,163.91 94.27,163.07 94.27,162.05V151.08C94.27,150.05 95.04,149.21 96.03,149.09V144.07C95.04,143.96 94.27,143.12 94.27,142.09V131.12C94.27,130.1 95.04,129.25 96.03,129.14V111.19C96.03,104.56 101.4,99.19 108.03,99.19H173.8Z"
      android:fillColor="#005993"/>
  <path
      android:pathData="M169,106.81C173.97,106.81 178,110.84 178,115.81V248.62C178,253.59 173.97,257.62 169,257.62H112.83C107.86,257.62 103.83,253.59 103.83,248.62V115.81C103.83,110.84 107.86,106.81 112.83,106.81H125.61C125.69,109.04 127.52,110.81 129.77,110.81H152.83C155.07,110.81 156.9,109.04 156.98,106.81H169Z"
      android:fillColor="#F8FCFF"/>
  <path
      android:strokeWidth="1"
      android:pathData="M107.53,104.35L174.33,104.35A2.5,2.5 0,0 1,176.83 106.85L176.83,130.72A2.5,2.5 0,0 1,174.33 133.22L107.53,133.22A2.5,2.5 0,0 1,105.03 130.72L105.03,106.85A2.5,2.5 0,0 1,107.53 104.35z"
      android:fillColor="#DE4D76"
      android:fillAlpha="0.15"
      android:strokeColor="#D71249"/>
  <path
      android:strokeWidth="1"
      android:pathData="M136.47,29.43L167.8,29.43A2.5,2.5 0,0 1,170.3 31.93L170.3,55.8A2.5,2.5 0,0 1,167.8 58.3L136.47,58.3A2.5,2.5 0,0 1,133.97 55.8L133.97,31.93A2.5,2.5 0,0 1,136.47 29.43z"
      android:fillColor="#DE4D76"
      android:fillAlpha="0.15"
      android:strokeColor="#D71249"/>
</vector>
