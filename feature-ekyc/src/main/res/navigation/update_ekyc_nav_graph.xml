<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/update_ekyc_nav_graph">

    <!--updateBiometricFragment -->
    <fragment
        android:id="@+id/updateBiometricFragment"
        android:name="com.vietinbank.core_ekyc.update_ekyc.update_biometric.UpdateBiometricFragment"
        android:label="updateBiometric">
        <deepLink app:uri="update://biometric" />
    </fragment>

    <!--verifyOtpFragment -->
    <fragment
        android:id="@+id/verifyOtpFragment"
        android:name="com.vietinbank.core_ekyc.update_ekyc.verify_otp.VerifyOtpFragment"
        android:label="verifyOtp">
    <deepLink app:uri="updateEkyc://verifyOtpFragment" />
    </fragment>

    <!--userInfoFragment -->
    <fragment
        android:id="@+id/userInfoFragment"
        android:name="com.vietinbank.core_ekyc.update_ekyc.user_info.UserInfoFragment"
        android:label="userInfo" >
    <deepLink app:uri="updateEkyc://userInfoFragment" />
    </fragment>


    <!--showContractFragment -->
    <fragment
        android:id="@+id/showContractFragment"
        android:name="com.vietinbank.core_ekyc.update_ekyc.show_contract.ShowContractFragment"
        android:label="showContract" />

    <!--detailContractFragment -->
    <fragment
        android:id="@+id/detailContractFragment"
        android:name="com.vietinbank.core_ekyc.update_ekyc.detail_contract.DetailContractFragment"
        android:label="detailContract" />

    <!--preApproveEkycFragment -->
    <fragment
        android:id="@+id/preApproveEkycFragment"
        android:name="com.vietinbank.core_ekyc.update_ekyc.pre_approve.PreApproveEkycFragment"
        android:label="preApproveEkyc" />


</navigation>