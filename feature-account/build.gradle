plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.hilt.android)
    alias(libs.plugins.compose.compiler)
    id('kotlin-kapt')
}

android {
    namespace 'com.vietinbank.feature_account'
    compileSdkVersion libs.versions.compileSdk.get().toInteger()

    defaultConfig {
        minSdkVersion libs.versions.minSdk.get().toInteger()
        targetSdkVersion libs.versions.targetSdk.get().toInteger()

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = '11'
    }
    buildFeatures {
        viewBinding = true
        buildConfig = true
        compose true
    }

}

dependencies {
    implementation project(':core-common')
    implementation project(':core-domain')
    implementation project(':core-data')
    implementation project(':core-ui')
    // UI Testing
    androidTestImplementation libs.androidx.ui.test.junit4
    debugImplementation libs.androidx.ui.test.manifest

    // Tích hợp với AndroidX
    implementation libs.androidx.activity.compose
    implementation libs.androidx.lifecycle.viewmodel.compose

    // AndroidX
    implementation libs.androidx.runtime.livedata
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)

    // Lifecycle
    implementation(libs.lifecycle.livedata)
    implementation(libs.lifecycle.viewmodel)

    // Hilt
    implementation(libs.hilt.android)
    kapt(libs.hilt.compiler)

    // Các thư viện XML
    implementation(libs.material)
    implementation(libs.navigation.fragment)
    implementation(libs.navigation.ui)
    implementation(libs.gson)

    // Thư viện Compose cơ bản
    implementation libs.compose.ui
    implementation libs.compose.ui.tooling.preview
    debugImplementation libs.compose.ui.tooling
    implementation libs.androidx.material3
    implementation libs.androidx.constraintlayout.compose
    implementation(libs.androidx.material)


    // Sử dụng BOM cho Compose
    def composeBom = platform(libs.compose.bom)
    implementation composeBom
    androidTestImplementation composeBom

    // Material Design
    implementation(libs.material)
    // qr code
    implementation(libs.zxing)
}