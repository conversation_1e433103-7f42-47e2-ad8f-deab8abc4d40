package com.vietinbank.feature_account.screen

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.vietinbank.core_data.models.response.SearchUiState
import com.vietinbank.core_domain.models.account.AccountModelUI
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.foundation.textfield.FoundationFieldType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.eFastBackgroundLevel2
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_account.AccountViewModel
import com.vietinbank.feature_account.IAccountAction
import com.vietinbank.feature_account.ScreenAction
import com.vietinbank.feature_account.SearchAction
import com.vietinbank.feature_account.render.IRenderAccount
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun SearchScreen(
    searchUiState: SearchUiState,
    renderAccount: IRenderAccount?,
    onAction: (IAccountAction) -> Unit,
) {
    val searchLst: List<AccountModelUI>? by remember(searchUiState.searchText) {
        derivedStateOf {
            if (searchUiState.searchText.isNullOrEmpty()) {
                searchUiState.accountLst
            } else {
                searchUiState.accountLst?.filter { item ->
                    true == item.aliasName?.contains(
                        searchUiState.searchText ?: "", true,
                    ) || true == item.accountNo?.contains(
                        searchUiState.searchText ?: "", true,
                    )
                }
            }
        }
    }
    Scaffold(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackgroundLevel2()
            .systemBarsPadding()
            .imePadding(), // Edge-to-edge display
        containerColor = Color.Transparent, // Keep gradient background from MainActivity
        topBar = {
            // Both AppBar and Tabs are sticky/pinned
            Column(modifier = Modifier.fillMaxWidth().padding(top = FDS.Sizer.Gap.gap8)) {
                // FoundationAppBar with title below buttons (as designed)
                FoundationAppBar(
                    title = stringResource(
                        R.string.account_inquiry_search_title,
                        searchUiState.inquiryName ?: "",
                    ),
                    isLightIcon = false,
                    isSingleLineAppBar = true,
                    onNavigationClick = { onAction.invoke(ScreenAction.ClickedOnBack) },
                )

                // Search with clear button
                FoundationFieldType(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = FDS.Sizer.Gap.gap16, horizontal = FDS.Sizer.Gap.gap24)
                        .clip(RoundedCornerShape(FDS.Sizer.Radius.radius32))
                        .background(FDS.Colors.gray50),
                    value = searchUiState.searchText ?: "",
                    onValueChange = {
                        onAction.invoke(SearchAction.ClickedOnSearch(it))
                    },
                    placeholder = if (searchUiState.inquiryType == AccountViewModel.DDA) {
                        stringResource(R.string.account_inquiry_dda_search_hint)
                    } else {
                        stringResource(R.string.account_inquiry_not_dda_search_hint)
                    },
                    leadingIcon = {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_search),
                            contentDescription = "",
                            tint = FDS.Colors.characterSecondary,
                            modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                        )
                    },
                    trailingIcon = if (!searchUiState.searchText.isNullOrEmpty()) {
                        {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_close),
                                contentDescription = "Clear",
                                tint = FDS.Colors.characterSecondary,
                                modifier = Modifier
                                    .size(FDS.Sizer.Icon.icon24)
                                    .safeClickable {
                                        onAction.invoke(SearchAction.ClickedOnSearch(""))
                                    },
                            )
                        }
                    } else {
                        null
                    },
                )
            }
        },
    ) { paddingValues ->
        if (!searchUiState.searchText.isNullOrEmpty()) {
            LazyColumn(
                state = rememberLazyListState(),
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .clip(
                        RoundedCornerShape(
                            topStart = FDS.Sizer.Radius.radius32,
                            topEnd = FDS.Sizer.Radius.radius32,
                        ),
                    )
                    .background(FDS.Colors.backgroundBgContainer),
            ) {
                when {
                    true == searchLst?.isEmpty() -> {
                        item {
                            Column(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalAlignment = Alignment.CenterHorizontally,
                                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap24),
                            ) {
                                Image(
                                    modifier = Modifier.padding(top = FDS.Sizer.Gap.gap32),
                                    painter = painterResource(R.drawable.ic_common_empty_search_96),
                                    contentDescription = null,
                                )

                                FoundationText(
                                    text = stringResource(R.string.account_inquiry_search_empty),
                                    style = FDS.Typography.headingH3,
                                    color = FDS.Colors.characterHighlighted,
                                    textAlign = TextAlign.Center,
                                )
                            }
                        }
                    }

                    else -> {
                        itemsIndexed(
                            searchLst ?: emptyList<AccountModelUI>(),
                            key = { index, item ->
                                "key_${index}_${item.accountNo}"
                            },
                        ) { index, item ->
                            renderAccount?.RenderAccountItem(
                                account = item,
                                isShowDivider = index != searchLst?.lastIndex,
                                highlightText = searchUiState.searchText,
                                onAction = onAction,
                            )
                        }
                    }
                }
            }
        }
    }
}