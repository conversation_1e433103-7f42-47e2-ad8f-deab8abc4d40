package com.vietinbank.feature_account.screen

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.res.imageResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.extensions.containsNotVietNam
import com.vietinbank.core_common.extensions.getAmountServer
import com.vietinbank.core_domain.models.account.ListEnterpriseDomain
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.ButtonSize
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationButtonLight
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.FoundationInfoVertical
import com.vietinbank.core_ui.components.FoundationTabs
import com.vietinbank.core_ui.components.TabType
import com.vietinbank.core_ui.components.foundation.textfield.FoundationEditText
import com.vietinbank.core_ui.components.foundation.textfield.FoundationFieldType
import com.vietinbank.core_ui.components.foundation.textfield.InputType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_account.R
import com.vietinbank.feature_account.di.AccountQRAction
import com.vietinbank.feature_account.fragment.BackgroundUI
import com.vietinbank.feature_account.fragment.QRAccountViewModel
import com.vietinbank.feature_account.fragment.QRAccountViewModel.Companion.STYPE_1
import com.vietinbank.feature_account.fragment.QRUIState
import com.vietinbank.feature_account.view.AccountBottomSheet
import com.vietinbank.feature_account.view.EditQRPreviewSectionView
import com.vietinbank.feature_account.view.EditQRStyleAndBackgroundView

@Composable
fun EditQRScreen(
    genQR: QRUIState?,
    onAction: (AccountQRAction) -> Unit,
    tabs: List<String>,
    imageList: List<BackgroundUI>,
    onAvatarListUpdate: (List<ImageBitmap?>, Int?, Int?) -> Unit,
) {
    val inputTabQR = genQR?.inputTabQR ?: 0
    val avatarBitmaps = genQR?.avatarBitmaps ?: emptyList()
    var selectedIndex by remember { mutableStateOf<Int?>(null) }
    var selectedPreviewIndex by remember { mutableStateOf<Int?>(null) }
    val innerScroll = rememberScrollState()
    val bgScreen = FoundationDesignSystem.Colors.backgroundBgScreen

    CompanyBottomSheet(
        visible = genQR?.isShowCompany ?: false,
        genQR?.allCompany ?: emptyList(),
        genQR?.selectedCompany,
        onAction,
    )

    AccountBottomSheet(
        visible = genQR?.isShowAccount ?: false,
        genQR?.allAccount ?: emptyList(),
        genQR?.selectedAccount,
        onAction,
    )

    Column(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding()
            .imePadding(),
    ) {
        FoundationAppBar(
            title = stringResource(R.string.common_qr_edit),
            isSingleLineAppBar = true,
            isLightIcon = false,
            titleStyle = FoundationDesignSystem.Typography.headingH4,
            navigationIcon = rememberVectorPainter(Icons.AutoMirrored.Filled.ArrowBack),
            onNavigationClick = { onAction(AccountQRAction.OnBackPressed) },
        )

        EditQRPreviewSectionView(
            backgroundImage = ImageBitmap.imageResource(id = com.vietinbank.core_ui.R.drawable.bg_common_efast_logo),
            backgroundQR = genQR?.backgroundQR,
            genQR = genQR?.qr?.asImageBitmap(),
            avatarQR = genQR?.avatarQR ?: QRAccountViewModel.AVATAR_CENTER,
            avatarBG = genQR?.avatarBG,
            isShowLogo = (genQR?.isShowAvatar == true),
            onAvatarClick = { onAction(AccountQRAction.OnAvatarClick(0)) },
        )

        FoundationTabs(
            tabs = tabs,
            selectedIndex = inputTabQR,
            onTabSelected = { onAction(AccountQRAction.OnTabSelected(it)) },
            type = TabType.Pill,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Gap.gap16),
        )

        Box(
            modifier = Modifier
                .weight(1f)
                .fillMaxWidth(),
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        FoundationDesignSystem.Colors.backgroundBgContainer,
                        RoundedCornerShape(
                            topStart = FoundationDesignSystem.Sizer.Radius.radius32,
                            topEnd = FoundationDesignSystem.Sizer.Radius.radius32,
                        ),
                    )
                    .padding(FoundationDesignSystem.Sizer.Gap.gap24),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .verticalScroll(innerScroll),
                ) {
                    when (inputTabQR) {
                        QRAccountViewModel.CONTENT -> {
                            EditQRContentInput(genQR, onAction)
                        }

                        QRAccountViewModel.AVATAR -> {
                            EditQRStyleAndBackgroundView(
                                styleQR = genQR?.styleQR ?: STYPE_1,
                                avatarQR = genQR?.avatarQR,
                                backgroundQR = genQR?.backgroundQR,
                                imageList = imageList,
                                onStyleChange = { onAction(AccountQRAction.OnStyleChange(it)) },
                                onBackgroundChange = { onAction(AccountQRAction.OnBackgroundChange(it)) },
                                onLocalClick = { onAction(AccountQRAction.OnLocalClick) },
                                onClickAbove = {
                                    onAction(AccountQRAction.OnPositionChange(QRAccountViewModel.AVATAR_TOP))
                                },
                                onClickInside = {
                                    onAction(AccountQRAction.OnPositionChange(QRAccountViewModel.AVATAR_CENTER))
                                },
                                onClickBelow = {
                                    onAction(AccountQRAction.OnPositionChange(QRAccountViewModel.AVATAR_BOTTOM))
                                },
                                isShowAvatar = genQR?.isShowAvatar == true,
                                onToggleAvatar = { enabled -> onAction(AccountQRAction.ToggleAvatar(enabled)) },
                            )
                        }

                        else -> {}
                    }

                    Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap80)) // chừa chỗ cho nút lưu
                }
            }

            // Button Lưu với nền mờ dần
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.BottomCenter)
                    .drawBehind {
                        drawRoundRect(
                            brush = Brush.linearGradient(
                                listOf(
                                    bgScreen,
                                    bgScreen,
                                    bgScreen,
                                    Color.White.copy(0f),
                                ),
                                start = Offset(0.5f, size.height),
                                end = Offset(0.5f, 0f),
                            ),
                        )
                    }
                    .padding(horizontal = FoundationDesignSystem.Sizer.Padding.padding24)
                    .padding(
                        top = FoundationDesignSystem.Sizer.Padding.padding16,
                        bottom = FoundationDesignSystem.Sizer.Padding.padding8,
                    ),
            ) {
                FoundationButtonLight(
                    text = stringResource(R.string.account_qr_edit_save),
                    onClick = {
                        onAction(
                            AccountQRAction.OnCreateClick(
                                genQR?.amount?.getAmountServer().toString(),
                                genQR?.note.toString(),
                            ),
                        )
                    },
                    size = ButtonSize.Large,
                    modifier = Modifier.fillMaxWidth(),
                )
            }
        }
    }
}

@Composable
fun EditQRContentInput(
    model: QRUIState? = null,
    onAction: (AccountQRAction) -> Unit,
) {
    FoundationInfoVertical(
        modifier = Modifier.padding(top = 32.dp),
        title = stringResource(R.string.account_qr_edit_cty),
        value = model?.companyName ?: "",
        icon = com.vietinbank.core_ui.R.drawable.ic_drop_down,
        valueColor = FoundationDesignSystem.Colors.characterHighlighted,
        valueStyle = FoundationDesignSystem.Typography.bodyB1,
        titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
        titleColor = FoundationDesignSystem.Colors.characterSecondary,
    ) {
        onAction(AccountQRAction.OnShowCompany(true))
    }

    FoundationDivider(
        modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap16),
    )

    FoundationInfoVertical(
        modifier = Modifier.padding(top = 32.dp),
        title = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_tab_saved_account),
        value = model?.accountNo ?: "",
        icon = com.vietinbank.core_ui.R.drawable.ic_drop_down,
        valueColor = FoundationDesignSystem.Colors.characterHighlighted,
        valueStyle = FoundationDesignSystem.Typography.bodyB1,
        titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
        titleColor = FoundationDesignSystem.Colors.characterSecondary,
    ) {
        onAction(AccountQRAction.OnShowAccount(true))
    }

    FoundationDivider(
        modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap16),
    )

    FoundationEditText(
        value = model?.amount ?: "",
        onValueChange = {
            onAction(AccountQRAction.OnValueChangeAmount(it))
        },
        placeholder = stringResource(R.string.account_qr_edit_money),
        hintText = stringResource(R.string.account_qr_edit_money_hint),
        inputType = InputType.AMOUNT,

        modifier = Modifier.fillMaxWidth().padding(top = FoundationDesignSystem.Sizer.Gap.gap16),
    )

    FoundationDivider(
        modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap16),
    )

    FoundationEditText(
        value = model?.note ?: "",
        onValueChange = {
            onAction(AccountQRAction.OnValueChangeContent(it))
        },
        maxLength = QRAccountViewModel.MAX_LENGTH_CONTENT,
        placeholder = stringResource(R.string.account_qr_edit_content_money),
        hintText = stringResource(R.string.account_qr_edit_content_money_hint),
        inputType = InputType.TEXT,
        modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap16),
    )
}

@Composable
fun CompanyBottomSheet(
    visible: Boolean,
    lstCompany: List<ListEnterpriseDomain>?,
    companySelected: ListEnterpriseDomain?,
    onAccountAction: (AccountQRAction) -> Unit,
) {
    var searchText by remember { mutableStateOf("") }
    val searchLst by remember(lstCompany, searchText) {
        derivedStateOf {
            if (searchText.isEmpty()) {
                lstCompany ?: emptyList()
            } else {
                lstCompany?.filter { item ->
                    true == item.enterpriseid?.containsNotVietNam(searchText) || true == item.enterprisename?.containsNotVietNam(
                        searchText,
                    )
                } ?: emptyList()
            }
        }
    }

    BaseBottomSheet<AccountQRAction>(
        visible = visible,
        onDismissRequest = {
            searchText = ""
            onAccountAction.invoke(AccountQRAction.OnShowCompany(false))
        },
        onResult = { onAccountAction(it) },
        allowTouchDismiss = true,
        secureFlag = true,
    ) { onSelect ->
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(FoundationDesignSystem.Sizer.Radius.radius32))
                .background(FoundationDesignSystem.Colors.backgroundBgContainer)
                .padding(vertical = FoundationDesignSystem.Sizer.Gap.gap20),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            FoundationText(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(com.vietinbank.core_ui.R.string.account_inquiry_list_company),
                style = FoundationDesignSystem.Typography.headingH3,
                color = FoundationDesignSystem.Colors.characterHighlighted,
                textAlign = TextAlign.Center,
            )

            FoundationDivider(modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap24))

            if (!lstCompany.isNullOrEmpty()) {
                FoundationFieldType(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = FoundationDesignSystem.Sizer.Gap.gap24, bottom = FoundationDesignSystem.Sizer.Gap.gap8)
                        .padding(horizontal = FoundationDesignSystem.Sizer.Gap.gap24)
                        .clip(RoundedCornerShape(FoundationDesignSystem.Sizer.Radius.radius32))
                        .background(FoundationDesignSystem.Colors.gray50),
                    value = searchText,
                    onValueChange = { searchText = it },
                    placeholder = stringResource(R.string.account_qr_edit_company_search),
                    leadingIcon = {
                        Image(
                            painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_search),
                            contentDescription = "",
                        )
                    },
                    trailingIcon = {
                        if (searchText.isNotEmpty()) {
                            Icon(
                                painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_close),
                                contentDescription = "Clear",
                                tint = FoundationDesignSystem.Colors.characterSecondary,
                                modifier = Modifier
                                    .size(FoundationDesignSystem.Sizer.Icon.icon24)
                                    .safeClickable {
                                        searchText = ""
                                    },
                            )
                        }
                    },
                )
            }

            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = FoundationDesignSystem.Sizer.Gap.gap8)
                    .weight(1f, fill = false)
                    .heightIn(max = FoundationDesignSystem.Sizer.Dialog.maxListHeight),
                contentPadding = PaddingValues(horizontal = FoundationDesignSystem.Sizer.Padding.padding8),
                verticalArrangement = Arrangement.spacedBy(FoundationDesignSystem.Sizer.Gap.gap0),
            ) {
                itemsIndexed(searchLst) { index, item ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(
                                horizontal = FoundationDesignSystem.Sizer.Gap.gap24,
                                vertical = FoundationDesignSystem.Sizer.Gap.gap8,
                            )
                            .safeClickable(onSafeClick = {
                                onSelect(AccountQRAction.ClickedOnCompany(item))
                            }),
                        horizontalArrangement = Arrangement.spacedBy(
                            FoundationDesignSystem.Sizer.Spacing.spacingSmall,
                        ),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        Column(modifier = Modifier.weight(1f)) {
                            FoundationText(
                                text = item.enterprisename ?: "",
                                color = FoundationDesignSystem.Colors.characterPrimary,
                                style = FoundationDesignSystem.Typography.bodyB2,
                            )

                            FoundationText(
                                modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap4),
                                text = item.enterpriseid ?: "",
                                color = FoundationDesignSystem.Colors.characterPrimary,
                                style = FoundationDesignSystem.Typography.captionCaptionL,
                            )
                        }

                        Image(
                            painter = painterResource(
                                if (companySelected?.enterpriseid == item.enterpriseid) {
                                    com.vietinbank.core_ui.R.drawable.ic_common_radio_check_24
                                } else {
                                    com.vietinbank.core_ui.R.drawable.ic_common_radio_uncheck_24
                                },
                            ),
                            contentDescription = null,
                        )
                    }
                    FoundationDivider(
                        modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap4),
                    )
                }
            }
        }
    }
}