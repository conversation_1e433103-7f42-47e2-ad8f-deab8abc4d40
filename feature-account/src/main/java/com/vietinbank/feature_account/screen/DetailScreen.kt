package com.vietinbank.feature_account.screen

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Alignment.Companion.CenterHorizontally
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import com.vietinbank.core_common.extensions.getAmountServer
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyCurrency
import com.vietinbank.core_data.models.response.AliasUiState
import com.vietinbank.core_data.models.response.DisplayModelUI
import com.vietinbank.core_domain.models.account.HistoryDomain
import com.vietinbank.core_ui.components.CircularIconButton
import com.vietinbank.core_ui.components.CircularIconButtonSize
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.FoundationInfoVertical
import com.vietinbank.core_ui.components.FoundationTabs
import com.vietinbank.core_ui.components.TabType
import com.vietinbank.core_ui.components.foundation.textfield.FoundationFieldType
import com.vietinbank.core_ui.components.text.foundation.FoundationIconText
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.models.IconConfig
import com.vietinbank.core_ui.models.IconPosition
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_account.AccountAction
import com.vietinbank.feature_account.AccountViewModel
import com.vietinbank.feature_account.AccountViewModel.Companion.TAB_HISTORY
import com.vietinbank.feature_account.DetailAction
import com.vietinbank.feature_account.IAccountAction
import com.vietinbank.feature_account.R
import com.vietinbank.feature_account.ScreenAction
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun DetailScreenFinal(
    viewModel: AccountViewModel,
    onAction: (IAccountAction) -> Unit,
) {
    val transactionHistory = viewModel.transactionHistory
    val detailUiState by viewModel.detailUiState.collectAsState()
    val focusManager = LocalFocusManager.current
    val keyboardController = LocalSoftwareKeyboardController.current

    // Focus requesters for input fields
    val aliasNameFocusRequester = remember { FocusRequester() }
    LaunchedEffect(Unit) {
        viewModel.observeListScroll()
    }

    FilterHistoryBottomSheet(
        visible = detailUiState.isShowFilter,
        historyFilter = detailUiState.filterHistory,
        onDismissRequest = {
            onAction.invoke(DetailAction.ClickedOnFilter(false))
        },
        onFilter = {
            onAction.invoke(DetailAction.OnApplyFilter(it))
        },
    )

    Scaffold(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding(), // Edge-to-edge display
        containerColor = Color.Transparent, // Keep gradient background from MainActivity
        topBar = {
            // Both AppBar and Tabs are sticky/pinned
            Column(Modifier.fillMaxWidth()) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            FDS.Colors.backgroundBgContainer,
                            RoundedCornerShape(
                                bottomStart = FDS.Sizer.Radius.radius32,
                                bottomEnd = FDS.Sizer.Radius.radius32,
                            ),
                        )
                        .padding(vertical = FDS.Sizer.Gap.gap24),
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = FDS.Sizer.Padding.padding24),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        CircularIconButton(
                            icon = painterResource(com.vietinbank.core_ui.R.drawable.ic_common_back_24dp),
                            onClick = {
                                onAction(ScreenAction.ClickedOnBack)
                            },
                            contentDescription = "back",
                            size = CircularIconButtonSize.Medium,
                            isLightIcon = true,
                        )

                        FoundationFieldType(
                            modifier = Modifier
                                .weight(1f)
                                .focusRequester(aliasNameFocusRequester),
                            value = detailUiState.alias?.aliasNameChange ?: "",
                            placeholder = stringResource(com.vietinbank.core_ui.R.string.account_detail_alias_placeholder),
                            onValueChange = {
                                onAction(
                                    DetailAction.OnChangeAlias(
                                        AliasUiState(
                                            aliasName = detailUiState.alias?.aliasName,
                                            aliasNameChange = it,
                                            isChanging = true,
                                        ),
                                    ),
                                )
//                                onAction(DetailAction.OnChangeAlias(true, it))
                            },
                            keyboardOptions = KeyboardOptions(
                                keyboardType = KeyboardType.Text,
                                imeAction = ImeAction.Done,
                            ),
                            keyboardActions = KeyboardActions(
                                onDone = {
                                    focusManager.clearFocus()
                                    onAction(
                                        DetailAction.OnChangeAlias(
                                            AliasUiState(
                                                aliasName = detailUiState.alias?.aliasName,
                                                aliasNameChange = detailUiState.alias?.aliasNameChange,
                                                isChanging = false,
                                            ),
                                            true,
                                        ),
                                    )
                                },
                            ),
                        )

                        if (true == detailUiState.alias?.isChanging) {
                            Image(
                                modifier = Modifier.safeClickable {
                                    // close alias
                                    focusManager.clearFocus()
                                    onAction(
                                        DetailAction.OnChangeAlias(
                                            AliasUiState(
                                                aliasName = detailUiState.alias?.aliasName,
                                                isChanging = false,
                                                aliasNameChange = detailUiState.alias?.aliasName,
                                            ),
                                        ),
                                    )
                                },
                                painter = painterResource(R.drawable.ic_account_alias_close_24),
                                contentDescription = "alias_close",
                            )
                        } else {
                            CircularIconButton(
                                icon = painterResource(R.drawable.ic_account_edit_24),
                                onClick = {
                                    aliasNameFocusRequester.requestFocus()
                                    onAction(
                                        DetailAction.OnChangeAlias(
                                            AliasUiState(
                                                aliasName = detailUiState.alias?.aliasName,
                                                isChanging = true,
                                                aliasNameChange = detailUiState.alias?.aliasName,
                                            ),
                                        ),
                                    )
                                },
                                contentDescription = "edit_alias",
                                size = CircularIconButtonSize.Medium, // Use medium size for app bar
                                isLightIcon = true,
                            )
                        }
                    }

                    FoundationInfoVertical(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = FDS.Sizer.Gap.gap24)
                            .padding(horizontal = FDS.Sizer.Gap.gap24),
                        title = stringResource(com.vietinbank.core_ui.R.string.common_company_name),
                        value = detailUiState.account?.companyName ?: "",
                    )

                    FoundationInfoVertical(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = FDS.Sizer.Gap.gap16)
                            .padding(horizontal = FDS.Sizer.Gap.gap24),
                        title = stringResource(com.vietinbank.core_ui.R.string.common_account_no),
                        value = detailUiState.account?.accountNo ?: "",
                        icon = com.vietinbank.core_ui.R.drawable.ic_account_copy_24,
                        onClick = {
                            onAction(
                                AccountAction.ClickedOnCopy(
                                    detailUiState.account?.accountNo ?: "",
                                ),
                            )
                        },
                    )

                    FoundationInfoVertical(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = FDS.Sizer.Gap.gap16)
                            .padding(horizontal = FDS.Sizer.Gap.gap24),
                        title = stringResource(com.vietinbank.core_ui.R.string.account_payment_current_balance),
                        value = Utils.g().getDotMoneyHasCcy(
                            detailUiState.account?.currentBalance ?: "",
                            detailUiState.account?.currency ?: "",
                        ),
                    )

                    FoundationInfoVertical(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = FDS.Sizer.Gap.gap16)
                            .padding(horizontal = FDS.Sizer.Gap.gap24),
                        title = stringResource(com.vietinbank.core_ui.R.string.account_payment_total_balance),
                        value = Utils.g().getDotMoneyHasCcy(
                            detailUiState.account?.availableBalance ?: "",
                            detailUiState.account?.currency ?: "",
                        ),
                    )

                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = FDS.Sizer.Gap.gap24)
                            .padding(horizontal = FDS.Sizer.Gap.gap24),
                    ) {
                        if (viewModel.isChecker() &&
                            detailUiState.account?.currency == MoneyCurrency.VND.name &&
                            detailUiState.account?.accountType == "D"
                        ) {
                            FoundationIconText(
                                modifier = Modifier
                                    .weight(1f)
                                    .safeClickable {
                                        onAction.invoke(AccountAction.ClickedOnQR)
                                    },
                                text = stringResource(com.vietinbank.core_ui.R.string.common_qr),
                                icons = mapOf(
                                    IconPosition.TOP to IconConfig(
                                        icon = R.drawable.ic_account_qr_32,
                                        size = FDS.Sizer.Icon.icon32,
                                    ),
                                ),
                                style = FDS.Typography.interactionLink,
                                color = FDS.Colors.blue900,
                                textAlign = TextAlign.Center,
                                horizontalAlignment = CenterHorizontally,
                            )
                        }

                        if (!viewModel.isChecker()) {
                            Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap16))
                            FoundationIconText(
                                modifier = Modifier
                                    .safeClickable {
                                        onAction.invoke(AccountAction.ClickedOnTransfer)
                                    }
                                    .weight(1f),
                                text = stringResource(com.vietinbank.core_ui.R.string.common_transfer),
                                icons = mapOf(
                                    IconPosition.TOP to IconConfig(
                                        icon = R.drawable.ic_accounnt_transfer_32,
                                        size = FDS.Sizer.Icon.icon32,
                                    ),
                                ),
                                style = FDS.Typography.interactionLink,
                                color = FDS.Colors.blue900,
                                textAlign = TextAlign.Center,
                                horizontalAlignment = CenterHorizontally,
                            )
                            Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap16))

                            FoundationIconText(
                                modifier = Modifier
                                    .safeClickable {
                                        onAction.invoke(AccountAction.ClickedOnBill)
                                    }
                                    .weight(1f),
                                text = stringResource(com.vietinbank.core_ui.R.string.common_bill),
                                icons = mapOf(
                                    IconPosition.TOP to IconConfig(
                                        icon = R.drawable.ic_accounnt_bill_32,
                                        size = FDS.Sizer.Icon.icon32,
                                    ),
                                ),
                                style = FDS.Typography.interactionLink,
                                color = FDS.Colors.blue900,
                                textAlign = TextAlign.Center,
                                horizontalAlignment = CenterHorizontally,
                            )
                            Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap16))
                            FoundationIconText(
                                modifier = Modifier
                                    .safeClickable {
                                        onAction.invoke(AccountAction.ClickedOnSaving)
                                    }
                                    .weight(1f),
                                text = stringResource(com.vietinbank.core_ui.R.string.common_saving),
                                icons = mapOf(
                                    IconPosition.TOP to IconConfig(
                                        icon = R.drawable.ic_accounnt_saving_32,
                                        size = FDS.Sizer.Icon.icon32,
                                    ),
                                ),
                                style = FDS.Typography.interactionLink,
                                color = FDS.Colors.blue900,
                                textAlign = TextAlign.Center,
                                horizontalAlignment = CenterHorizontally,
                            )
                        }
                    }
                }

                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = FDS.Sizer.Gap.gap24, vertical = FDS.Sizer.Gap.gap16),
                ) {
                    FoundationTabs(
                        tabs = listOf(
                            stringResource(com.vietinbank.core_ui.R.string.account_detail_tab_history),
                            stringResource(com.vietinbank.core_ui.R.string.account_detail_tab_info),
                        ),
                        selectedIndex = detailUiState.tabSelected,
                        onTabSelected = { onAction(ScreenAction.ClickedOnTab(it)) },
                        type = TabType.Pill,
                    )
                }
            }
        },
    ) { paddingValues ->

        LazyColumn(
            state = viewModel.lazyDetailState,
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .clip(
                    RoundedCornerShape(
                        topStart = FDS.Sizer.Radius.radius32,
                        topEnd = FDS.Sizer.Radius.radius32,
                    ),
                )
                .background(FDS.Colors.backgroundBgContainer),
            contentPadding = PaddingValues(bottom = FDS.Sizer.Padding.padding16),
        ) {
            when (detailUiState.tabSelected) {
                TAB_HISTORY -> {
                    item {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(FDS.Sizer.Gap.gap24),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
                        ) {
                            // Search with clear button
                            FoundationFieldType(
                                modifier = Modifier
                                    .weight(1f)
                                    .clip(RoundedCornerShape(FDS.Sizer.Radius.radius32))
                                    .background(FDS.Colors.gray50),
                                value = detailUiState.filterText ?: "",
                                onValueChange = {
                                    onAction.invoke(DetailAction.ClickedOnSearch(it))
                                },
                                placeholder = stringResource(com.vietinbank.core_ui.R.string.common_search_hint),
                                leadingIcon = {
                                    Icon(
                                        painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_search),
                                        contentDescription = "Search",
                                        tint = FDS.Colors.characterSecondary,
                                        modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                                    )
                                },
                                trailingIcon = if (!detailUiState.filterText.isNullOrEmpty()) {
                                    {
                                        Icon(
                                            painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_close),
                                            contentDescription = "Clear",
                                            tint = FDS.Colors.characterSecondary,
                                            modifier = Modifier
                                                .size(FDS.Sizer.Icon.icon24)
                                                .safeClickable {
                                                    onAction.invoke(DetailAction.ClickedOnSearch(""))
                                                },
                                        )
                                    }
                                } else {
                                    null
                                },
                            )

                            Icon(
                                painter = painterResource(id = R.drawable.ic_account_filter_24),
                                contentDescription = "Filter",
                                modifier = Modifier
                                    .size(FDS.Sizer.Icon.icon24)
                                    .safeClickable {
                                        onAction.invoke(DetailAction.ClickedOnFilter(true))
                                    },
                                tint = FDS.Colors.characterSecondary,
                            )
                        }
                    }

                    when {
                        detailUiState.isCallHistory && true == transactionHistory.asListHistory()
                            .isEmpty() -> {
                            item {
                                TabHistoryEmpty(isAdvanceQuery = detailUiState.queryType == AccountViewModel.ADVANCED_FILTER)
                            }
                        }

                        detailUiState.isCallHistory && true == transactionHistory.asListHistory()
                            .isNotEmpty() -> {
                            transactionHistory.asListHistory()
                                .forEach { (groupKey, accountLst, _) ->
                                    item(key = "header_$groupKey") {
                                        FoundationText(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .background(FDS.Colors.backgroundBgContainer)
                                                .padding(horizontal = FDS.Sizer.Gap.gap24),
                                            text = groupKey,
                                            style = FDS.Typography.captionCaptionL,
                                            color = FDS.Colors.gray500,
                                        )
                                    }

                                    itemsIndexed(
                                        accountLst,
                                        key = { index, account -> "body_${groupKey}_$index" },
                                    ) { index, item ->
                                        ItemTransaction(item = item, onAction = onAction)

                                        if (index == accountLst.lastIndex) {
                                            FoundationDivider(
                                                modifier = Modifier
                                                    .background(FDS.Colors.backgroundBgContainer)
                                                    .padding(
                                                        vertical = FDS.Sizer.Gap.gap16,
                                                        horizontal = FDS.Sizer.Gap.gap24,
                                                    ),
                                            )
                                        }
                                    }
                                }

                            if (detailUiState.isLoadingMore) {
                                item {
                                    Box(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .background(FDS.Colors.backgroundBgContainer)
                                            .padding(FDS.Sizer.Gap.gap16),
                                        contentAlignment = Alignment.Center,
                                    ) {
                                        CircularProgressIndicator(
                                            modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                                            color = FDS.Colors.blue900,
                                            strokeWidth = FDS.Sizer.Stroke.stroke2,
                                        )
                                    }
                                }
                            }
                        }

                        else -> {
                            // chua co thong tin gi hien thi
                        }
                    }
                }

                else -> {
                    item {
                        TabInfo(displayList = detailUiState.infoLst)
                    }
                }
            }
        }
    }
}

@Composable
fun ItemTransaction(
    modifier: Modifier = Modifier,
    item: HistoryDomain? = null,
    onAction: (DetailAction) -> Unit,
) {
    println("tuna5 recompose body ${item?.tranDate ?: "haha"}")
    val isDebit = true == item?.amount?.contains("+")
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(FDS.Colors.backgroundBgContainer)
            .padding(
                start = FDS.Sizer.Gap.gap24,
                top = FDS.Sizer.Gap.gap12,
                end = FDS.Sizer.Gap.gap24,
            )
            .safeClickable { onAction.invoke(DetailAction.ClickedOnDetailHistory(item)) },
    ) {
        Image(
            painter = painterResource(
                if (isDebit) {
                    com.vietinbank.core_ui.R.drawable.ic_account_in_20
                } else {
                    com.vietinbank.core_ui.R.drawable.ic_account_out_20
                },
            ),
            contentDescription = null,
        )

        Column(
            modifier = Modifier
                .padding(horizontal = FDS.Sizer.Gap.gap8)
                .weight(1f),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
        ) {
            FoundationText(
                text = item?.remark ?: "",
                color = FDS.Colors.textPrimary,
                style = FDS.Typography.bodyB2Emphasized,
                maxLines = 3,
            )

            FoundationText(
                text = item?.tranDate ?: "",
                color = FDS.Colors.gray500,
                style = FDS.Typography.captionCaptionL,
            )
        }
        FoundationText(
            text = Utils.g().getDotMoneyHasCcy(
                item?.amount?.getAmountServer() ?: "",
                item?.currency ?: "",
            ),
            color = if (isDebit) {
                FDS.Colors.green700
            } else {
                FDS.Colors.red600
            },
            style = FDS.Typography.bodyB2Emphasized,
        )
    }
}

@Composable
fun TabHistoryEmpty(
    modifier: Modifier = Modifier,
    isAdvanceQuery: Boolean,
) {
    Column(
        modifier = modifier
            .background(
                FDS.Colors.backgroundBgContainer,
                RoundedCornerShape(
                    topStart = FDS.Sizer.Radius.radius32,
                    topEnd = FDS.Sizer.Radius.radius32,
                ),
            )
            .padding(FDS.Sizer.Gap.gap24),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Image(
            painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_empty),
            contentDescription = null,
        )

        FoundationText(
            text = if (isAdvanceQuery) {
                stringResource(com.vietinbank.core_ui.R.string.account_detail_empty_advance)
            } else {
                stringResource(com.vietinbank.core_ui.R.string.account_detail_empty_default)
            },
            textAlign = TextAlign.Center,
            color = FDS.Colors.blue900,
        )
    }
}

@Composable
fun TabInfo(
    modifier: Modifier = Modifier,
    displayList: List<Any>?,
) {
    if (displayList.isNullOrEmpty()) return
    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(
                FDS.Colors.backgroundBgContainer,
                RoundedCornerShape(
                    topStart = FDS.Sizer.Radius.radius32,
                    topEnd = FDS.Sizer.Radius.radius32,
                ),
            )
            .padding(FDS.Sizer.Gap.gap24),
    ) {
        displayList.forEachIndexed { index, item ->
            if (item is DisplayModelUI) {
                if (index != 0) {
                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
                }
                FoundationInfoHorizontal(
                    title = item.title ?: "",
                    value = item.value ?: "",
                )
            }
        }
    }
}
