package com.vietinbank.feature_account.view

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_ui.components.FoundationChips
import com.vietinbank.core_ui.components.FoundationSelector
import com.vietinbank.core_ui.components.SelectorType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_account.R
import com.vietinbank.feature_account.fragment.BackgroundUI
import com.vietinbank.feature_account.fragment.QRAccountViewModel

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun EditQRStyleAndBackgroundView(
    styleQR: String?,
    backgroundQR: BackgroundUI?,
    imageList: List<BackgroundUI>,
    onStyleChange: (String) -> Unit,
    onBackgroundChange: (BackgroundUI) -> Unit,
    onLocalClick: () -> Unit,
    onClickAbove: () -> Unit,
    onClickInside: () -> Unit,
    onClickBelow: () -> Unit,
    avatarQR: String?,
    isShowAvatar: Boolean = false,
    onToggleAvatar: (Boolean) -> Unit = {},
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        FoundationText(
            stringResource(R.string.account_qr_edit_location),
            style = FoundationDesignSystem.Typography.captionCaptionL,
            color = FoundationDesignSystem.Colors.gray600,
        )

        Spacer(Modifier.weight(1f))

        FoundationSelector(
            boxType = SelectorType.Switch,
            title = "",
            isSelected = isShowAvatar,
            isEnable = true,
            onClick = {
                onToggleAvatar(!isShowAvatar)
                if (!isShowAvatar && avatarQR != QRAccountViewModel.AVATAR_CENTER) {
                    onClickInside()
                }
            },
            modifier = Modifier.wrapContentHeight(),
        )
    }

    if (isShowAvatar) {
        Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap12))

        // Các nút chọn vị trí
        val options = remember(onClickAbove, onClickInside, onClickBelow) {
            listOf(
                Triple(QRAccountViewModel.AVATAR_TOP, R.string.account_qr_edit_on, onClickAbove),
                Triple(QRAccountViewModel.AVATAR_CENTER, R.string.account_qr_edit_in, onClickInside),
                Triple(QRAccountViewModel.AVATAR_BOTTOM, R.string.account_qr_edit_out, onClickBelow),
            )
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(FoundationDesignSystem.Sizer.Gap.gap12),
        ) {
            options.forEach { (id, labelRes, action) ->
                FoundationChips(
                    text = stringResource(labelRes),
                    subChipSize = 0,
                    isSelector = (avatarQR == id),
                    onClick = action,
                )
            }
        }
    }

    Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap24))

    FoundationText(
        text = stringResource(R.string.account_qr_edit_style),
        style = FoundationDesignSystem.Typography.captionCaptionL,
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = FoundationDesignSystem.Sizer.Gap.gap16),
    )

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = FoundationDesignSystem.Sizer.Gap.gap16),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        listOf(
            QRAccountViewModel.STYPE_1 to R.drawable.a_ic_qr_type_1,
            QRAccountViewModel.STYPE_2 to R.drawable.a_ic_qr_type_2,
            QRAccountViewModel.STYPE_3 to R.drawable.a_ic_qr_type_3,
        ).forEachIndexed { i, (style, resId) ->
            Box(
                modifier = Modifier
                    .weight(1f)
                    .height(FoundationDesignSystem.Sizer.Gap.gap100)
                    .background(
                        if (styleQR == style) FoundationDesignSystem.Colors.blue100 else FoundationDesignSystem.Colors.blue50,
                        RoundedCornerShape(
                            FoundationDesignSystem.Sizer.Radius.radius32,
                        ),
                    )
                    .border(
                        FoundationDesignSystem.Sizer.Gap.gap2,
                        if (styleQR == style) FoundationDesignSystem.Colors.stateActive else FoundationDesignSystem.Colors.blue50,
                        RoundedCornerShape(FoundationDesignSystem.Sizer.Radius.radius32),
                    )
                    .safeClickable { onStyleChange(style) },
                contentAlignment = Alignment.Center,
            ) {
                Image(
                    painter = painterResource(id = resId),
                    contentDescription = null,
                    modifier = Modifier
                        .width(FoundationDesignSystem.Sizer.Gap.gap40)
                        .aspectRatio(1f),
                )
            }

            if (i < 2) Spacer(modifier = Modifier.width(FoundationDesignSystem.Sizer.Gap.gap16))
        }
    }

    FoundationText(
        text = stringResource(R.string.account_qr_edit_background),
        style = FoundationDesignSystem.Typography.captionCaptionL,
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = FoundationDesignSystem.Sizer.Gap.gap16, bottom = FoundationDesignSystem.Sizer.Gap.gap16),
    )

    FlowRow(
        modifier = Modifier.fillMaxWidth(),
        maxItemsInEachRow = 3,
        verticalArrangement = Arrangement.spacedBy(FoundationDesignSystem.Sizer.Gap.gap8),
        horizontalArrangement = Arrangement.Absolute.SpaceBetween,
    ) {
        imageList.forEach { item ->
            Box(
                modifier = Modifier
                    .fillMaxWidth(0.3f)
                    .safeClickable { onBackgroundChange(item) },
                contentAlignment = Alignment.Center,
            ) {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(FoundationDesignSystem.Sizer.Gap.gap1),
                        contentAlignment = Alignment.Center,
                    ) {
                        item.image?.let {
                            Image(
                                bitmap = it.asImageBitmap(),
                                contentDescription = null,
                                modifier = Modifier.aspectRatio(1f)
                                    .border(
                                        FoundationDesignSystem.Sizer.Gap.gap1,
                                        if (backgroundQR?.id == item.id) FoundationDesignSystem.Colors.blue500 else FoundationDesignSystem.Colors.blue100,
                                        RoundedCornerShape(FoundationDesignSystem.Sizer.Radius.radius32),
                                    )
                                    .clip(RoundedCornerShape(FoundationDesignSystem.Sizer.Radius.radius32)),
                                contentScale = ContentScale.FillBounds,
                            )
                        }

                        if (backgroundQR?.id == item.id) {
                            Image(
                                painter = painterResource(id = R.drawable.a_ic_check),
                                contentDescription = "Selected background",
                            )
                        }
                    }

                    FoundationText(
                        modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap16),
                        text = item.title.orEmpty(),
                        style = FoundationDesignSystem.Typography.bodyB2,
                    )
                }
            }
        }

        // Add button
        Box(
            modifier = Modifier
                .fillMaxWidth(0.3f)
                .aspectRatio(1f)
                .border(
                    FoundationDesignSystem.Sizer.Gap.gap1,
                    FoundationDesignSystem.Colors.strokeDivider,
                    RoundedCornerShape(
                        FoundationDesignSystem.Sizer.Radius.radius32,
                    ),
                )
                .padding(FoundationDesignSystem.Sizer.Gap.gap1)
                .background(
                    FoundationDesignSystem.Colors.homeBackgroundIcon, // #EDF4F8
                    RoundedCornerShape(
                        FoundationDesignSystem.Sizer.Radius.radius32,
                    ),
                )
                .safeClickable {
                    onLocalClick()
                },
            contentAlignment = Alignment.Center,
        ) {
            Image(
                painter = painterResource(id = R.drawable.a_ic_add),
                contentDescription = "Add",
            )
        }

        // Spacer nếu cần căn chỉnh cuối dòng
        if ((imageList.size) % 3 == 1) {
            Spacer(modifier = Modifier.fillMaxWidth(0.3f))
        }
    }
}