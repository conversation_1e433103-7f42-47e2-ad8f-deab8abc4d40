package com.vietinbank.feature_account.view

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_common.extensions.dd_MM_yyyy
import com.vietinbank.core_common.extensions.getDateToFormat
import com.vietinbank.core_common.extensions.toTimeInMillis
import com.vietinbank.core_common.extensions.todayAsString
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_data.models.response.DisplayModelUI
import com.vietinbank.core_domain.models.account.AccountDetailDomain
import com.vietinbank.core_domain.models.account.HistoryDomain
import com.vietinbank.core_ui.base.compose.BaseInputText
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_account.AccountViewModel
import com.vietinbank.feature_account.R
import java.util.Locale

@Composable
fun AccountDetailView(
    modifier: Modifier = Modifier,
    viewModel: AccountViewModel,
    account: AccountDetailDomain? = null,
    aliasAccount: String? = "",
    onQrClick: ((AccountDetailDomain?) -> Unit)? = null,
    onAliasUpdateClick: ((String?) -> Unit)? = null,
) {
    Box(modifier = modifier) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(2.dp))
                .background(Color.White)
                .padding(horizontal = 12.dp, vertical = 8.dp),
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Text(
                    text = account?.accountNo ?: "123456",
                    style = getComposeFont(5, 16.sp, AppColors.blue02),
                )

                Spacer(
                    modifier = Modifier
                        .weight(1f)
                        .height(0.dp),
                )

                Text(
                    text = account?.currency ?: "VND",
                    style = getComposeFont(5, 10.sp, AppColors.blue02),
                    modifier = Modifier
                        .border(
                            1.dp,
                            AppColors.greyMist,
                            RoundedCornerShape(12.dp),
                        )
                        .padding(horizontal = 4.dp, vertical = 2.dp),
                )

                if (account?.currency == "VND") {
                    Image(
                        painter = painterResource(id = R.drawable.a_icon_qr),
                        contentDescription = "Qr pay",
                        modifier = Modifier.safeClickable { onQrClick?.invoke(account) },
                    )
                }
            }

            BaseInputText(
                modifier = Modifier.width(IntrinsicSize.Max),
                value = aliasAccount ?: "",
                placeholder = "Đặt tên",
                iconEnd = R.drawable.a_ic_pencil,
                onValueChange = {
//                    viewModel.onChangeAliasName(it)
                },
                onFocusChanged = { focus ->
                    if (!focus) {
                        onAliasUpdateClick?.invoke(aliasAccount)
                    }
                },
            )

            Row(modifier = Modifier.padding(top = 20.dp)) {
                Text(
                    text = "Số dư hiện tại",
                    style = getComposeFont(4, 14.sp, AppColors.grey09),
                    modifier = Modifier.weight(1f),
                )

                Text(
                    text = "Số dư khả dụng",
                    style = getComposeFont(4, 14.sp, AppColors.grey09),
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = 16.dp),
                )
            }

            Row(modifier = Modifier.padding(top = 4.dp)) {
                Text(
                    text = Utils.g().getDotMoney(account?.currentBalance ?: ""),
                    style = getComposeFont(2, 14.sp, AppColors.blue02),
                    modifier = Modifier.weight(1f),
                )

                Text(
                    text = Utils.g().getDotMoney(account?.availableBalance ?: ""),
                    style = getComposeFont(2, 14.sp, AppColors.blue02),
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = 16.dp),
                )
            }
        }
    }
}

@Preview
@Composable
fun AccountLoanDetailView(modifier: Modifier = Modifier, account: AccountDetailDomain? = null) {
    Box(modifier = modifier) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(2.dp))
                .background(Color.White)
                .padding(horizontal = 12.dp, vertical = 8.dp),
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
            ) {
                Text(
                    text = account?.accountNo ?: "123456",
                    style = getComposeFont(5, 16.sp, AppColors.blue02),
                )

                Text(
                    text = account?.currency ?: "VND",
                    style = getComposeFont(5, 10.sp, AppColors.blue02),
                    modifier = Modifier
                        .border(
                            1.dp,
                            AppColors.greyMist,
                            RoundedCornerShape(12.dp),
                        )
                        .padding(horizontal = 4.dp, vertical = 2.dp),
                )
            }

            Row(
                modifier = Modifier
                    .padding(top = 20.dp)
                    .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(16.dp),
            ) {
                Column(modifier = Modifier.weight(1f), horizontalAlignment = Alignment.Start) {
                    Text(
                        text = "Dư nợ hiện tại",
                        style = getComposeFont(4, 14.sp, AppColors.grey09),
                    )

                    Text(
                        text = Utils.g().getDotMoney(account?.totalCurAmt ?: ""),
                        style = getComposeFont(2, 14.sp, AppColors.blue02),
                        modifier = Modifier.padding(top = 4.dp),
                    )
                }

                Column(modifier = Modifier.weight(1f), horizontalAlignment = Alignment.Start) {
                    Text(
                        text = "Lãi cộng dồn",
                        style = getComposeFont(4, 14.sp, AppColors.grey09),
                    )

                    Text(
                        text = Utils.g().getDotMoney(account?.accruedInterest ?: ""),
                        style = getComposeFont(2, 14.sp, AppColors.blue02),
                        modifier = Modifier.padding(top = 4.dp),
                    )
                }
            }

            Row(
                modifier = Modifier
                    .padding(top = 20.dp)
                    .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(16.dp),
            ) {
                Column(modifier = Modifier.weight(1f), horizontalAlignment = Alignment.Start) {
                    Text(
                        text = "Ngày đến hạn tiếp theo",
                        style = getComposeFont(4, 14.sp, AppColors.grey09),
                    )

                    Text(
                        text = account?.nextRepaymentDate?.getDateToFormat() ?: "123456",
                        style = getComposeFont(2, 14.sp, AppColors.blue02),
                        modifier = Modifier.padding(top = 4.dp),
                    )
                }

                Column(modifier = Modifier.weight(1f), horizontalAlignment = Alignment.Start) {
                    Text(
                        text = "Ngày đáo hạn",
                        maxLines = 1,
                        style = getComposeFont(4, 14.sp, AppColors.grey09),
                    )

                    Text(
                        text = account?.maturityDate.getDateToFormat() ?: "123456",
                        style = getComposeFont(2, 14.sp, AppColors.blue02),
                        modifier = Modifier.padding(top = 4.dp),
                    )
                }
            }
        }
    }
}

@Preview
@Composable
fun AccountSavingDetailView(modifier: Modifier = Modifier, account: AccountDetailDomain? = null) {
    Box(modifier = modifier) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(2.dp))
                .background(Color.White)
                .padding(horizontal = 12.dp, vertical = 8.dp),
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
            ) {
                Text(
                    text = account?.accountNo ?: "123456",
                    style = getComposeFont(5, 16.sp, AppColors.blue02),
                )

                Text(
                    text = account?.currency ?: "VND",
                    style = getComposeFont(5, 10.sp, AppColors.blue02),
                    modifier = Modifier
                        .border(
                            1.dp,
                            AppColors.greyMist,
                            RoundedCornerShape(12.dp),
                        )
                        .padding(horizontal = 4.dp, vertical = 2.dp),
                )
            }
            val processTerm = try {
                (
                    todayAsString(dd_MM_yyyy).toTimeInMillis(dd_MM_yyyy) -
                        account?.openDate.toTimeInMillis(
                            dd_MM_yyyy,
                        )
                    ).div(
                    account?.maturityDate.toTimeInMillis(dd_MM_yyyy) - account?.openDate.toTimeInMillis(
                        dd_MM_yyyy,
                    ) * 1f,
                )
            } catch (_: Exception) {
                0f
            }
            Box(
                modifier = Modifier
                    .padding(top = 20.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .height(8.dp)
                    .fillMaxWidth()
                    .background(AppColors.grey200),
            ) {
                Spacer(
                    modifier = Modifier
                        .fillMaxWidth(processTerm)
                        .fillMaxHeight()
                        .background(AppColors.blue02),
                )
            }

            Row(
                modifier = Modifier
                    .padding(top = 4.dp)
                    .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Column(
                    modifier = Modifier.weight(1f),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    Text(
                        text = "Ngày mở",
                        style = getComposeFont(4, 14.sp, AppColors.grey09),
                    )

                    Text(
                        text = account?.openDate.getDateToFormat() ?: "123456",
                        style = getComposeFont(2, 14.sp, AppColors.blue02),
                        modifier = Modifier,
                    )
                }

                Spacer(
                    modifier = modifier
                        .height(40.dp)
                        .width(1.dp)
                        .background(AppColors.grey200),
                )

                Column(
                    modifier = Modifier.weight(1f),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    Text(
                        text = "Ngày đáo hạn",
                        style = getComposeFont(4, 14.sp, AppColors.grey09),
                    )

                    Text(
                        text = account?.maturityDate.getDateToFormat() ?: "123456",
                        style = getComposeFont(2, 14.sp, AppColors.blue02),
                        modifier = Modifier,
                    )
                }
            }

            Row(
                modifier = Modifier
                    .padding(top = 20.dp)
                    .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Column(
                    modifier = Modifier.weight(1f),
                    horizontalAlignment = Alignment.Start,
                ) {
                    Text(
                        maxLines = 1,
                        text = "Số tiền gốc",
                        style = getComposeFont(4, 14.sp, AppColors.grey09),
                    )

                    Text(
                        text = Utils.g().getDotMoney(account?.originalAmount ?: ""),
                        style = getComposeFont(2, 14.sp, AppColors.blue02),
                        modifier = Modifier.padding(top = 4.dp),
                    )
                }

                Column(
                    modifier = Modifier.weight(1f),
                    horizontalAlignment = Alignment.Start,
                ) {
                    Text(
                        maxLines = 1,
                        text = "Số dư khả dụng",
                        style = getComposeFont(4, 14.sp, AppColors.grey09),
                    )

                    Text(
                        text = Utils.g().getDotMoney(account?.availableBalance ?: ""),
                        style = getComposeFont(2, 14.sp, AppColors.blue02),
                        modifier = Modifier.padding(top = 4.dp),
                    )
                }

                Column(
                    modifier = Modifier.weight(1f),
                    horizontalAlignment = Alignment.Start,
                ) {
                    Text(
                        maxLines = 1,
                        text = "Lãi cộng dồn",
                        style = getComposeFont(4, 14.sp, AppColors.grey09),
                    )

                    Text(
                        text = Utils.g().getDotMoney(account?.accruedInterest ?: ""),
                        style = getComposeFont(2, 14.sp, AppColors.blue02),
                        modifier = Modifier.padding(top = 4.dp),
                    )
                }
            }

            Row(
                modifier = Modifier
                    .padding(top = 20.dp)
                    .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Column(
                    modifier = Modifier.weight(1f),
                    horizontalAlignment = Alignment.Start,
                ) {
                    Text(
                        text = "Kỳ hạn",
                        maxLines = 1,
                        style = getComposeFont(4, 14.sp, AppColors.grey09),
                    )

                    Text(
                        text = account?.interestTerm ?: "123456",
                        style = getComposeFont(2, 14.sp, AppColors.blue02),
                        modifier = Modifier.padding(top = 4.dp),
                    )
                }

                Column(
                    modifier = Modifier.weight(1f),
                    horizontalAlignment = Alignment.Start,
                ) {
                    Text(
                        text = "Lãi suất",
                        maxLines = 1,
                        style = getComposeFont(4, 14.sp, AppColors.grey09),
                    )

                    Text(
                        text = String.format(
                            Locale.getDefault(),
                            "%.2f%%/năm",
                            account?.interestRate?.toDoubleOrNull() ?: 0.0,
                        ),
                        style = getComposeFont(2, 14.sp, AppColors.blue02),
                        modifier = Modifier.padding(top = 4.dp),
                    )
                }

                Spacer(modifier = Modifier.weight(1f))
            }
        }
    }
}

@Preview
@Composable
fun ItemDetailView(modifier: Modifier = Modifier, item: HistoryDomain? = null) {
    Column(modifier = modifier) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 5.dp)
                .background(Color.White),
        ) {
            Text(
                text = item?.remark ?: "",
                style = getComposeFont(4, 14.sp, AppColors.grey09),
                modifier = Modifier.weight(1f),
                maxLines = 2,
            )

            Column(
                horizontalAlignment = Alignment.End,
                modifier = Modifier
                    .padding(start = 16.dp)
                    .weight(1f),
            ) {
                Text(
                    text = item?.tranDate ?: "",
                    style = getComposeFont(4, 14.sp, AppColors.grey09),
                )

                val formatInput = if (item?.amount?.contains("+") == true) "+" else ""

                Text(
                    modifier = Modifier.padding(top = 5.dp),
                    textAlign = TextAlign.End,
                    text = formatInput.plus(Utils.g().getDotMoneyHasCcy(item?.amount ?: "", item?.currency ?: "")),
                    style = getComposeFont(
                        2,
                        14.sp,
                        if (item?.amount?.contains("+") == true) {
                            AppColors.successGreen
                        } else if (true == item?.amount?.contains("-")) {
                            AppColors.primaryRed
                        } else {
                            AppColors.grey09
                        },
                    ),
                )
            }
        }

        Spacer(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 5.dp)
                .height(1.dp)
                .background(AppColors.lineColor),
        )
    }
}

@Preview
@Composable
fun ItemDisplayView(
    modifier: Modifier = Modifier,
    displayUI: DisplayModelUI? = null,
    onClick: ((String) -> Unit)? = null,
) {
    displayUI?.let {
        Box(modifier = modifier) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White)
                    .safeClickable {
                        it.clickDirect?.let { direct ->
                            onClick?.invoke(direct)
                        }
                    },
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                ) {
                    Text(
                        text = it.title ?: "123456",
                        style = getComposeFont(
                            it.titleFont,
                            it.titleSize,
                            it.titleColor ?: AppColors.grey08,
                        ),
                        modifier = Modifier.padding(end = 16.dp),
                    )

                    Spacer(modifier = Modifier.weight(1f))

                    Row(modifier = it.modifierValue) {
                        it.icon?.let { image ->
                            Image(
                                painter = painterResource(id = image),
                                contentDescription = null,
                                modifier = Modifier.padding(end = 4.dp),
                            )
                        }
                        Text(
                            text = it.value ?: "123456",
                            textAlign = TextAlign.End,
                            style = getComposeFont(
                                it.valueFont,
                                it.valueSize,
                                it.valueColor ?: AppColors.grey09,
                            ),
                        )
                    }
                }

                if (displayUI.isShowLine) {
                    Spacer(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(1.dp)
                            .background(AppColors.grey200),
                    )
                }
            }
        }
    }
}