package com.vietinbank.feature_account.view

import android.graphics.Bitmap
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.painter.BitmapPainter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.vietinbank.core_common.extensions.containsNotVietNam
import com.vietinbank.core_domain.models.account.AccountModelUI
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.foundation.textfield.FoundationFieldType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_account.R
import com.vietinbank.feature_account.di.AccountQRAction
import com.vietinbank.feature_account.fragment.BackgroundUI
import com.vietinbank.feature_account.fragment.QRAccountViewModel

@Composable
fun EditQRPreviewSectionView(
    backgroundQR: BackgroundUI?,
    backgroundImage: ImageBitmap,
    genQR: ImageBitmap?,
    avatarQR: String?,
    avatarBG: Bitmap?,
    isShowLogo: Boolean,
    onAvatarClick: () -> Unit,
) {
    val isCenter = avatarQR == QRAccountViewModel.AVATAR_CENTER
    val isTop = avatarQR == QRAccountViewModel.AVATAR_TOP
    val isBottom = avatarQR == QRAccountViewModel.AVATAR_BOTTOM

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                horizontal = FoundationDesignSystem.Sizer.Gap.gap24,
                vertical = FoundationDesignSystem.Sizer.Gap.gap16,
            ),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight() // Tự động co giãn theo nội dung
                .clip(RoundedCornerShape(FoundationDesignSystem.Sizer.Radius.radius32)),
        ) {
            val displayBackground = backgroundQR?.image?.asImageBitmap() ?: backgroundImage

            // Ảnh nền
            Image(
                bitmap = displayBackground,
                contentDescription = null,
                contentScale = ContentScale.Crop,
                modifier = Modifier.matchParentSize(),
            )

            // Toàn bộ nội dung (QR + logo + text) nằm trong Column
            Column(
                modifier = Modifier
                    .align(Alignment.Center)
                    .padding(
                        horizontal = FoundationDesignSystem.Sizer.Gap.gap16,
                        vertical = FoundationDesignSystem.Sizer.Gap.gap32,
                    ),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                // QRLogo nằm trên QR
                if (isShowLogo && isTop) {
                    QRLogo(
                        avatarBG = avatarBG,
                        onClick = onAvatarClick,
                    )
                    Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))
                }

                // QRCode và QRLogo ở giữa (chồng lên nhau)

                if (isShowLogo && isCenter) {
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier
                            .padding(
                                top = FoundationDesignSystem.Sizer.Gap.gap16,
                                bottom = FoundationDesignSystem.Sizer.Gap.gap16,
                            )
                            .height(FoundationDesignSystem.Sizer.Gap.gap144)
                            .aspectRatio(1f),
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.ic_qr_defaut),
                            contentDescription = "QR",
                            modifier = Modifier
                                .fillMaxSize(),
                        )
                        QRLogo(
                            avatarBG = avatarBG,
                            onClick = onAvatarClick,
                        )
                    }
                } else {
                    // QR không có logo ở giữa
                    Image(
                        painter = painterResource(id = R.drawable.ic_qr_defaut),
                        contentDescription = "QR",
                        modifier = Modifier
                            .padding(
                                top = FoundationDesignSystem.Sizer.Gap.gap16,
                                bottom = FoundationDesignSystem.Sizer.Gap.gap16,
                            )
                            .height(FoundationDesignSystem.Sizer.Gap.gap144)
                            .aspectRatio(1f),
                    )
                }

                // QRLogo dưới cùng
                if (isShowLogo && isBottom) {
                    Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))
                    QRLogo(
                        avatarBG = avatarBG,
                        onClick = onAvatarClick,
                    )
                }
            }
        }
    }
}

@Composable
fun QRLogo(
    avatarBG: Bitmap?,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .background(
                FoundationDesignSystem.Colors.transparent,
            )
            .height(FoundationDesignSystem.Sizer.Gap.gap64)
            .aspectRatio(1f)
            .safeClickable { onClick() },
        contentAlignment = Alignment.Center,
    ) {
        Image(
            painter = avatarBG?.asImageBitmap()?.let { BitmapPainter(it) }
                ?: painterResource(id = R.drawable.ic_avatar),
            contentDescription = null,
            contentScale = ContentScale.Crop,
            modifier = Modifier
                .padding(FoundationDesignSystem.Sizer.Gap.gap1)
                .clip(RoundedCornerShape(FoundationDesignSystem.Sizer.Radius.radius20))
                .height(FoundationDesignSystem.Sizer.Gap.gap48)
                .aspectRatio(1f),
        )

        Image(
            painter = painterResource(id = R.drawable.ic_camera_qr),
            contentDescription = null,
            modifier = Modifier.align(Alignment.TopEnd)
                .width(FoundationDesignSystem.Sizer.Gap.gap32)
                .height(FoundationDesignSystem.Sizer.Gap.gap32),
        )
    }
}

@Composable
fun AccountBottomSheet(
    visible: Boolean,
    lstAccount: List<AccountModelUI>?,
    accountSelected: AccountModelUI?,
    onAccountAction: (AccountQRAction) -> Unit,
) {
    var searchText by remember { mutableStateOf("") }
    val searchLst by remember(lstAccount, searchText) {
        derivedStateOf {
            if (searchText.isEmpty()) {
                lstAccount ?: emptyList()
            } else {
                lstAccount?.filter { item ->
                    true == item.accountNo?.containsNotVietNam(searchText)
                } ?: emptyList()
            }
        }
    }

    BaseBottomSheet<AccountQRAction>(
        visible = visible,
        onDismissRequest = {
            searchText = ""
            onAccountAction.invoke(AccountQRAction.OnShowAccount(false))
        },
        onResult = { onAccountAction(it) },
        allowTouchDismiss = true,
        secureFlag = true,
    ) { onSelect ->
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(FoundationDesignSystem.Sizer.Radius.radius32))
                .background(FoundationDesignSystem.Colors.backgroundBgContainer)
                .padding(vertical = FoundationDesignSystem.Sizer.Gap.gap20),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            FoundationText(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(com.vietinbank.core_ui.R.string.account_inquiry_list_company),
                style = FoundationDesignSystem.Typography.headingH3,
                color = FoundationDesignSystem.Colors.characterHighlighted,
                textAlign = TextAlign.Center,
            )

            FoundationDivider(modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap24))

            if (!lstAccount.isNullOrEmpty()) {
                FoundationFieldType(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = FoundationDesignSystem.Sizer.Gap.gap24, bottom = FoundationDesignSystem.Sizer.Gap.gap8)
                        .padding(horizontal = FoundationDesignSystem.Sizer.Gap.gap24)
                        .clip(RoundedCornerShape(FoundationDesignSystem.Sizer.Radius.radius32))
                        .background(FoundationDesignSystem.Colors.gray50),
                    value = searchText,
                    onValueChange = { searchText = it },
                    placeholder = stringResource(com.vietinbank.core_ui.R.string.account_inquiry_not_dda_search_hint),
                    leadingIcon = {
                        Image(
                            painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_search),
                            contentDescription = "",
                        )
                    },
                    trailingIcon = {
                        if (searchText.isNotEmpty()) {
                            Icon(
                                painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_close),
                                contentDescription = "Clear",
                                tint = FoundationDesignSystem.Colors.characterSecondary,
                                modifier = Modifier
                                    .size(FoundationDesignSystem.Sizer.Icon.icon24)
                                    .safeClickable {
                                        searchText = ""
                                    },
                            )
                        }
                    },
                )
            }

            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = FoundationDesignSystem.Sizer.Gap.gap8)
                    .weight(1f, fill = false)
                    .heightIn(max = FoundationDesignSystem.Sizer.Dialog.maxListHeight),
                contentPadding = PaddingValues(horizontal = FoundationDesignSystem.Sizer.Padding.padding8),
                verticalArrangement = Arrangement.spacedBy(FoundationDesignSystem.Sizer.Gap.gap0),
            ) {
                itemsIndexed(searchLst) { index, item ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(
                                horizontal = FoundationDesignSystem.Sizer.Gap.gap24,
                                vertical = FoundationDesignSystem.Sizer.Gap.gap8,
                            )
                            .safeClickable(onSafeClick = {
                                onSelect(AccountQRAction.ClickedOnAccount(item))
                            }),
                        horizontalArrangement = Arrangement.spacedBy(
                            FoundationDesignSystem.Sizer.Spacing.spacingSmall,
                        ),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        Column(modifier = Modifier.weight(1f)) {
                            FoundationText(
                                text = (item.accountNo + " (" + item.currency + ")") ?: "",
                                color = FoundationDesignSystem.Colors.characterPrimary,
                                style = FoundationDesignSystem.Typography.captionCaptionL,
                            )

                            FoundationText(
                                modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap4),
                                text = item.accountName ?: item.aliasName ?: "",
                                color = FoundationDesignSystem.Colors.characterPrimary,
                                style = FoundationDesignSystem.Typography.bodyB2,
                            )
                        }

                        Image(
                            painter = painterResource(
                                if (accountSelected?.accountNo == item.accountNo) {
                                    com.vietinbank.core_ui.R.drawable.ic_common_radio_check_24
                                } else {
                                    com.vietinbank.core_ui.R.drawable.ic_common_radio_uncheck_24
                                },
                            ),
                            contentDescription = null,
                        )
                    }
                    FoundationDivider(
                        modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap4),
                    )
                }
            }
        }
    }
}