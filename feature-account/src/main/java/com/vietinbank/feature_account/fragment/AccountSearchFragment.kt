package com.vietinbank.feature_account.fragment

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.ClipboardHelper
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_account.AccountSearchViewModel
import com.vietinbank.feature_account.InquiryAction
import com.vietinbank.feature_account.ScreenAction
import com.vietinbank.feature_account.SearchAction
import com.vietinbank.feature_account.di.IAccountNavigator
import com.vietinbank.feature_account.screen.SearchScreen
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import kotlin.getValue

@AndroidEntryPoint
class AccountSearchFragment : BaseFragment<AccountSearchViewModel>() {
    override val viewModel: AccountSearchViewModel by viewModels()

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override val useCompose: Boolean = true

    @Inject
    lateinit var accountNavigator: IAccountNavigator

    @Inject
    lateinit var clipboardHelper: ClipboardHelper

    @Composable
    override fun ComposeScreen() {
        val searchUiState by viewModel.searchUiState.collectAsState()
        val renderAccount by viewModel.renderAccount.collectAsState()
        AppTheme {
            SearchScreen(searchUiState, renderAccount) { action ->
                when (action) {
                    // action screen
                    is ScreenAction.ClickedOnBack -> {
                        accountNavigator.popBackStack()
                    }

                    is InquiryAction.ClickedOnAccount -> {
                        accountNavigator.toDetailAccount(
                            Tags.TRANSACTION_BUNDLE to Utils.g().provideGson()
                                .toJson(action.account),
                        )
                    }

                    is SearchAction.ClickedOnSearch -> {
                        viewModel.onChangeSearch(action.inputText)
                    }

                    else -> {
                    }
                }
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.initState(arguments)
    }
}