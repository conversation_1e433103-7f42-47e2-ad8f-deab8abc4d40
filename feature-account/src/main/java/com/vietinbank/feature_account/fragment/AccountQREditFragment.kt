package com.vietinbank.feature_account.fragment

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.graphics.Bitmap
import android.os.Bundle
import android.provider.MediaStore
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.extensions.toBitmap
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.account.AccountSaveQR
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_account.bottomSheet.AvatarSelectionSheet
import com.vietinbank.feature_account.di.IAccountNavigator
import com.vietinbank.feature_account.screen.EditQRScreen
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

@AndroidEntryPoint
class AccountQREditFragment : BaseFragment<QRAccountViewModel>() {
    override val viewModel: QRAccountViewModel by viewModels()

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override val useCompose: Boolean = true

    @Inject
    lateinit var accountNavigator: IAccountNavigator

    private var selectionSheet: AvatarSelectionSheet? = null

    private val takePhotoEditLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                viewModel.onChangeBgAvatar(
                    result.data?.extras?.get(Tags.PHOTOEDIT_QR) as Bitmap?,
                )
            }
        }

    @Composable
    override fun ComposeScreen() {
        val genQR by viewModel.genQRState.collectAsState()

        AppTheme {
            EditQRScreen(
                genQR,
                viewModel::onAction,
                tabs = viewModel.inputLstQR,
                viewModel.lstImageQR,
                onAvatarListUpdate = { newList, selectedIndex, previewIndex ->
                    viewModel.onUpdateAvatarList(newList, selectedIndex, previewIndex)
                },
            )
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        try {
            viewModel.setAccountDefaultEdit(
                Utils.g().provideGson().fromJson(
                    arguments?.getString(Tags.DATA_TYPE),
                    AccountSaveQR::class.java,
                ),
            )
            viewModel.genQREditToDisplay()

            // list image
            viewModel.getImageQRAccount()

            // Gọi loadQR để lấy lại dữ liệu QR đã lưu nếu có
            viewModel.accountDetailModel?.accountNo?.let { accNo ->
                viewModel.loadQR(accNo)
            }
        } catch (e: Exception) {
            accountNavigator.popBackStack()
        }
        // chon anh tu bo nho
        requestPermissionGalleryListener { uri ->
            viewLifecycleOwner.lifecycleScope.launch(Dispatchers.IO) {
                val bmp = uri.toBitmap(context)
                withContext(Dispatchers.Main) {
                    bmp?.let {
                        if (viewModel.isAvatar.value) { // Nếu là chọn ảnh từ Avatar thì là onChangeBgAvatar
                            viewModel.onChangeBgAvatar(it)
                        } else { // Nếu là chọn ảnh từ Background thì là onChangeQRBackground
                            viewModel.onChangeQRBackground(BackgroundUI("", "", it))
                        }
                    } ?: run {
                        printLog("Không đọc được ảnh hoặc ảnh quá lớn")
                    }
                }
            }
        }

        requestAccessPermissionListener { allow ->
            if (allow) {
                openCamera()
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.apply {
                    launch {
                        accountQRState.collect {
                            accountNavigator.toQRAccount(saveQR = viewModel.getAccountQRDomain()?.let { Utils.g().provideGson().toJson(it) })
                        }
                    }
                }
            }
        }

        handleSingleEvent { event ->
            when (event) {
                is AccountQREvent.NavigateBack -> {
                    appNavigator.popBackStack()
                }

                is AccountQREvent.AvatarClick -> {
                    if (selectionSheet == null) {
                        selectionSheet = AvatarSelectionSheet()
                    }
                    selectionSheet?.setOnSelectionClick {
                        when (it) {
                            Tags.EDITQR_CAPTURE -> {
                                requestAccessPermission(Manifest.permission.CAMERA)
                            }

                            else -> {
                                // chọn từ thư viện cho Avatar
                                viewModel.pickAvatar()
                                openGallery()
                            }
                        }
                    }
                    selectionSheet?.show(
                        childFragmentManager,
                        AvatarSelectionSheet::class.java.name,
                    )
                }

                is AccountQREvent.LocalClick -> {
                    viewModel.pickBackground() // chọn từ thư viện cho Background()
                    openGallery()
                }
            }
        }
    }

    private fun openCamera() {
        Intent(MediaStore.ACTION_IMAGE_CAPTURE).also { takePictureIntent ->
            // Ensure that there's a camera activity to handle the intent
            takePictureIntent.resolveActivity(requireContext().packageManager)?.also {
                /**
                 * Truong hop can chinh sua anh -> lay link anh
                 val photoFile: File? = try {
                 createImageFile(requireContext())
                 } catch (ex: IOException) {
                 null
                 }
                 photoFile?.also {
                 val photoURI: Uri? = getUriForFile(requireContext(), it)
                 takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoURI)
                 takePictureIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                 takePictureIntent.addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION);
                 takePictureIntent.putExtra("return-data", true);
                 openCameraLauncher.launch(takePictureIntent)
                 }
                 */
                takePhotoEditLauncher.launch(takePictureIntent)
            }
        }
    }
}
