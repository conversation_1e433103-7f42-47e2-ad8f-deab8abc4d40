package com.vietinbank.feature_account.fragment

import android.content.Context
import android.graphics.Bitmap
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.asImageBitmap
import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.bitmapToBase64Result
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.extensions.toBitmap
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyCurrency
import com.vietinbank.core_domain.models.account.AccountDetailDomain
import com.vietinbank.core_domain.models.account.AccountImagesQRParams
import com.vietinbank.core_domain.models.account.AccountListEnterpriseParams
import com.vietinbank.core_domain.models.account.AccountLstParams
import com.vietinbank.core_domain.models.account.AccountModelUI
import com.vietinbank.core_domain.models.account.AccountSaveQR
import com.vietinbank.core_domain.models.account.AccountSaveQRDomain
import com.vietinbank.core_domain.models.account.AccountSaveQRParams
import com.vietinbank.core_domain.models.account.ListEnterpriseDomain
import com.vietinbank.core_domain.repository.cache.IQRCacheManager
import com.vietinbank.core_domain.usecase.account.AccountUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.OneTimeEvent
import com.vietinbank.feature_account.AccountViewModel.Companion.DDA
import com.vietinbank.feature_account.R
import com.vietinbank.feature_account.di.AccountQRAction
import com.vietinbank.feature_account.fragment.QRAccountViewModel.Companion.AVATAR_CENTER
import com.vietinbank.feature_account.fragment.QRAccountViewModel.Companion.STYPE_1
import com.vietinbank.feature_account.helper.QRHelper
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class QRAccountViewModel
@Inject constructor(
    private val accountUseCase: AccountUseCase,
    val qrHelper: QRHelper,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
    @ApplicationContext context: Context,
    val iQRCacheManager: IQRCacheManager,
) : BaseViewModel() {
    companion object {
        // AVATAR QR
        const val AVATAR_TOP = "1"
        const val AVATAR_CENTER = "2"
        const val AVATAR_BOTTOM = "3"

        // STYPE QR
        const val STYPE_1 = "1"
        const val STYPE_2 = "2"
        const val STYPE_3 = "3"

        // SO OPTION
        const val CONTENT = 0
        const val AVATAR = 1
        const val STYLE = 2

        const val MAX_LENGTH_CONTENT = 25
        const val RULE_COMPANY = "RULE_COMPANY"
        const val RULE_ACCOUNT = "RULE_ACCOUNT"
        const val DEFAULT_CURRENCY = "VND"
    }

    init {
        viewModelScope.launch { // Gọi trong ViewModel giúp kiểm soát thời điểm, an toàn lifecycle, tránh I/O trong constructor, và dễ test.
            iQRCacheManager.warmUp() // <— hydrate từ CustomEncryptedPrefs về RAM ngay khi VM khởi chạy
        }
    }

    private val _listEnterpriseDomain = MutableStateFlow<List<ListEnterpriseDomain>>(emptyList())
    val listEnterpriseDomain = _listEnterpriseDomain.asStateFlow()

    private val _listAccount = MutableStateFlow<List<AccountModelUI>>(emptyList())
    val listAccount = _listAccount.asStateFlow()

    // nếu là true = đang chọn Avatar, ngược lại false = đang chọn Background
    private val _isAvatar = MutableStateFlow(true)
    val isAvatar: StateFlow<Boolean> = _isAvatar.asStateFlow()

    fun pickAvatar() { _isAvatar.value = true } // Chọn mở ảnh từ Avatar
    fun pickBackground() { _isAvatar.value = false } // Chọn mở ảnh từ Background

    var accountSaveQR: AccountSaveQR? = null
    var accountSaveQRParams: AccountSaveQRParams? = null

    fun initIfNeeded(
        rawAccountJson: String?,
        rawEditJson: String?,
    ) {
        try {
            accountDetailModel = rawAccountJson?.let { Utils.g().provideGson().fromJson(it, AccountDetailDomain::class.java) }

            setAccountDefaultEdit(
                rawEditJson?.let { Utils.g().provideGson().fromJson(it, AccountSaveQR::class.java) },
            )

            val accNo = accountSaveQR?.accountNo ?: accountDetailModel?.accountNo
            if (accNo != null) {
                loadQR(accNo)
            }

            // Dùng cho lần đầu vào gen qr khi accNo = null
            genQRToDisplay(accountSaveQR?.amount, accountSaveQR?.notes)
        } catch (e: Exception) {
            // log nếu cần
        }
    }

    // StateFlow thay cho mutableStateOf
    private val _accountQRUiState = MutableStateFlow<AccountSaveQR?>(null)

    fun loadQR(accountNo: String, fallback: AccountSaveQR? = null) {
        _accountQRUiState.value = iQRCacheManager.getQR(accountNo) ?: fallback
        if (_accountQRUiState.value != null) {
            accountSaveQR = _accountQRUiState.value

            if (!isKeepCallInit()) {
                genQRToDisplay(accountSaveQR?.amount, accountSaveQR?.notes)
                setKeepCallInit()
            }
        }
    }

    fun saveQR(data: AccountSaveQR) {
        data.accountNo?.let {
            iQRCacheManager.saveQR(it, data)
            _accountQRUiState.value = data
        }
    }

    fun onAction(action: AccountQRAction) {
        when (action) {
            is AccountQRAction.OnBackPressed -> {
                sendEvent(AccountQREvent.NavigateBack)
            }

            is AccountQRAction.OnDownloadClick -> {
                sendEvent(AccountQREvent.DownloadClick)
            }

            is AccountQRAction.OnEditQRClick -> {
                sendEvent(AccountQREvent.EditQRClick)
            }

            is AccountQRAction.OnShareClick -> {
                sendEvent(AccountQREvent.ShareClick)
            }

            is AccountQRAction.OnCreateClick -> {
                setQRAccount(action.amount, action.content)
            }

            is AccountQRAction.OnLocalClick -> {
                sendEvent(AccountQREvent.LocalClick)
            }
            is AccountQRAction.OnAvatarClick -> {
                if (action.typeAvatar == 0) {
                    sendEvent(AccountQREvent.AvatarClick)
                } else {
                    sendEvent(AccountQREvent.AvatarClick)
                    onChangeBgAvatar(null)
                }
            }
            is AccountQRAction.ToggleAvatar -> {
                _genQRState.update { cur ->
                    cur?.copy(isShowAvatar = !(cur?.isShowAvatar ?: false))
                }
                val now = _genQRState.value?.isShowAvatar
                printLog("VM.ToggleAvatar -> now=$now")
            }

            is AccountQRAction.OnTabSelected -> {
                _genQRState.update { it?.copy(inputTabQR = action.inputTabQR) }
            }

            is AccountQRAction.OnPositionChange -> {
                _genQRState.update { it?.copy(avatarQR = action.qrPos) }
                accountSaveQR?.avatarQRPositon = action.qrPos
            }

            is AccountQRAction.OnStyleChange -> {
                _genQRState.update { it?.copy(styleQR = action.style) }
                accountSaveQR?.styleQR = action.style
            }

            is AccountQRAction.OnBackgroundChange -> {
                onChangeQRBackground(action.background)
            }

            is AccountQRAction.OnAccountClick -> {
                // Xét lại QR khi chọn từng item
                accountSaveQR = AccountSaveQR(
                    accountName = accountSaveQR?.accountName ?: accountDetailModel?.accountName,
                    accountNo = action.accountNo,
                    companyName = action.companyName,
                    amount = accountSaveQR?.amount ?: "",
                    branchId = accountSaveQR?.branchId ?: accountDetailModel?.branchId,
                    branchName = accountSaveQR?.branchName ?: accountDetailModel?.branchName,
                    currency = accountSaveQR?.currency ?: accountDetailModel?.currency,
                    notes = accountSaveQR?.notes ?: "",
                    qrData = qrHelper.generateQRData(
                        accountNumber = action.accountNo,
                        amount = accountSaveQR?.amount,
                        desc = accountSaveQR?.notes,
                    ),
                    username = userProf.getUserName(),
                    styleQR = accountSaveQR?.styleQR ?: "1",
                    avatarBG = accountSaveQR?.avatarBG,
                )

                accountSaveQR?.let { params ->
                    qrHelper.generateQRCodeImage(params.qrData ?: "", params.styleQR.toString())?.let {
                        onGenQR(
                            QRUIState(
                                params.companyName,
                                params.accountName,
                                params.accountNo,
                                params.branchName,
                                it,
                                params.amount,
                                params.notes,
                                params.styleQR.toString(),
                                params.avatarBG.toBitmap(),
                                params.backgroundQR.toBitmap(),
                                currency = params.currency,
                            ),
                        )
                    }
                }
            }

            is AccountQRAction.OnValueChangeAmount -> {
                _genQRState.update { it?.copy(amount = action.amount) }
            }

            is AccountQRAction.OnValueChangeContent -> {
                _genQRState.update { it?.copy(note = action.content) }
            }

            is AccountQRAction.OnShowAccount -> {
                if (action.isShowAccount) {
                    if (_listAccount.value.isEmpty()) {
                        getAccList(action.isShowAccount)
                    } else {
                        _genQRState.update {
                            it?.copy(
                                isShowAccount = true,
                                allAccount = _listAccount.value,
                                selectedAccount = _genQRState.value?.selectedAccount,
                            )
                        }
                    }
                } else {
                    _genQRState.update { it?.copy(isShowAccount = false) }
                }
            }

            is AccountQRAction.OnShowCompany -> {
                if (action.isShow) {
                    if (_listEnterpriseDomain.value.isEmpty()) {
                        getListEnterprise(action.isShow)
                    } else {
                        _genQRState.update {
                            it?.copy(
                                isShowCompany = true,
                                allCompany = _listEnterpriseDomain.value,
                                selectedCompany = _genQRState.value?.selectedCompany,
                            )
                        }
                    }
                } else {
                    _genQRState.update { it?.copy(isShowCompany = false) }
                }
            }

            is AccountQRAction.ClickedOnCompany -> {
                _genQRState.update {
                    it?.copy(
                        selectedCompany = action.companySelected,
                        companyName = action.companySelected?.enterprisename,
                    )
                }
            }

            is AccountQRAction.ClickedOnAccount -> {
                _genQRState.update {
                    it?.copy(
                        selectedAccount = action.accountSelected,
                        accountNo = action.accountSelected?.accountNo,
                    )
                }
            }
        }
    }

    var accountDetailModel: AccountDetailDomain? = null

    // tao qr
    private val _genQRState = MutableStateFlow<QRUIState?>(null)
    val genQRState: StateFlow<QRUIState?> = _genQRState.asStateFlow()

    fun onGenQR(bitmap: QRUIState?) {
        _genQRState.value = bitmap
    }

    fun genQRToDisplay(
        amount: String? = null,
        note: String? = null,
    ) {
        accountSaveQR = AccountSaveQR(
            accountName = accountDetailModel?.accountName ?: accountSaveQR?.accountName,
            companyName = accountDetailModel?.companyName ?: accountSaveQR?.companyName,
            accountNo = accountDetailModel?.accountNo ?: accountSaveQR?.accountNo,
            amount = amount ?: "",
            branchId = accountDetailModel?.branchId ?: accountSaveQR?.branchId,
            branchName = accountDetailModel?.branchName ?: accountSaveQR?.branchName,
            currency = accountDetailModel?.currency ?: accountSaveQR?.currency,
            notes = note ?: "",
            qrData = qrHelper.generateQRData(
                accountNumber = accountDetailModel?.accountNo,
                amount = amount,
                desc = note,
            ),
            username = userProf.getUserName(),
            styleQR = accountSaveQR?.styleQR ?: "1",
            avatarBG = accountSaveQR?.avatarBG,
        )

        accountSaveQR?.let { params ->
            qrHelper.generateQRCodeImage(params.qrData ?: "", params.styleQR.toString())?.let {
                onGenQR(
                    QRUIState(
                        params.companyName,
                        params.accountName,
                        params.accountNo,
                        params.branchName,
                        it,
                        params.amount,
                        params.notes,
                        params.styleQR.toString(),
                        params.avatarBG.toBitmap(),
                        params.backgroundQR.toBitmap(),
                        currency = params.currency,
                    ),
                )
            }
        }
    }

    fun genQREditToDisplay() {
        accountSaveQR?.let { params ->
            qrHelper.generateQRCodeImage(params.qrData ?: "", params.styleQR.toString())?.let {
                onGenQR(
                    QRUIState(
                        params.companyName,
                        params.accountName,
                        params.accountNo,
                        params.branchName,
                        it,
                        params.amount,
                        params.notes,
                        params.styleQR.toString(),
                        params.avatarBG.toBitmap(),
                        params.backgroundQR.toBitmap(),
                        isShowAvatar = if (params.avatarBG.toBitmap() != null) true else false,
                        currency = params.currency,
                    ),
                )
            }
        }
    }

    // tab focus
    val inputLstQR = listOf(
        context.getString(com.vietinbank.core_ui.R.string.maker_transfer_tab_saved_account),
        context.getString(R.string.account_qr_edit_tab),
    )

    fun onChangeBgAvatar(avatarBg: Bitmap?) {
        printLog("Bitmap received: ${avatarBg?.width}x${avatarBg?.height}")
        _genQRState.update { current ->
            val newBitmap = avatarBg?.asImageBitmap()
            val newList = if (newBitmap != null) current?.avatarBitmaps?.plus(newBitmap) else current?.avatarBitmaps
            current?.copy(
                avatarBG = avatarBg,
                avatarBitmaps = newList ?: emptyList(),
            )
        }
        accountSaveQR?.avatarBG = avatarBg
            ?.let { bitmapToBase64Result(it, 300, 300) }
            ?.onFailure { e -> printLog("Encode avatarBG failed:$e") }
            ?.getOrNull()
    }

    fun onChangeQRBackground(background: BackgroundUI?) {
        _genQRState.update { it?.copy(backgroundQR = background) }
        accountSaveQR?.backgroundQR = background?.image
            ?.let { bitmapToBase64Result(it, 400, 400) }
            ?.onFailure { e -> printLog("Encode avatarBG failed:$e") }
            ?.getOrNull()
    }

    fun onUpdateAvatarList(updatedList: List<ImageBitmap?>, selectedIndex: Int?, selectedPreviewIndex: Int?) {
        _genQRState.update {
            it?.copy(
                avatarBitmaps = updatedList,
            )
        }
    }

    // save qr
    private val _accountQRState = MutableSharedFlow<AccountSaveQRDomain>()
    val accountQRState: SharedFlow<AccountSaveQRDomain> = _accountQRState.asSharedFlow()

    fun setQRAccount(
        amount: String? = null,
        note: String? = null,
    ) = launchJobSilent {
        try {
            accountSaveQR?.amount = amount
            accountSaveQR?.notes = note

            accountSaveQRParams = AccountSaveQRParams(
                accountName = accountSaveQR?.accountName,
                companyName = accountSaveQR?.companyName,
                accountNo = accountSaveQR?.accountNo,
                amount = accountSaveQR?.amount ?: "",
                branchId = accountSaveQR?.branchId,
                branchName = accountSaveQR?.branchName,
                currency = accountSaveQR?.currency,
                notes = accountSaveQR?.notes ?: "",
                qrData = qrHelper.generateQRData(
                    accountNumber = accountSaveQR?.accountNo,
                    amount = accountSaveQR?.amount,
                    desc = accountSaveQR?.notes,
                ),
                username = userProf.getUserName(),
            )

            accountSaveQRParams?.let {
                val res = accountUseCase.setQRAccount(it)
                handleResourceSilent(
                    resource = res,
                    onSuccess = { data ->
                        _accountQRState.emit(data)
                        accountSaveQR?.let {
                            saveQR(it)
                        }
                    },
                )
            }
        } catch (e: Exception) {
            printLog(e.printStackTrace())
        }
    }

    fun setAccountDefaultEdit(model: AccountSaveQR?) {
        accountSaveQR = model
    }

    fun getAccountQRDomain() = accountSaveQR

    // list image qr
    val lstImageQR: MutableList<BackgroundUI> = mutableListOf()

    fun getImageQRAccount() = launchJobSilent {
        try {
            val params = AccountImagesQRParams(Tags.TYPE_QR_BACKGROUND, userProf.getUserName())
            val res = accountUseCase.getImageQRAccount(params)
            handleResourceSilent(
                resource = res,
                onSuccess = { data ->
                    data.ibImageList?.let { lst ->
                        lstImageQR.clear()
                        lstImageQR.addAll(
                            lst.map { item ->
                                BackgroundUI(
                                    item.id,
                                    item.title_vi,
                                    item.image?.toBitmap(),
                                )
                            },
                        )
                    }
                },
            )
        } catch (e: Exception) {
            printLog(e.printStackTrace())
        }
    }

    private fun getListEnterprise(isShow: Boolean) = launchJob {
        val res = accountUseCase.getListEnterprise(AccountListEnterpriseParams(userProf.getUserName()))
        handleResource(res) { data ->
            if (data.enterprises != null && data.enterprises?.size!! > 1) {
                _listEnterpriseDomain.emit(data.enterprises ?: emptyList())

                _genQRState.update {
                    it?.copy(
                        allCompany = data.enterprises,
                        isShowCompany = isShow,
                        selectedCompany = ListEnterpriseDomain(enterprisename = _genQRState.value?.companyName, enterpriseid = ""),
                    )
                }
            } else {
                _genQRState.update { it?.copy(isShowCompany = false) }
            }
        }
    }

    fun getAccList(isShow: Boolean) = launchJob {
        val params = AccountLstParams(
            serviceAcctType = DDA,
            username = userProf.getUserName() ?: "",
            isForceReq = "1",
            cifno = _genQRState.value?.selectedCompany?.enterpriseid,
        )
        val res = accountUseCase.getAccountList(params)
        handleResource(res) { data ->
            if (data.accounts != null && data.accounts?.size!! > 1) {
                val listAccount = data.accounts?.map { account ->
                    AccountModelUI(
                        cifNo = account.cifNo,
                        accountName = account.accountName,
                        accountNo = account.accountNo,
                        accountType = account.accountType,
                        accruedInterest = account.accruedInterest,
                        aliasName = account.aliasName,
                        availableBalance = account.availableBalance,
                        currency = account.currency,
                        interestRate = account.interestRate,
                        maturityDate = account.maturityDate,
                        term = account.term,
                        interestTerm = account.interestTerm,
                        cifName = account.cifName,
                        remainingLoanTotal = account.remainingLoanTotal,
                        nextRepaymentDate = account.nextPaymentDate,
                        currentBalance = account.currentBalance,
                        holdBalance = account.holdBalance,
                        branchName = account.branchName,
                    )
                }
                listAccount?.filter { it.currency == MoneyCurrency.VND.value && it.cifName == _genQRState.value?.companyName }
                    ?.sortedByDescending {
                        (it.availableBalance ?: "0").toDoubleOrNull()
                    }?.let { lst ->
                        if (lst.isNotEmpty()) {
                            _listAccount.emit(lst)

                            _genQRState.update {
                                it?.copy(
                                    allAccount = lst,
                                    isShowAccount = isShow,
                                    selectedAccount = AccountModelUI(accountNo = _genQRState.value?.accountNo),
                                )
                            }
                        }
                    }
            } else {
                _genQRState.update { it?.copy(isShowAccount = false) }
            }
        }
    }
}

data class QRUIState(
    var companyName: String? = null,
    var accountName: String? = null,
    var accountNo: String? = null,
    var branch: String? = null,
    var qr: Bitmap? = null,
    var amount: String? = null,
    var note: String? = null,
    var styleQR: String = STYPE_1,
    var avatarBG: Bitmap? = null,
    var backgroundQRImage: Bitmap? = null,
    var backgroundQR: BackgroundUI? = null,
    var isShowAvatar: Boolean = false,
    var inputTabQR: Int = 0,
    var avatarQR: String = AVATAR_CENTER,
    val avatarBitmaps: List<ImageBitmap?> = emptyList(),
    var currency: String? = null,
    var isShowCompany: Boolean = false,
    var allCompany: List<ListEnterpriseDomain>? = null,
    var selectedCompany: ListEnterpriseDomain? = null,
    var isShowAccount: Boolean = false,
    var allAccount: List<AccountModelUI>? = null,
    var selectedAccount: AccountModelUI? = null,
)

data class BackgroundUI(
    val id: String?,
    val title: String?,
    val image: Bitmap?,
)

sealed class AccountQREvent : OneTimeEvent {
    data object NavigateBack : AccountQREvent()
    data object DownloadClick : AccountQREvent()
    data object EditQRClick : AccountQREvent()
    data object ShareClick : AccountQREvent()
    data class CreateClick(val amount: String, val content: String) : AccountQREvent()
    data object LocalClick : AccountQREvent()
    data object AvatarClick : AccountQREvent()
    data object BackEditClick : AccountQREvent()
}
