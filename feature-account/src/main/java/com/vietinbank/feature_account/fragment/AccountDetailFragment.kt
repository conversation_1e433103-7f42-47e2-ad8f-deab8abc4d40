package com.vietinbank.feature_account.fragment

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.ClipboardHelper
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_data.models.response.AccountModelUI
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_account.AccountAction
import com.vietinbank.feature_account.AccountViewModel
import com.vietinbank.feature_account.DetailAction
import com.vietinbank.feature_account.ScreenAction
import com.vietinbank.feature_account.di.IAccountNavigator
import com.vietinbank.feature_account.screen.DetailScreenFinal
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class AccountDetailFragment : BaseFragment<AccountViewModel>() {
    override val viewModel: AccountViewModel by viewModels()

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override val useCompose: Boolean = true

    @Inject
    lateinit var accountNavigator: IAccountNavigator

    @Inject
    lateinit var clipboardHelper: ClipboardHelper

    @Composable
    override fun ComposeScreen() {
        AppTheme {
            DetailScreenFinal(viewModel) { action ->
                when (action) {
                    // action screen
                    is ScreenAction.OnLoadMore -> {
                        viewModel.loadMoreTransactions()
                    }

                    is ScreenAction.ClickedOnBack -> {
                        appNavigator.popBackStack()
                    }

                    is ScreenAction.ClickedOnTab -> {
                        viewModel.onChangeDetailSelected(action.tabIndex)
                    }

                    // action detail
                    is DetailAction.ClickedOnInfoLoan -> {
//                        if (infoLoanSheet == null) {
//                            infoLoanSheet = AccountInfoLoanSheet(
//                                viewModel.getDisplayLNInfoPayment(it),
//                            )
//                        }
//                        infoLoanSheet?.show(
//                            childFragmentManager,
//                            AccountInfoLoanSheet::class.java.name
//                        )
                    }

                    is DetailAction.ClickedOnDetailHistory -> {
                        accountNavigator.toDetailTransaction(
                            Tags.TRANSACTION_BUNDLE to Utils.g().provideGson()
                                .toJson(action.itemHistory),
                        )
                    }

                    is DetailAction.ClickedOnFilter -> {
                        viewModel.setShowFilter(action.isShow)
                    }

                    is DetailAction.ClickedOnSearch -> {
                        viewModel.onChangeSearch(action.search)
                    }

                    is DetailAction.OnChangeAlias -> {
                        viewModel.onChangeAliasName(action.aliasData, action.isApply)
                    }

                    is DetailAction.OnApplyFilter -> {
                        viewModel.getHistoryWithFilter(action.historyFilter)
                    }
                    // action account
                    is AccountAction.ClickedOnCopy -> {
                        clipboardHelper.copyToClipboard(action.accountNumber)
                    }

                    is AccountAction.ClickedOnQR -> {
                        accountNavigator.toQRAccount(
                            Tags.DATA_TYPE to Utils.g().provideGson().toJson(
                                viewModel.getAccountDetailDomain(),
                            ),
                        )
                    }

                    is AccountAction.ClickedOnTransfer -> {
                        showNoticeDialog(getString(com.vietinbank.core_ui.R.string.common_feature_development))
                    }

                    is AccountAction.ClickedOnBill -> {
                        showNoticeDialog(getString(com.vietinbank.core_ui.R.string.common_feature_development))
                    }

                    is AccountAction.ClickedOnSaving -> {
                        showNoticeDialog(getString(com.vietinbank.core_ui.R.string.common_feature_development))
                    }

                    else -> {
                    }
                }
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initObserve()
        try {
            viewModel.setAccountDefault(
                Utils.g().provideGson().fromJson(
                    arguments?.getString(Tags.TRANSACTION_BUNDLE),
                    AccountModelUI::class.java,
                ),
            )
            if (!viewModel.isKeepCallInit()) {
                viewModel.getAccountDetail()
                viewModel.getTransactionList()
                viewModel.setKeepCallInit()
            }
        } catch (e: Exception) {
            printLog("Lỗi parse transaction JSON: ${e.message}")
            e.printStackTrace()
            accountNavigator.popBackStack()
        }

        requestPermissionExternalStoreListener {
            if (it) {
                viewModel.exportFile()
            }
        }
    }

    private fun initObserve() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.apply {
                    launch {
                        aliasAccountState.collect { data ->
                            showNoticeDialog("Qúy khách đã cập nhật Alias thành công")
                        }
                    }

                    launch {
                        fileStatus.collect { data ->
                            if (data.fileName.isNullOrEmpty()) {
                                showNoticeDialog("Không tìm thấy file đính kèm hoặc file không khả dụng")
                            }
                        }
                    }

                    launch {
                        fileDownload.collect { data ->
                            showNoticeDialog("Tải xuống thành công")
                        }
                    }
                }
            }
        }
    }
}