package com.vietinbank.feature_account

import android.os.Bundle
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.getParcelableArrayListCustom
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_data.models.response.SearchUiState
import com.vietinbank.core_domain.models.account.AccountModelUI
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.feature_account.AccountViewModel.Companion.CD
import com.vietinbank.feature_account.AccountViewModel.Companion.DDA
import com.vietinbank.feature_account.AccountViewModel.Companion.LN
import com.vietinbank.feature_account.render.AccountRenderFactory
import com.vietinbank.feature_account.render.IRenderAccount
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

@HiltViewModel
class AccountSearchViewModel
@Inject constructor(
    private val accountFactory: AccountRenderFactory,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
) : BaseViewModel() {

    // new design
    private val _renderAccount = MutableStateFlow<IRenderAccount?>(null)
    val renderAccount = _renderAccount.asStateFlow()

    private val _searchUiState = MutableStateFlow<SearchUiState>(SearchUiState())
    val searchUiState = _searchUiState.asStateFlow()
    fun initState(bundle: Bundle?) {
        val typeSearch = bundle?.getString(Tags.ACCOUNT_INQUIRY, DDA)
        _renderAccount.value = accountFactory.render(typeSearch)
        _searchUiState.value = _searchUiState.value.copy(
            inquiryType = typeSearch,
            inquiryName = when (typeSearch) {
                CD -> resourceProvider.getString(com.vietinbank.core_ui.R.string.account_inquiry_tab_saving)
                LN -> resourceProvider.getString(com.vietinbank.core_ui.R.string.account_inquiry_tab_loan)
                else -> resourceProvider.getString(com.vietinbank.core_ui.R.string.account_inquiry_tab_payment)
            }.lowercase(),
            accountLst = bundle?.getParcelableArrayListCustom<AccountModelUI>(Tags.TRANSACTION_BUNDLE),
        )
    }

    fun onChangeSearch(input: String?) {
        _searchUiState.value = _searchUiState.value.copy(searchText = input)
    }
}