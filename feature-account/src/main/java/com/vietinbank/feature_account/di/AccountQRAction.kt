package com.vietinbank.feature_account.di

import com.vietinbank.core_domain.models.account.AccountModelUI
import com.vietinbank.core_domain.models.account.ListEnterpriseDomain
import com.vietinbank.feature_account.fragment.BackgroundUI

sealed interface AccountQRAction {
    data object OnBackPressed : AccountQRAction
    data object OnDownloadClick : AccountQRAction
    data object OnEditQRClick : AccountQRAction
    data object OnShareClick : AccountQRAction
    data class OnCreateClick(val amount: String, val content: String) : AccountQRAction
    data object OnLocalClick : AccountQRAction
    data class OnAvatarClick(val typeAvatar: Int) : AccountQRAction
    data class OnTabSelected(val inputTabQR: Int) : AccountQRAction
    data class OnPositionChange(val qrPos: String) : AccountQRAction
    data class OnStyleChange(val style: String) : AccountQRAction
    data class OnValueChangeAmount(val amount: String) : AccountQRAction
    data class OnValueChangeContent(val content: String) : AccountQRAction
    data class OnBackgroundChange(val background: BackgroundUI) : AccountQRAction
    data class OnAccountClick(val accountNo: String, val companyName: String) : AccountQRAction
    data class ToggleAvatar(val enabled: Boolean) : AccountQRAction
    data class OnShowAccount(val isShowAccount: Boolean) : AccountQRAction
    data class OnShowCompany(val isShow: Boolean) : AccountQRAction
    data class ClickedOnCompany(val companySelected: ListEnterpriseDomain?) : AccountQRAction
    data class ClickedOnAccount(val accountSelected: AccountModelUI?) : AccountQRAction
}