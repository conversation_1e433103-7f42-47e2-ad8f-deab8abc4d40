package com.vietinbank.feature_account

import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.snapshotFlow
import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.dd_MM_yyyy_HH_mm_ss_1
import com.vietinbank.core_common.extensions.downloadFile
import com.vietinbank.core_common.extensions.getDateToFormat
import com.vietinbank.core_common.extensions.getDayAgo
import com.vietinbank.core_common.extensions.todayAsString
import com.vietinbank.core_common.models.ItemResult
import com.vietinbank.core_common.models.TransferResult
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyCurrency
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_data.models.response.AliasUiState
import com.vietinbank.core_data.models.response.DetailUiState
import com.vietinbank.core_data.models.response.DisplayModelUI
import com.vietinbank.core_data.models.response.HistoryUiState
import com.vietinbank.core_domain.models.account.AccountDetailDomain
import com.vietinbank.core_domain.models.account.AccountDetailParams
import com.vietinbank.core_domain.models.account.AccountFileSavingDomain
import com.vietinbank.core_domain.models.account.AccountFileSavingParams
import com.vietinbank.core_domain.models.account.AccountFilterHistory
import com.vietinbank.core_domain.models.account.AccountHistoryDetailParams
import com.vietinbank.core_domain.models.account.AccountHistoryListParams
import com.vietinbank.core_domain.models.account.AccountModelUI
import com.vietinbank.core_domain.models.account.AliasAccountParams
import com.vietinbank.core_domain.models.account.HistoryDomain
import com.vietinbank.core_domain.models.account.SetAliasAccountDomain
import com.vietinbank.core_domain.models.account.StatusDescriptionUI
import com.vietinbank.core_domain.models.account.TransactionStatusParams
import com.vietinbank.core_domain.models.maker.DataBankDomain
import com.vietinbank.core_domain.models.maker.NapasBankListParams
import com.vietinbank.core_domain.repository.cache.ITransferCacheManager
import com.vietinbank.core_domain.usecase.account.AccountUseCase
import com.vietinbank.core_domain.usecase.transfer.TransferUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.feature_account.AccountViewModel.Companion.D
import com.vietinbank.feature_account.AccountViewModel.Companion.DAY_31
import com.vietinbank.feature_account.AccountViewModel.Companion.DDA
import com.vietinbank.feature_account.AccountViewModel.Companion.L
import com.vietinbank.feature_account.AccountViewModel.Companion.LN
import com.vietinbank.feature_account.render.AccountRenderFactory
import com.vietinbank.feature_account.render.IRenderAccount
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch
import java.io.File
import javax.inject.Inject

@HiltViewModel
class AccountDetailViewModel
@Inject constructor(
    private val accountUseCase: AccountUseCase,
    private val transferUseCase: TransferUseCase,
    val moneyHelper: MoneyHelper,
    private val transferCacheManager: ITransferCacheManager,
    private val accountFactory: AccountRenderFactory,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
) : BaseViewModel() {

    companion object {
        // type transaction
        const val TRANSACTION_ALL = "Debit&Credit" // amount out
        const val TRANSACTION_DEBIT = "Debit" // amount out
        const val TRANSACTION_CREDIT = "Credit" // amount in

        const val TAB_HISTORY = 0
        const val TAB_INFO = 1

        const val DRO = "DRO"
        const val RTGS = "RTGS"
        const val ADVANCED_FILTER = "ADVANCED"
        const val NORMAL_FILTER = "NORMAL"
        const val PAGE_SIZE = "30"
        const val COPY_TRANSACTION_NO = "COPY_TRANSACTION_NO"
    }

    private val _detailUiState = MutableStateFlow<DetailUiState>(
        DetailUiState(isChecker = userProf.isChecker()),
    )

    val detailUiState = _detailUiState.asStateFlow()

    fun onChangeDetailSelected(index: Int) {
        _detailUiState.value = _detailUiState.value.copy(tabSelected = index)
    }

    fun onChangeAliasName(aliasEdit: String?) {
        _detailUiState.value.alias?.copy(
            aliasNameChange = aliasEdit,
        )?.let { _detailUiState.value = _detailUiState.value.copy(alias = it) }
    }

    fun setAccountDefault(account: AccountModelUI?) {
        if (!isKeepCallInit()) {
            _detailUiState.value = _detailUiState.value.copy(
                accountDefault = account,
                filterHistory = AccountFilterHistory(
                    queryType = NORMAL_FILTER,
                    typeTransaction = TRANSACTION_ALL,
                    typeDate = AccountViewModel.DAY_OPTION,
                    startDate = when (account?.accountType) {
                        in listOf(D, DDA) -> todayAsString()
                        else -> getDayAgo(dayAgo = DAY_31.toInt() - 1)
                    },
                    endDate = todayAsString(),
                    emptyMessage = when (account?.accountType) {
                        in listOf(
                            D,
                            DDA,
                        ),
                        -> resourceProvider.getString(com.vietinbank.core_ui.R.string.account_detail_empty_default)

                        else -> resourceProvider.getString(com.vietinbank.core_ui.R.string.account_detail_empty_default_31_day)
                    },
                ),
            )
            getAccountDetail()
            getTransactionList()
            setKeepCallInit()
        }
    }

    private var accountDetailModel: AccountDetailDomain? = null

    fun getAccountDetailDomain() = accountDetailModel

    fun getAccountDetail() = launchJob {
        _detailUiState.value.accountDefault?.let {
            val params = AccountDetailParams(
                it.accountNo,
                it.accountType,
                it.currency,
                userProf.getUserName(),
            )
            val res = accountUseCase.getAccountDetail(params)
            handleResource(res) { data ->
                accountDetailModel = data
                // thong tin tai khoan
                _detailUiState.value = _detailUiState.value.copy(
                    account = data,
                    infoLst = getDisplayDetailAccount(data),
                    alias = AliasUiState(
                        aliasName = data.aliasName,
                        aliasNameChange = data.aliasName,
                    ),
                    isCreateQR = data.currency == MoneyCurrency.VND.name && data.accountType in listOf(DDA, D),
                )
                _renderAccount.value = accountFactory.render(it.accountType)
            }
        }
    }

    fun getTransactionList() = launchJob {
        val params = AccountHistoryListParams(
            userName = userProf.getUserName(),
            accountNo = _detailUiState.value.accountDefault?.accountNo,
            accountType = _detailUiState.value.accountDefault?.accountType,
            currency = _detailUiState.value.accountDefault?.currency,
            pageIndex = _detailUiState.value.currentPage,
            pageSize = PAGE_SIZE,
            fromDate = _detailUiState.value.filterHistory?.startDate ?: "",
            toDate = _detailUiState.value.filterHistory?.endDate ?: "",
            dorcD = if (true == _detailUiState.value.filterHistory?.typeTransaction?.contains(
                    TRANSACTION_DEBIT,
                )
            ) {
                TRANSACTION_DEBIT
            } else {
                ""
            },
            dorcC = if (true == _detailUiState.value.filterHistory?.typeTransaction?.contains(
                    TRANSACTION_CREDIT,
                )
            ) {
                TRANSACTION_CREDIT
            } else {
                ""
            },
            fromAmount = _detailUiState.value.filterHistory?.startAmount ?: "",
            toAmount = _detailUiState.value.filterHistory?.endAmount ?: "",
            queryType = _detailUiState.value.filterHistory?.queryType,
        )
        val res = accountUseCase.getTransactionList(params)
        handleResource(res) { data ->
            // them senderName && senderAccount && isViewCheck
            data.transactions?.forEach {
                it.isViewCheck = it.pmtType == DRO || it.pmtType == RTGS
                it.senderName = data.sender
                it.senderAccount = _detailUiState.value.accountDefault?.accountNo
                it.amountInWord =
                    moneyHelper.convertAmountToWords(it.amount ?: "", it.currency ?: "")
                it.timeDate = it.tranDate.getDateToFormat(dd_MM_yyyy_HH_mm_ss_1)
            }
            _detailUiState.value = _detailUiState.value.copy(
                canLoadMore = (data.transactions?.size ?: -1) >= PAGE_SIZE.toInt(),
                currentPage = data.nextPage ?: "0",
                isCallHistory = true,
                historyOriginLst = data.transactions ?: mutableListOf(),
            )
        }
    }

    fun getTransactionDetail(
        historyDomain: HistoryDomain? = null,
    ) = launchJob {
        val params = AccountHistoryDetailParams(
            historyDomain?.senderAccount,
            historyDomain?.dorc,
            historyDomain?.pmtType,
            historyDomain?.trxRefNo,
            historyDomain?.trxId,
            "",
            userProf.getUserName(),
        )
        val res = accountUseCase.getTransactionDetail(params)
        handleResource(res) { data ->
//            _historyDetailState.value = data.transaction
//            _lstDetailTransactionState.emit(
//                getDisplayDetailTransaction(sender, historyDomain, data.transaction),
//            )
        }
    }

    private val lstDetailDisplay = mutableListOf<DisplayModelUI>()

    private fun getDisplayDetailAccount(account: AccountDetailDomain?): List<DisplayModelUI> {
        lstDetailDisplay.clear()
        if (account?.accountType in listOf(LN, L)) {
            lstDetailDisplay.add(
                DisplayModelUI(
                    title = resourceProvider.getString(
                        com.vietinbank.core_ui.R.string.account_detail_loan_next_payment,
                        account?.accountNo ?: "",
                        account?.nextRepaymentDate ?: "",
                    ),
                    titleColor = resourceProvider.getComposeColor(
                        com.vietinbank.core_ui.R.color.foundation_state_warning,
                    ),
                ),
            )
        }
        account?.data?.forEach { item ->
            lstDetailDisplay.add(DisplayModelUI(title = item.key, value = item.value))
        }

        return lstDetailDisplay
    }

    // dat ten tai khoan
    private val _aliasAccountState = MutableSharedFlow<SetAliasAccountDomain>()
    val aliasAccountState: SharedFlow<SetAliasAccountDomain> = _aliasAccountState.asSharedFlow()
    fun setAliasAccount() {
        _detailUiState.value.alias?.let {
            if (it.aliasName == it.aliasNameChange) {
                return
            }
            launchJob {
                val res = accountUseCase.setAliasAccount(
                    AliasAccountParams(
                        accountNo = accountDetailModel?.accountNo,
                        aliasName = it.aliasNameChange ?: "",
                        aliasNameGroups = "",
                        cifSubsidiary = _detailUiState.value.accountDefault?.cifNo,
                        username = userProf.getUserName(),
                    ),
                )
                handleResource(res) { data ->
                    _detailUiState.value = _detailUiState.value.copy(
                        alias = AliasUiState(
                            aliasName = _detailUiState.value.alias?.aliasNameChange ?: "",
                            aliasNameChange = _detailUiState.value.alias?.aliasNameChange ?: "",
                        ),
                    )
                    _aliasAccountState.emit(data)
                }
            }
        }
    }

    private val _fileState = MutableSharedFlow<AccountFileSavingDomain>()
    val fileStatus = _fileState.asSharedFlow()

    private val _fileDownload = MutableSharedFlow<File>()
    val fileDownload = _fileDownload.asSharedFlow()

    fun exportFile() = launchJob {
        val params = AccountFileSavingParams(
            accountNo = _detailUiState.value.accountDefault?.accountNo,
            currency = _detailUiState.value.accountDefault?.currency,
            username = userProf.getUserName(),
        )
        val res = accountUseCase.exportFile(params)
        handleResource(res) { data ->
            _fileState.emit(data)
            if (!data.fileName.isNullOrEmpty()) {
                viewModelScope.launch(Dispatchers.IO) {
                    _fileDownload.emit(downloadFile(data.fileName!!, data.file))
                }
            }
        }
    }

    // new design
    private val _renderAccount = MutableStateFlow<IRenderAccount?>(null)
    val renderAccount = _renderAccount.asStateFlow()

    // xử lý load more trong lich su giao dich
    val lazyDetailState = LazyListState()
    fun observeListScroll() {
        viewModelScope.launch {
            snapshotFlow {
                val layoutInfo = lazyDetailState.layoutInfo
                val totalItems = layoutInfo.totalItemsCount
                val lastVisible = layoutInfo.visibleItemsInfo.lastOrNull()?.index ?: 0
                lastVisible >= totalItems - 3 && _detailUiState.value.filterText.isNullOrEmpty() && _detailUiState.value.canLoadMore && !_detailUiState.value.isLoadingMore && _detailUiState.value.tabSelected == TAB_HISTORY
            }.distinctUntilChanged().collect { shouldLoad ->
                if (shouldLoad) {
                    loadMoreTransactions()
                }
            }
        }
    }

    // loadmore lich su giao dich
    private fun loadMoreTransactions() {
        if (_detailUiState.value.isLoadingMore || !_detailUiState.value.canLoadMore) return
        _detailUiState.value = _detailUiState.value.copy(isLoadingMore = true)
        launchJob(showLoading = false) {
            try {
                val params = AccountHistoryListParams(
                    userName = userProf.getUserName(),
                    accountNo = _detailUiState.value.accountDefault?.accountNo,
                    accountType = _detailUiState.value.accountDefault?.accountType,
                    currency = _detailUiState.value.accountDefault?.currency,
                    pageIndex = _detailUiState.value.currentPage,
                    pageSize = PAGE_SIZE,
                    fromDate = _detailUiState.value.filterHistory?.startDate ?: "",
                    toDate = _detailUiState.value.filterHistory?.endDate ?: "",
                    dorcD = if (true == _detailUiState.value.filterHistory?.typeTransaction?.contains(
                            TRANSACTION_DEBIT,
                        )
                    ) {
                        TRANSACTION_DEBIT
                    } else {
                        ""
                    },
                    dorcC = if (true == _detailUiState.value.filterHistory?.typeTransaction?.contains(
                            TRANSACTION_CREDIT,
                        )
                    ) {
                        TRANSACTION_CREDIT
                    } else {
                        ""
                    },
                    fromAmount = _detailUiState.value.filterHistory?.startAmount ?: "",
                    toAmount = _detailUiState.value.filterHistory?.endAmount ?: "",
                    queryType = _detailUiState.value.filterHistory?.queryType,
                )

                val res = accountUseCase.getTransactionList(params)
                handleResource(res) { data ->
                    val currentLst =
                        _detailUiState.value.historyOriginLst?.toMutableList() ?: mutableListOf()
                    currentLst.addAll(data.transactions ?: mutableListOf())
                    _detailUiState.value = _detailUiState.value.copy(
                        canLoadMore = (data.transactions?.size ?: -1) >= PAGE_SIZE.toInt(),
                        currentPage = data.nextPage ?: "0",
                        historyOriginLst = currentLst,
                    )
                    // group theo ngay
                    // neu dang search => khong them vào map
                    // confirm Sếp, BA chỉ search data trên local không loadmore
//                    if (!_detailUiState.value.filterText.isNullOrEmpty()) {
//                        handleSearchHistory()
//                    }
                }
            } finally {
                _detailUiState.value = _detailUiState.value.copy(isLoadingMore = false)
            }
        }
    }

    // key-in search
    fun onChangeSearch(input: String?) {
        _detailUiState.value = _detailUiState.value.copy(filterText = input)
    }

    // lich su giao dich voi dieu kien filter moi
    fun getHistoryWithFilter(advanceFilter: AccountFilterHistory?) {
        _detailUiState.value = _detailUiState.value.copy(
            isShowFilter = false,
            filterHistory = advanceFilter?.copy(
                queryType = ADVANCED_FILTER,
                emptyMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.account_detail_empty_advance),
            ),
            currentPage = "0",
        )
        getTransactionList()
    }

    fun setShowFilter(isShow: Boolean) {
        // reset ve trang thai filter da chon truoc do
        // neu khong co thi hien thi default
        _detailUiState.value = _detailUiState.value.copy(
            isShowFilter = isShow,
        )
    }

    // lich su giao dich
    private val _historyState = MutableStateFlow<HistoryUiState>(HistoryUiState())
    val historyState = _historyState.asStateFlow()
    fun setHistoryDetail(domain: HistoryDomain?) {
        val contentLst = listOf(
            ItemResult(
                resourceProvider.getString(com.vietinbank.core_ui.R.string.account_transaction_no),
                domain?.trxRefNo ?: "",
                COPY_TRANSACTION_NO,
                R.drawable.ic_account_copy_24,
            ),
            ItemResult(
                resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_content),
                domain?.remark ?: "",
            ),
            ItemResult(
                resourceProvider.getString(com.vietinbank.core_ui.R.string.common_balance),
                Utils.g().getDotMoneyHasCcy(
                    domain?.balance ?: "",
                    domain?.currency ?: "",
                ),
            ),
            ItemResult(
                resourceProvider.getString(com.vietinbank.core_ui.R.string.common_date),
                domain?.tranDate ?: "",
            ),
            ItemResult(
                resourceProvider.getString(com.vietinbank.core_ui.R.string.account_detail_history_channel),
                domain?.channel ?: "",
            ),
        )
        _historyState.value = _historyState.value.copy(
            historyModel = domain,
            displayLst = contentLst,
        )
        getBankList(domain)
    }

    fun onChangeTab(tabIndex: Int) {
        _historyState.value = _historyState.value.copy(tabSelected = tabIndex)
        if (tabIndex == 1 && _historyState.value.statusForTransaction == null) {
            getTransactionStatus(_historyState.value.historyModel?.trxId)
        }
    }

    // trạng thái giao dịch
    private fun getTransactionStatus(refNo: String? = null) = launchJob {
        val res = accountUseCase.getTransactionStatus(
            TransactionStatusParams(refNo, userProf.getUserName()),
        )
        handleResource(res) { data ->
            val statusUILst = data.data?.tranStatusDetailList?.map { item ->
                StatusDescriptionUI(
                    position = item.statusCode,
                    positionName = item.statusDesc,
                    positionView = item.statusView,
                )
            }?.sortedByDescending { item -> item.position } ?: emptyList()

            try {
                val successColor = listOf(
                    resourceProvider.getResColor(com.vietinbank.core_ui.R.color.foundation_state_success_lighter),
                    resourceProvider.getResColor(com.vietinbank.core_ui.R.color.foundation_state_success_lighter),
                )

                val pendingColor = listOf(
                    resourceProvider.getResColor(com.vietinbank.core_ui.R.color.foundation_state_success_lighter),
                    resourceProvider.getResColor(com.vietinbank.core_ui.R.color.foundation_state_error_lighter),
                )

                val failColor = listOf(
                    resourceProvider.getResColor(com.vietinbank.core_ui.R.color.foundation_state_error_lighter),
                    resourceProvider.getResColor(com.vietinbank.core_ui.R.color.foundation_state_error_lighter),
                )
                when {
                    statusUILst[0].positionView.isNullOrEmpty() && statusUILst[1].positionView.isNullOrEmpty() -> {
                        // thanh cong ca 3
                        statusUILst[0].statusLine = successColor
                        statusUILst[1].statusLine = successColor
                        statusUILst[2].statusLine = successColor
                    }

                    statusUILst[0].positionView.isNullOrEmpty() -> {
                        // 1, 2 thanh cong
                        statusUILst[0].statusLine = successColor
                        statusUILst[1].statusLine = successColor
                        // 3 that bai
                        statusUILst[2].isSuccess = false
                        statusUILst[2].statusLine = pendingColor
                    }

                    else -> {
                        // 1 thanh cong
                        statusUILst[0].statusLine = pendingColor
                        // 2, 3 that bai
                        statusUILst[1].isSuccess = false
                        statusUILst[1].statusLine = failColor
                        statusUILst[2].isSuccess = false
                        statusUILst[2].statusLine = failColor
                    }
                }
            } catch (_: Exception) {
            }
            _historyState.value = _historyState.value.copy(
                statusForTransaction = data,
                statusAccounting = statusUILst,
            )
        }
    }

    private fun getBankList(historyModel: HistoryDomain?) {
        if (transferCacheManager.getBankList()?.isNotEmpty() == true) {
            getBankCache(historyModel, transferCacheManager.getBankList())
            return
        }
        launchJob {
            val res = transferUseCase.getNapasBankList(
                NapasBankListParams(
                    username = userProf.getUserName() ?: "",
                    cifno = userProf.getCifNo() ?: "",
                ),
            )
            handleResource(res) { data ->
                transferCacheManager.saveBankList(data.dataBanks)
                getBankCache(historyModel, data.dataBanks)
            }
        }
    }

    private fun getBankCache(historyModel: HistoryDomain?, bankLst: List<DataBankDomain>? = null) {
        val bankFrom = bankLst?.firstOrNull { bank ->
            (
                bank.type.equals(
                    Tags.TransferType.TYPE_IN, true,
                )
                ) || bank.ebankCode == historyModel?.branchId || bank.binCode == historyModel?.branchId
        }?.icon ?: ""

        val bankTo = bankLst?.firstOrNull { bank ->
            (
                bank.type.equals(
                    Tags.TransferType.TYPE_IN, true,
                )
                ) || bank.ebankCode == historyModel?.branchId || bank.binCode == historyModel?.branchId
        }?.icon ?: ""

        _historyState.value = _historyState.value.copy(
            accountFrom = TransferResult(
                bankIconURL = bankFrom,
                accountName = historyModel?.senderName ?: "",
                accountNo = historyModel?.senderAccount ?: "",
            ),
            accountTo = TransferResult(
                bankIconURL = bankTo,
                accountName = historyModel?.corresponsiveName ?: "",
                accountNo = "${historyModel?.beneficiaryBankName ?: ""} - ${historyModel?.corresponsiveAccount ?: ""}",
            ),
        )
    }

    fun isCanViewHistory(): Boolean =
        _detailUiState.value.accountDefault?.accountType in listOf(DDA, D)
}