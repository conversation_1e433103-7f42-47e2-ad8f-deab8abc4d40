<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="33dp"
    android:height="32dp"
    android:viewportWidth="33"
    android:viewportHeight="32">
  <path
      android:pathData="M16.5,0.5C25.06,0.5 32,7.44 32,16C32,24.56 25.06,31.5 16.5,31.5C7.94,31.5 1,24.56 1,16C1,7.44 7.94,0.5 16.5,0.5Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="0.5"
          android:startY="0"
          android:endX="32.5"
          android:endY="32"
          android:type="linear">
        <item android:offset="0" android:color="#FF72CEFF"/>
        <item android:offset="1" android:color="#FFF5FCFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:strokeWidth="1"
      android:pathData="M16.5,0.5C25.06,0.5 32,7.44 32,16C32,24.56 25.06,31.5 16.5,31.5C7.94,31.5 1,24.56 1,16C1,7.44 7.94,0.5 16.5,0.5Z"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="0.5"
          android:startY="0"
          android:endX="32.5"
          android:endY="32"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#FF8FD9FF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M11.833,12.667H12.5C12.854,12.667 13.193,12.526 13.443,12.276C13.693,12.026 13.833,11.687 13.833,11.333C13.833,11.156 13.904,10.987 14.029,10.862C14.154,10.737 14.323,10.667 14.5,10.667H18.5C18.677,10.667 18.846,10.737 18.971,10.862C19.096,10.987 19.167,11.156 19.167,11.333C19.167,11.687 19.307,12.026 19.557,12.276C19.807,12.526 20.146,12.667 20.5,12.667H21.167C21.52,12.667 21.859,12.807 22.11,13.057C22.36,13.307 22.5,13.646 22.5,14V20C22.5,20.354 22.36,20.693 22.11,20.943C21.859,21.193 21.52,21.333 21.167,21.333H11.833C11.48,21.333 11.141,21.193 10.891,20.943C10.641,20.693 10.5,20.354 10.5,20V14C10.5,13.646 10.641,13.307 10.891,13.057C11.141,12.807 11.48,12.667 11.833,12.667Z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#002E51"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M14.5,16.667C14.5,17.197 14.711,17.706 15.086,18.081C15.461,18.456 15.97,18.667 16.5,18.667C17.03,18.667 17.539,18.456 17.914,18.081C18.289,17.706 18.5,17.197 18.5,16.667C18.5,16.136 18.289,15.627 17.914,15.252C17.539,14.877 17.03,14.667 16.5,14.667C15.97,14.667 15.461,14.877 15.086,15.252C14.711,15.627 14.5,16.136 14.5,16.667Z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#002E51"
      android:strokeLineCap="round"/>
</vector>
