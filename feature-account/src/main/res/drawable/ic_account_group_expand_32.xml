<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="32dp"
    android:height="32dp"
    android:viewportWidth="32"
    android:viewportHeight="32">
  <path
      android:pathData="M16,0.5C24.56,0.5 31.5,7.44 31.5,16C31.5,24.56 24.56,31.5 16,31.5C7.44,31.5 0.5,24.56 0.5,16C0.5,7.44 7.44,0.5 16,0.5Z"
      android:fillAlpha="0.1">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="16"
          android:centerY="32"
          android:gradientRadius="24"
          android:type="radial">
        <item android:offset="0" android:color="#FF2EB2F7"/>
        <item android:offset="1" android:color="#0CFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:strokeWidth="1"
      android:pathData="M16,0.5C24.56,0.5 31.5,7.44 31.5,16C31.5,24.56 24.56,31.5 16,31.5C7.44,31.5 0.5,24.56 0.5,16C0.5,7.44 7.44,0.5 16,0.5Z"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="0"
          android:startY="0"
          android:endX="32"
          android:endY="32"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#FF8FD9FF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M12,14L16,18L20,14"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#0F4C7A"
      android:strokeLineCap="round"/>
</vector>
