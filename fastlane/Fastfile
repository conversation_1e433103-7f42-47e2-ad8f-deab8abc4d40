# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:android)

platform :android do
  before_all do
    check_branch_validity
    load_properties_file
  end

  desc "Build UAT and deploy to Firebase"
  lane :deploy_uat do
    branch = current_branch

    # UAT chỉ từ developing
    unless ["developing", "develop"].include?(branch)
      UI.error("⚠️  WARNING: UAT should be built from 'developing' branch!")
      UI.error("Current branch: #{branch}")

      if UI.confirm("Do you really want to deploy UAT from '#{branch}'?")
        UI.important("Proceeding with deployment from non-standard branch...")
      else
        UI.user_error!("Deployment cancelled. Please switch to 'developing' branch.")
      end
    end

    # Append build info vào notes.txt TRƯỚC khi build
    append_build_info_to_notes(branch: branch, flavor: "UAT")

    # Build
    gradle(task: "clean assembleUatRelease")

    # Deploy với notes.txt đã được update
    # Sử dụng đường dẫn tuyệt đối để đảm bảo Firebase tìm thấy file
    notes_file_path = File.expand_path("../notes.txt", Dir.pwd)
    UI.message("📄 Using release notes from: #{notes_file_path}")

    firebase_app_distribution(
      app: @properties["firebase.app.id.uat"],
      groups: @properties["firebase.groups.uat"] || "eFast",
      release_notes_file: notes_file_path,
      apk_path: "./app/build/outputs/apk/uat/release/app-uat-release.apk",
      service_credentials_file: @properties["firebase.service.account"]
    )

    UI.success("UAT deployed from branch: #{branch}")
  end

  desc "Build PPE and deploy to Firebase"
  lane :deploy_ppe do
    branch = current_branch
    valid_branches = ["developing", "develop", "staging"]
    is_release_branch = branch.start_with?("release/")

    unless valid_branches.include?(branch) || is_release_branch
      UI.error("⚠️  WARNING: PPE should be built from developing or release/* branches!")
      UI.error("Current branch: #{branch}")

      unless UI.confirm("Continue anyway?")
        UI.user_error!("Deployment cancelled.")
      end
    end

    # Append build info vào notes.txt
    append_build_info_to_notes(branch: branch, flavor: "PPE")

    # Build
    gradle(task: "clean assemblePpeRelease")

    # Deploy với đường dẫn tuyệt đối
    notes_file_path = File.expand_path("../notes.txt", Dir.pwd)
    UI.message("📄 Using release notes from: #{notes_file_path}")

    firebase_app_distribution(
      app: @properties["firebase.app.id.ppe"],
      groups: @properties["firebase.groups.ppe"] || "eFast",
      release_notes_file: notes_file_path,
      apk_path: "./app/build/outputs/apk/ppe/release/app-ppe-release.apk",
      service_credentials_file: @properties["firebase.service.account"]
    )

    UI.success("PPE deployed from branch: #{branch}")
  end

  desc "Build Production - RELEASE BRANCH ONLY"
  lane :build_production do
    branch = current_branch

    # Production từ release/* branches
    unless branch.start_with?("release/")
      UI.user_error!("🚫 Production builds MUST be from release/* branch! Current: #{branch}")
    end

    UI.important("Building PRODUCTION from #{branch}")

    # Extra confirmation for production
    if UI.confirm("Are you sure you want to build PRODUCTION from '#{branch}'?")
      gradle(task: "clean bundleLiveRelease")
      UI.success("Production AAB built successfully from #{branch}!")
    else
      UI.user_error!("Production build cancelled.")
    end
  end

  desc "Build UAT only"
  lane :build_uat do
    gradle(task: "clean assembleUatRelease")
    UI.success("UAT APK built successfully!")
  end

  desc "Build PPE only"
  lane :build_ppe do
    gradle(task: "clean assemblePpeRelease")
    UI.success("PPE APK built successfully!")
  end

  desc "Run tests"
  lane :test do
    gradle(task: "test")
  end

  desc "Run ktlint"
  lane :lint do
    gradle(task: "ktlintCheck")
  end

  # Private helper methods
  private_lane :current_branch do
    branch = ""
    begin
      branch = sh("git rev-parse --abbrev-ref HEAD").strip
    rescue
      branch = "unknown"
    end
    branch
  end

  private_lane :check_branch_validity do
    branch = current_branch
    UI.message("📍 Current branch: #{branch}")

    # Check uncommitted changes
    begin
      has_changes = sh("git status --porcelain").strip.length > 0
      if has_changes
        UI.important("⚠️  You have uncommitted changes!")
        sh("git status --short")
      end
    rescue
      UI.message("Could not check git status")
    end
  end

  # APPEND build info vào cuối file notes.txt
  private_lane :append_build_info_to_notes do |options|
    branch = options[:branch]
    flavor = options[:flavor]

    # Sử dụng đường dẫn tuyệt đối đến file notes.txt ở root
    # Dir.pwd trong Fastlane context sẽ là thư mục fastlane/
    notes_path = File.expand_path("../../notes.txt", __FILE__)

    # Debug log
    UI.message("📝 Updating build info in: #{notes_path}")

    # Kiểm tra file tồn tại
    unless File.exist?(notes_path)
      UI.user_error!("❌ notes.txt not found at: #{notes_path}")
    end

    # Đọc nội dung hiện tại
    current_content = File.read(notes_path)

    # Xóa tất cả Build Information cũ (nếu có)
    # Tìm vị trí của Build Information đầu tiên
    build_info_start = current_content.index("━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n📱 Build Information")

    if build_info_start
      # Nếu đã có Build Information, chỉ giữ phần trước đó
      clean_content = current_content[0...build_info_start].rstrip
    else
      # Nếu chưa có, giữ nguyên nội dung
      clean_content = current_content.rstrip
    end

    # Build info mới (đã bỏ Commit, Author, Message, Built with theo yêu cầu)
    build_info = "\n\n"
    build_info += "━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
    build_info += "📱 Build Information\n"
    build_info += "━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
    build_info += "Build Type: #{flavor}\n"
    build_info += "Branch: #{branch}\n"
    build_info += "Date: #{Time.now.strftime('%Y-%m-%d')}\n"
    build_info += "Time: #{Time.now.strftime('%H:%M:%S')}\n"
    build_info += "━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"

    # Ghi lại với Build Information mới nhất
    File.write(notes_path, clean_content + build_info)

    UI.message("✅ Updated build info in notes.txt")
    UI.message("📋 File size: #{File.size(notes_path)} bytes")
  end

  private_lane :load_properties_file do
    properties_file = "../local.properties"

    if File.exist?(properties_file)
      @properties = {}
      File.foreach(properties_file) do |line|
        line.strip!
        next if line.empty? || line.start_with?('#')

        key, value = line.split('=', 2)
        @properties[key.strip] = value.strip if key && value
      end

      UI.success("Loaded properties from local.properties")
    else
      UI.user_error!("local.properties not found!")
    end
  end
end