package com.vietinbank.core_common.livedata

import androidx.annotation.MainThread
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import java.util.concurrent.atomic.AtomicBoolean

/**
 * * Created by vand<PERSON> on 26/2/25.
 * Custom LiveData chỉ emit dữ liệu 1 lần cho mỗi observer
 * Tr<PERSON>h việc bị gọi lại sau khi màn hình xoay hoặc tái tạo.
 */
class SingleLiveEvent<T> : MutableLiveData<T>() {

    private val pending = AtomicBoolean(false)

    @MainThread
    override fun setValue(value: T?) {
        pending.set(true)
        super.setValue(value)
    }

    /**
     * Cho SingleLiveEvent<Unit>
     */
    @MainThread
    fun call() {
        value = null
    }

    override fun observe(owner: LifecycleOwner, observer: Observer<in T>) {
        super.observe(owner) { t ->
            if (pending.compareAndSet(true, false)) {
                observer.onChanged(t)
            }
        }
    }
}
