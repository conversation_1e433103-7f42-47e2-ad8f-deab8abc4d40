package com.vietinbank.core_common.constants

import com.vietinbank.core_common.models.ContactType
import com.vietinbank.core_common.models.DataFillObject

/**
 * Created by vandz on 13/1/25.
 */
object Tags {
    // cache
    const val LIST_BANKS_CACHE = "LIST_ACCOUNT_CACHE"
    const val LIST_CONTACTS_CACHE = "LIST_CONTACTS_CACHE"
    const val LIST_TEMPS_CACHE = "LIST_TEMPS_CACHE"
    const val LIST_LATEST_CACHE = "LIST_LATEST_CACHE"
    const val LIST_ACCOUNTS_IN_CACHE = "LIST_ACCOUNTS_IN_CACHE"
    const val LIST_ACCOUNTS_OUT_CACHE = "LIST_ACCOUNTS_OUT_CACHE"
    const val LIST_ACCOUNTS_NP247_CACHE = "LIST_ACCOUNTS_NP247_CACHE"
    const val LIST_ACCOUNTS_CARD_CACHE = "LIST_ACCOUNTS_CARD_CACHE"
    const val LIST_ACCOUNTS_PM_CACHE = "LIST_ACCOUNTS_PM_CACHE"
    const val LIST_BRANCH_CACHE = "LIST_BRANCH_CACHE"
    const val LIST_REGISTER_OTT_CACHE = "LIST_BRANCH_CACHE"
    const val COUNT_TIME_SHOW_REGISTER_OTT_CACHE = "COUNT_TIME_SHOW_REGISTER_OTT_CACHE"
    const val USERNAME = "USERNAME"
    const val FULLNAME = "FULLNAME"
    const val LOGOUTTIME = "LOGOUTTIME"
    const val TIMECONFINGLIST = "TIMECONFINGLIST"
    const val CIFNO = "CIFNO"
    const val SESSIONID = "SESSIONID"
    const val CHECKEKYC = "CHECKEKYC"
    const val PHONE = "PHONE"
    const val FIRST_LOGIN_SIGNAL = "TMP"
    const val PASSWORD_EXPIRE = "881"
    const val NEW_PASSWORD_EXPIRE = "11"
    const val USER_MULTI_CIF = "88"
    const val FORCE_UPDATE = "100"
    const val LOGIN_AUTHENTICATION = "98"
    const val ACTIVE = "ACTIVE"
    const val USER_PROFILE = "USER_PROFILE"
    const val RECENT_USERS = "RECENT_USERS"
    const val CHECKER = "8"
    const val SOTP = "S"
    const val KEYPASS = "K"
    const val VNPT_SMARTCA = "smartca"
    const val REJECT = "REJECT"
    const val APPROVE = "APPROVE"
    const val UPDATE = "UPDATE"
    const val REGISTER_EKYC = "REGISTER_EKYC"
    const val REGISTER_EKYC_ID = "REGISTER_EKYC_ID"
    const val ACCEPTABLE_REGISTER_SOTP = "T"
    const val SMS_OTP = "0"
    const val EMAIL_OTP = "1"
    const val SESSION_EXPIRED = "99"
    const val FORCE_SESSION_EXPIRED = "991"
    const val UI_MODEL = "ui_model"
    const val UI_TRANSACTION = "transaction"
    const val UI_BATCH = "batch"
    const val CSAT_FUNC_ID_KEY = "eFASTMobileNew"
    const val OVER_DATE = "0"
    const val LIST_PAYMENT_ACC_NUMBER = "LIST_PAYMENT_ACC_NUMBER"
    const val LIST_IDENTITY_ACC_NUMBER = "LIST_IDENTITY_ACC_NUMBER"
    const val LIST_LOAN_ACC_NUMBER = "LIST_LOAN_ACC_NUMBER"
    const val ACC_NUMBER = "ACC_NUMBER"
    const val PREF_KEY_VERSION = "PREF_KEY_VERSION"
    const val PREF_KEY_SHOW_COUNT = "PREF_KEY_SHOW_COUNT"
    const val PREF_KEY_VERSION_UPDATE = "PREF_KEY_VERSION_UPDATE"
    const val PREF_KEY_SHOW_COUNT_UPDATE = "PREF_KEY_SHOW_COUNT_UPDATE"

    // bundle key
    const val ORIGINAL_TRANSACTION_BUNDLE = "ORIGINAL_TRANSACTION_BUNDLE"
    const val TRANSACTION_BUNDLE = "TRANSACTION_BUNDLE"
    const val IS_BATCH_FILE_MODE = "IS_BATCH_FILE_MODE"
    const val ACCOUNT_NUMBER_BUNDLE = "ACCOUNT_NUMBER_BUNDLE"
    const val SERVICE_ID_BUNDLE = "SERVICE_ID_BUNDLE"
    const val CURRENCY_BUNDLE = "CURRENCY_BUNDLE"
    const val PAYMENT_METHOD_BUNDLE = "PAYMENT_METHOD_BUNDLE"
    const val TOKEN_BUNDLE = "TOKEN_BUNDLE"
    const val STEP_BUNDLE = "STEP_BUNDLE"
    const val CHALLENGE_CODE_BUNDLE = "CHALLENGE_CODE_BUNDLE"
    const val TRANSACTION_ID_BUNDLE = "TRANSACTION_ID_BUNDLE"
    const val CONFIRM_TYPE_BUNDLE = "CONFIRM_TYPE_BUNDLE"
    const val SUCCESS_LIST_BUNDLE = "SUCCESS_LIST_BUNDLE"
    const val CONFIRM_RESPONSE_BUNDLE = "CONFIRM_RESPONSE_BUNDLE"
    const val ADDFIELD1_BUNDLE = "ADDFIELD1"
    const val TYPE_BUNDLE = "TYPE_BUNDLE"
    const val EFAST_ID_BUNDLE = "EFAST_ID_BUNDLE"
    const val USERNAME_BUNDLE = "USERNAME_BUNDLE"
    const val CIFNO_BUNDLE = "CIFNO_BUNDLE"
    const val DATA_TYPE = "DATA_TYPE"
    const val NEXT_APPROVER_BUNDLE = "NEXT_APPROVER_BUNDLE"

    const val TRAN_TYPE_BUNDLE = "TRAN_TYPE_BUNDLE"
    const val GROUP_TYPE_BUNDLE = "GROUP_TYPE_BUNDLE"
    const val SERVICE_TYPE_BUNDLE = "SERVICE_TYPE_BUNDLE"

    const val GEN_OTP_TINE = "GEN_OTP_TINE"
    const val MT_ID = "MT_ID"
    const val LIST_METHOD = "LIST_METHOD"
    const val SOFT_OTP = "Soft OTP"
    const val KEY_PASS = "Keypass"
    const val OTT_ROUTE_TAG = "OTT_ROUTE_TAG"
    const val OTT_ALERT_TYPE = "OTT_ALERT_TYPE"
    const val OTT_ACTION_TYPE = "OTT_ACTION_TYPE"
    const val NOTIFICATION_ROUTE_TAG = "NOTIFICATION_ALERT_TYPE"

    const val ACCOUNT_BALANCE_TYPE_CHANGE_SMS = "ACCOUNT_BALANCE_TYPE_CHANGE_SMS"
    const val LOAN_ACCOUNT_TYPE = "LOAN_ACCOUNT_TYPE"
    const val IDENTITY_ACCOUNT_TYPE_REG = "IDENTITY_ACCOUNT_TYPE_REG"

    const val SHOW_STATUS = "SHOW_STATUS"
    const val SHOW_LOAN = "SHOW_LOAN"
    const val TYPE_FILE = "TYPE_FILE"
    const val SS_30_DAY = **********
    const val SS_31_DAY = **********
    const val SS_90_DAY = **********

    const val LANGUAGE_VI = "vi"
    const val LANGUAGE_EN = "en"

    // quan ly giao dich
    const val REGEX_AWAITING_APPROVAL = "."
    const val STATUS_USER_REJECT = "01" // ktt/ctk tu choi
    const val STATUS_AWAITING_APPROVAL = "1.1" // dang cho phe duyet
    const val STATUS_BANK_WAITING = "04" // cho ngan hang xu ly
    const val STATUS_SUCCESS = "05" // thanh cong
    const val STATUS_BANK_REJECT = "06" // ngan hang tu choi
    const val STATUS_BANK_PROCESSING = "09" // ngan hang dang xu ly
    const val STATUS_ERROR = "11" // loi
    const val STATUS_DELETE = "13" // da xoa

    const val TYPE_GROUP_TRANSFER = "CT" // chuyen tien don
    const val TYPE_GROUP_TRANSFER_IN = "IN" // chuyen tien trong he thong
    const val TYPE_GROUP_TRANSFER_OUT = "OU" // chuyen tien ngoai he thong
    const val TYPE_GROUP_TRANSFER_NAPAS = "NP" // chuyen tien ngoai he thong
    const val TYPE_GROUP_TRANSFER_CTTF300 = "BA" // CTTF 300 (áp dụng với điện con)
    const val TYPE_GROUP_TRANSFER_CTTF5000 = "HU" // CTTF 5000 (áp dụng với điện con)
    const val TYPE_GROUP_LOOKUP = "TR" // tra soat
    const val TYPE_GROUP_PAYMENT = "PM" // lenh chi
    const val TYPE_GROUP_SALARY_AUTO = "SLO" // chi luong tu dong
    const val TYPE_GROUP_SALARY = "SL" // chi luong qua ngan hang + chi luong ngoai te
    const val TYPE_GROUP_SALARY_FOREIGN = "SX" //  chi luong ngoai te
    const val TYPE_DISBURSEMENT_ONLINE = "DO"
    const val TYPE_GUARANTEE = "O"
    const val TYPE_GROUP_INFRASTRUCTURE = "IF" // nộp thuế hạ tầng
    const val TYPE_GROUP_FILENSNN = "BTX" // nộp thuế theo file
    const val TYPE_GROUP_CUSTOMSINLAND = "TX" // nộp NSNN
    const val TYPE_GROUP_RESET_PASSWORD = "RP" // cap lai mk
    const val TYPE_GROUP_UNLOCK_USER = "UL" // mo khoa nguoi dung
    const val TYPE_GROUP_GUARANTEE = "GOR" // giai toa bl
    const val TYPE_GROUP_GUARANTEE_ISSUE = "GO" // phat hanh bl
    const val TYPE_GROUP_GUARANTEE_AMEND = "GOC" // sua doi bl

    // type Transfer
    const val COPY_TRANSFER_OBJECT_BUNDLE = "COPY_TRANSFER_OBJECT_BUNDLE"
    const val TRANSFER_OBJECT_BUNDLE = "TRANSFER_OBJECT_BUNDLE"
    const val MTID_STRING_BUNDLE = "MTID_STRING_BUNDLE"

    const val NEED_SYNC_KEYPASS = "91"
    const val NEED_SYNC_SOTP = "91"
    const val NEED_REACTIVE_SOTP = "96"

    // type force update
    const val STATUS_FORCE_UPDATE_F = "F" // F: force
    const val STATUS_FORCE_UPDATE_O = "O" // O: optional
    const val STATUS_FORCE_UPDATE_N = "N" // N: none
    const val CHECKER_TRANTYPE_CT = "pm,in,ou,np,sn"
    const val CHECKER_TRANTYPE_TX = "tx"
    const val CHECKER_TRANTYPE_HU = "hu"
    const val CHECKER_TRANTYPE_BA = "ba"
    const val CHECKER_TRANTYPE_SL = "sl"
    const val CHECKER_TRANTYPE_SLO = "slo"
    const val CHECKER_TRANTYPE_SX = "sx"
    const val CHECKER_TRANTYPE_TR = "tr"

    // type download
    const val DOWNLOAD_FILE_TYPE_BM = "1"
    const val DOWNLOAD_FILE_TYPE_CM = "2"
    const val DOWNLOAD_FILE_SIGN_TYPE_BM = "0"
    const val DOWNLOAD_FILE_SIGN_TYPE_CM = "1"
    const val DOWNLOAD_FILE_BM01 = "BM 01"
    const val DOWNLOAD_FILE_C102 = "C1-02"
    const val DOWNLOAD_FILE_C102CKS = "C102 CÓ CKS"

    const val TYPE_SAY_YES = "Y"
    const val TYPE_FLAG_YES = "1"
    const val TYPE_FLAG_NO = "0"

    object TransferType {
        val dataSetFillDataOUR =
            DataFillObject(
                id = "1",
                name = "Phí ngoài",
            )
        val dataSetFillDataBEN =
            DataFillObject(
                id = "0",
                name = "Phí trong",
            )

        //
        val dataSetFillDataTimeNow =
            DataFillObject(
                id = "0",
                name = "Chuyển ngay",
            )
        val dataSetFillDataTimeSchedule =
            DataFillObject(
                id = "1",
                name = "Đặt lịch",
            )
        const val TYPE_SN = "sn"
        const val TYPE_IN = "in"
        const val TYPE_00 = "in"
        const val TYPE_OUT = "ou"
        const val TYPE_NAPAS = "np"
        const val TYPE_NORMAL = "TYPE_NORMAL" // tự định nghĩa
        const val TYPE_NAPAS_ACCOUNT = "npa"
        const val TYPE_NAPAS_CARD = "npc"
        const val TYPE_PAYMENT_ORDER_TRANSFER = "pm"
        const val SERVICE_TYPE_TRANSFER_IN = "Pfromtoint"
        const val SERVICE_TYPE_TRANSFER_OUT = "Pfromtoext"
        const val SERVICE_TYPE_TRANSFER_NAPAS_CARD = "NapasTransfer"
        const val TYPE_SPLIT_TRANSFER_YES = "Y"

        val contactTypeIn =
            ContactType(
                id = TYPE_IN,
                type = "Trong Vietinbank",
                isSelected = true,
                isEnable = true,
            )
        val contactTypeNPA =
            ContactType(
                id = TYPE_NAPAS_ACCOUNT,
                type = "Liên ngân hàng",
                isSelected = false,
                isEnable = true,
            )
        val contactTypeNPC =
            ContactType(
                id = TYPE_NAPAS_CARD,
                type = "Liên ngân hàng qua thẻ",
                isSelected = false,
                isEnable = true,
            )
        val listTypeContact: MutableList<ContactType> =
            mutableListOf(contactTypeIn, contactTypeNPA, contactTypeNPC)
        const val SERVICE_TYPE_PAYORD = "Payord"
        const val TYPE_ACCOUNT = "TYPE_ACCOUNT"
        const val TYPE_ACCOUNT_SPLIT = "TYPE_ACCOUNT_SPLIT"
        const val TYPE_TRACE_PAYMENT = "TYPE_TRACE_PAYMENT"
        const val TYPE_CARD = "TYPE_CARD"
        const val TYPE_PAYMENT_ORDER = "TYPE_PAYMENT_ORDER"
        const val TYPE_TRANSFER = "TYPE_TRANSFER"
        const val IS_CONTACT_CREATE = "IS_CONTACT_CREATE"

        const val LIST_CONFIRM_OBJECT = "LIST_CONFIRM_OBJECT"
        const val CONFIRM_OBJECT = "CONFIRM_OBJECT"
        const val DETAIL_OBJECT = "DETAIL_OBJECT"
        const val DETAIL_OBJECT_CONFIRM = "DETAIL_OBJECT_CONFIRM"
        const val FEE_STRING_BUNDLE = "FEE_STRING_BUNDLE"
        const val LIST_RESULT_OBJECT = "LIST_RESULT_OBJECT"
        const val CREATE_TRANSFER_OBJECT = "CREATE_TRANSFER_OBJECT"
        const val ERROR_CODE_NORMAL_TRANSFER = "19"

        //
        val dataSetFillDataTakePicture =
            DataFillObject(
                id = "0",
                name = "Chụp ảnh",
            )
        val dataSetFillDataChoosePicture =
            DataFillObject(
                id = "1",
                name = "Chọn từ thư viện hình ảnh",
            )
        val dataSetFillDataChooseFile =
            DataFillObject(
                id = "2",
                name = "Tải hồ sơ từ điện thoại",
            )
    }

    // navigate
    const val KEYPASS_HOME = "keypass://main"
    const val SOFT_ACTIVE = "soft://active"
    const val SOFT_PIN = "soft://enter_pin/"
    const val SOFT_HOME = "soft://home"
    const val TRANSACTION_FILTER = "transaction://filter"
    const val ACCOUNT_INQUIRY = "account://inquiry"
    const val SMARTCA_LIST = "smartca://list"
    const val DISBURSEMENT_HOME = "disbursement://main"
    const val OTT_DASHBOARD = "ott://dashboard"
    const val REGISTER_OTT = "ott://registerOTT/"
    const val DASHBOARD_FEATURE_OTT = "ott://dashboardFeatureOTT/"
    const val SMS_OTP_CREATE = "ott://smsOtpCreate/"
    const val CONFIRM_OTT = "ott://confirmOTTReg"
    const val CONFIRM_OTT_LOAN = "ott://confirmOTTLoanReg"
    const val UPDATE_BIOMETRIC = "update://biometric"
    const val UPDATE_EKYC_USER_INFO = "updateEkyc://userInfoFragment"
    const val ACCOUNT_BALANCE_TAG_NAV = "setting://account_balance/"
    const val CONFIRM_SMS = "setting://confirm_sms"
    const val MAKER_TRANSFER_BANK_CHOOSED_FRAGMENT = "maker://makerTransferBankChoosedFragment/"
    const val MAKER_TRANSFER_BANK_CONFIRM_FRAGMENT = "maker://makerTransferConfirmFragment/"
    const val MAKER_TRANSFER_BANK_RESULT_FRAGMENT = "maker://makerTransferResultFragment/"

    // register EKYC
    const val EKYC_STEP_REGISTER = "ekyc://stepFragment"

    const val TERMSTATE = "TERMSTATE"

    // TracePaymentComfirm
    const val COMFIRM_EDTMSGTRACE = "COMFIRM_EDTMSGTRACE"
    const val COMFIRM_ACCOUNTFREE = "COMFIRM_ACCOUNTFREE"
    const val COMFIRM_FREETRACE = "COMFIRM_FREETRACE"

    // key config local
    const val LIMIT_TRANSACTION = 300 // phe duyet toi da 300 giao dich
    const val LIMIT_FILE_TRANSACTION = 15 // phe duyet toi da 15 file
    const val LIMIT_PAGE_SIZE = "15"

    // home key
    const val TYPE_COUNT_TRANS_GROUP = "GROUP"
    const val UPDATE_TIME_MY_REQUEST = "UPDATE_TIME_MY_REQUEST"
    const val HOME_PENDING = "PENDING"
    const val HOME_APPROVED = "APPROVED"
    const val HOME_REJECTED = "REJECTED"
    const val HOME_LIST_LATEST_PARAMS_STATUS = "Create"
    const val HOME_LIST_LATEST_PARAMS_PAGE_SIZE = "3"
    const val HOME_LIST_LATEST_PARAMS_PAGE_NUM = "0"
    const val HOME_DATE_PATTERN = "HH:mm:ss"
    const val HOME_MONEY_PATTERN = "#,###"
    const val HOME_LIST_KEY_PARAM_R = "R"

    const val HOME_EMPTY_RECENT_CARD = "emptyRecent"
    const val HOME_RECOMMENDED_CARD = "recommended"
    const val HOME_BANNER_CARD = "banner"
    const val HOME_MY_REQUEST_CARD = "myRequest"
    const val HOME_RECENT_TRANS_CARD = "transaction"
    const val HOME_EMPTY_MY_REQUEST_CARD = "emptyMyRequest"
    const val HOME_ASSET_CARD = "assetCard"
    const val HOME_CREDIT_CARD = "creditCard"
    const val HOME_ACCOUNT_FROM_LOGIN = "HOME_ACCOUNT_FROM_LOGIN"

    // shortcut
    const val IN_FAVOURITE_SECTION = "IN_FAVOURITE_SECTION"
    const val LIST_FAVOURITE = "LIST_FAVOURITE"

    // role
    const val ROLE_MAKER = "0"
    const val ROLE_ACCOUNT_INQUIRY = "5"
    const val ROLE_INVOICE = "4"

    // home account
    const val IS_BALANCE = "1"
    const val IS_RELOAD_ACCOUNT = "IS_RELOAD_ACCOUNT"
    const val HOME_ACCOUNT_ASSET = "HOME_ACCOUNT_ASSET"
    const val HOME_ACCOUNT_CREDIT = "HOME_ACCOUNT_CREDIT"
    const val IS_HAVE_CREDIT_ACCOUNT = "IS_HAVE_CREDIT_ACCOUNT"

    const val HOME_FUNC_SETTING = "Cài đặt"
    const val HOME_FUNC_EFAST_MANAGE = "Quản lý giao dịch eFAST"
    const val HOME_FUNC_EINVOICE = "Hóa đơn điện tử"
    const val HOME_FUNC_ACCOUNT = "Tài khoản"
    const val HOME_FUNC_TRANSFER = "Chuyển tiền"
    const val HOME_FUNC_QR = "Quét QR"
    const val HOME_FUNC_MANAGE = "Quản lý giao dịch"
    const val HOME_FUNC_TAX = "Nộp Ngân sách Nhà nước"
    const val HOME_FUNC_SALARY = "Thanh toán lương"

    const val DAY_7 = 7
    const val DAY_15 = 15
    const val DAY_30 = 30
    const val SIZE_OPTION_SEARCH = 10
}
