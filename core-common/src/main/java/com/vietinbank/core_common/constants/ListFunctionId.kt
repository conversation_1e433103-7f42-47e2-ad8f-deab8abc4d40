package com.vietinbank.core_common.constants

object ListFunctionId {
    /** ALL Toàn app -- Đ<PERSON> làm */
    const val FORCE_UPDATE_ALL = "ALL" // toàn app

    /** ALL Tài khoản */
    const val FORCE_UPDATE_A_ACCOUNT = "A-ACCOUNT"

    /** ALL cài đặt*/
    const val FORCE_UPDATE_A_SETTING = "A-SETTING"

    /** MAKER - Chuyển tiền -- Đã làm */
    const val FORCE_UPDATE_M_TRANSFER_CT = "M-TRANSFER-CT" // M-TRANSFER-CT

    /** MAKER - TRA SOAT  -- Đ<PERSON> làm */
    const val FORCE_UPDATE_M_TRANSFER_TR = "M-TRANSFER-TR" // /** MAKER - TRA SOAT */

    /** CHECKER - Chi hộ và thanh toán lương */
    const val FORCE_UPDATE_C_TRANSFER_SL_HO = "C-TRANSFER-SL"

    /** CHECKER - Chuyển tiền  -- <PERSON><PERSON> làm */
    const val FORCE_UPDATE_C_TRANSFER_CT = "C-TRANSFER-CT" // /** CHECKER - Chuyển tiền */

    /** CHECKER - Chuyển tiền ngoại tệ */
    const val FORCE_UPDATE_C_TRANSFER_FX = "C-TRANSFER-FX"

    /** CHECKER - Bán ngoại tệ */
    const val FORCE_UPDATE_C_TRANSFER_SF = "C-TRANSFER-SF"

    /** CHECKER - Chuyển tiền theo file > 300GD */
    const val FORCE_UPDATE_C_BULK_HU = "C-BULK-HU"

    /** CHECKER - Chuyển tiền theo file <= 300GD */
    const val FORCE_UPDATE_C_BULK_BA = "C-BULK-BA"

    /** CHECKER - Tiền gửi */
    const val FORCE_UPDATE_C_TRANSFER_SA = "C-TRANSFER-SA"

    /** CHECKER - Tất toán khoản vay */
    const val FORCE_UPDATE_C_TRANSFER_LNC = "C-TRANSFER-LNC"

    /** CHECKER - Trả nợ khoản vay */
    const val FORCE_UPDATE_C_TRANSFER_LN = "C-TRANSFER-LN"

    /** CHECKER - Trích nợ tự động trả nợ vay */
    const val FORCE_UPDATE_C_TRANSFER_ALN = "C-TRANSFER-ALN"

    /** CHECKER - Nộp NSNN  -- Đã làm */
    const val FORCE_UPDATE_C_TAX_TX = "C-TAX-TX" // /** CHECKER - Nộp NSNN */

    /** CHECKER - Tra soát + MAKER -  Tra soát*/
    const val FORCE_UPDATE_C_OTHER_TR = "C-OTHER-TR"

    /** CHECKER - Chi lương ngoại tệ */
    const val FORCE_UPDATE_C_TRANSFER_SX = "C-TRANSFER-SX"

    /** CHECKER - Chi lương qua ngân hàng */
    const val FORCE_UPDATE_C_TRANSFER_SL = "C-TRANSFER-SL"

    /** CHECKER - Chi lương tự động */
    const val FORCE_UPDATE_C_TRANSFER_SLO = "C-TRANSFER-SLO"

    /** CHECKER - Điện thanh toán - Nộp phí công đoàn */
    const val FORCE_UPDATE_C_TRANSFER_UF = "C-TRANSFER-UF"

    /** CHECKER - Trích nợ tự động thanh toán hóa đơn */
    const val FORCE_UPDATE_C_TRANSFER_AP = "C-TRANSFER-AP"

    /** CHECKER - gửi chứng từ nhanh + MAKER - gửi chứng từ nhanh */
    const val FORCE_UPDATE_C_OTHER_SR = "C-OTHER-SR"

    /** CHECKER - Thu hộ học phí*/
    const val FORCE_UPDATE_C_TRANSFER_TP = "C-TRANSFER-TP"
}