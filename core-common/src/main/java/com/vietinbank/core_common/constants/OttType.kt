package com.vietinbank.core_common.constants

enum class OttType(val value: Int) {
    AccountBalanceNotification(159), // Tin biến động số dư
    FlightTicket(2), // Tin vé máy bay
    TextMessage(3), // Tin nhắn dạng text
    RechargeCard(4), // Tin mua mã thẻ
    DebtReminder(6), // <PERSON> nhắc nợ
    FlightTicketWithPayment(12), // Tin vé máy bay có nút thanh toán
    ReconciliationRequest(22), // Tin tra soát
    NotifyOnly(21), // Chỉ gửi notify
    TicketCondition(23), // Tin điều kiện về vé
    QrCode(24), // Tin QR
    ContractPayment(25), // Tin thanh toán hợp đồng
    OnlineLuckyMoney(26), // <PERSON>ì xì online
    Promotion(13), // Khuyến mại
    WinningCode(41), // <PERSON><PERSON> số trúng thưởng
    BillNotify(77), // Nh<PERSON><PERSON> nợ hóa đơn Điện/Nước/Viễn thông
    AcceptRejectBankRequest(200), // Chấp nhận/Từ chối yêu cầu từ ngân hàng
    VideoCallNotification(300), // Thông báo cuộc gọi qua VoIP
    VirtualAccountBalanceNotification(1591), // Tin biến động số dư TK ảo
    TransactionStatusNotification(500), // Thông báo trạng thái giao dịch
    UnDefine(0), // Thông báo trạng thái giao dịch
    ;

    companion object {
        private fun fromValue(value: Int): OttType? {
            return entries.find { it.value == value }
        }

        fun fromValueString(value: String?): OttType {
            return value?.toIntOrNull()?.let { fromValue(it) } ?: UnDefine
        }
    }
}
