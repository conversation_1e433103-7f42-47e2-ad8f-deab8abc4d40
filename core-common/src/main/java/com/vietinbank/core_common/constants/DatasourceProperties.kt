package com.vietinbank.core_common.constants

import com.vietinbank.core_common.environment.IEnvironmentProvider
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Created by vandz on 13/1/25.
 */
@Singleton
class DataSourceProperties @Inject constructor(
    private val environmentProvider: IEnvironmentProvider,

) {
    private val serverType = environmentProvider.getServerType()

    fun getServerType(): Int {
        return serverType
    }

    fun getUrl(): String {
        return when (serverType) {
            Server.LIVE -> Constants.SERVER_URL_LIVE
            Server.PPE -> Constants.SERVER_URL_PPE
            Server.UAT -> Constants.SERVER_URL_UAT
            else -> Constants.SERVER_URL_LIVE
        }
    }

    fun getDomain(): String {
        return when (serverType) {
            Server.LIVE -> Constants.DOMAIN_LIVE
            Server.PPE -> Constants.DOMAIN_PPE
            Server.UAT -> Constants.DOMAIN_UAT
            else -> Constants.DOMAIN_LIVE
        }
    }

    fun getCerts(): List<String> {
        val list = arrayListOf<String>()
        list.add("WoiWRyIOVNa9ihaBciRSC7XHjliYS9VwUGOIud4PB18=")
        list.add("m9Z7mswlRljf8acQ07EesjKOVJDGy2nR0ZrOM22PE40=")
        list.add("hETpgVvaLC0bvcGG3t0cuqiHvr4XyP2MTwCiqhgRWwU=")
        list.add("cGuxAXyFXFkWm61cF4HPWX8S0srS9j0aSqN0k4AP+4A=")

        return list
    }
}
