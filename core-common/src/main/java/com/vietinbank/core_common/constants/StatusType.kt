package com.vietinbank.core_common.constants

// trạng thai cua giao dich
enum class StatusType(val value: String) {
    REGEX_AWAITING_APPROVAL("."),
    ALL("000"), // tat ca
    KTT_CTK_REJECT("01"), // KTT/CTK từ chối
    WAITING_APPROVE("02"), // Chờ phê duyệt
    BANK_REJECT("06"), // Ngân hàng từ chối
    BANK_PROCESSING("07"), // Ngân hàng đang xử lý (07, 08, 09, 98)
    WAITING_BANK_PROCESSING("04"), // Chờ ngân hàng xử lý
    SUCCESS("05"), // Thành công
    ERROR("11"), // Giao dịch lỗi
    DELETED_13("13"), // Đã xóa
    DELETED_93("93"), // Đ<PERSON> xóa
    CANCELED("90"), // Đã hủy
    ERROR_DAO_GD("11ca"), // Lỗi đảo giao dịch
    PROCESSING("99"), // <PERSON><PERSON> xử lý
    WAITING_CREDIT("11p"), // Đang chờ ghi có
    SKIP("071"), // Bỏ qua
    REQUEST_RASOAT("00"), // Tạo yêu cầu rà soát hồ sơ
    BANK_RECEIVE_DOCUMENTS("A1"), // Ngân hàng nhận hồ sơ
    BANK_PLUS_DOCUMENTS("A2"), // Ngân hàng yêu cầu bổ sung hồ sơ
    BANK_PLUS_DOCUMENTS_2("A21"), // Ngân hàng yêu cầu bổ sung chứng từ
    DISBURSED_OF_DOCUMENTS("F"), // Hồ sơ đã được giải ngân
    USER_DELETE_DOCUMENTS("X"), // Khách hàng xóa hồ sơ
    USER_CANCEL_DOCUMENTS("C"), // Khách hàng hủy hồ sơ
    ;

    companion object {
        fun fromValue(value: String?): StatusType? {
            return StatusType.entries.find { it.value == value }
        }
    }
}
