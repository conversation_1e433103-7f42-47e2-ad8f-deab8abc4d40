package com.vietinbank.core_common.constants

/**
 * Created by vandz on 13/1/25.
 */
object Constants {
    const val SERVER_URL_LIVE = "https://webdemo.vietinbank.vn/"
    const val SERVER_URL_UAT = "https://webdemo.vietinbank.vn/"
    const val SERVER_URL_PPE = "https://webdemo.vietinbank.vn/"

    const val DOMAIN_LIVE = "webdemo.vietinbank.vn"
    const val DOMAIN_UAT = "webdemo.vietinbank.vn"
    const val DOMAIN_PPE = "webdemo.vietinbank.vn"

    const val URL_KEYPASCO = "https://ebanking.vietinbank.vn/synchronizeOTP/keypass/sync/"

    // login
    const val MB_LOGIN = "/efast/mb/login"
    const val MB_BANNER = "/efast/mb/promotion/list"
    const val MB_REGISTER_TOUCH_ID = "/efast/mb/register-touchid"
    const val MB_LOGOUT = "/efast/mb/logout"
    const val MB_CHANGE_PWD = "/efast/mb/change-password"
    const val MB_GEN_OTP = "/efast/mb/gentOtp"
    const val MB_VERIFY_OTP = "/efast/mb/verifyOtp"
    const val MB_ACCOUNT_LIST = "/efast/mb/account/list"
    const val MB_CHECK_PASSWORD = "/efast/mb/check-password"

    const val MB_GET_ACTIVATION_CODE = "/efast/mb/authen/get-soft-otp-activation-code"
    const val MB_BLOCK_SOFT = "/efast/mb/authen/block-soft-token"

    const val MB_CHECK_VERSION_APP = "/efast/mb/account/check-version-app"

    // keypass
    const val MB_KEYPASS_FORGOT_PIN = "/efast/mb/authen/forgot-keypass-pin"

    // maker
    const val MB_NAPAS_BANK_LIST = "/efast/mb/transfer-new/bank-list"
    const val MB_VALIDATE_ACCOUNT = "/efast/mb/transfer-new/validate-account"
    const val MB_CREATE_ACCOUNT_TRANSFER = "/efast/mb/transfer-new/create-account-transfer"
    const val MB_CREATE_CARD_TRANSFER = "/efast/mb/transfer/create-napas-card-transfer-v2"
    const val MB_CREATE_ORDER_PAYMENT_TRANSFER = "/efast/mb/transfer/create-payment-order-transfer"
    const val MB_VALIDATE_ACCOUNT_TRANSFER = "/efast/mb/transfer-new/validate-account-transfer"
    const val MB_VALIDATE_NAPAS_CARD = "/efast/mb/transfer/validate-napas-card"
    const val MB_VALIDATE_NAPAS_CARD_TRANSFER = "/efast/mb/transfer/validate-napas-card-transfer-v2"
    const val MB_CONTACT_LIST = "/efast/mb/contact/list"
    const val MB_GET_PAYMENT_TEMPLATE_LIST = "/efast/mb/transfer/get-payment-template-list"
    const val MB_CONTACT_CREATE = "/efast/mb/contact/create"
    const val MB_CREATE_TEMPLATE = "/efast/mb/transaction/create-template"
    const val MB_VALIDATE_PAYMENT_ORDER_TRANSFER =
        "/efast/mb/transfer-new/validate-payment-order-transfer"
    const val MB_NEXT_STATUS_TRANSACTION_BY_RULE =
        "/efast/mb/transaction/nextStatusTransactionByRule"
    const val MB_BRANCH = "/efast/mb/transfer-new/branch"

    // checker
    const val MB_GET_TRANSACTION_LIST = "/efast/mb/transaction/transaction-list"
    const val MB_GET_TRAN_LIST = "/efast/mb/transaction/tran-list"
    const val MB_GET_TRANSACTION_DETAIL = "/efast/mb/transaction/transaction-detail"
    const val MB_COUNT_PENDING = "/efast/mb/account/count-pending"
    const val MB_PRE_REJECT = "/efast/mb/transfer-new/pre-reject"
    const val MB_REJECT = "/efast/mb/transfer-new/reject"
    const val MB_PRE_APPROVE = "/efast/mb/transaction/pre-approve"
    const val MB_APPROVE = "/efast/mb/transaction/approve"
    const val MB_GEN_SOTP_TRANS_CODE = "/efast/mb/authen/create-soft-otp-transaction"
    const val MB_GEN_KEYPASS_CHALLENGE_CDE = "/efast/mb/authen/get-keypass-challenge-code"
    const val MB_GET_BATCH_FILE_LIST = "/efast/mb/transaction/old-batch-file-list"
    const val MB_GET_BATCH_TRANS_LIST = "/efast/mb/transaction/old-batch-transaction-list"
    const val MB_GET_DOWNLOAD_FILE_ID = "/efast/mb/transaction/get-download-file-id"
    const val MB_GET_DOWNLOAD_BASE_FILE = "/efast/mb/report/exportNSNNTrans"
    const val MB_GET_DOWNLOAD_NSNN_FILE = "/efast/mb/report/exportNSNN-file-template"
    const val MB_DOWNLOAD_FILE = "efast/mb/transaction/downloadFile/{encryptStr}"
    const val MB_NEXT_STATUS_TRANS = "efast/mb/transaction/nextStatusTransactionByRule"
    const val MB_SUB_TRANSACTION = "/efast/mb/transfer-new/subTransaction"

    // report filter
    const val MB_REPORT_PRE_FILTER = "/efast/mb/report/pre-filter"
    const val MB_REPORT_LIST_FILTER = "/efast/mb/report/list"
    const val MB_REPORT_DETAIL_FILTER = "/efast/mb/report/detail"
    const val MB_INQUIRY_APPROVER = "/efast/mb/transfer-new/inquiry-approver"
    const val MB_TRACE_INQUIRY = "/efast/mb/transaction/trace/inquiry"
    const val MB_REPORT_DETAIL = "/efast/mb/report/detail"
    const val MB_TRACE_CREATE = "/efast/mb/transaction/trace/create"

    // inquiry account
    const val MB_ACCOUNT_LIST_NEW = "/efast/mb/account/list"
    const val MB_ACCOUNT_DETAIL = "/efast/mb/account/detail"
    const val MB_ACCOUNT_HISTORY_DETAIL = "/efast/mb/account/history-detail"
    const val MB_ACCOUNT_HISTORY_LIST = "/efast/mb/account/history"
    const val MB_ACCOUNT_SAVE_QR = "/efast/mb/qr/save-qr-transfer-data"
    const val MB_ACCOUNT_IMAGE_QR = "/efast/mb/common/get-images"
    const val MB_ACCOUNT_HISTORY_STATUS = "/efast/mb/account/history/tran-status"
    const val MB_ACCOUNT_UPDATE_ALIAS = "/efast/mb/account/update/alias"
    const val MB_ACCOUNT_EXPORT_FILE = "/efast/mb/account/export/deposit-confirm"

    // rate
    const val MB_CSAT_RATE = "/efast/mb/csat/rate"
    const val MB_CSAT_CONFIG = "/efast/mb/csat/config"

    // report transfer
    const val MB_TRANSACTION_LIST = "/efast/mb/transaction/transaction-list"

    // smart ca
    const val MB_SMART_CA_LIST = "/efast/mb/sign/cert-list" // Lấy danh sách cks đã đăng ký
    const val MB_SMART_CA_BRANCH_LIST = "/efast/mb/sign/branch-list" // Lấy danh sách chi nhanh
    const val MB_SMART_CA_DETAIL = "/efast/mb/sign/cert-detail" //  Xem chi tiết cks
    const val MB_SMART_CA_UPDATE = "/efast/mb/sign/cert-update" // Đóng/xóa cks
    const val MB_SMART_CA_GET_PARAM = "/efast/mb/sign/get-params" // params
    const val MB_SMART_CA_GET_CERT_VNPT = "/efast/mb/sign/get-cert-list" // lay thong tin tu vnpt
    const val MB_SMART_CA_GET_CERT = "/efast/mb/sign/get-cert" // lay thong tin

    // home
    const val MB_CHECK_USER_EKYC = "/efast/mb/ekyc-sth/checkUserEkyc"
    const val MB_GENERATE_OTP = "/efast/mb/ekyc-sth/generate-otp-service-retail"
    const val MB_VERIFY_OTP_EKYC = "/efast/mb/ekyc-sth/verify-otp-service-retail"
    const val MB_UPDATE_EKYC = "/efast/mb/ekyc-sth/update-ekyc-sth"
    const val MB_GET_BIOMETRIC_FACE = "/efast/mb/ekyc-sth/biometric-face"
    const val MB_COUNT_TRANS_GROUP = "/efast/mb/account/count-trans-group"
    const val MB_LIST_LATEST = "/efast/mb/account/list-lastest"
    const val MB_HOME_ACCOUNT_LIST = "/efast/mb/account/home-account-list"
    const val MB_LIST_KEY = "/efast/mb/transfer-new/list-key"
    const val MB_LIST_FUNCTION = "/efast/mb/transfer-new/list-function"
    const val MB_LIST_FUNCTION_FAVOURITE = "/efast/mb/transfer-new/favorite-functions"
    const val MB_LIST_UPDATE_FUNCTION_FAVOURITE = "/efast/mb/transfer-new/update-favorite-functions"

    // ott
    const val MB_OTT_CHECK_REG = "/efast/mb/ott/checkReg"
    const val MB_OTT_LIST_REG = "/efast/mb/ott/listRegAll"
    const val MB_OTT_SMS_CREATE = "/efast/mb/ott/smsOtpCreate"
    const val MB_OTT_SMS_VERIFY = "/efast/mb/ott/smsOtpVerify"
    const val MB_OTT_REGISTER = "/efast/mb/ott/register"
    const val MB_OTT_STATUS_LIST = "/ott-status/list"
    const val MB_OTT_STATUS_UPDATE = "/ott-status/update"
    const val MB_OTT_CONFIRM_VACCT = "/efast/mb/ott/vacct/confirm"
    const val MB_OTT_CANCEL_VACCT = "/efast/mb/ott/vacct/cancel"

    // het phien client
    const val MB_MOBILE_CONFIG_LAST_PARAMS = "/efast/mb/common/get-mobile-config-last"

    // ekyc
    const val MB_EKYC_OCR = "/efast/mb/ekyc-sth/ekyc-ocr"
    const val MB_EKYC_NFC = "/efast/mb/ekyc-sth/ekyc-nfc"
    const val MB_EKYC_CONFIRM_INPUT = "/efast/mb/ekyc-sth/confirm-input"
    const val MB_EKYC_COMPARE_IMAGE = "/efast/mb/ekyc-sth/compare-image"

    // account lock
    const val MB_ACCOUNT_LOCK = "/efast/mb/resetPwd/check-infor"
    const val MB_ACCOUNT_LOCK_OTP = "/efast/mb/resetPwd/send-otp"
    const val MB_ACCOUNT_LOCK_FACE = "/efast/mb/ekyc-sth/face-compare"
}
