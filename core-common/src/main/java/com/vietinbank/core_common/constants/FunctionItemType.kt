package com.vietinbank.core_common.constants

object FunctionItemType {

    object GroupConstants {
        // Chức năng mới
        const val NEW_FEATURE = "1"

        // Tài khoản
        const val ACCOUNT = "2"

        // BFM
        const val BFM = "3"

        // Dịch vụ Chuyển tiền
        const val TRANSFER = "4"

        // Dịch vụ nộp NSNN
        const val TAX_PAYMENT = "5"

        // Mua bán - Chuyển tiền ngoại tệ
        const val FX_TRANSFER = "6"

        // Dịch vụ Thanh toán
        const val PAYMENT = "7"

        // Dịch vụ Tiền gửi
        const val DEPOSIT = "8"

        // Dịch vụ Tín dụng
        const val CREDIT = "9"

        // Dịch vụ Thu/Chi hộ
        const val COLLECTION = "10"

        // Dịch vụ Khác
        const val OTHERS = "11"

        // Quản lý Giao dịch
        const val TRANSACTION_MGMT = "12"

        // Cài đặt
        const val SETTINGS = "13"

        // Tra soát
        const val INVESTIGATION = "14"
    }

    object NewFeature {
        // Đặt vé máy bay
        const val PA = "pa"
    }

    object Settings {
        // Cài đặt VietinBank SoftOTP
        const val OTP = "otp"

        // Mở khóa người dùng
        const val UL = "ul"

        // Cấp lại mật khẩu
        const val RP_1 = "rp-1"

        // Danh bạ hóa đơn
        const val BPC = "bpc"

        // Thông tin doanh nghiệp
        const val CI = "ci"

        // Thông tin cá nhân
        const val PI_1 = "pi-1"

        // Lãi suất
        const val IR = "ir"

        // Tỷ giá
        const val ER = "er"

        // Thay avatar
        const val AV = "av"

        // Chia sẻ ứng dụng
        const val SH = "sh"

        // Ngôn ngữ
        const val LG = "lg"

        // Mạng lưới CN/PGD/ATM
        const val BRN = "brn"

        // Câu hỏi thường gặp
        const val QT = "qt"

        // Hỗ trợ Khách hàng
        const val SP = "sp"

        // Biến động số dư
        const val BC = "bc"

        // Quên PIN thẻ Keypass
        const val PK = "pk"

        // Đổi mật khẩu
        const val PWD = "pwd"

        // Đăng nhập bằng FaceID/TouchID
        const val ID = "id"
    }

    object TransferServices {
        // Chuyển tiền trong VTB
        const val IN = "in"

        // Chuyển tiền theo file <= 300 GD
        const val BA = "ba"

        // Quét QR chuyển khoản
        const val QR = "qr"

        // Đặt lịch chuyển tiền định kỳ
        const val AST = "ast"

        // Lệnh chi
        const val PM = "pm"

        // Chuyển tiền theo file > 300 GD
        const val HU = "hu"

        // Chuyển tiền liên NH 24/7 qua số thẻ
        const val NPC = "npc"

        // Chuyển tiền liên NH 24/7 qua TK
        const val NPA = "npa"

        // Chuyển tiền liên NH thường
        const val OU = "ou"
    }

    object OtherServices {
        // Gửi chứng từ nhanh
        const val SR = "sr"

        // Tra soát
        const val TR = "tr"
    }

    object BudgetPaymentServices {
        // Nộp thuế nội địa
        const val TAX_01 = "tax-01"

        // Nộp ngân sách nhà nước
        const val TAX_02 = "tax-02"

        // Nộp phí hạ tầng
        const val IF = "if"

        // Nộp thuế hải quan
        const val TAX_04 = "tax-04"
    }

    object PaymentServices {
        // Nộp phí công đoàn
        const val UF = "uf"

        // Nộp BHXH
        const val SIP = "sip"

        // Trích nợ tự động thanh toán hóa đơn
        const val AP = "ap"

        // Thanh toán hóa đơn
        const val PI = "pi"

        // Nộp phí không dừng
        const val NT = "nt"
    }

    object CollectionServices {
        // Chi lương qua ngân hàng
        const val SL = "sl"

        // Thu hộ học phí
        const val TP = "tp"

        // Chi lương ngoại tệ
        const val SX = "sx"

        // Chi lương tự động
        const val SLO = "slo"
    }

    object DepositServices {
        // Tiền gửi có kỳ hạn
        const val SA = "sa"

        // Tất toán tiền gửi online
        const val CA = "ca"
    }

    object CreditServices {
        // Trả nợ khoản vay
        const val LN = "ln"

        // Giải ngân
        const val DO = "do"

        // Trích nợ tự động trả nợ khoản vay
        const val ALN = "aln"

        // Tất toán khoản vay
        const val LN_1 = "ln-1"
    }

    object ForeignCurrencyServices {
        // Chuyển tiền ngoại tệ
        const val FX = "fx"

        // Chuyển tiền ngoại tệ nội bộ - Thanh toán biên mậu
        const val FXR = "fxr"

        // Bán ngoại tệ
        const val SF = "sf"

        // Mua và chuyển tiền ngoại tệ tỷ giá ưu đãi
        const val FXR_1 = "fxr-1"

        // Mua và chuyển tiền ngoại tệ
        const val FC = "fc"
    }

    object TransactionManagement {
        // Quản lý giao dịch eFAST (Báo cáo giao dịch)
        const val RP = "rp"

        // Hóa đơn điện tử
        const val EIN = "ein"

        // Danh bạ thụ hưởng
        const val PC = "pc"

        // Báo cáo thu hộ học phí
        const val RTP = "rtp"
    }

    object Account {
        // Danh sách tài khoản
        const val AC = "ac"

        // Danh sách tài khoản thanh toán_TK ủy quyền
        const val AA = "aa"
    }
}