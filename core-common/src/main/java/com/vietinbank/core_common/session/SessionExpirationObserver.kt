package com.vietinbank.core_common.session

import com.vietinbank.core_common.extensions.printLog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Qualifier
import javax.inject.Singleton

/**
 * Created by vand<PERSON> on 30/3/25.
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class ApplicationScope

@Singleton
class SessionExpirationObserver @Inject constructor(
    private val sessionManager: ISessionManager,
    @ApplicationScope private val coroutineScope: CoroutineScope,
) {
    init {
        observeSessionExpiration()
    }

    private fun observeSessionExpiration() {
        printLog("SessionExpirationObserver: Starting to observe session expiration events")
        coroutineScope.launch {
            SessionEvent.sessionExpiredEvent.collect { message ->
                printLog("SessionExpirationObserver: Received session expired event - $message")
                // Clear session when received event
                sessionManager.clearSession()
                printLog("SessionExpirationObserver: Session cleared - $message")
            }
        }
    }
}
