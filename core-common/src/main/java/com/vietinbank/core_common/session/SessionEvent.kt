package com.vietinbank.core_common.session

import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow

/**
 * Centralized event bus for session expiry events
 * Uses SharedFlow to broadcast to all active observers
 */
object SessionEvent {
    private val _sessionExpiredEvent = MutableSharedFlow<Unit>()
    val sessionExpiredEvent = _sessionExpiredEvent.asSharedFlow()

    suspend fun emitSessionExpired() {
        _sessionExpiredEvent.emit(Unit)
    }
}
