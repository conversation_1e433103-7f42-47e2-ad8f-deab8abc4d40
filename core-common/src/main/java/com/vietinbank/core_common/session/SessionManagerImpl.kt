package com.vietinbank.core_common.session

import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.models.SessionData
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Created by vandz on 19/2/25.
 */
@Singleton
class SessionManagerImpl @Inject constructor(
    private val prefs: CustomEncryptedPrefs,
) : ISessionManager {

    private val _sessionFlow = MutableStateFlow(SessionData())
    override val sessionFlow: StateFlow<SessionData> get() = _sessionFlow

    override fun loadSession() {
        val sid = prefs.getString(Tags.SESSIONID)
        _sessionFlow.value = SessionData(
            sessionID = sid,
        )
    }

    override fun saveSession(sessionId: String) {
        prefs.setString(Tags.SESSIONID, sessionId)
        _sessionFlow.value = SessionData(
            sessionID = sessionId,
        )
    }

    override fun clearSession() {
        prefs.remove(Tags.SESSIONID)
        prefs.remove(Tags.HOME_ACCOUNT_ASSET)
        prefs.remove(Tags.HOME_ACCOUNT_CREDIT)
        prefs.remove(Tags.LIST_FAVOURITE)
        _sessionFlow.value = SessionData()
    }

    override fun getSessionId(): String? = _sessionFlow.value.sessionID
}
