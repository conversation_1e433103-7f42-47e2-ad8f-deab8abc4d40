package com.vietinbank.core_common.session

import android.text.TextUtils
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.models.UserProfData
import com.vietinbank.core_common.utils.Utils
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Created by vandz on 19/2/25.
 */

@Singleton
class UserProfImpl @Inject constructor(
    private val prefs: CustomEncryptedPrefs,
) : IUserProf {

    private val _userProfFlow = MutableStateFlow(UserProfData())
    override val userProfFlow: StateFlow<UserProfData> get() = _userProfFlow

    /**
     * Load toàn bộ user profile từ prefs (json) => parse => emit state
     */
    override fun loadProfile() {
        val json = prefs.getString(Tags.USER_PROFILE)
        if (!json.isNullOrEmpty()) {
            val obj = runCatching {
                Utils.g().provideGson().fromJson(json, UserProfData::class.java)
            }.getOrNull()
            if (obj != null) {
                _userProfFlow.value = obj
            } else {
                _userProfFlow.value = UserProfData()
            }
        } else {
            _userProfFlow.value = UserProfData()
        }
    }

    /**
     * Hoặc ta cung cấp 1 hàm "saveProfile(userProfData: UserProfData)"
     * => toJson => prefs => _userProfFlow.value = userProfData
     */
    override fun saveProfile(userProfData: UserProfData) {
        val json = Utils.g().provideGson().toJson(userProfData)
        prefs.setString(Tags.USER_PROFILE, json)
        _userProfFlow.value = userProfData
    }

    override fun isActive(): Boolean = !TextUtils.isEmpty(_userProfFlow.value.phone)
    override fun isChecker(): Boolean = _userProfFlow.value.roleId == Tags.CHECKER
    override fun isLogined(): Boolean {
        return _userProfFlow.value.sessionID?.isNotEmpty() ?: false
    }

    override fun clearProfile() {
        prefs.remove(Tags.USER_PROFILE)
        _userProfFlow.value = UserProfData()
    }

    override fun getPhoneNo(): String? {
        return _userProfFlow.value.phone
    }

    override fun getUserName(): String? {
        return _userProfFlow.value.userName
    }

    override fun getUserId(): String? {
        return _userProfFlow.value.userId
    }

    override fun getFullName(): String? {
        return if (TextUtils.isEmpty(_userProfFlow.value.fullName)) {
            "Quý khách"
        } else {
            _userProfFlow.value.fullName
        }
    }

    override fun getCifNo(): String? {
        return _userProfFlow.value.cifNo
    }

    override fun getFingerID(): String? {
        return _userProfFlow.value.fingerID
    }

    override fun getCorpUserStatus(): String? {
        return _userProfFlow.value.status
    }

    override fun getKeypassProfile(): String? {
        return _userProfFlow.value.keypassprofile
    }

    override fun getKeypassSoftotp(): String? {
        return _userProfFlow.value.keypasssoftotp
    }

    override fun getEmail(): String? {
        return _userProfFlow.value.email
    }

    override fun getCorpName(): String? {
        return _userProfFlow.value.corpName
    }

    override fun isKeypassActive(): Boolean {
        return !TextUtils.isEmpty(_userProfFlow.value.keypassToken)
    }

    override fun getKeypassToken(): String? {
        return _userProfFlow.value.keypassToken
    }

    override fun getKeypassCode(): String? {
        return _userProfFlow.value.keypassCode
    }

    override fun getRoleLevel(): String? {
        return _userProfFlow.value.roleLevel
    }

    override fun getGroupType(): String? {
        return _userProfFlow.value.groupType
    }

    override fun getRoleId(): String? {
        return _userProfFlow.value.roleId
    }

    override fun getIdNumber(): String? {
        return _userProfFlow.value.idNumber
    }

    override fun getFinancialPackage(): String? {
        return _userProfFlow.value.addField4
    }
}
