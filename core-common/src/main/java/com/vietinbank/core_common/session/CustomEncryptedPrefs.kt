package com.vietinbank.core_common.session

import android.content.SharedPreferences
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Persistence level for preferences
 */
enum class Persistence {
    NON_PERSISTENT, // Removed by clearAll() and clearEverything()
    PERSISTENT, // Kept by clearAll(), only removed by clearEverything()
}

/**
 * Created by vandz on 13/1/25.
 * Enhanced with persistence mechanism to support selective clearing
 */
@Singleton
class CustomEncryptedPrefs @Inject constructor(
    private val encryptedPrefs: SharedPreferences,
    private val regularPrefs: SharedPreferences,
) {
    private val gson = Gson()

    companion object {
        // Metadata keys for tracking persistent keys
        private const val ENCRYPTED_PERSISTENT_KEYS = "PERSISTENT_KEYS_ENC"
        private const val REGULAR_PERSISTENT_KEYS = "PERSISTENT_KEYS_REG"
    }

    fun getString(key: String, defaultValue: String? = null, encrypted: Boolean = true): String? {
        val prefs = if (encrypted) encryptedPrefs else regularPrefs
        return prefs.getString(key, defaultValue)
    }

    fun setString(
        key: String,
        value: String?,
        encrypted: Boolean = true,
        persistence: Persistence = Persistence.NON_PERSISTENT,
    ) {
        val prefs = if (encrypted) encryptedPrefs else regularPrefs
        prefs.edit().putString(key, value).apply()

        if (persistence == Persistence.PERSISTENT) {
            val persistentKeys = loadPersistentKeySet(encrypted)
            if (persistentKeys.add(key)) {
                savePersistentKeySet(persistentKeys, encrypted)
            }
        }
    }

    fun getBoolean(key: String, defaultValue: Boolean = false, encrypted: Boolean = true): Boolean {
        val prefs = if (encrypted) encryptedPrefs else regularPrefs
        return prefs.getBoolean(key, defaultValue)
    }

    fun setBoolean(
        key: String,
        value: Boolean,
        encrypted: Boolean = true,
        persistence: Persistence = Persistence.NON_PERSISTENT,
    ) {
        val prefs = if (encrypted) encryptedPrefs else regularPrefs
        prefs.edit().putBoolean(key, value).apply()

        if (persistence == Persistence.PERSISTENT) {
            val persistentKeys = loadPersistentKeySet(encrypted)
            if (persistentKeys.add(key)) {
                savePersistentKeySet(persistentKeys, encrypted)
            }
        }
    }

    fun getInt(key: String, defaultValue: Int = 0, encrypted: Boolean = true): Int {
        val prefs = if (encrypted) encryptedPrefs else regularPrefs
        return prefs.getInt(key, defaultValue)
    }

    fun setInt(
        key: String,
        value: Int,
        encrypted: Boolean = true,
        persistence: Persistence = Persistence.NON_PERSISTENT,
    ) {
        val prefs = if (encrypted) encryptedPrefs else regularPrefs
        prefs.edit().putInt(key, value).apply()

        if (persistence == Persistence.PERSISTENT) {
            val persistentKeys = loadPersistentKeySet(encrypted)
            if (persistentKeys.add(key)) {
                savePersistentKeySet(persistentKeys, encrypted)
            }
        }
    }

    fun setLong(
        key: String,
        value: Long,
        encrypted: Boolean = true,
        persistence: Persistence = Persistence.NON_PERSISTENT,
    ) {
        val prefs = if (encrypted) encryptedPrefs else regularPrefs
        prefs.edit().putLong(key, value).apply()

        if (persistence == Persistence.PERSISTENT) {
            val persistentKeys = loadPersistentKeySet(encrypted)
            if (persistentKeys.add(key)) {
                savePersistentKeySet(persistentKeys, encrypted)
            }
        }
    }

    fun getLong(key: String, defaultValue: Long = 0L, encrypted: Boolean = true): Long {
        val prefs = if (encrypted) encryptedPrefs else regularPrefs
        return prefs.getLong(key, defaultValue)
    }

    fun remove(key: String, encrypted: Boolean = true) {
        val prefs = if (encrypted) encryptedPrefs else regularPrefs
        prefs.edit().remove(key).apply()

        // Also remove from persistent keys if present
        val persistentKeys = loadPersistentKeySet(encrypted)
        if (persistentKeys.remove(key)) {
            savePersistentKeySet(persistentKeys, encrypted)
        }
    }

    /**
     * Clears all NON_PERSISTENT keys, preserves PERSISTENT keys
     */
    fun clearAll() {
        safeRemoveNonPersistent(encrypted = true)
        safeRemoveNonPersistent(encrypted = false)
    }

    /**
     * Clears everything including PERSISTENT keys (old clearAll behavior)
     */
    fun clearEverything() {
        try {
            encryptedPrefs.edit().clear().apply()
        } catch (_: Throwable) {
            // Ignore errors during clear
        }
        try {
            regularPrefs.edit().clear().apply()
        } catch (_: Throwable) {
            // Ignore errors during clear
        }
    }

    /**
     * Helper to load persistent key set from metadata
     */
    private fun loadPersistentKeySet(encrypted: Boolean): MutableSet<String> {
        return try {
            val metadataKey = if (encrypted) ENCRYPTED_PERSISTENT_KEYS else REGULAR_PERSISTENT_KEYS
            val prefs = if (encrypted) encryptedPrefs else regularPrefs
            val json = prefs.getString(metadataKey, null)
            if (json != null) {
                val type = object : TypeToken<Set<String>>() {}.type
                gson.fromJson<Set<String>>(json, type).toMutableSet()
            } else {
                mutableSetOf()
            }
        } catch (e: Exception) {
            // Log warning and return empty set on error
            mutableSetOf()
        }
    }

    /**
     * Helper to save persistent key set to metadata
     */
    private fun savePersistentKeySet(keySet: Set<String>, encrypted: Boolean) {
        try {
            val metadataKey = if (encrypted) ENCRYPTED_PERSISTENT_KEYS else REGULAR_PERSISTENT_KEYS
            val prefs = if (encrypted) encryptedPrefs else regularPrefs
            val json = gson.toJson(keySet)
            prefs.edit().putString(metadataKey, json).apply()
        } catch (e: Exception) {
            // Log warning but don't crash
        }
    }

    /**
     * Safely remove all non-persistent keys from the specified preference store
     */
    private fun safeRemoveNonPersistent(encrypted: Boolean) {
        try {
            val prefs = if (encrypted) encryptedPrefs else regularPrefs
            val persistentKeys = loadPersistentKeySet(encrypted)
            val metadataKey = if (encrypted) ENCRYPTED_PERSISTENT_KEYS else REGULAR_PERSISTENT_KEYS

            // Add metadata key to persistent set to avoid deleting it
            persistentKeys.add(metadataKey)

            // Get all keys and remove non-persistent ones
            val editor = prefs.edit()
            val allKeys = prefs.all.keys.toList() // Create a copy to avoid concurrent modification

            for (key in allKeys) {
                if (!persistentKeys.contains(key)) {
                    try {
                        editor.remove(key)
                    } catch (_: Exception) {
                        // Ignore individual key removal errors
                    }
                }
            }

            editor.apply()
        } catch (e: Exception) {
            // Log warning but don't crash
        }
    }
}
