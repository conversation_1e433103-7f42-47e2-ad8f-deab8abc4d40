package com.vietinbank.core_common.session

import com.vietinbank.core_common.models.UserProfData
import kotlinx.coroutines.flow.StateFlow

/**
 * Created by vand<PERSON> on 19/2/25.
 */

interface IUserProf {
    val userProfFlow: StateFlow<UserProfData>

    fun loadProfile()
    fun saveProfile(userProfData: UserProfData)
    fun clearProfile()
    fun isActive(): Boolean
    fun isChecker(): Boolean
    fun isLogined(): Boolean
    fun getPhoneNo(): String?
    fun getUserName(): String?
    fun getUserId(): String?
    fun getFullName(): String?
    fun getCifNo(): String?
    fun getFingerID(): String?
    fun getCorpUserStatus(): String?

    fun getKeypassProfile(): String?
    fun getKeypassSoftotp(): String?
    fun getEmail(): String?
    fun getCorpName(): String?

    // keypass authen
    fun isKeypassActive(): Boolean
    fun getKeypassToken(): String?
    fun getKeypassCode(): String?

    // next approver
    fun getRoleLevel(): String?
    fun getGroupType(): String?
    fun getRoleId(): String?

    // cccd -> ky so
    fun getIdNumber(): String?

    fun getFinancialPackage(): String?
}
