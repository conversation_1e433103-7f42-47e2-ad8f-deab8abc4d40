package com.vietinbank.core_common.session

import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.models.SubConfigListDomain
import com.vietinbank.core_common.utils.Utils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Created by vandz on 20/6/25.
 * InactivityManager.startMonitoring() được gọi từ BaseFragment.onResume()
 * User interact to screen => onUserInteraction() update lastTouchTime
 * Background job check every 20s → compare current time with lastTouchTime
 * REached timeout? → Emit SessionEvent.sessionExpiredEvent
 * SessionExpirationObserver received event? → Clear sessionBaseFragment observe LiveData → Show dialog và navigate to login
 * BaseViewModel observe SessionEvent → Post to expireSessionEvent LiveData
 * BaseFragment observe LiveData → Show dialog & navigate to login
 */
@Singleton
class InactivityManager @Inject constructor(
    private val sessionManager: ISessionManager,
    private val appConfig: IAppConfigManager,
    private val prefs: CustomEncryptedPrefs,
    @ApplicationScope private val applicationScope: CoroutineScope,
) {
    private var timeoutJob: Job? = null
    private var isMonitoring = false

    companion object {
        private const val DEFAULT_TIMEOUT_SECONDS = 300 // default 5 mins
        private const val CHECK_INTERVAL_MS = 15_000L // interval check every ~15 sec
    }

    fun startMonitoring() {
        if (!isUserLoggedIn()) {
            return
        }

        // If already monitoring, don't restart (don't reset timer)
        if (isMonitoring) {
            return
        }

        stopMonitoring()
        isMonitoring = true

        // Only update last interaction time for initial start
        // This prevents resetting timer when app goes background/foreground
        updateLastInteractionTime()
        printLog("InactivityManager: monitoring started; timeout=${getTimeoutMillis()}ms")

        timeoutJob = applicationScope.launch {
            while (isMonitoring) {
                delay(CHECK_INTERVAL_MS)
                checkInactivity()
            }
        }
    }

    fun stopMonitoring() {
        isMonitoring = false
        timeoutJob?.cancel()
        timeoutJob = null
    }

    fun onUserInteraction() {
        if (!isMonitoring || !isUserLoggedIn()) {
            return
        }

        updateLastInteractionTime()
    }

    private suspend fun checkInactivity() {
        if (!isUserLoggedIn()) {
            stopMonitoring()
            return
        }

        val currentTime = System.currentTimeMillis()
        val lastTouchTime = appConfig.getLastTouchTime()
        val timeoutMillis = getTimeoutMillis()

        val inactiveTime = currentTime - lastTouchTime
        printLog(
            "InactivityManager: inactive=${inactiveTime}ms, threshold=${timeoutMillis}ms, lastTouch=$lastTouchTime",
        )

        if (inactiveTime > timeoutMillis) {
            handleSessionTimeout()
        }
    }

    private suspend fun handleSessionTimeout() {
        // 1. Clear all session data FIRST (before stopping monitoring)
        // This prevents isUserLoggedIn() from returning true if checked again
        sessionManager.clearSession()

        // 2. Emit event BEFORE stopping monitoring to avoid coroutine cancellation
        // CRITICAL: Must emit while the coroutine is still active
        // Message will be retrieved from string resources in the UI layer
        SessionEvent.emitSessionExpired()

        // 3. Stop monitoring LAST after event is successfully emitted
        // Now safe to cancel the job since event has been sent
        stopMonitoring()
    }

    private fun isUserLoggedIn(): Boolean {
        return sessionManager.getSessionId()?.isNotEmpty() == true
    }

    private fun getTimeoutMillis(): Long {
        var rawValue: String? = null
        var typeHint: String? = null

        prefs.getString(Tags.TIMECONFINGLIST)?.let { json ->
            if (json.isNotEmpty()) {
                val configList: List<SubConfigListDomain> = Utils.g().provideGson().fromJson(
                    json,
                    object : TypeToken<List<SubConfigListDomain>>() {}.type,
                )
                configList.firstOrNull { it.code == "TIMEOUT_TIME" }?.let { item ->
                    rawValue = item.value?.trim()
                    typeHint = item.type?.trim()?.lowercase()
                }
            }
        }

        val numeric = rawValue?.toLongOrNull()
        val millis = when {
            // Explicit type hints from backend
            numeric != null && typeHint?.contains("min") == true -> numeric * 60_000L
            numeric != null && typeHint?.contains("sec") == true -> numeric * 1_000L

            // Heuristic fallback when type is missing/unclear
            // Common configs: 5, 10, 15, 30, 60 (minutes) → treat as minutes
            numeric != null && numeric in 1..120 -> numeric * 60_000L

            // Larger values likely represent seconds (e.g., 300, 1800)
            numeric != null -> numeric * 1_000L

            else -> DEFAULT_TIMEOUT_SECONDS * 1_000L
        }

        printLog("InactivityManager: parsed timeout raw='$rawValue' type='$typeHint' => ${millis}ms")
        return millis
    }

    private fun updateLastInteractionTime() {
        appConfig.updateLastTouchTime(System.currentTimeMillis())
    }
}
