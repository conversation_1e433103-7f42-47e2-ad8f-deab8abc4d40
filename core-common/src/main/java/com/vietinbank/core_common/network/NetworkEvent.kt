package com.vietinbank.core_common.network

import com.vietinbank.core_common.extensions.printLog
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow

/**
 * Global event bus for network connectivity changes.
 * Similar to SessionEvent, this allows any part of the app to observe network state changes.
 */
object NetworkEvent {
    private val _networkLostEvent = MutableSharedFlow<Unit>()
    val networkLostEvent = _networkLostEvent.asSharedFlow()

    private val _networkAvailableEvent = MutableSharedFlow<Unit>()
    val networkAvailableEvent = _networkAvailableEvent.asSharedFlow()

    suspend fun emitNetworkLost() {
        printLog("NetworkEvent: Emitting network lost event")
        try {
            _networkLostEvent.emit(Unit)
            printLog("NetworkEvent: Network lost event emitted successfully")
        } catch (e: Exception) {
            printLog("NetworkEvent: Failed to emit network lost event - ${e.message}")
        }
    }

    suspend fun emitNetworkAvailable() {
        printLog("NetworkEvent: Emitting network available event")
        try {
            _networkAvailableEvent.emit(Unit)
            printLog("NetworkEvent: Network available event emitted successfully")
        } catch (e: Exception) {
            printLog("NetworkEvent: Failed to emit network available event - ${e.message}")
        }
    }
}