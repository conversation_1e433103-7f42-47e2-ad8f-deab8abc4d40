package com.vietinbank.core_common.network

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.os.Build
import android.os.PowerManager
import com.vietinbank.core_common.extensions.printLog
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Service to monitor network connectivity changes.
 * Automatically emits events when network is lost or becomes available.
 */
@Singleton
class NetworkMonitor @Inject constructor(
    @ApplicationContext private val context: Context,
) {
    private val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    private val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
    private var isMonitoring = false
    private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    // Track current network state to avoid duplicate events
    private var wasConnected: Boolean = isNetworkAvailable()

    // Track all active networks
    private val activeNetworks = mutableSetOf<Network>()

    // Debouncing mechanism
    private var networkLostDebounceJob: Job? = null
    private var networkAvailableDebounceJob: Job? = null

    // Debounce delays (milliseconds)
    private val NETWORK_LOST_DEBOUNCE_DELAY = 500L
    private val NETWORK_AVAILABLE_DEBOUNCE_DELAY = 300L

    // Track last known capabilities for comparison
    private val networkCapabilitiesMap = mutableMapOf<Network, NetworkCapabilities>()

    private val networkCallback = object : ConnectivityManager.NetworkCallback() {
        override fun onAvailable(network: Network) {
            super.onAvailable(network)
            val isDozing = isDeviceDozing()
            printLog("NetworkMonitor: Network available - Network: $network, Dozing: $isDozing")

            activeNetworks.add(network)

            // Cancel any pending network lost event
            networkLostDebounceJob?.cancel()
            networkLostDebounceJob = null

            if (!wasConnected && !isDozing) {
                // Debounce network available event
                networkAvailableDebounceJob?.cancel()
                networkAvailableDebounceJob = scope.launch {
                    delay(NETWORK_AVAILABLE_DEBOUNCE_DELAY)
                    if (hasValidInternetConnection()) {
                        wasConnected = true
                        NetworkEvent.emitNetworkAvailable()
                        printLog("NetworkMonitor: Emitted network available event")
                    }
                }
            }
        }

        override fun onLost(network: Network) {
            super.onLost(network)
            val isDozing = isDeviceDozing()
            printLog("NetworkMonitor: Network lost - Network: $network, Dozing: $isDozing")

            activeNetworks.remove(network)
            networkCapabilitiesMap.remove(network)

            // Cancel any pending network available event
            networkAvailableDebounceJob?.cancel()
            networkAvailableDebounceJob = null

            // Only emit network lost if ALL networks are gone and not in doze mode
            if (wasConnected && activeNetworks.isEmpty() && !isDozing) {
                // Debounce network lost event
                networkLostDebounceJob?.cancel()
                networkLostDebounceJob = scope.launch {
                    delay(NETWORK_LOST_DEBOUNCE_DELAY)
                    // Double-check network is still unavailable
                    if (!hasValidInternetConnection()) {
                        wasConnected = false
                        NetworkEvent.emitNetworkLost()
                        printLog("NetworkMonitor: Emitted network lost event")
                    }
                }
            }
        }

        override fun onCapabilitiesChanged(network: Network, networkCapabilities: NetworkCapabilities) {
            super.onCapabilitiesChanged(network, networkCapabilities)

            val previousCapabilities = networkCapabilitiesMap[network]
            val hasSignificantChange = hasSignificantCapabilityChange(previousCapabilities, networkCapabilities)

            networkCapabilitiesMap[network] = networkCapabilities

            if (!hasSignificantChange) {
                printLog("NetworkMonitor: Capabilities changed but no significant change - Network: $network")
                return
            }

            val hasInternet = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
                networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
            val isDozing = isDeviceDozing()

            printLog("NetworkMonitor: Significant capabilities changed - Network: $network, HasInternet: $hasInternet, Dozing: $isDozing")

            if (hasInternet) {
                activeNetworks.add(network)
                if (!wasConnected && !isDozing) {
                    // Cancel any pending network lost event
                    networkLostDebounceJob?.cancel()
                    networkLostDebounceJob = null

                    networkAvailableDebounceJob?.cancel()
                    networkAvailableDebounceJob = scope.launch {
                        delay(NETWORK_AVAILABLE_DEBOUNCE_DELAY)
                        if (hasValidInternetConnection()) {
                            wasConnected = true
                            NetworkEvent.emitNetworkAvailable()
                            printLog("NetworkMonitor: Emitted network available event from capabilities change")
                        }
                    }
                }
            } else {
                activeNetworks.remove(network)
                if (wasConnected && activeNetworks.isEmpty() && !isDozing) {
                    // Cancel any pending network available event
                    networkAvailableDebounceJob?.cancel()
                    networkAvailableDebounceJob = null

                    networkLostDebounceJob?.cancel()
                    networkLostDebounceJob = scope.launch {
                        delay(NETWORK_LOST_DEBOUNCE_DELAY)
                        if (!hasValidInternetConnection()) {
                            wasConnected = false
                            NetworkEvent.emitNetworkLost()
                            printLog("NetworkMonitor: Emitted network lost event from capabilities change")
                        }
                    }
                }
            }
        }
    }

    /**
     * Start monitoring network connectivity changes
     */
    fun startMonitoring() {
        if (!isMonitoring) {
            printLog("NetworkMonitor: Starting network monitoring")
            val request = NetworkRequest.Builder()
                .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                .addCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
                .build()

            try {
                connectivityManager.registerNetworkCallback(request, networkCallback)
                isMonitoring = true

                // Check initial state and emit if no network
                if (!isNetworkAvailable()) {
                    scope.launch {
                        NetworkEvent.emitNetworkLost()
                    }
                }
            } catch (e: Exception) {
                printLog("NetworkMonitor: Failed to start monitoring - ${e.message}")
            }
        }
    }

    /**
     * Stop monitoring network connectivity changes
     */
    fun stopMonitoring() {
        if (isMonitoring) {
            printLog("NetworkMonitor: Stopping network monitoring")
            try {
                connectivityManager.unregisterNetworkCallback(networkCallback)
                isMonitoring = false
            } catch (e: Exception) {
                printLog("NetworkMonitor: Failed to stop monitoring - ${e.message}")
            }
        }
    }

    /**
     * Check if network is currently available
     */
    private fun isNetworkAvailable(): Boolean {
        val network = connectivityManager.activeNetwork ?: return false
        val networkCapabilities = connectivityManager.getNetworkCapabilities(network) ?: return false

        return networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
            networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
    }

    /**
     * Check if device has valid internet connection (considers all networks)
     */
    private fun hasValidInternetConnection(): Boolean {
        // First check active network
        if (isNetworkAvailable()) return true

        // Then check all tracked networks
        return activeNetworks.any { network ->
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            capabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) == true &&
                capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
        }
    }

    /**
     * Check if device is in Doze mode
     */
    private fun isDeviceDozing(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            powerManager.isDeviceIdleMode
        } else {
            false
        }
    }

    /**
     * Check if network capabilities have changed significantly
     */
    private fun hasSignificantCapabilityChange(
        oldCaps: NetworkCapabilities?,
        newCaps: NetworkCapabilities,
    ): Boolean {
        if (oldCaps == null) return true

        // Check significant capabilities that affect connectivity
        val significantCapabilities = listOf(
            NetworkCapabilities.NET_CAPABILITY_INTERNET,
            NetworkCapabilities.NET_CAPABILITY_VALIDATED,
            NetworkCapabilities.NET_CAPABILITY_NOT_SUSPENDED,
        )

        return significantCapabilities.any { capability ->
            oldCaps.hasCapability(capability) != newCaps.hasCapability(capability)
        }
    }
}