package com.vietinbank.core_common.ott

/**
 * Service interface for setting up OTT
 * This interface is defined in core-common to avoid circular dependencies
 * Implementation will be in core-ott module
 */
interface IOttSetupService {
    /**
     * Setup OTT service for the current user
     * This includes Firebase initialization and WebSocket connection
     * Note: Message sync is now a separate operation
     *
     * @param cifNo Customer identification number
     * @param phoneNumber User's phone number
     * @param username User's username
     */
    fun setupOttForUser(
        cifNo: String,
        phoneNumber: String,
        username: String,
    )

    /**
     * Sync OTT messages from server
     * This should be called only when needed (e.g., after login on HomeFragment)
     *
     * @param forceFullSync Whether to force a full sync of all messages
     */
    suspend fun syncOttMessages(forceFullSync: Boolean = true)
}