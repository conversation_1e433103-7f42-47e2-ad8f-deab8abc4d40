package com.vietinbank.core_common.biometric

import android.content.Context
import android.os.Build
import androidx.biometric.BiometricManager
import com.vietinbank.core_common.config.IAppConfigManager
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of IBiometricManager that handles biometric authentication checks.
 * This class encapsulates the Android-specific biometric functionality.
 */
@Singleton
class BiometricManagerImpl @Inject constructor(
    @ApplicationContext private val context: Context,
    private val appConfigManager: IAppConfigManager,
) : IBiometricManager {

    override fun canAuthenticate(): <PERSON>olean {
        val biometricManager = BiometricManager.from(context)
        val result = biometricManager.canAuthenticate(
            BiometricManager.Authenticators.BIOMETRIC_STRONG
                or BiometricManager.Authenticators.DEVICE_CREDENTIAL,
        )
        return result == BiometricManager.BIOMETRIC_SUCCESS
    }

    override fun getStatus(): BiometricStatus {
        // Check if OS version supports biometric (Android 6.0+, API 23+)
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
            return BiometricStatus.NotSupportedOS
        }

        val biometricManager = BiometricManager.from(context)
        val result = biometricManager.canAuthenticate(
            BiometricManager.Authenticators.BIOMETRIC_STRONG
                or BiometricManager.Authenticators.DEVICE_CREDENTIAL,
        )

        return when (result) {
            BiometricManager.BIOMETRIC_ERROR_NONE_ENROLLED -> {
                // Device supports biometric but no fingerprint/PIN/pattern is enrolled
                BiometricStatus.NotConfiguredDevice
            }
            BiometricManager.BIOMETRIC_SUCCESS -> {
                // Check if app has configured biometric (fingerID exists)
                val fingerID = appConfigManager.getFingerID()
                if (fingerID.isEmpty()) {
                    BiometricStatus.NotConfiguredApp
                } else {
                    BiometricStatus.Ready
                }
            }
            else -> {
                // Other errors (HW not available, security update required, etc.)
                // Treat as not supported by OS for simplicity
                BiometricStatus.NotSupportedOS
            }
        }
    }
}