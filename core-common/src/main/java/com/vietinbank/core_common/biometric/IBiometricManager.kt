package com.vietinbank.core_common.biometric

/**
 * Represents the different states of biometric authentication availability
 */
sealed class BiometricStatus {
    /** Device OS does not support biometric authentication (Android < 6.0) */
    object NotSupportedOS : BiometricStatus()

    /** Device supports biometric but no fingerprint/PIN/pattern is enrolled */
    object NotConfiguredDevice : BiometricStatus()

    /** Device has biometric enrolled but app has not configured biometric authentication */
    object NotConfiguredApp : BiometricStatus()

    /** Biometric authentication is fully ready to use */
    object Ready : BiometricStatus()
}

/**
 * Interface for managing biometric authentication functionality.
 * This abstraction allows ViewModels to check biometric availability without depending on Android Context.
 */
interface IBiometricManager {
    /**
     * Checks whether the device supports biometric authentication or device credential (PIN/Pattern).
     * @return true if biometric or device credential authentication is available, false otherwise
     */
    fun canAuthenticate(): Boolean

    /**
     * Gets the detailed status of biometric authentication availability
     * @return BiometricStatus indicating the current state of biometric authentication
     */
    fun getStatus(): BiometricStatus
}