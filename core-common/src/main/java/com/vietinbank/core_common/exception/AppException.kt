package com.vietinbank.core_common.exception

/**
 * Created by vandz on 30/12/24.
 */
sealed class AppException(
    message: String,
    cause: Throwable? = null,
    open val requestId: String? = null,
) : Exception(message, cause) {
    // Lỗi mạng (mất kết nối, không resolve được host, v.v.)
    class NetworkException(
        message: String,
        cause: Throwable? = null,
        override val requestId: String? = null,
        val code: String?,
    ) : AppException(message, cause)

    // Lỗi do server trả về code != "thành công"
    class ApiException(
        val requestPath: String?,
        val rawResponseJson: String?,
        message: String,
        cause: Throwable? = null,
        override val requestId: String? = null,
        val code: String?,
        val subCode: String?,
    ) : AppException(message, cause)

    class HttpException(
        val httpCode: Int,
        val errorBody: String?, // raw body text, nếu muốn
        message: String,
        cause: Throwable? = null,
        override val requestId: String? = null,
    ) : AppException(message, cause)

    // Lỗi mã hoá (encryption/decryption)
    class EncryptionException(
        message: String,
        cause: Throwable? = null,
        override val requestId: String? = null,
        val code: String?,
    ) :
        AppException(message, cause)

    // Lỗi không xác định
    class IllegalArgumentException(
        message: String,
        cause: Throwable? = null,
        override val requestId: String? = null,
        val code: String?,
    ) :
        AppException(message, cause)

    // Lỗi không xác định
    class UnknownException(
        message: String,
        cause: Throwable? = null,
        override val requestId: String? = null,
        val code: String? = "999",
    ) : AppException(message, cause)
}
