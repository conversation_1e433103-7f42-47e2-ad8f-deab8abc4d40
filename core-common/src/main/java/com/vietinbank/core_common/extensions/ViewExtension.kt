package com.vietinbank.core_common.extensions

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.text.Editable
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextPaint
import android.text.TextWatcher
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.text.style.UnderlineSpan
import android.util.Log
import android.view.View
import android.view.ViewTreeObserver
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.annotation.ColorInt
import androidx.core.text.bold
import androidx.core.text.color
import androidx.core.text.inSpans
import androidx.core.text.italic
import androidx.core.text.underline
import androidx.core.view.isVisible
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.HttpException
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.vietinbank.core_common.R
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.utils.SearchUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

fun View.focusAndShowKeyboard() {
    fun View.showTheKeyboardNow() {
        if (isFocused) {
            post {
                val imm =
                    context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                imm.showSoftInput(this, InputMethodManager.SHOW_IMPLICIT)
            }
        }
    }
    requestFocus()
    if (hasWindowFocus()) {
        showTheKeyboardNow()
    } else {
        viewTreeObserver.addOnWindowFocusChangeListener(
            object : ViewTreeObserver.OnWindowFocusChangeListener {
                override fun onWindowFocusChanged(hasFocus: Boolean) {
                    if (hasFocus) {
                        <EMAIL>()
                        viewTreeObserver.removeOnWindowFocusChangeListener(this)
                    }
                }
            },
        )
    }
}

fun View.hideKeyboard() {
    val imm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    imm.hideSoftInputFromWindow(windowToken, 0)
}

fun View.showKeyboardExt() {
    Handler(Looper.getMainLooper()).postDelayed({
        val imm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        this.requestFocus()
        imm.showSoftInput(this, 0)
    }, 100)
}

fun TextView.appendText(
    text: String?,
    bold: Boolean = false,
    underline: Boolean = false,
    italic: Boolean = false,
    @ColorInt color: Int? = null,
    clickable: ((View) -> Unit)? = null,
): TextView {
    val spannable = SpannableStringBuilder().applySpan(
        text ?: "",
        bold,
        underline,
        italic,
        color,
        clickable,
    )
    append(spannable)
    if (clickable != null) {
        movementMethod = LinkMovementMethod.getInstance()
        highlightColor = Color.TRANSPARENT
    }
    return this
}

/**
 * Cho phép chèn 1 ký tự Space vào TextView
 */
fun TextView.appendSpace(): TextView {
    append(" ")
    return this
}

fun SpannableStringBuilder.applySpan(
    text: String,
    bold: Boolean = false,
    underline: Boolean = false,
    italic: Boolean = false,
    @ColorInt colorInt: Int? = null,
    clickable: ((View) -> Unit)? = null,
    builder: SpannableStringBuilder.() -> Unit = {},
): SpannableStringBuilder {
    return when {
        clickable != null -> inSpans(object : ClickableSpan() {
            override fun onClick(view: View) {
                clickable(view)
            }

            override fun updateDrawState(ds: TextPaint) {
                ds.isUnderlineText = underline
            }
        }) {
            applySpan(text, bold, underline, italic, colorInt, null, builder)
        }

        colorInt != null -> color(colorInt) {
            applySpan(text, bold, underline, italic, null, clickable, builder)
        }

        italic -> italic {
            applySpan(text, bold, underline, false, colorInt, clickable, builder)
        }

        underline -> underline {
            applySpan(text, bold, false, italic, colorInt, clickable, builder)
        }

        bold -> bold {
            applySpan(text, false, underline, italic, colorInt, clickable, builder)
        }

        else -> append(text)
    }
}

fun TextView.textHighlight(
    fullText: String = "",
    lightText: String = "",
    bold: Boolean = false,
    underline: Boolean = false,
    italic: Boolean = false,
    @ColorInt color: Int? = null,
    flags: Int = Spannable.SPAN_EXCLUSIVE_EXCLUSIVE,
    onClick: (() -> Unit)? = null,
) {
    try {
        val startHighlights = fullText.lastIndexOf(lightText)
        val endHighlights = startHighlights + lightText.length
        setText(fullText, TextView.BufferType.SPANNABLE)
        val str = text as Spannable
        str.apply {
            // color
            if (color != null) {
                setSpan(ForegroundColorSpan(color), startHighlights, endHighlights, flags)
            }
            // bold
            if (bold) {
                setSpan(StyleSpan(Typeface.BOLD), startHighlights, endHighlights, flags)
            }
            // italic
            if (italic) {
                setSpan(StyleSpan(Typeface.ITALIC), startHighlights, endHighlights, flags)
            }
            // underline
            if (underline) {
                setSpan(UnderlineSpan(), startHighlights, endHighlights, flags)
            }
            // action
            if (onClick != null) {
                str.setSpan(
                    object : ClickableSpan() {
                        override fun onClick(widget: View) {
                            onClick()
                        }

                        override fun updateDrawState(ds: TextPaint) {
                            ds.setUnderlineText(false)
                        }
                    },
                    startHighlights,
                    endHighlights,
                    flags,
                )
                movementMethod = LinkMovementMethod.getInstance()
            }
        }
    } catch (e: Exception) {
    }
}

fun TextView.copyText(textLight: String? = null, copyDescription: String = "") {
    setThrottleClickListener {
        val clipboardManager =
            context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clip = ClipData.newPlainText("Copied String", textLight ?: text)
        clipboardManager.setPrimaryClip(clip)
        Toast.makeText(context, copyDescription, Toast.LENGTH_SHORT)
            .show()
    }
}

fun View?.toBitmap(): Bitmap? {
    if (this == null) return null
    // Define a bitmap with the same size as the view
    // Build cache cho layout
//    this.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED)
//    this.layout(0, 0, this.measuredWidth, this.measuredHeight)
    val returnedBitmap = Bitmap.createBitmap(this.width, this.height, Bitmap.Config.ARGB_8888)
    // Bind a canvas to it
    val canvas = Canvas(returnedBitmap)
    // Get the view's background
    val bgDrawable = this.background
    if (bgDrawable != null) {
        // has background drawable, then draw it on the canvas
        bgDrawable.draw(canvas)
    } else {
        // does not have background drawable, then draw white background on the canvas
        canvas.drawColor(Color.WHITE)
    }
    // draw the view on the canvas
    this.draw(canvas)
    // return the bitmap
    return returnedBitmap
}

fun View?.shareBitmapToApps() {
    if (this != null) {
        try {
            val i = Intent(Intent.ACTION_SEND)
            i.setType("image/*")
            i.putExtra(Intent.EXTRA_STREAM, this.toBitmap().toUri(context))
            context.startActivity(Intent.createChooser(i, "Receipt"))
        } catch (_: Exception) {
        }
    }
}

fun EditText.onSearchTextChanged(
    timeDelay: Long = 0,
    callBack: (String) -> Unit,
) {
    this.addTextChangedListener(object : TextWatcher {
        override fun beforeTextChanged(
            p0: CharSequence?,
            p1: Int,
            p2: Int,
            p3: Int,
        ) {
            // Not used
        }

        override fun onTextChanged(
            p0: CharSequence?,
            p1: Int,
            p2: Int,
            p3: Int,
        ) {
            // Not used
        }

        override fun afterTextChanged(s: Editable?) {
            val currentText = s.toString()
            if (SearchUtil.textSearch != currentText) {
                SearchUtil.jobTextSearch?.cancel()
                SearchUtil.textSearch = currentText
                SearchUtil.jobTextSearch = SearchUtil.debounce(timeDelay) {
                    withContext(Dispatchers.Main) {
                        callBack(currentText)
                    }
                }
            }
        }
    })
}

// image view
fun ImageView.loadUrl(
    url: String?,
    isCache: Boolean = true,
) {
    if (url.isNullOrEmpty()) {
        loadDrawable(R.drawable.ic_launcher_foreground)
        return
    }
    if (!url.startsWith("http") && !url.startsWith("https") && !url.startsWith("file")) {
        loadDrawable(R.drawable.ic_launcher_foreground)
        return
    }
    try {
        Glide.with(context).load(url)
            .diskCacheStrategy(if (isCache) DiskCacheStrategy.ALL else DiskCacheStrategy.NONE)
            .placeholder(R.color.white).error(R.drawable.ic_launcher_foreground)
            .timeout(5000)
            .listener(object : RequestListener<Drawable> {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: com.bumptech.glide.request.target.Target<Drawable>,
                    isFirstResource: Boolean,
                ): Boolean {
                    val errorMessage = when {
                        e?.rootCauses?.any { it is HttpException && it.statusCode == 404 } == true -> "404 Not Found "

                        else -> "Failed to load"
                    }
                    Log.e("ImageLoading", errorMessage, e)
                    return false
                }

                override fun onResourceReady(
                    resource: Drawable,
                    model: Any,
                    target: Target<Drawable>?,
                    dataSource: DataSource,
                    isFirstResource: Boolean,
                ): Boolean {
                    return false
                }
            }).into(this)
    } catch (e: Exception) {
        loadDrawable(R.drawable.ic_launcher_foreground)
    }
}

fun ImageView.loadBitmap(
    bitmap: Bitmap?,
    isCache: Boolean = true,
) {
    Glide
        .with(context)
        .load(bitmap)
        .diskCacheStrategy(
            if (isCache) {
                DiskCacheStrategy.ALL
            } else {
                DiskCacheStrategy.NONE
            },
        )
        .skipMemoryCache(true)
        .placeholder(R.color.white)
        .error(R.drawable.ic_launcher_foreground)
        .into(this)
}

fun ImageView.loadUri(
    uri: Uri?,
    isCache: Boolean = true,
) {
    Glide
        .with(context)
        .load(uri)
        .diskCacheStrategy(
            if (isCache) {
                DiskCacheStrategy.ALL
            } else {
                DiskCacheStrategy.NONE
            },
        ).placeholder(R.color.white)
        .error(R.drawable.ic_launcher_foreground)
        .into(this)
}

fun ImageView.loadDrawable(
    value: Int,
    isCache: Boolean = true,
) {
    Glide
        .with(context)
        .load(value)
        .diskCacheStrategy(
            if (isCache) {
                DiskCacheStrategy.ALL
            } else {
                DiskCacheStrategy.NONE
            },
        )
        .placeholder(R.color.white)
        .error(R.drawable.ic_launcher_foreground)
        .into(this)
}

fun setVisible(isVisible: Boolean, vararg view: View) {
    view.forEach {
        it.isVisible = isVisible
    }
}
