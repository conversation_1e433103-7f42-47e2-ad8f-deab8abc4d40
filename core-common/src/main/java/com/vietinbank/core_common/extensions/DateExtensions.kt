package com.vietinbank.core_common.extensions

import android.annotation.SuppressLint
import android.app.DatePickerDialog
import android.content.Context
import android.view.View
import androidx.core.util.Pair
import com.google.android.material.datepicker.CalendarConstraints
import com.google.android.material.datepicker.DateValidatorPointBackward
import com.google.android.material.datepicker.MaterialDatePicker
import com.vietinbank.core_common.R
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.TimeZone

const val dd_MM_yyyy = "dd-MM-yyyy"
const val dd_MM_yyyy_1 = "dd/MM/yyyy"
const val dd_MM_yyyy_HH_mm = "dd/MM/yyyy HH:mm"
const val dd_MM_yyyy_HH_mm_ss = "dd/MM/yyyy HH:mm:ss"
const val yyyy_MM_dd_HH_mm_ss = "yyyy-MM-dd HH:mm:ss"
const val HH_mm_dd_MM_yyyy = "HH:mm dd/MM/yyyy"
const val yy_MM_dd = "yyMMdd"
const val dd_MM_yyyy_HH_mm_ss_1 = "dd-MM-yyyy HH:mm:ss"
const val EEEE_dd_MMMM_yyyy = "EEEE, dd MMMM yyyy"

@SuppressLint("SimpleDateFormat")
fun getDatePickerInRangeDialog(
    dateRange: Pair<String, String>? = null,
    datePattern: String = dd_MM_yyyy_1,
    onDateSelected: ((String, String) -> Unit)? = null,
): MaterialDatePicker<Pair<Long, Long>> {
    val dateFormat = SimpleDateFormat(datePattern)
    dateFormat.timeZone = TimeZone.getTimeZone("UTC")
    val calendarRange = CalendarConstraints.Builder()
    calendarRange.setValidator(DateValidatorPointBackward.now())
    val builder =
        MaterialDatePicker.Builder.dateRangePicker().setTitleText("Chọn thời gian tìm kiếm")
            .setTheme(R.style.CoreMaterialCalendarTheme)
            .setCalendarConstraints(calendarRange.build())

    if (!dateRange?.first.isNullOrEmpty() && !dateRange.second.isNullOrEmpty()) {
        try {
            builder.setSelection(
                Pair(
                    dateFormat.parse(dateRange.first!!)?.time,
                    dateFormat.parse(dateRange.second!!)?.time,
                ),
            )
        } catch (_: Exception) {
        }
    }
    val datePicker = builder.build()
    datePicker.addOnPositiveButtonClickListener { selection ->
        onDateSelected?.invoke(
            dateFormat.format(selection.first),
            dateFormat.format(selection.second),
        )
    }
    datePicker.addOnNegativeButtonClickListener { view: View? ->
    }
    return datePicker
}

fun todayAsString(format: String = dd_MM_yyyy_1): String {
    val calendar = Calendar.getInstance()
    val sdf = SimpleDateFormat(format, Locale.getDefault())
    return sdf.format(calendar.time)
}

fun getDayAgo(format: String = dd_MM_yyyy_1, dayAgo: Int = 0): String {
    val calendar = Calendar.getInstance()
    calendar.set(Calendar.DAY_OF_MONTH, calendar.get(Calendar.DAY_OF_MONTH) - dayAgo)
    val sdf = SimpleDateFormat(format, Locale.getDefault())
    return sdf.format(calendar.time)
}

fun getNextDay(): String = Calendar.getInstance().apply {
    add(Calendar.DAY_OF_YEAR, 1)
}.time.let {
    SimpleDateFormat(dd_MM_yyyy_1, Locale.getDefault()).format(it)
}

fun getNextDateTime(): String = Calendar.getInstance().apply {
    add(Calendar.DAY_OF_YEAR, 1)
    set(Calendar.HOUR_OF_DAY, 8)
    set(Calendar.MINUTE, 0)
    set(Calendar.SECOND, 0)
}.time.let {
    SimpleDateFormat(dd_MM_yyyy_HH_mm, Locale.getDefault()).format(it)
}

fun lockSoftToTime(format: String = HH_mm_dd_MM_yyyy): String {
    val calendar = Calendar.getInstance()
    calendar.set(Calendar.HOUR, calendar.get(Calendar.HOUR) + 1)
    val sdf = SimpleDateFormat(format, Locale.getDefault())
    return sdf.format(calendar.time)
}

fun String?.getDateToFormat(
    formatFrom: String = dd_MM_yyyy,
    formatTo: String = dd_MM_yyyy_1,
): String? {
    val inputFormat = SimpleDateFormat(formatFrom, Locale.getDefault())
    val outputFormat = SimpleDateFormat(formatTo, Locale.getDefault())
    return try {
        this?.let {
            inputFormat.parse(it)?.let { date ->
                outputFormat.format(Date(date.time))
            }
        } ?: this
    } catch (_: Exception) {
        this
    }
}

fun String?.toTimeInMillis(
    format: String = HH_mm_dd_MM_yyyy,
): Long {
    return try {
        val sdf = SimpleDateFormat(format, Locale.UK).apply {
            timeZone = TimeZone.getTimeZone("UTC") // UTC => không bị lệch múi giờ
        }
        this?.let { sdf.parse(it)?.time } ?: 0
    } catch (_: Exception) {
        0
    }
}

fun Context.showDatePickerDialog(
    onDateSelected: (String) -> Unit,
) {
    val calendar = Calendar.getInstance()
    val today = calendar.clone() as Calendar
    calendar.add(Calendar.DAY_OF_MONTH, 1)
    val minDate = calendar.timeInMillis
    DatePickerDialog(
        this,
        { _, year, monthOfYear, dayOfMonth ->
            val selectedDate = String.format("%02d/%02d/%d", dayOfMonth, monthOfYear + 1, year)
            onDateSelected(selectedDate)
        },
        today.get(Calendar.YEAR),
        today.get(Calendar.MONTH),
        today.get(Calendar.DAY_OF_MONTH),
    ).apply {
        datePicker.minDate = minDate
        show()
    }
}

fun Context.showDateTimePickerDialog(
    onDateTimeSelected: (String) -> Unit,
) {
    val calendar = Calendar.getInstance()
    val today = calendar.clone() as Calendar
    calendar.add(Calendar.DAY_OF_MONTH, 1)
    val minDate = calendar.timeInMillis
    DatePickerDialog(
        this,
        { _, year, monthOfYear, dayOfMonth ->
            showTimeSlotPickerDialog { timeString ->
                val dateTimeString = String.format("%02d/%02d/%d %s", dayOfMonth, monthOfYear + 1, year, timeString)
                onDateTimeSelected(dateTimeString)
            }
        },
        today.get(Calendar.YEAR),
        today.get(Calendar.MONTH),
        today.get(Calendar.DAY_OF_MONTH),
    ).apply {
        datePicker.minDate = minDate
        show()
    }
}

private fun Context.showTimeSlotPickerDialog(
    onTimeSelected: (String) -> Unit,
) {
    val timeSlots = mutableListOf<String>()
    for (hour in 8..15) {
        timeSlots.add(String.format("%02d:00", hour))
        // Add :30 slot except for the last hour (15:30 is included, but not 16:00)
        if (hour < 15 || hour == 15) {
            timeSlots.add(String.format("%02d:30", hour))
        }
    }
    val builder = androidx.appcompat.app.AlertDialog.Builder(this)
    builder.setTitle("Select Time Slot")

    builder.setItems(timeSlots.toTypedArray()) { dialog, which ->
        val selectedTimeSlot = timeSlots[which]
        onTimeSelected(selectedTimeSlot)
        dialog.dismiss()
    }

    builder.setNegativeButton("Cancel") { dialog, _ ->
        dialog.dismiss()
    }

    builder.show()
}

fun Context.showDateAndTimePickerDialog(onDateTimeSelected: (String) -> Unit) {
    showDateTimePickerDialog(onDateTimeSelected)
}

fun Long.toDateString(pattern: String = dd_MM_yyyy_1, locale: Locale = Locale.getDefault()): String {
    val formatter = SimpleDateFormat(pattern, locale)
    return formatter.format(Date(this))
}
