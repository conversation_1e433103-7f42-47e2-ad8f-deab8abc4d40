package com.vietinbank.core_common.extensions

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Environment
import android.provider.MediaStore
import android.util.Base64
import androidx.core.content.FileProvider
import java.io.File
import java.io.FileOutputStream

fun Context.getRealPathFromUri(path: String): Uri? {
    val file = File(path)
    val authority = "$packageName.provider"
    return FileProvider.getUriForFile(this, authority, file)
}

fun Context.getPathFromUri(uri: Uri): String? {
    return try {
        var realPath: String? = null
        contentResolver.query(uri, null, null, null, null)?.use {
            it.moveToFirst()
            val columnIndex = it.getColumnIndex(MediaStore.Images.Media.DATA)
            realPath = it.getString(columnIndex)
        } ?: run {
            // For media not in the media store (e.g., files in external directories)
            val pathSegments = uri.pathSegments
            if (pathSegments.isNotEmpty()) {
                realPath = pathSegments.last()
            } else {
                null
            }
        }

        realPath
    } catch (e: Exception) {
        e.printStackTrace()
        null
    }
}

fun removeFile(filePath: String) {
    val file = File(filePath)
    if (file.exists()) {
        file.delete()
    }
}

fun downloadFile(fileName: String, b64: String?): File {
    var fos: FileOutputStream? = null
    val outputFile = File(
        Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS),
        fileName.plus(".pdf"),
    )
    try {
        fos = FileOutputStream(outputFile)
        fos.write(Base64.decode(b64, Base64.NO_WRAP))
        fos.flush()
    } catch (e: Exception) {
        println(e)
    } finally {
        if (fos != null) {
            try {
                fos.close()
            } catch (e: Exception) {
                println(e)
            }
        }
    }
    return outputFile
}

fun getFileFromName(fileName: String): File? {
    val outputFile = File(
        Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS),
        fileName.plus(".pdf"),
    )
    if (outputFile.exists()) {
        return outputFile
    }
    return null
}

fun Context.getRealPathFromUri(file: File): Uri {
    val authority = "$packageName.provider"
    return FileProvider.getUriForFile(this, authority, file)
}

fun Context.shareFile(file: File, typeFile: String = "application/pdf") {
    val fileUri = getRealPathFromUri(file)
    val share = Intent()
    share.setAction(Intent.ACTION_SEND)
    share.setType(typeFile)
    share.putExtra(Intent.EXTRA_STREAM, fileUri)
    startActivity(share)
}