package com.vietinbank.core_common.extensions

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.provider.MediaStore
import android.provider.OpenableColumns
import android.util.Base64
import android.util.Log
import android.widget.Toast
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.core.graphics.scale
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.utils.VTBLogger
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import kotlin.random.Random

/**
 * Created by vandz on 19/12/24.
 */

fun <T> T.toJsonString(signature: String, requestPath: String, requestId: String): String {
    val gson = Gson()
    val json = gson.toJson(this)
    return json
}

/**
 * Main logging function - delegate to VTBLogger
 */
fun printLog(message: Any?, prefix: String = "") {
    VTBLogger.log(message, prefix)
}

// Error logging
fun printLogError(message: String, prefix: String = "", throwable: Throwable? = null) {
    if (throwable != null) {
        VTBLogger.log(throwable, prefix, Log.ERROR)
    } else {
        VTBLogger.log(message, prefix, Log.ERROR)
    }
}

// Info logging
fun printLogInfo(message: String, prefix: String = "") {
    VTBLogger.log(message, prefix, Log.INFO)
}

// Warning logging
fun printLogWarn(message: String, prefix: String = "") {
    VTBLogger.log(message, prefix, Log.WARN)
}

/**
 * Convert the request to a string before sending it to the server
 */
inline fun <reified T> T.toJsonString(): String {
    return try {
        val gson = GsonBuilder().disableHtmlEscaping().create()
        val type = object : TypeToken<T>() {}.type
        gson.toJson(this, type)
    } catch (e: Exception) {
        e.printStackTrace()
        ""
    }
}

fun Context.toast(
    message: String,
    duration: Int = Toast.LENGTH_SHORT,
) {
    Toast.makeText(this, message, duration).show()
}

fun generateRequestID(): String {
    val charPool: List<Char> = ('A'..'Z') + ('0'..'9')

    return (1..8).map { Random.nextInt(0, charPool.size) }.map(charPool::get).joinToString("")
}

fun Context.openUrl(url: String) {
    val syncIntent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
    startActivity(syncIntent)
}

fun resizeAndConvertUriToBase64(
    context: Context,
    uri: Uri,
    targetWidth: Int = 1920,
    targetHeight: Int = 1080,
): String {
    val contentResolver = context.contentResolver

    // Load the bitmap from URI
    val inputStream = contentResolver.openInputStream(uri)
    val originalBitmap = BitmapFactory.decodeStream(inputStream)
    inputStream?.close()

    // Calculate dimensions while maintaining aspect ratio
    val originalWidth = originalBitmap.width
    val originalHeight = originalBitmap.height

    val aspectRatio = originalWidth.toFloat() / originalHeight.toFloat()

    val width: Int
    val height: Int

    if (aspectRatio > (targetWidth.toFloat() / targetHeight.toFloat())) {
        // Image is wider than target aspect ratio
        width = targetWidth
        height = (targetWidth / aspectRatio).toInt()
    } else {
        // Image is taller than target aspect ratio
        height = targetHeight
        width = (targetHeight * aspectRatio).toInt()
    }

    // Resize the bitmap
    val resizedBitmap = originalBitmap.scale(width, height)

    // Convert the resized bitmap to Base64
    val outputStream = ByteArrayOutputStream()
    resizedBitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)

    val byteArray = outputStream.toByteArray()
    return Base64.encodeToString(byteArray, Base64.DEFAULT)
}

fun getFileNameFromUri(context: Context, uri: Uri): String? {
    var result: String? = null
    if (uri.scheme == "content") {
        val cursor = context.contentResolver.query(uri, null, null, null, null)
        cursor?.use {
            if (it.moveToFirst()) {
                val displayNameIndex = it.getColumnIndex(OpenableColumns.DISPLAY_NAME)
                if (displayNameIndex != -1) {
                    result = it.getString(displayNameIndex)
                }
            }
        }
    }
    if (result == null) {
        // If the query didn't work, try using the path
        result = uri.path
        val cut = result?.lastIndexOf('/')
        if (cut != -1) {
            result = result?.substring(cut!! + 1)
        }
    }
    return result
}

fun isFileSizeValid(context: Context, uri: Uri, sizeMB: Int = 5): Boolean {
    val contentResolver = context.contentResolver

    return try {
        contentResolver.openFileDescriptor(uri, "r")?.use { fileDescriptor ->
            val fileSize = fileDescriptor.statSize
            // Check if file size < 10MB (10 * 1024 * 1024 bytes)
            fileSize < sizeMB * 1024 * 1024
        } ?: false
    } catch (e: IOException) {
        e.printStackTrace()
        false
    }
}

fun convertFileToBase64(context: Context, uri: Uri): String? {
    val contentResolver = context.contentResolver

    return try {
        contentResolver.openInputStream(uri)?.use { inputStream ->
            val bytes = inputStream.readBytes()
            Base64.encodeToString(bytes, Base64.DEFAULT)
        }
    } catch (e: Exception) {
        e.printStackTrace()
        null
    }
}

fun getFileType(context: Context, uri: Uri): String? {
    val contentResolver = context.contentResolver
    return contentResolver.getType(uri)
}

fun Uri?.toBitmap(context: Context?): Bitmap? {
    if (this == null) return null
    return try {
        val inputStream: InputStream? = context?.contentResolver?.openInputStream(this)
        BitmapFactory.decodeStream(inputStream)
    } catch (e: Exception) {
        e.printStackTrace()
        null
    }
}

fun Bitmap?.toUri(context: Context): Uri? {
    if (this == null) return null
    return try {
        val bytes = ByteArrayOutputStream()
        this.compress(Bitmap.CompressFormat.JPEG, 100, bytes)
        val path = MediaStore.Images.Media.insertImage(
            context.contentResolver,
            this,
            "Vietin_QR" + System.currentTimeMillis(),
            null,
        )
        Uri.parse(path)
    } catch (e: Exception) {
        null
    }
}

fun Bitmap?.addImageToGallery(context: Context) {
    try {
        val bytes = ByteArrayOutputStream()
        this?.compress(Bitmap.CompressFormat.JPEG, 100, bytes)
        MediaStore.Images.Media.insertImage(
            context.contentResolver,
            this,
            "Vietin_QR" + System.currentTimeMillis(),
            "",
        )
    } catch (e: Exception) {
        MediaStore.Images.Media.insertImage(
            context.contentResolver,
            this,
            "Vietin_QR" + System.currentTimeMillis(),
            "",
        )
    }

//    if (isShowToast) VtbToastView(
//        context,
//        VtbToastStatus.SUCCESS,
//        context.getString(R.string.vtb_ra_save_success),
//    )
}

fun Activity?.isInstalledApp(uri: String): Boolean {
    var installedApp = false
    try {
        this?.packageManager?.getPackageInfo(uri, 1)
        installedApp = true
    } catch (_: Exception) {
    }
    return installedApp
}

fun saveBase64File(context: Context, base64Data: String, fileName: String): Result<File> {
    return try {
        val decodedBytes = Base64.decode(base64Data, Base64.DEFAULT)

        val directory = context.getExternalFilesDir(null)
            ?: throw NullPointerException("Không tìm được thư mục lưu file")

        val file = File(directory, fileName)

        FileOutputStream(file).use { it.write(decodedBytes) }

        Result.success(file)
    } catch (e: IllegalArgumentException) {
        return Result.failure(IllegalArgumentException("Dữ liệu base64 không hợp lệ", e))
    } catch (e: NullPointerException) {
        return Result.failure(e)
    } catch (e: IOException) {
        return Result.failure(IOException("Không thể ghi dữ liệu vào file", e))
    } catch (e: Exception) {
        return Result.failure(RuntimeException("Lỗi không xác định khi lưu file", e))
    }
}

fun String.buildBoldText(boldWords: List<String>): AnnotatedString {
    return buildAnnotatedString {
        append(this@buildBoldText)
        boldWords.forEach { word ->
            // Dùng Regex để tìm tất cả vị trí xuất hiện (nếu có lặp lại)
            Regex(Regex.escape(word)).findAll(this@buildBoldText).forEach { match ->
                addStyle(
                    style = SpanStyle(fontWeight = FontWeight.Bold),
                    start = match.range.first,
                    end = match.range.last + 1,
                )
            }
        }
    }
}