package com.vietinbank.core_common.multiuser

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.session.CustomEncryptedPrefs
import com.vietinbank.core_common.session.Persistence
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation của IMultiUserStore sử dụng CustomEncryptedPrefs
 * Dữ liệu được lưu với Persistence.PERSISTENT để không bị xóa khi clearAll()
 */
@Singleton
class MultiUserStoreImpl @Inject constructor(
    private val customEncryptedPrefs: CustomEncryptedPrefs,
    private val gson: Gson,
) : IMultiUserStore {

    companion object {
        private const val TAG = "MultiUserStore"
        private const val KEY_MULTI_USER_LIST = "MULTI_USER_LIST_V1"
        private const val DEFAULT_MAX_USERS = 5
        private const val MIGRATION_DONE_KEY = "MULTI_USER_MIGRATION_V1"
    }

    private var migrationChecked = false

    private fun normalize(u: String): String = u.trim().lowercase()

    override fun getAllSavedUsers(): List<IMultiUserStore.SavedUserInfo> {
        return try {
            val json = customEncryptedPrefs.getString(KEY_MULTI_USER_LIST, encrypted = true)
            printLog(TAG, "[DEBUG] getAllSavedUsers - Raw JSON: ${json?.take(100)}...")
            if (json.isNullOrEmpty()) {
                printLog(TAG, "[DEBUG] getAllSavedUsers - No saved users found (empty JSON)")
                emptyList()
            } else {
                val type = object : TypeToken<List<IMultiUserStore.SavedUserInfo>>() {}.type
                val users: List<IMultiUserStore.SavedUserInfo> = gson.fromJson(json, type) ?: emptyList()
                printLog(TAG, "[DEBUG] getAllSavedUsers - Loaded ${users.size} users: ${users.map { it.username }}")

                // Migration: Re-save với PERSISTENT flag nếu có data (one-time migration)
                if (users.isNotEmpty() && !migrationChecked) {
                    migrationChecked = true
                    val migrationDone = customEncryptedPrefs.getBoolean(MIGRATION_DONE_KEY, false, encrypted = false)

                    if (!migrationDone) {
                        try {
                            saveUserList(users) // Sẽ lưu với PERSISTENT
                            customEncryptedPrefs.setBoolean(
                                key = MIGRATION_DONE_KEY,
                                value = true,
                                encrypted = false,
                                persistence = Persistence.PERSISTENT,
                            )
                            printLog(TAG, "Migrated ${users.size} users to PERSISTENT storage")
                        } catch (e: Exception) {
                            printLog(TAG, "Migration failed but data preserved: ${e.message}")
                        }
                    }
                }

                // Sắp xếp theo thời gian đăng nhập gần nhất
                users.sortedByDescending { it.lastLoginTime }
            }
        } catch (e: Exception) {
            printLog(TAG, "Error loading users: ${e.message}")
            emptyList()
        }
    }

    override fun getUserByUsername(username: String): IMultiUserStore.SavedUserInfo? {
        return try {
            val target = normalize(username)
            getAllSavedUsers().find { normalize(it.username) == target }
        } catch (e: Exception) {
            printLog(TAG, "Error getting user $username: ${e.message}")
            null
        }
    }

    override fun saveOrUpdateUser(userInfo: IMultiUserStore.SavedUserInfo, maxUsers: Int) {
        try {
            printLog(TAG, "[DEBUG] saveOrUpdateUser - Starting save for user: ${userInfo.username}")
            val currentList = getAllSavedUsers().toMutableList()
            printLog(TAG, "[DEBUG] saveOrUpdateUser - Current list has ${currentList.size} users")

            // Xóa user cũ nếu đã tồn tại (so khớp không phân biệt hoa thường, bỏ khoảng trắng)
            val target = normalize(userInfo.username)
            currentList.removeAll { normalize(it.username) == target }

            // Thêm user mới với lastLoginTime được cập nhật
            val updatedUser = userInfo.copy(lastLoginTime = System.currentTimeMillis())
            currentList.add(0, updatedUser)

            // Giới hạn số lượng user
            val limitedList = currentList.take(maxUsers)
            printLog(TAG, "[DEBUG] saveOrUpdateUser - Will save ${limitedList.size} users: ${limitedList.map { it.username }}")

            // Lưu danh sách
            saveUserList(limitedList)

            printLog(TAG, "[DEBUG] saveOrUpdateUser - User ${userInfo.username} saved/updated successfully")
        } catch (e: Exception) {
            printLog(TAG, "Error saving user: ${e.message}")
        }
    }

    override fun removeUser(username: String): Boolean {
        return try {
            val currentList = getAllSavedUsers()
            val target = normalize(username)
            val updatedList = currentList.filter { normalize(it.username) != target }

            if (updatedList.size != currentList.size) {
                saveUserList(updatedList)
                printLog(TAG, "User $username removed from saved list")
                true
            } else {
                false
            }
        } catch (e: Exception) {
            printLog(TAG, "Error removing user: ${e.message}")
            false
        }
    }

    override fun clearAll() {
        try {
            // Xóa cả key chính và key migration
            customEncryptedPrefs.remove(KEY_MULTI_USER_LIST, encrypted = true)
            customEncryptedPrefs.remove(MIGRATION_DONE_KEY, encrypted = false)
            migrationChecked = false
            printLog(TAG, "All saved users cleared")
        } catch (e: Exception) {
            printLog(TAG, "Error clearing users: ${e.message}")
        }
    }

    override fun updateLastLoginTime(username: String) {
        try {
            val user = getUserByUsername(username)
            if (user != null) {
                val updatedUser = user.copy(lastLoginTime = System.currentTimeMillis())
                saveOrUpdateUser(updatedUser)
                printLog(TAG, "Last login time updated for user: $username")
            }
        } catch (e: Exception) {
            printLog(TAG, "Error updating last login time: ${e.message}")
        }
    }

    /**
     * Lưu danh sách user với PERSISTENT flag
     * Đảm bảo data không bị xóa khi clearAll() được gọi
     */
    private fun saveUserList(users: List<IMultiUserStore.SavedUserInfo>) {
        try {
            val json = gson.toJson(users)
            printLog(TAG, "[DEBUG] saveUserList - Saving ${users.size} users with PERSISTENT flag")
            printLog(TAG, "[DEBUG] saveUserList - Users to save: ${users.map { it.username }}")
            customEncryptedPrefs.setString(
                key = KEY_MULTI_USER_LIST,
                value = json,
                encrypted = true,
                persistence = Persistence.PERSISTENT, // Quan trọng: Đánh dấu PERSISTENT
            )
            printLog(TAG, "[DEBUG] saveUserList - Successfully saved with PERSISTENT flag")
        } catch (e: Exception) {
            printLog(TAG, "[ERROR] saveUserList - Error saving user list: ${e.message}")
        }
    }
}