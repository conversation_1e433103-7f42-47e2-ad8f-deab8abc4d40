package com.vietinbank.core_common.multiuser

/**
 * Interface để quản lý lưu trữ thông tin nhiều user cho tính năng switch user
 * Dữ liệu được lưu trữ độc lập và không bị xóa khi logout/login user khác
 */
interface IMultiUserStore {
    /**
     * Model lưu trữ thông tin user
     * @property username Tên đăng nhập
     * @property fullName Tên đầy đủ của user
     * @property corpName Tên công ty (nếu có)
     * @property roleId ID vai trò (MAKER/CHECKER)
     * @property lastLoginTime Thời gian đăng nhập gần nhất
     */
    data class SavedUserInfo(
        val username: String,
        val fullName: String?,
        val corpName: String? = null,
        val roleId: String? = null,
        val lastLoginTime: Long = System.currentTimeMillis(),
    )

    /**
     * Lấy danh sách tất cả user đã lưu
     * @return Danh sách user đư<PERSON><PERSON> sắp xếp theo thời gian đăng nhập gần nhất
     */
    fun getAllSavedUsers(): List<SavedUserInfo>

    /**
     * Lấy thông tin user theo username
     * @param username Tên đăng nhập cần tìm
     * @return Thông tin user hoặc null nếu không tìm thấy
     */
    fun getUserByUsername(username: String): SavedUserInfo?

    /**
     * Lưu hoặc cập nhật thông tin user
     * @param userInfo Thông tin user cần lưu
     * @param maxUsers Số lượng user tối đa được lưu (mặc định 5)
     */
    fun saveOrUpdateUser(userInfo: SavedUserInfo, maxUsers: Int = 5)

    /**
     * Xóa thông tin user
     * @param username Tên đăng nhập cần xóa
     * @return true nếu xóa thành công
     */
    fun removeUser(username: String): Boolean

    /**
     * Xóa toàn bộ danh sách user đã lưu
     * Chỉ nên sử dụng khi cần reset hoàn toàn
     */
    fun clearAll()

    /**
     * Cập nhật thời gian đăng nhập gần nhất
     * @param username Tên đăng nhập
     */
    fun updateLastLoginTime(username: String)
}