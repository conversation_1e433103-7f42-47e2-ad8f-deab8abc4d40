package com.vietinbank.core_common.nav

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Created by vand<PERSON> on 13/6/25.
 */
@Singleton
class NavigationEventManager @Inject constructor() {

    private val _pendingOttNavigation = MutableStateFlow<OttNavigationEvent?>(null)
    val pendingOttNavigation: StateFlow<OttNavigationEvent?> = _pendingOttNavigation

    data class OttNavigationEvent(
        val messageId: String,
        val timestamp: Long = System.currentTimeMillis(),
    )

    fun setPendingOttNavigation(messageId: String) {
        _pendingOttNavigation.value = OttNavigationEvent(messageId)
    }

    fun consumePendingNavigation(): OttNavigationEvent? {
        val event = _pendingOttNavigation.value
        _pendingOttNavigation.value = null
        return event
    }

    fun hasPendingNavigation(): Boolean = _pendingOttNavigation.value != null
}