package com.vietinbank.core_common.nav

import android.os.Bundle

/**
 * Created by van<PERSON><PERSON> on 27/2/25.
 */
interface IAppNavigator {
    fun goToHome()
    fun popToHomeFromAccount()
    fun goToHomeTest()
    fun goToEditShortcut()
    fun goToAccountFromLogin(isFromLogin: Boolean)
    fun goToAccountNoAnm(assetModel: String, creditModel: String)
    fun goToLogin()
    fun goToLoginAndPopAll(sessionExpiredMessage: String? = null)
    fun goToLoginFromSplash()
    fun goToHomePreLogin()
    fun goToHomePreLoginFromSplash()
    fun goToHomeFrom2FA()
    fun goToHomePreLoginAndPopAll()
    fun goToActive()
    fun goToApprovalList(
        transaction: String,
        tranType: String,
        groupType: String,
        serviceType: String,
        isBatchFileMode: Boolean,
    )

    fun popToApprovalList(clearBackStack: Boolean = true)

    fun goToInitTransaction(transaction: String)
    fun goToConfirmChecker(
        transaction: String,
        paymentMethod: String,
        confirmType: String,
        dataType: String,
    )

    fun goToConfirmOTP(
        transaction: String,
        transactionID: String,
        originalTransaction: String,
        token: String,
        step: Int,
        nextApprover: String,
        confirmType: String,
        mtId: String,
        ekycId: String = "",
    )

    fun goToConfirmKeypass(
        challengeCode: String,
        originalTransaction: String,
        confirmType: String,
        transaction: String,
        nextApprover: String,
        mtId: String,
        ekycId: String = "",
    )

    fun gotoSuccessChecker(successList: String, confirmRes: String, confirmType: String)
    fun navigateUp() // optional
    fun popBackStack() // optional
    fun goToViewPagerTransferFragment(bundle: Bundle)
    fun goToMakerTransfer(bundle: Bundle)
    fun goToMakerTransferBankChoosed(typeTransfer: String, itemStringObj: String)
    fun goToMakerTransferCard(typeTransfer: String)
    fun goToMakerTransferConfirm(typeTransfer: String, validateItem: String)
    fun goToMakerTransferResult(typeTransfer: String, confirmItem: String, resultItem: String)
    fun goToPaymentOrderFragment() // lệnh chi
    fun goToMakerCreateTransferAccountFragment()
    fun goToMakerConfirmTransferFragment(bundle: Bundle)
    fun goToMakerResultTransferFragment(bundle: Bundle)
    fun goToActive2FAStep1Fragment(bundle: Bundle)
    fun goToActive2FAStep2Fragment(bundle: Bundle)

    fun setFragmentResult(requestKey: String, result: Bundle)

    fun gotoKeypassMain()
    fun gotoActivSoft()
    fun goToEnterPIN(flowPin: String) // nhap pin
    fun gotoHomeSoft()

    // quan ly giao dich
    fun gotoTransactionManager()
    fun gotoFilterTransaction()
    fun goToinquiryApproverListFragment(bundle: Bundle) // tra soát

    // van tin tai khoan
    fun goToInquiryAccount()

    // tra soat
    fun goToTracePaymentFragment(bundle: Bundle)
    fun goToTracePaymentDetailFragment(bundle: Bundle)
    fun goToTracePaymentComfirmFragment(bundle: Bundle)

    // bao cao giao dich
    fun goToDashBoardReportFragment(bundle: Bundle)
    fun gotoListRejectTransferDetailFragment(bundle: Bundle)

    // dang ky smart CA
    fun goToListSmartCA()

    // giai ngan bao lanh
    fun goToDisbursement()

    // Quan ly ott
    fun goToOttDashboard()
    fun goToDashboardFeatureOTT(flowSetting: String)
    fun goToOttDashboard(messageId: String? = null)
    fun goToRegisterOTT(tag: String, flowSetting: String)
    fun goToValidateOTT(tag: String, alertType: String? = null)
    fun goToConfirmOTT()
    fun goToConfirmOTTLoan()
    fun goToConfirmSmsTransform(alertType: String, tagRoute: String)
    fun goToTransactionStatus()

    // ekyc
    fun gotoUpdateEkyc()
    fun gotoUpdateEkycUserInfo()
    fun goToConfirmUpdateEkycScreen()
    fun goToRegisterEkycScreen()

    fun goToSetting()
    fun goToAccountBalanceSetting(accountType: String)

    // scan qr transfer
    fun goToMakerQRTransferFragment()

    // lock user
    fun goToLockAccount(bundle: Bundle?)
    fun goToLockConfirmAccount(bundle: Bundle?)
    fun goToLockResultAccount(bundle: Bundle?)
    fun popToLogin(clearBackStack: Boolean)

    // duyet nhieu giao dich
    fun goToMultipleApprovalList(
        tranType: String,
        groupType: String,
        serviceType: String,
    )
    fun goToMultipleConfirmApproval(bundle: Bundle?)
    fun goToMultipleResultApproval(bundle: Bundle?)
}
