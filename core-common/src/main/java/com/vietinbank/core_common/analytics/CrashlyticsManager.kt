package com.vietinbank.core_common.analytics

import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.session.IUserProf
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Created by vandz on 4/6/25.
 */
@Singleton
class CrashlyticsManager @Inject constructor(
    private val userProf: IUserProf,
) {

    private val crashlytics: FirebaseCrashlytics by lazy {
        FirebaseCrashlytics.getInstance()
    }

    companion object {
        private const val TAG = "CrashlyticsManager"
        private const val USER_ID_SEPARATOR = "_"
        private const val USER_ID_UNKNOWN = "unknown"
        private const val MIN_VISIBLE_CHARS = 3
        private const val MASK_SEPARATOR = "***"
        private const val FIELD_SEPARATOR = "_"
        private const val USER_PREFIX = "USR"
        private const val CIF_PREFIX = "CIF"
        private const val FALLBACK_VALUE = "XXX"
    }

    /**
     * Set user identifier cho Crashlytics
     * Format: {username}_{cifno}
     * <PERSON><PERSON><PERSON><PERSON> gọ<PERSON> sau khi user login thành công
     */
    fun setUserIdentifier() {
        try {
            val userId = userProf.getUserId() ?: USER_ID_UNKNOWN
            val cifNo = userProf.getCifNo() ?: USER_ID_UNKNOWN
            val userIdentifier = generateMaskedUserId(userId, cifNo)

            printLog("$TAG: Setting user identifier: $userIdentifier")
            crashlytics.setUserId(userIdentifier)

            // Set custom keys để có thêm thông tin chi tiết
            setCustomKeys()

            // Log event
            log("User identifier set successfully")
        } catch (e: Exception) {
            printLog("$TAG: Error setting user identifier: ${e.message}")
            // Silent fail - không làm crash app vì Crashlytics
            recordException(e, "Failed to set user identifier")
        }
    }

    private fun generateMaskedUserId(userId: String?, cifNo: String?): String {
        // Xử lý null và empty
        val safeUserId = userId?.trim() ?: ""
        val safeCifNo = cifNo?.trim() ?: ""

        // Nếu cả 2 đều empty, return default
        if (safeUserId.isEmpty() && safeCifNo.isEmpty()) {
            return "${USER_PREFIX}${FALLBACK_VALUE}${FIELD_SEPARATOR}${CIF_PREFIX}${FALLBACK_VALUE}"
        }

        val maskedUserId = maskValue(safeUserId, USER_PREFIX)
        val maskedCifNo = maskValue(safeCifNo, CIF_PREFIX)

        return "${maskedUserId}${FIELD_SEPARATOR}$maskedCifNo"
    }

    private fun maskValue(value: String, prefix: String): String {
        return when {
            value.isEmpty() -> {
                // Empty string
                "$prefix$FALLBACK_VALUE"
            }
            value.length <= MIN_VISIBLE_CHARS -> {
                // Không đủ ký tự để mask, hiển thị toàn bộ
                "$prefix$value"
            }
            value.length <= MIN_VISIBLE_CHARS * 2 -> {
                // Có 4-6 ký tự, chỉ mask giữa
                val visibleChars = value.length / 3
                val start = value.take(visibleChars)
                val end = value.takeLast(visibleChars)
                "$prefix$start$MASK_SEPARATOR$end"
            }
            else -> {
                val start = value.take(MIN_VISIBLE_CHARS)
                val end = value.takeLast(MIN_VISIBLE_CHARS)
                "$prefix$start$MASK_SEPARATOR$end"
            }
        }
    }

    /**
     * Clear user identifier khi logout
     */
    fun clearUserIdentifier() {
        try {
            printLog("$TAG: Clearing user identifier")
            crashlytics.setUserId("")

            // Clear all custom keys
            clearCustomKeys()

            // Log event
            log("User identifier cleared")
        } catch (e: Exception) {
            printLog("$TAG: Error clearing user identifier: ${e.message}")
            // Silent fail
        }
    }

    /**
     * Set custom keys để track thêm thông tin user
     */
    private fun setCustomKeys() {
        try {
            crashlytics.apply {
                // User basic info
                val userId = userProf.getUserId()
                val cifNo = userProf.getCifNo()

                setCustomKey("masked_id", generateMaskedUserId(userId ?: "", cifNo ?: ""))
                setCustomKey("user_prefix", userId?.take(3) ?: "N/A")
                setCustomKey("cif_prefix", cifNo?.take(3) ?: "N/A")

                // Role information
                userProf.getRoleId()?.let { setCustomKey("user_role", it) }
                setCustomKey("is_checker", userProf.isChecker())

                // Group and level info
                userProf.getGroupType()?.let { setCustomKey("group_type", it) }
                userProf.getRoleLevel()?.let { setCustomKey("role_level", it) }

                // User status
                userProf.getCorpUserStatus()?.let { setCustomKey("user_status", it) }
            }
        } catch (e: Exception) {
            printLog("$TAG: Error setting custom keys: ${e.message}")
            FirebaseCrashlytics.getInstance().setUserId("ERROR_GENERATING_ID")
            FirebaseCrashlytics.getInstance().recordException(e)
        }
    }

    /**
     * Clear all custom keys
     */
    private fun clearCustomKeys() {
        try {
            crashlytics.apply {
                setCustomKey("masked_id", "")
                setCustomKey("user_prefix", "")
                setCustomKey("cif_prefix", "")
                setCustomKey("user_role", "")
                setCustomKey("is_checker", false)
                setCustomKey("group_type", "")
                setCustomKey("role_level", "")
                setCustomKey("user_status", "")
            }
        } catch (e: Exception) {
            printLog("$TAG: Error clearing custom keys: ${e.message}")
        }
    }

    /**
     * Log exception với context
     * @param throwable Exception cần log
     * @param context Context của exception
     */
    private fun recordException(throwable: Throwable, context: String? = null) {
        try {
            context?.let {
                crashlytics.log("Context: $it")
            }
            crashlytics.recordException(throwable)
            printLog("$TAG: Exception recorded: ${throwable.message}")
        } catch (e: Exception) {
            printLog("$TAG: Error recording exception: ${e.message}")
            // Silent fail
        }
    }

    /**
     * Log custom message
     * @param message Message cần log
     */
    fun log(message: String) {
        try {
            crashlytics.log(message)
            printLog("$TAG: $message")
        } catch (e: Exception) {
            printLog("$TAG: Error logging message: ${e.message}")
            // Silent fail
        }
    }

    /**
     * Set custom key-value pair
     * @param key Key name
     * @param value Value (String, Boolean, Int, Long, Float, Double)
     */
    fun setCustomKey(key: String, value: Any) {
        try {
            when (value) {
                is String -> crashlytics.setCustomKey(key, value)
                is Boolean -> crashlytics.setCustomKey(key, value)
                is Int -> crashlytics.setCustomKey(key, value)
                is Long -> crashlytics.setCustomKey(key, value)
                is Float -> crashlytics.setCustomKey(key, value)
                is Double -> crashlytics.setCustomKey(key, value)
                else -> crashlytics.setCustomKey(key, value.toString())
            }
        } catch (e: Exception) {
            printLog("$TAG: Error setting custom key: ${e.message}")
        }
    }

    /**
     * Force a test crash (for testing only)
     * Should be removed or wrapped in debug check for production
     */
    fun testCrash() {
        log("Test crash initiated")
        throw RuntimeException("Test Crash from CrashlyticsManager")
    }
}