# Keep public API only
-keep public class com.vietinbank.core_common.** {
    public protected *;
}

# Keep essential classes
-keep class com.vietinbank.core_common.livedata.SingleLiveEvent { *; }
-keep class com.vietinbank.core_common.session.** { *; }
-keep class com.vietinbank.core_common.config.** { *; }
-keep class com.vietinbank.core_common.environment.** { *; }
-keep class com.vietinbank.core_common.constants.** { *; }
-keep class com.vietinbank.core_common.exception.** { *; }