plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id('kotlin-kapt')
    alias(libs.plugins.compose.compiler)
}

android {
    namespace 'com.vietinbank.core_common'
    compileSdkVersion libs.versions.compileSdk.get().toInteger()
    defaultConfig {
        minSdkVersion libs.versions.minSdk.get().toInteger()
        targetSdkVersion libs.versions.targetSdk.get().toInteger()
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            consumerProguardFiles "consumer-rules.pro"
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = '11'
    }
}

dependencies {

    implementation libs.androidx.runtime.livedata
    implementation(libs.coroutines.stdlib)

    implementation(libs.gson)

    implementation libs.androidx.biometric

    // Coroutines
    implementation(libs.coroutines.core)
    implementation(libs.coroutines.android)

    implementation(libs.androidx.appcompat)
    implementation(libs.androidx.appcompat.resources)
    implementation(libs.material)

    // Hilt
    implementation(libs.hilt.android)
    implementation(platform(libs.firebase.bom))
    implementation(libs.firebase.crashlytics)
    kapt(libs.hilt.compiler)

    implementation(libs.security.crypto)

    // Testing
    testImplementation "junit:junit:4.13.2"
    testImplementation "io.mockk:mockk:1.13.3"
    implementation(libs.glide)
    implementation libs.androidx.ui.unit.android
    implementation libs.compose.ui

}
