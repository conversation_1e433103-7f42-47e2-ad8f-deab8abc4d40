package com.vietinbank.core_ott.queue

import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_domain.models.ott.OttMessage
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentLinkedQueue
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Queue manager for batching OTT message inserts to improve performance
 * when receiving multiple messages simultaneously
 */
@Singleton
class OttMessageQueue @Inject constructor() {

    private val messageQueue = ConcurrentLinkedQueue<OttMessage>()
    private var scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    // Configuration
    private val BATCH_SIZE = 50
    private val BATCH_DELAY_MS = 500L // Wait up to 500ms to collect messages

    // State
    private val _pendingCount = MutableStateFlow(0)
    val pendingCount = _pendingCount.asStateFlow()

    private var batchJob: Job? = null

    /**
     * Add a message to the queue for batch processing
     */
    fun enqueue(message: OttMessage) {
        messageQueue.offer(message)
        _pendingCount.value = messageQueue.size

        printLog("OttMessageQueue: Enqueued message ${message.messageId}, queue size: ${messageQueue.size}")

        // Start or restart batch processing
        scheduleBatchProcess()
    }

    /**
     * Add multiple messages to the queue
     */
    fun enqueueAll(messages: List<OttMessage>) {
        messageQueue.addAll(messages)
        _pendingCount.value = messageQueue.size

        printLog("OttMessageQueue: Enqueued ${messages.size} messages, queue size: ${messageQueue.size}")

        scheduleBatchProcess()
    }

    /**
     * Get and remove a batch of messages from the queue
     */
    fun dequeueBatch(): List<OttMessage> {
        val batch = mutableListOf<OttMessage>()

        // Take up to BATCH_SIZE messages
        repeat(BATCH_SIZE) {
            messageQueue.poll()?.let { batch.add(it) } ?: return@repeat
        }

        _pendingCount.value = messageQueue.size
        printLog("OttMessageQueue: Dequeued ${batch.size} messages, remaining: ${messageQueue.size}")

        return batch
    }

    /**
     * Schedule batch processing with debouncing
     */
    private fun scheduleBatchProcess() {
        // Cancel previous job
        batchJob?.cancel()

        // Schedule new batch processing
        batchJob = scope.launch {
            delay(BATCH_DELAY_MS)
            processBatch()
        }
    }

    /**
     * Process messages immediately if queue is getting too large
     */
    fun processIfNeeded() {
        if (messageQueue.size >= BATCH_SIZE) {
            scope.launch {
                processBatch()
            }
        }
    }

    /**
     * Set the batch processor callback
     */
    var batchProcessor: (suspend (List<OttMessage>) -> Unit)? = null

    private suspend fun processBatch() {
        printLog("OttMessageQueue: Processing batch, queue size: ${messageQueue.size}")
        val batch = dequeueBatch()
        if (batch.isNotEmpty()) {
            printLog("OttMessageQueue: Processing ${batch.size} messages")
            try {
                if (batchProcessor != null) {
                    batchProcessor?.invoke(batch)
                    printLog("OttMessageQueue: Batch processor completed")
                } else {
                    printLog("OttMessageQueue: ERROR - No batch processor set!")
                }
            } catch (e: Exception) {
                printLog("OttMessageQueue: ERROR processing batch: ${e.message}")
                e.printStackTrace()
                // Could implement retry logic here
            }

            // Continue processing if more messages remain
            if (messageQueue.isNotEmpty()) {
                printLog("OttMessageQueue: Scheduling next batch, remaining: ${messageQueue.size}")
                scheduleBatchProcess()
            }
        } else {
            printLog("OttMessageQueue: Batch is empty")
        }
    }

    /**
     * Process all remaining messages immediately
     */
    suspend fun flush() {
        while (messageQueue.isNotEmpty()) {
            processBatch()
        }
    }

    /**
     * Clear the queue without processing
     */
    fun clear() {
        messageQueue.clear()
        _pendingCount.value = 0
        batchJob?.cancel()
    }

    /**
     * Clean up resources
     */
    fun cleanup() {
        printLog("OttMessageQueue: Cleaning up - cancelling scope and clearing queue")
        scope.cancel()
        clear()

        // Create new scope for next usage
        scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
        printLog("OttMessageQueue: Created new scope for next usage")
    }
}