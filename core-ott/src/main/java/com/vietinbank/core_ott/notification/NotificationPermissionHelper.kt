package com.vietinbank.core_ott.notification

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Created by vandz on 19/4/25.
 */

@Singleton
class NotificationPermissionHelper @Inject constructor(
    private val context: Context,
) {
    companion object {
        private const val REQUEST_NOTIFICATION_PERMISSION = 123
    }

    /**
     * Kiểm tra xem app đã có quyền hiển thị thông báo chưa
     */
    fun hasNotificationPermission(): <PERSON>olean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.POST_NOTIFICATIONS,
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            true // Trước Android 13 không cần quyền riêng
        }
    }

    /**
     * Yêu cầu quyền thông báo từ Activity
     */
    fun requestPermissionFromActivity(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (!hasNotificationPermission()) {
                ActivityCompat.requestPermissions(
                    activity,
                    arrayOf(Manifest.permission.POST_NOTIFICATIONS),
                    REQUEST_NOTIFICATION_PERMISSION,
                )
            }
        }
    }

    /**
     * Thiết lập launcher cho Fragment để yêu cầu quyền
     */
    fun createPermissionLauncher(activity: AppCompatActivity): ActivityResultLauncher<String> {
        return activity.registerForActivityResult(
            ActivityResultContracts.RequestPermission(),
        ) { isGranted: Boolean ->
            // Callback khi quyền được cấp hoặc từ chối
        }
    }

    /**
     * Mở cài đặt ứng dụng để người dùng có thể bật quyền thông báo
     */
    fun openNotificationSettings(activity: Activity) {
        val intent = Intent().apply {
            action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
            data = Uri.fromParts("package", activity.packageName, null)
        }
        activity.startActivity(intent)
    }
}