package com.vietinbank.core_ott.notification

import android.annotation.SuppressLint
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.extensions.printLogError
import com.vietinbank.core_domain.models.ott.OttMessage
import com.vietinbank.core_domain.ott.INotificationHandler
import com.vietinbank.core_ott.R
import javax.inject.Inject

/**
 * Implementation of the notification handler
 */
class NotificationHandlerImpl @Inject constructor(
    private val context: Context,
) : INotificationHandler {

    override fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = context.getString(R.string.ott_notification_channel_name)
            val descriptionText = context.getString(R.string.ott_notification_channel_desc)
            val importance = NotificationManager.IMPORTANCE_HIGH

            val channel = NotificationChannel(CHANNEL_ID, name, importance).apply {
                description = descriptionText
                enableLights(true)
                lightColor = Color.GREEN // Green for banking app
                enableVibration(true)
                vibrationPattern = longArrayOf(0, 1000, 500, 1000) // Strong vibration pattern
                lockscreenVisibility = Notification.VISIBILITY_PUBLIC
                setShowBadge(true)
                setBypassDnd(true) // Important notifications bypass DND
            }

            val notificationManager =
                context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
            printLog("OTT: Notification channel created with high importance", TAG)
        }
    }

    @SuppressLint("MissingPermission")
    override fun showNotification(message: OttMessage) {
        try {
            // Create an intent for when the notification is tapped
            val intent = Intent().apply {
                // Target MainActivity for foreground notifications
                setClassName(context.packageName, "com.vietinbank.newcore.ui.MainActivity")
                action = "com.vietinbank.action.OTT_NOTIFICATION"
                // Put full message data for background case
                putExtra("ott_message_id", message.messageId)
                putExtra("ott_message_type", message.type)
                putExtra("ott_message_title", message.title)
                putExtra("ott_message_content", message.content)
                putExtra("ott_message_send_time", message.sendTime)
                putExtra("ott_message_message_type", message.messageType)
                putExtra("ott_message_timestamp", message.timestamp)
                putExtra("ott_message_state", message.state)

                // Add ottData as JSON string for MainActivity to process
                val ottDataJson = org.json.JSONObject().apply {
                    put("messageId", message.messageId)
                    put("type", message.messageType)
                    put("title", message.title)
                    put("content", message.content)
                    put("sendTime", message.sendTime)
                    put("timestamp", message.timestamp)
                    put("priority", message.priority ?: "")
                    put("serverStatus", message.serverStatus ?: "SENT")
                }.toString()
                putExtra("ottData", ottDataJson)

                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP)
            }

            val pendingIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                PendingIntent.getActivity(
                    context,
                    message.messageId.hashCode(),
                    intent,
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE,
                )
            } else {
                PendingIntent.getActivity(
                    context,
                    message.messageId.hashCode(),
                    intent,
                    PendingIntent.FLAG_UPDATE_CURRENT,
                )
            }

            val bigTextStyle = NotificationCompat.BigTextStyle()
                .bigText(message.content)
                .setBigContentTitle(message.title)

            val builder = NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(R.mipmap.ic_app)
                .setContentTitle(message.title)
                .setContentText(message.content)
                .setStyle(bigTextStyle)
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setAutoCancel(true)
                .setContentIntent(pendingIntent)
                .setGroup(GROUP_KEY)
                .setCategory(NotificationCompat.CATEGORY_MESSAGE)
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                .setDefaults(NotificationCompat.DEFAULT_ALL) // Sound, vibrate, lights
                .setWhen(message.timestamp)
                .setShowWhen(true)
                .setLocalOnly(true) // Security: not shared to wearables

            val notificationManager = NotificationManagerCompat.from(context)

            // Show individual notification
            notificationManager.notify(message.messageId.hashCode(), builder.build())

            // Create summary notification for grouping (Android will handle when to show it)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                val summaryNotification = NotificationCompat.Builder(context, CHANNEL_ID)
                    .setSmallIcon(R.mipmap.ic_app)
                    .setContentTitle("VietinBank eFast")
                    .setContentText("Tin nhắn mới")
                    .setPriority(NotificationCompat.PRIORITY_HIGH)
                    .setGroup(GROUP_KEY)
                    .setGroupSummary(true)
                    .setAutoCancel(true)
                    .build()

                notificationManager.notify(SUMMARY_ID, summaryNotification)
            }
        } catch (e: Exception) {
            printLogError("OTT: Error showing notification", TAG, e)
        }
    }

    companion object {
        private const val TAG = "OTT"
        private const val CHANNEL_ID = "ott_channel"
        private const val GROUP_KEY = "com.vietinbank.OTT_NOTIFICATIONS"
        private const val SUMMARY_ID = 0
    }
}
