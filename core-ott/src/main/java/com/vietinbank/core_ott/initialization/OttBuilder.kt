package com.vietinbank.core_ott.initialization

import android.content.Context
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_domain.models.ott.OttMessage
import com.vietinbank.core_domain.ott.INotificationHandler
import com.vietinbank.core_domain.ott.IOttFirebaseInitializer
import com.vietinbank.core_domain.ott.IOttMessageListener
import com.vietinbank.core_domain.ott.IOttWebSocketListener
import com.vietinbank.core_domain.ott.IOttWebSocketService
import com.vietinbank.core_ott.websocket.OttSocketIOClient
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Created by vandz on 16/4/25.
 */
@Singleton
class OttBuilder @Inject constructor(
    @ApplicationContext private val appContext: Context,
    private val firebaseInitializer: IOttFirebaseInitializer,
    private val webSocketService: IOttWebSocketService,
    private val socketClient: OttSocketIOClient,
    private val notificationHandler: INotificationHandler,
) {
    companion object {
        private const val TAG = "OTT"
    }

    private var isInitialized = false
    private var ottMessageListener: IOttMessageListener? = null

    // socket io
    private val webSocketListener = object : IOttWebSocketListener {
        override fun onConnected() {
            printLog("OTT: WebSocket connected", TAG)
        }

        override fun onDisconnected() {
            printLog("OTT: WebSocket disconnected", TAG)
        }

        override fun onMessageReceived(
            messageId: String,
            content: String,
            data: Map<String, String>?,
        ) {
            printLog("OTT: Received message: $messageId", TAG)

            // Chuyển tiếp tới OTT message listener theo interface mới
            try {
                val timestamp = System.currentTimeMillis()
                val title = content.substringBefore("\n")

                // Tạo OttMessage từ các thông số nhận được
                val ottMessage = OttMessage(
                    messageId = messageId,
                    content = content,
                    title = title,
                    timestamp = timestamp,
                    // Các trường khác đặt giá trị mặc định
                    type = OttMessage.TYPE_TEXT,
                    state = OttMessage.STATE_UNREAD,
                    sendTime = "chua lam",
                    messageType = "chua lam not",
                )

                // Gọi callback mới
                ottMessageListener?.onMessageReceived(ottMessage)
            } catch (e: Exception) {
                printLog("OTT: Error processing message: ${e.message}", TAG)
                ottMessageListener?.onError(e)
            }
        }

        override fun onStatusUpdate(messageId: String, status: Int) {
            printLog("OTT: Status update for message $messageId: $status", TAG)
        }

        override fun onError(error: Throwable) {
            printLog("OTT: WebSocket error: ${error.message}", TAG)
            ottMessageListener?.onError(error)
        }

        // Implementa phương thức getSocketId từ interface IOttWebSocketListener
        override fun getSocketId(): String? {
            // Lấy socketId từ socketClient nếu đang kết nối
            return if (socketClient.isConnected()) {
                socketClient.getSocketId()
            } else {
                null
            }
        }
    }

    /**
     * Khởi tạo module OTT
     * @return instance của OttBuilder để cho phép method chaining
     */
    fun install(): OttBuilder {
        if (isInitialized) {
            printLog("OTT: Module đã được khởi tạo trước đó", TAG)
            return this
        }

        try {
            printLog("OTT: Đang khởi tạo module...", TAG)

            // Khởi tạo notification channel
            notificationHandler.createNotificationChannel()

            // Khởi tạo Firebase
            firebaseInitializer.initialize()

            // Đăng ký lắng nghe WebSocket
            webSocketService.registerListener(webSocketListener)

            isInitialized = true
            printLog("OTT: Module khởi tạo thành công", TAG)
        } catch (e: Exception) {
            printLog("OTT: Lỗi khi khởi tạo module: ${e.message}", TAG)
        }

        return this
    }

    /**
     * Đăng ký callback để nhận thông báo tin nhắn OTT
     * @param listener callback sẽ được gọi khi có tin nhắn mới
     * @return instance của OttBuilder để cho phép method chaining
     */
    fun setCallback(listener: IOttMessageListener): OttBuilder {
        this.ottMessageListener = listener
        printLog("OTT: Đã đăng ký callback: ${listener.javaClass.simpleName}", TAG)
        return this
    }

    /**
     * Lấy callback listener đã đăng ký
     * @return instance của IOttMessageListener hoặc null nếu chưa đăng ký
     */
    fun getMessageListener(): IOttMessageListener? = ottMessageListener

    /**
     * Kiểm tra xem module đã được khởi tạo chưa
     * @return true nếu đã khởi tạo, false nếu chưa
     */
    fun isInitialized(): Boolean = isInitialized

    /**
     * Lấy Socket ID hiện tại
     * @return Socket ID hoặc null nếu chưa kết nối
     */
    fun getSocketId(): String? {
        return webSocketListener.getSocketId()
    }
}