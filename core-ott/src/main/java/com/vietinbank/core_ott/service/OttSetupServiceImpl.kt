package com.vietinbank.core_ott.service

import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.ott.OttSetupParams
import com.vietinbank.core_domain.ott.IOttManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of IOttSetupService
 * Handles OTT setup logic including Firebase initialization and message sync
 */
@Singleton
class OttSetupServiceImpl @Inject constructor(
    private val ottManager: IOttManager,
) : IOttSetupService {

    // Use SupervisorJob to ensure one failed coroutine doesn't cancel others
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    override fun setupOttForUser(
        cifNo: String,
        phoneNumber: String,
        username: String,
    ) {
        // Validate input parameters
        if (cifNo.isBlank()) {
            printLog("OTT setup skipped: CIF number is empty")
            return
        }

        scope.launch {
            try {
                val params = OttSetupParams(
                    userId = cifNo,
                    cifNo = cifNo,
                    phoneNumber = phoneNumber.ifBlank { "" },
                    username = username.ifBlank { "" },
                    channelId = "efast",
                )

                when (val result = ottManager.setup(params)) {
                    is Resource.Success -> {
                        printLog("OTT setup successful for user: $username")
                    }
                    is Resource.Error -> {
                        printLog("OTT setup failed: ${result.message}")
                    }
                }
            } catch (e: Exception) {
                printLog("OTT setup exception: ${e.message}")
            }
        }
    }

    override suspend fun syncOttMessages(forceFullSync: Boolean) {
        try {
            // Default pageSize will be used (500 for HomeViewModel sync)
            when (val result = ottManager.syncMessagesFromServer(forceFullSync = forceFullSync)) {
                is Resource.Success -> {
                    printLog("OTT messages synced successfully: ${result.data} messages")
                }
                is Resource.Error -> {
                    printLog("OTT message sync failed: ${result.message}")
                }
            }
        } catch (e: Exception) {
            printLog("OTT message sync exception: ${e.message}")
        }
    }
}