package com.vietinbank.core_ott.firebase

import android.content.Context
import com.google.firebase.FirebaseApp
import com.google.firebase.messaging.FirebaseMessaging
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_domain.ott.IOttFirebaseInitializer
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation của IOttFirebaseInitializer
 * Chịu trách nhiệm khởi tạo Firebase cho module OTT
 */
@Singleton
class OttFirebaseInitializerImpl @Inject constructor(
    @ApplicationContext private val context: Context,
) : IOttFirebaseInitializer {
    companion object {
        private const val TAG = "OTT"
    }

    /**
     * Khởi tạo Firebase và đăng ký các topic cần thiết
     */
    override fun initialize() {
        try {
            // Đảm bảo Firebase đã được khởi tạo
            if (FirebaseApp.getApps(context).isEmpty()) {
                FirebaseApp.initializeApp(context)
                printLog("OTT: Firebase được khởi tạo thành công", TAG)
            }

            // Đăng ký nhận thông báo từ topic "ott"
            FirebaseMessaging.getInstance().subscribeToTopic("ott")
                .addOnCompleteListener { task ->
                    if (task.isSuccessful) {
                        printLog("OTT: Đăng ký topic 'ott' thành công", TAG)
                    } else {
                        printLog("OTT: Đăng ký topic 'ott' thất bại: ${task.exception?.message}", TAG)
                    }
                }
        } catch (e: Exception) {
            printLog("OTT: Khởi tạo Firebase thất bại: ${e.message}", TAG)
        }
    }
}