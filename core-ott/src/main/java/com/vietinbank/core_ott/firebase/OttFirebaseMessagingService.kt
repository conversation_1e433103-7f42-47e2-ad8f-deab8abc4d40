package com.vietinbank.core_ott.firebase

import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.extensions.printLogError
import com.vietinbank.core_domain.models.ott.OttMessage
import com.vietinbank.core_domain.ott.INotificationHandler
import com.vietinbank.core_ott.initialization.OttBuilder
import dagger.hilt.android.AndroidEntryPoint
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import javax.inject.Inject

@AndroidEntryPoint
class OttFirebaseMessagingService : FirebaseMessagingService() {

    companion object {
        private const val TAG = "OTT"
    }

    @Inject
    lateinit var ottBuilder: OttBuilder

    @Inject
    lateinit var notificationHandler: INotificationHandler

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        printLog("OTT: Message received. Has notification: ${remoteMessage.notification != null}", TAG)
        printLog("OTT: Data payload: ${remoteMessage.data}", TAG)

        // Kiểm tra xem tin nhắn có phải từ OTT không
        if (isOttMessage(remoteMessage)) {
            try {
                val data = remoteMessage.data
                val ottDataString = data["ottData"]

                printLog("OTT: Raw ottData: $ottDataString", TAG)

                // Parse ottData JSON
                var actualMessageId: String? = null
                var messageType: String? = null
                var messageContent: String? = null
                var sendTime: String? = null

                if (!ottDataString.isNullOrEmpty()) {
                    try {
                        val ottDataJson = JSONObject(ottDataString)
                        actualMessageId = ottDataJson.optString("messageId")
                        messageType = ottDataJson.optString("type")
                        messageContent = ottDataJson.optString("content")
                        sendTime = ottDataJson.optString("sendTime", "")

                        printLog("OTT: Parsed ottData - messageId: $actualMessageId, type: $messageType", TAG)
                    } catch (e: Exception) {
                        printLogError("OTT: Error parsing ottData JSON", TAG, e)
                    }
                }

                // Fallback values
                val messageId = actualMessageId ?: remoteMessage.messageId ?: System.currentTimeMillis().toString()
                val notification = remoteMessage.notification
                val title = notification?.title ?: data["title"] ?: "VietinBank eFAST"
                val content = messageContent ?: notification?.body ?: data["content"] ?: data["body"] ?: ""
                val finalMessageType = messageType ?: data["messageType"] ?: ""

                // Generate sendTime if not provided
                val finalSendTime = if (sendTime.isNullOrEmpty()) {
                    val dateFormat = SimpleDateFormat("dd/MM/yyyy HH:mm:ss", Locale.getDefault())
                    dateFormat.format(Date())
                } else {
                    sendTime
                }

                printLog("OTT: Using sendTime: $finalSendTime", TAG)

                // Tạo object OttMessage theo interface mới
                val ottMessage = OttMessage(
                    messageId = messageId,
                    content = content,
                    title = title,
                    timestamp = System.currentTimeMillis(),
                    type = OttMessage.TYPE_NOTIFICATION,
                    state = OttMessage.STATE_UNREAD,
                    sendTime = finalSendTime,
                    messageType = finalMessageType,
                )

                printLog("OTT: Created OttMessage with messageId: ${ottMessage.messageId}, messageType: ${ottMessage.messageType}", TAG)

                // Show notification immediately to ensure it displays even when app is killed
                // This is critical for data-only messages with high priority
                try {
                    notificationHandler.showNotification(ottMessage)
                    printLog("OTT: Notification shown for messageId: ${ottMessage.messageId}", TAG)
                } catch (e: Exception) {
                    printLogError("OTT: Error showing notification", TAG, e)
                }

                // Gọi callback theo interface mới để save vào DB
                ottBuilder.getMessageListener()?.onMessageReceived(ottMessage)
            } catch (e: Exception) {
                printLogError("OTT: Lỗi xử lý tin nhắn: ${e.message}", TAG, e)
                ottBuilder.getMessageListener()?.onError(e)
            }
        } else {
            // Xử lý các loại tin nhắn khác (nếu cần)
            super.onMessageReceived(remoteMessage)
        }
    }

    override fun onNewToken(token: String) {
        printLog("OTT: Nhận token Firebase mới: $token", TAG)
        // Xử lý token mới (nếu cần)
    }

    private fun isOttMessage(remoteMessage: RemoteMessage): Boolean {
        val data = remoteMessage.data
        return data.containsKey("ottData")
    }
}