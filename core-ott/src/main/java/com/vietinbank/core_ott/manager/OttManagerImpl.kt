package com.vietinbank.core_ott.manager

import android.app.Activity
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.extensions.printLogError
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_data.database.ott.dao.OttMessageDao
import com.vietinbank.core_data.database.ott.entity.OttMessageEntity
import com.vietinbank.core_domain.models.ott.OttGetAllMessageDomain
import com.vietinbank.core_domain.models.ott.OttGetAllMessageParams
import com.vietinbank.core_domain.models.ott.OttMessage
import com.vietinbank.core_domain.models.ott.OttMessageItemDomain
import com.vietinbank.core_domain.models.ott.OttRegisterDeviceDomain
import com.vietinbank.core_domain.models.ott.OttRegisterDeviceParams
import com.vietinbank.core_domain.models.ott.OttSetupParams
import com.vietinbank.core_domain.models.ott.SocketMessage
import com.vietinbank.core_domain.ott.IDeviceTokenProvider
import com.vietinbank.core_domain.ott.INotificationHandler
import com.vietinbank.core_domain.ott.IOttManager
import com.vietinbank.core_domain.ott.IOttMessageListener
import com.vietinbank.core_domain.repository.ConfigRepository
import com.vietinbank.core_domain.usecase.ott.ConnectWebSocketUseCase
import com.vietinbank.core_domain.usecase.ott.OttGetAllMessageUseCase
import com.vietinbank.core_domain.usecase.ott.OttRegisterDeviceUseCase
import com.vietinbank.core_domain.usecase.ott.UpdateMessageStatusWebSocketUseCase
import com.vietinbank.core_ott.extensions.flatMap
import com.vietinbank.core_ott.extensions.isValidInput
import com.vietinbank.core_ott.extensions.parseSendTimeToTimestamp
import com.vietinbank.core_ott.extensions.safeContextCall
import com.vietinbank.core_ott.extensions.safeDbOperation
import com.vietinbank.core_ott.extensions.safeNetworkCall
import com.vietinbank.core_ott.extensions.safeSocketOperation
import com.vietinbank.core_ott.extensions.toEntities
import com.vietinbank.core_ott.extensions.validateFirebaseToken
import com.vietinbank.core_ott.initialization.OttBuilder
import com.vietinbank.core_ott.notification.NotificationPermissionHelper
import com.vietinbank.core_ott.queue.OttMessageQueue
import com.vietinbank.core_ott.websocket.OttSocketIOClient
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class OttManagerImpl @Inject constructor(
    private val ottBuilder: OttBuilder,
    private val deviceTokenProvider: IDeviceTokenProvider,
    private val ottRegisterDeviceUseCase: OttRegisterDeviceUseCase,
    private val connectWebSocketUseCase: ConnectWebSocketUseCase,
    private val updateMessageStatusWebSocketUseCase: UpdateMessageStatusWebSocketUseCase,
    private val ottGetAllMessageUseCase: OttGetAllMessageUseCase,
    private val configRepository: ConfigRepository,
    private val socketClient: OttSocketIOClient,
    private val notificationHandler: INotificationHandler,
    private val notificationPermissionHelper: NotificationPermissionHelper,
    private val validator: OttManagerValidator,
    private val ottMessageDao: OttMessageDao,
    private val messageQueue: OttMessageQueue,
) : IOttManager {

    // =====================================================
    // CONSTANTS
    // =====================================================
    companion object {
        private const val TAG = "OTT"
    }

    // =====================================================
    // STATE MANAGEMENT
    // =====================================================
    private val _ottState =
        MutableStateFlow<IOttManager.OttState>(IOttManager.OttState.Uninitialized)
    override val ottState: StateFlow<IOttManager.OttState> = _ottState.asStateFlow()

    private val _socketConnectionState = MutableStateFlow<IOttManager.SocketConnectionState>(
        IOttManager.SocketConnectionState.Disconnected,
    )
    override val socketConnectionState: StateFlow<IOttManager.SocketConnectionState> =
        _socketConnectionState.asStateFlow()

    private val _messageStatusUpdates = MutableSharedFlow<IOttManager.MessageStatusUpdate>()
    override val messageStatusUpdates: SharedFlow<IOttManager.MessageStatusUpdate> =
        _messageStatusUpdates.asSharedFlow()

    // =====================================================
    // USER INFORMATION STORAGE
    // =====================================================
    private data class UserInfo(
        val userId: String,
        val cifNo: String,
        val phoneNumber: String,
        val username: String,
        val actionId: String,
        val roleId: String,
        val channelId: String,
    ) {
        fun isValid(): Boolean {
            return userId.isNotEmpty() &&
                cifNo.isNotEmpty() &&
                phoneNumber.isNotEmpty() &&
                username.isNotEmpty()
        }
    }

    private var currentUserInfo: UserInfo? = null

    // =====================================================
    // COROUTINE MANAGEMENT
    // =====================================================
    private val exceptionHandler = CoroutineExceptionHandler { _, throwable ->
        printLogError("OTT: Uncaught exception in OttManager", TAG, throwable)
        _ottState.value = IOttManager.OttState.Error(
            message = "Unexpected error: ${throwable.message}",
            throwable = throwable,
        )
    }

    // =====================================================
    // MESSAGE LISTENER MANAGEMENT
    // =====================================================
    private var appMessageListener: IOttMessageListener? = null

    // =====================================================
    // NOTIFICATION PROCESSING STATE
    // =====================================================
    private var isProcessingNotificationFlag = false

    // Internal listener để handle messages và save to DB
    private val internalMessageListener = object : IOttMessageListener {
        override fun onMessageReceived(message: OttMessage) {
            // Add to queue for batch processing
            messageQueue.enqueue(message)

            // Forward to app listener immediately for UI update
            appMessageListener?.onMessageReceived(message)

            // Process immediately if queue is getting large
            messageQueue.processIfNeeded()
        }

        override fun onError(error: Throwable) {
            printLogError("OTT: Real-time message error", TAG, error)
            appMessageListener?.onError(error)
        }
    }

    /**
     * Process batch of messages for efficient database insertion
     */
    private suspend fun processBatchMessages(messages: List<OttMessage>) {
        try {
            printLog("OTT: [SAVE] Processing batch of ${messages.size} messages", TAG)

            val cifToSave = currentUserInfo?.cifNo ?: ""
            val phoneToSave = currentUserInfo?.phoneNumber ?: ""

            printLog("OTT: [SAVE] Current user info - CIF: $cifToSave, Phone: $phoneToSave", TAG)

            if (cifToSave.isEmpty() || phoneToSave.isEmpty()) {
                printLogError("OTT: [SAVE] ERROR - Cannot save messages: Empty CIF or Phone!", TAG)
                return
            }

            // Filter out duplicates
            val messageIds = messages.map { it.messageId }
            val existingIds = ottMessageDao.getExistingMessageIds(messageIds)

            val newMessages = messages.filter { it.messageId !in existingIds }

            if (newMessages.isEmpty()) {
                printLog("OTT: [SAVE] All ${messages.size} messages already exist in DB", TAG)
                return
            }

            printLog("OTT: [SAVE] Found ${newMessages.size} new messages to save", TAG)

            // Convert to entities
            val entities = newMessages.map { message ->
                OttMessageEntity(
                    messageId = message.messageId,
                    title = message.title,
                    content = message.content,
                    cif = cifToSave,
                    phone = phoneToSave,
                    status = message.state,
                    data = null,
                    sendTime = message.sendTime,
                    messageType = message.messageType,
                    timestamp = parseSendTimeToTimestamp(message.sendTime),
                )
            }

            // Batch insert
            ottMessageDao.insertMessages(entities)
            printLog("OTT: [SAVE] SUCCESS - Saved ${entities.size} messages for CIF: $cifToSave", TAG)

            // Verify save
            val savedCount = ottMessageDao.getMessagesWithPaging(cifToSave, phoneToSave, limit = 10, offset = 0).size
            printLog("OTT: [SAVE] Verification - Total messages in DB for user: $savedCount", TAG)
        } catch (e: Exception) {
            printLogError("OTT: [SAVE] ERROR processing batch messages", TAG, e)
            // Fall back to individual processing
            messages.forEach { message ->
                try {
                    processSingleMessage(message)
                } catch (ex: Exception) {
                    printLogError("OTT: [SAVE] ERROR saving individual message ${message.messageId}", TAG, ex)
                }
            }
        }
    }

    /**
     * Process single message (fallback method)
     */
    private suspend fun processSingleMessage(message: OttMessage) {
        val existingMessage = ottMessageDao.getMessageById(message.messageId)
        if (existingMessage != null) {
            return
        }

        val cifToSave = currentUserInfo?.cifNo ?: ""
        val phoneToSave = currentUserInfo?.phoneNumber ?: ""

        val entity = OttMessageEntity(
            messageId = message.messageId,
            title = message.title,
            content = message.content,
            cif = cifToSave,
            phone = phoneToSave,
            status = message.state,
            data = null,
            sendTime = message.sendTime,
            messageType = message.messageType,
            timestamp = parseSendTimeToTimestamp(message.sendTime),
        )

        ottMessageDao.insertMessage(entity)
    }

    /**
     * Convert SocketMessage to OttMessage domain model
     */
    private fun convertSocketMessageToOttMessage(socketMessage: SocketMessage): OttMessage {
        val content = socketMessage.data?.get("content") ?: ""
        val title = socketMessage.data?.get("title")
            ?: content.substringBefore(":", "Thông báo")

        // Use current timestamp for real-time messages
        val timestamp = socketMessage.data?.get("timestamp")?.toLongOrNull()
            ?: System.currentTimeMillis()

        return OttMessage(
            messageId = socketMessage.messageId,
            title = title,
            content = content,
            timestamp = timestamp,
            type = OttMessage.TYPE_NOTIFICATION,
            state = OttMessageEntity.MESSAGE_STATUS_UNREAD,
            messageType = socketMessage.data?.get("messageType") ?: "",
            priority = socketMessage.data?.get("priority"),
            serverStatus = "SENT",
            sendTime = "",
        )
    }

    // =====================================================
    // COROUTINE SCOPE
    // =====================================================
    private var scope = CoroutineScope(
        SupervisorJob() + Dispatchers.IO + CoroutineExceptionHandler { _, throwable ->
            printLogError("OTT: Uncaught exception in OttManager", TAG, throwable)
            _ottState.value = IOttManager.OttState.Error(
                message = "Unexpected error: ${throwable.message}",
                throwable = throwable,
            )
        },
    )

    // =====================================================
    // SOCKET CONNECTION MANAGEMENT
    // =====================================================
    private var socketConnectionDeferred: CompletableDeferred<String?>? = null
    private var isTokenChangeListenerRegistered = false

    // =====================================================
    // ERROR CLASSES
    // =====================================================
    sealed class OttSetupError(val code: String, message: String) : Exception(message) {
        object PermissionDenied :
            OttSetupError(OttManagerConstants.OttErrorCodes.ERROR_PERMISSION_DENIED, "Missing notification permission")

        object BuilderNotInitialized : OttSetupError(
            OttManagerConstants.OttErrorCodes.ERROR_BUILDER_CONFIG,
            "OttBuilder not initialized",
        )

        object InvalidUserParams :
            OttSetupError(OttManagerConstants.OttErrorCodes.ERROR_MISSING_CONFIG, "Invalid user parameters")

        class TokenError(cause: Throwable?) : OttSetupError(
            OttManagerConstants.OttErrorCodes.ERROR_FIREBASE_INIT,
            "Failed to get Firebase token: ${cause?.message}",
        )

        class TokenRegistrationError(message: String) : OttSetupError(
            OttManagerConstants.OttErrorCodes.ERROR_REGISTER_FIREBASE,
            "Failed to register token: $message",
        )

        class SocketConnectionError(message: String) : OttSetupError(
            OttManagerConstants.OttErrorCodes.ERROR_SOCKET_CONNECTION,
            "Socket connection failed: $message",
        )

        class SocketRegistrationError(message: String) : OttSetupError(
            OttManagerConstants.OttErrorCodes.ERROR_REGISTER_SOCKET,
            "Socket registration failed: $message",
        )

        class ConfigurationError(message: String) : OttSetupError(
            OttManagerConstants.OttErrorCodes.ERROR_MISSING_CONFIG,
            "Configuration error: $message",
        )

        class GeneralError(cause: Throwable) :
            OttSetupError(OttManagerConstants.OttErrorCodes.ERROR_SETUP_GENERAL, "Setup failed: ${cause.message}")
    }

    // =====================================================
    // SOCKET CALLBACK IMPLEMENTATION
    // =====================================================
    private val socketCallback = object : OttSocketIOClient.SocketCallback {
        override fun onConnected() {
            scope.launch {
                _socketConnectionState.value = IOttManager.SocketConnectionState.Connected
                val socketId = socketClient.getSocketId()
//                printLog("OTT: Socket connected with ID: $socketId", TAG)
                socketConnectionDeferred?.complete(socketId)
            }
        }

        override fun onDisconnected() {
            scope.launch {
                _socketConnectionState.value = IOttManager.SocketConnectionState.Disconnected
//                printLog("OTT: Socket disconnected", TAG)
                socketConnectionDeferred?.cancel("Socket disconnected")
            }
        }

        override fun onMessage(message: SocketMessage) {
            printLog("OTT: Message received: ${message.messageId}, type: ${message.type}", TAG)

            when (message.type) {
                OttSocketIOClient.MESSAGE_TYPE_STATUS_UPDATE -> {
                    handleStatusUpdateMessage(message)
                }

                OttSocketIOClient.MESSAGE_TYPE_MESSAGE -> {
                    // Convert SocketMessage to OttMessage và trigger internal listener
                    try {
                        val ottMessage = convertSocketMessageToOttMessage(message)
                        printLog("OTT: New message from socket: ${ottMessage.messageId}", TAG)

                        // Gọi internal listener để save to DB và forward to app
                        internalMessageListener.onMessageReceived(ottMessage)
                    } catch (e: Exception) {
                        printLogError("OTT: Error converting socket message", TAG, e)
                    }
                }

                else -> {
                    printLog("OTT: Unhandled message type: ${message.type}", TAG)
                }
            }
        }

        override fun onError(error: Throwable) {
            scope.launch {
                _socketConnectionState.value = IOttManager.SocketConnectionState.Error(
                    message = "Socket error: ${error.message}",
                    throwable = error,
                )
                printLogError("OTT: Socket error", TAG, error)
                socketConnectionDeferred?.completeExceptionally(error)
            }
        }
    }

    override suspend fun triggerTestRealtimeMessage(): Resource<Boolean> {
        return safeContextCall(
            context = scope.coroutineContext,
            errorCode = OttManagerConstants.OttErrorCodes.ERROR_TEST_MESSAGE,
            errorPrefix = "Failed to trigger test message",
        ) {
            printLog("OTT: DEBUG: Triggering test real-time message", TAG)

            val testMessage = createTestRealtimeMessage()

            printLog("OTT: Triggering test real-time message: ${testMessage.messageId}", TAG)

            // Trigger through internal listener (which will save to DB)
            internalMessageListener.onMessageReceived(testMessage)

            true
        }
    }

    private fun createTestRealtimeMessage(): OttMessage {
        val timestamp = System.currentTimeMillis()
        val dateStr = SimpleDateFormat("HH:mm dd/MM/yyyy", Locale("vi", "VN")).format(Date(timestamp))

        return OttMessage(
            messageId = "REALTIME_TEST_$timestamp",
            title = "VietinBank",
            content = "VietinBank:TK 104...529 +888,888VND lúc $dateStr. SD: 9,999,999VND. ND: Test real-time message",
            timestamp = timestamp,
            type = OttMessage.TYPE_NOTIFICATION,
            state = OttMessage.STATE_UNREAD,
            sendTime = dateStr,
            messageType = "BALANCE_CHANGE",
        )
    }

    override suspend fun fillDummyMessages(count: Int): Resource<Int> {
        return safeDbOperation(errorPrefix = "Failed to create dummy messages") {
            printLog("OTT: Creating $count dummy messages...", TAG)

            val messages = createDummyMessages(count)

            // Convert to entities using existing extension
            // Cast to nullable list for extension function
            val entities = (messages as List<OttMessageItemDomain?>).toEntities(
                cif = currentUserInfo?.cifNo ?: "*********",
                phone = currentUserInfo?.phoneNumber ?: "**********",
            )

            // Insert with OnConflictStrategy.REPLACE
            ottMessageDao.insertMessages(entities)

            printLog("OTT: Filled ${entities.size} dummy messages to DB", TAG)
            entities.size
        }
    }

    private fun createDummyMessages(count: Int): List<OttMessageItemDomain> {
        val messages = mutableListOf<OttMessageItemDomain>()
        val baseTime = System.currentTimeMillis()

        for (i in 0 until count) {
            val timestamp = baseTime - (count - i) * 3600000 // 1 hour apart
            val dateStr = SimpleDateFormat("HH:mm dd/MM/yyyy", Locale("vi", "VN")).format(Date(timestamp))

            messages.add(
                OttMessageItemDomain(
                    messageId = "DUMMY_${timestamp}_$i",
                    title = when (i % 4) {
                        0 -> null // Test case với null title để test extract từ content
                        1 -> "Chuyển tiền"
                        2 -> "Thanh toán"
                        else -> "Thông báo"
                    },
                    content = when (i % 4) {
                        0 -> "VietinBank:TK 104...529 +${(i + 1) * 50000}VND lúc $dateStr. SD: ${1000000 + i * 50000}VND. ND: Test giao dich $i"
                        1 -> "TK 104...529 nhận ${(i + 1) * 100000}VND từ NGUYEN VAN A lúc $dateStr. ND: Chuyen tien test $i"
                        2 -> "TK 104...529 -${(i + 1) * 30000}VND lúc $dateStr. SD: ${800000 - i * 30000}VND. ND: Thanh toan hoa don"
                        else -> "Thông báo: Giao dịch #$i đã được xử lý thành công lúc $dateStr"
                    },
                    type = when (i % 4) {
                        0 -> "BALANCE_CHANGE"
                        1 -> "TRANSFER_IN"
                        2 -> "TRANSFER_OUT"
                        else -> "NOTIFICATION"
                    },
                    timestamp = timestamp.toString(),
                    status = if (i < 10) 0 else 1, // 0 = unread, 1 = read
                    data = mapOf(
                        "amount" to ((i + 1) * 50000).toString(),
                        "balance" to (1000000 + i * 50000).toString(),
                    ),
                    sendTime = dateStr,
                ),
            )
        }

        return messages
    }

    // =====================================================
    // INITIALIZATION
    // =====================================================
    init {
        initializeComponents()
    }

    private fun initializeComponents() {
        try {
            printLog("OTT: Initializing Manager components...", TAG)

            // Register socket callback
            socketClient.registerCallback(socketCallback)

            // Register token change listener
            registerTokenChangeListener()

            // Initialize message queue batch processor
            messageQueue.batchProcessor = { messages ->
                processBatchMessages(messages)
            }

            printLog("OTT: Manager components initialized successfully", TAG)
        } catch (e: Exception) {
            printLogError("OTT: Error initializing components", TAG, e)
            _ottState.value = IOttManager.OttState.Error(
                message = "Failed to initialize components: ${e.message}",
                throwable = e,
            )
        }
    }

    private fun registerTokenChangeListener() {
        if (!isTokenChangeListenerRegistered) {
            deviceTokenProvider.registerTokenChangeListener { newToken ->
                handleTokenChange(newToken)
            }
            isTokenChangeListenerRegistered = true
            printLog("OTT: Token change listener registered", TAG)
        }
    }

    // =====================================================
    // RESOURCE HANDLING HELPERS
    // =====================================================
    private inline fun <T> Resource<T>.handleResult(
        operation: String,
        onSuccess: (T) -> Unit = {},
    ): Resource<T> {
        when (this) {
            is Resource.Success -> {
                printLog("OTT: $operation succeeded", TAG)
                data?.let(onSuccess)
            }

            is Resource.Error -> {
                printLogError("OTT: $operation failed: $message", TAG)
            }
        }
        return this
    }

    // =====================================================
    // PUBLIC API METHODS
    // =====================================================
    override suspend fun getAllMessage(params: OttGetAllMessageParams): Resource<OttGetAllMessageDomain> {
        return safeNetworkCall(
            errorPrefix = "Failed to fetch messages",
        ) {
            ottGetAllMessageUseCase.invoke(params)
        }.flatMap { result ->
            when (result) {
                is Resource.Success -> {
                    printLog("OTT: Successfully retrieved messages", TAG)
                    Resource.Success(result.data)
                }
                is Resource.Error -> {
                    printLogError("OTT: Failed to get messages: ${result.message}", TAG)
                    Resource.Error(result.message, result.code)
                }
            }
        }
    }

    override suspend fun getMessagesFromLocalDbForUser(cif: String, phone: String, page: Int, pageSize: Int): List<OttMessage> {
        return withContext(scope.coroutineContext) {
            val result = safeDbOperation(
                errorPrefix = "Failed to fetch messages from database for user",
            ) {
                val offset = page * pageSize

                printLog("OTT: Querying messages for user with CIF=$cif, Phone=$phone, Page=$page, PageSize=$pageSize", TAG)

                val entities = ottMessageDao.getMessagesWithPaging(
                    cif = cif,
                    phone = phone,
                    limit = pageSize,
                    offset = offset,
                )

                printLog("OTT: Found ${entities.size} messages in DB for user", TAG)

                // Convert entities to domain models
                entities.map { entity -> entity.toDomainModel() }
            }

            when (result) {
                is Resource.Success -> result.data
                is Resource.Error -> {
                    printLogError("OTT: Error getting messages from DB for user: ${result.message}", TAG)
                    emptyList()
                }
            }
        }
    }

    private fun OttMessageEntity.toDomainModel(): OttMessage {
        return OttMessage(
            messageId = this.messageId,
            title = this.title,
            content = this.content,
            timestamp = parseSendTimeToTimestamp(this.sendTime),
            sendTime = this.sendTime,
            type = OttMessage.TYPE_NOTIFICATION,
            state = this.status,
            messageType = this.messageType,
            priority = null,
            serverStatus = when (this.status) {
                OttMessageEntity.MESSAGE_STATUS_READ -> "READ"
                else -> "SENT"
            },
        )
    }

    override suspend fun setup(params: OttSetupParams): Resource<Boolean> {
        return withContext(scope.coroutineContext) {
            try {
                // Smart check: Đã setup cho CIF này chưa?
                if (isAlreadySetupForCif(params)) {
                    printLog("OTT: Already setup for CIF ${params.cifNo}", TAG)

                    if (!isSocketConnected() &&
                        _socketConnectionState.value != IOttManager.SocketConnectionState.Connecting
                    ) {
                        retryWebSocketConnectionOnly()
                    }

                    return@withContext Resource.Success(true)
                }

                // Check if there are messages from different CIF in database
                val hasMessagesFromOtherCif = ottMessageDao.hasMessagesFromDifferentUser(params.cifNo)

                printLog("OTT: Setup check - Current CIF: ${currentUserInfo?.cifNo}, New CIF: ${params.cifNo}", TAG)
                printLog("OTT: Has messages from other CIF in DB: $hasMessagesFromOtherCif", TAG)

                // Nếu khác CIF -> cleanup và setup mới
                // Only cleanup if there's a real CIF mismatch (not empty CIF cases)
                val shouldCleanup = when {
                    // Case 1: No current user - proceed without cleanup
                    currentUserInfo == null -> false

                    // Case 2: Current user has different CIF than new user
                    currentUserInfo?.cifNo != params.cifNo -> true

                    // Case 3: Messages from other CIF exist (but ignore empty CIF messages)
                    hasMessagesFromOtherCif -> {
                        // Double check it's not just empty CIF messages
                        val messagesWithCif = ottMessageDao.getMessagesWithPaging(params.cifNo, params.phoneNumber, limit = 1, offset = 0)
                        val hasValidMessages = messagesWithCif.isNotEmpty()
                        printLog("OTT: Has valid messages for current CIF: $hasValidMessages", TAG)

                        // Only cleanup if there are real messages from other CIF (not empty)
                        hasMessagesFromOtherCif && !hasValidMessages
                    }

                    else -> false
                }

                if (shouldCleanup) {
                    printLog("OTT: Different CIF detected! Triggering cleanup...", TAG)
                    printLog("OTT: - Current user: ${currentUserInfo?.cifNo}/${currentUserInfo?.phoneNumber}", TAG)
                    printLog("OTT: - New user: ${params.cifNo}/${params.phoneNumber}", TAG)
                    printLog("OTT: - Reason: ${if (currentUserInfo?.cifNo != params.cifNo) "CIF mismatch" else "Found messages from other user"}", TAG)

                    // Reset state FIRST to prevent any sync operations with old user data
                    _ottState.value = IOttManager.OttState.Uninitialized

                    // Clear currentUserInfo immediately to prevent using old user data
                    currentUserInfo = null

                    // Clear all messages if different user
                    cleanupForNewCif()
                }

                // Proceed with full setup...
                printLog("OTT: Starting fresh setup for CIF ${params.cifNo}", TAG)

                // Step 1: Initialize setup state
                setupInitialState()
                // Step 2: Validate and store user parameters
                validateAndStoreUserParams(params)
                // Step 3: Validate preconditions
                validatePreconditions()

                // Step 4: Get Firebase token
                val token = obtainFirebaseToken()
                // Step 5: Register Firebase token with server
                registerTokenWithServer(token)
                // Complete basic setup (firebase ready)
                val result = completeSetup()

                // Step 6: Establish socket connection in background (non-blocking)
                scope.launch {
                    try {
                        establishAndRegisterSocketConnection(token)
                    } catch (e: Exception) {
                        printLogError("OTT: WebSocket failed, will retry", TAG, e)
                    }
                }

                result
            } catch (e: Exception) {
                handleSetupFailure(e)
            }
        }
    }

    private suspend fun cleanupForNewCif() {
        try {
            printLog("OTT: Starting cleanup for new CIF...", TAG)

            // 1. Flush pending messages in queue first
            messageQueue.flush()

            // 2. Ngắt socket cũ
            socketClient.disconnect()

            // 3. Clear ALL messages in database (not just for old user)
            // This ensures complete data isolation between users
            ottMessageDao.deleteAllMessages()
            printLog("OTT: Cleared all messages from database", TAG)

            // 4. Clear message queue completely
            messageQueue.cleanup()

            // 5. Reset socket state
            _socketConnectionState.value = IOttManager.SocketConnectionState.Disconnected

            // 6. Clear any pending socket connection
            socketConnectionDeferred?.cancel("Cleanup for new CIF")
            socketConnectionDeferred = null

            printLog("OTT: Cleanup for new CIF completed", TAG)
        } catch (e: Exception) {
            printLogError("OTT: Error during CIF cleanup", TAG, e)
        }
    }

    // Retry chỉ WebSocket (không register lại Firebase)
    private fun retryWebSocketConnectionOnly() {
        scope.launch {
            try {
                // Lấy token hiện tại (đã được update tự động qua listener)
                val currentToken = deviceTokenProvider.getDeviceToken().getOrNull()
                if (currentToken != null && currentUserInfo != null) {
                    establishAndRegisterSocketConnection(currentToken)
                }
            } catch (e: Exception) {
                printLogError("OTT: WebSocket retry failed", TAG, e)
            }
        }
    }

    override fun isSocketConnected(): Boolean {
        return socketClient.isConnected()
    }

    override suspend fun registerFirebaseToken(token: String): Resource<OttRegisterDeviceDomain> {
        return safeContextCall(
            context = scope.coroutineContext,
            errorCode = OttManagerConstants.OttErrorCodes.ERROR_REGISTER_FIREBASE,
            errorPrefix = "Failed to register Firebase token",
        ) {
            printLog("OTT: Registering Firebase token...", TAG)

            // Validate token
            validateFirebaseToken(token)

            val userInfo = getCurrentUserInfo()
            val params = createRegisterDeviceParams(
                userInfo = userInfo,
                fcmToken = token,
                socketId = "",
                tokenType = OttManagerConstants.FCM_TOKEN_TYPE,
            )

            ottRegisterDeviceUseCase.invoke(params)
        }.flatMap { result ->
            when (result) {
                is Resource.Success -> {
                    printLog("OTT: Firebase token registered successfully", TAG)
                    Resource.Success(result.data)
                }
                is Resource.Error -> {
                    printLogError("OTT: Failed to register Firebase token: ${result.message}", TAG)
                    Resource.Error(result.message, result.code)
                }
            }
        }
    }

    override suspend fun registerSocket(
        fcmToken: String,
        socketId: String,
    ): Resource<OttRegisterDeviceDomain> {
        return safeSocketOperation(
            errorPrefix = "Failed to register socket",
        ) {
            printLog("OTT: Registering socket with ID: $socketId", TAG)

            // Validate inputs
            require(fcmToken.isValidInput()) { "FCM token is required" }
            require(socketId.isValidInput()) { "Socket ID is required" }

            val userInfo = getCurrentUserInfo()
            val params = createRegisterDeviceParams(
                userInfo = userInfo,
                fcmToken = fcmToken,
                socketId = socketId,
                tokenType = OttManagerConstants.SOCKET_TOKEN_TYPE,
            )

            ottRegisterDeviceUseCase.invoke(params)
        }.flatMap { result ->
            when (result) {
                is Resource.Success -> {
                    printLog("OTT: Socket registered successfully", TAG)
                    Resource.Success(result.data)
                }
                is Resource.Error -> {
                    printLogError("OTT: Failed to register socket: ${result.message}", TAG)
                    Resource.Error(result.message, result.code)
                }
            }
        }
    }

    override suspend fun getFirebaseToken(): Result<String> {
        return try {
            deviceTokenProvider.getDeviceToken()
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun syncMessageStatus(messageId: String, status: Int): Resource<Boolean> {
        return safeContextCall(
            context = scope.coroutineContext,
            errorCode = OttManagerConstants.OttErrorCodes.ERROR_API_UPDATE_STATUS,
            errorPrefix = "Failed to sync message status",
        ) {
            printLog("OTT: Syncing message status: $messageId -> $status", TAG)

            // Validate inputs
            require(messageId.isNotBlank()) { "Message ID cannot be blank" }
            require(status in 0..2) { "Invalid status value: $status" }

            updateMessageStatusWebSocketUseCase.invoke(messageId, status)
            true
        }
    }

    override fun disconnect() {
        try {
            printLog("OTT: Disconnecting socket (keeping user data)...", TAG)

            // Only disconnect socket, keep everything else
            socketClient.disconnect()

            // Reset socket state only
            _socketConnectionState.value = IOttManager.SocketConnectionState.Disconnected
            socketConnectionDeferred?.cancel("Socket disconnected")
            socketConnectionDeferred = null

            // Keep user info, scope, and DB data intact for reconnection
            printLog("OTT: Socket disconnected. User data preserved for reconnection.", TAG)
        } catch (e: Exception) {
            printLogError("OTT: Error disconnecting socket", TAG, e)
        }
    }

    override fun requestNotificationPermission(activity: Activity) {
        try {
            notificationPermissionHelper.requestPermissionFromActivity(activity)
        } catch (e: Exception) {
            printLogError("OTT: Error requesting notification permission", TAG, e)
        }
    }

    override fun hasNotificationPermission(): Boolean {
        return try {
            notificationPermissionHelper.hasNotificationPermission()
        } catch (e: Exception) {
            printLogError("OTT: Error checking notification permission", TAG, e)
            false
        }
    }

    override fun cleanup() {
        try {
            printLog("OTT: ${OttManagerConstants.LOG_CLEANUP_START}", TAG)

            // Use runBlocking to ensure cleanup completes before proceeding
            runBlocking {
                try {
                    // Flush message queue
                    messageQueue.flush()

                    // Clear ALL messages from database
                    ottMessageDao.deleteAllMessages()
                    printLog("OTT: Cleared all messages from database", TAG)
                } catch (e: Exception) {
                    printLogError("OTT: Error clearing database", TAG, e)
                }
            }

            // Cleanup message queue after flushing
            messageQueue.cleanup()

            // Unregister callbacks
            socketClient.unregisterCallback(socketCallback)

            // Disconnect socket
            socketClient.disconnect()

            // Cancel pending operations
            socketConnectionDeferred?.cancel("Cleanup called")
            socketConnectionDeferred = null

            // Reset states
            _ottState.value = IOttManager.OttState.Uninitialized
            _socketConnectionState.value = IOttManager.SocketConnectionState.Disconnected

            // Cancel coroutine scopes
            scope.cancel()

            // Create new scope for next setup
            scope = CoroutineScope(
                SupervisorJob() + Dispatchers.IO + exceptionHandler,
            )

            // Clear user info
            currentUserInfo = null
            isTokenChangeListenerRegistered = false

            printLog("OTT: ${OttManagerConstants.LOG_CLEANUP_COMPLETE}", TAG)
        } catch (e: Exception) {
            printLogError("OTT: Error during cleanup", TAG, e)
        }
    }

    override fun registerMessageListener(listener: IOttMessageListener) {
        appMessageListener = listener
        // Also register internal listener với OttBuilder
        ottBuilder.setCallback(internalMessageListener)
        printLog("OTT: Registered message listener: ${listener.javaClass.simpleName}", TAG)
    }

    // =====================================================
    // PRIVATE HELPER METHODS - SETUP PROCESS
    // =====================================================
    private fun setupInitialState() {
        _ottState.value = IOttManager.OttState.Initializing
        notificationHandler.createNotificationChannel()
        printLog("OTT: Initial state setup completed", TAG)
    }

    private fun validateAndStoreUserParams(params: OttSetupParams) {
        val validationResult = validator.validateUserParams(params)
        when (validationResult) {
            is OttManagerValidator.ValidationResult.Success -> {
                // Clear old user info first to ensure clean state
                val oldUserInfo = currentUserInfo
                currentUserInfo = null

                // Store new user information
                currentUserInfo = UserInfo(
                    userId = params.userId,
                    cifNo = params.cifNo,
                    phoneNumber = params.phoneNumber,
                    username = params.username,
                    actionId = params.actionId,
                    roleId = params.roleId,
                    channelId = params.channelId,
                )

                printLog("OTT: [USER] User info updated:", TAG)
                printLog("OTT: [USER] - Old CIF: ${oldUserInfo?.cifNo}, Old Phone: ${oldUserInfo?.phoneNumber}", TAG)
                printLog("OTT: [USER] - New CIF: ${params.cifNo}, New Phone: ${params.phoneNumber}", TAG)
                printLog("OTT: [USER] - Username: ${params.username}", TAG)
            }

            is OttManagerValidator.ValidationResult.Error -> {
                throw OttSetupError.InvalidUserParams
            }
        }
    }

    private fun validatePreconditions() {
        val validationResult = validator.validateSetupPreconditions()
        when (validationResult) {
            is OttManagerValidator.ValidationResult.Success -> {
                printLog("OTT: Preconditions validation passed", TAG)
            }

            is OttManagerValidator.ValidationResult.Error -> {
                if (validationResult.ottState != null) {
                    _ottState.value = validationResult.ottState
                }
                when (validationResult.code) {
                    OttManagerConstants.OttErrorCodes.ERROR_PERMISSION_DENIED -> throw OttSetupError.PermissionDenied
                    OttManagerConstants.OttErrorCodes.ERROR_BUILDER_CONFIG -> throw OttSetupError.BuilderNotInitialized
                    else -> throw OttSetupError.GeneralError(Exception(validationResult.message))
                }
            }
        }
    }

    private suspend fun obtainFirebaseToken(): String {
        val tokenResult = getFirebaseToken()
        return if (tokenResult.isSuccess) {
            val token = tokenResult.getOrNull()
            if (token.isNullOrEmpty()) {
                throw OttSetupError.TokenError(Exception("Firebase token is null or empty"))
            } else {
                printLog("OTT: ${OttManagerConstants.LOG_TOKEN_OBTAINED}", TAG)
                token
            }
        } else {
            val exception = tokenResult.exceptionOrNull()
            throw OttSetupError.TokenError(exception)
        }
    }

    private suspend fun registerTokenWithServer(token: String) {
        when (val result = registerFirebaseToken(token)) {
            is Resource.Success -> {
                printLog("OTT: ${OttManagerConstants.LOG_TOKEN_REGISTERED}", TAG)
            }

            is Resource.Error -> {
                throw OttSetupError.TokenRegistrationError(result.message ?: "Unknown error")
            }
        }
    }

    private suspend fun establishAndRegisterSocketConnection(token: String) {
        try {
            // Step 1: Start socket connection
            val socketId = connectAndWaitForSocket(token)

            // Step 2: Register socket with server
            registerSocketWithServer(token, socketId)

            printLog("OTT: ${OttManagerConstants.LOG_SOCKET_CONNECTED}", TAG)
        } catch (e: Exception) {
            when (e) {
                is OttSetupError -> throw e
                else -> throw OttSetupError.SocketConnectionError(
                    e.message ?: "Unknown socket error",
                )
            }
        }
    }

    private suspend fun connectAndWaitForSocket(fcmToken: String): String {
        return try {
            _socketConnectionState.value = IOttManager.SocketConnectionState.Connecting
            socketConnectionDeferred = CompletableDeferred()

            // Get socket configuration
            val (socketBaseUrl, socketPath) = getSocketConfiguration()

            // Start WebSocket connection
            val userInfo = getCurrentUserInfo()
            connectWebSocketUseCase.invoke(
                socketBaseUrl,
                socketPath,
                fcmToken,
                userInfo.cifNo,
                userInfo.phoneNumber,
            )

            // Wait for connection with timeout
            val socketId = withTimeoutOrNull(OttManagerConstants.SOCKET_CONNECTION_TIMEOUT_MS) {
                socketConnectionDeferred?.await()
            }

            if (socketId.isNullOrEmpty()) {
                throw OttSetupError.SocketConnectionError("Socket connection timeout")
            }

            printLog(TAG, "Socket connected with ID: $socketId")
            socketId
        } finally {
            socketConnectionDeferred = null
        }
    }

    override suspend fun syncMessagesFromServer(forceFullSync: Boolean): Resource<Int> {
        return safeContextCall(
            context = scope.coroutineContext,
            errorCode = OttManagerConstants.OttErrorCodes.ERROR_DB_SYNC,
            errorPrefix = "Messages sync failed",
        ) {
            printLog("OTT: Starting messages sync...", TAG)

            // Clear messages if needed
            if (forceFullSync) {
                clearUserMessages()
            }

            // Sync all pages
            val totalSynced = syncAllMessagePages()

            printLog("OTT: Sync completed. Total synced: $totalSynced messages", TAG)
            totalSynced
        }
    }

    private suspend fun clearUserMessages() {
        val cif = currentUserInfo?.cifNo ?: ""
        val phone = currentUserInfo?.phoneNumber ?: ""

        ottMessageDao.deleteAllMessagesForUser(cif, phone)
        printLog("OTT: Cleared all messages for full sync", TAG)
    }

    private suspend fun syncAllMessagePages(): Int {
        var currentPage = 0
        val pageSize = 500 // Default page size
        var totalSynced = 0
        val maxPages = 5 // Safety limit

        while (currentPage < maxPages) {
            val syncResult = syncSinglePage(currentPage, pageSize)

            when (syncResult) {
                is PageSyncResult.Success -> {
                    totalSynced += syncResult.syncedCount

                    if (syncResult.isLastPage) {
                        break
                    }

                    currentPage++
                }
                is PageSyncResult.Error -> {
                    printLogError("OTT: Sync failed at page $currentPage: ${syncResult.message}", TAG)
                    throw Exception(syncResult.message)
                }
            }
        }

        return totalSynced
    }

    private suspend fun syncSinglePage(page: Int, pageSize: Int): PageSyncResult {
        val params = createGetAllMessageParams(page, pageSize)

        printLog("OTT: [SYNC] Syncing page $page for CIF: ${params.cif}, Phone: ${params.phone}", TAG)

        return when (val result = getAllMessage(params)) {
            is Resource.Success -> {
                val data = result.data.data
                printLog("OTT: [SYNC] Fetched page $page, items: ${data?.content?.size}", TAG)

                val syncedCount = data?.content?.let { messages ->
                    saveMessagesToDatabase(messages)
                } ?: 0

                PageSyncResult.Success(
                    syncedCount = syncedCount,
                    isLastPage = data?.last == true || data?.empty == true,
                )
            }
            is Resource.Error -> {
                printLogError("OTT: [SYNC] Error syncing page $page: ${result.message}", TAG)
                PageSyncResult.Error(result.message)
            }
        }
    }

    private fun createGetAllMessageParams(page: Int, pageSize: Int): OttGetAllMessageParams {
        return OttGetAllMessageParams(
            channelType = "default",
            cif = currentUserInfo?.cifNo ?: "",
            phone = currentUserInfo?.phoneNumber ?: "",
            username = currentUserInfo?.username ?: "",
            page = page,
            size = pageSize.toString(),
            channelId = "efast",
        )
    }

    private suspend fun saveMessagesToDatabase(messages: List<OttMessageItemDomain?>): Int {
        // Filter out null messages first
        val nonNullMessages = messages.filterNotNull()
        if (nonNullMessages.isEmpty()) {
            printLog("OTT: [SYNC-SAVE] No messages to save (all null)", TAG)
            return 0
        }

        val cif = currentUserInfo?.cifNo ?: ""
        val phone = currentUserInfo?.phoneNumber ?: ""

        printLog("OTT: [SYNC-SAVE] Saving ${nonNullMessages.size} messages for CIF: $cif, Phone: $phone", TAG)

        if (cif.isEmpty() || phone.isEmpty()) {
            printLogError("OTT: [SYNC-SAVE] ERROR - Cannot save: Empty CIF or Phone!", TAG)
            return 0
        }

        // Check for duplicates
        val uniqueMessages = nonNullMessages.distinctBy { it.messageId }
        if (uniqueMessages.size != nonNullMessages.size) {
            printLog("OTT: [SYNC-SAVE] Found ${nonNullMessages.size - uniqueMessages.size} duplicate messages", TAG)
        }

        val entities = (uniqueMessages as List<OttMessageItemDomain?>).toEntities(cif, phone)
        ottMessageDao.insertMessages(entities)

        printLog("OTT: [SYNC-SAVE] SUCCESS - Saved ${entities.size} messages to DB", TAG)

        return entities.size
    }

    private sealed class PageSyncResult {
        data class Success(val syncedCount: Int, val isLastPage: Boolean) : PageSyncResult()
        data class Error(val message: String) : PageSyncResult()
    }

    private suspend fun registerSocketWithServer(fcmToken: String, socketId: String) {
        val result = registerSocket(fcmToken, socketId)
        when (result) {
            is Resource.Success -> {
                printLog("OTT: Socket registered with server successfully", TAG)
            }

            is Resource.Error -> {
                throw OttSetupError.SocketRegistrationError(
                    result.message ?: "Unknown registration error",
                )
            }
        }
    }

    private fun completeSetup(): Resource<Boolean> {
        _ottState.value = IOttManager.OttState.Initialized
        printLog("OTT: ${OttManagerConstants.LOG_SETUP_SUCCESS}", TAG)
        return Resource.Success(true)
    }

    // =====================================================
    // PRIVATE HELPER METHODS - UTILITY
    // =====================================================
    private suspend fun getSocketConfiguration(): Pair<String, String> {
        val socketBaseUrl = configRepository.getSocketBaseUrl()
        val socketPath = configRepository.getSocketPath()

        // Sử dụng validator
        val validationResult = validator.validateSocketConfig(socketBaseUrl, socketPath)
        when (validationResult) {
            is OttManagerValidator.ValidationResult.Success -> {
                return Pair(socketBaseUrl, socketPath)
            }

            is OttManagerValidator.ValidationResult.Error -> {
                throw OttSetupError.ConfigurationError(validationResult.message)
            }
        }
    }

    @Throws(IllegalStateException::class)
    private fun getCurrentUserInfo(): UserInfo {
        val userInfo = currentUserInfo
            ?: throw IllegalStateException("User info not initialized")

        if (!userInfo.isValid()) {
            throw IllegalStateException("User info is invalid")
        }

        return userInfo
    }

    private fun createRegisterDeviceParams(
        userInfo: UserInfo,
        fcmToken: String,
        socketId: String,
        tokenType: String,
    ): OttRegisterDeviceParams {
        return OttRegisterDeviceParams(
            channelType = OttManagerConstants.DEFAULT_CHANNEL_TYPE,
            cif = userInfo.cifNo,
            phone = userInfo.phoneNumber,
            fcmToken = fcmToken,
            socketId = socketId,
            username = userInfo.username,
            tokenType = tokenType,
        )
    }

    private fun handleStatusUpdateMessage(message: SocketMessage) {
        scope.launch {
            try {
                val messageId = message.messageId
                val status = message.data?.get("status")?.toIntOrNull() ?: return@launch

                _messageStatusUpdates.emit(
                    IOttManager.MessageStatusUpdate(
                        messageId = messageId,
                        newStatus = status,
                    ),
                )
                printLog("OTT: Status update processed: $messageId -> $status", TAG)
            } catch (e: Exception) {
                printLogError("OTT: Error processing message status update", TAG)
            }
        }
    }

    private fun isAlreadySetupForCif(params: OttSetupParams): Boolean {
        // Check if initialized
        if (_ottState.value != IOttManager.OttState.Initialized) {
            return false
        }

        // Check same CIF
        val currentUser = currentUserInfo
        return currentUser != null && currentUser.cifNo == params.cifNo
    }

    private fun handleTokenChange(newToken: String) {
        printLog("OTT: Firebase token changed, registering new token...", TAG)
        scope.launch {
            try {
                val result = registerFirebaseToken(newToken)
                when (result) {
                    is Resource.Success -> {
                        printLog("OTT: New Firebase token registered successfully", TAG)
                    }

                    is Resource.Error -> {
                        printLogError("OTT: Failed to register new Firebase token: ${result.message}", TAG)
                    }
                }
            } catch (e: Exception) {
                printLogError("OTT: Exception registering new Firebase token", TAG, e)
            }
        }
    }

    override suspend fun saveMessageFromNotification(message: OttMessage): Resource<Boolean> {
        return withContext(Dispatchers.IO) {
            try {
                printLog("OTT: Saving message from notification: ${message.messageId}", TAG)

                // Get current user info
                val userInfo = currentUserInfo
                if (userInfo == null) {
                    printLogError("OTT: Cannot save message - no user info available", TAG)
                    return@withContext Resource.Error("No user logged in", "NO_USER")
                }

                // Validate CIF and phone are not empty
                if (userInfo.cifNo.isBlank() || userInfo.phoneNumber.isBlank()) {
                    printLogError("OTT: Cannot save message - CIF or Phone is empty! CIF='${userInfo.cifNo}', Phone='${userInfo.phoneNumber}'", TAG)
                    return@withContext Resource.Error("Invalid user info", "INVALID_USER_INFO")
                }

                printLog("OTT: Saving with user info - CIF: ${userInfo.cifNo}, Phone: ${userInfo.phoneNumber}", TAG)

                // Convert OttMessage to OttMessageEntity
                val entity = OttMessageEntity(
                    messageId = message.messageId,
                    title = message.title,
                    content = message.content,
                    cif = userInfo.cifNo,
                    phone = userInfo.phoneNumber,
                    status = message.state,
                    sendTime = message.sendTime,
                    messageType = message.messageType,
                    readTime = null,
                    data = null,
                    priority = null,
                    timestamp = parseSendTimeToTimestamp(message.sendTime),
                )

                // Save to database
                ottMessageDao.insertMessage(entity)
                printLog("OTT: Saved message from notification to DB: ${message.messageId} with CIF: ${userInfo.cifNo}", TAG)

                // Don't notify listener - this message is from tapping old notification
                // The notification was already shown by Firebase SDK

                Resource.Success(true)
            } catch (e: Exception) {
                printLogError("OTT: Error saving message from notification", TAG, e)
                Resource.Error("Failed to save message: ${e.message}", "SAVE_ERROR")
            }
        }
    }

    private fun handleSetupFailure(error: Throwable): Resource<Boolean> {
        val ottError = when (error) {
            is OttSetupError -> error
            else -> OttSetupError.GeneralError(error)
        }

        printLogError("OTT: Setup failed: ${ottError.message}", TAG)

        _ottState.value = IOttManager.OttState.Error(
            message = ottError.message ?: "Unknown error",
            throwable = ottError,
        )

        return Resource.Error(
            message = ottError.message ?: "Unknown error",
            code = ottError.code,
        )
    }

    override fun isProcessingNotification(): Boolean {
        return isProcessingNotificationFlag
    }

    override fun setProcessingNotification(processing: Boolean) {
        printLog("OTT: Setting notification processing flag to: $processing", TAG)
        isProcessingNotificationFlag = processing
    }
}