package com.vietinbank.core_ott.manager

import com.vietinbank.core_domain.models.ott.OttSetupParams
import com.vietinbank.core_domain.ott.IOttManager
import com.vietinbank.core_ott.extensions.isValidInput
import com.vietinbank.core_ott.initialization.OttBuilder
import com.vietinbank.core_ott.notification.NotificationPermissionHelper
import javax.inject.Inject

/**
 * Validator class cho OTT Manager setup validation
 * Tách biệt logic validation để dễ test và maintain
 */
class OttManagerValidator @Inject constructor(
    private val notificationPermissionHelper: NotificationPermissionHelper,
    private val ottBuilder: OttBuilder,
) {

    /**
     * Validates tất cả preconditions cho OTT setup
     */
    fun validateSetupPreconditions(): ValidationResult {
        // Check notification permission
        if (!notificationPermissionHelper.hasNotificationPermission()) {
            return ValidationResult.Error(
                code = OttManagerConstants.ERROR_PERMISSION,
                message = "Cần quyền hiển thị thông báo",
                ottState = IOttManager.OttState.NeedsNotificationPermission,
            )
        }

        // Check OttBuilder initialization
        if (!ottBuilder.isInitialized()) {
            return ValidationResult.Error(
                code = OttManagerConstants.ERROR_BUILDER_NOT_INITIALIZED,
                message = "OttBuilder chưa được khởi tạo",
            )
        }

        return ValidationResult.Success
    }

    /**
     * Validates user parameters
     */
    fun validateUserParams(params: OttSetupParams): ValidationResult {
        return when {
            !params.userId.isValidInput() -> ValidationResult.Error(
                code = OttManagerConstants.ERROR_INVALID_PARAMS,
                message = "User ID không được để trống",
            )
            !params.cifNo.isValidInput() -> ValidationResult.Error(
                code = OttManagerConstants.ERROR_INVALID_PARAMS,
                message = "CIF không được để trống",
            )
            !params.phoneNumber.isValidInput() -> ValidationResult.Error(
                code = OttManagerConstants.ERROR_INVALID_PARAMS,
                message = "Số điện thoại không được để trống",
            )
            !params.username.isValidInput() -> ValidationResult.Error(
                code = OttManagerConstants.ERROR_INVALID_PARAMS,
                message = "Username không được để trống",
            )
            else -> ValidationResult.Success
        }
    }

    /**
     * Validates socket configuration
     */
    fun validateSocketConfig(baseUrl: String, path: String): ValidationResult {
        return when {
            !baseUrl.isValidInput() -> ValidationResult.Error(
                code = OttManagerConstants.ERROR_WEBSOCKET_CONFIG,
                message = "Socket base URL không được để trống",
            )
            !path.isValidInput() -> ValidationResult.Error(
                code = OttManagerConstants.ERROR_WEBSOCKET_CONFIG,
                message = "Socket path không được để trống",
            )
            !baseUrl.startsWith("http") -> ValidationResult.Error(
                code = OttManagerConstants.ERROR_WEBSOCKET_CONFIG,
                message = "Socket base URL không hợp lệ",
            )
            else -> ValidationResult.Success
        }
    }

    /**
     * Validates Firebase token
     */
    fun validateFirebaseToken(token: String?): ValidationResult {
        return if (token.isValidInput()) {
            ValidationResult.Success
        } else {
            ValidationResult.Error(
                code = OttManagerConstants.ERROR_NULL_TOKEN,
                message = "Firebase token is null or empty",
            )
        }
    }

    /**
     * Validation result sealed class
     */
    sealed class ValidationResult {
        object Success : ValidationResult()
        data class Error(
            val code: String,
            val message: String,
            val ottState: IOttManager.OttState? = null,
        ) : ValidationResult()
    }
}