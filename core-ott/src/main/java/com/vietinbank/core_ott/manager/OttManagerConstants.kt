package com.vietinbank.core_ott.manager

/**
 * Constants được sử dụng trong OTT Manager
 */
object OttManagerConstants {

    // =====================================================
    // TIMEOUTS (milliseconds)
    // =====================================================
    const val SOCKET_CONNECTION_TIMEOUT_MS = 15_000L
    const val TOKEN_OPERATION_TIMEOUT_MS = 10_000L
    const val MESSAGE_STATUS_UPDATE_TIMEOUT_MS = 5_000L

    // =====================================================
    // TOKEN TYPES
    // =====================================================
    const val FCM_TOKEN_TYPE = "fcmToken"
    const val SOCKET_TOKEN_TYPE = "socketId"

    // =====================================================
    // CHANNEL TYPES
    // =====================================================
    const val DEFAULT_CHANNEL_TYPE = "default"

    // =====================================================
    // ERROR CODES
    // =====================================================
    const val ERROR_PERMISSION = "ERROR_PERMISSION"
    const val ERROR_BUILDER_NOT_INITIALIZED = "ERROR_OTTBUILDER_NOT_INITIALIZED"
    const val ERROR_NULL_TOKEN = "ERROR_NULL_TOKEN"
    const val ERROR_GET_TOKEN = "ERROR_GET_TOKEN"
    const val ERROR_REGISTER_TOKEN = "ERROR_REGISTER_TOKEN"
    const val ERROR_REGISTER_SOCKET = "ERROR_REGISTER_SOCKET"
    const val ERROR_SOCKET_TIMEOUT = "ERROR_SOCKET_ID_TIMEOUT"
    const val ERROR_WEBSOCKET_CONFIG = "ERROR_CONFIG_WEBSOCKET"
    const val ERROR_SETUP_GENERAL = "ERROR_SETUP_OTT"
    const val ERROR_UPDATE_MESSAGE_STATUS = "ERROR_UPDATE_MESSAGE_STATUS"
    const val ERROR_SOCKET_CONNECTION = "ERROR_AWAIT_SOCKET"
    const val ERROR_INVALID_PARAMS = "ERROR_INVALID_PARAMS"

    // =====================================================
    // LOG MESSAGES
    // =====================================================
    const val LOG_SETUP_START = "Starting OTT setup process..."
    const val LOG_SETUP_SUCCESS = "OTT setup completed successfully"
    const val LOG_TOKEN_OBTAINED = "Firebase token obtained successfully"
    const val LOG_TOKEN_REGISTERED = "Firebase token registered successfully"
    const val LOG_SOCKET_CONNECTED = "Socket connected and registered successfully"
    const val LOG_CLEANUP_START = "Starting OTT cleanup..."
    const val LOG_CLEANUP_COMPLETE = "OTT cleanup completed"

    // =====================================================
    // SOCKET EVENTS
    // =====================================================
    const val EVENT_MESSAGE = "message"
    const val EVENT_STATUS_UPDATE = "status_update"
    const val EVENT_NOTIFICATION = "notification"
    const val EVENT_TRANSACTION = "transaction"

    // =====================================================
    // MESSAGE TYPES
    // =====================================================
    const val MESSAGE_TYPE_STATUS_UPDATE = "status_update"
    const val MESSAGE_TYPE_NOTIFICATION = "notification"
    const val MESSAGE_TYPE_MESSAGE = "message"

    /**
     * Centralized error codes for OTT Manager
     * Format: OTTXXX where XXX is the error number
     */
    object OttErrorCodes {
        // Setup & Initialization Errors (OTT001-099)
        const val ERROR_SETUP_GENERAL = "OTT001"
        const val ERROR_PERMISSION_DENIED = "OTT002"
        const val ERROR_FIREBASE_INIT = "OTT003"
        const val ERROR_BUILDER_CONFIG = "OTT004"
        const val ERROR_SOCKET_INIT = "OTT005"

        // Registration Errors (OTT100-199)
        const val ERROR_REGISTER_DEVICE = "OTT100"
        const val ERROR_REGISTER_FIREBASE = "OTT101"
        const val ERROR_REGISTER_SOCKET = "OTT102"
        const val ERROR_INVALID_TOKEN = "OTT103"

        // Network & API Errors (OTT200-299)
        const val ERROR_NETWORK_GENERAL = "OTT200"
        const val ERROR_API_FETCH_MESSAGES = "OTT201"
        const val ERROR_API_UPDATE_STATUS = "OTT202"
        const val ERROR_SOCKET_CONNECTION = "OTT203"
        const val ERROR_SOCKET_DISCONNECTION = "OTT204"

        // Database Errors (OTT300-399)
        const val ERROR_DB_GENERAL = "OTT300"
        const val ERROR_DB_INSERT = "OTT301"
        const val ERROR_DB_UPDATE = "OTT302"
        const val ERROR_DB_DELETE = "OTT303"
        const val ERROR_DB_QUERY = "OTT304"
        const val ERROR_DB_SYNC = "OTT305"

        // Message Processing Errors (OTT400-499)
        const val ERROR_MESSAGE_INVALID = "OTT400"
        const val ERROR_MESSAGE_DUPLICATE = "OTT401"
        const val ERROR_MESSAGE_PARSE = "OTT402"
        const val ERROR_TEST_MESSAGE = "OTT403"

        // State & Configuration Errors (OTT500-599)
        const val ERROR_INVALID_STATE = "OTT500"
        const val ERROR_MISSING_CIF = "OTT501"
        const val ERROR_MISSING_CONFIG = "OTT502"
        const val ERROR_NOT_INITIALIZED = "OTT503"

        // Unknown/Generic Errors (OTT999)
        const val ERROR_UNKNOWN = "OTT999"
    }
}