package com.vietinbank.core_ott.token

import android.content.Context
import android.util.Log
import com.google.android.gms.tasks.Tasks
import com.google.firebase.messaging.FirebaseMessaging
import com.vietinbank.core_domain.ott.IDeviceTokenProvider
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Firebase implementation of IDeviceTokenProvider
 */
@Singleton
class FirebaseTokenProvider @Inject constructor(
    private val context: Context,
) : IDeviceTokenProvider {

    private var tokenChangeListener: ((String) -> Unit)? = null

    override suspend fun getDeviceToken(): Result<String> {
        return withContext(Dispatchers.IO) {
            try {
                val token = Tasks.await(FirebaseMessaging.getInstance().token)
                Result.success(token)
            } catch (e: Exception) {
                Log.e(TAG, "Error getting Firebase token", e)
                Result.failure(e)
            }
        }
    }

    override fun registerTokenChangeListener(listener: (String) -> Unit) {
        this.tokenChangeListener = listener
    }

    companion object {
        private const val TAG = "FirebaseTokenProvider"
    }
}
