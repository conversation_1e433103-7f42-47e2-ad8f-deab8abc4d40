package com.vietinbank.core_ott.extensions

import com.vietinbank.core_common.extensions.printLogError
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_data.database.ott.entity.OttMessageEntity
import com.vietinbank.core_domain.models.ott.OttMessageItemDomain
import com.vietinbank.core_ott.manager.OttManagerConstants
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.Locale
import kotlin.coroutines.CoroutineContext

/**
 * Extension functions cho OTT Manager
 * Giúp giảm boilerplate code và tăng tính nhất quán
 */

/**
 * Converts any Resource.Error to a setup error with custom prefix
 */
fun <T> Resource<T>.toSetupError(prefix: String): Resource<Boolean> {
    return when (this) {
        is Resource.Error -> Resource.Error("$prefix: $message", code)
        is Resource.Success -> Resource.Success(true)
    }
}

/**
 * Executes a block and converts exceptions to Resource.Error
 */
inline fun <T> safeResourceCall(
    errorCode: String,
    errorPrefix: String = "Operation failed",
    block: () -> T,
): Resource<T> {
    return try {
        Resource.Success(block())
    } catch (e: Exception) {
        printLogError("OTT: Error in safeResourceCall: ${e.message}", "OTT", e)
        Resource.Error("$errorPrefix: ${e.message}", errorCode)
    }
}

/**
 * Executes a suspend block and converts exceptions to Resource.Error
 */
suspend inline fun <T> safeSuspendResourceCall(
    errorCode: String,
    errorPrefix: String = "Operation failed",
    crossinline block: suspend () -> T,
): Resource<T> {
    return try {
        Resource.Success(block())
    } catch (e: Exception) {
        printLogError("OTT: Error in safeSuspendResourceCall: ${e.message}", "OTT", e)
        Resource.Error("$errorPrefix: ${e.message}", errorCode)
    }
}

/**
 * Executes a suspend block with context switch and error handling
 */
suspend inline fun <T> safeContextCall(
    context: CoroutineContext,
    errorCode: String,
    errorPrefix: String = "Context operation failed",
    crossinline block: suspend () -> T,
): Resource<T> {
    return try {
        withContext(context) {
            Resource.Success(block())
        }
    } catch (e: Exception) {
        printLogError("OTT: Error in safeContextCall: ${e.message}", "OTT", e)
        Resource.Error("$errorPrefix: ${e.message}", errorCode)
    }
}

/**
 * Validates if a string is not empty and not blank
 */
fun String?.isValidInput(): Boolean {
    return !this.isNullOrEmpty() && !this.isNullOrBlank()
}

/**
 * Safely gets a value from a map with default fallback
 */
fun <K, V> Map<K, V>.safeGet(key: K, default: V): V {
    return this[key] ?: default
}

fun OttMessageItemDomain.toEntity(cif: String, phone: String): OttMessageEntity {
    val extractedTitle = title ?: content.substringBefore(":", "Thông báo")
    val finalSendTime = sendTime ?: ""

    return OttMessageEntity(
        messageId = messageId,
        title = extractedTitle,
        content = content,
        cif = cif,
        phone = phone,
        status = status,
        data = data?.let { Utils.g().provideGson().toJson(it) },
        sendTime = finalSendTime,
        messageType = type ?: "",
        timestamp = if (finalSendTime.isNotEmpty()) parseSendTimeToTimestamp(finalSendTime) else System.currentTimeMillis(),
    )
}

/**
 * Convert list of OttMessageItemDomain to list of OttMessageEntity
 * Filters out null items automatically
 */
fun List<OttMessageItemDomain?>.toEntities(cif: String, phone: String): List<OttMessageEntity> {
    return this.filterNotNull().map { it.toEntity(cif, phone) }
}

/**
 * Parse sendTime string to timestamp
 * Format: "dd/MM/yyyy HH:mm:ss"
 */
fun parseSendTimeToTimestamp(sendTime: String): Long {
    return try {
        SimpleDateFormat("dd/MM/yyyy HH:mm:ss", Locale.getDefault())
            .parse(sendTime)?.time ?: System.currentTimeMillis()
    } catch (e: Exception) {
        printLogError("OTT: Error parsing sendTime: $sendTime", "OTT", e)
        System.currentTimeMillis()
    }
}

// Database operations wrapper
suspend inline fun <T> safeDbOperation(
    errorPrefix: String = "Database operation failed",
    crossinline block: suspend () -> T,
): Resource<T> = safeSuspendResourceCall(
    errorCode = OttManagerConstants.OttErrorCodes.ERROR_DB_GENERAL,
    errorPrefix = errorPrefix,
    block = block,
)

// Network/API operations wrapper
suspend inline fun <T> safeNetworkCall(
    errorPrefix: String = "Network operation failed",
    crossinline block: suspend () -> T,
): Resource<T> = safeSuspendResourceCall(
    errorCode = OttManagerConstants.OttErrorCodes.ERROR_NETWORK_GENERAL,
    errorPrefix = errorPrefix,
    block = block,
)

// Socket operations wrapper
suspend inline fun <T> safeSocketOperation(
    errorPrefix: String = "Socket operation failed",
    crossinline block: suspend () -> T,
): Resource<T> = safeSuspendResourceCall(
    errorCode = OttManagerConstants.OttErrorCodes.ERROR_SOCKET_CONNECTION,
    errorPrefix = errorPrefix,
    block = block,
)

// Message processing wrapper
suspend inline fun <T> safeMessageOperation(
    errorPrefix: String = "Message operation failed",
    crossinline block: suspend () -> T,
): Resource<T> = safeSuspendResourceCall(
    errorCode = OttManagerConstants.OttErrorCodes.ERROR_MESSAGE_INVALID,
    errorPrefix = errorPrefix,
    block = block,
)

/**
 * Resource chaining extensions
 */
suspend fun <T, R> Resource<T>.flatMap(
    transform: suspend (T) -> Resource<R>,
): Resource<R> = when (this) {
    is Resource.Success -> transform(data)
    is Resource.Error -> Resource.Error(message, code)
}

suspend fun <T> Resource<T>.onSuccess(
    action: suspend (T) -> Unit,
): Resource<T> {
    if (this is Resource.Success) {
        action(data)
    }
    return this
}

suspend fun <T> Resource<T>.onError(
    action: suspend (message: String, code: String) -> Unit,
): Resource<T> {
    if (this is Resource.Error) {
        action(message, code)
    }
    return this
}

fun <T> Resource<T>.mapError(
    transform: (message: String, code: String) -> Pair<String, String>,
): Resource<T> = when (this) {
    is Resource.Success -> this
    is Resource.Error -> {
        val (newMessage, newCode) = transform(message, code)
        Resource.Error(newMessage, newCode)
    }
}

/**
 * Validation helpers
 */
fun requireCif(cif: String?): String {
    return requireNotNull(cif) { "CIF is required for this operation" }
}

fun requireSocketToken(token: String?): String {
    return requireNotNull(token) { "Socket token is required for this operation" }
}

fun validateFirebaseToken(token: String): String {
    require(token.isValidInput()) { "Invalid Firebase token format" }
    return token
}