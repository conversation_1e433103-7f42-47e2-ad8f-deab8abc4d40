package com.vietinbank.core_ott.di

import android.content.Context
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_data.database.ott.dao.OttMessageDao
import com.vietinbank.core_data.websocket.OttWebSocketServiceImpl
import com.vietinbank.core_domain.ott.IDeviceTokenProvider
import com.vietinbank.core_domain.ott.INotificationHandler
import com.vietinbank.core_domain.ott.IOttFirebaseInitializer
import com.vietinbank.core_domain.ott.IOttManager
import com.vietinbank.core_domain.ott.IOttWebSocketService
import com.vietinbank.core_domain.ott.ISocketClient
import com.vietinbank.core_domain.repository.ConfigRepository
import com.vietinbank.core_domain.usecase.ott.ConnectWebSocketUseCase
import com.vietinbank.core_domain.usecase.ott.OttGetAllMessageUseCase
import com.vietinbank.core_domain.usecase.ott.OttRegisterDeviceUseCase
import com.vietinbank.core_domain.usecase.ott.UpdateMessageStatusWebSocketUseCase
import com.vietinbank.core_ott.firebase.OttFirebaseInitializerImpl
import com.vietinbank.core_ott.initialization.OttBuilder
import com.vietinbank.core_ott.manager.OttManagerImpl
import com.vietinbank.core_ott.manager.OttManagerValidator
import com.vietinbank.core_ott.notification.NotificationHandlerImpl
import com.vietinbank.core_ott.notification.NotificationPermissionHelper
import com.vietinbank.core_ott.queue.OttMessageQueue
import com.vietinbank.core_ott.service.OttSetupServiceImpl
import com.vietinbank.core_ott.token.FirebaseTokenProvider
import com.vietinbank.core_ott.websocket.OttSocketIOClient
import com.vietinbank.core_ott.websocket.SocketClientAdapter
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object OttModule {

    // Firebase và Token Provider
    @Provides
    @Singleton
    fun provideFirebaseTokenProvider(@ApplicationContext context: Context): FirebaseTokenProvider {
        return FirebaseTokenProvider(context)
    }

    @Provides
    @Singleton
    fun provideDeviceTokenProvider(firebaseTokenProvider: FirebaseTokenProvider): IDeviceTokenProvider {
        return firebaseTokenProvider
    }

    @Provides
    @Singleton
    fun provideOttFirebaseInitializerImpl(
        @ApplicationContext context: Context,
    ): OttFirebaseInitializerImpl {
        return OttFirebaseInitializerImpl(context)
    }

    @Provides
    @Singleton
    fun provideIOttFirebaseInitializer(
        firebaseInitializerImpl: OttFirebaseInitializerImpl,
    ): IOttFirebaseInitializer {
        return firebaseInitializerImpl
    }

    // Socket.IO Client
    @Provides
    @Singleton
    fun provideOttSocketIOClient(
        @ApplicationContext context: Context,
    ): OttSocketIOClient {
        return OttSocketIOClient(context)
    }

    // Socket Client Adapter (implement ISocketClient interface)
    @Provides
    @Singleton
    fun provideSocketClient(socketIOClient: OttSocketIOClient): ISocketClient {
        return SocketClientAdapter(socketIOClient)
    }

    @Provides
    @Singleton
    fun provideOttWebSocketService(socketClient: ISocketClient): IOttWebSocketService {
        return OttWebSocketServiceImpl(socketClient)
    }

    @Provides
    @Singleton
    fun provideOttBuilder(
        @ApplicationContext context: Context,
        firebaseInitializer: IOttFirebaseInitializer,
        webSocketService: IOttWebSocketService,
        socketClient: OttSocketIOClient,
        notificationHandler: INotificationHandler,
    ): OttBuilder {
        return OttBuilder(
            context,
            firebaseInitializer,
            webSocketService,
            socketClient,
            notificationHandler,
        )
    }

    @Provides
    @Singleton
    fun provideNotificationPermissionHelper(@ApplicationContext context: Context): NotificationPermissionHelper {
        return NotificationPermissionHelper(context)
    }

    @Provides
    @Singleton
    fun provideNotificationHandlerImpl(@ApplicationContext context: Context): NotificationHandlerImpl {
        return NotificationHandlerImpl(context)
    }

    @Provides
    @Singleton
    fun provideINotificationHandler(impl: NotificationHandlerImpl): INotificationHandler {
        return impl
    }

    @Provides
    @Singleton
    fun provideOttManager(
        ottBuilder: OttBuilder,
        deviceTokenProvider: IDeviceTokenProvider,
        ottRegisterTokenUseCase: OttRegisterDeviceUseCase,
        connectWebSocketUseCase: ConnectWebSocketUseCase,
        updateMessageStatusWebSocketUseCase: UpdateMessageStatusWebSocketUseCase,
        ottGetAllMessageUseCase: OttGetAllMessageUseCase,
        configRepository: ConfigRepository,
        socketClient: OttSocketIOClient,
        notificationHandler: INotificationHandler,
        notificationPermissionHelper: NotificationPermissionHelper,
        validator: OttManagerValidator,
        ottMessageDao: OttMessageDao,
        messageQueue: OttMessageQueue,
    ): IOttManager {
        return OttManagerImpl(
            ottBuilder,
            deviceTokenProvider,
            ottRegisterTokenUseCase,
            connectWebSocketUseCase,
            updateMessageStatusWebSocketUseCase,
            ottGetAllMessageUseCase,
            configRepository,
            socketClient,
            notificationHandler,
            notificationPermissionHelper,
            validator,
            ottMessageDao,
            messageQueue,
        )
    }

    @Provides
    @Singleton
    fun provideOttManagerValidator(
        notificationPermissionHelper: NotificationPermissionHelper,
        ottBuilder: OttBuilder,
    ): OttManagerValidator {
        return OttManagerValidator(notificationPermissionHelper, ottBuilder)
    }

    @Provides
    @Singleton
    fun provideOttMessageQueue(): OttMessageQueue {
        return OttMessageQueue()
    }

    @Provides
    @Singleton
    fun provideOttSetupService(
        ottManager: IOttManager,
    ): IOttSetupService {
        return OttSetupServiceImpl(ottManager)
    }
}