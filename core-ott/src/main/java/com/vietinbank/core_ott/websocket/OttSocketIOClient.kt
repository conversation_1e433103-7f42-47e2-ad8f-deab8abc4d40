package com.vietinbank.core_ott.websocket

import android.content.Context
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.extensions.printLogError
import com.vietinbank.core_domain.models.ott.SocketMessage
import com.vietinbank.core_ott.manager.OttManagerConstants
import io.socket.client.IO
import io.socket.client.Socket
import io.socket.emitter.Emitter
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.util.concurrent.CopyOnWriteArraySet
import java.util.concurrent.atomic.AtomicBoolean
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class OttSocketIOClient @Inject constructor(
    private val context: Context,
) {

    // =====================================================
    // CONSTANTS
    // =====================================================
    companion object {
        private const val TAG = "OTT"

        // Message Types - delegate to OttManagerConstants where possible
        const val MESSAGE_TYPE_STATUS_UPDATE = 1
        const val MESSAGE_TYPE_NOTIFICATION = 2
        const val MESSAGE_TYPE_MESSAGE = 0
        const val MESSAGE_TYPE_TEXT = 0
        const val MESSAGE_TYPE_TRANSACTION = 3

        // Events - sử dụng từ OttManagerConstants
        private val EVENT_MESSAGE = OttManagerConstants.EVENT_MESSAGE
        private val EVENT_STATUS_UPDATE = OttManagerConstants.EVENT_STATUS_UPDATE
        private val EVENT_NOTIFICATION = OttManagerConstants.EVENT_NOTIFICATION
        private const val EVENT_TRANSACTION = OttManagerConstants.EVENT_TRANSACTION

        // Reconnection Config
        private const val RECONNECTION_ATTEMPTS = 999
        private const val RECONNECTION_DELAY = 5000L
        private const val MAX_RECONNECTION_DELAY = 30000L

        // Connection Timeout
        private const val CONNECTION_TIMEOUT = 20000L
    }

    // =====================================================
    // STATE MANAGEMENT
    // =====================================================
    private var socket: Socket? = null
    private var isConnected = AtomicBoolean(false)
    private var isConnecting = AtomicBoolean(false)
    private var connectionParams: ConnectionParams? = null
    private var reconnectJob: Job? = null

    // =====================================================
    // COROUTINE SCOPE
    // =====================================================
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    // =====================================================
    // CALLBACK MANAGEMENT
    // =====================================================
    private val callbacks = CopyOnWriteArraySet<SocketCallback>()

    // =====================================================
    // DATA CLASSES
    // =====================================================
    data class ConnectionParams(
        val baseUrl: String,
        val path: String,
        val token: String,
        val cif: String,
        val phone: String,
    )

    // =====================================================
    // INTERFACE DEFINITIONS
    // =====================================================
    interface SocketCallback {
        fun onConnected()
        fun onDisconnected()
        fun onMessage(message: SocketMessage)
        fun onError(error: Throwable)
    }

    // =====================================================
    // PUBLIC API METHODS
    // =====================================================

    /**
     * Kết nối với Socket.IO server
     */
    fun connect(baseUrl: String, path: String, token: String, cif: String, phone: String) {
        if (isConnected.get() || isConnecting.get()) {
            printLog("OTT: Socket.IO already connected or connecting", TAG)
            return
        }

        scope.launch {
            try {
                isConnecting.set(true)

                // Store connection parameters for reconnection
                connectionParams = ConnectionParams(
                    baseUrl = baseUrl,
                    path = path,
                    token = token,
                    cif = cif,
                    phone = phone,
                )

                // Create and configure socket
                createSocket(baseUrl, path, token, cif, phone)

                // Setup listeners
                setupSocketListeners()

                // Start connection
                socket?.connect()

                printLog("OTT: Socket.IO connection initiated to: $baseUrl", TAG)
            } catch (e: Exception) {
                isConnecting.set(false)
                printLogError("OTT: Error connecting to Socket.IO", TAG, e)
                notifyError(e)
            }
        }
    }

    /** Gửi tin nhắn - method for SocketClientAdapter với eventName
     * @param message SocketMessage object để send
     * @param eventName Tên event để emit (do caller quyết định)
     * @return Boolean indicating success/failure
     */
    fun sendMessage(message: SocketMessage, eventName: String): Boolean {
        return if (isConnected()) {
            try {
                val messageData = JSONObject().apply {
                    put("messageId", message.messageId)
                    put("content", message.content)
                    put("type", message.type)
                    put("timestamp", message.timestamp)

                    // Add data if present
                    message.data?.let { data ->
                        val dataObject = JSONObject()
                        data.forEach { (key, value) ->
                            dataObject.put(key, value)
                        }
                        put("data", dataObject)
                    }
                }

                // Use the provided eventName instead of auto-detecting
                socket?.emit(eventName, messageData)
                printLog(
                    "OTT: Message sent: ${message.messageId}, event: $eventName, type: ${message.type}",
                    TAG,
                )
                true
            } catch (e: Exception) {
                printLogError("OTT: Error sending message", TAG, e)
                false
            }
        } else {
            printLog("OTT: Cannot send message, socket not connected", TAG)
            false
        }
    }

    /**
     * Ngắt kết nối Socket.IO
     */
    fun disconnect() {
        scope.launch {
            try {
                // Cancel reconnection if running
                reconnectJob?.cancel()
                reconnectJob = null

                // Disconnect socket
                socket?.disconnect()
                socket?.off() // Remove all listeners
                socket = null

                // Update state
                isConnected.set(false)
                isConnecting.set(false)

                printLog("OTT: Socket.IO disconnected", TAG)
                notifyDisconnected()
            } catch (e: Exception) {
                printLogError("OTT: Error disconnecting Socket.IO", TAG, e)
            }
        }
    }

    /**
     * Lấy Socket ID hiện tại
     */
    fun getSocketId(): String? {
        return if (isConnected.get() && socket != null) {
            socket?.id()
        } else {
            null
        }
    }

    /**
     * Kiểm tra trạng thái kết nối
     */
    fun isConnected(): Boolean {
        return isConnected.get() && socket?.connected() == true
    }

    /**
     * Gửi tin nhắn qua Socket.IO
     */
    fun emit(event: String, data: JSONObject) {
        if (isConnected()) {
            socket?.emit(event, data)
            printLog("OTT: Message emitted: $event", TAG)
        } else {
            printLog("OTT: Cannot emit message, socket not connected", TAG)
        }
    }

    /**
     * Đăng ký callback
     */
    fun registerCallback(callback: SocketCallback) {
        callbacks.add(callback)
        printLog("OTT: Callback registered, total callbacks: ${callbacks.size}", TAG)
    }

    /**
     * Hủy đăng ký callback
     */
    fun unregisterCallback(callback: SocketCallback) {
        callbacks.remove(callback)
        printLog("OTT: Callback unregistered, remaining callbacks: ${callbacks.size}", TAG)
    }

    /**
     * Force reconnection
     */
    fun forceReconnect() {
        if (connectionParams != null) {
            printLog("OTT: Force reconnecting...", TAG)
            disconnect()

            scope.launch {
                delay(1000) // Wait a bit before reconnecting
                connectionParams?.let { params ->
                    connect(params.baseUrl, params.path, params.token, params.cif, params.phone)
                }
            }
        }
    }

    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================

    private fun createSocket(
        baseUrl: String,
        path: String,
        token: String,
        cif: String,
        phone: String,
    ) {
        val options = IO.Options().apply {
            // Basic configuration
            this.path = path
            this.forceNew = true
            this.timeout = CONNECTION_TIMEOUT

            // Transport configuration
            this.transports = arrayOf("websocket")

            // Reconnection configuration
            this.reconnection = true
            this.reconnectionAttempts = RECONNECTION_ATTEMPTS
            this.reconnectionDelay = RECONNECTION_DELAY
            this.reconnectionDelayMax = MAX_RECONNECTION_DELAY
            this.randomizationFactor = 0.5

            // Authentication headers
            this.extraHeaders = mapOf(
                "Authorization" to listOf("Bearer $token"),
                "X-Cif" to listOf(cif),
                "X-Phone" to listOf(phone),
                "X-Client-Type" to listOf("android"),
            )

            // Query parameters
            this.query = "token=$token&cif=$cif&phone=$phone"
        }

        socket = IO.socket(baseUrl, options)
        printLog("OTT: Socket created with config: $baseUrl$path", TAG)
    }

    private fun setupSocketListeners() {
        socket?.apply {
            on(Socket.EVENT_CONNECT, onConnect)
            on(Socket.EVENT_DISCONNECT, onDisconnect)
            on(Socket.EVENT_CONNECT_ERROR, onConnectError)

            // Message events
            on(EVENT_MESSAGE, onMessage)
            on(EVENT_STATUS_UPDATE, onStatusUpdate)
            on(EVENT_NOTIFICATION, onNotification)
            on(EVENT_TRANSACTION, onTransaction)
        }
    }

    // =====================================================
    // SOCKET EVENT LISTENERS
    // =====================================================

    private val onConnect = Emitter.Listener {
        scope.launch {
            isConnected.set(true)
            isConnecting.set(false)

            val socketId = socket?.id()
            printLog("OTT: Socket connected successfully with ID: $socketId", TAG)

            notifyConnected()
        }
    }

    private val onDisconnect = Emitter.Listener { args ->
        scope.launch {
            isConnected.set(false)
            val reason = if (args.isNotEmpty()) args[0].toString() else "unknown"
            printLog("OTT: Socket disconnected, reason: $reason", TAG)

            // Only notify if not manually disconnected
            if (reason != "io client disconnect") {
                notifyDisconnected()
                scheduleReconnection()
            }
        }
    }

    private val onConnectError = Emitter.Listener { args ->
        scope.launch {
            isConnecting.set(false)
            val error = if (args.isNotEmpty()) {
                Exception("Socket connection error: ${args[0]}")
            } else {
                Exception("Socket connection error")
            }

            printLogError("OTT: Socket connection error", TAG, error)
            notifyError(error)
        }
    }

    private val onError = Emitter.Listener { args ->
        scope.launch {
            val error = if (args.isNotEmpty()) {
                Exception("Socket error: ${args[0]}")
            } else {
                Exception("Socket error")
            }
            printLogError("OTT: Socket error", TAG, error)
            notifyError(error)
        }
    }

    private val onMessage = Emitter.Listener { args ->
        scope.launch {
            try {
                if (args.isNotEmpty() && args[0] is JSONObject) {
                    val jsonData = args[0] as JSONObject
                    val message = parseSocketMessage(jsonData, MESSAGE_TYPE_MESSAGE)
                    notifyMessage(message)
                }
            } catch (e: Exception) {
                printLogError("OTT: Error parsing message", TAG, e)
            }
        }
    }

    private val onStatusUpdate = Emitter.Listener { args ->
        scope.launch {
            try {
                if (args.isNotEmpty() && args[0] is JSONObject) {
                    val jsonData = args[0] as JSONObject
                    val message = parseSocketMessage(jsonData, MESSAGE_TYPE_STATUS_UPDATE)
                    notifyMessage(message)
                }
            } catch (e: Exception) {
                printLogError("OTT: Error parsing status update", TAG, e)
            }
        }
    }

    private val onNotification = Emitter.Listener { args ->
        scope.launch {
            try {
                if (args.isNotEmpty() && args[0] is JSONObject) {
                    val jsonData = args[0] as JSONObject
                    val message = parseSocketMessage(jsonData, MESSAGE_TYPE_NOTIFICATION)
                    notifyMessage(message)
                }
            } catch (e: Exception) {
                printLogError("OTT: Error parsing notification", TAG, e)
            }
        }
    }

    private val onTransaction = Emitter.Listener { args ->
        scope.launch {
            try {
                if (args.isNotEmpty() && args[0] is JSONObject) {
                    val jsonData = args[0] as JSONObject
                    val message = parseSocketMessage(jsonData, MESSAGE_TYPE_TRANSACTION)
                    notifyMessage(message)
                }
            } catch (e: Exception) {
                printLogError("OTT: Error parsing transaction", TAG, e)
            }
        }
    }

    // =====================================================
    // HELPER METHODS
    // =====================================================

    private fun parseSocketMessage(jsonData: JSONObject, messageType: Int): SocketMessage {
        val messageId = jsonData.optString("messageId", "")
        val content = jsonData.optString("content", "")
        val timestamp = jsonData.optLong("timestamp", System.currentTimeMillis())
        val dataObject = jsonData.optJSONObject("data")

        val data = mutableMapOf<String, String>()
        dataObject?.let { obj ->
            val keys = obj.keys()
            while (keys.hasNext()) {
                val key = keys.next() as? String ?: continue
                data[key] = obj.optString(key, "")
            }
        }

        return SocketMessage(
            type = messageType,
            messageId = messageId,
            content = content,
            timestamp = timestamp,
            data = data.ifEmpty { null },
        )
    }

    private fun scheduleReconnection() {
        if (connectionParams != null && reconnectJob?.isActive != true) {
            reconnectJob = scope.launch {
                delay(RECONNECTION_DELAY)

                if (!isConnected.get() && connectionParams != null) {
                    printLog("OTT: Attempting manual reconnection...", TAG)
                    connectionParams?.let { params ->
                        connect(params.baseUrl, params.path, params.token, params.cif, params.phone)
                    }
                }
            }
        }
    }

    // =====================================================
    // CALLBACK NOTIFICATION METHODS
    // =====================================================

    private fun notifyConnected() {
        callbacks.forEach { callback ->
            try {
                callback.onConnected()
            } catch (e: Exception) {
                printLogError("OTT: Error in callback onConnected", TAG, e)
            }
        }
    }

    private fun notifyDisconnected() {
        callbacks.forEach { callback ->
            try {
                callback.onDisconnected()
            } catch (e: Exception) {
                printLogError("OTT: Error in callback onDisconnected", TAG, e)
            }
        }
    }

    private fun notifyMessage(message: SocketMessage) {
        callbacks.forEach { callback ->
            try {
                callback.onMessage(message)
            } catch (e: Exception) {
                printLogError("OTT: Error in callback onMessage", TAG, e)
            }
        }
    }

    private fun notifyError(error: Throwable) {
        callbacks.forEach { callback ->
            try {
                callback.onError(error)
            } catch (e: Exception) {
                printLogError("OTT: Error in callback onError", TAG, e)
            }
        }
    }

    // =====================================================
    // CLEANUP
    // =====================================================

    fun cleanup() {
        try {
            reconnectJob?.cancel()
            reconnectJob = null

            socket?.off()
            socket?.disconnect()
            socket = null

            callbacks.clear()
            connectionParams = null

            isConnected.set(false)
            isConnecting.set(false)

            printLog("OTT: Socket client cleaned up", TAG)
        } catch (e: Exception) {
            printLogError("OTT: Error during cleanup", TAG, e)
        }
    }
}