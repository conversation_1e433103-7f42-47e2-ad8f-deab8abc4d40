package com.vietinbank.core_ott.websocket

import com.vietinbank.core_domain.models.ott.SocketMessage
import com.vietinbank.core_domain.models.ott.WebSocketMessage
import com.vietinbank.core_domain.ott.ISocketClient
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Adapter để kết nối OttSocketIOClient với ISocketClient interface từ domain layer
 */
@Singleton
class SocketClientAdapter @Inject constructor(
    private val socketIOClient: OttSocketIOClient,
) : ISocketClient {

    private val callbackAdapters = mutableMapOf<ISocketClient.ISocketCallback, CallbackAdapter>()

    override fun connect(baseUrl: String, path: String, token: String, cif: String, phone: String) {
        socketIOClient.connect(baseUrl, path, token, cif, phone)
    }

    override fun disconnect() {
        socketIOClient.disconnect()
    }

    override fun isConnected(): <PERSON><PERSON>an {
        return socketIOClient.isConnected()
    }

    override fun sendMessage(message: WebSocketMessage, eventName: String): Boolean {
        // <PERSON>y<PERSON>n đổi từ domain model sang SocketMessage
        val socketMessage = SocketMessage(
            type = convertMessageType(message.type),
            messageId = message.messageId,
            content = message.content,
            timestamp = message.timestamp,
            data = message.data,
        )

        return socketIOClient.sendMessage(socketMessage, eventName)
    }

    override fun registerCallback(callback: ISocketClient.ISocketCallback) {
        // Tạo CallbackAdapter mới nếu chưa có
        if (!callbackAdapters.containsKey(callback)) {
            val adapter = CallbackAdapter(callback)
            callbackAdapters[callback] = adapter
            socketIOClient.registerCallback(adapter)
        }
    }

    override fun unregisterCallback(callback: ISocketClient.ISocketCallback) {
        // Tìm và xóa CallbackAdapter tương ứng
        callbackAdapters[callback]?.let { adapter ->
            socketIOClient.unregisterCallback(adapter)
            callbackAdapters.remove(callback)
        }
    }

    /**
     * Adapter để chuyển đổi giữa các loại callback
     */
    private inner class CallbackAdapter(
        private val domainCallback: ISocketClient.ISocketCallback,
    ) : OttSocketIOClient.SocketCallback {

        override fun onConnected() {
            domainCallback.onConnected()
        }

        override fun onDisconnected() {
            domainCallback.onDisconnected()
        }

        override fun onMessage(message: SocketMessage) {
            // Chuyển đổi SocketMessage sang WebSocketMessage (domain model)
            val domainMessage = WebSocketMessage(
                type = convertSocketMessageType(message.type),
                messageId = message.messageId,
                content = message.content,
                timestamp = message.timestamp,
                data = message.data,
            )

            domainCallback.onMessage(domainMessage)
        }

        override fun onError(error: Throwable) {
            domainCallback.onError(error)
        }
    }

    /**
     * Hàm chuyển đổi từ loại tin nhắn domain sang loại tin nhắn socket
     */
    private fun convertMessageType(domainType: Int): Int {
        return when (domainType) {
            WebSocketMessage.TYPE_TEXT -> OttSocketIOClient.MESSAGE_TYPE_TEXT
            WebSocketMessage.TYPE_NOTIFICATION -> OttSocketIOClient.MESSAGE_TYPE_NOTIFICATION
            WebSocketMessage.TYPE_TRANSACTION -> OttSocketIOClient.MESSAGE_TYPE_TRANSACTION
            WebSocketMessage.TYPE_STATUS_UPDATE -> OttSocketIOClient.MESSAGE_TYPE_STATUS_UPDATE
            else -> OttSocketIOClient.MESSAGE_TYPE_TEXT
        }
    }

    /**
     * Hàm chuyển đổi từ loại tin nhắn socket sang loại tin nhắn domain
     */
    private fun convertSocketMessageType(socketType: Int): Int {
        return when (socketType) {
            OttSocketIOClient.MESSAGE_TYPE_TEXT -> WebSocketMessage.TYPE_TEXT
            OttSocketIOClient.MESSAGE_TYPE_NOTIFICATION -> WebSocketMessage.TYPE_NOTIFICATION
            OttSocketIOClient.MESSAGE_TYPE_TRANSACTION -> WebSocketMessage.TYPE_TRANSACTION
            OttSocketIOClient.MESSAGE_TYPE_STATUS_UPDATE -> WebSocketMessage.TYPE_STATUS_UPDATE
            else -> WebSocketMessage.TYPE_TEXT
        }
    }
}